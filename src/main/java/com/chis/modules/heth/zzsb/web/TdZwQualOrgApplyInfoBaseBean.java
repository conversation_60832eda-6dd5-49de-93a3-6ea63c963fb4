package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.logic.*;
import com.chis.modules.heth.zzsb.service.TdZwQualOrgApplyService;
import com.chis.modules.heth.zzsb.service.ZzsbZwPsnPlanServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.enumn.WordReportCodeEnum;
import com.chis.modules.system.logic.WordReportDTO;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import org.primefaces.component.datatable.DataTable;
import com.chis.modules.system.utils.WordReportGenerateUtil;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.TabChangeEvent;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.io.IOException;

/**
 * @Description: 操作资质机构备案公共页的Bean
 *
 * @ClassAuthor pw,2022年04月14日,TdZwQualOrgApplyInfoBaseBean
 */
public class TdZwQualOrgApplyInfoBaseBean {
    public TdZwQualOrgApplyService orgApplyService = (TdZwQualOrgApplyService) SpringContextHolder.getBean(TdZwQualOrgApplyService.class);
    public SystemModuleServiceImpl moduleService = (SystemModuleServiceImpl)SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    public CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    public ZzsbZwPsnPlanServiceImpl psnPlanServiceImpl = SpringContextHolder.getBean(ZzsbZwPsnPlanServiceImpl.class);
    /** 资质机构备案主表的rid */
    private Integer orgApplyRid;
    /** 缓存父页面传递过来的码表 key codeTypeName value对应码表集合 */
    private Map<String,List<TsSimpleCode>> allSimpleCodeMap;
    /**5527服务项目码表*/
    private Map<String,List<TsSimpleCode>> jkItemSimpleCodeMap;
    /** 是否显示外出开展职业健康检查工作能力地区(外检车辆) 配置参数 */
    private Boolean ifHethFj;
    /** 检查机构是否有质量管理体系 配置参数 */
    private Boolean ifQualAnnex;
    /** 是否有人员汇总及填报说明人员汇总 配置参数*/
    private Boolean ifPersonSummary;
    /** 是否终审 */
    private Boolean ifFinalAudit;
    /** 资质备案有无有效期限 配置参数*/
    private Boolean ifValidityPeriod;
    /** 是否详情 */
    private Boolean ifView;

    /** tab name */
    private String tabTitle;
    /** 法人职务列表 */
    private List<String> careerList;
    /** 服务项目 */
    private List<SelectItem> serviceObjList;
    /** 选择的服务项目 */
    private List<String> selectServiceObjList;
    /** 服务项目名称集合 详情用 */
    private List<String> serviceItemNameList;
    /**所属地区名称*/
    private String orgZoneName;
    /**所属地区id*/
    private Integer orgZoneId;
    /**能看到的所属地区集合*/
    private List<TsZone> orgZoneList;
    /** 服务项目码表 key codeNo  */
    private Map<String, TsSimpleCode> serviceItemSimpleCodeMap;

    /** 机构基本信息[机构备案] */
    private TdZwTjorginfoApply tjorginfoApply;
    /** 进页面时机构备案主表的状态 */
    private Integer orgInfoState;
    /** 机构资质附件 */
    private List<TdZwTjorgannexApply> tjorgannexApplyList;
    /**服务明细小类*/
    private Map<Integer,List<TdZwTjorgItmDetailApy>> tdZwTjorgItmDetailApyMap;

    /** 机构资质 */
    private TsSimpleCode qualType;
    /** 申请类型 */
    private TsSimpleCode applyType;
    /** 机构类型 1：职业健康检查机构 2：职业病诊断机构 */
    private Integer orgType;
    /**
     * 资质机构类型 orgType 1>0 2>2
     */
    private Integer zzjgType;
    /** 是否有外检车辆 默认无 */
    private Boolean ifOutWork = Boolean.FALSE;
    /** 申请材料 */
    private List<TdZwTjorgannexApply> applyAnnexList;
    /** 操作的资质附件附件类型 */
    private Integer selectAnnexType=1;
    /** 操作的申请材料或者资质附件 */
    private TdZwTjorgannexApply selectAnnex;
    /** 删除的资质附件rid */
    private List<Integer> annexApplyRemoveRidList;
    /** 备案回执 */
    private TdZwTjorgannexApply reportReceipt;

    /** 是否显示备案编号 */
    private Boolean ifBaseInfoApplyShow = Boolean.FALSE;
    /** 基础信息的通用信息部分是否可编辑 */
    private Boolean ifBaseInfoCommInfoEdit = Boolean.FALSE;
    /** 基础信息的服务项目部分是否可编辑 */
    private Boolean ifBaseInfoServiceInfoEdit = Boolean.FALSE;
    /** 基础信息的备案编号部分是否可编辑 */
    private Boolean ifBaseInfoApplyInfoEdit = Boolean.FALSE;
    /** 检查项目码表Map */
    private Map<TsSimpleCode, List<TsSimpleCode>> diagMap;
    /**检查项目对象-列表显示*/
    private List<TjitemsApplyVo> showItemList;
    /** 当前选中父类Id */
    private String selItemId;
    /**已选择的检查项目*/
    private List<String> jcItems;
    /**已选择的检查项目Map*/
    private Map<Integer,String[]> selCodeRidMap;
    /** 地区Map key rid */
    private Map<Integer, TsZone> tsZoneMap;
    /** 外检车辆列表 */
    private List<Object[]> tjorgOutcarApplyList;
    /** 服务明细服务大类Rid*/
    private String jcItemRid;
    /**服务明细*/
    private List<ZwJkItemPO> jkItemList;
    private List<ZwJkItemRowPO> jkItemRowList;
    /**服务项目大类存储*/
    private Map<String,TsSimpleCode> jkMap;
    /** 质量管理体系制度集合  码表5507 num排序 需初始化*/
    private List<TsSimpleCode> institutionList;
    /** 质量管理体系制度 顶级制度集合 */
    private List<TsSimpleCode> topOfInstitutionList;
    /** 质量管理体系制度 顶级制度对应Map */
    private Map<Integer, TsSimpleCode> topInstitutionMap;
    /** 质量管理体系制度 顶级制度下的分制度 */
    private Map<Integer, List<TsSimpleCode>> institutionMap;
    /** 质量管理体系制度 各制度对应的List */
    private Map<Integer,List<TdZwTjorgQualAnnexApy>> qualAnnexMap;
    /** 质量管理体系制度 添加用的codeId */
    private Integer codeId;
    /** 质量管理体系制度 上传附件 删除传递对象 */
    private TdZwTjorgQualAnnexApy annexTmp;
    /** 质量管理体系制度 组合动态id 用于文件上传插件 更新对应表格数据 */
    private String topInsPanelStr;
    /**资质附件删除接收对象*/
    private Object[] selectZwAnnex;
    /**资质附件*/
    private List<ZwAnnexsPO> annexsPOList;
    /**资质附件-每种资质附件的计数*/
    private Map<String,Integer>  countMap;
    /**资质附件-地区*/
    private Integer searchZoneId;
    /**资质附件-地区*/
    private String searchZoneCode;
    /**资质附件-地区*/
    private String searchZoneName;
    /**资质附件-地区*/
    private List<TsZone> zoneList;
    /**资质附件*/
    private TdZwTjorgannexApply organnexApply;
    /**资质附件-发证单位*/
    private String unitName;
    /**资质附件-发证单位级别*/
    private Integer unitLevel;
    /**资质附件-许可证信息*/
    private TdZwTjorgannexApply ylAnnex;
    /** 当前操作的外检车辆 */
    private TdZwTjorgOutcarApply outcarZzsb;
    /**编辑，删除时接收页面值*/
    private Object[] modOutcarZzsb;
    /** 外检车辆已选择仪器名称 */
    private String outCarInstNames;
    /** 外检车辆选择仪器弹出框查询条件 仪器名称 */
    private String searchOutCarInstName;
    /** 外检车辆选择仪器弹出框查询条件 仪器型号 */
    private String searchOutCarModel;
    /** 外检车辆仪器弹出框列表 */
    private List<OutCarInstPO> outCarInstList = new ArrayList<>();
    /**外检车辆仪器  key rid value 仪器名称*/
    private Map<Integer,String> outCarInstMap=new HashMap<>();
    /** 外检车辆已选择仪器Rid */
    private List<Integer> outCarInstSelectRidList = new ArrayList<>();
    private List<Integer> outCarInstRealSelectRidList = new ArrayList<>();
    /**
     * 外检车辆版本，1：陕西
     */
    private String outCarVersion;
    /**
     * 外检车辆操作附件类型，1 行驶证复印件 ; 2 车辆照片 ; 3 车载DR照片
     */
    private Integer outCarOpFile;
    /** 当前省份的所有地区 */
    private List<TsZone> allTsZoneList;
    /**已删除的服务明细*/
    private List<Integer> resultList;

    /**
     * 人员信息VO
     */
    private ZzsbPsnInfoVO psnInfoVO;
    private TdzwPsnInfoZzsbBaseNew psnInfoBase;
    /**
     * 仪器信息VO
     */
    private ZzsbInstInfoVO instInfoVO;
    /** 备案回执生成的文件路径 */
    private String reportReceiptFilePath;
    /** 备案回执 下载包含流信息的对象 */
    private StreamedContent reportReceiptFile;
    /** 用于给申请材料以及回执用的资质附件类型 */
    private Integer annexTypeBase;
    /**备案日期开始日期最大日期，不能大于今天也不能大于有效结束日期*/
    private Date firstGetdayMaxDate =new Date();


    public TdZwQualOrgApplyInfoBaseBean(){}

    /**
     * @Description: 当前的类 需要依赖一些必要参数 所有参数通过构造方法传入 由于参数过多 参数通过对象封装
     * orgApplyRid  备案主表rid
     * ifHethFj 是否显示外出开展职业健康检查工作能力地区(外检车辆)配置参数转换的boolean
     * ifQualAnnex 检查机构是否有质量管理体系配置参数转换的boolean
     * ifPersonSummary 职业健康检查机构是否有人员汇总及填报说明人员汇总配置参数转换的boolean
     * ifFinalAudit 是否终审
     * ifValidityPeriod 资质备案有无有效期限配置参数转换的boolean
     * ifView 是否详情
     * allSimpleCodeMap 缓存父页面传递过来的码表 key codeTypeName value对应码表集合
     * @MethodAuthor pw,2022年04月14日
     */
    public TdZwQualOrgApplyInfoBaseBean(QualOrgApplyParamPO paramPO){
        this.orgApplyRid = paramPO.getOrgApplyRid();
        this.ifHethFj = paramPO.getIfHethFj();
        this.ifQualAnnex = paramPO.getIfQualAnnex();
        this.ifPersonSummary = paramPO.getIfPersonSummary();
        this.ifFinalAudit = paramPO.getIfFinalAudit();
        this.ifValidityPeriod = paramPO.getIfValidityPeriod();
        this.ifView = null == paramPO.getIfView() ? false : paramPO.getIfView();
        this.allSimpleCodeMap = null == paramPO.getAllSimpleCodeMap() ? new HashMap<String, List<TsSimpleCode>>() : paramPO.getAllSimpleCodeMap();
        this.orgZoneList = null == paramPO.getOrgZoneList() ? Collections.EMPTY_LIST : paramPO.getOrgZoneList();
        this.allTsZoneList = null == paramPO.getAllZoneList() ? Collections.EMPTY_LIST : paramPO.getAllZoneList();
        this.outCarVersion = "";
        try {
            this.outCarVersion = StringUtils.objectToString(PropertyUtils.getValue("zzsb.outCarVersion"));
        } catch (Exception ignored) {
        }
        //必要的码表检查
        this.necessSimpleCodeCheck();
        this.initOrgApplyInfo();
        //这个别动 放最下边
        this.initSelectTabView();
    }

    /**
     * @Description: 生成备案回执
     *
     * @MethodAuthor pw,2022年04月24日
     */
    public void generateReport(){
        this.reportReceiptFilePath = null;
        //生成前 先验证保存
        if(this.submitValidate() || !this.executeSaveOrUpdate(false, false)){
            return;
        }
        WordReportDTO reportDTO = WordReportGenerateUtil.generateReport(WordReportCodeEnum.HETH_BAHZ_1001,
                this.orgApplyRid, false);
        if(null != reportDTO && "00".equals(reportDTO.getType()) && StringUtils.isNotBlank(reportDTO.getFilePath())){
            this.reportReceiptFilePath = reportDTO.getFilePath();
        }
        if(StringUtils.isBlank(this.reportReceiptFilePath)){
            JsfUtil.addErrorMessage("生成备案回执失败！");
            return;
        }
        // 调用前端函数下载文件
        RequestContext.getCurrentInstance().execute("generateClick()");
    }

    /**
     * @Description: 删除备案回执
     *
     * @MethodAuthor pw,2022年04月24日
     */
    public void delReportReceipt(){
        if(null != this.reportReceipt && null != this.reportReceipt.getRid()){
            this.commService.deleteEntity(TdZwTjorgannexApply.class, this.reportReceipt.getRid());
        }
        this.reportReceipt = new TdZwTjorgannexApply();
        //服务明细初始化
        this.initJkItemRowList();
    }

    /**
     * @Description: 备案回执 上传
     *
     * @MethodAuthor pw,2022年04月25日
     */
    public synchronized void preUploadReportReceipt(){
        //验证后保存  提交验证
        if(this.submitValidate() || !this.executeSaveOrUpdate(false, false)){
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('fileUIdVar').show();");
    }

    /**
     *  <p>方法描述：服务明细初始化</p>
     * @MethodAuthor hsj 2022/4/20 15:32
     */
    public void initJkItemRowList() {
        List<TsSimpleCode> jkList = this.allSimpleCodeMap.get("5018");
        jkMap = new HashMap<>();
        jkItemList = new ArrayList<>();
        /*职业健康服务项目  小类*/
        serverSubItemSimpleCodeListByExt2();
        for(TsSimpleCode simpleCode:jkList){
            jkMap.put(simpleCode.getCodeNo(),simpleCode);
            List<TsSimpleCode>  jkItems= jkItemSimpleCodeMap.get(simpleCode.getCodeNo());
            ZwJkItemPO zwJkItemPO=new ZwJkItemPO();
            zwJkItemPO.setJcItem(simpleCode);
            zwJkItemPO.setJcItemList(jkItems);
            jkItemList.add(zwJkItemPO);
        }
        initSerItem(this.jkItemList);
        this.jkItemRowList = convertZwJkItemPOToRowPO(jkItemList);
        changeSerItmDetail();
    }

    /**
     *  <p>方法描述：初始化服务明细小类</p>
     * @MethodAuthor hsj 2022/4/20 15:40
     */
    public void initSerItem(List<ZwJkItemPO> zwJkItemPOS){
        Map<String,String[]> selectItems=new HashMap<String,String[]>();
        Map<String,String[]> selectItemsExter=new HashMap<String,String[]>();
        Map<String,String> selectItemName=new HashMap<String,String>();
        List<String> zwSvritmNoArray=new ArrayList<>();
        if(!CollectionUtils.isEmpty(this.tjorginfoApply.getTjorggitemsApplyList())){
            for(int i = 0 ; i < this.tjorginfoApply.getTjorggitemsApplyList().size();i++){
                TdZwTjorggitemsApply t = this.tjorginfoApply.getTjorggitemsApplyList().get(i);
                zwSvritmNoArray.add(t.getFkByItemId().getCodeNo());
                //服务明细的小类
                List<TdZwTjorgItmDetailApy> zwSvritmSubList = tdZwTjorgItmDetailApyMap.get(t.getRid());
                if(!CollectionUtils.isEmpty(zwSvritmSubList)) {
                    String[] itemSubs = new String[zwSvritmSubList.size()];
                    List<String> itemSubsExter = new ArrayList<>();
                    StringBuffer itemNames=new StringBuffer();
                    for(int j = 0 ; j<zwSvritmSubList.size(); j++){
                        TdZwTjorgItmDetailApy sub = zwSvritmSubList.get(j);
                        if (null == sub.getFkByItemDetailId()) {
                            continue;
                        }
                        itemSubs[j] = sub.getFkByItemDetailId().getRid().toString();
                        itemNames.append(sub.getFkByItemDetailId().getCodeName()).append("，");
                        if(sub.getIfExterInspect()!=null && sub.getIfExterInspect()==1){
                            itemSubsExter.add(itemSubs[j]);
                        }
                    }
                    selectItems.put(String.valueOf(t.getFkByItemId().getCodeNo()),itemSubs);
                    selectItemName.put(String.valueOf(t.getFkByItemId().getCodeNo()),itemNames.toString().substring(0,itemNames.toString().length()-1));
                    selectItemsExter.put(String.valueOf(t.getFkByItemId().getCodeNo()),itemSubsExter.toArray(new String[0]));
                }
            }

        }
        List<ZwJkItemPO> newZwJkItemPO=new ArrayList<>();
        for(ZwJkItemPO itemPO:zwJkItemPOS){
            String key=itemPO.getJcItem().getCodeNo();
            if(null!=selectItems&&null!=selectItems.get(key)&&selectItems.get(key).length>0){
                itemPO.setSelectedSerItmDetail(selectItems.get(key));
                itemPO.setSelectedItemDetail(selectItemName.get(key));
            }
            itemPO.setSelectedIfExterInspect(selectItemsExter.get(key));
            if(zwSvritmNoArray.contains(itemPO.getJcItem().getCodeNo())){
                newZwJkItemPO.add(itemPO);
            }
        }
        for(ZwJkItemPO itemPO: newZwJkItemPO){
            if(itemPO.getSelectedSerItmDetail()!=null&&itemPO.getSelectedSerItmDetail().length==0){
                itemPO.setSelectedItemDetail(null);
            }
        }
        zwJkItemPOS.clear();
        zwJkItemPOS.addAll(newZwJkItemPO);
    }
    /**
     * <p>描述 转换ZwJkItemPO-->ZwJkItemRowPO</p>
     *
     * @param ZwJkItemPOS
     * @MethodAuthor gongzhe,2022/4/15 15:43,convertZwJkItemPOToRowPO
     * @return java.util.List<com.chis.modules.heth.zzsb.logic.ZwJkItemRowPO>
     */
    public List<ZwJkItemRowPO> convertZwJkItemPOToRowPO(List<ZwJkItemPO> ZwJkItemPOS){
        boolean out = false;
        List<ZwJkItemRowPO> result = new ArrayList<>();
        int start = 0;
        Map<Integer,Integer> rowspanMap = new HashMap<>();
        boolean ifBaseInfoServiceInfo = getIfBaseInfoServiceInfoEdit();
        if(!ifBaseInfoServiceInfo){
            //查看时，需过滤掉停用的码表，计算需要合并的行
            for (int i = 0; i < ZwJkItemPOS.size(); i++) {
                ZwJkItemPO itemPO = ZwJkItemPOS.get(i);
                TsSimpleCode jcItem = itemPO.getJcItem();
                List<TsSimpleCode> jcItemList = itemPO.getJcItemList();
                //已勾选的服务小类
                List<String> selectedSerItmDetail = itemPO.getSelectedSerItmDetail()==null ? null: Arrays.asList(itemPO.getSelectedSerItmDetail());
                if(CollectionUtils.isEmpty(selectedSerItmDetail)){
                    rowspanMap.put(jcItem.getRid(),0);
                    continue;
                }
                //未停用码表数量
                int count = 0;
                for (int j = 0; j < jcItemList.size(); j++) {
                    TsSimpleCode c = jcItemList.get(j);
                    if(selectedSerItmDetail.contains(c.getRid().toString())){
                        count++;
                    }
                }
                rowspanMap.put(jcItem.getRid(),count);
            }
        }
        for (int i = 0; i < ZwJkItemPOS.size(); i++) {
            ZwJkItemPO itemPO = ZwJkItemPOS.get(i);
            TsSimpleCode jcItem = itemPO.getJcItem();
            List<TsSimpleCode> jcItemList = itemPO.getJcItemList();
            if(i>0){
                if(!ifBaseInfoServiceInfo ){
                    start += rowspanMap.get(ZwJkItemPOS.get(i-1).getJcItem().getRid());
                }else{
                    start += ZwJkItemPOS.get(i-1).getJcItemList().size();
                }
            }
            //已勾选的服务小类
            List<String> selectedSerItmDetail = itemPO.getSelectedSerItmDetail()==null ? null: Arrays.asList(itemPO.getSelectedSerItmDetail());
            List<String> selectedIfExterInspect = itemPO.getSelectedIfExterInspect()==null ? null: Arrays.asList(itemPO.getSelectedIfExterInspect());
            for (int j = 0; j < jcItemList.size(); j++) {
                TsSimpleCode c = jcItemList.get(j);
                if(!ifBaseInfoServiceInfo  && (selectedSerItmDetail == null || !selectedSerItmDetail.contains(c.getRid().toString()))) {
                    //如果是查看，且勾选的里面未包含该子项，则跳过
                    continue;
                }
                ZwJkItemRowPO row = new ZwJkItemRowPO();
                row.setJcItem(jcItem);
                row.setSerItem(c);
                if(!ifBaseInfoServiceInfo){
                    row.setRowspan(rowspanMap.get(jcItem.getRid()));
                }else{
                    row.setRowspan(jcItemList.size());
                }
                row.setRowspanStart(start);
                if(selectedSerItmDetail!=null && selectedSerItmDetail.contains(c.getRid().toString())){
                    row.setSelectedSerItmDetail(new String[]{c.getRid().toString()});
                }
                if(out){
                    row.setIfExterInspect("0");
                }
                if(selectedIfExterInspect!=null && selectedIfExterInspect.contains(c.getRid().toString())){
                    row.setSelectedIfExterInspect(new String[]{c.getRid().toString()});
                    row.setIfExterInspect("1");
                }
                result.add(row);
            }
        }
        return result;
    }
    /**
     *  <p>方法描述：服务明细大类选择</p>
     * @MethodAuthor hsj 2022/4/20 16:24
     */
    public void changeJcItem(){
        String codeNo = null;
        String[] selectedJcItem = null;
        boolean out = tjorginfoApply!=null && tjorginfoApply.getOutWorkPower()!=null && tjorginfoApply.getOutWorkPower()==1;
        for(ZwJkItemRowPO itemPO:jkItemRowList){
            TsSimpleCode jcItem = itemPO.getJcItem();
            if(codeNo == null && jcItemRid.equals(jcItem.getRid().toString())){
                codeNo = jcItem.getCodeNo();
                selectedJcItem = itemPO.getSelectedJcItem();
            }
            if(codeNo!=null && codeNo.equals(jcItem.getCodeNo())){
                itemPO.setSelectedJcItem(selectedJcItem);
                if(selectedJcItem!=null && selectedJcItem.length>0){
                    itemPO.setSelectedSerItmDetail(new String[]{itemPO.getSerItem().getRid().toString()});
                    if(!out){
                        //若不具备外检能力，则清空外检勾选情况
                        itemPO.setSelectedIfExterInspect(new String[0]);
                    }
                }else{
                    itemPO.setSelectedSerItmDetail(new String[0]);
                    itemPO.setSelectedIfExterInspect(new String[0]);
                }
            }
        }
    }
    /**
     *  <p>方法描述：服务明细小类选择</p>
     * @MethodAuthor hsj 2022/4/20 16:25
     */
    public void changeSerItmDetail(){
        Map<String,List<ZwJkItemRowPO>> map = new HashMap<>();
        Map<String,List<ZwJkItemRowPO>> selectMap = new HashMap<>();
        boolean out = tjorginfoApply!=null && tjorginfoApply.getOutWorkPower()!=null && tjorginfoApply.getOutWorkPower()==1;
        for(ZwJkItemRowPO itemPO:jkItemRowList){
            TsSimpleCode jcItem = itemPO.getJcItem();
            List<ZwJkItemRowPO> list = map.get(jcItem.getCodeNo());
            if(list == null){
                list = new ArrayList<>();
            }
            list.add(itemPO);
            map.put(jcItem.getCodeNo(),list);
            if(itemPO.getDisabled()!=null && !itemPO.getDisabled()){
                List<ZwJkItemRowPO> selectList = selectMap.get(jcItem.getCodeNo());
                if(selectList == null){
                    selectList = new ArrayList<>();
                }
                selectList.add(itemPO);
                selectMap.put(jcItem.getCodeNo(),selectList);
            }
        }
        for(ZwJkItemRowPO itemPO:jkItemRowList){
            String codeNo = itemPO.getJcItem().getCodeNo();
            if(selectMap.get(codeNo)!=null && selectMap.get(codeNo).size() == map.get(codeNo).size()){
                itemPO.setSelectedJcItem(new String[]{codeNo});
            }else{
                itemPO.setSelectedJcItem(new String[0]);
            }
            if(itemPO.getDisabled()){
                //取消勾选
                itemPO.setSelectedIfExterInspect(new String[0]);
            }
            //若勾选框不可编辑或不具备外检能力，则清空外检勾选情况
            if(itemPO.getDisabled() || !out){
                //取消勾选
                itemPO.setSelectedIfExterInspect(new String[0]);
            }
        }
    }

    /**
     * @Description: 打开时tabView 默认选择第一个tab
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private void initSelectTabView(){
        if(null == this.orgType){
            return;
        }
        if(1 == this.orgType){
            RequestContext.getCurrentInstance().execute("PF('OrgTjApplyTabView').select(0)");
        }else if(2 == this.orgType){
            RequestContext.getCurrentInstance().execute("PF('OrgDiagApplyTabView').select(0)");
        }
    }

    /**
     * @Description: 保存
     *
     * @MethodAuthor pw,2022年04月15日
     */
    public void saveAction(){
        if(this.saveValidate()){
            return;
        }
        this.executeSaveOrUpdate(false, true);
    }

    /**
     * @Description: 提交
     *
     * @MethodAuthor pw,2022年04月15日
     */
    public boolean submitAction(){
        if(this.submitValidate()){
            this.tjorginfoApply.setState(this.orgInfoState);
            return false;
        }
        return this.executeSaveOrUpdate(true, true);
    }


    /**
     * @Description: 撤销 状态由父页面调用处赋值
     *
     * @MethodAuthor pw,2022年04月18日
     */
    public void cancelAction(){
        if(null == this.tjorginfoApply || null == this.tjorginfoApply.getRid()){
            return;
        }
        try{
            this.orgApplyService.upsertEntity(this.tjorginfoApply);
            this.ifView = false;
            this.initOrgApplyInfo();
        }catch(Exception e){
            e.printStackTrace();
            this.tjorginfoApply.setState(this.orgInfoState);
            this.ifView = true;
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
     * @Description: 执行保存或者提交 状态由父页面调用处赋值
     *
     * @MethodAuthor pw,2022年04月16日
     */
    public boolean executeSaveOrUpdate(boolean ifSubmit, boolean ifShowTip){
        try{
            if(null == this.orgType || null == this.orgInfoState){
                return false;
            }else if(1 == this.orgType || 2 == this.orgType){
                //执行封装与存储
                //服务明细封装
                fillItemRemoveRidListForSave();
                //资质附件集合 包含申请材料以及资质附件
                List<TdZwTjorgannexApply> saveTjorgannexApplyList = this.fillAnnexApplyForSave();
                //要删除的资质附件ID集合
                List<Integer> saveAnnexApplyRemoveRidList = this.fillAnnexApplyRemoveRidForSave();
                //有其他参数 继续在后边添加
                this.orgApplyService.saveOrUpdateTjorginfo(this.tjorginfoApply,
                        this.resultList,saveTjorgannexApplyList, saveAnnexApplyRemoveRidList, this.orgInfoState,this.qualAnnexMap);
            }else{
                return false;
            }
            if(ifSubmit){
                this.ifView = Boolean.TRUE;
            }
            this.initOrgApplyInfo();
            if(ifShowTip){
                JsfUtil.addSuccessMessage((ifSubmit ? "提交" : "保存")+"成功！");
            }
            return true;
        }catch(Exception e){
            e.printStackTrace();
            //状态置回
            this.tjorginfoApply.setState(this.orgInfoState);
            JsfUtil.addErrorMessage((ifSubmit ? "提交" : "保存")+"失败！");
            return false;
        }
    }

    /**
     * @Description: 组合服务项目
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private void fillItemsApplyListForSave(){
        List<TdZwTjorggitemsApply> resultList = new ArrayList<>();
        if(CollectionUtils.isEmpty(this.selectServiceObjList)){
            this.tjorginfoApply.setTjorggitemsApplyList(new ArrayList<TdZwTjorggitemsApply>());
        }else {
            for(String codeNo : this.selectServiceObjList){
                TsSimpleCode simpleCode = this.serviceItemSimpleCodeMap.get(codeNo);
                if(null == simpleCode){
                    continue;
                }
                TdZwTjorggitemsApply itemApply = null;
                if(!CollectionUtils.isEmpty(this.tjorginfoApply.getTjorggitemsApplyList())){
                    for(TdZwTjorggitemsApply curItemApply : this.tjorginfoApply.getTjorggitemsApplyList()){
                        String curCodeNo = null == curItemApply.getFkByItemId() ? null :
                                curItemApply.getFkByItemId().getCodeNo();
                        if(codeNo.equals(curCodeNo)){
                            itemApply = curItemApply;
                            break;
                        }
                    }
                }
                if(null == itemApply){
                    itemApply = new TdZwTjorggitemsApply();
                    itemApply.setFkByOrgId(this.tjorginfoApply);
                    itemApply.setFkByItemId(simpleCode);
                }
                resultList.add(itemApply);
            }
            this.tjorginfoApply.setTjorggitemsApplyList(resultList);
        }
        fillDetailApyListForSave();
    }

    /**
     * @Description: 组合删除的服务项目ID集合
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private void fillItemRemoveRidListForSave(){
        resultList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(this.tjorginfoApply.getTjorggitemsApplyList())){
            for(TdZwTjorggitemsApply curItemApply : this.tjorginfoApply.getTjorggitemsApplyList()){
                String curCodeNo = null == curItemApply.getFkByItemId() ? null :
                        curItemApply.getFkByItemId().getCodeNo();
                if(CollectionUtils.isEmpty(this.selectServiceObjList) || null == curCodeNo ||
                        !this.selectServiceObjList.contains(curCodeNo)){
                    resultList.add(curItemApply.getRid());
                }
            }
        }
        fillItemsApplyListForSave();
    }

    /**
     * @Description: 组合服务项目明细
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private void fillDetailApyListForSave(){
        List<TdZwTjorgItmDetailApy> tdZwTjorgItmDetailApyList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(this.tjorginfoApply.getTjorggitemsApplyList())){
            for(TdZwTjorggitemsApply tdZwTjorggitemsApply: this.tjorginfoApply.getTjorggitemsApplyList()){
                for(ZwJkItemRowPO zwJkItemRowPO:jkItemRowList){
                    if(tdZwTjorggitemsApply.getFkByItemId().getCodeNo().equals(zwJkItemRowPO.getJcItem().getCodeNo()) && !zwJkItemRowPO.getDisabled()){
                        TdZwTjorgItmDetailApy tdZwTjorgItmDetailApy = new TdZwTjorgItmDetailApy();
                        tdZwTjorgItmDetailApy.setFkByMainId(tdZwTjorggitemsApply);
                        tdZwTjorgItmDetailApy.setFkByItemDetailId(zwJkItemRowPO.getSerItem());
                        if(null != zwJkItemRowPO.getSelectedIfExterInspect() && zwJkItemRowPO.getSelectedIfExterInspect().length > 0){
                            tdZwTjorgItmDetailApy.setIfExterInspect(1);
                        }else {
                            tdZwTjorgItmDetailApy.setIfExterInspect(0);
                        }
                        tdZwTjorgItmDetailApy.setCreateDate(new Date());
                        tdZwTjorgItmDetailApy.setCreateManid(Global.getUser().getRid());
                        tdZwTjorgItmDetailApyList.add(tdZwTjorgItmDetailApy);
                    }
                }
            }
        }
        tjorginfoApply.setTjorgItmDetailApies(tdZwTjorgItmDetailApyList);
    }

    /**
     * @Description: 组合删除的服务项目明细ID集合
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private List<Integer> fillDetailApyRemoveRidListForSave(){
        //需替换
        return null;
    }

    /**
     * @Description: 组合删除的资质附件ID集合
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private List<Integer> fillAnnexApplyRemoveRidForSave(){
        return this.annexApplyRemoveRidList;
    }

    /**
     * @Description: 组合准备存储的资质附件
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private List<TdZwTjorgannexApply> fillAnnexApplyForSave(){
        List<TdZwTjorgannexApply> resultList = new ArrayList<>();
        //申请材料
        if(!CollectionUtils.isEmpty(this.applyAnnexList)){
            resultList.addAll(this.applyAnnexList);
        }
        return resultList;
    }

    /**
     * @Description: 基本信息中服务大类change事件
     *
     * @MethodAuthor pw,2022年04月16日
     */
    public void serviceChange(){
        if(!CollectionUtils.isEmpty(selectServiceObjList)){
            //去重
//            removeDuplicate(selectServiceObjList);
            jkItemList= new ArrayList<>();
            for(String codeno:selectServiceObjList){
                List<TsSimpleCode>  jkItems= this.jkItemSimpleCodeMap.get(jkMap.get(codeno).getCodeNo());
                ZwJkItemPO zwJkItemPO=new ZwJkItemPO();
                zwJkItemPO.setJcItem(jkMap.get(codeno));
                zwJkItemPO.setJcItemList(jkItems);
                jkItemList.add(zwJkItemPO);
            }
        }else{
            JsfUtil.addErrorMessage("服务项目不能为空！");
            jkItemList= new ArrayList<>();
        }
        initJcList(selectServiceObjList);
    }
    /**
     *  <p>方法描述：list去重不改变顺序</p>
     * @MethodAuthor hsj 2022/4/25 15:15
     */
    public  List<String> removeDuplicate(List<String> list) {
        Set set = new HashSet();
        List newList = new ArrayList();
        for (Iterator iter = list.iterator(); iter.hasNext();) {
            Object element = iter.next();
            if(set.add(element)){
                newList.add(element);
            }
        }
        list.clear();
        list.addAll(newList);
        return list;
    }
 /**
  *  <p>方法描述： 勾选大类时，实时更新小类中被勾选的值 </p>
  * @MethodAuthor hsj 2022/4/20 17:59
  */
    public void initJcList(List<String> selectServiceObjList){
        if(!CollectionUtils.isEmpty(this.tjorginfoApply.getTjorggitemsApplyList())){
            Map<String,String[]> selectItems=new HashMap<String,String[]>();
            for(int i = 0 ; i < this.tjorginfoApply.getTjorggitemsApplyList().size();i++){
                TdZwTjorggitemsApply t = this.tjorginfoApply.getTjorggitemsApplyList().get(i);
                List<TdZwTjorgItmDetailApy> zwSvritmSubList = tdZwTjorgItmDetailApyMap.get(t.getRid());
                if(!CollectionUtils.isEmpty(zwSvritmSubList)) {
                    String[] itemSubs = new String[zwSvritmSubList.size()];
                    for(int j = 0 ; j<zwSvritmSubList.size(); j++){
                        TdZwTjorgItmDetailApy sub = zwSvritmSubList.get(j);

                        if (null == sub.getFkByItemDetailId()) {
                            continue;
                        }
                        if(selectServiceObjList.contains(String.valueOf(sub.getFkByItemDetailId().getExtendS2()))){
                            itemSubs[j] = sub.getFkByItemDetailId().getRid().toString();
                        }
                    }
                    selectItems.put(String.valueOf(t.getFkByItemId().getCodeNo()),itemSubs);
                }
            }

            for(ZwJkItemPO itemPO:jkItemList){
                String key=itemPO.getJcItem().getCodeNo();
                if(null!=selectItems.get(key)&&selectItems.get(key).length>0){
                    itemPO.setSelectedSerItmDetail(selectItems.get(key));
                }
            }
        }
        List<ZwJkItemRowPO> tempList =  convertZwJkItemPOToRowPO(jkItemList);
        List<ZwJkItemRowPO> resultList = new ArrayList<>(tempList.size());
        Iterator<ZwJkItemRowPO> iterator = tempList.iterator();
        while(iterator.hasNext()){
            ZwJkItemRowPO tmpRow = iterator.next();
            ZwJkItemRowPO realRow = null;
            for(ZwJkItemRowPO pageRow:this.jkItemRowList){
                if(tmpRow.equals(pageRow)){
                    realRow = pageRow;
                    break;
                }
            }
            if(realRow != null){
                resultList.add(realRow);
            }else{
                resultList.add(tmpRow);
            }
        }
        this.jkItemRowList = resultList;
        recalculateRowspan(jkItemRowList);
        changeSerItmDetail();
    }
    /**
     * <p>描述 重新计算合并行以及起始行</p>
     *
     * @MethodAuthor gongzhe,2022/4/25 10:12,recalculateRowspan
     * @return void
     */
    private void recalculateRowspan(List<ZwJkItemRowPO> list){
        Map<Integer,Integer> rowspanMap = new HashMap<>();
        for (int i = 0; i < list.size(); i++) {
            ZwJkItemRowPO itemPO = list.get(i);
            TsSimpleCode jcItem = itemPO.getJcItem();
            Integer count = rowspanMap.get(jcItem.getRid());
            count = count == null ? 1 : ++count;
            rowspanMap.put(jcItem.getRid(),count);
        }
        int start = 0;
        for (int i = 0; i < list.size(); i++) {
            ZwJkItemRowPO itemPO = list.get(i);
            TsSimpleCode jcItem = itemPO.getJcItem();
            if(i>0 && i == start+rowspanMap.get(list.get(i-1).getJcItem().getRid())){
                start += rowspanMap.get(list.get(i-1).getJcItem().getRid());
            }
            itemPO.setRowspanStart(start);
            itemPO.setRowspan(rowspanMap.get(jcItem.getRid()));
        }
    }
    /**
     * @Description: 删除申请材料或者资质附件
     *
     * @MethodAuthor pw,2022年04月15日
     */
    public void deleteApplyAnnex(){
        if(null != this.selectAnnex && null != this.annexTypeBase){
            if(null != this.selectAnnex.getRid()){
                this.annexApplyRemoveRidList.add(this.selectAnnex.getRid());
            }
            if(21 == this.annexTypeBase){
                this.applyAnnexList.remove(this.selectAnnex);
            }
        }
    }

    /**
     * @Description: 保存验证 true 不通过验证
     *
     * @MethodAuthor pw,2022年04月15日
     */
    private boolean saveValidate(){
        boolean flag = null == this.orgType || (1 != this.orgType && 2 != this.orgType);
        if(flag || this.addSaveValidate()){
            return true;
        }
        if(this.ifFinalAudit){
            if(StringUtils.isBlank(this.tjorginfoApply.getCertNo())){
                JsfUtil.addErrorMessage("基本信息的备案编号不允许为空！");
                flag = true;
            }
            if(this.ifValidityPeriod){
                if(null == this.tjorginfoApply.getFirstGetday()){
                    JsfUtil.addErrorMessage("基本信息的备案开始日期不允许为空！");
                    flag = true;
                }
                if(null == this.tjorginfoApply.getFilingDate()){
                    JsfUtil.addErrorMessage("基本信息的备案结束日期不允许为空！");
                    flag = true;
                }
            }
        }
        if(null != this.tjorginfoApply.getFirstGetday() && null != this.tjorginfoApply.getFilingDate() &&
                this.tjorginfoApply.getFirstGetday().after(this.tjorginfoApply.getFilingDate())){
            JsfUtil.addErrorMessage("基本信息的备案开始日期不允许大于备案结束日期！");
            flag = true;
        }
        //服务明细的验证
        if(1 == this.orgType){
            if(CollectionUtils.isEmpty(this.selectServiceObjList)){
                JsfUtil.addErrorMessage("基本信息的服务项目不允许为空！");
                flag = true;
            }
        }else{
            ////诊断的特殊判断
        }


        return flag;
    }

    /**
     * @Description: 申报的时候保存验证
     *
     * @MethodAuthor pw,2022年04月27日
     */
    private boolean addSaveValidate(){
        //非申报 不需要验证
        if(null == this.orgInfoState || 0 != this.orgInfoState){
            return false;
        }
        boolean flag = false;
        if(StringUtils.isBlank(this.tjorginfoApply.getOrgName())){
            JsfUtil.addErrorMessage("基本信息的单位名称不允许为空！");
            flag = true;
        }
        if(null == this.orgZoneId){
            JsfUtil.addErrorMessage("基本信息的行政区划所属地区不允许为空！");
            flag = true;
        }else{
            TsZone qualOrgZone = this.tsZoneMap.get(this.orgZoneId);
            if(null == qualOrgZone){
                JsfUtil.addErrorMessage("基本信息的行政区划所属地区异常！");
                flag = true;
            }else if(StringUtils.isBlank(qualOrgZone.getZoneGb())){
                JsfUtil.addErrorMessage("基本信息的行政区划所属地区编码对照失败！");
                flag = true;
            }else if(null != qualOrgZone.getRealZoneType() && qualOrgZone.getRealZoneType() < 4){
                JsfUtil.addErrorMessage("基本信息的行政区划所属地区只能选择区县及以下！");
                flag = true;
            }
            if(null != qualOrgZone){
                this.tjorginfoApply.setFkByZoneId(qualOrgZone);
            }
        }
        if(StringUtils.isBlank(this.tjorginfoApply.getOrgAddr())){
            JsfUtil.addErrorMessage("基本信息的注册地址不允许为空！");
            flag = true;
        }
        if(StringUtils.isBlank(this.tjorginfoApply.getOrgFz())){
            JsfUtil.addErrorMessage("基本信息的法定代表人不允许为空！");
            flag = true;
        }
        if(StringUtils.isBlank(this.tjorginfoApply.getOrgFzzw())){
            JsfUtil.addErrorMessage("基本信息的法定代表人职务不允许为空！");
            flag = true;
        }
        if(StringUtils.isBlank(this.tjorginfoApply.getLinkMan())){
            JsfUtil.addErrorMessage("基本信息的联系人不允许为空！");
            flag = true;
        }
        if(StringUtils.isBlank(this.tjorginfoApply.getLinkMb())){
            JsfUtil.addErrorMessage("基本信息的联系人手机不允许为空！");
            flag = true;
        }else if(!StringUtils.vertyMobilePhone(this.tjorginfoApply.getLinkMb())){
            JsfUtil.addErrorMessage("基本信息的联系人手机格式不正确！");
            flag = true;
        }
        if(StringUtils.isBlank(this.tjorginfoApply.getLinkTel())){
            JsfUtil.addErrorMessage("基本信息的电话不允许为空！");
            flag = true;
        }else if(!StringUtils.vertyFixedPhone(this.tjorginfoApply.getLinkTel())){
            JsfUtil.addErrorMessage("基本信息的电话格式不正确！");
            flag = true;
        }
        if(StringUtils.isNotBlank(this.tjorginfoApply.getZipcode()) &&
                !StringUtils.vertyPost(this.tjorginfoApply.getZipcode())){
            JsfUtil.addErrorMessage("基本信息的邮政编码格式不正确！");
            flag = true;
        }
        if(StringUtils.isNotBlank(this.tjorginfoApply.getEmail()) &&
                !StringUtils.checkEmail(this.tjorginfoApply.getEmail())){
            JsfUtil.addErrorMessage("基本信息的电子邮箱格式不正确！");
            flag = true;
        }
        return flag;
    }

    /**
     * @Description: 申报的时候提交验证
     *
     * @MethodAuthor pw,2022年04月27日
     */
    private boolean addSubmitValidate(){
        boolean flag = false;
        //非申报 不需要验证
        if(null == this.orgInfoState || 0 != this.orgInfoState){
            if(null != this.orgType && 1 == this.orgType){
                for (ZwJkItemPO zwItemPO : this.jkItemList) {
                    //服务类别
                    Integer jcItemRid = zwItemPO.getJcItem().getRid();
                    boolean jcHasSubFlag = false;
                    for (ZwJkItemRowPO zwItemRowPO : this.jkItemRowList) {
                        //服务小类
                        if (jcItemRid.equals(zwItemRowPO.getJcItem().getRid())) {
                            if (zwItemRowPO.getSelectedSerItmDetail() != null && zwItemRowPO.getSelectedSerItmDetail().length == 1) {
                                //该服务类别有小类
                                jcHasSubFlag = true;
                                break;
                            }
                        }
                    }
                    if (!jcHasSubFlag) {
                        JsfUtil.addErrorMessage("每个服务类别中至少选择一项服务项目！");
                        flag = true;
                        break;
                    }
                }
            }
            return flag;
        }
        if(CollectionUtils.isEmpty(this.applyAnnexList)){
            JsfUtil.addErrorMessage("基本信息的申请材料不允许为空！");
            flag = true;
        }
        if (CollectionUtils.isEmpty(this.psnInfoVO.getOrgPsnApplyListView())) {
            JsfUtil.addErrorMessage("人员信息不允许为空！");
            flag = true;
        }

        if (CollectionUtils.isEmpty(this.instInfoVO.getOrgInstApplyListView())) {
            JsfUtil.addErrorMessage("仪器信息不允许为空！");
            flag = true;
        }

        if(1 == this.orgType) {
            for (ZwJkItemPO zwItemPO : this.jkItemList) {
                //服务类别
                Integer jcItemRid = zwItemPO.getJcItem().getRid();
                boolean jcHasSubFlag = false;
                for (ZwJkItemRowPO zwItemRowPO : this.jkItemRowList) {
                    //服务小类
                    if (jcItemRid.equals(zwItemRowPO.getJcItem().getRid())) {
                        if (zwItemRowPO.getSelectedSerItmDetail() != null && zwItemRowPO.getSelectedSerItmDetail().length == 1) {
                            //该服务类别有小类
                            jcHasSubFlag = true;
                            break;
                        }
                    }
                }
                if (!jcHasSubFlag) {
                    JsfUtil.addErrorMessage("每个服务类别中至少选择一项服务项目！");
                    flag = true;
                    break;
                }
            }

            //外检车辆校验
            if(tjorginfoApply.getOutWorkPower()!=null&&1==tjorginfoApply.getOutWorkPower()){
                this.initOutCarList();
                if(CollectionUtils.isEmpty(tjorgOutcarApplyList)){
                    JsfUtil.addErrorMessage("外检车辆不允许为空！");
                    flag = true;
                }
            }

            //质量管理体系验证
            if(null != topOfInstitutionList && topOfInstitutionList.size() > 0 && null != qualAnnexMap && !qualAnnexMap.isEmpty()){
                //必须顺序遍历再验证 否则提示顺序会错误
                for(TsSimpleCode simpleCode : topOfInstitutionList){
                    List<TdZwTjorgQualAnnexApy> list = qualAnnexMap.get(simpleCode.getRid());
                    if(null != list && list.size() > 0){
                        int i = 1;
                        for(TdZwTjorgQualAnnexApy top : list){
                            if(top.getFkByRuleId().getRid() == simpleCode.getRid().intValue() &&
                                    StringUtils.isEmpty(top.getOtherRuleName())){
                                JsfUtil.addErrorMessage(new StringBuilder(simpleCode.getCodeName()).
                                        append("第").append(i).append("行手动添加制度未填写制度名称！").toString());

                                flag = true;
                            }
                            if(null == top.getIfHas()){
                                JsfUtil.addErrorMessage(new StringBuilder(simpleCode.getCodeName()).
                                        append("第").append(i).append("行未选择有无！").toString());
                                flag = true;
                            }
                            if(null != top.getIfHas() && top.getIfHas() == 1 && StringUtils.isEmpty(top.getAnnexPath())){
                                JsfUtil.addErrorMessage(new StringBuilder(simpleCode.getCodeName()).
                                        append("第").append(i).append("行选择有附件，但未上传附件！").toString());
                                flag = true;
                            }
                            i++;
                        }
                    }
                }
            }
        }

        boolean bool=false;
        if(!CollectionUtils.isEmpty(this.tjorginfoApply.getTjitemsApplies())){
            for(TdZwTjitemsApply item:this.tjorginfoApply.getTjitemsApplies()){
                if(item.getIsOutter()!=null&&1==item.getIsOutter()){
                    bool=true;
                    break;
                }
            }
        }
        if(bool){
            bool = 1 == this.orgType;
        }

        //资质附件验证
        if(countMap!=null&&countMap.isEmpty()){
            JsfUtil.addErrorMessage("单位法人证明材料（营业执照）不允许为空！");
            JsfUtil.addErrorMessage("医疗机构执业许可证不允许为空！");
            if(bool){
                JsfUtil.addErrorMessage("委托协议和委托项目明细不允许为空！");
            }
            flag = true;
        }else{
            if(countMap!=null&&countMap.get("1")==null||(countMap!=null&&countMap.get("1")!=null&&countMap.get("1")==0)){
                JsfUtil.addErrorMessage("单位法人证明材料（营业执照）不允许为空！");
                flag = true;
            }
            if(countMap!=null&&countMap.get("2")==null||(countMap!=null&&countMap.get("2")!=null&&countMap.get("2")==0)){
                JsfUtil.addErrorMessage("医疗机构执业许可证不允许为空！");
                flag = true;
            }

            if(countMap!=null&&countMap.get("20")==null||(countMap!=null&&countMap.get("20")!=null&&countMap.get("20")==0&&bool)){
                JsfUtil.addErrorMessage("委托协议和委托项目明细不允许为空！");
                flag = true;
            }
        }
        return flag;
    }

    /**
     * @Description: 提交验证 true不通过验证
     *
     * @MethodAuthor pw,2022年04月15日
     */
    public boolean submitValidate(){
        //避免出现 saveValidate 验证不通过 就不验证addSubmitValidate的情况
        boolean saveFlag = this.saveValidate();
        boolean submitFlag = this.addSubmitValidate();
        return saveFlag || submitFlag;
    }

    /**
     * @Description: tab切换事件
     *
     * @MethodAuthor pw,2022年04月14日
     */
    public void tableChange(TabChangeEvent event) {
        this.tabTitle = event.getTab().getTitle();
    }

    /**
     * @Description: 初始化
     *
     * @MethodAuthor pw,2022年04月14日
     */
    private void initOrgApplyInfo(){
        this.serviceObjList = new ArrayList<>();
        this.selectServiceObjList = new ArrayList<>();
        this.serviceItemNameList = new ArrayList<>();
        this.applyAnnexList = new ArrayList<>();
        this.annexApplyRemoveRidList = new ArrayList<>();
        this.serviceItemSimpleCodeMap = new HashMap<>();
        this.initQualOrgApply();
        this.initPsnInfoList();
        this.initInstInfoList();
        //服务明细初始化
        this.initJkItemRowList();
        if(ifHethFj){
            //外检车辆列表初始化
            this.initOutCarList();
        }
        //质量管理体系初始化
        this.initInstitutionList();
        //资质附件
        this.initAnnex();
        countMap=new HashMap<>();
        if(!CollectionUtils.isEmpty(annexsPOList)) {
            for (ZwAnnexsPO po : annexsPOList) {
                countMap.put(po.getAnnexType().toString(), po.getValues().size());
            }
        }
    }

    /**
     * @Description: 初始化地区
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private void initZoneList(){
        this.tsZoneMap = new HashMap<>();
        String zoneGb = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0,2);
        if(CollectionUtils.isEmpty(this.orgZoneList)){
            this.orgZoneList = this.moduleService.findZoneListWithAllZoneByFlag(false, zoneGb);
        }
        if(CollectionUtils.isEmpty(this.zoneList)){
            this.zoneList = new ArrayList<>();
            if(CollectionUtils.isEmpty(this.allTsZoneList)){
                this.allTsZoneList = this.moduleService.findZoneListWithAllZoneByFlag(true,zoneGb);
            }
            if(!CollectionUtils.isEmpty(this.allTsZoneList)){
                for(TsZone tsZone : this.allTsZoneList){
                    if(null != tsZone.getRealZoneType() && tsZone.getRealZoneType() >= 2 && tsZone.getRealZoneType() <= 5){
                        this.zoneList.add(tsZone);
                    }
                }
            }
        }
        if(CollectionUtils.isEmpty(this.orgZoneList)){
            return;
        }
        for(TsZone tsZone : this.orgZoneList){
            this.tsZoneMap.put(tsZone.getRid(), tsZone);
        }
    }

    /**
     * @Description: 初始化参数
     *
     * @MethodAuthor pw,2022年04月15日
     */
    private void initParams(){
        //必要的码表 检查allSimpleCodeMap是否有
        this.initAllSimpleCodeMap();
        //初始化法人职务
        this.initCareerList();
        //初始化地区
        this.initZoneList();
    }

    /**
     * @Description: 初始化服务项目对象
     *
     * @MethodAuthor pw,2022年04月15日
     */
    private void initServiceObjList(){
        if(null == this.orgType){
            return;
        }
        List<TsSimpleCode> serviceItemList = null;
        if(1 == this.orgType){
            serviceItemList = this.allSimpleCodeMap.get("5018");
        }else if(2 == this.orgType){
            serviceItemList = this.allSimpleCodeMap.get("5020");
        }else{
            return;
        }
        if(!CollectionUtils.isEmpty(serviceItemList)){
            for(TsSimpleCode t : serviceItemList){
                this.serviceObjList.add(new SelectItem(t.getCodeNo(),t.getCodeName()));
                this.serviceItemSimpleCodeMap.put(t.getCodeNo(), t);
            }
        }
    }

    /**
     * @Description: 初始化资质机构备案主表
     *
     * @MethodAuthor pw,2022年04月14日
     */
    private void initQualOrgApply(){
        this.tjorginfoApply = this.orgApplyService.find(TdZwTjorginfoApply.class, this.orgApplyRid);
        if(null == this.tjorginfoApply){
            return;
        }
        this.orgInfoState = this.tjorginfoApply.getState();
        TsZone tsZone = null == this.tjorginfoApply ? null : this.tjorginfoApply.getFkByZoneId();
        if(null != tsZone){
            this.orgZoneName = tsZone.getZoneName();
            this.orgZoneId = tsZone.getRid();
        }
        this.qualType = this.tjorginfoApply.getFkByQualTypeId();
        this.applyType = this.tjorginfoApply.getFkByApplyTypeId();
        //初始化基本信息
        this.initOrginfoApply();
        if(null == this.orgInfoState || 0 == this.orgInfoState ||
                (this.ifView && 1 == this.orgInfoState)){
            //填报页初始化码表以及地区
            this.initParams();
        }

        if((null == this.orgInfoState || 0 == this.orgInfoState ||
                (this.ifView && 1 == this.orgInfoState) || !this.ifView)){
            //初始化服务项目对象
            this.initServiceObjList();
        }
        if(null != this.orgType && 1 == this.orgType){
            //检查项目
            initZwJcItemList();
        }
        this.fillItemsApplyList();
    }

    /**
     * @Description: 初始化基本信息[机构备案]
     *
     * @MethodAuthor pw,2022年04月14日
     */
    private void initOrginfoApply(){
        String qualTypeExt1 = null == this.qualType ? null : this.qualType.getExtendS1().trim();
        if(StringUtils.isBlank(qualTypeExt1)){
            return;
        }
        if("1".equals(qualTypeExt1)){
            this.orgType = 1;
        }else if("2".equals(qualTypeExt1)){
            this.orgType = 2;
        }
        this.fillAnnexList();
    }

    /**
     * @Description: 初始化法人职务
     *
     * @MethodAuthor pw,2022年04月15日
     */
    public void initCareerList() {
        //仅初始化一次
        if(!CollectionUtils.isEmpty(this.careerList)){
            return;
        }
        this.careerList = new ArrayList<>();
        List<TsSimpleCode> list = this.allSimpleCodeMap.get("2002");
        if (!CollectionUtils.isEmpty(list)) {
            for (TsSimpleCode t : list) {
                this.careerList.add(t.getCodeName());
            }
        }
    }

    /**
     * @Description: 申请材料以及资质附件List赋值
     *
     * @MethodAuthor pw,2022年04月15日
     */
    private void fillAnnexList(){
        if(null == this.orgType){
            return;
        }else if(null != this.tjorginfoApply && null != this.tjorginfoApply.getRid() &&
                (1 == this.orgType || 2 == this.orgType)){
            this.fillTjorgAnnexApplyList();
        }
    }

    /**
     * @Description: 服务项目赋值
     *
     * @MethodAuthor pw,2022年04月15日
     */
    private void fillItemsApplyList(){
        boolean flag = null != this.orgType && null != this.tjorginfoApply && null != this.tjorginfoApply.getRid() &&
                (1 == this.orgType || 2 == this.orgType);
        if(flag){
            this.fillTjorgItemsApplyList();
        }
    }

    /**
     * @Description: 查询服务项目并赋值selectServiceObjList
     *
     * @MethodAuthor pw,2022年04月15日
     */
    private void fillTjorgItemsApplyList(){
        tdZwTjorgItmDetailApyMap = new HashMap<>();
        List<TdZwTjorggitemsApply> tjorggitemsApplyList = this.orgApplyService.findEntityListByOtMainId(TdZwTjorggitemsApply.class,
                "fkByOrgId", this.tjorginfoApply.getRid());
        if(CollectionUtils.isEmpty(tjorggitemsApplyList)){
            return;
        }
        //排序
        Collections.sort(tjorggitemsApplyList, new Comparator<TdZwTjorggitemsApply>() {
            @Override
            public int compare(TdZwTjorggitemsApply o1, TdZwTjorggitemsApply o2) {
                Integer num1 = null == o1.getFkByItemId() ? null : o1.getFkByItemId().getNum();
                Integer num2 = null == o2.getFkByItemId() ? null : o2.getFkByItemId().getNum();
                if(null != num1 && null != num2){
                    return num1.compareTo(num2);
                }else if(null == num1 && null != num2){
                    return -1;
                }else if(null == num2 && null != num1){
                    return 1;
                }
                return 0;
            }
        });
        this.tjorginfoApply.setTjorggitemsApplyList(tjorggitemsApplyList);
        for(TdZwTjorggitemsApply itemApply : tjorggitemsApplyList){
            String itemCode = null == itemApply || null == itemApply.getFkByItemId() ? null : itemApply.getFkByItemId().getCodeNo();
            if(StringUtils.isNotBlank(itemCode) && !this.selectServiceObjList.contains(itemCode)){
                this.selectServiceObjList.add(itemCode);
                this.serviceItemNameList.add(itemApply.getFkByItemId().getCodeName());
            }
        }
        //服务明细小类
        List<TdZwTjorgItmDetailApy> itmDetailList = this.orgApplyService.findEntityListByOtMainId(TdZwTjorgItmDetailApy.class,
                "fkByMainId.fkByOrgId", this.tjorginfoApply.getRid());
        this.tjorginfoApply.setTjorgItmDetailApies(itmDetailList);
        if(!CollectionUtils.isEmpty(itmDetailList)){
            for(TdZwTjorgItmDetailApy t : itmDetailList){
                if(tdZwTjorgItmDetailApyMap.containsKey(t.getFkByMainId().getRid())){
                    tdZwTjorgItmDetailApyMap.get(t.getFkByMainId().getRid()).add(t);
                }else {
                    List<TdZwTjorgItmDetailApy> tt = new ArrayList<>();
                    tt.add(t);
                    tdZwTjorgItmDetailApyMap.put(t.getFkByMainId().getRid(),tt);
                }
            }
        }
    }

    /**
     * @Description: 资质附件赋值
     *
     * @MethodAuthor pw,2022年04月15日
     */
    private void fillTjorgAnnexApplyList(){
        this.tjorgannexApplyList = this.orgApplyService.findEntityListByOtMainId(TdZwTjorgannexApply.class,
                "fkByOrgId", this.tjorginfoApply.getRid());
        if(CollectionUtils.isEmpty(this.tjorgannexApplyList)){
            return;
        }
        for(TdZwTjorgannexApply tjorgannexApply : this.tjorgannexApplyList){
            Integer annexType = tjorgannexApply.getAnnexType();
            if(null != annexType){
                if(21 == annexType){
                    this.applyAnnexList.add(tjorgannexApply);
                }else if(6 == annexType){
                    this.reportReceipt = tjorgannexApply;
                    this.reportReceiptFilePath = this.reportReceipt.getFilePath();
                }
            }
        }
    }

    /**
     * @Description: 必要的码表检查
     *
     * @MethodAuthor pw,2022年04月27日
     */
    private void necessSimpleCodeCheck(){
        //质量管理体系
        this.initAllSimpleCodeMapHelper("5507", false);
        //检查项目
        this.initAllSimpleCodeMapHelper("5015",false);
        //职业健康检查机构服务项目
        this.initAllSimpleCodeMapHelper("5018", false);
        //健康检查资质服务小类
        List<TsSimpleCode> list = this.allSimpleCodeMap.get("5527");
        if(CollectionUtils.isEmpty(list)){
            list = this.commService.findallSimpleCodesByTypeIdAndExtends2("5527",null);
            this.allSimpleCodeMap.put("5527", list);
        }
    }

    /**
     * @Description: 初始化码表集合
     * 需要用到的码表 自己添加
     * @MethodAuthor pw,2022年04月18日
     */
    private void initAllSimpleCodeMap(){
        //法人职务
        this.initAllSimpleCodeMapHelper("2002", false);
        //诊断机构服务项目
        this.initAllSimpleCodeMapHelper("5020", false);
    }

    /**
     * @Description: 辅助初始化码表
     *
     * @MethodAuthor pw,2022年04月18日
     */
    private void initAllSimpleCodeMapHelper(String typeNo, boolean ifGetAll){
        List<TsSimpleCode> list = this.allSimpleCodeMap.get(typeNo);
        if(CollectionUtils.isEmpty(list)){
            list = ifGetAll ? this.commService.findallSimpleCodesByTypeIdOrderByNum(typeNo) :
                    this.commService.findLevelSimpleCodesByTypeId(typeNo);
            this.allSimpleCodeMap.put(typeNo, list);
        }
    }

    /**
     * @Description: 是否显示外检车辆信息点
     *
     * @MethodAuthor pw,2022年04月15日
     */
    public Boolean getIfOutWork() {
        Integer outWorkPower = null == this.tjorginfoApply ? null : this.tjorginfoApply.getOutWorkPower();
        ifOutWork = null != outWorkPower && 1 == outWorkPower;
        return ifOutWork;
    }

    /**
     * @Description: 是否显示备案编号信息
     *
     * @MethodAuthor pw,2022年04月16日
     */
    public Boolean getIfBaseInfoApplyShow() {
        //当申请类型是资质变更 或者 终审进来的 或者终审通过状态的 显示备案编号信息点
        ifBaseInfoApplyShow = null != this.applyType &&
                ("2".equals(this.applyType.getExtendS1()) ||
                        this.ifFinalAudit ||
                        (null != this.orgInfoState && 5 == this.orgInfoState));
        return ifBaseInfoApplyShow;
    }

    /**
     * @Description: 基础信息的通用信息部分是否可编辑
     *
     * @MethodAuthor pw,2022年04月16日
     */
    public Boolean getIfBaseInfoCommInfoEdit() {
        ifBaseInfoCommInfoEdit = !this.ifView && (null != this.orgInfoState && 0 == this.orgInfoState);
        return ifBaseInfoCommInfoEdit;
    }

    /**
     * @Description: 基础信息的服务项目部分是否可编辑
     *
     * @MethodAuthor pw,2022年04月16日
     */
    public Boolean getIfBaseInfoServiceInfoEdit() {
        //编辑页都可以编辑服务项目
        ifBaseInfoServiceInfoEdit = !this.ifView && (this.reportReceipt == null || StringUtils.isBlank(this.reportReceipt.getFilePath()));
        return ifBaseInfoServiceInfoEdit;
    }

    /**
     * @Description: 基础信息的备案编号部分是否可编辑
     *
     * @MethodAuthor pw,2022年04月16日
     */
    public Boolean getIfBaseInfoApplyInfoEdit() {
        //编辑页并且是终审进来的 可编辑备案编号信息
        ifBaseInfoApplyInfoEdit = !this.ifView && this.ifFinalAudit &&
                (this.reportReceipt == null || StringUtils.isBlank(this.reportReceipt.getFilePath()));
        return ifBaseInfoApplyInfoEdit;
    }

    /**
     *  <p>方法描述：检查项目添加</p>
     * @MethodAuthor hsj 2022/4/18 16:46
     */
    public void addItemInitAction() {
        this.initJcItemList();
    }
    /**
     *  <p>方法描述：检查项目添加初始化</p>
     * @MethodAuthor hsj 2022/4/18 16:47
     */
    private void initJcItemList() {
        if (null == diagMap) {// 5015
            selCodeRidMap = new HashMap<>();
            List<TsSimpleCode> list = this.allSimpleCodeMap.get("5015");
            // 选中的检查项目ids
            if (null != list && list.size() > 0) {
                // 页面展示码表Map
                diagMap = new LinkedHashMap<TsSimpleCode, List<TsSimpleCode>>();
                for (TsSimpleCode code : list) {
                    if (!StringUtils.contains(code.getCodeLevelNo(), ".")) {
                        diagMap.put(code, new ArrayList<TsSimpleCode>());
                    }
                }
                Iterator<TsSimpleCode> it = diagMap.keySet().iterator();
                while (it.hasNext()) {
                    // 父级编码
                    TsSimpleCode parentCode = it.next();
                    String parentC = parentCode.getCodeNo();
                    // 清空选择和子集合
                    parentCode.setIfSelected(false);
                    parentCode.setSelCodeRids(null);
                    // 选中的Id
                    List<Integer> selList = new ArrayList<Integer>();
                    for (TsSimpleCode code : list) {
                        if (code.getCodeLevelNo().startsWith(parentC + ".")) {
                            diagMap.get(parentCode).add(code);
                            if(!CollectionUtils.isEmpty(jcItems) && jcItems.contains( code.getRid().toString())){
                                selList.add(code.getRid());
                            }
                        }
                    }
                    if (selList.size() > 0) {
                        String[] rids = new String[selList.size()];
                        for (int i = 0; i < selList.size(); i++) {
                            rids[i] = String.valueOf(selList.get(i));
                        }
                        parentCode.setSelCodeRids(rids);
                        selCodeRidMap.put(parentCode.getRid(),rids);
                        if (rids.length == diagMap.get(parentCode).size()) {
                            parentCode.setIfSelected(true);
                        }
                    }
                }
            }
        }else{
            //已选择的项目赋值
            if(null != selCodeRidMap){
                Iterator<TsSimpleCode> it = diagMap.keySet().iterator();
                while (it.hasNext()) {
                    TsSimpleCode parentCode = it.next();
                    parentCode.setSelCodeRids(selCodeRidMap.get(parentCode.getRid()));
                }
            }

        }
    }
    /**
     *  <p>方法描述：初始化检查项目</p>
     * @MethodAuthor hsj 2022/4/19 10:08
     */
    public void initZwJcItemList(){
        //已保存的服务项目
        jcItems = new ArrayList<>();
        List<TdZwTjitemsApply> simList =   this.orgApplyService.findEntityListByOtMainId(TdZwTjitemsApply.class,
                "fkByOrgId", this.tjorginfoApply.getRid());
        if(!CollectionUtils.isEmpty(simList)){
            this.tjorginfoApply.setTjitemsApplies(simList);
            for(TdZwTjitemsApply tdZwTjitemsApply:simList){
                jcItems.add(tdZwTjitemsApply.getFkByItemId().getRid().toString());
            }
        }
        initZwJcItems();
    }
    /**
     *  <p>方法描述：初始化检查项目页面展示集合</p>
     * @MethodAuthor hsj 2022/4/19 10:08
     */
    public void initZwJcItems() {
        diagMap = null;
        showItemList = new ArrayList<>();
        // 初始化选择检查项目
        this.initJcItemList();
        // 封装页面展示集合
        if (!CollectionUtils.isEmpty(jcItems)) {
            // 封装页面展示集合
            if (diagMap != null) {
                Iterator<TsSimpleCode> iterator = diagMap.keySet().iterator();
                // 序号
                int xh = 1;
                while (iterator.hasNext()) {
                    // 存放子集数据
                    List<String[]> subList = new ArrayList<String[]>();
                    TsSimpleCode next = iterator.next();
                    List<TsSimpleCode> list = diagMap.get(next);
                    if (null != list && list.size() > 0) {
                        for (TsSimpleCode tscode : list) {
                            String rid = tscode.getRid().toString();
                            if(jcItems.contains(rid)){
                                String[] str=new String[2];
                                str[0]=tscode.getCodeName();
                                str[1]=rid;
                                subList.add(str);
                            }
                        }
                        if (subList.size() > 0) {
                            for (int i = 0; i < subList.size(); i++) {
                                String[] str = subList.get(i);
                                TjitemsApplyVo tjitemsApplyVo = new TjitemsApplyVo();
                                tjitemsApplyVo.setXh(xh);
                                tjitemsApplyVo.setItemName(str[0].toString());
                                tjitemsApplyVo.setItemPro(next.getCodeName());
                                tjitemsApplyVo.setRid(str[1].toString());
                                tjitemsApplyVo.setIsFirst(i == 0);
                                tjitemsApplyVo.setRowspan(subList.size());
                                if(!CollectionUtils.isEmpty(this.tjorginfoApply.getTjitemsApplies())){
                                    for(TdZwTjitemsApply item:this.tjorginfoApply.getTjitemsApplies()){
                                        if(null != str[1] && str[1].toString().equals(item.getFkByItemId().getRid().toString())){
                                            if(null != item.getIsOutter() && item.getIsOutter().intValue()==1){
                                                tjitemsApplyVo.setIsOutter(true);
                                                showItemList.add(tjitemsApplyVo);
                                                continue;
                                            }else{
                                                tjitemsApplyVo.setIsOutter(false);
                                                showItemList.add(tjitemsApplyVo);
                                                continue;
                                            }
                                        }
                                    }
                                }else{
                                    tjitemsApplyVo.setIsOutter(false);
                                    showItemList.add(tjitemsApplyVo);
                                }
                                xh++;
                            }
                            subList.clear();
                        }
                    }
                }
            }
        }
    }
    /**
     *  <p>方法描述：选中检查项目</p>
     * @MethodAuthor hsj 2022/4/19 10:08
     */
    public void selectItem() {
        Iterator<TsSimpleCode> it = diagMap.keySet().iterator();
        while (it.hasNext()) {
            // 父级编码
            TsSimpleCode parentCode = it.next();
            if (parentCode.getRid().toString().equals(selItemId)) {
                List<TsSimpleCode> list = diagMap.get(parentCode);
                String[] selRids = new String[list.size()];
                int i = 0;
                for (TsSimpleCode sc : list) {
                    if (parentCode.isIfSelected()) {
                        selRids[i] = sc.getRid().toString();
                        i++;
                    }
                }
                parentCode.setSelCodeRids(selRids);
                selCodeRidMap.put(parentCode.getRid(),selRids);
                return;
            }
        }
    }
    /**
     *  <p>方法描述：检查项目保存</p>
     * @MethodAuthor hsj 2022/4/19 10:19
     */
    public void checkItemSaveAction() {
        // 保存检查项目时，进行处理检查项目
        this.handleTjItemSave();
        this.initZwJcItems();
    }

    /**
     *  <p>方法描述：保存检查项目</p>
     * @MethodAuthor hsj 2022/4/19 10:19
     */
    public void handleTjItemSave() {
        jcItems = new ArrayList<>();
        tjorginfoApply.setTjitemsApplies(new ArrayList<TdZwTjitemsApply>());
        if (null != diagMap && diagMap.size() > 0) {
            //已选择的检查项目
            List<String> listRids = new ArrayList<>();
            //外检的检查项目
            List<String> listSelRids = new ArrayList<>();
            if(null != showItemList && showItemList.size()>0){
                for(TjitemsApplyVo obj:showItemList){
                    listRids.add(obj.getRid());
                    if(null != obj.getIsOutter() && "true".equals(obj.getIsOutter())){
                        listSelRids.add(obj.getRid());
                    }
                }
            }
            Iterator<TsSimpleCode> iterator = diagMap.keySet().iterator();
            TdZwTjitemsApply tdZwTjitems = null;
            while (iterator.hasNext()) {
                TsSimpleCode next = iterator.next();
                String[] selCodeRids = next.getSelCodeRids();
                if (null != selCodeRids && selCodeRids.length > 0) {
                    for (String s : selCodeRids) {
                        if(listRids.contains(s)){
                            for(TjitemsApplyVo obj:showItemList){
                                if(s.equals(obj.getRid())){
                                    tdZwTjitems = new TdZwTjitemsApply();
                                    tdZwTjitems.setFkByOrgId(tjorginfoApply);
                                    tdZwTjitems.setCreateDate(new Date());
                                    tdZwTjitems.setCreateManid(Global.getUser().getRid());
                                    tdZwTjitems.setFkByItemId(new TsSimpleCode(Integer.valueOf(s)));
                                    if(null != obj.getIsOutter() && "true".equals(obj.getIsOutter().toString())){
                                        tdZwTjitems.setIsOutter(1);
                                    }
                                    this.tjorginfoApply.getTjitemsApplies().add(tdZwTjitems);
                                    jcItems.add(s);
                                    continue;
                                }
                            }
                        }else if(!CollectionUtils.isEmpty(listSelRids) && listSelRids.contains(s)){
                            tdZwTjitems = new TdZwTjitemsApply();
                            tdZwTjitems.setFkByOrgId(tjorginfoApply);
                            tdZwTjitems.setCreateDate(new Date());
                            tdZwTjitems.setCreateManid(Global.getUser().getRid());
                            tdZwTjitems.setFkByItemId(new TsSimpleCode(Integer.valueOf(s)));
                            tdZwTjitems.setIsOutter(1);
                            this.tjorginfoApply.getTjitemsApplies().add(tdZwTjitems);
                            jcItems.add(s);
                        }else{
                            tdZwTjitems = new TdZwTjitemsApply();
                            tdZwTjitems.setFkByOrgId(tjorginfoApply);
                            tdZwTjitems.setCreateDate(new Date());
                            tdZwTjitems.setCreateManid(Global.getUser().getRid());
                            tdZwTjitems.setFkByItemId(new TsSimpleCode(Integer.valueOf(s)));;
                            this.tjorginfoApply.getTjitemsApplies().add(tdZwTjitems);
                            jcItems.add(s);
                        }
                    }
                }
            }
        }
    }
    /**
     *  <p>方法描述：是否外包选择</p>
     * @MethodAuthor hsj 2022/4/19 14:58
     */
    public void changeIsOutter(TjitemsApplyVo vo){
        for(TdZwTjitemsApply obj:this.tjorginfoApply.getTjitemsApplies()){
            if(vo.getRid().equals(obj.getFkByItemId().getRid().toString())){
                if(vo.getIsOutter()){
                    obj.setIsOutter(1);
                }else {
                    obj.setIsOutter(null);
                }
                break;
            }
        }
    }
    /**
     * @Description: 上传资质信息
     *
     * @MethodAuthor pw,2022年04月18日
     */
    public synchronized void handleFileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();
            String contentType = file.getContentType().toLowerCase();
            try {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
            } catch (IOException e) {
                FacesMessage msg = new FacesMessage("上传失败", fileName + "上传失败！");
                FacesContext.getCurrentInstance().addMessage("上传失败", msg);
                e.printStackTrace();
                return;
            }
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
            String filePath = JsfUtil.getAbsolutePath() + "heth/zworgannex/" + uuid + "." + hz;
            String showDir = "/heth/zworgannex/" + uuid + "." + hz;
            TdZwTjorgannexApply annex = new TdZwTjorgannexApply();
            annex.setFilePath(showDir);
            annex.setFileName(fileName);
            annex.setAnnexType(this.annexTypeBase);
            annex.setFkByOrgId(this.tjorginfoApply);
            try {
                FileUtils.copyFile(filePath, file.getInputstream());
                if(null != this.annexTypeBase && 21 == this.annexTypeBase){
                    this.applyAnnexList.add(annex);
                }else if(null != this.annexTypeBase && 6 == this.annexTypeBase){
                    this.reportReceipt = annex;
                    //保存备案回执
                    this.orgApplyService.upsertEntity(this.reportReceipt);
                    //附件重新初始化
                    this.initAnnex();
                    //服务明细初始化
                    this.initJkItemRowList();
                }else if(null != this.annexTypeBase && 2 == this.annexTypeBase){
                    if(countMap.get(String.valueOf(annexTypeBase))!=null){
                        countMap.put(String.valueOf(annexTypeBase),countMap.get(String.valueOf(annexTypeBase))+1);
                    }else{
                        countMap.put(String.valueOf(annexTypeBase),1);
                    }
                    ylAnnex=new TdZwTjorgannexApply();
                    ylAnnex.setFilePath(showDir);
                    ylAnnex.setFileName(fileName);
                    ylAnnex.setAnnexType(this.annexTypeBase);
                    ylAnnex.setFkByOrgId(this.tjorginfoApply);
                    ylAnnex.setCreateDate(new Date());
                    ylAnnex.setCreateManid(Global.getUser().getRid());
                    //selectAnnexType赋值
                    this.selectAnnexType = this.annexTypeBase;
                    JsfUtil.addSuccessMessage("上传成功！");
                    RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:photo");
                    RequestContext.getCurrentInstance().execute("PF('YlFileUId').hide();");
                    return;
                }
                JsfUtil.addSuccessMessage("上传成功！");
                RequestContext.getCurrentInstance().update("tabView:editForm");
                RequestContext.getCurrentInstance().execute("fileUIdVar.hide();");
                //update 放在fileUpload 组件内
            } catch (IOException e) {
                if(null != this.annexTypeBase && 6 == this.annexTypeBase){
                    this.reportReceipt = new TdZwTjorgannexApply();
                }
                FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
                FacesContext.getCurrentInstance().addMessage("上传失败", msg);
                e.printStackTrace();
            }
        }
    }

    /**
     * <p>方法描述：资质附件上传</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-24
     **/
    public synchronized void handleFileZZUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();
            String contentType = file.getContentType().toLowerCase();
            try {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
            } catch (IOException e) {
                FacesMessage msg = new FacesMessage("上传失败", fileName + "上传失败！");
                FacesContext.getCurrentInstance().addMessage("上传失败", msg);
                e.printStackTrace();
            }
            String uuid = UUID.randomUUID().toString().replaceAll("-", "");
            String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
            String filePath = JsfUtil.getAbsolutePath() + "heth/zworgannex/" + uuid + "." + hz;
            String showDir = "/heth/zworgannex/" + uuid + "." + hz;
            TdZwTjorgannexApply annex = new TdZwTjorgannexApply();
            annex.setFilePath(showDir);
            annex.setFileName(fileName);
            annex.setAnnexType(this.selectAnnexType);
            annex.setFkByOrgId(this.tjorginfoApply);
            try {
                FileUtils.copyFile(filePath, file.getInputstream());
                if(countMap.get(String.valueOf(selectAnnexType))!=null){
                    countMap.put(String.valueOf(selectAnnexType),countMap.get(String.valueOf(selectAnnexType))+1);
                }else{
                    countMap.put(String.valueOf(selectAnnexType),1);
                }
                annex.setCreateDate(new Date());
                annex.setCreateManid(Global.getUser().getRid());
                commService.saveObj(annex);
                this.initAnnex();
                JsfUtil.addSuccessMessage("上传成功！");
                RequestContext.getCurrentInstance().execute("PF('FileUIdVarUpload').hide();");

            } catch (IOException e) {
                FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
                FacesContext.getCurrentInstance().addMessage("上传失败", msg);
                e.printStackTrace();
            }
        }
    }


    /**
     * <p>方法描述：外检车辆初始化列表</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-18
     **/
    public void initOutCarList(){
        outcarZzsb=new TdZwTjorgOutcarApply();
        tjorgOutcarApplyList=new ArrayList<>();
        tjorgOutcarApplyList=orgApplyService.getOutcarApplyList(this.orgApplyRid);
        this.tjorginfoApply.setTjorgOutcarApplyList(tjorgOutcarApplyList);
    }

    /**
     * <p>方法描述：质量管理体系数据初始化</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void initInstitutionList(){
        institutionList= this.allSimpleCodeMap.get("5507");
        if(null == institutionList || institutionList.size() == 0){
            return;// 码表无值 停止
        }
        topOfInstitutionList = new ArrayList<>();
        topInstitutionMap = new HashMap<>();
        institutionMap = new HashMap<>();
        qualAnnexMap = new HashMap<>();
        StringBuilder builder = new StringBuilder("");
        //初始化顶级
        for(TsSimpleCode tsCode : institutionList){
            if(null != tsCode.getCodeLevelNo() && !tsCode.getCodeLevelNo().contains(".")){
                //码表扩展字段2无值 或者不是1 或者具备外出开展职业健康检查工作能力
                boolean flag = null == tsCode.getExtendS2() || 1 != tsCode.getExtendS2() ||
                        (null != tjorginfoApply.getOutWorkPower() && 1 == tjorginfoApply.getOutWorkPower());
                if(flag){
                    topOfInstitutionList.add(tsCode);
                    topInstitutionMap.put(tsCode.getRid(),tsCode);
                    builder.append(",topInsPanel").append(tsCode.getRid());
                }
            }
        }
        topInsPanelStr = builder.toString();
        for(TsSimpleCode tsChCode : institutionList){
            for(TsSimpleCode top : topOfInstitutionList){
                if(null != tsChCode.getCodeLevelNo() && tsChCode.getCodeLevelNo().contains(".") &&
                        tsChCode.getCodeLevelNo().startsWith(top.getCodeLevelNo())){
                    List<TsSimpleCode> tsList = institutionMap.get(top.getRid());
                    if(null == tsList){
                        tsList = new ArrayList<>();
                    }
                    tsList.add(tsChCode);
                    institutionMap.put(top.getRid(),tsList);
                }
            }
        }
        if(null != institutionMap && !institutionMap.isEmpty()){
            for(Map.Entry<Integer, List<TsSimpleCode>> mapTmp : institutionMap.entrySet()){
                Integer mapKey = mapTmp.getKey();
                List<TsSimpleCode> tsTmpList = mapTmp.getValue();
                List<TdZwTjorgQualAnnexApy> annexList = qualAnnexMap.get(mapKey);
                if(null == annexList){
                    annexList = new ArrayList<>();
                }
                if(null != tsTmpList && tsTmpList.size() > 0){
                    for(TsSimpleCode tsCode : tsTmpList){
                        annexList.add(new TdZwTjorgQualAnnexApy(tsCode));
                    }
                }
                qualAnnexMap.put(mapKey,annexList);
            }
        }
        if(null != orgApplyRid){
            String hql = " from TdZwTjorgQualAnnexApy t where t.fkByMainId.rid = " + orgApplyRid;
            List<TdZwTjorgQualAnnexApy> annexList = commService.findData(hql, null);
            this.tjorginfoApply.setTjorgQualAnnexApies(annexList);
            if(annexList != null && annexList.size() > 0){
                for(TdZwTjorgQualAnnexApy annex : annexList){
                    if(null != topInstitutionMap && !topInstitutionMap.isEmpty() &&
                            null != topInstitutionMap.get(annex.getFkByRuleId().getRid())){
                        List<TdZwTjorgQualAnnexApy> tmpAnnexList = qualAnnexMap.get(annex.getFkByRuleId().getRid());
                        if(null == tmpAnnexList){
                            tmpAnnexList = new ArrayList<>();
                        }
                        tmpAnnexList.add(annex);
                        qualAnnexMap.put(annex.getFkByRuleId().getRid(), tmpAnnexList);
                        continue;
                    }

                    for(Map.Entry<Integer,List<TdZwTjorgQualAnnexApy>> tmpMap : qualAnnexMap.entrySet()){
                        List<TdZwTjorgQualAnnexApy> tmpList = tmpMap.getValue();
                        boolean flag = false;
                        if(null != tmpList && tmpList.size() > 0){
                            for(TdZwTjorgQualAnnexApy qualTmp : tmpList){
                                if(qualTmp.getFkByRuleId().getRid() ==
                                        annex.getFkByRuleId().getRid().intValue()){
                                    qualTmp.setRid(annex.getRid());
                                    qualTmp.setFkByMainId(annex.getFkByMainId());
                                    qualTmp.setFkByRuleId(annex.getFkByRuleId());
                                    qualTmp.setOtherRuleName(annex.getOtherRuleName());
                                    qualTmp.setIfHas(annex.getIfHas());
                                    qualTmp.setAnnexPath(annex.getAnnexPath());
                                    qualTmp.setCreateDate(annex.getCreateDate());
                                    qualTmp.setCreateManid(annex.getCreateManid());
                                    qualTmp.setModifyManid(annex.getModifyManid());
                                    qualTmp.setModifyDate(annex.getModifyDate());
                                    flag = true;
                                    break;
                                }
                            }
                            if(flag){
                                break;
                            }
                        }
                        if(flag){
                            break;
                        }
                    }
                }
            }
        }
    }

    /** 质量管理体系  添加按钮 */
    public void institutionAddOne(){
        if(null != codeId && null != qualAnnexMap && !qualAnnexMap.isEmpty()
                && null != topInstitutionMap && !topInstitutionMap.isEmpty()){
            TsSimpleCode top = topInstitutionMap.get(codeId);
            if(null == top){
                return;
            }
            List<TdZwTjorgQualAnnexApy> list = qualAnnexMap.get(codeId);
            if(null == list){
                list = new ArrayList<>();
            }
            TdZwTjorgQualAnnexApy tmp = new TdZwTjorgQualAnnexApy(top);
            tmp.setIfHas(1);
            list.add(tmp);
            codeId = null;
        }
    }
    /** 质量管理体系 附件删除 */
    public void delInsFile(){
        if(null != annexTmp){
            boolean flag = false;
            for(Map.Entry<Integer,List<TdZwTjorgQualAnnexApy>> map : qualAnnexMap.entrySet()){
                if(flag){
                    break;
                }
                Integer key = map.getKey();
                List<TdZwTjorgQualAnnexApy> list = map.getValue();
                if(null != list && list.size() > 0){
                    for(TdZwTjorgQualAnnexApy tmp : list){
                        if(tmp.equals(annexTmp)){
                            tmp.setAnnexPath(null);
                            flag = true;
                            break;
                        }
                    }
                }
            }
        }
    }
    /** 质量管理体系 删除 */
    public void institutionDeleteOne(){
        if(null != annexTmp && qualAnnexMap != null && !qualAnnexMap.isEmpty()){
            for(Map.Entry<Integer,List<TdZwTjorgQualAnnexApy>> map : qualAnnexMap.entrySet()){
                Integer key = map.getKey();
                List<TdZwTjorgQualAnnexApy> list = map.getValue();
                if(null != list && list.size() > 0){
                    if(list.contains(annexTmp)){
                        list.remove(annexTmp);
                        annexTmp = null;
                        break;
                    }
                }
            }
        }
    }

    /**
     * 质量管理体系 文件上传
     * @param event
     */
    public void institutionAnnexFileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String fileName = file.getFileName();// 文件名称
                String contentType = file.getContentType().toLowerCase();
                String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String uuid = UUID.randomUUID().toString()
                        .replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                //rpt文件夹
                String relativePath = new StringBuffer("tjorgannex/")
                        .append(uuid)
                        .append(fileName.substring(fileName
                                .lastIndexOf("."))).toString();
                // 文件路径
                String filePath = new StringBuilder(path).append(
                        relativePath).toString();

                FileUtils.copyFile(filePath, file.getInputstream());
                RequestContext.getCurrentInstance().execute(
                        "PF('TjorgAnnexFileDialog').hide()");
                if(null != annexTmp && null != qualAnnexMap && !qualAnnexMap.isEmpty()){
                    boolean flag = false;
                    for(Map.Entry<Integer,List<TdZwTjorgQualAnnexApy>> mapTmp : qualAnnexMap.entrySet()){
                        Integer key = mapTmp.getKey();
                        List<TdZwTjorgQualAnnexApy> list = mapTmp.getValue();
                        for(TdZwTjorgQualAnnexApy annex : list){
                            if(annex.equals(annexTmp)){
                                annex.setAnnexPath(relativePath);
                                flag = true;
                                break;
                            }
                        }
                        if(flag){
                            break;
                        }
                    }
                }
                JsfUtil.addSuccessMessage("上传成功！");
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }


    /**
     * <p>方法描述：附件初始化</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void  initAnnex(){
        annexsPOList=new ArrayList<>();
        Map<String,List<Object[]>> annexTypeMap=new LinkedHashMap<>();
        //初始化资质附件列表
        List<Object[]> annexList= orgApplyService.findAnnexList(this.orgApplyRid);
        tjorginfoApply.setAnnexList(annexList);
        initAnnexList(annexTypeMap,annexList);
        packageAnnexs(annexTypeMap,annexsPOList);
    }

    /**
     * <p>方法描述：初始化资质附件初始列表</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-20
     **/
    public void initAnnexList(Map<String,List<Object[]>> annexTypeMap,List<Object[]> annexList){
        if(1==orgType) {
            annexTypeMap.put("1",new ArrayList<Object[]>());
            annexTypeMap.put("2",new ArrayList<Object[]>());
            annexTypeMap.put("3",new ArrayList<Object[]>());
            annexTypeMap.put("20",new ArrayList<Object[]>());
        }else if(2==orgType){
            annexTypeMap.put("1",new ArrayList<Object[]>());
            annexTypeMap.put("2",new ArrayList<Object[]>());
            annexTypeMap.put("3",new ArrayList<Object[]>());
        }
        if(!CollectionUtils.isEmpty(annexList)){
            for(Object[] obj:annexList){
                if(annexTypeMap.get(String.valueOf(obj[2]))!=null){
                    annexTypeMap.get(String.valueOf(obj[2])).add(obj);
                }
            }
        }
    }

    /**
     * <p>方法描述：封装资质附件</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void packageAnnexs(Map<String,List<Object[]>> annexTypeMap,List<ZwAnnexsPO> annexsPOList){
        if(annexTypeMap!=null&&!annexTypeMap.isEmpty()){
           for(Map.Entry<String,List<Object[]>> map:annexTypeMap.entrySet()){
               ZwAnnexsPO zwAnnexsPO=new ZwAnnexsPO();
               zwAnnexsPO.setAnnexType(Integer.valueOf(map.getKey()));
               setAnnexName(map.getKey(),zwAnnexsPO);
               zwAnnexsPO.setValues(map.getValue());
               annexsPOList.add(zwAnnexsPO);
           }
        }
    }
    /**
     * <p>方法描述：设置附件名称</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-20
     **/
    public void setAnnexName(String type,ZwAnnexsPO zwAnnexsPO){
        if(1==orgType){
            switch (type){
                case "1":
                    zwAnnexsPO.setAnnexName("单位法人证明材料（营业执照）");
                    break;
                case "2":
                    zwAnnexsPO.setAnnexName("医疗机构执业许可证");
                    break;
                case "3":
                    zwAnnexsPO.setAnnexName("放射诊疗许可证");
                    break;
                case "20":
                    zwAnnexsPO.setAnnexName("委托协议和委托项目明细");
                    break;
                default:
                    break;
            }
        }else if(2==orgType){
            switch (type){
                case "1":
                    zwAnnexsPO.setAnnexName("单位法人证明材料（营业执照）");
                    break;
                case "2":
                    zwAnnexsPO.setAnnexName("医疗机构执业许可证");
                    break;
                case "3":
                    zwAnnexsPO.setAnnexName("放射诊疗许可证");
                    break;
                default:
                    break;
            }
        }
    }


    /**
     * <p>方法描述：资质附件打开弹框</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-20
     **/
    public void annexOpenDiag(){
        ylAnnex=new TdZwTjorgannexApply();
        this.unitLevel=null;
        this.unitName=null;
        this.searchZoneName=null;
        this.searchZoneId=null;
        this.searchZoneCode=null;
        if(selectAnnexType!=null&&selectAnnexType!=2){
            RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:fileUIdVarUpload");
            RequestContext.getCurrentInstance().execute("PF('FileUIdVarUpload').show();");
        }else{
            RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:photoDialog");
            RequestContext.getCurrentInstance().execute("PF('PhotoDialog').show();");
        }
    }

    /**
     * <p>方法描述：删除附件</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-20
     **/
    public void deleteAnnex(){
        try{
            if(selectZwAnnex!=null){
                orgApplyService.deleteAnnexRec(Integer.parseInt(String.valueOf(selectZwAnnex[0])));
            }
            this.initAnnex();
            countMap.put(String.valueOf(selectAnnexType),countMap.get(String.valueOf(selectAnnexType))-1);
            RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:annexList"+selectAnnexType);
            JsfUtil.addSuccessMessage("删除成功！");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述：删除附件</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-21
     **/
    public void deleteYlAnnex(){
           ylAnnex=new TdZwTjorgannexApply();
    }

    /**
     * <p>方法描述：保存附件</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-21
     **/
    public void saveYlFile(){
        if(null== ylAnnex || StringUtils.isBlank(ylAnnex.getFilePath())){
            JsfUtil.addErrorMessage("医疗机构执业许可证不能为空！");
            return;
        }
        if(StringUtils.isBlank(searchZoneCode)){
            JsfUtil.addErrorMessage("注册地点不能为空！");
            return;
        }
        if(StringUtils.isBlank(unitName)){
            JsfUtil.addErrorMessage("发证单位不能为空！");
            return;
        }
        if(null == unitLevel){
            JsfUtil.addErrorMessage("发证单位级别不能为空！");
            return;
        }
        ylAnnex.setUnitLevel(this.unitLevel);
        ylAnnex.setUnitName(this.unitName);
        ylAnnex.setRegZoneId(this.searchZoneId);
        orgApplyService.saveYlAnnex(ylAnnex);
        this.initAnnex();
        RequestContext.getCurrentInstance().execute("PF('PhotoDialog').hide();;");
        RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:annexList"+selectAnnexType);
    }


    /**
     * <p>方法描述：添加外检车辆信息弹框</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-18
     **/
    public void addOutCarDialog(){
        outCarInstSelectRidList.clear();
        outCarInstRealSelectRidList=new ArrayList<>();
        outCarInstNames="";
        outcarZzsb=new TdZwTjorgOutcarApply();
        outcarZzsb.setStateMark(1);
        RequestContext.getCurrentInstance().execute("PF('OutCarEditDialog').show()");
    }

    /**
     * <p>方法描述：配套仪器清空</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-18
     **/
    public void clearOutCarInst() {
        outCarInstNames=null;
        outCarInstSelectRidList.clear();
        outCarInstRealSelectRidList.clear();
        if(null != outcarZzsb){
            outcarZzsb.setOutcInstZzsbList(new ArrayList<TdZwTjorgOutcInstApy>());
        }
    }

    /**
     * <p>方法描述：编辑外检车辆信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-18
     **/
    public void modOutcar(){
        if(null == this.modOutcarZzsb){
            return;
        }
        outCarInstNames="";
        outCarInstSelectRidList.clear();
        outCarInstRealSelectRidList.clear();
        outcarZzsb=orgApplyService.findOutcarApply(Integer.valueOf(modOutcarZzsb[0].toString()));
        if(!CollectionUtils.isEmpty(outcarZzsb.getOutcInstZzsbList())){
            for(TdZwTjorgOutcInstApy outcInstApy:outcarZzsb.getOutcInstZzsbList()){
                outCarInstRealSelectRidList.add(outcInstApy.getFkByInstId().getRid());
                outCarInstSelectRidList.add(outcInstApy.getFkByInstId().getRid());
                outCarInstNames+="，"+outcInstApy.getFkByInstId().getInstName();
            }
        }
        if(StringUtils.isNotBlank(outCarInstNames)){
            outCarInstNames=outCarInstNames.substring(1);
        }
        RequestContext.getCurrentInstance().execute("PF('OutCarEditDialog').show();");
    }
    /**
     * <p>方法描述：删除外检车辆信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-18
     **/
    public void delOutcar(){
        if(null == this.modOutcarZzsb){
            return;
        }
        try{
            this.orgApplyService.deleteOutCarRec(Integer.valueOf(this.modOutcarZzsb[0].toString()));
            JsfUtil.addSuccessMessage("删除成功！");
            this.initOutCarList();
        }catch(Exception e){
            JsfUtil.addErrorMessage("删除失败！");
            e.printStackTrace();
        }
    }

    /**
     * <p>方法描述：初始化仪器信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-18
     **/
    public void initOutCarInstAction() {
        searchOutCarInstName = null;
        searchOutCarModel = null;
        outCarInstMap=new HashMap<>();
        //初始化仪器列表
        outCarInstList = orgApplyService.findInstInfoByOrgRid(this.orgApplyRid, null, null);
        if (!CollectionUtils.isEmpty(outCarInstList)) {
            for (OutCarInstPO outCarInstPO : outCarInstList) {
                outCarInstMap.put(outCarInstPO.getInstRid(), outCarInstPO.getInstName());
            }
        }
        outCarInstSelectRidList.clear();
        //仪器状态选中状态初始化
        if(!CollectionUtils.isEmpty(outCarInstRealSelectRidList)){
            for (OutCarInstPO outCarInstPO : outCarInstList) {
                if(outCarInstRealSelectRidList.contains(outCarInstPO.getInstRid())){
                    outCarInstPO.setSelected(true);
                }
            }
            outCarInstSelectRidList.addAll(outCarInstRealSelectRidList);
        }
    }

    /**
     * <p>方法描述：仪器添加弹框</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void sureAddOutCarInst(){
        if(!CollectionUtils.isEmpty(outCarInstSelectRidList)){
            outCarInstRealSelectRidList.clear();
            outCarInstNames="";
            for(Integer rid:outCarInstSelectRidList){
                outCarInstNames+="，"+outCarInstMap.get(rid);
            }
            outCarInstRealSelectRidList.addAll(outCarInstSelectRidList);
            if(StringUtils.isNotBlank(outCarInstNames)){
                outCarInstNames=outCarInstNames.substring(1);
            }
        }else{
            JsfUtil.addErrorMessage("请选择仪器！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('AddOutCarInstDialog').hide();");
    }

    /**
     * <p>方法描述：监听查询功能</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void searchOutCarInst(){
        outCarInstList=orgApplyService.findInstInfoByOrgRid(this.orgApplyRid,searchOutCarInstName,searchOutCarModel);
        //仪器状态选中状态初始化
        if(!CollectionUtils.isEmpty(outCarInstSelectRidList)){
            for (OutCarInstPO outCarInstPO : outCarInstList) {
                if(outCarInstSelectRidList.contains(outCarInstPO.getInstRid())){
                    outCarInstPO.setSelected(true);
                }
            }
        }
    }

    /**
     * <p>方法描述：监听选中事件</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void selectCarInstListAction(OutCarInstPO instinfo) {
        if(null != instinfo.getSelected() && instinfo.getSelected()){
            if(!outCarInstSelectRidList.contains(instinfo.getInstRid())){
                outCarInstSelectRidList.add(instinfo.getInstRid());
            }
        }else{
            outCarInstSelectRidList.remove(instinfo.getInstRid());
        }
    }

    /**
     * <p>方法描述：外检车辆保存-真保存</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void submitOutcar(){
        if (this.outcarZzsb == null) {
            JsfUtil.addErrorMessage("保存失败！");
            return;
        }
        boolean filed = false;
        if (ObjectUtil.isEmpty(this.outcarZzsb.getCarNo())) {
            JsfUtil.addErrorMessage("车牌号不允许为空！");
            filed = true;
        }
        if ("1".equals(this.outCarVersion)) {
            if (ObjectUtil.isEmpty(this.outcarZzsb.getCarBand())) {
                JsfUtil.addErrorMessage("品牌不允许为空！");
                filed = true;
            }
            if (ObjectUtil.isEmpty(this.outcarZzsb.getCarStyle())) {
                JsfUtil.addErrorMessage("型号不允许为空！");
                filed = true;
            }
            if (ObjectUtil.isEmpty(this.outcarZzsb.getCarFrameNo())) {
                JsfUtil.addErrorMessage("车架号不允许为空！");
                filed = true;
            }
            if (ObjectUtil.isEmpty(this.outcarZzsb.getProductionDate())) {
                JsfUtil.addErrorMessage("购置日期不允许为空！");
                filed = true;
            }
            if (ObjectUtil.isEmpty(this.outCarInstRealSelectRidList)) {
                JsfUtil.addErrorMessage("配套仪器不允许为空！");
                filed = true;
            }
            if (ObjectUtil.isEmpty(this.outcarZzsb.getDrivingAnnexPath())) {
                JsfUtil.addErrorMessage("请上传行驶证复印件！");
                filed = true;
            }
            if (ObjectUtil.isEmpty(this.outcarZzsb.getCarPhotoAnnexPath())) {
                JsfUtil.addErrorMessage("请上传车辆照片！");
                filed = true;
            }
            if (ObjectUtil.isEmpty(this.outcarZzsb.getCarDrAnnexPath())) {
                JsfUtil.addErrorMessage("请上传车载DR照片！");
                filed = true;
            }
        }
        if (ObjectUtil.isEmpty(this.outcarZzsb.getStateMark())) {
            JsfUtil.addErrorMessage("状态不允许为空！");
            filed = true;
        }
        if (filed) {
            return;
        }
        try{
            orgApplyService.insetUpOutCarRec(this.orgApplyRid,outcarZzsb,outCarInstRealSelectRidList);
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().execute("PF('OutCarEditDialog').hide();");
            this.initOutCarList();
        }catch (Exception e){
            JsfUtil.addErrorMessage("保存失败！");
            e.printStackTrace();
        }
    }

    public void showOutCarFileUpload() {
        RequestContext.getCurrentInstance().execute("PF('OutCarFileDialog').show();");
    }

    /**
     * 外检车辆上传附件
     */
    public void outCarFileUpload(FileUploadEvent event) {
        if (ObjectUtil.isEmpty(this.outCarOpFile) || event == null) {
            return;
        }
        UploadedFile file = event.getFile();
        try {
            String fileName = file.getFileName();
            String contentType = file.getContentType().toLowerCase();
            String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
            if (StringUtils.isNotBlank(errorMsg)) {
                JsfUtil.addErrorMessage(errorMsg);
                return;
            }
            // 文件名称
            String uuid = UUID.randomUUID().toString()
                    .replaceAll("-", "");
            String relativePath = "heth/zworgannex/" + uuid + fileName.substring(fileName.lastIndexOf("."));
            String filePath = JsfUtil.getAbsolutePath() + relativePath;
            FileUtils.copyFile(filePath, file.getInputstream());
            RequestContext.getCurrentInstance().execute(
                    "PF('OutCarFileDialog').hide()");
            switch (this.outCarOpFile) {
                case 1:
                    this.outcarZzsb.setDrivingAnnexPath(relativePath);
                    break;
                case 2:
                    this.outcarZzsb.setCarPhotoAnnexPath(relativePath);
                    break;
                case 3:
                    this.outcarZzsb.setCarDrAnnexPath(relativePath);
                    break;
                default:
                    break;
            }
            JsfUtil.addSuccessMessage("上传成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("上传失败！");
            throw new RuntimeException(e);
        }
    }

    /**
     * 外检车辆删除附件
     */
    public void outCarFileDel() {
        if (ObjectUtil.isEmpty(this.outCarOpFile)) {
            return;
        }
        switch (this.outCarOpFile) {
            case 1:
                this.outcarZzsb.setDrivingAnnexPath("");
                break;
            case 2:
                this.outcarZzsb.setCarPhotoAnnexPath("");
                break;
            case 3:
                this.outcarZzsb.setCarDrAnnexPath("");
                break;
            default:
                break;
        }
    }

    /**
     *  <p>方法描述：基本信息中 是否具备外检车辆</p>
     * @MethodAuthor hsj 2022/4/24 10:06
     */
    public void onChangeOutWorkPower(){
        if(tjorginfoApply == null || tjorginfoApply.getOutWorkPower()==null || tjorginfoApply.getOutWorkPower()!=1){
            //不具备
            for(ZwJkItemRowPO itemPO:jkItemRowList){
                itemPO.setSelectedIfExterInspect(new String[0]);
            }
        }
        this.initInstitutionList();
    }
    /**
     *  <p>方法描述：服务明细小类</p>
     * @MethodAuthor hsj 2022/4/22 14:26
     */
    private void serverSubItemSimpleCodeListByExt2(){
        List<TsSimpleCode> resultList = this.allSimpleCodeMap.get("5527");
        jkItemSimpleCodeMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(resultList)){
            for(TsSimpleCode simpleCode : resultList){
                if(null == simpleCode.getExtendS2()){
                    continue;
                }
                if(jkItemSimpleCodeMap.containsKey(simpleCode.getExtendS2().toString())){
                    jkItemSimpleCodeMap.get(simpleCode.getExtendS2().toString()).add(simpleCode);
                }else {
                    List<TsSimpleCode> simpleCodes = new ArrayList<>();
                    simpleCodes.add(simpleCode);
                    jkItemSimpleCodeMap.put(simpleCode.getExtendS2().toString(),simpleCodes);
                }
            }

        }
    }

    /**
     * 初始化人员信息TAG
     */
    public void initPsnInfoList() {
        if (new Integer(2).equals(this.orgType)) {
            this.zzjgType = 2;
        } else {
            this.zzjgType = 0;
        }
        this.psnInfoVO = new ZzsbPsnInfoVO();
        this.psnInfoVO.init();
        this.psnInfoVO.setOrgPsnApplyListView(pakOrgPsnInfoApplyList(-1));
        //初始化科室
        if (CollectionUtils.isEmpty(this.psnInfoVO.getWorkOfficeList())) {
            List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeNo("5014");
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            this.psnInfoVO.setWorkOfficeList(list);
        }

        this.psnInfoBase = new TdzwPsnInfoZzsbBaseNew();
        TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
        psninfo.setAddSource(this.zzjgType);
        psninfo.setFkByTitleId(new TsSimpleCode());
        psninfo.setFkByEducationId(new TsSimpleCode());
        psninfo.setFkByNation(new TsSimpleCode());
        psninfo.setFkByDegreeId(new TsSimpleCode());
        psninfo.setFkByTitleLevelId(new TsSimpleCode());
        psninfo.setFkByDoctLevelId(new TsSimpleCode());
        psninfo.setFkByDoctTypeId(new TsSimpleCode());
        psninfo.setFkByDoctSpecId(new TsSimpleCode());
        this.psnInfoBase.setPsnInfo(psninfo);
        this.psnInfoBase.setValidPsnInfoId(false);
        this.psnInfoBase.initPsnZzlb();
        this.psnInfoBase.setOrgRid(this.orgApplyRid);
    }

    /**
     * 获取人员列表信息
     *
     * @return 机构人员列表信息
     */
    public List<TdZwTjorgpsnsApply> pakOrgPsnInfoApplyList(Integer psnRid) {
        List<TdZwTjorgpsnsApply> list = new ArrayList<>();
        Integer rid = this.orgApplyRid;
        List<Object[]> resultList = this.orgApplyService.findPersonnelListByOrgId(this.zzjgType, rid, psnRid);
        for (Object[] o : resultList) {
            try {
                TdZwTjorgpsnsApply orgPsnInfoApply = new TdZwTjorgpsnsApply();
                orgPsnInfoApply.setRid(((BigDecimal) o[5]).intValue());
                orgPsnInfoApply.setWordOfficeNames(removeDupByContains(StringUtils.objectToString(o[9]), "#@，@#", "，"));
                orgPsnInfoApply.setWordOfficeRids(removeDupByContains(StringUtils.objectToString(o[10]), "#@，@#", "#@，@#"));
                orgPsnInfoApply.setFkByEmpId(pakPsnInfoZzsb(o));
                orgPsnInfoApply.setFkByOrgId(new TdZwTjorginfoApply(rid));
                list.add(orgPsnInfoApply);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        this.tjorginfoApply.setTjorgpsnsApplyList(list);
        return list;
    }

    /**
     * 封装人员信息
     */
    public TdZwPsninfoZzsb pakPsnInfoZzsb(Object[] o) {
        TdZwPsninfoZzsb psn = new TdZwPsninfoZzsb();
        psn.setRid(((BigDecimal) o[0]).intValue());
        psn.setEmpName(StringUtils.objectToString(o[1]));
        psn.setSex(StringUtils.objectToString(o[2]));
        psn.setFkByTitleId(new TsSimpleCode(((BigDecimal) o[3]).intValue(), StringUtils.objectToString(o[4]), "2003"));
        psn.setPsnTypes(removeDupByContains(StringUtils.objectToString(o[6]), "#@，@#", "，"));
        psn.setCertNoStr(removeDupByContains(StringUtils.objectToString(o[7]), "#@，@#", ","));
        psn.setZzlbRange(removeDupByContains(StringUtils.objectToString(o[8]), "#@，@#", "，"));
        return psn;
    }

    public void addPsnInfoAction() {
        TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
        psninfo.setAddSource(this.zzjgType);
        psninfo.setFkByTitleId(new TsSimpleCode());
        psninfo.setFkByEducationId(new TsSimpleCode());
        psninfo.setFkByNation(new TsSimpleCode());
        psninfo.setFkByDegreeId(new TsSimpleCode());
        psninfo.setFkByTitleLevelId(new TsSimpleCode());
        psninfo.setFkByDoctLevelId(new TsSimpleCode());
        psninfo.setFkByDoctTypeId(new TsSimpleCode());
        psninfo.setFkByDoctSpecId(new TsSimpleCode());
        this.psnInfoVO.setPsnDialogType(0);
        this.psnInfoVO.setSelectPsnInfo(new TdZwPsninfoZzsb());
        this.psnInfoVO.getSelectOrgPsnApply().setRid(null);
        this.psnInfoBase = new TdzwPsnInfoZzsbBaseNew();
        this.psnInfoBase.setZwOrgType(this.zzjgType);
        this.psnInfoBase.setPsnInfo(psninfo);
        this.psnInfoBase.setValidPsnInfoId(false);
        this.psnInfoBase.initPsnInfo();
        this.psnInfoBase.initPsnZzlb();
        this.psnInfoBase.setOrgRid(this.orgApplyRid);
    }

    public void viewPsnInfoAction() {
        this.psnInfoVO.setPsnDialogType(2);
        viewAndEditInit();
        RequestContext.getCurrentInstance().execute("disabledInput()");
    }

    public void editPsnInfoAction() {
        this.psnInfoVO.setPsnDialogType(1);
        viewAndEditInit();
    }

    private void viewAndEditInit() {
        TdZwPsninfoZzsb tdZwPsninfo = this.orgApplyService.findTdZwPsnInfo(this.psnInfoVO.getSelectOrgPsnApply().getFkByEmpId().getRid());
        this.psnInfoBase.setZwOrgType(this.zzjgType);
        this.psnInfoBase.setPsnInfo(tdZwPsninfo);
        this.psnInfoBase.initPsnInfo();
        this.psnInfoBase.initPsnZzlb();
        this.psnInfoBase.setOrgPsnInfoRid(this.psnInfoVO.getSelectOrgPsnApply().getRid());
        this.psnInfoBase.setOrgRid(this.orgApplyRid);
    }

    public void savePsnAction() {
        try {
            // 验证
            if (!this.psnInfoBase.beforeSave()) {
                return;
            }
            this.psnInfoBase.preSave();
            boolean ifSave = this.psnInfoVO.getSelectPsnInfo() == null || null == this.psnInfoVO.getSelectPsnInfo().getRid();
            if (!ifSave) {
                this.psnInfoVO.setSelectPsnInfo(new TdZwPsninfoZzsb());
                this.psnInfoVO.setSelectOrgPsnApply(new TdZwTjorgpsnsApply());
            }
            //存储
            this.psnInfoVO.getSelectOrgPsnApply().setFkByOrgId(new TdZwTjorginfoApply(this.orgApplyRid));
            this.psnInfoVO.getSelectOrgPsnApply().setFkByEmpId(this.psnInfoBase.getPsnInfo());
            this.psnPlanServiceImpl.saveOrUpdatePsninfoAndRelationNew(this.psnInfoBase.getPsnInfo(),
                    this.psnInfoBase.getPsnTypeRows(), this.psnInfoBase.getZwOrgType(), this.psnInfoVO.getSelectOrgPsnApply());
            this.psnInfoBase.afterSave();
            //保存完更新
            this.psnInfoBase.setOrgPsnInfoRid(this.psnInfoVO.getSelectOrgPsnApply().getRid());
            this.psnInfoVO.setOrgPsnApplyListView(pakOrgPsnInfoApplyList(-1));
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败");
        }
    }

    public void selPsnWordOfficeAction() {
        this.psnInfoVO.setSelectWorkOfficeRidList(StringUtils.string2list(this.psnInfoVO.getSelectOrgPsnApply().getWordOfficeRids(), "#@，@#"));
    }

    public void savePsnWorkOfficeAction() {
        List<TdZwWorkdeptApply> workDeptApplyList = new ArrayList<>();
        Integer orgPsnApplyRid = this.psnInfoVO.getSelectOrgPsnApply().getRid();
        if (!CollectionUtils.isEmpty(this.psnInfoVO.getSelectWorkOfficeRidList())) {
            // 科室名称
            for (String s : this.psnInfoVO.getSelectWorkOfficeRidList()) {
                if (StringUtils.isBlank(s)) {
                    continue;
                }
                TdZwWorkdeptApply workDeptApply = new TdZwWorkdeptApply();
                workDeptApply.setFkByEmpId(new TdZwTjorgpsnsApply(orgPsnApplyRid));
                workDeptApply.setFkByDeptId(new TsSimpleCode(Integer.valueOf(s)));
                workDeptApply.setCreateDate(new Date());
                workDeptApply.setCreateManid(Global.getUser().getRid());
                workDeptApplyList.add(workDeptApply);
            }
        }
        this.orgApplyService.savePsnWorkOfficeList(orgPsnApplyRid, workDeptApplyList);
        this.psnInfoVO.setOrgPsnApplyListView(pakOrgPsnInfoApplyList(-1));
    }

    public void deletePsnAction() {
        try {
            Integer rid = this.psnInfoVO.getSelectOrgPsnApply().getRid();
            this.orgApplyService.delOrgPsnApply(rid);
            JsfUtil.addSuccessMessage("删除成功！");
            this.psnInfoVO.setOrgPsnApplyListView(pakOrgPsnInfoApplyList(-1));
        } catch (Exception e) {
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    public void executeZzlbDelAction() {
        this.psnInfoBase.zzlbDelAction();
        this.psnInfoVO.setOrgPsnApplyListView(pakOrgPsnInfoApplyList(-1));
    }

    public void executeSaveZzlbAction() {
        this.psnInfoBase.saveZzlbAction();
        this.psnInfoVO.setOrgPsnApplyListView(pakOrgPsnInfoApplyList(-1));
    }

    /**
     * 去重string(保持原分割顺序)
     *
     * @param str  原始按str1分割的string
     * @param str1 原始string分割符
     * @param str2 去重后的string分割符
     * @return 去重后的List(保持原List顺序)
     */
    public static String removeDupByContains(String str, String str1, String str2) {
        List<String> list = StringUtils.string2list(str, str1);
        List<String> newList = new ArrayList<>();
        for (String s : list) {
            boolean isContains = newList.contains(s);
            if (!isContains) {
                newList.add(s);
            }
        }
        list.clear();
        list.addAll(newList);
        return StringUtils.list2string(list, str2);
    }

    /**
     * 初始化仪器信息TAG
     */
    public void initInstInfoList() {
        this.instInfoVO = new ZzsbInstInfoVO();
        this.instInfoVO.init();
        this.instInfoVO.setOrgInstApplyListView(pakOrgInstApplyList());
    }

    /**
     * 获取机构所有仪器信息
     *
     * @return 机构所有仪器信息
     */
    public List<TdZwTjinstApply> pakOrgInstApplyList() {
        List<TdZwTjinstApply> list = new ArrayList<>();
        Integer rid = this.orgApplyRid;
        List<Object[]> resultList = this.orgApplyService.findInstrumentListByOrgId(rid);
        this.instInfoVO.setOrgInstApplyInstRidRidSet(new HashSet<Integer>());
        for (Object[] o : resultList) {
            try {
                TdZwTjinstApply orgInstApply = new TdZwTjinstApply();
                orgInstApply.setRid(((BigDecimal) o[0]).intValue());
                orgInstApply.setFkByInstId(pakSimpleInstInfo(1, o));
                orgInstApply.setFkByOrgId(new TdZwTjorginfoApply(rid));
                this.instInfoVO.getOrgInstApplyInstRidRidSet().add(orgInstApply.getFkByInstId().getRid());
                list.add(orgInstApply);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        this.tjorginfoApply.setTjinstApplyList(list);
        return list;
    }

    /**
     * 删除机构仪器
     */
    public void deleteOrgInstApplyAction() {
        try {
            Integer rid = this.instInfoVO.getSelectOrgInstApply().getRid();
            this.orgApplyService.deleteOrgInstApply(rid);
            JsfUtil.addSuccessMessage("删除成功！");
            this.instInfoVO.setOrgInstApplyListView(pakOrgInstApplyList());
        } catch (Exception e) {
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * 封装仪器信息
     *
     * @param type 1 机构仪器信息;2 弹出框仪器信息.
     */
    private TdZwInstinfoZzsb pakSimpleInstInfo(int type, Object[] o) {
        TdZwInstinfoZzsb inst = new TdZwInstinfoZzsb();
        inst.setRid(((BigDecimal) o[1]).intValue());
        inst.setInstName(StringUtils.objectToString(o[2]));
        inst.setInstModel(StringUtils.objectToString(o[3]));
        inst.setInstCode(StringUtils.objectToString(o[4]));
        if (type == 1) {
            inst.setLastAcptDate(o[5] != null ? (Date) o[5] : null);
            inst.setLastAcptCircleDot(o[6] != null ? ((BigDecimal) o[6]) : null);
            inst.setOutter(o[7] != null ? ((BigDecimal) o[7]).shortValue() : null);
            inst.setIfSuptOutChk(o[8] != null ? ((BigDecimal) o[8]).intValue() : null);
        } else if (type == 2) {
            inst.setOutter(o[5] != null ? ((BigDecimal) o[5]).shortValue() : null);
            inst.setSelected(!"0".equals(StringUtils.objectToString(o[6])));
        }
        return inst;
    }

    /**
     * 获取仪器信息List
     *
     * @param searchInstInfo 查询条件
     * @return 仪器信息
     */
    public List<TdZwInstinfoZzsb> pakInstInfoList(String[] searchInstInfo) {
        List<TdZwInstinfoZzsb> list = new ArrayList<>();
        List<Object[]> resultList = this.orgApplyService.findInstInfoListBySearchInfo(searchInstInfo, this.orgApplyRid);
        for (Object[] o : resultList) {
            try {
                list.add(pakSimpleInstInfo(2, o));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return list;
    }

    /**
     * 重置仪器页面弹出框表格当前页码
     */
    private void resetPopUpBoxPageNumber() {
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent(":tabView:editForm:orgTjApplyTabView:instinfoDataTable");
        if (dataTable != null) {
            dataTable.setFirst(0);
            dataTable.setRows(10);
        }
    }

    /**
     * 初始化仪器列表
     */
    public void initInstInfoListAction() {
        if (CollectionUtils.isEmpty(this.instInfoVO.getInstKindTypeList())) {
            List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeNo("5024");
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            this.instInfoVO.setInstKindTypeList(list);
        }
        if (CollectionUtils.isEmpty(this.instInfoVO.getInstAcptReqList())) {
            List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeNo("5841");
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            this.instInfoVO.setInstAcptReqList(list);
        }
        if (CollectionUtils.isEmpty(this.instInfoVO.getInstStateList())) {
            List<TsSimpleCode> list = this.commService.findSimpleCodesByTypeNo("5842");
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            this.instInfoVO.setInstStateList(list);
        }
        this.instInfoVO.setSearchInstInfo(new String[2]);
        searchInstAction();
    }

    /**
     * 搜索仪器
     */
    public void searchInstAction() {
        this.instInfoVO.setAllInstListView(pakInstInfoList(this.instInfoVO.getSearchInstInfo()));
        resetPopUpBoxPageNumber();
    }

    /**
     * 初始化仪器信息
     */
    public void initInstInfoAction(TdZwInstinfoZzsb instInfo) {
        this.instInfoVO.setSelectInstInfo(instInfo);
        this.instInfoVO.setInstTypeList(new ArrayList<SelectItemGroup>());
        this.instInfoVO.setInstTypeMap(new HashMap<Integer, Integer>(16));
        for (TsSimpleCode t : this.instInfoVO.getInstKindTypeList()) {
            if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
                SelectItemGroup group = new SelectItemGroup();
                group.setLabel(t.getCodeName());
                group.setValue(t.getRid());
                this.instInfoVO.getInstTypeList().add(group);
            } else {
                SelectItemGroup group = this.instInfoVO.getInstTypeList().get(this.instInfoVO.getInstTypeList().size() - 1);
                SelectItem[] selectItems = group.getSelectItems();
                SelectItem[] newArray;
                if (null == selectItems) {
                    newArray = new SelectItem[1];
                    newArray[0] = new SelectItem(t.getRid(), t.getCodeName());
                } else {
                    newArray = Arrays.copyOf(selectItems, selectItems.length + 1);
                    newArray[selectItems.length] = new SelectItem(t.getRid(), t.getCodeName());
                }
                group.setSelectItems(newArray);
                this.instInfoVO.getInstTypeMap().put(t.getRid(), (Integer) group.getValue());
            }
        }
        changeWaringDate();
        RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:instEditGrid");
        RequestContext.getCurrentInstance().execute("PF('InstEditDialog').show();");
    }

    public void pakInstInfo(TdZwInstinfoZzsb instInfo) {
        if (instInfo.getFkByInstKindDetalId() == null) {
            instInfo.setFkByInstKindDetalId(new TsSimpleCode());
        }
        if (instInfo.getCreateDate() == null) {
            instInfo.setCreateDate(new Date());
        }
        if (instInfo.getCreateManid() == null) {
            instInfo.setCreateManid(Global.getUser().getRid());
        }
        if (instInfo.getTsUnit() == null) {
            instInfo.setTsUnit(Global.getUser().getTsUnit());
        }
        if (instInfo.getInstType() == null) {
            instInfo.setInstType(0);
        }
        if (instInfo.getOutter() == null) {
            instInfo.setOutter((short) 0);
        }
        if (instInfo.getIfNeedAcpt() == null) {
            instInfo.setIfNeedAcpt(1);
        }
        if (instInfo.getAcptReqId() == null) {
            instInfo.setAcptReqId(new TsSimpleCode());
        }
        if (instInfo.getInstStateId() == null) {
            instInfo.setInstStateId(new TsSimpleCode());
        }
        if (instInfo.getFkByInstKindDetalId() == null) {
            instInfo.setFkByInstKindDetalId(new TsSimpleCode());
        }
    }

    /**
     * 添加仪器
     */
    public void addInstInfoAction() {
        TdZwInstinfoZzsb instInfo = new TdZwInstinfoZzsb();
        pakInstInfo(instInfo);
        initInstInfoAction(instInfo);
    }

    /**
     * 机构选择添加仪器
     */
    public void addOrgInstApplyAction() {
        try {
            Integer orgRid = this.orgApplyRid;
            Integer instRid = this.instInfoVO.getTempInstInfo().getRid();
            int count = this.orgApplyService.insertOrgInstApply(orgRid, instRid);
            if (count <= 0) {
                JsfUtil.addErrorMessage("该仪器已被引用，无法选择！");
                this.instInfoVO.getTempInstInfo().setSelected(true);
                return;
            }
            this.instInfoVO.setOrgInstApplyListView(pakOrgInstApplyList());
            this.instInfoVO.getTempInstInfo().setSelected(true);
            JsfUtil.addSuccessMessage("添加成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("添加失败！");
        }
    }

    /**
     * 修改仪器信息
     */
    public void modInstInfoAction() {
        TdZwInstinfoZzsb instInfo = this.orgApplyService.findInstInfoByRid(this.instInfoVO.getSelectInstInfo().getRid());
        pakInstInfo(instInfo);
        initInstInfoAction(instInfo);
    }

    /**
     * 删除仪器
     */
    public void deleteInstInfoAction() {
        if (this.instInfoVO.getSelectInstInfo().getSelected()) {
            JsfUtil.addErrorMessage("该仪器已被引用，无法删除！");
            return;
        }
        try {
            Integer rid = this.instInfoVO.getSelectInstInfo().getRid();
            this.orgApplyService.deleteInstInfo(rid);
            this.instInfoVO.setAllInstListView(pakInstInfoList(this.instInfoVO.getSearchInstInfo()));
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("该仪器已被引用，无法删除！");
        }
    }

    /**
     * 仪器类型修改
     */
    public void instTypeChange() {
        if (null == this.instInfoVO.getSelectInstInfo().getFkByInstKindDetalId().getRid()) {
            this.instInfoVO.getSelectInstInfo().setInstName("");
            return;
        }
        if (!CollectionUtils.isEmpty(this.instInfoVO.getInstKindTypeList())) {
            for (TsSimpleCode item : this.instInfoVO.getInstKindTypeList()) {
                if (this.instInfoVO.getSelectInstInfo().getFkByInstKindDetalId().getRid().equals(item.getRid())) {
                    this.instInfoVO.getSelectInstInfo().setInstName(item.getCodeName());
                }
            }
        }
    }

    /**
     * 计算预警日期
     */
    public void onDateSelect(SelectEvent event) {
        Date date = (Date) event.getObject();
        onDateValueChange(date, this.instInfoVO.getSelectInstInfo().getLastAcptCircleDot());
    }

    /**
     * 修改显示的预警日期
     */
    public void changeWaringDate() {
        onDateValueChange(this.instInfoVO.getSelectInstInfo().getLastAcptDate(), this.instInfoVO.getSelectInstInfo().getLastAcptCircleDot());
    }

    /**
     * 修订内容: 修改显示的预警日期
     */
    public void onDateValueChange(Date lastAcptDate, BigDecimal lastAcptCircleDot) {
        if (lastAcptDate != null && lastAcptCircleDot != null) {
            Calendar theCa = Calendar.getInstance();
            theCa.setTime(lastAcptDate);
            //小数位数值<=0.5则预警日期增加6个月（统一折算为半年），小数位数值>0.5则预警日期增加1年
            //五舍六入后的值
            int num = lastAcptCircleDot.setScale(0,BigDecimal.ROUND_HALF_DOWN).intValue();
            //向下取整
            int floor = lastAcptCircleDot.setScale(0,BigDecimal.ROUND_DOWN).intValue();
            if(num > floor){
                //说明lastAcptCircleDot小数位大于0.5
                theCa.add(Calendar.YEAR,num);
            } else if(lastAcptCircleDot.compareTo(new BigDecimal(floor)) > 0){
                theCa.add(Calendar.YEAR,floor);
                theCa.add(Calendar.MONTH,6);
            } else {
                theCa.add(Calendar.YEAR,floor);
            }
            String formatDate = DateUtils.formatDate(theCa.getTime(), "yyyy-MM-dd");
            if (StringUtils.isNoneBlank(formatDate) && formatDate.split("-").length > 2) {
                String[] split = formatDate.split("-");
                this.instInfoVO.setWaringDate("预警日期：" + split[0] + "-" + split[1] + "-" + split[2]);
            } else {
                this.instInfoVO.setWaringDate("");
            }
        } else {
            this.instInfoVO.setWaringDate("");
        }
    }

    /**
     * 保存仪器信息
     */
    public void saveInstAction() {
        try {
            if (!verificationInstInfo()) {
                return;
            }
            this.orgApplyService.saveOrUpdateInstInfo(this.instInfoVO.getSelectInstInfo());
            TdZwInstinfoZzsb instInfo = new TdZwInstinfoZzsb();
            pakInstInfo(instInfo);
            this.instInfoVO.setSelectInstInfo(instInfo);
            searchInstAction();
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.update("tabView:editForm:orgTjApplyTabView:instDataTable");
            this.instInfoVO.setOrgInstApplyListView(pakOrgInstApplyList());
            currentInstance.update("tabView:editForm:orgTjApplyTabView:instinfoDataTable");
            currentInstance.execute("PF('InstEditDialog').hide()");
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    public boolean verificationInstInfo() {
        TdZwInstinfoZzsb instInfo = this.instInfoVO.getSelectInstInfo();
        boolean flag = true;

        if (null ==instInfo.getFkByInstKindDetalId() || null ==instInfo.getFkByInstKindDetalId().getRid()) {
            JsfUtil.addErrorMessage("请选择仪器类型！");
            flag = false;
        }
        if (StringUtils.isBlank(instInfo.getInstName())) {
            JsfUtil.addErrorMessage("仪器名称不允许为空！");
            flag = false;
        }
        if (StringUtils.isBlank(instInfo.getInstModel())) {
            JsfUtil.addErrorMessage("仪器型号不允许为空！");
            flag = false;
        }
        if (StringUtils.isBlank(instInfo.getInstCode())) {
            JsfUtil.addErrorMessage("仪器编号不允许为空！");
            flag = false;
        }

        if (null != instInfo.getInstProDate() && null != instInfo.getPurchaseDate() &&
                DateUtils.isDateAfter(instInfo.getInstProDate(), instInfo.getPurchaseDate())) {
            JsfUtil.addErrorMessage("购置日期应大于等于生产日期！");
            flag = false;
        }

        if (null == instInfo.getOutter()) {
            JsfUtil.addErrorMessage("是否外包不允许为空！");
            flag = false;
        }

        if (null == instInfo.getAcptReqId() || null == instInfo.getAcptReqId().getRid()) {
            JsfUtil.addErrorMessage("请选择检定要求！");
            flag = false;
        }
        if (null == instInfo.getIfSuptOutChk()) {
            JsfUtil.addErrorMessage("是否支持外检不能为空！");
            flag = false;
        }
        if (null == instInfo.getIfNeedAcpt()) {
            JsfUtil.addErrorMessage("是否需要校验不能为空！");
            flag = false;
        }
        if (null != instInfo.getIfNeedAcpt() && instInfo.getIfNeedAcpt() == 1) {
            if (null == instInfo.getLastAcptDate()) {
                JsfUtil.addErrorMessage("最近校验日期不能为空！");
                flag = false;
            }
            if (null != instInfo.getLastAcptDate() && null != instInfo.getPurchaseDate() &&
                    DateUtils.isDateAfter(instInfo.getPurchaseDate(), instInfo.getLastAcptDate())) {
                JsfUtil.addErrorMessage("最近校验日期应大于等于购置日期！");
                flag = false;
            }
            if (null != instInfo.getLastAcptDate() && null != instInfo.getInstProDate() &&
                    DateUtils.isDateAfter(instInfo.getInstProDate(), instInfo.getLastAcptDate())) {
                JsfUtil.addErrorMessage("最近校验日期应大于等于生产日期！");
                flag = false;
            }
            if (null == instInfo.getLastAcptCircleDot()) {
                JsfUtil.addErrorMessage("校验周期不能为空！");
                flag = false;
            }
        }
        if (null == instInfo.getInstStateId() || null == instInfo.getInstStateId().getRid()) {
            JsfUtil.addErrorMessage("仪器状态不允许为空！");
            flag = false;
        }

        // 仪器编号本单位唯一
        if(StringUtils.isNotBlank(instInfo.getInstCode())) {
            int count = this.orgApplyService.findExistsInstNo(instInfo.getInstCode(), instInfo.getRid());
            if (count > 0) {
                JsfUtil.addErrorMessage("本单位下仪器编号重复！");
                flag = false;
            }
        }

        if (!flag) {
            return false;
        }
        if (null != instInfo.getIfNeedAcpt() && instInfo.getIfNeedAcpt() == 0) {
            instInfo.setLastAcptDate(null);
            instInfo.setLastAcptCircle(null);
            instInfo.setLastAcptCircleDot(null);
        }
        if (instInfo.getTsUnit() != null && instInfo.getTsUnit().getRid() == null) {
            instInfo.setTsUnit(Global.getUser().getTsUnit());
        }
        if (instInfo.getFkByInstKindId() != null && instInfo.getFkByInstKindId().getRid() == null) {
            instInfo.setFkByInstKindId(null);
        }
        if (instInfo.getFkByInstKindDetalId() != null && instInfo.getFkByInstKindDetalId().getRid() == null) {
            instInfo.setFkByInstKindDetalId(null);
        }
        return true;
    }

    /**
     * @Description: 下载生成的备案回执
     *
     * @MethodAuthor pw,2022年04月24日
     */
    public StreamedContent getReportReceiptFile() {
        this.reportReceiptFile = null;
        try{
            if(StringUtils.isBlank(this.reportReceiptFilePath)){
                return null;
            }
            String filePath;
            String xnPath = JsfUtil.getAbsolutePath();
            if(this.reportReceiptFilePath.indexOf(xnPath) != -1){
                filePath = this.reportReceiptFilePath;
            }else{
                filePath = xnPath + this.reportReceiptFilePath;
            }
            String fileName = this.tjorginfoApply.getOrgName()+"备案回执.docx";
            InputStream stream = new FileInputStream(filePath);
            this.reportReceiptFile = new DefaultStreamedContent(stream, "application/msword", URLEncoder.encode(fileName, "UTF-8"));
            if(null != this.reportReceiptFile){
                RequestContext.getCurrentInstance().execute("PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);");
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        return reportReceiptFile;
    }
    /**
     *  <p>方法描述：备案有效期日期监听</p>
     * @MethodAuthor hsj 2022/4/26 9:58
     */
    public void filingDateListener(){
        Date today = new Date();
        if(this.tjorginfoApply.getFilingDate() == null || this.tjorginfoApply.getFilingDate().after(today)){
            //有效结束日期为空，或结束日期晚于今天，则最大日期为今天
            this.firstGetdayMaxDate = today;
        }else{
            this.firstGetdayMaxDate = this.tjorginfoApply.getFilingDate();
        }
    }

    /**
     * <p>方法描述：更新资质机构备案主表</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-26
     **/
    public boolean updateFstChkApply(){
        if(tjorginfoApply==null){
            return false;
        }
        try{
            orgApplyService.updateFstChkApply(tjorginfoApply);
            return true;
        }catch (Exception e){
            return false;
        }
    }

    public Date getFirstGetdayMaxDate() {
        return firstGetdayMaxDate;
    }

    public void setFirstGetdayMaxDate(Date firstGetdayMaxDate) {
        this.firstGetdayMaxDate = firstGetdayMaxDate;
    }

    public Integer getOrgApplyRid() {
        return orgApplyRid;
    }

    public void setOrgApplyRid(Integer orgApplyRid) {
        this.orgApplyRid = orgApplyRid;
    }

    public Map<String, List<TsSimpleCode>> getAllSimpleCodeMap() {
        return allSimpleCodeMap;
    }

    public void setAllSimpleCodeMap(Map<String, List<TsSimpleCode>> allSimpleCodeMap) {
        this.allSimpleCodeMap = allSimpleCodeMap;
    }

    public Boolean getIfHethFj() {
        return ifHethFj;
    }

    public void setIfHethFj(Boolean ifHethFj) {
        this.ifHethFj = ifHethFj;
    }

    public Boolean getIfQualAnnex() {
        return ifQualAnnex;
    }

    public void setIfQualAnnex(Boolean ifQualAnnex) {
        this.ifQualAnnex = ifQualAnnex;
    }

    public Boolean getIfPersonSummary() {
        return ifPersonSummary;
    }

    public void setIfPersonSummary(Boolean ifPersonSummary) {
        this.ifPersonSummary = ifPersonSummary;
    }

    public Boolean getIfValidityPeriod() {
        return ifValidityPeriod;
    }

    public void setIfValidityPeriod(Boolean ifValidityPeriod) {
        this.ifValidityPeriod = ifValidityPeriod;
    }


    public Boolean getIfView() {
        return ifView;
    }

    public void setIfView(Boolean ifView) {
        this.ifView = ifView;
    }

    public String getTabTitle() {
        return tabTitle;
    }

    public void setTabTitle(String tabTitle) {
        this.tabTitle = tabTitle;
    }

    public List<String> getCareerList() {
        return careerList;
    }

    public void setCareerList(List<String> careerList) {
        this.careerList = careerList;
    }

    public List<SelectItem> getServiceObjList() {
        return serviceObjList;
    }

    public void setServiceObjList(List<SelectItem> serviceObjList) {
        this.serviceObjList = serviceObjList;
    }

    public List<String> getSelectServiceObjList() {
        return selectServiceObjList;
    }

    public void setSelectServiceObjList(List<String> selectServiceObjList) {
        this.selectServiceObjList = selectServiceObjList;
    }

    public List<String> getServiceItemNameList() {
        return serviceItemNameList;
    }

    public void setServiceItemNameList(List<String> serviceItemNameList) {
        this.serviceItemNameList = serviceItemNameList;
    }

    public String getOrgZoneName() {
        return orgZoneName;
    }

    public void setOrgZoneName(String orgZoneName) {
        this.orgZoneName = orgZoneName;
    }

    public Integer getOrgZoneId() {
        return orgZoneId;
    }

    public void setOrgZoneId(Integer orgZoneId) {
        this.orgZoneId = orgZoneId;
    }

    public List<TsZone> getOrgZoneList() {
        return orgZoneList;
    }

    public void setOrgZoneList(List<TsZone> orgZoneList) {
        this.orgZoneList = orgZoneList;
    }

    public TdZwTjorginfoApply getTjorginfoApply() {
        return tjorginfoApply;
    }

    public void setTjorginfoApply(TdZwTjorginfoApply tjorginfoApply) {
        this.tjorginfoApply = tjorginfoApply;
    }

    public List<TdZwTjorgannexApply> getTjorgannexApplyList() {
        return tjorgannexApplyList;
    }

    public void setTjorgannexApplyList(List<TdZwTjorgannexApply> tjorgannexApplyList) {
        this.tjorgannexApplyList = tjorgannexApplyList;
    }

    public TsSimpleCode getQualType() {
        return qualType;
    }

    public void setQualType(TsSimpleCode qualType) {
        this.qualType = qualType;
    }

    public TsSimpleCode getApplyType() {
        return applyType;
    }

    public void setApplyType(TsSimpleCode applyType) {
        this.applyType = applyType;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public List<TdZwTjorgannexApply> getApplyAnnexList() {
        return applyAnnexList;
    }

    public void setApplyAnnexList(List<TdZwTjorgannexApply> applyAnnexList) {
        this.applyAnnexList = applyAnnexList;
    }

    public Integer getSelectAnnexType() {
        return selectAnnexType;
    }

    public void setSelectAnnexType(Integer selectAnnexType) {
        this.selectAnnexType = selectAnnexType;
    }

    public TdZwTjorgannexApply getSelectAnnex() {
        return selectAnnex;
    }

    public void setSelectAnnex(TdZwTjorgannexApply selectAnnex) {
        this.selectAnnex = selectAnnex;
    }

    public List<Integer> getAnnexApplyRemoveRidList() {
        return annexApplyRemoveRidList;
    }

    public void setAnnexApplyRemoveRidList(List<Integer> annexApplyRemoveRidList) {
        this.annexApplyRemoveRidList = annexApplyRemoveRidList;
    }

    public Boolean getIfFinalAudit() {
        return ifFinalAudit;
    }

    public void setIfFinalAudit(Boolean ifFinalAudit) {
        this.ifFinalAudit = ifFinalAudit;
    }

    public Integer getOrgInfoState() {
        return orgInfoState;
    }

    public void setOrgInfoState(Integer orgInfoState) {
        this.orgInfoState = orgInfoState;
    }

    public Map<TsSimpleCode, List<TsSimpleCode>> getDiagMap() {
        return diagMap;
    }

    public void setDiagMap(Map<TsSimpleCode, List<TsSimpleCode>> diagMap) {
        this.diagMap = diagMap;
    }

    public List<TjitemsApplyVo> getShowItemList() {
        return showItemList;
    }

    public void setShowItemList(List<TjitemsApplyVo> showItemList) {
        this.showItemList = showItemList;
    }

    public Map<String, TsSimpleCode> getServiceItemSimpleCodeMap() {
        return serviceItemSimpleCodeMap;
    }

    public void setServiceItemSimpleCodeMap(Map<String, TsSimpleCode> serviceItemSimpleCodeMap) {
        this.serviceItemSimpleCodeMap = serviceItemSimpleCodeMap;
    }

    public Map<Integer, TsZone> getTsZoneMap() {
        return tsZoneMap;
    }

    public void setTsZoneMap(Map<Integer, TsZone> tsZoneMap) {
        this.tsZoneMap = tsZoneMap;
    }

    public String getSelItemId() {
        return selItemId;
    }

    public void setSelItemId(String selItemId) {
        this.selItemId = selItemId;
    }

    public List<Object[]> getTjorgOutcarApplyList() {
        return tjorgOutcarApplyList;
    }

    public void setTjorgOutcarApplyList(List<Object[]> tjorgOutcarApplyList) {
        this.tjorgOutcarApplyList = tjorgOutcarApplyList;
    }

    public String getJcItemRid() {
        return jcItemRid;
    }

    public void setJcItemRid(String jcItemRid) {
        this.jcItemRid = jcItemRid;
    }

    public List<ZwJkItemRowPO> getJkItemRowList() {
        return jkItemRowList;
    }

    public void setJkItemRowList(List<ZwJkItemRowPO> jkItemRowList) {
        this.jkItemRowList = jkItemRowList;
    }

    public List<TsSimpleCode> getInstitutionList() {
        return institutionList;
    }

    public void setInstitutionList(List<TsSimpleCode> institutionList) {
        this.institutionList = institutionList;
    }

    public List<TsSimpleCode> getTopOfInstitutionList() {
        return topOfInstitutionList;
    }

    public void setTopOfInstitutionList(List<TsSimpleCode> topOfInstitutionList) {
        this.topOfInstitutionList = topOfInstitutionList;
    }

    public Map<Integer, TsSimpleCode> getTopInstitutionMap() {
        return topInstitutionMap;
    }

    public void setTopInstitutionMap(Map<Integer, TsSimpleCode> topInstitutionMap) {
        this.topInstitutionMap = topInstitutionMap;
    }

    public Map<Integer, List<TsSimpleCode>> getInstitutionMap() {
        return institutionMap;
    }

    public void setInstitutionMap(Map<Integer, List<TsSimpleCode>> institutionMap) {
        this.institutionMap = institutionMap;
    }

    public Map<Integer, List<TdZwTjorgQualAnnexApy>> getQualAnnexMap() {
        return qualAnnexMap;
    }

    public void setQualAnnexMap(Map<Integer, List<TdZwTjorgQualAnnexApy>> qualAnnexMap) {
        this.qualAnnexMap = qualAnnexMap;
    }

    public Integer getCodeId() {
        return codeId;
    }

    public void setCodeId(Integer codeId) {
        this.codeId = codeId;
    }

    public TdZwTjorgQualAnnexApy getAnnexTmp() {
        return annexTmp;
    }

    public void setAnnexTmp(TdZwTjorgQualAnnexApy annexTmp) {
        this.annexTmp = annexTmp;
    }

    public String getTopInsPanelStr() {
        return topInsPanelStr;
    }

    public void setTopInsPanelStr(String topInsPanelStr) {
        this.topInsPanelStr = topInsPanelStr;
    }

    public List<ZwAnnexsPO> getAnnexsPOList() {
        return annexsPOList;
    }

    public void setAnnexsPOList(List<ZwAnnexsPO> annexsPOList) {
        this.annexsPOList = annexsPOList;
    }

    public Object[] getSelectZwAnnex() {
        return selectZwAnnex;
    }

    public void setSelectZwAnnex(Object[] selectZwAnnex) {
        this.selectZwAnnex = selectZwAnnex;
    }

    public Integer getSearchZoneId() {
        return searchZoneId;
    }

    public void setSearchZoneId(Integer searchZoneId) {
        this.searchZoneId = searchZoneId;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public void setOrgannexApply(TdZwTjorgannexApply organnexApply) {
        this.organnexApply = organnexApply;
    }

    public TdZwTjorgannexApply getOrgannexApply() {
        return organnexApply;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Integer getUnitLevel() {
        return unitLevel;
    }

    public void setUnitLevel(Integer unitLevel) {
        this.unitLevel = unitLevel;
    }

    public TdZwTjorgannexApply getYlAnnex() {
        return ylAnnex;
    }

    public void setYlAnnex(TdZwTjorgannexApply ylAnnex) {
        this.ylAnnex = ylAnnex;
    }

    public TdZwTjorgOutcarApply getOutcarZzsb() {
        return outcarZzsb;
    }

    public void setOutcarZzsb(TdZwTjorgOutcarApply outcarZzsb) {
        this.outcarZzsb = outcarZzsb;
    }

    public Object[] getModOutcarZzsb() {
        return modOutcarZzsb;
    }

    public void setModOutcarZzsb(Object[] modOutcarZzsb) {
        this.modOutcarZzsb = modOutcarZzsb;
    }

    public String getOutCarInstNames() {
        return outCarInstNames;
    }

    public void setOutCarInstNames(String outCarInstNames) {
        this.outCarInstNames = outCarInstNames;
    }

    public String getSearchOutCarInstName() {
        return searchOutCarInstName;
    }

    public void setSearchOutCarInstName(String searchOutCarInstName) {
        this.searchOutCarInstName = searchOutCarInstName;
    }

    public String getSearchOutCarModel() {
        return searchOutCarModel;
    }

    public void setSearchOutCarModel(String searchOutCarModel) {
        this.searchOutCarModel = searchOutCarModel;
    }

    public List<OutCarInstPO> getOutCarInstList() {
        return outCarInstList;
    }

    public void setOutCarInstList(List<OutCarInstPO> outCarInstList) {
        this.outCarInstList = outCarInstList;
    }

    public Map<Integer, String> getOutCarInstMap() {
        return outCarInstMap;
    }

    public void setOutCarInstMap(Map<Integer, String> outCarInstMap) {
        this.outCarInstMap = outCarInstMap;
    }

    public List<Integer> getOutCarInstSelectRidList() {
        return outCarInstSelectRidList;
    }

    public void setOutCarInstSelectRidList(List<Integer> outCarInstSelectRidList) {
        this.outCarInstSelectRidList = outCarInstSelectRidList;
    }

    public List<TsZone> getAllTsZoneList() {
        return allTsZoneList;
    }

    public void setAllTsZoneList(List<TsZone> allTsZoneList) {
        this.allTsZoneList = allTsZoneList;
    }

    public ZzsbPsnInfoVO getPsnInfoVO() {
        return psnInfoVO;
    }

    public void setPsnInfoVO(ZzsbPsnInfoVO psnInfoVO) {
        this.psnInfoVO = psnInfoVO;
    }

    public TdzwPsnInfoZzsbBaseNew getPsnInfoBase() {
        return psnInfoBase;
    }

    public void setPsnInfoBase(TdzwPsnInfoZzsbBaseNew psnInfoBase) {
        this.psnInfoBase = psnInfoBase;
    }

    public ZzsbInstInfoVO getInstInfoVO() {
        return instInfoVO;
    }

    public void setInstInfoVO(ZzsbInstInfoVO instInfoVO) {
        this.instInfoVO = instInfoVO;
    }

    public TdZwTjorgannexApply getReportReceipt() {
        return reportReceipt;
    }

    public void setReportReceipt(TdZwTjorgannexApply reportReceipt) {
        this.reportReceipt = reportReceipt;
    }

    public String getReportReceiptFilePath() {
        return reportReceiptFilePath;
    }

    public void setReportReceiptFilePath(String reportReceiptFilePath) {
        this.reportReceiptFilePath = reportReceiptFilePath;
    }

    public Integer getAnnexTypeBase() {
        return annexTypeBase;
    }

    public void setAnnexTypeBase(Integer annexTypeBase) {
        this.annexTypeBase = annexTypeBase;
    }

    public Integer getZzjgType() {
        return zzjgType;
    }

    public void setZzjgType(Integer zzjgType) {
        this.zzjgType = zzjgType;
    }

    public String getOutCarVersion() {
        return outCarVersion;
    }

    public void setOutCarVersion(String outCarVersion) {
        this.outCarVersion = outCarVersion;
    }

    public Integer getOutCarOpFile() {
        return outCarOpFile;
    }

    public void setOutCarOpFile(Integer outCarOpFile) {
        this.outCarOpFile = outCarOpFile;
    }
}
