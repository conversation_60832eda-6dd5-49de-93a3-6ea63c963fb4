package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-5-21
 */
@Entity
@Table(name = "TB_YSJC_CHK_ABILITY_REL")
@SequenceGenerator(name = "TbYsjcChkAbilityRel", sequenceName = "TB_YSJC_CHK_ABILITY_REL_SEQ", allocationSize = 1)
public class TbYsjcChkAbilityRelZzsb implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbYsjcChkAbilityZzsb fkByMainId;
	private TsSimpleCode fkByItemId;
	private String jcWay;
	private TbTjLawCheckZzsb fkByJcBaseId;
	private Integer labApprovalTag;
	private Integer qualApprovalTag;
	private Integer outTag;
	private Integer state;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	private String limitDesc;

	public TbYsjcChkAbilityRelZzsb() {
	}

	public TbYsjcChkAbilityRelZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbYsjcChkAbilityRel")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbYsjcChkAbilityZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbYsjcChkAbilityZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TsSimpleCode getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TsSimpleCode fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "JC_WAY")	
	public String getJcWay() {
		return jcWay;
	}

	public void setJcWay(String jcWay) {
		this.jcWay = jcWay;
	}	
			
	@ManyToOne
	@JoinColumn(name = "JC_BASE_ID")			
	public TbTjLawCheckZzsb getFkByJcBaseId() {
		return fkByJcBaseId;
	}

	public void setFkByJcBaseId(TbTjLawCheckZzsb fkByJcBaseId) {
		this.fkByJcBaseId = fkByJcBaseId;
	}	
			
	@Column(name = "LAB_APPROVAL_TAG")	
	public Integer getLabApprovalTag() {
		return labApprovalTag;
	}

	public void setLabApprovalTag(Integer labApprovalTag) {
		this.labApprovalTag = labApprovalTag;
	}	
			
	@Column(name = "QUAL_APPROVAL_TAG")	
	public Integer getQualApprovalTag() {
		return qualApprovalTag;
	}

	public void setQualApprovalTag(Integer qualApprovalTag) {
		this.qualApprovalTag = qualApprovalTag;
	}	
			
	@Column(name = "OUT_TAG")	
	public Integer getOutTag() {
		return outTag;
	}

	public void setOutTag(Integer outTag) {
		this.outTag = outTag;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Column(name = "LIMIT_DESC")
	public String getLimitDesc() {
		return limitDesc;
	}
	public void setLimitDesc(String limitDesc) {
		this.limitDesc = limitDesc;
	}
}