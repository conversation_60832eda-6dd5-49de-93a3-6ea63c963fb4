package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.JsfUtil;
import com.chis.modules.heth.zzsb.entity.*;
import com.google.common.collect.Lists;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.List;

/**
 * 放射机构资质申报
 * Created by wlj on 2015-04-22.
 */
@ManagedBean(name = "zwSrvOrgInfoEditZzsbBean")
@ViewScoped
public class ZwSrvOrgInfoEditZzsbBean extends ZwOrgInfoBaseZzsbBean {
    private static final long serialVersionUID = -176666137235734555L;
    private TdZwPsntjMainZzsb firstTjMain = new TdZwPsntjMainZzsb();
    private List<TdZwPsntjSubZzsb> firstTjSubList = Lists.newArrayList();
    private TdZwPsntjMainZzsb lastTjMain = new TdZwPsntjMainZzsb();
    private List<TdZwPsntjSubZzsb> lastTjSubList = Lists.newArrayList();
    private TdZwPsntjSubZzsb tjSub;
    
    private List<TdZwInstJcmainZzsb> jcMainList  = Lists.newArrayList();
    private TdZwInstJcmainZzsb jcMain = new TdZwInstJcmainZzsb();
    private List<TdZwInstJcsubZzsb> jcSubList = Lists.newArrayList();
    private TdZwInstJcsubZzsb jcSub = new TdZwInstJcsubZzsb();

    private List<TdZwInstSbjcmainZzsb> sbJcMainList  = Lists.newArrayList();
    private TdZwInstSbjcmainZzsb sbJcMain = new TdZwInstSbjcmainZzsb();
    private List<TdZwInstSbjcsubZzsb> sbJcSubList = Lists.newArrayList();
    private TdZwInstSbjcsubZzsb sbJcSub = new TdZwInstSbjcsubZzsb();
    
    public ZwSrvOrgInfoEditZzsbBean() {
        super.init();
    }

    
    /**********设备检测开始************/
    public void addInitInstSbJc() {
    	this.sbJcMainList = this.selectOrgInst.getTdZwInstinfo().getInstSbjcmainList();
    }
    
    public void addInitInstSbJcSub() {
    	this.sbJcSubList = sbJcMain.getSubList();
    }
    
    public void addLineInstSbJc() {
    	TdZwInstSbjcmainZzsb t = new TdZwInstSbjcmainZzsb();
    	t.setFkByInstId(this.selectOrgInst.getTdZwInstinfo());
    	t.setCreateManid(super.sessionData.getUser().getRid());
    	this.sbJcMainList.add(t);
    }
    
    public void addLineInstSbJcSub() {
    	TdZwInstSbjcsubZzsb t = new TdZwInstSbjcsubZzsb();
    	t.setFkByMainId(this.sbJcMain);
    	t.setCreateManid(super.sessionData.getUser().getRid());
    	
    	this.sbJcSubList.add(t);
    }
    
    public void delLineInstSbJc() {
    	this.sbJcMainList.remove(this.sbJcMain);
    }
    
    public void delLineInstSbJcSub() {
    	this.sbJcSubList.remove(this.sbJcSub);
    }
    
    public void saveInstSbJc() {
    	RequestContext.getCurrentInstance().execute("PF('InstSbJcDialog').hide()");
    }
    
    public void saveInstSbJcSub() {
    	RequestContext.getCurrentInstance().execute("PF('InstSbJcSubDialog').hide()");
    }
    /**********设备检测结束************/
    
    /**********场所监测开始************/
    public void addInitInstJc() {
    	this.jcMainList = this.selectOrgInst.getTdZwInstinfo().getInstJcMainList();
    }
    
    public void addInitInstJcSub() {
    	this.jcSubList = jcMain.getSubList();
    }
    
    public void addLineInstJc() {
    	TdZwInstJcmainZzsb t = new TdZwInstJcmainZzsb();
    	t.setFkByInstId(this.selectOrgInst.getTdZwInstinfo());
    	t.setCreateManid(super.sessionData.getUser().getRid());
    	this.jcMainList.add(t);
    }
    
    public void addLineInstJcSub() {
    	TdZwInstJcsubZzsb t = new TdZwInstJcsubZzsb();
    	t.setFkByMainId(this.jcMain);
    	t.setCreateManid(super.sessionData.getUser().getRid());
    	
    	this.jcSubList.add(t);
    }
    
    public void delLineInstJc() {
    	this.jcMainList.remove(this.jcMain);
    }
    
    public void delLineInstJcSub() {
    	this.jcSubList.remove(this.jcSub);
    }
    
    public void saveInstJc() {
    	RequestContext.getCurrentInstance().execute("PF('InstJcDialog').hide()");
    }
    
    public void saveInstJcSub() {
    	RequestContext.getCurrentInstance().execute("PF('InstJcSubDialog').hide()");
    }
    /**********场所监测结束************/    
    
    
    /**********放射人员体检开始************/
    public void addInitTjRst() {
    	List<TdZwPsntjMainZzsb> mainList = selectZwOrgPsn.getTdZwPsninfo().getTjMainList();
    	if(null != mainList && mainList.size() > 0) {
    		for(TdZwPsntjMainZzsb main : mainList) {
    			if(main.getTjType().intValue() == 1) {
    				//入职前
    				this.firstTjMain = main;
    				this.firstTjSubList = main.getSubList();
    			}else {
    				//末次
    				this.lastTjMain = main;
    				this.lastTjSubList = main.getSubList();
    			}
    		}
    	}else {
    	    this.firstTjMain = new TdZwPsntjMainZzsb();
    	    this.firstTjMain.setFkByEmpId(selectZwOrgPsn.getTdZwPsninfo());
    	    this.firstTjMain.setTjType(1);
    	    this.firstTjSubList = Lists.newArrayList();
    	    this.lastTjMain = new TdZwPsntjMainZzsb();
    	    this.lastTjMain.setFkByEmpId(selectZwOrgPsn.getTdZwPsninfo());
    	    this.lastTjMain.setTjType(2);
    	    this.lastTjSubList = Lists.newArrayList();
    	}
    }
    
    public void addLine(Integer type) {
    	TdZwPsntjSubZzsb sub = new TdZwPsntjSubZzsb();
    	if(type.intValue() == 1) {
    		//就业前体检
    		sub.setFkByMainId(this.firstTjMain);
    		this.firstTjSubList.add(sub);
    	}else {
    		//末次体检
    		sub.setFkByMainId(this.lastTjMain);
    		this.lastTjSubList.add(sub);
    	}
    }
    
    public void delLine(Integer type) {
    	if(type.intValue() == 1) {
    		//就业前体检
    		this.firstTjSubList.remove(this.tjSub);
    	}else {
    		//末次体检
    		this.lastTjSubList.remove(this.tjSub);
    	}
    }
    
    public void saveTjRst() {
        if(null == this.firstTjMain.getTjDate()) {
            JsfUtil.addErrorMessage("体检日期不能为空！");
            return;
        }
    	this.firstTjMain.setSubList(this.firstTjSubList);
    	this.lastTjMain.setSubList(this.lastTjSubList);
    	
    	if(null == selectZwOrgPsn.getTdZwPsninfo().getTjMainList() || selectZwOrgPsn.getTdZwPsninfo().getTjMainList().size() == 0) {
    		List<TdZwPsntjMainZzsb> mainList = Lists.newArrayList();
    		mainList.add(this.firstTjMain);
    		mainList.add(this.lastTjMain);
    		selectZwOrgPsn.getTdZwPsninfo().setTjMainList(mainList);
    	}
    	
    	RequestContext.getCurrentInstance().execute("PF('PsnTjDialog').hide()");
    }
    /**********放射人员体检结束************/
    
    @Override
    public Class getZwIntellReportClass() {
        return TdZwSrvorginfoZzsb.class;
    }

    @Override
    public Integer getOrgInfoType() {
        return 1;
    }
    

	public TdZwPsntjMainZzsb getFirstTjMain() {
		return firstTjMain;
	}

	public void setFirstTjMain(TdZwPsntjMainZzsb firstTjMain) {
		this.firstTjMain = firstTjMain;
	}

	public TdZwPsntjMainZzsb getLastTjMain() {
		return lastTjMain;
	}

	public void setLastTjMain(TdZwPsntjMainZzsb lastTjMain) {
		this.lastTjMain = lastTjMain;
	}

	public List<TdZwPsntjSubZzsb> getFirstTjSubList() {
		return firstTjSubList;
	}

	public void setFirstTjSubList(List<TdZwPsntjSubZzsb> firstTjSubList) {
		this.firstTjSubList = firstTjSubList;
	}

	public List<TdZwPsntjSubZzsb> getLastTjSubList() {
		return lastTjSubList;
	}

	public void setLastTjSubList(List<TdZwPsntjSubZzsb> lastTjSubList) {
		this.lastTjSubList = lastTjSubList;
	}

	public TdZwPsntjSubZzsb getTjSub() {
		return tjSub;
	}

	public void setTjSub(TdZwPsntjSubZzsb tjSub) {
		this.tjSub = tjSub;
	}

	public List<TdZwInstJcmainZzsb> getJcMainList() {
		return jcMainList;
	}

	public void setJcMainList(List<TdZwInstJcmainZzsb> jcMainList) {
		this.jcMainList = jcMainList;
	}

	public TdZwInstJcmainZzsb getJcMain() {
		return jcMain;
	}

	public void setJcMain(TdZwInstJcmainZzsb jcMain) {
		this.jcMain = jcMain;
	}

	public List<TdZwInstJcsubZzsb> getJcSubList() {
		return jcSubList;
	}

	public void setJcSubList(List<TdZwInstJcsubZzsb> jcSubList) {
		this.jcSubList = jcSubList;
	}

	public TdZwInstJcsubZzsb getJcSub() {
		return jcSub;
	}

	public void setJcSub(TdZwInstJcsubZzsb jcSub) {
		this.jcSub = jcSub;
	}

	public List<TdZwInstSbjcmainZzsb> getSbJcMainList() {
		return sbJcMainList;
	}

	public void setSbJcMainList(List<TdZwInstSbjcmainZzsb> sbJcMainList) {
		this.sbJcMainList = sbJcMainList;
	}

	public TdZwInstSbjcmainZzsb getSbJcMain() {
		return sbJcMain;
	}

	public void setSbJcMain(TdZwInstSbjcmainZzsb sbJcMain) {
		this.sbJcMain = sbJcMain;
	}

	public List<TdZwInstSbjcsubZzsb> getSbJcSubList() {
		return sbJcSubList;
	}

	public void setSbJcSubList(List<TdZwInstSbjcsubZzsb> sbJcSubList) {
		this.sbJcSubList = sbJcSubList;
	}

	public TdZwInstSbjcsubZzsb getSbJcSub() {
		return sbJcSub;
	}

	public void setSbJcSub(TdZwInstSbjcsubZzsb sbJcSub) {
		this.sbJcSub = sbJcSub;
	}

}
