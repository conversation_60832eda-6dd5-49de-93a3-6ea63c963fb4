package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_DIAGINST")
@SequenceGenerator(name = "TdZwDiaginstSeq", sequenceName = "TD_ZW_DIAGINST_SEQ", allocationSize = 1)
public class TdZwDiaginstZzsb implements Serializable,IZwInstZzsb {

	private static final long serialVersionUID = 1734247136866420139L;

	private Integer rid;
	private TdZwDiagorginfoZzsb tdZwDiagorginfo;
	private TdZwInstinfoZzsb tdZwInstinfo;
	private Date createDate;
	private Integer createManid;

	public TdZwDiaginstZzsb() {
	}

	public TdZwDiaginstZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwDiaginstSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwDiagorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwDiagorginfo = (TdZwDiagorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwDiagorginfoZzsb getTdZwDiagorginfo() {
		return this.tdZwDiagorginfo;
	}

	public void setTdZwDiagorginfo(TdZwDiagorginfoZzsb tdZwDiagorginfo) {
		this.tdZwDiagorginfo = tdZwDiagorginfo;
	}

	@ManyToOne
	@JoinColumn(name = "INST_ID" )
	public TdZwInstinfoZzsb getTdZwInstinfo() {
		return this.tdZwInstinfo;
	}

	public void setTdZwInstinfo(TdZwInstinfoZzsb tdZwInstinfo) {
		this.tdZwInstinfo = tdZwInstinfo;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}