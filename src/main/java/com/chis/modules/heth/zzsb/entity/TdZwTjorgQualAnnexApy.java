package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-14
 */
@Entity
@Table(name = "TD_ZW_TJORG_QUAL_ANNEX_APY")
@SequenceGenerator(name = "TdZwTjorgQualAnnexApy", sequenceName = "TD_ZW_TJORG_QUAL_ANNEX_APY_SEQ", allocationSize = 1)
public class TdZwTjorgQualAnnexApy implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorginfoApply fkByMainId;
	private TsSimpleCode fkByRuleId;
	private String otherRuleName;
	private Integer ifHas;
	private String annexPath;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwTjorgQualAnnexApy() {
	}
	public TdZwTjorgQualAnnexApy(TsSimpleCode fkByRuleId){
		this.fkByRuleId = fkByRuleId;
	}
	public TdZwTjorgQualAnnexApy(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgQualAnnexApy")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwTjorginfoApply getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwTjorginfoApply fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "RULE_ID")			
	public TsSimpleCode getFkByRuleId() {
		return fkByRuleId;
	}

	public void setFkByRuleId(TsSimpleCode fkByRuleId) {
		this.fkByRuleId = fkByRuleId;
	}	
			
	@Column(name = "OTHER_RULE_NAME")	
	public String getOtherRuleName() {
		return otherRuleName;
	}

	public void setOtherRuleName(String otherRuleName) {
		this.otherRuleName = otherRuleName;
	}	
			
	@Column(name = "IF_HAS")	
	public Integer getIfHas() {
		return ifHas;
	}

	public void setIfHas(Integer ifHas) {
		this.ifHas = ifHas;
	}	
			
	@Column(name = "ANNEX_PATH")	
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}