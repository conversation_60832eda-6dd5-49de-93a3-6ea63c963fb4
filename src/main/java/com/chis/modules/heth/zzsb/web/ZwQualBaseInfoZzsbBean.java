package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.entity.vo.SupportInstAbilityVO;
import com.chis.modules.heth.zzsb.logic.*;
import com.chis.modules.heth.zzsb.service.ZzsbZwIntellReportServiceImpl;
import com.chis.modules.heth.zzsb.service.ZzsbZwPsnPlanServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <p>类描述：资质申报基本信息</p>
 * @ClassAuthor qrr,2018年8月27日,ZwQualBaseInfoBean
 * */
public abstract class ZwQualBaseInfoZzsbBean extends FacesBean{
	private static final long serialVersionUID = 1L;
	/**
	 * 存在session中的对象
	 */
	protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**
	 * 查询统计会话bean
	 */
	protected ZzsbZwIntellReportServiceImpl zwIntellReportServiceImpl = (ZzsbZwIntellReportServiceImpl) SpringContextHolder
			.getBean(ZzsbZwIntellReportServiceImpl.class);
	protected CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	protected ZzsbZwPsnPlanServiceImpl psnPlanServiceImpl = SpringContextHolder.getBean(ZzsbZwPsnPlanServiceImpl.class);
	protected SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	protected IZwOrginfoZzsb zwOrginfoEntity;
	/* 职务列表 */
	protected List<String> careerList = new ArrayList<String>();
	/* 服务项目 */
	protected String[] serviceObj;
	protected List<SelectItem> serviceObjList = new ArrayList<SelectItem>();
	/**服务项目查看*/
	protected String[] itemStr;
	/**职业健康项目*/
	protected List<TsSimpleCode> jkList=new ArrayList<TsSimpleCode>();
	/*放射卫生资质申报  服务项目调整*/
	protected List<ZwItemPO> fsServiceObjList = new ArrayList<>();

	/**资质等级*/
	protected List<TsSimpleCode> levelList = new ArrayList<TsSimpleCode>();

	/**地区树*/
	protected TreeNode zoneTree;
	protected TreeNode[] selectZones;
	protected String zoneNames;
	protected String zoneIds;
	protected List<TsZone> outZoneList;

	/** 检查机构资质备案有无有效期限 空或0：无 1：有 默认空或0*/
	protected String ifValidityPeriod;
	/** 有无资质机构备案审核流程 空或0：无 1：有 默认空或0*/
	protected Boolean ifReviewProcess;
	/** 是否有服务明细 空或0：无 1：有 默认空或0*/
	protected String ifServiceDetails;
	/**备案编号提示内容*/
	protected String certNoTip;
	/** 备案日期开始日期最大日期，不能大于今天也不能大于有效结束日期*/
	protected Date firstGetdayMaxDate = new Date();
	/*服务项目小类*/
	protected List<ZwJkItemPO> jkItemList= new ArrayList<>();
	protected List<ZwJkItemRowPO> jkItemRowList= new ArrayList<>();
	/** 职业病诊断机构是否显示备案单位名称、放射卫生技术服务机构与职业卫生技术服务机构是否显示资质批准单位 0否1是 */
	protected String ifShowApproveUnit;
	/** 四类资质的资质附件是否显示房屋所有权证或租赁合同，0否1是，默认为0 */
	protected String ifHaveHouseAnnex;
	/**四类资质申报是否有审核功能（默认为无；0：无；1：有）*/
	protected String ifCheckOrg;
	/** 支撑机构有无科研、学术信息 */
	protected String ifHasReSearchAndAcademic;
	/** 列表数据 */
	protected List<List<TdZwSupportTrain>> showSupportTrainList = new ArrayList<>();
	/** 标题用学术培训类别 */
	protected List<List<TsSimpleCode>> showAcademicList = new ArrayList<>();
	/** 添加、修改学术培训信息 弹框数据集 */
	protected List<SupportTrainPO> supportTrainDiagDataList = new ArrayList<>();
	/** 修改、删除支撑机构学术培训信息，传递的年份 */
	protected Integer trainYear;
	/** 支撑机构学术培训信息弹出框 用于删除、上传附件的对象 */
	protected TdZwSupportTrain editSupportTrain;
	/** 用于支撑机构学术培训列表遍历动态数据 */
	protected Integer academicDataSize = 0;
	/**
	 * 支撑机构-仪器设备配置信息-仪器设备码表
	 */
	protected List<TsSimpleCode> supportInstSimpleCodeList;
	/**
	 * 支撑机构-仪器设备配置信息-VO
	 */
	protected List<SupportInstAbilityVO> supportInstVOList;
	/**
	 * 支撑机构-仪器设备配置信息-VO-存储用
	 */
	protected List<SupportInstAbilityVO> allSupportInstVOList;

	/**支撑机构-单位类型 1:疾控  2：职业防治院*/
	protected String unitType;

	/**支撑机构-选中的具备职业健康技术服务相关资质或能力的情况*/
	protected Integer[] selectedQualTypes;

	protected String redStr="<font color='red'>*</font>";

	/** 支撑机构—职业健康教育和健康促进 */
	protected TdZwSupportHealth supportHealth;

	/**
	 * 支撑机构-能力建设码表
	 */
	protected List<TsSimpleCode> abilitySimpleCodeList;
	/**
	 * 支撑机构-能力建设 页面显示集合
	 */
	protected List<SupportInstAbilityVO> abilityShowList;
	/**
	 * 支撑机构-能力建设 存储对象集合
	 */
	protected List<SupportInstAbilityVO> abilitySaveList;


	public ZwQualBaseInfoZzsbBean(){
		//职业健康服务项目
		jkList=commService.findNumSimpleCodesByTypeId("5018");
		ifValidityPeriod = PropertyUtils.getValueWithoutException("zzsb.ifValidityPeriod");
		ifServiceDetails = PropertyUtils.getValueWithoutException("zzsb.ifServiceDetails");
		certNoTip = PropertyUtils.getValueWithoutException("zzsb.certNoTip");
		String reviewProcess = PropertyUtils.getValueWithoutException("zzsb.ifReviewProcess");
		if(StringUtils.isBlank(reviewProcess)||(StringUtils.isNotBlank(reviewProcess)&&"0".equals(reviewProcess))){
			ifReviewProcess=false;
		}else{
			ifReviewProcess=true;
		}
		this.ifShowApproveUnit = PropertyUtils.getValueWithoutException("zzsb.ifShowApproveUnit");
		this.ifHaveHouseAnnex = PropertyUtils.getValueWithoutException("zzsb.ifHaveHouseAnnex");
		this.ifCheckOrg = PropertyUtils.getValueWithoutException("zzsb.ifCheckOrg");
		this.ifHasReSearchAndAcademic = PropertyUtils.getValueWithoutException("zzsb.hasResearchAndAcademiaInfo");
	}

	/**
	 * <p>方法描述：初始化服务项目集合</p>
	 * @MethodAuthor qrr,2018年8月27日,initServiceObjList
	 * */
	public void initServiceObjList(List<TsSimpleCode> serList) {
		if(null != serList && serList.size()>0){
			for(TsSimpleCode t:serList){
				if("1".equals(t.getIfReveal().toString())){
					serviceObjList.add(new SelectItem(t.getCodeNo(),t.getCodeName()));
				}
			}
		}
	}

	/**
	 * <p>方法描述： 放射卫生资质申报 服务项目调整功   </p>
	 * @MethodAuthor  yzz，2021-04-21，initServiceObjList
	 *
	 **/
	public void initServiceObjList(List<TsSimpleCode> serList, List<TsSimpleCode> subList) {
		if(null != serList && serList.size()>0){
			for(TsSimpleCode t:serList){
				//if("1".equals(t.getIfReveal().toString())){
					ZwItemPO zwItemPO = new ZwItemPO(t.getCodeNo(),t.getCodeName(), t.getExtendS1(), false, false);
					this.fsServiceObjList.add(zwItemPO);
					if(!CollectionUtils.isEmpty(subList)) {
						for (TsSimpleCode sub : subList) {
							if(null != sub.getExtendS2()) {
								if(sub.getExtendS2().toString().equals(t.getCodeNo())) {
									zwItemPO.getZwItemSubPOList().add(new ZwItemSubPO(sub.getCodeNo(), sub.getCodeName(), true));
								}
							}
						}
					}

				//}
			}
		}
	}


	/**
	 * <p>方法描述： 初始化服务明细小类   </p>
	 * @MethodAuthor  yzz，2021-06-30，initSerItem
	 **/
	public void initSerItem(List<ZwJkItemPO> zwJkItemPOS){
        Map<String,String[]> selectItems=new HashMap<String,String[]>();
        Map<String,String[]> selectItemsExter=new HashMap<String,String[]>();
        Map<String,String> selectItemName=new HashMap<String,String>();
        List<String> zwSvritmNoArray=new ArrayList<>();
        if(zwOrginfoEntity.getZwSvritm() != null && zwOrginfoEntity.getZwSvritm().size() > 0){
                List<IZwSvritmZzsb> list = zwOrginfoEntity.getZwSvritm();

				for(int i = 0 ; i < list.size();i++){
					IZwSvritmZzsb t = list.get(i);
					zwSvritmNoArray.add(t.getItemCode().toString());
					List<IZwSvritmSubZzsb> zwSvritmSubList = t.getZwSvritmSubList();
					if(!CollectionUtils.isEmpty(zwSvritmSubList)) {
						String[] itemSubs = new String[zwSvritmSubList.size()];
						List<String> itemSubsExter = new ArrayList<>();
						StringBuffer itemNames=new StringBuffer();
						for(int j = 0 ; j<zwSvritmSubList.size(); j++){
							IZwSvritmSubZzsb sub = zwSvritmSubList.get(j);
							if (null == sub.getFkBySubItemId()) {
								continue;
							}
							itemSubs[j] = sub.getFkBySubItemId().getRid().toString();
							itemNames.append(sub.getFkBySubItemId().getCodeName()).append("，");
							if(sub.getIfExterInspect()!=null && sub.getIfExterInspect()==1){
								itemSubsExter.add(itemSubs[j]);
							}
						}
						selectItems.put(String.valueOf(t.getItemCode()),itemSubs);
						selectItemName.put(String.valueOf(t.getItemCode()),itemNames.toString().substring(0,itemNames.toString().length()-1));
						selectItemsExter.put(String.valueOf(t.getItemCode()),itemSubsExter.toArray(new String[0]));
					}
				}

			}
            List<ZwJkItemPO> newZwJkItemPO=new ArrayList<>();
            for(ZwJkItemPO itemPO:zwJkItemPOS){
                String key=itemPO.getJcItem().getCodeNo();
                if(null!=selectItems&&null!=selectItems.get(key)&&selectItems.get(key).length>0){
                    itemPO.setSelectedSerItmDetail(selectItems.get(key));
					itemPO.setSelectedItemDetail(selectItemName.get(key));
                }
				itemPO.setSelectedIfExterInspect(selectItemsExter.get(key));
                if(zwSvritmNoArray.contains(itemPO.getJcItem().getCodeNo())){
					newZwJkItemPO.add(itemPO);
				}
            }
			for(ZwJkItemPO itemPO: newZwJkItemPO){
				if(itemPO.getSelectedSerItmDetail()!=null&&itemPO.getSelectedSerItmDetail().length==0){
					itemPO.setSelectedItemDetail(null);
				}
			}
            zwJkItemPOS.clear();
            zwJkItemPOS.addAll(newZwJkItemPO);
		}

	/**
	 * <p>方法描述：初始化服务项目</p>
	 * @MethodAuthor qrr,2018年8月27日,initService
	 * */
	public void initService(List<TsSimpleCode> serList) {
		serviceObj = null;
		if(zwOrginfoEntity.getZwSvritm() != null && zwOrginfoEntity.getZwSvritm().size() > 0){
			List<IZwSvritmZzsb> list = zwOrginfoEntity.getZwSvritm();
			serviceObj = new String[list.size()];
			itemStr=new String[list.size()];
			for(int i = 0 ; i < list.size();i++){
				IZwSvritmZzsb t = list.get(i);
				serviceObj[i] = t.getItemCode().toString();
				for(TsSimpleCode ts:serList){
					if(ts.getCodeNo().equals(t.getItemCode().toString())){
						itemStr[i]=ts.getCodeName();
					}
				}
			}
		}
	}
	/**
	 * <p>方法描述：初始化法定负责人职务</p>
	 * @MethodAuthor qrr,2018年8月27日,initCareerList
	 * */
	public void initCareerList() {
		List<TsSimpleCode> list = commService.findSimpleCodesByTypeId("2002");
		if (list != null && list.size() > 0) {
			for (TsSimpleCode t : list) {
				careerList.add(t.getCodeName());
			}
		}

	}

	/**
	 * @Description : 初始化资质等级列表
	 * @MethodAuthor: anjing
	 * @Date : 2019/9/24 10:57
	 **/
	public void initLevelList() {
		List<TsSimpleCode> list = commService.findSimpleCodesByTypeId("5321");
		if (list != null && list.size() > 0) {
			for (TsSimpleCode t : list) {
				this.levelList.add(t);
			}
		}
	}

	/**
	 * <p>方法描述：初始化资质申报信息
	 * plat:0上海1江苏
	 * </p>
	 * @MethodAuthor qrr,2018年8月27日,initReportData
	 */
	protected void initReportData(Integer zwOrgType,Integer plat) {
		// 根据单位Id获取不同的资质对象
		zwOrginfoEntity = zwIntellReportServiceImpl.findTdZwTjorginfo(zwOrgType, sessionData.getUser().getTsUnit()
				.getRid());
		if (zwOrginfoEntity == null) {
			Class clazz = getZwIntellReportClass();
			try {
				zwOrginfoEntity = (IZwOrginfoZzsb) clazz.newInstance();
				zwOrginfoEntity.setOrgName(sessionData.getUser().getTsUnit().getUnitname());
				zwOrginfoEntity.setState((short) 0);
				zwOrginfoEntity.setTsUnit(sessionData.getUser().getTsUnit());
				zwOrginfoEntity.setCancelState(0);
//				if (null!=plat) {
//					if (0==plat) {
//						//上海
//						zwOrginfoEntity.setCertNo("沪卫职检字[]第号");
//					}else if (1==plat) {
//						//江苏
//						zwOrginfoEntity.setCertNo("苏卫职技字[]第号");
//					}
//				}
				IZwOrginfoZzsb zwOrginfo1 = null;
				IZwOrginfoZzsb zwOrginfo2 = null;
				IZwOrginfoZzsb zwOrginfo3 = null;
				IZwOrginfoZzsb zwOrginfo4 = null;
				Integer unitid = sessionData.getUser().getTsUnit().getRid();
				if (zwOrgType.equals(0)) {
					zwOrginfo1 = zwIntellReportServiceImpl.findTdZwTjorginfo(1, unitid);
					zwOrginfo2 = zwIntellReportServiceImpl.findTdZwTjorginfo(2, unitid);
					zwOrginfo3 = zwIntellReportServiceImpl.findTdZwTjorginfo(3, unitid);
					zwOrginfo4 = zwIntellReportServiceImpl.findTdZwTjorginfo(4, unitid);
				} else if (zwOrgType.equals(1)) {
					zwOrginfo1 = zwIntellReportServiceImpl.findTdZwTjorginfo(0, unitid);
					zwOrginfo2 = zwIntellReportServiceImpl.findTdZwTjorginfo(2, unitid);
					zwOrginfo3 = zwIntellReportServiceImpl.findTdZwTjorginfo(3, unitid);
					zwOrginfo4 = zwIntellReportServiceImpl.findTdZwTjorginfo(4, unitid);
				} else if (zwOrgType.equals(2)) {
					zwOrginfo1 = zwIntellReportServiceImpl.findTdZwTjorginfo(0, unitid);
					zwOrginfo2 = zwIntellReportServiceImpl.findTdZwTjorginfo(1, unitid);
					zwOrginfo3 = zwIntellReportServiceImpl.findTdZwTjorginfo(3, unitid);
					zwOrginfo4 = zwIntellReportServiceImpl.findTdZwTjorginfo(4, unitid);
				}else if(zwOrgType.equals(4)){
					zwOrginfo1 = zwIntellReportServiceImpl.findTdZwTjorginfo(0, unitid);
					zwOrginfo2 = zwIntellReportServiceImpl.findTdZwTjorginfo(1, unitid);
					zwOrginfo3 = zwIntellReportServiceImpl.findTdZwTjorginfo(3, unitid);
					zwOrginfo4 = zwIntellReportServiceImpl.findTdZwTjorginfo(2, unitid);
				} else {
					zwOrginfo1 = zwIntellReportServiceImpl.findTdZwTjorginfo(0, unitid);
					zwOrginfo2 = zwIntellReportServiceImpl.findTdZwTjorginfo(1, unitid);
					zwOrginfo3 = zwIntellReportServiceImpl.findTdZwTjorginfo(2, unitid);
					zwOrginfo4 = zwIntellReportServiceImpl.findTdZwTjorginfo(4, unitid);
				}
				if (zwOrginfo1 != null || zwOrginfo2 != null || zwOrginfo3 != null || zwOrginfo4 != null) {
					if (zwOrginfo1 != null) {
						setZwOrgInfo(zwOrginfo1);
					} else if (zwOrginfo2 != null) {
						setZwOrgInfo(zwOrginfo2);
					} else if(null != zwOrginfo3){
						setZwOrgInfo(zwOrginfo3);
					}else {
						setZwOrgInfo(zwOrginfo4);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}else{
			if(null == zwOrginfoEntity.getCancelState()){
				zwOrginfoEntity.setCancelState(0);
			}
//			if(StringUtils.isBlank(zwOrginfoEntity.getCertNo())){
//				zwOrginfoEntity.setCertNo("苏卫职技字[]第号");
//			}
		}
	}
	/**
	 * <p>方法描述：资质申报基本信息初始化</p>
	 * @MethodAuthor qrr,2018年8月27日,setZwOrgInfo
	 * */
	private void setZwOrgInfo(IZwOrginfoZzsb zwOrginfo) {
		zwOrginfoEntity.setFkByLevelId(zwOrginfo.getFkByLevelId());

		zwOrginfoEntity.setOrgName(zwOrginfo.getOrgName());
		zwOrginfoEntity.setOrgAddr(zwOrginfo.getOrgAddr());
		zwOrginfoEntity.setOrgFz(zwOrginfo.getOrgFz());
		zwOrginfoEntity.setOrgFzzw(zwOrginfo.getOrgFzzw());
		zwOrginfoEntity.setLinkMan(zwOrginfo.getLinkMan());
		zwOrginfoEntity.setLinkMb(zwOrginfo.getLinkMb());
		zwOrginfoEntity.setLinkTel(zwOrginfo.getLinkTel());
		zwOrginfoEntity.setFax(zwOrginfo.getFax());
		zwOrginfoEntity.setZipcode(zwOrginfo.getZipcode());
		zwOrginfoEntity.setEmail(zwOrginfo.getEmail());
	}
	/**
	 * <p>方法描述：验证基本信息</p>
	 * @MethodAuthor qrr,2018年8月27日,veryBaseInfo
	 * */
	public boolean veryBaseInfo(Integer zwOrgType) {
		boolean flag = false;
		if (StringUtils.isNotBlank(zwOrginfoEntity.getLinkMb())
				&& !StringUtils.vertyMobilePhone(zwOrginfoEntity.getLinkMb())) {
			JsfUtil.addErrorMessage("联系人手机格式不正确！");
			flag = true;
		}
		if (StringUtils.isNotBlank(zwOrginfoEntity.getLinkTel())
				&& !StringUtils.vertyFixedPhone(zwOrginfoEntity.getLinkTel())) {
			JsfUtil.addErrorMessage("电话格式不正确！");
			flag = true;
		}
		//邮编
		if (StringUtils.isNotBlank(zwOrginfoEntity.getZipcode())
				&& !StringUtils.vertyPost(zwOrginfoEntity.getZipcode())) {
			JsfUtil.addErrorMessage("邮编格式不正确！");
			flag = true;
		}
		//邮箱
		if (!StringUtils.checkEmail(zwOrginfoEntity.getEmail())) {
			JsfUtil.addErrorMessage("邮箱格式不正确！");
			flag = true;
		}
		if(null != zwOrginfoEntity.getValidDate() && null != zwOrginfoEntity.getFirstGetday()
				&& zwOrginfoEntity.getFirstGetday().after(zwOrginfoEntity.getValidDate())){
			JsfUtil.addErrorMessage(zwOrgType == 0||zwOrgType == 2?"失效日期不能小于备案日期！":"失效日期不能小于发证日期！");
			flag = true;
		}
		if(zwOrgType == 1) {
			boolean itemFlag = false;
			boolean subFlag = true;
			for(ZwItemPO zwItemPO : this.fsServiceObjList) {
				if(zwItemPO.isIfSelected()) {
					itemFlag = true;
					if(!CollectionUtils.isEmpty(zwItemPO.getZwItemSubPOList())) {
						if (zwItemPO.getItemSubs() == null || zwItemPO.getItemSubs().length == 0) {
							JsfUtil.addErrorMessage("服务项目大类：'" + zwItemPO.getCodeName() + "'下服务项目小类不允许为空！");
							subFlag = false;
						}
					}
				}
			}
			if(!subFlag) {
				flag = true;
			}
			if(!itemFlag) {
				JsfUtil.addErrorMessage("服务项目不允许为空！");
				flag = true;
			}
		}
		//备案日期验证
		if("1".equals(this.ifValidityPeriod) && 0 == zwOrgType){
			if(zwOrginfoEntity.getFirstGetday()==null){
				flag = true;
				JsfUtil.addErrorMessage("备案有效期限开始日期不能为空！");
			}
			if(zwOrginfoEntity.getFilingDate()==null){
				flag = true;
				JsfUtil.addErrorMessage("备案有效期限结束日期不能为空！");
			}
		}
		if("1".equals(this.ifShowApproveUnit) && StringUtils.isNotBlank(zwOrginfoEntity.getRcdUnitName()) &&
				zwOrginfoEntity.getRcdUnitName().length() > 100){
			flag = true;
			JsfUtil.addErrorMessage(2 == zwOrgType ? "备案单位名称超出限制的100个字！" : "资质批准单位超出限制的100个字！");
		}
		return flag;
	}
	/**
	 * <p>方法描述：保存</p>
	 * @MethodAuthor qrr,2018年8月27日,toSaveBaseInfo
	 * */
	public void toSaveBaseInfo(List<TsSimpleCode> serList, Integer zwOrgType, List<TdZwJsffjgCommAnnexZzsb> annexList) {
		if(4 != zwOrgType){
			packetItemStr(serList,zwOrgType);
		}
		if (1!=zwOrginfoEntity.getCancelState()) {
			zwOrginfoEntity.setCancelDate(null);
		}
		// 主表设置
		if (zwOrginfoEntity.getRid() == null) {
			zwOrginfoEntity.setTsUnit(sessionData.getUser().getTsUnit());
			zwOrginfoEntity.setCreateDate(new Date());
			zwOrginfoEntity.setCreateManid(sessionData.getUser().getRid());
		}
		// 保存或者提交时，更新MODIFY_DATE和MODIFY_MANID
		zwOrginfoEntity.setModifyDate(new Date());
		zwOrginfoEntity.setModifyManid(sessionData.getUser().getRid());
		// 提交时，同时更新MASTER_DATA_TIME为当前系统日期
		if (zwOrginfoEntity.getState() != null && (zwOrginfoEntity.getState() == 1 || zwOrginfoEntity.getState() == 2)) {
			zwOrginfoEntity.setMasterDataTime(new Date());
		}
		if (zwOrgType==0||zwOrgType==3) {
			saveOrgInfo(annexList);
		}else if (zwOrgType == 4) {
			TdZwSupportOrgZzsb supportOrg = (TdZwSupportOrgZzsb) zwOrginfoEntity;
			saveSupportBeforeAction(supportOrg);
			zwOrginfoEntity = zwIntellReportServiceImpl.saveOrUpdateSupportEntity(supportOrg, annexList);
		}else {
			zwOrginfoEntity = zwIntellReportServiceImpl.saveOrUpdateZwOrginfoEntity(zwOrginfoEntity, annexList);
		}
	}
	/**
	 * 保存支撑机构前操作
	 */
	public void saveSupportBeforeAction(TdZwSupportOrgZzsb supportOrg) {

	}
	/**
	 * <p>方法描述： 监察机构专用   </p>
	 * @MethodAuthor  yzz，2021-06-30，toSaveBaseInfo2
	 **/
	public void toSaveBaseInfo2(List<TsSimpleCode> serList, Integer zwOrgType, List<ZwJkItemRowPO> subList,
								List<TdZwJsffjgCommAnnexZzsb> annexList, Map<Integer,TsSimpleCode> jkJcMap,
								String zoneApproveRid) {
		//批准的执业区域
		zoneApproveRid = StringUtils.objectToString(zoneApproveRid);
		zwOrginfoEntity.setZoneApproves(zoneApproveRid);

		packetJcItemStr(serList,zwOrgType,subList,jkJcMap);
		if (1!=zwOrginfoEntity.getCancelState()) {
			zwOrginfoEntity.setCancelDate(null);
		}
		// 主表设置
		if (zwOrginfoEntity.getRid() == null) {
			zwOrginfoEntity.setTsUnit(sessionData.getUser().getTsUnit());
			zwOrginfoEntity.setCreateDate(new Date());
			zwOrginfoEntity.setCreateManid(sessionData.getUser().getRid());
		}
		// 保存或者提交时，更新MODIFY_DATE和MODIFY_MANID
		zwOrginfoEntity.setModifyDate(new Date());
		zwOrginfoEntity.setModifyManid(sessionData.getUser().getRid());
		// 提交时，同时更新MASTER_DATA_TIME为当前系统日期
		if (zwOrginfoEntity.getState() != null && (zwOrginfoEntity.getState() == 1 || zwOrginfoEntity.getState() == 2)) {
			zwOrginfoEntity.setMasterDataTime(new Date());
		}
		saveOrgInfo(annexList);
		//zwOrginfoEntity = zwIntellReportServiceImpl.saveOrUpdateZwOrginfoEntity(zwOrginfoEntity,annexList);
	}

	/**
	 * <p>方法描述：    </p>
	 * @MethodAuthor  yzz，2021-04-21，
	 **/
	public void toSaveBaseInfo1(List<TsSimpleCode> serList,Integer zwOrgType, List<TsSimpleCode> subList,List<TdZwJsffjgCommAnnexZzsb> annexList) {
		if(zwOrgType == 1) {
			packetFsItemStr(subList);
		}else {
			packetItemStr(serList,zwOrgType);
		}
		if (1!=zwOrginfoEntity.getCancelState()) {
			zwOrginfoEntity.setCancelDate(null);
		}
		// 主表设置
		if (zwOrginfoEntity.getRid() == null) {
			zwOrginfoEntity.setTsUnit(sessionData.getUser().getTsUnit());
			zwOrginfoEntity.setCreateDate(new Date());
			zwOrginfoEntity.setCreateManid(sessionData.getUser().getRid());
		}
		// 保存或者提交时，更新MODIFY_DATE和MODIFY_MANID
		zwOrginfoEntity.setModifyDate(new Date());
		zwOrginfoEntity.setModifyManid(sessionData.getUser().getRid());
		// 提交时，同时更新MASTER_DATA_TIME为当前系统日期
		if (zwOrginfoEntity.getState() != null && (zwOrginfoEntity.getState() == 1 || zwOrginfoEntity.getState() == 2)) {
			zwOrginfoEntity.setMasterDataTime(new Date());
		}

		zwOrginfoEntity = zwIntellReportServiceImpl.saveOrUpdateZwOrginfoEntity(zwOrginfoEntity,annexList);

	}


	public void packetJcItemStr(List<TsSimpleCode> serList,Integer zwOrgType,List<ZwJkItemRowPO> zwJkItemPOS,Map<Integer,TsSimpleCode> jkJcMap) {
		//服务项目
		List<IZwSvritmZzsb> zwSvritmList = new ArrayList<IZwSvritmZzsb>();
		if (zwOrginfoEntity.getZwSvritm() != null && zwOrginfoEntity.getZwSvritm().size() > 0) {
			zwSvritmList = zwOrginfoEntity.getZwSvritm();
			zwSvritmList.clear();
		}
		if (null!=serviceObj && serviceObj.length>0) {
			this.itemStr=new String[serviceObj.length];
			int i=0;
			for(String s : serviceObj){
				for(TsSimpleCode ts:serList){
					if(Short.valueOf(s).toString().equals(ts.getCodeNo())){
						if("1".equals(ts.getIfReveal().toString())){
							TdZwTjorggitemsZzsb zwSvritm = null;
							zwSvritm = new TdZwTjorggitemsZzsb();

							zwSvritm.setItemCode(Short.valueOf(s));
							zwSvritm.setZwOrginfo(zwOrginfoEntity);
							zwSvritm.setCreateDate(new Date());
							zwSvritm.setCreateManid(sessionData.getUser().getRid());
							List<IZwSvritmSubZzsb> subsList = new ArrayList<>();
							if (zwSvritm.getZwSvritmSubList() != null && zwSvritm.getZwSvritmSubList().size() > 0) {
								subsList = zwSvritm.getZwSvritmSubList();
								subsList.clear();
							}
							if(!CollectionUtils.isEmpty(zwJkItemPOS)){
								for (ZwJkItemRowPO t : zwJkItemPOS) {
									if(t.getJcItem().getCodeNo().equals(s)) {
										String[] selectedSerItmDetail = t.getSelectedSerItmDetail();
										String[] selectedIfExterInspect = t.getSelectedIfExterInspect();
										if (null != selectedSerItmDetail && selectedSerItmDetail.length > 0) {
											TdZwTjorgItmDetail tdZwTjorgItmDetail = new TdZwTjorgItmDetail();
											tdZwTjorgItmDetail.setFkByMainId(zwSvritm);
											tdZwTjorgItmDetail.setFkBySubItemId(jkJcMap.get(Integer.valueOf(selectedSerItmDetail[0])));
											if(zwOrginfoEntity.getOutWorkPower()!=null && zwOrginfoEntity.getOutWorkPower()==1){
												if(selectedIfExterInspect!=null && selectedIfExterInspect.length>0 && selectedIfExterInspect[0].equals(selectedSerItmDetail[0])){
													tdZwTjorgItmDetail.setIfExterInspect(1);
												}else {
													tdZwTjorgItmDetail.setIfExterInspect(0);
												}
											}
											tdZwTjorgItmDetail.setCreateDate(new Date());
											tdZwTjorgItmDetail.setCreateManid(sessionData.getUser().getRid());

											subsList.add(tdZwTjorgItmDetail);
										}
									}
							    }
							}
							zwSvritm.setZwSvritmSubList(subsList);
							zwSvritmList.add(zwSvritm);
							itemStr[i]=ts.getCodeName();
							i++;
						}
					}
				}
			}
		}
		zwOrginfoEntity.setZwSvritm(zwSvritmList);


	}
	/**
	 * <p>方法描述：    </p>
	 * @MethodAuthor  yzz，2021-04-21，
	 **/
	public void packetFsItemStr(List<TsSimpleCode> fsList) {
		//服务项目
		List<IZwSvritmZzsb> zwSvritmList = new ArrayList<IZwSvritmZzsb>();
		if (zwOrginfoEntity.getZwSvritm() != null && zwOrginfoEntity.getZwSvritm().size() > 0) {
			zwSvritmList = zwOrginfoEntity.getZwSvritm();
			zwSvritmList.clear();
		}

		IZwSvritmZzsb zwSvritm = null;
		for(ZwItemPO zwItemPO : this.fsServiceObjList) {
			if(zwItemPO.isIfSelected()) {
				zwSvritm = new TdZwSrvorgitemsZzsb();
				zwSvritm.setItemCode(Short.valueOf(zwItemPO.getCodeNo()));
				zwSvritm.setZwOrginfo(zwOrginfoEntity);
				zwSvritm.setCreateDate(new Date());
				zwSvritm.setCreateManid(sessionData.getUser().getRid());
				List<IZwSvritmSubZzsb> subsList = new ArrayList<>();
				if (zwSvritm.getZwSvritmSubList() != null && zwSvritm.getZwSvritmSubList().size() > 0) {
					subsList = zwSvritm.getZwSvritmSubList();
					subsList.clear();
				}
				zwSvritmList.add(zwSvritm);
				if(null != zwItemPO.getItemSubs() && zwItemPO.getItemSubs().length != 0) {
					for(String s : zwItemPO.getItemSubs()) {
						for(TsSimpleCode ts : fsList){
							if(Short.valueOf(s).toString().equals(ts.getCodeNo())){
								if("1".equals(ts.getIfReveal().toString())){
									TdZwSrvorgitemsSubsZzsb tdZwSrvorgitemsSubs = new TdZwSrvorgitemsSubsZzsb();
									tdZwSrvorgitemsSubs.setFkBySubItemId(ts);
									tdZwSrvorgitemsSubs.setZwSvritm(zwSvritm);
									tdZwSrvorgitemsSubs.setCreateDate(new Date());
									tdZwSrvorgitemsSubs.setCreateManid(sessionData.getUser().getRid());
									subsList.add(tdZwSrvorgitemsSubs);
								}
							}
						}
					}
				}
				zwSvritm.setZwSvritmSubList(subsList);
			}
		}
		zwOrginfoEntity.setZwSvritm(zwSvritmList);
	}


	/**
	 * <p>方法描述：职业健康检查机构保存在managedBean操作</p>
	 * @MethodAuthor qrr,2020年10月15日,saveZwTjOrgInfo
	 * */
	protected void saveOrgInfo(List<TdZwJsffjgCommAnnexZzsb> annexList) {

	}
	/**
	 * <p>方法描述：封装服务项目</p>
	 * @MethodAuthor qrr,2018年8月27日,packetItemStr
	 * */
	public void packetItemStr(List<TsSimpleCode> serList,Integer zwOrgType) {
		//服务项目
		List<IZwSvritmZzsb> zwSvritmList = new ArrayList<IZwSvritmZzsb>();
		if (zwOrginfoEntity.getZwSvritm() != null && zwOrginfoEntity.getZwSvritm().size() > 0) {
			zwSvritmList = zwOrginfoEntity.getZwSvritm();
			zwSvritmList.clear();
		}
		if (null!=serviceObj && serviceObj.length>0) {
			this.itemStr=new String[serviceObj.length];
			int i=0;
			for(String s : serviceObj){
				for(TsSimpleCode ts:serList){
					if(Short.valueOf(s).toString().equals(ts.getCodeNo())){
						if("1".equals(ts.getIfReveal().toString())){
							IZwSvritmZzsb zwSvritm = null;
							if(zwOrgType.equals(0)){
								zwSvritm = new TdZwTjorggitemsZzsb();
							}else if (zwOrgType.equals(1)) {
								zwSvritm = new TdZwSrvorgitemsZzsb();
							}else if (zwOrgType.equals(2)){
								zwSvritm = new TdZwDiagitemsZzsb();
							}else {
								zwSvritm = new TdZwOcchethItemsZzsb();
							}
							zwSvritm.setItemCode(Short.valueOf(s));
							zwSvritm.setZwOrginfo(zwOrginfoEntity);
							zwSvritm.setCreateDate(new Date());
							zwSvritm.setCreateManid(sessionData.getUser().getRid());
							zwSvritmList.add(zwSvritm);
							itemStr[i]=ts.getCodeName();
							i++;
						}
					}
				}
			}
		}
		zwOrginfoEntity.setZwSvritm(zwSvritmList);
	}

	/**
	 * <p>方法描述：    </p>
	 * @MethodAuthor  yzz，2021-04-21，
	 **/
	public void initFsService() {
		if(zwOrginfoEntity.getZwSvritm() != null && zwOrginfoEntity.getZwSvritm().size() > 0){
			List<IZwSvritmZzsb> list = zwOrginfoEntity.getZwSvritm();
			String itemStr1 = null;
			itemStr = new String[list.size()];
			for(int i = 0 ; i < list.size();i++){
				IZwSvritmZzsb t = list.get(i);
					for(ZwItemPO itemPO : this.fsServiceObjList) {
					if(itemPO.getCodeNo().equals(t.getItemCode().toString())) {
						itemStr1 = itemPO.getCodeName();
						// 设置服务项目大类选中状态
						itemPO.setIfSelected(true);
						// 设置互斥服务项目大类不能选
						if("1".equals(itemPO.getExtendS1())) {
							for (ZwItemPO zwItemPO1 : this.fsServiceObjList) {
								if ("1".equals(zwItemPO1.getExtendS1()) && !itemPO.getCodeNo().equals(zwItemPO1.getCodeNo())) {
									zwItemPO1.setDisabled(true);
									// 相应服务项目大类下小类不可勾选
									if (!CollectionUtils.isEmpty(zwItemPO1.getZwItemSubPOList())) {
										for (ZwItemSubPO subPO : zwItemPO1.getZwItemSubPOList()) {
											subPO.setDisabled(true);
										}
									}
								}
							}
						}
						// 设置服务项目小类可选
						for(ZwItemSubPO subPO : itemPO.getZwItemSubPOList()) {
							subPO.setDisabled(false);
						}
						List<IZwSvritmSubZzsb> zwSvritmSubList = t.getZwSvritmSubList();
						List<ZwItemSubPO> subPOList = itemPO.getZwItemSubPOList();
						if(!CollectionUtils.isEmpty(zwSvritmSubList) && !CollectionUtils.isEmpty(subPOList)) {
							StringBuffer subItemNames = new StringBuffer();
							String[] itemSubs = new String[zwSvritmSubList.size()];
							for(int j = 0 ; j<zwSvritmSubList.size(); j++){
								IZwSvritmSubZzsb sub = zwSvritmSubList.get(j);
								if (null == sub.getFkBySubItemId()) {
									continue;
								}
								subItemNames.append("，").append(sub.getFkBySubItemId().getCodeName());
								for(ZwItemSubPO subPO : subPOList) {
									if(subPO.getCodeNo().equals(sub.getFkBySubItemId().getCodeNo())) {
										itemSubs[j] = sub.getFkBySubItemId().getCodeNo();
									}
								}
							}
							if (subItemNames.length()>0) {
								itemStr1 = itemStr1 +"（"+subItemNames.substring(1)+"）";
							}
							itemPO.setItemSubs(itemSubs);
						}
					}
				}
				itemStr[i] = itemStr1;
			}
		}
	}

	/**
	 * <p>方法描述：撤销</p>
	 * @MethodAuthor qrr,2018年8月27日,toCancel
	 * */
	public void toCancel() {
		zwOrginfoEntity.setState((short) 0);
		/*if(null != zwOrginfoEntity.getFkByLevelId() && null == zwOrginfoEntity.getFkByLevelId().getRid()){
			zwOrginfoEntity.setFkByLevelId(null);
		}
		zwOrginfoEntity = zwIntellReportServiceImpl.saveOrUpdateZwOrginfoEntity(zwOrginfoEntity);*/
		//updateToCancelState
		zwIntellReportServiceImpl.updateToCancelState(zwOrginfoEntity);
		zwOrginfoEntity = zwIntellReportServiceImpl.findZwOrgInfoByEntityRid(zwOrginfoEntity);
	}

	/**
	 * @Description : 地区树初始化
	 * @MethodAuthor: anjing
	 * @Date : 2020/2/26 11:27
	 **/
	public void initZoneTree() {
		if(null == this.zoneTree || this.zoneTree.getChildCount() <=0) {
			this.zoneTree = new DefaultTreeNode("root", null);
			this.zoneTree.setExpanded(true);
			List<TsZone> list = this.commService.findZoneList(true, null, "1", "3");
			if(null != list && list.size() > 0) {
				Map<String, TreeNode> map = new HashMap<String, TreeNode>();
				TsZone top = list.get(0);

				Short topLvl = top.getZoneType();
				TreeNode temNode = new DefaultTreeNode(top, this.zoneTree);
				temNode.setExpanded(true);
				map.put(top.getZoneCode(), temNode);

				if(list.size() > 1) {
					for(int i=1; i<list.size(); i++) {
						TsZone t = list.get(i);
						if(topLvl.equals(t.getZoneType())) {
							TreeNode node = new DefaultTreeNode(t, this.zoneTree);
							node.setExpanded(true);
							map.put(t.getZoneCode(), node);
						}else {
							String parentCode = ZoneUtil.getParentCode(t.getZoneCode());
							if(StringUtils.isNotBlank(parentCode)) {
								TreeNode parentNode = map.get(parentCode);
								if(null != parentNode) {
									TreeNode node = new DefaultTreeNode(t, parentNode);
									node.setExpanded(true);
									map.put(t.getZoneCode(), node);
								}
							}
						}
					}
				}
				map.clear();
			}
		}
	}

	public void hideZoneAction() {
		// 遍历选择的危害因素，获取选择的id与名称
		this.zoneNames = null;
		this.zoneIds = null;
		this.outZoneList = new ArrayList<>();

		List<TreeNode> children = this.zoneTree.getChildren();
		if(null != children && children.size()>0) {
			for(TreeNode node : children) {
				boolean flag = true;
				List<TreeNode> children2 = node.getChildren();
				if(null != children2 && children2.size()>0) {
					for(TreeNode node2 : children2) {
						if(!node2.isSelected()) {
							flag = false;
							break;
						}
					}
				}
				if(flag) {
					TsZone tsZone = (TsZone) node.getData();
					this.outZoneList.add(tsZone);
				} else {
					if(null != children2 && children2.size()>0) {
						for(TreeNode node2 : children2) {
							if(node2.isSelected()) {
								TsZone tsZone = (TsZone) node2.getData();
								this.outZoneList.add(tsZone);
							}
						}
					}
				}
			}
		}

		if(!CollectionUtils.isEmpty(this.outZoneList)) {
			StringBuilder nameSb = new StringBuilder();
			StringBuilder idSb = new StringBuilder();
			for(TsZone tsZone : this.outZoneList) {
				nameSb.append("，").append(tsZone.getZoneName());
				idSb.append(",").append(tsZone.getRid().toString());
			}
			this.zoneNames = nameSb.toString().substring(1);
			this.zoneIds = idSb.toString().substring(1);
		}
	}

	/**
	 * @Description : 清空地区树
	 * @MethodAuthor: anjing
	 * @Date : 2020/2/26 13:15
	 **/
	public void clearSelectZones() {
		this.zoneNames = null;
		this.zoneIds = null;
		this.selectZones = null;
		this.outZoneList = new ArrayList<>();
		List<TreeNode> children = this.zoneTree.getChildren();
		if(null != children && children.size()>0) {
			for(TreeNode node : children) {
				node.setSelected(false);
				List<TreeNode> children2 = node.getChildren();
				if(null != children2 && children2.size()>0) {
					for(TreeNode node2 : children2) {
						node2.setSelected(false);
					}
				}
			}
		}
	}

	/**
	 * @Description : +外出开展职业健康检查工作能力范围20200211
	 * @MethodAuthor: anjing
	 * @Date : 2020/2/26 16:19
	 **/
	public void packetOutZoneList(Integer zwOrgType) {
		List<IZwOrgOutRangeZzsb> zwOrgOutRangeList = new ArrayList<IZwOrgOutRangeZzsb>();
		if(zwOrginfoEntity.getZwOrgOutRanges() != null && zwOrginfoEntity.getZwOrgOutRanges().size() > 0) {
			zwOrgOutRangeList = zwOrginfoEntity.getZwOrgOutRanges();
			zwOrgOutRangeList.clear();
		}
		if(null != zwOrginfoEntity.getOutWorkPower() && 1 == zwOrginfoEntity.getOutWorkPower()) {
			for(TsZone tsZone : outZoneList){
				IZwOrgOutRangeZzsb iZwOrgOutRange = null;
				if(zwOrgType.equals(0)){
					iZwOrgOutRange = new TdZwTjorgOutRangeZzsb();
				}
				iZwOrgOutRange.setZwOrginfo(zwOrginfoEntity);
				iZwOrgOutRange.setFkByZoneId(tsZone);
				iZwOrgOutRange.setCreateDate(new Date());
				iZwOrgOutRange.setCreateManid(sessionData.getUser().getRid());
				zwOrgOutRangeList.add(iZwOrgOutRange);
			}

		} else {
			this.clearSelectZones();
		}
		zwOrginfoEntity.setZwOrgOutRanges(zwOrgOutRangeList);
	}

	/**
	 * <p>描述 备案有效期日期监听</p>
	 *
	 * @ClassAuthor gongzhe,2022/4/16 20:35,ZwQualBaseInfoZzsbBean
	 */
	public void filingDateListener(){
		Date today = new Date();
		if(this.zwOrginfoEntity.getFilingDate() == null || this.zwOrginfoEntity.getFilingDate().after(today)){
			//有效结束日期为空，或结束日期晚于今天，则最大日期为今天
			this.firstGetdayMaxDate = today;
		}else{
			this.firstGetdayMaxDate = this.zwOrginfoEntity.getFilingDate();
		}
	}

	/**
	 * <p>方法描述： 支撑机构添加学术培训信息 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	public void addSupportTrain(){}
	/**
	 * <p>方法描述： 支撑机构修改学术培训信息 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	public void modSupportTrain(){}
	/**
	 * <p>方法描述： 支撑机构保存或修改学术培训信息 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	public void saveOrUpdateSupportTrain(){}
	/**
	 * <p>方法描述： 支撑机构删除学术培训信息 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	public void delSupportTrain(){}
	/**
	 * <p>方法描述： 支撑机构删除学术培训信息附件 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	public void removeSupportTrainFilePath(){}

	public abstract Class<IZwOrginfoZzsb> getZwIntellReportClass();
	public IZwOrginfoZzsb getZwOrginfoEntity() {
		return zwOrginfoEntity;
	}
	public void setZwOrginfoEntity(IZwOrginfoZzsb zwOrginfoEntity) {
		this.zwOrginfoEntity = zwOrginfoEntity;
	}

	public List<String> getCareerList() {
		return careerList;
	}
	public void setCareerList(List<String> careerList) {
		this.careerList = careerList;
	}
	public String[] getServiceObj() {
		return serviceObj;
	}
	public void setServiceObj(String[] serviceObj) {
		this.serviceObj = serviceObj;
	}
	public List<SelectItem> getServiceObjList() {
		return serviceObjList;
	}
	public void setServiceObjList(List<SelectItem> serviceObjList) {
		this.serviceObjList = serviceObjList;
	}
	public String[] getItemStr() {
		return itemStr;
	}
	public void setItemStr(String[] itemStr) {
		this.itemStr = itemStr;
	}
	public List<TsSimpleCode> getJkList() {
		return jkList;
	}
	public void setJkList(List<TsSimpleCode> jkList) {
		this.jkList = jkList;
	}

	public List<TsSimpleCode> getLevelList() {
		return levelList;
	}

	public void setLevelList(List<TsSimpleCode> levelList) {
		this.levelList = levelList;
	}

	public TreeNode getZoneTree() {
		return zoneTree;
	}

	public void setZoneTree(TreeNode zoneTree) {
		this.zoneTree = zoneTree;
	}

	public TreeNode[] getSelectZones() {
		return selectZones;
	}

	public void setSelectZones(TreeNode[] selectZones) {
		this.selectZones = selectZones;
	}

	public String getZoneNames() {
		return zoneNames;
	}

	public void setZoneNames(String zoneNames) {
		this.zoneNames = zoneNames;
	}

	public String getZoneIds() {
		return zoneIds;
	}

	public void setZoneIds(String zoneIds) {
		this.zoneIds = zoneIds;
	}

	public List<TsZone> getOutZoneList() {
		return outZoneList;
	}

	public void setOutZoneList(List<TsZone> outZoneList) {
		this.outZoneList = outZoneList;
	}

	public List<ZwItemPO> getFsServiceObjList() {
		return fsServiceObjList;
	}

	public void setFsServiceObjList(List<ZwItemPO> fsServiceObjList) {
		this.fsServiceObjList = fsServiceObjList;
	}

	public String getIfValidityPeriod() {
		return ifValidityPeriod;
	}

	public void setIfValidityPeriod(String ifValidityPeriod) {
		this.ifValidityPeriod = ifValidityPeriod;
	}

	public Boolean getIfReviewProcess() {
		return ifReviewProcess;
	}

	public void setIfReviewProcess(Boolean ifReviewProcess) {
		this.ifReviewProcess = ifReviewProcess;
	}

	public String getIfServiceDetails() {
		return ifServiceDetails;
	}

	public void setIfServiceDetails(String ifServiceDetails) {
		this.ifServiceDetails = ifServiceDetails;
	}

	public Date getFirstGetdayMaxDate() {
		return firstGetdayMaxDate;
	}

	public void setFirstGetdayMaxDate(Date firstGetdayMaxDate) {
		this.firstGetdayMaxDate = firstGetdayMaxDate;
	}
	public List<ZwJkItemPO> getJkItemList() {
		return jkItemList;
	}


	public void setJkItemList(List<ZwJkItemPO> jkItemList) {
		this.jkItemList = jkItemList;
	}

	public List<ZwJkItemRowPO> getJkItemRowList() {
		return jkItemRowList;
	}

	public void setJkItemRowList(List<ZwJkItemRowPO> jkItemRowList) {
		this.jkItemRowList = jkItemRowList;
	}

	public String getCertNoTip() {
		return certNoTip;
	}

	public void setCertNoTip(String certNoTip) {
		this.certNoTip = certNoTip;
	}

	public String getIfShowApproveUnit() {
		return ifShowApproveUnit;
	}

	public void setIfShowApproveUnit(String ifShowApproveUnit) {
		this.ifShowApproveUnit = ifShowApproveUnit;
	}

	public String getIfHaveHouseAnnex() {
		return ifHaveHouseAnnex;
	}

	public void setIfHaveHouseAnnex(String ifHaveHouseAnnex) {
		this.ifHaveHouseAnnex = ifHaveHouseAnnex;
	}

	public String getIfCheckOrg() {
		return ifCheckOrg;
	}

	public void setIfCheckOrg(String ifCheckOrg) {
		this.ifCheckOrg = ifCheckOrg;
	}

	public String getIfHasReSearchAndAcademic() {
		return ifHasReSearchAndAcademic;
	}

	public void setIfHasReSearchAndAcademic(String ifHasReSearchAndAcademic) {
		this.ifHasReSearchAndAcademic = ifHasReSearchAndAcademic;
	}

	public List<List<TdZwSupportTrain>> getShowSupportTrainList() {
		return showSupportTrainList;
	}

	public void setShowSupportTrainList(List<List<TdZwSupportTrain>> showSupportTrainList) {
		this.showSupportTrainList = showSupportTrainList;
	}

	public List<List<TsSimpleCode>> getShowAcademicList() {
		return showAcademicList;
	}

	public void setShowAcademicList(List<List<TsSimpleCode>> showAcademicList) {
		this.showAcademicList = showAcademicList;
	}

	public List<SupportTrainPO> getSupportTrainDiagDataList() {
		return supportTrainDiagDataList;
	}

	public void setSupportTrainDiagDataList(List<SupportTrainPO> supportTrainDiagDataList) {
		this.supportTrainDiagDataList = supportTrainDiagDataList;
	}

	public Integer getTrainYear() {
		return trainYear;
	}

	public void setTrainYear(Integer trainYear) {
		this.trainYear = trainYear;
	}

	public TdZwSupportTrain getEditSupportTrain() {
		return editSupportTrain;
	}

	public void setEditSupportTrain(TdZwSupportTrain editSupportTrain) {
		this.editSupportTrain = editSupportTrain;
	}

	public Integer getAcademicDataSize() {
		return academicDataSize;
	}

	public void setAcademicDataSize(Integer academicDataSize) {
		this.academicDataSize = academicDataSize;
	}

	public List<TsSimpleCode> getSupportInstSimpleCodeList() {
		return supportInstSimpleCodeList;
	}

	public void setSupportInstSimpleCodeList(List<TsSimpleCode> supportInstSimpleCodeList) {
		this.supportInstSimpleCodeList = supportInstSimpleCodeList;
	}

	public List<SupportInstAbilityVO> getSupportInstVOList() {
		return supportInstVOList;
	}

	public void setSupportInstVOList(List<SupportInstAbilityVO> supportInstVOList) {
		this.supportInstVOList = supportInstVOList;
	}

	public List<SupportInstAbilityVO> getAllSupportInstVOList() {
		return allSupportInstVOList;
	}

	public void setAllSupportInstVOList(List<SupportInstAbilityVO> allSupportInstVOList) {
		this.allSupportInstVOList = allSupportInstVOList;
	}

	public String getUnitType() {
		return unitType;
	}

	public void setUnitType(String unitType) {
		this.unitType = unitType;
	}

	public Integer[] getSelectedQualTypes() {
		return selectedQualTypes;
	}

	public void setSelectedQualTypes(Integer[] selectedQualTypes) {
		this.selectedQualTypes = selectedQualTypes;
	}

	public String getRedStr() {
		return redStr;
	}

	public void setRedStr(String redStr) {
		this.redStr = redStr;
	}

	public TdZwSupportHealth getSupportHealth() {
		return supportHealth;
	}

	public void setSupportHealth(TdZwSupportHealth supportHealth) {
		this.supportHealth = supportHealth;
	}

	public List<TsSimpleCode> getAbilitySimpleCodeList() {
		return abilitySimpleCodeList;
	}

	public void setAbilitySimpleCodeList(List<TsSimpleCode> abilitySimpleCodeList) {
		this.abilitySimpleCodeList = abilitySimpleCodeList;
	}

	public List<SupportInstAbilityVO> getAbilityShowList() {
		return abilityShowList;
	}

	public void setAbilityShowList(List<SupportInstAbilityVO> abilityShowList) {
		this.abilityShowList = abilityShowList;
	}

	public List<SupportInstAbilityVO> getAbilitySaveList() {
		return abilitySaveList;
	}

	public void setAbilitySaveList(List<SupportInstAbilityVO> abilitySaveList) {
		this.abilitySaveList = abilitySaveList;
	}

	/**
	 * <p>方法描述：执行撤销前的操作 </p>
	 * pw 2023/10/27
	 **/
	public void preExecuteCancel () {}

}
