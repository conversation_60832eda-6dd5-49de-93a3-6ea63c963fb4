package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-5-21
 */
@Entity
@Table(name = "TB_TJ_LAW")
@SequenceGenerator(name = "TbTjLaw", sequenceName = "TB_TJ_LAW_SEQ", allocationSize = 1)
public class TbTjLawCheckZzsb implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private String lawCode;
	private String lawName;
	private String briefExplain;
	private Date decreeDate;
	private String decreeOrgan;
	private String fileName;
	private String filePath;
	private Integer stateMark;
	private TsSimpleCode fkByLawTypeId;

	public TbTjLawCheckZzsb() {
	}

	public TbTjLawCheckZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjLaw")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "LAW_CODE")	
	public String getLawCode() {
		return lawCode;
	}

	public void setLawCode(String lawCode) {
		this.lawCode = lawCode;
	}	
			
	@Column(name = "LAW_NAME")	
	public String getLawName() {
		return lawName;
	}

	public void setLawName(String lawName) {
		this.lawName = lawName;
	}	
			
	@Column(name = "BRIEF_EXPLAIN")	
	public String getBriefExplain() {
		return briefExplain;
	}

	public void setBriefExplain(String briefExplain) {
		this.briefExplain = briefExplain;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DECREE_DATE")			
	public Date getDecreeDate() {
		return decreeDate;
	}

	public void setDecreeDate(Date decreeDate) {
		this.decreeDate = decreeDate;
	}	
			
	@Column(name = "DECREE_ORGAN")	
	public String getDecreeOrgan() {
		return decreeOrgan;
	}

	public void setDecreeOrgan(String decreeOrgan) {
		this.decreeOrgan = decreeOrgan;
	}	
			
	@Column(name = "FILE_NAME")	
	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Column(name = "STATE_MARK")	
	public Integer getStateMark() {
		return stateMark;
	}

	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}	
			
	@ManyToOne
	@JoinColumn(name = "LAW_TYPE_ID")			
	public TsSimpleCode getFkByLawTypeId() {
		return fkByLawTypeId;
	}

	public void setFkByLawTypeId(TsSimpleCode fkByLawTypeId) {
		this.fkByLawTypeId = fkByLawTypeId;
	}	
			
}