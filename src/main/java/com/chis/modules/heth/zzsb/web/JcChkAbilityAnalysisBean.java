package com.chis.modules.heth.zzsb.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesSimpleBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.DefaultStreamedContent;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.util.*;

@ManagedBean(name = "jcChkAbilityAnalysisBean")
@ViewScoped
public class JcChkAbilityAnalysisBean extends FacesSimpleBean {
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    /**
     * 查询条件：地区
     */
    private List<TsZone> zoneList;
    private String searchZoneGb;
    private String searchZoneName;

    /**
     * 查询条件：职业卫生技术服务机构-机构名称
     */
    private String searchOrgName;
    /**
     * 查询条件：职业卫生技术服务机构-系统单位ID
     */
    private String searchUnitId;
    /**
     * 导出类型(码表5320大类)
     */
    private List<TsSimpleCode> exportTypeList;
    private Integer exportType;
    /**
     * 业务范围
     */
    private List<TsSimpleCode> scopeOfBusinessList;

    public JcChkAbilityAnalysisBean() {
        init();
    }

    public void init() {
        this.ifSQL = true;
        // 地区信息初始化 管辖地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
        this.searchZoneGb = this.zoneList.get(0).getZoneGb();
        this.searchZoneName = this.zoneList.get(0).getZoneName();
        initSimpleCode();
    }

    private void initSimpleCode() {
        // 业务范围
        this.scopeOfBusinessList = this.commService.findSimpleCodeListOrderByNumNo("5320", true);
        // 业务范围大类
        this.exportTypeList = new ArrayList<>();
        for (TsSimpleCode simpleCode : this.scopeOfBusinessList) {
            String codeLevelNo = StringUtils.objectToString(simpleCode.getCodeLevelNo());
            if (!new Short("1").equals(simpleCode.getIfReveal()) || !codeLevelNo.equals(simpleCode.getCodeNo())) {
                continue;
            }
            this.exportTypeList.add(simpleCode);
        }
    }

    @Override
    public void searchAction() {
        if (StringUtils.isBlank(this.searchUnitId)) {
            JsfUtil.addErrorMessage("请选择职业卫生技术服务机构！");
            return;
        }
        super.searchAction();
    }

    @Override
    public String[] buildHqls() {
        String sql = "SELECT " +
                "       AR.RID                  AS P0, " +
                "       SC.CODE_NAME            AS P1, " +
                "       L.LAW_CODE || AR.JC_WAY AS P2, " +
                "       AR.LIMIT_DESC           AS P3, " +
                "       LISTAGG(SC1.CODE_NAME, '、') WITHIN GROUP (ORDER BY SC1.NUM, SC1.CODE_LEVEL_NO, SC1.CODE_NO) AS P4, " +
                "       ''                      AS P5, " +
                "       AR.LAB_APPROVAL_TAG     AS P6, " +
                "       AR.QUAL_APPROVAL_TAG    AS P7 ";
        String baseSql = buildQueryCriteria(this.paramMap);
        sql += baseSql + " GROUP BY AR.RID, SC.CODE_NAME, SC.NUM, SC.CODE_NO, L.LAW_CODE, AR.JC_WAY, AR.LIMIT_DESC, AR.LAB_APPROVAL_TAG, AR.QUAL_APPROVAL_TAG "
                + " ORDER BY SC.NUM, SC.CODE_NO ";
        String countSql = "SELECT COUNT(DISTINCT AR.RID) " + baseSql;
        return new String[]{sql, countSql};
    }

    private String buildQueryCriteria(Map<String, Object> paramMap) {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append(" FROM TB_YSJC_CHK_ABILITY_REL AR " +
                        "         INNER JOIN TB_YSJC_CHK_ABILITY A ON AR.MAIN_ID = A.RID " +
                        "         LEFT JOIN TS_UNIT U ON A.ORG_ID = U.RID " +
                        "         LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID " +
                        "         LEFT JOIN TS_SIMPLE_CODE SC ON AR.ITEM_ID = SC.RID " +
                        "         LEFT JOIN TB_TJ_LAW L ON AR.JC_BASE_ID = L.RID " +
                        "         LEFT JOIN TB_YSJC_CHK_METHOD M ON AR.RID = M.MAIN_ID " +
                        "         LEFT JOIN TS_SIMPLE_CODE SC1 ON M.METHOD_ID = SC1.RID ")
                .append(" WHERE AR.STATE = 1 ");
        // 地区
        if (StringUtils.isNotBlank(this.searchZoneGb)) {
            baseSql.append(" AND Z.ZONE_GB LIKE :zoneGb escape '\\\' ");
            paramMap.put("zoneGb", ZoneUtil.zoneSelect(this.searchZoneGb.trim()) + "%");
        }
        // 职业卫生技术服务机构-系统单位
        if (StringUtils.isNotBlank(this.searchUnitId)) {
            baseSql.append(" AND U.RID = :unitId ");
            paramMap.put("unitId", this.searchUnitId);
        }
        return baseSql.toString();
    }

    /**
     * 查询职业卫生技术服务机构
     */
    public void selUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 400);

        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add("3");
        paramMap.put("orgType", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.searchZoneGb);
        paramMap.put("zongGb", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/orgChoose/orgSingleSelectList", options, paramMap);
    }

    /**
     * 处理选择的机构
     */
    public void onSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (selectedMap == null || selectedMap.isEmpty() || selectedMap.get("selectOrg") == null) {
            return;
        }
        Object[] data = (Object[]) selectedMap.get("selectOrg");
        if (ObjectUtil.isEmpty(data)) {
            this.searchUnitId = "";
            this.searchOrgName = "";
            return;
        }
        this.searchUnitId = StringUtils.objectToString(data[1]);
        this.searchOrgName = StringUtils.objectToString(data[3]);
    }

    /**
     * 清空机构选择
     */
    public void clearUnit() {
        this.searchUnitId = null;
        this.searchOrgName = null;
    }

    /**
     * 导出数据前的处理函数。
     * 该函数用于在导出数据前检查查询条件职业卫生技术服务机构是否为空以及是否有符合查询条件的数据。
     * 如果没有数据，则提示用户；如果有数据，则触发导出文件的下载。
     *
     * @param extendS2 导出类型
     */
    public void exportBefore(Integer extendS2) {
        if (extendS2 == null || (extendS2 != 1 && extendS2 != 2)) {
            return;
        }
        if (StringUtils.isBlank(this.searchUnitId)) {
            JsfUtil.addErrorMessage("请选择职业卫生技术服务机构！");
            return;
        }
        RequestContext context = RequestContext.getCurrentInstance();
        Map<String, Object> paramMap = new HashMap<>();
        int count = this.commService.findCountBySql(buildExportSqls(1, extendS2, paramMap), paramMap);
        if (count == 0) {
            JsfUtil.addErrorMessage("无可导出的数据！");
            return;
        }
        this.exportType = extendS2;
        context.execute("getDownloadFileClick();");
    }

    /**
     * 生成导出sql
     *
     * @param type       类型<pre>1：count</pre><pre>2：导出当前机构业务范围小类</pre><pre>3：导出</pre>
     * @param exportType 导出类型
     * @param paramMap   sql参数
     * @return sql
     */
    public String buildExportSqls(int type, Integer exportType, Map<String, Object> paramMap) {
        String sql;
        paramMap.put("orgId", this.searchUnitId);
        paramMap.put("exportType", exportType);
        if (type == 1) {
            sql = "SELECT COUNT(1) " +
                    "FROM TB_YSJC_CHK_ABILITY_REL AR " +
                    "         INNER JOIN TB_YSJC_CHK_ABILITY A ON AR.MAIN_ID = A.RID " +
                    "         LEFT JOIN TS_UNIT U ON A.ORG_ID = U.RID " +
                    "         LEFT JOIN TB_ZW_BUSI_REL_ITEM RI ON AR.ITEM_ID = RI.ITEM_ID " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC2 ON RI.BUS_ID = SC2.RID " +
                    "WHERE AR.STATE = 1 AND A.ORG_ID = :orgId AND SC2.EXTENDS2 = :exportType ";
            return sql;
        } else if (type == 2) {
            sql = "SELECT SC.RID AS RANGE_LAST_ID, SC.CODE_NAME AS RANGE_LAST " +
                    "    FROM TD_ZW_OCCHETH_INFO O " +
                    "             INNER JOIN TD_ZW_OCCHETH_ITEMS OI ON O.RID = OI.ORG_ID " +
                    "             INNER JOIN TS_SIMPLE_CODE SC ON TO_CHAR(OI.ITEM_CODE) = SC.CODE_NO " +
                    "         INNER JOIN TS_SIMPLE_CODE SC1 " +
                    "                    ON SC.CODE_TYPE_ID = SC1.CODE_TYPE_ID AND " +
                    "                       SC.CODE_LEVEL_NO = SC1.CODE_NO || '.' || SC.CODE_NO " +
                    "             INNER JOIN TS_CODE_TYPE CT ON SC.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5320' " +
                    "    WHERE O.ORG_ID = :orgId AND SC1.EXTENDS2 = :exportType";
            return sql;
        }
        sql = "SELECT SC.CODE_NAME AS P0, L.LAW_CODE || AR.JC_WAY AS P1, AR.LIMIT_DESC AS P2, " +
                "  LISTAGG(SC1.CODE_NAME, '、') " +
                "          WITHIN GROUP (ORDER BY SC1.NUM, SC1.CODE_LEVEL_NO, SC1.CODE_NO) AS P3, " +
                "  ''                                                                      AS P4, " +
                "  AR.LAB_APPROVAL_TAG                                                     AS P5, " +
                "  AR.QUAL_APPROVAL_TAG                                                    AS P6, " +
                "  SC3.RID                                                                 AS P7, " +
                "  SC3.CODE_NAME                                                           AS P8, " +
                "  I.BUS_ID                                                                AS P9, " +
                "  I.CHECK_TYPE                                                            AS P10, " +
                "  AR.RID                                                                  AS P11 " +
                " FROM TB_YSJC_CHK_ABILITY_REL AR " +
                "     INNER JOIN TB_YSJC_CHK_ABILITY A ON AR.MAIN_ID = A.RID " +
                "     LEFT JOIN TS_UNIT U ON A.ORG_ID = U.RID " +
                "     LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID " +
                "     LEFT JOIN TS_SIMPLE_CODE SC ON AR.ITEM_ID = SC.RID " +
                "     LEFT JOIN TB_TJ_LAW L ON AR.JC_BASE_ID = L.RID " +
                "     LEFT JOIN TB_YSJC_CHK_METHOD M ON AR.RID = M.MAIN_ID " +
                "     LEFT JOIN TS_SIMPLE_CODE SC1 ON M.METHOD_ID = SC1.RID " +
                "     LEFT JOIN TB_ZW_BUSI_REL_ITEM RI ON AR.ITEM_ID = RI.ITEM_ID " +
                "     LEFT JOIN TS_SIMPLE_CODE SC2 ON RI.BUS_ID = SC2.RID " +
                "     LEFT JOIN TS_SIMPLE_CODE SC3 ON RI.PARENT_ITEM_ID = SC3.RID " +
                "     LEFT JOIN TB_ZW_BUSI_MAIN_ITEM I ON SC.RID = I.ITEM_ID " +
                "WHERE AR.RID IS NULL OR (AR.STATE = 1 AND A.ORG_ID = :orgId AND SC2.EXTENDS2 = :exportType) " +
                "GROUP BY AR.RID, SC.RID, SC.CODE_NAME, SC.NUM, SC.CODE_NO, L.LAW_CODE, AR.JC_WAY, AR.LIMIT_DESC, " +
                "         AR.LAB_APPROVAL_TAG, AR.QUAL_APPROVAL_TAG, SC.CODE_NAME, SC.NUM, SC.CODE_LEVEL_NO, " +
                "         SC3.RID, SC3.CODE_NAME, SC3.NUM, SC3.CODE_LEVEL_NO, I.BUS_ID, I.CHECK_TYPE " +
                "ORDER BY SC3.NUM, SC3.CODE_LEVEL_NO, SC.NUM, SC.CODE_LEVEL_NO, I.CHECK_TYPE ";
        return sql;
    }

    /**
     * 导出体检报告抽取数据为Excel文件。
     *
     * @return 返回DefaultStreamedContent对象，用于浏览器下载Excel文件。返回null表示导出失败。
     * 文件名为"体检报告抽取.xlsx"，编码为UTF-8，内容包含体检报告抽取相关数据。
     */
    public DefaultStreamedContent export() {
        if (this.exportType == null || (this.exportType != 1 && this.exportType != 2)) {
            return null;
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        String excelTitle = this.searchOrgName;
        if (new Integer(1).equals(this.exportType)) {
            excelTitle += "【职业病危害因素检测能力对比表】";
        } else if (new Integer(2).equals(this.exportType)) {
            excelTitle += "【放射性职业病危害因素检测能力对比表】";
        }
        String[] excelHeaders = new String[]{"序号", "检测项目", "检测标准（方法）名称、编号（含年号）及条款号", "限制说明", "方法验证、确认或论证", "是否通过CMA或CNAS"};

        Integer[] columnWidths = new Integer[]{5, 25, 90, null, null, null};
        List<ExcelExportObject[]> excelExportObjects = new ArrayList<>();
        List<List<ExcelExportObject>> excelExportObjectList2 = new ArrayList<>();
        ExcelExportUtil excelExportUtil = new ExcelExportUtil(excelTitle, excelHeaders, excelExportObjects);
        excelExportUtil.setFrozenPaneRowsNum(2);
        excelExportUtil.setColumnWidths(columnWidths);
        excelExportUtil.setAppendDataList(excelExportObjectList2);
        try {
            pakExcelExportDataList(
                    excelExportUtil, excelExportObjects, excelExportObjectList2,
                    executeExportSql(3), excelHeaders.length
            );
            Workbook wb = excelExportUtil.exportExcel();
            if (wb == null) {
                context.execute("hideStatus();");
                return null;
            }

            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = excelTitle + ".xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(
                        new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName
                );
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("文件导出错误！");
        }
        context.execute("hideStatus();");
        return null;
    }

    /**
     * 执行导出SQL查询，获取并处理数据，用于报表生成。
     *
     * @return 返回一个包含查询结果的Object数组列表。
     */
    private List<Object[]> executeExportSql(int type) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = buildExportSqls(type, this.exportType, paramMap);
        return CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap));
    }

    /**
     * 将数据列表打包为Excel导出对象列表。
     *
     * @param excelExportUtil        导出EXCEL对象。
     * @param excelExportObjectList  导出对象列表。
     * @param excelExportObjectList2 额外的导出对象列表。
     * @param dataList               数据列表，每个元素是一个对象数组，代表一行数据。
     * @param headerSize             Excel表格的头部大小，用于确定每个数据行中导出对象的数量。
     */
    private void pakExcelExportDataList(ExcelExportUtil excelExportUtil,
                                        List<ExcelExportObject[]> excelExportObjectList,
                                        List<List<ExcelExportObject>> excelExportObjectList2,
                                        List<Object[]> dataList, int headerSize) {
        // 检查数据列表和头部大小是否有效，如果无效则返回空列表
        if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
            return;
        }
        // 处理数据
        Set<String> itemIdSet = new HashSet<>();
        // key: 检测项目对应的大类ID value: 检测项目对应的大类名称
        Map<String, String> proNameMap = new HashMap<>();
        // key: 检测项目对应的大类ID value: 检测项目
        Map<String, List<Object[]>> allDataMap = new LinkedHashMap<>();
        // key: 检测项目对应的大类ID value: 检测项目对应的大类名称
        Map<String, String> rangeLastNameMap = new LinkedHashMap<>();
        // key: 检测项目对应的大类ID value: 检测项目
        Map<String, Map<String, Integer>> rangeLastTypeMap = new HashMap<>();
        Map<String, Set<String>> itemTypeIdSetMap = new HashMap<>();
        pakExcelExportMap(dataList, proNameMap, allDataMap, rangeLastNameMap, rangeLastTypeMap, itemTypeIdSetMap);

        // 封装导出数据对象
        // 第一层序号
        int xhFirst = 0;
        // 当前行序号
        int colNum = 1;
        for (Map.Entry<String, List<Object[]>> allDataEntry : allDataMap.entrySet()) {
            String proId = allDataEntry.getKey();
            List<Object[]> lastDataList = allDataEntry.getValue();
            // 第一类业务范围 先放大类
            if (!"-1".equals(proId)) {
                colNum++;
                String chiseseXh = Convert.numberToChinese(++xhFirst, false);
                String proName = proNameMap.get(proId);
                excelExportObjectList.add(pakExcelExportObjects(headerSize, chiseseXh, proName));
                excelExportUtil.addMergeCells(colNum, colNum, 1, 5);
            }
            // 放入检测项目
            int xhSecond = 1;
            for (Object[] data : lastDataList) {
                String itemId = StringUtils.objectToString(data[11]);
                if (itemIdSet.contains(itemId)) {
                    continue;
                }
                itemIdSet.add(itemId);
                colNum++;
                excelExportObjectList.add(pakExcelExportObjects(headerSize, xhSecond++, data));
            }
        }

        // 放入业务范围小类重点、一般检测项目数
        if (new Integer(1).equals(this.exportType)) {
            pakFirstTypeAppend(excelExportUtil, excelExportObjectList2, itemTypeIdSetMap, rangeLastNameMap, rangeLastTypeMap, colNum);
        } else {
            for (TsSimpleCode simpleCode : this.scopeOfBusinessList) {
                String rangeLastId = StringUtils.objectToString(simpleCode.getRid());
                if (!rangeLastNameMap.containsKey(rangeLastId)) {
                    continue;
                }
                colNum++;
                String rangeLastNameStr = rangeLastNameMap.get(rangeLastId) + "：";
                Map<String, Integer> rangeLastTypeNumMap = rangeLastTypeMap.get(rangeLastId);
                excelExportObjectList.add(pakExcelExportObjects(headerSize, rangeLastNameStr, rangeLastTypeNumMap));
                excelExportUtil.addMergeCells(colNum, colNum, 0, 2);
                excelExportUtil.addMergeCells(colNum, colNum, 3, 5);
            }
        }
    }

    /**
     * 封装第一类业务范围追加导出数据
     *
     * @param excelExportUtil        导出EXCEL对象。
     * @param excelExportObjectList2 额外的导出对象列表。
     * @param itemTypeIdSetMap       检测项目重点、一般项目ID映射 {重点、一般} -> {检测项目重点、一般项目SET}
     * @param rangeLastNameMap       检测项目大类名称映射 {检测项目大类ID} -> {检测项目大类名称}
     * @param rangeLastTypeMap       检测项目大类重点、一般项目数映射 {检测项目大类ID} -> {重点、一般} -> {检测项目大类重点、一般项目数}
     * @param colNum                 当前行
     */
    private void pakFirstTypeAppend(ExcelExportUtil excelExportUtil,
                                    List<List<ExcelExportObject>> excelExportObjectList2,
                                    Map<String, Set<String>> itemTypeIdSetMap,
                                    Map<String, String> rangeLastNameMap,
                                    Map<String, Map<String, Integer>> rangeLastTypeMap,
                                    int colNum) {
        // 放入业务范围小类重点、一般检测项目数
        int index = 2, totalNum = 0, totalKeyNum = 0, totalGeneralNum = 0, totalOtherNum = 0;
        if (itemTypeIdSetMap.get("-1") != null) {
            totalNum = itemTypeIdSetMap.get("-1").size();
        }
        if (itemTypeIdSetMap.get("1") != null) {
            totalKeyNum = itemTypeIdSetMap.get("1").size();
        }
        if (itemTypeIdSetMap.get("2") != null) {
            totalGeneralNum = itemTypeIdSetMap.get("2").size();
        }
        totalOtherNum = totalNum - totalKeyNum - totalGeneralNum;
        if (totalOtherNum < 0) {
            totalOtherNum = 0;
        }
        excelExportObjectList2.add(new ArrayList<ExcelExportObject>());
        excelExportObjectList2.get(0).add(pakExcelExportObject("认证情况", XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.get(0).add(pakExcelExportObject("参数类别", XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.add(new ArrayList<ExcelExportObject>());
        excelExportObjectList2.get(1).add(pakExcelExportObject("现有通过CRM认证参数汇总", XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.get(1).add(pakExcelExportObject("★", XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.add(new ArrayList<ExcelExportObject>());
        excelExportObjectList2.get(2).add(pakExcelExportObject("", XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.get(2).add(pakExcelExportObject("☆", XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.add(new ArrayList<ExcelExportObject>());
        excelExportObjectList2.get(3).add(pakExcelExportObject("", XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.get(3).add(pakExcelExportObject("△", XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.add(new ArrayList<ExcelExportObject>());
        String str = "共通过" + totalNum + "项，其中：重点检测项目（★）" + totalKeyNum +
                "项，一般检测项目（☆）" + totalGeneralNum + "项，其他项目" + totalOtherNum + "项。";
        excelExportObjectList2.get(4).add(pakExcelExportObject(str, XSSFCellStyle.ALIGN_CENTER));
        excelExportObjectList2.get(4).add(pakExcelExportObject("", XSSFCellStyle.ALIGN_CENTER));

        for (TsSimpleCode simpleCode : this.scopeOfBusinessList) {
            String rangeLastId = StringUtils.objectToString(simpleCode.getRid());
            if (!rangeLastNameMap.containsKey(rangeLastId)) {
                continue;
            }
            excelExportObjectList2.get(0).add(pakExcelExportObject(rangeLastNameMap.get(rangeLastId), XSSFCellStyle.ALIGN_CENTER));

            Map<String, Integer> rangeLastTypeNumMap = rangeLastTypeMap.get(rangeLastId);
            // 重点项目
            Integer numKey = ObjectUtil.convert(Integer.class, rangeLastTypeNumMap.get("1"), 0);
            excelExportObjectList2.get(1).add(pakExcelExportObject(numKey, XSSFCellStyle.ALIGN_CENTER));
            // 一般项目
            Integer numGeneral = ObjectUtil.convert(Integer.class, rangeLastTypeNumMap.get("2"), 0);
            excelExportObjectList2.get(2).add(pakExcelExportObject(numGeneral, XSSFCellStyle.ALIGN_CENTER));
            excelExportObjectList2.get(3).add(pakExcelExportObject("", XSSFCellStyle.ALIGN_CENTER));
            excelExportObjectList2.get(4).add(pakExcelExportObject("", XSSFCellStyle.ALIGN_CENTER));
            index++;
        }
        excelExportUtil.addMergeCells(colNum + 2, colNum + 4, 0, 0);
        excelExportUtil.addMergeCells(colNum + 5, colNum + 5, 0, index - 1);
    }

    /**
     * 封装导出数据map
     *
     * @param dataList         原始数据
     * @param proNameMap       业务范围大类名称映射 {业务范围大类ID} -> {业务范围大类名称}
     * @param allDataMap       业务范围大类下小类映射 {业务范围大类ID} -> {业务范围大类下小类}
     * @param rangeLastNameMap 检测项目大类名称映射 {检测项目大类ID} -> {检测项目大类名称}
     * @param rangeLastTypeMap 检测项目大类重点、一般项目数映射 {检测项目大类ID} -> {重点、一般} -> {检测项目大类重点、一般项目数}
     * @param itemTypeIdSetMap 检测项目重点、一般项目ID映射 {重点、一般} -> {检测项目重点、一般项目SET}
     */
    private void pakExcelExportMap(List<Object[]> dataList, Map<String, String> proNameMap,
                                   Map<String, List<Object[]>> allDataMap,
                                   Map<String, String> rangeLastNameMap,
                                   Map<String, Map<String, Integer>> rangeLastTypeMap,
                                   Map<String, Set<String>> itemTypeIdSetMap) {
        List<Object[]> rangeLastList = executeExportSql(2);
        for (Object[] rangeLast : rangeLastList) {
            String rangeLastId = StringUtils.objectToString(rangeLast[0]);
            String rangeLastName = StringUtils.objectToString(rangeLast[1]);
            if (!rangeLastNameMap.containsKey(rangeLastId)) {
                rangeLastNameMap.put(rangeLastId, rangeLastName);
            }
            if (!rangeLastTypeMap.containsKey(rangeLastId)) {
                rangeLastTypeMap.put(rangeLastId, new HashMap<String, Integer>());
            }
        }
        for (Object[] data : dataList) {
            String proId = StringUtils.objectToString(data[7]);
            if (StringUtils.isBlank(proId)) {
                proId = "-1";
            }
            String proName = StringUtils.objectToString(data[8]);
            String rangeLastId = StringUtils.objectToString(data[9]);
            String checkType = StringUtils.objectToString(data[10]);
            boolean empty = ObjectUtil.isEmpty(data[11]);
            if (!proNameMap.containsKey(proId)) {
                proNameMap.put(proId, proName);
            }
            if (!allDataMap.containsKey(proId)) {
                allDataMap.put(proId, new ArrayList<Object[]>());
            }
            if (!empty) {
                allDataMap.get(proId).add(data);
                // 存储所有重点、一般项目的检测能力id
                if (!itemTypeIdSetMap.containsKey("-1")) {
                    itemTypeIdSetMap.put("-1", new HashSet<String>());
                }
                itemTypeIdSetMap.get("-1").add(StringUtils.objectToString(data[11]));
                if (rangeLastNameMap.containsKey(rangeLastId)) {
                    if (!itemTypeIdSetMap.containsKey(checkType)) {
                        itemTypeIdSetMap.put(checkType, new HashSet<String>());
                    }
                    itemTypeIdSetMap.get(checkType).add(StringUtils.objectToString(data[11]));
                }
            }
            if (!rangeLastTypeMap.containsKey(rangeLastId)) {
                rangeLastTypeMap.put(rangeLastId, new HashMap<String, Integer>());
            }
            Map<String, Integer> rangeLastTypeNumMap = rangeLastTypeMap.get(rangeLastId);
            if (!rangeLastTypeNumMap.containsKey(checkType)) {
                rangeLastTypeNumMap.put(checkType, 0);
            }
            // 若没有检测项目则不加一
            if (empty) {
                continue;
            }
            rangeLastTypeNumMap.put(checkType, rangeLastTypeNumMap.get(checkType) + 1);
        }
    }

    /**
     * 封装第一类业务范围大类导出行数据
     *
     * @param headerSize 列数
     * @param chiseseXh  当前序号
     * @param proName    第一类业务范围大类名称
     * @return 第一类业务范围大类导出行数据
     */
    private ExcelExportObject[] pakExcelExportObjects(int headerSize, String chiseseXh, String proName) {
        ExcelExportObject[] objects = new ExcelExportObject[headerSize];
        // 序号
        objects[0] = pakExcelExportObject(chiseseXh, XSSFCellStyle.ALIGN_CENTER);
        // 检测项目对应的大类
        objects[1] = pakExcelExportObject(proName, XSSFCellStyle.ALIGN_LEFT);
        objects[2] = new ExcelExportObject("");
        objects[3] = new ExcelExportObject("");
        objects[4] = new ExcelExportObject("");
        objects[5] = new ExcelExportObject("");
        return objects;
    }

    /**
     * 封装业务范围小类导出行数据
     *
     * @param headerSize 列数
     * @param i          当前序号
     * @param data       业务范围小类原始数据
     * @return 业务范围小类导出行数据
     */
    private ExcelExportObject[] pakExcelExportObjects(int headerSize, int i, Object[] data) {
        ExcelExportObject[] objects = new ExcelExportObject[headerSize];
        // 序号
        objects[0] = pakExcelExportObject(i, XSSFCellStyle.ALIGN_CENTER);
        // 检测项目
        objects[1] = pakExcelExportObject(data[0], XSSFCellStyle.ALIGN_CENTER);
        // 检测标准（方法）名称、编号（含年号）及条款号
        objects[2] = pakExcelExportObject(data[1], XSSFCellStyle.ALIGN_LEFT);
        // 限制说明
        objects[3] = pakExcelExportObject(data[2], XSSFCellStyle.ALIGN_LEFT);
        // 方法验证、确认或论证
        objects[4] = pakExcelExportObject(data[3], XSSFCellStyle.ALIGN_CENTER);
        // 是否通过CMA或CNAS
        String cma = StringUtils.objectToString(data[5]);
        String cnas = StringUtils.objectToString(data[6]);
        data[4] = ("1".equals(cma) || "1".equals(cnas)) ? "是" : "否";
        objects[5] = pakExcelExportObject(data[4], XSSFCellStyle.ALIGN_CENTER);
        return objects;
    }

    /**
     * 封装业务范围小类重点、一般检测项目数导出行数据
     *
     * @param headerSize          列数
     * @param rangeLastNameStr    业务范围小类名称
     * @param rangeLastTypeNumMap 检测项目大类重点、一般项目数映射 {重点、一般} -> {检测项目大类重点、一般项目数}
     * @return 业务范围小类重点、一般检测项目数导出行数据
     */
    private ExcelExportObject[] pakExcelExportObjects(int headerSize, String rangeLastNameStr,
                                                      Map<String, Integer> rangeLastTypeNumMap) {
        ExcelExportObject[] objects = new ExcelExportObject[headerSize];
        // 业务范围小类名称
        objects[0] = pakExcelExportObject(rangeLastNameStr, XSSFCellStyle.ALIGN_CENTER);
        objects[1] = new ExcelExportObject("");
        objects[2] = new ExcelExportObject("");
        // 重点项目
        Integer numKey = ObjectUtil.convert(Integer.class, rangeLastTypeNumMap.get("1"), 0);
        // 一般项目
        Integer numGeneral = ObjectUtil.convert(Integer.class, rangeLastTypeNumMap.get("2"), 0);
        String str = "标注“★”项目共申请" + numKey + "项，标注“☆”项目共申请" + numGeneral + "项；";
        objects[3] = pakExcelExportObject(str, XSSFCellStyle.ALIGN_LEFT);
        objects[4] = new ExcelExportObject("");
        objects[5] = new ExcelExportObject("");
        return objects;
    }

    /**
     * 根据水平样式封装单元格（垂直居中+自动换行）
     *
     * @param object         单元格内容
     * @param alignStyleType 水平样式
     * @return 单元格
     */
    private ExcelExportObject pakExcelExportObject(Object object, short alignStyleType) {
        return new ExcelExportObject(
                StringUtils.objectToString(object), alignStyleType, XSSFCellStyle.VERTICAL_CENTER, true
        );
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public String getSearchUnitId() {
        return searchUnitId;
    }

    public void setSearchUnitId(String searchUnitId) {
        this.searchUnitId = searchUnitId;
    }

    public List<TsSimpleCode> getExportTypeList() {
        return exportTypeList;
    }

    public void setExportTypeList(List<TsSimpleCode> exportTypeList) {
        this.exportTypeList = exportTypeList;
    }

    public List<TsSimpleCode> getScopeOfBusinessList() {
        return scopeOfBusinessList;
    }

    public void setScopeOfBusinessList(List<TsSimpleCode> scopeOfBusinessList) {
        this.scopeOfBusinessList = scopeOfBusinessList;
    }
}
