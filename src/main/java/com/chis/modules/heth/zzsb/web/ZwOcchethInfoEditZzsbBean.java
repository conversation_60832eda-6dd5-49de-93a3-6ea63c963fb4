package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.logic.TdZwOcchethItemsShow;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.enumn.SystemMessageEnum;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description : 职业卫生技术服务机构-申报
 * @ClassAuthor : anjingzwOrgType
 * @Date : 2019/9/24 9:39
 **/
@ManagedBean(name = "zwOcchethInfoEditZzsbBean")
@ViewScoped
public class ZwOcchethInfoEditZzsbBean extends ZwOrgInfoBaseZzsbBean {
    //private List<TdZwOcchethItemsShow> itemsShows;
    private Map<String, TsSimpleCode> itemMap;


    /**
     * 修改、删除、停用、启用操作 传递的检测能力关系表的rid
     */
    private Integer modAbilityRelRid;
    private TbYsjcChkAbilityRelZzsb tbYsjcChkAbilityRel = new TbYsjcChkAbilityRelZzsb();
    /**
     * 选中的方法验证、确认或论证表 码表rid
     */
    private List<String> chkMethodRids;
    /**
     * 法律法规
     */
    private Integer typeNo;
    /**
     * 方法验证、确认或论证表  码表5624
     */
    private List<TsSimpleCode> chkMethodList;
    /**
     * 检测能力
     */
    protected List<Object[]> chkAbilityList;
    /**
     * 查询条件 检测项目名称
     */
    protected String searchProjectName;
    /**
     * 查询条件 检测方法
     */
    protected String searchJcWay;
    /**
     * 查询条件 状态
     */
    protected List<String> searchStates;

    public ZwOcchethInfoEditZzsbBean() {
        super.init();
        this.initItemsShows();
        if (null != zwOrginfoEntity.getState()
                && 1 == zwOrginfoEntity.getState().intValue()) {
            this.viewItemStr();
        }
        initTypeNo();
        //检测能力初始化
        this.searchStates = new ArrayList<>();
        this.searchStates.add("1");//默认启用
        this.initCheckCapability();
    }

    private void initTypeNo() {
        //查询法律法规的id
        this.typeNo = this.zwIntellReportServiceImpl.selectLowTypeId();
        //查询方法验证、确认或论证表 码表5624 初始化
        this.chkMethodList = commService.findLevelSimpleCodesByTypeId("5624");
    }

    /**
     * <p>方法描述：检测能力初始化</p>
     *
     * @MethodAuthor hsj 2025-04-02 14:17
     */
    public void initCheckCapability() {
        this.chkAbilityList = this.zwIntellReportServiceImpl.findChkAbility(zwOrginfoEntity.getTsUnit().getRid(), searchProjectName, searchJcWay, searchStates);
        addChkAbility();
    }

    private void initItemsShows() {
        this.itemsShows = new ArrayList<>();
        this.itemMap = new HashMap<>();
        List<IZwSvritmZzsb> zwSvritms = zwOrginfoEntity.getZwSvritm();
        Map<String, IZwSvritmZzsb> itmMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(zwSvritms)) {//已选择的服务项目
            for (IZwSvritmZzsb svritm : zwSvritms) {
                if (null == svritm.getItemCode()) {
                    continue;
                }
                itmMap.put(svritm.getItemCode().toString(), svritm);
            }
        }
        if (!CollectionUtils.isEmpty(this.jsfwList)) {
            Map<String, TdZwOcchethItemsShow> map = new HashMap<String, TdZwOcchethItemsShow>();
            for (TsSimpleCode t : this.jsfwList) {
                String codeLevelNo = t.getCodeLevelNo();
                if (StringUtils.isBlank(codeLevelNo)) {
                    continue;
                }
                this.itemMap.put(t.getCodeNo(), t);
                if (StringUtils.containsNone(codeLevelNo, ".")) {//业务范围大类
                    TdZwOcchethItemsShow itemShow = new TdZwOcchethItemsShow();
                    itemShow.setItem(t);
                    this.itemsShows.add(itemShow);
                    map.put(codeLevelNo, itemShow);
                } else {
                    String[] val = codeLevelNo.split("\\.");
                    TdZwOcchethItemsShow itemShow = map.get(val[0]);
                    if (null == itemShow) {
                        continue;
                    }
                    List<TsSimpleCode> childList = itemShow.getChildList();
                    if (CollectionUtils.isEmpty(childList)) {//业务范围小类
                        childList = new ArrayList<>();
                        itemShow.setChildList(childList);
                    }
                    childList.add(t);
                    if (null != itmMap.get(t.getCodeNo())) {//已选择的业务范围
                        List<String> selectItems = itemShow.getSelectItems();
                        if (CollectionUtils.isEmpty(selectItems)) {
                            selectItems = new ArrayList<>();
                            itemShow.setSelectItems(selectItems);
                        }
                        selectItems.add(t.getCodeNo());
                    }
                }
            }
        }
    }

    @Override
    protected boolean veryOtherBeforeSave() {
        if (!CollectionUtils.isEmpty(itemsShows)) {
            boolean flag = false;
            for (TdZwOcchethItemsShow t : itemsShows) {
                if (!CollectionUtils.isEmpty(t.getSelectItems())) {
                    flag = true;
                    break;
                }
            }
            if (!flag) {
                JsfUtil.addErrorMessage("业务范围不能为空！");
                return true;
            }
        }
        return false;
    }

    /**
     * <p>方法描述：</p>
     *
     * @MethodAuthor qrr, 2021年3月1日, saveOrgInfo
     */
    @Override
    protected void saveOrgInfo(List<TdZwJsffjgCommAnnexZzsb> annexList) {
        viewItemStr();
        zwOrginfoEntity = zwIntellReportServiceImpl.saveOccHethOrgInfo(zwOrginfoEntity, itemsShows, annexList);
    }

    private void viewItemStr() {
        this.itemStr = null;
        if (!CollectionUtils.isEmpty(itemsShows)) {
            List<String> itemName = new ArrayList<>();
            for (TdZwOcchethItemsShow t : itemsShows) {
                List<String> selectItems = t.getSelectItems();
                if (!CollectionUtils.isEmpty(selectItems)) {
                    StringBuffer sb = new StringBuffer();
                    sb.append(t.getItem().getCodeName());
                    sb.append("（");
                    StringBuffer child = new StringBuffer();
                    for (String s : selectItems) {
                        if (null == this.itemMap.get(s)) {
                            continue;
                        }
                        child.append("，").append(this.itemMap.get(s).getCodeName());
                    }
                    sb.append(child.substring(1)).append("）");
                    itemName.add(sb.toString());
                }
                this.itemStr = itemName.toArray(new String[itemName.size()]);
            }
        }
    }

    /**
     * <p>方法描述：能力检测-停用前提验证</p>
     *
     * @MethodAuthor hsj 2025-04-07 10:35
     */
    public void stopBeforeAction() {
        Integer state = this.zwIntellReportServiceImpl.findCheckAbilityStateByMainId(this.modAbilityRelRid);
        if (!new Integer(1).equals(state)) {
            SystemMessageEnum.CHECK_STATUS_CHANGE.showMessage();
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('StopDialog').show();");
    }

    /**
     * <p>方法描述：检测能力-停用</p>
     *
     * @MethodAuthor hsj 2025-04-02 14:31
     */
    public void stopAction() {
        try {
            this.zwIntellReportServiceImpl.updateCheckAbilityState(this.modAbilityRelRid, 0);
            this.initCheckCapability();
            SystemMessageEnum.DISABLE_SUCCESS.showMessage();
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.DISABLE_FAIL.showMessage();
        }

    }

    /**
     * <p>方法描述：检测能力-启用前提验证</p>
     *
     * @MethodAuthor hsj 2025-04-07 10:38
     */
    public void startBeforeAction() {
        Integer state = this.zwIntellReportServiceImpl.findCheckAbilityStateByMainId(this.modAbilityRelRid);
        if (!new Integer(0).equals(state)) {
            SystemMessageEnum.CHECK_STATUS_CHANGE.showMessage();
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('StartDialog').show();");

    }

    /**
     * <p>方法描述：检测能力-启用</p>
     *
     * @MethodAuthor hsj 2025-04-02 14:32
     */
    public void startAction() {

        try {
            this.zwIntellReportServiceImpl.updateCheckAbilityState(this.modAbilityRelRid, 1);
            this.initCheckCapability();
            SystemMessageEnum.ENABLE_SUCCESS.showMessage();
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.ENABLE_FAIL.showMessage();
        }
    }

    /**
     * <p>方法描述：检测能力-删除前提验证</p>
     *
     * @MethodAuthor hsj 2025-04-07 10:40
     */
    public void deleteBeforeAction() {
        boolean flag = this.zwIntellReportServiceImpl.findCheckAbilityByMainId(this.modAbilityRelRid);
        if (!flag) {
            SystemMessageEnum.DATA_NOT_EXIST.showMessage();
            return;
        }
        //先查询是否有与其他项目相关联，如果有不能删除
        String sql = "SELECT COUNT(RID)  FROM TD_YSJC_SAMP_RSN_ITEM WHERE JC_BASE_ID = " + this.modAbilityRelRid;
        if (this.zwIntellReportServiceImpl.findCountBySql(sql) > 0) {
            JsfUtil.addErrorMessage("该检测项目已被引用，不允许删除!");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('DeleteDialog').show();");
    }

    /**
     * <p>方法描述：检测能力-删除</p>
     *
     * @MethodAuthor hsj 2025-04-02 14:34
     */
    public void deleteAction() {
        try {
            this.zwIntellReportServiceImpl.deleteTdYsjcChkManage(this.modAbilityRelRid);
            this.initCheckCapability();
            SystemMessageEnum.DELETE_SUCCESS.showMessage();
        } catch (Exception e) {
            e.printStackTrace();
            SystemMessageEnum.DELETE_FAIL.showMessage();
        }
    }

    /**
     * <p>方法描述：检测能力-添加初始化</p>
     *
     * @MethodAuthor hsj 2025-04-02 14:48
     */
    public void addChkAbility() {
        tbYsjcChkAbilityRel = new TbYsjcChkAbilityRelZzsb();
        tbYsjcChkAbilityRel.setFkByJcBaseId(new TbTjLawCheckZzsb());
        tbYsjcChkAbilityRel.setFkByItemId(new TsSimpleCode());
        tbYsjcChkAbilityRel.setState(1);
        this.chkMethodRids = new ArrayList<>();
    }

    /**
     * <p>方法描述：检测能力-修改初始化</p>
     *
     * @MethodAuthor hsj 2025-04-02 14:49
     */
    public void modChkAbility() {
        boolean flag = this.zwIntellReportServiceImpl.findCheckAbilityByMainId(this.modAbilityRelRid);
        if (!flag) {
            SystemMessageEnum.DATA_NOT_EXIST.showMessage();
            return;
        }
        this.tbYsjcChkAbilityRel = this.commService.find(TbYsjcChkAbilityRelZzsb.class, this.modAbilityRelRid);
        //初始化方法验证、确认或论证
        this.chkMethodRids = new ArrayList<>();
        List<TbYsjcChkMethodZzsb> chkMethods = this.commService.findEntityListByMainId(TbYsjcChkMethodZzsb.class, this.tbYsjcChkAbilityRel.getRid());
        if (!CollectionUtils.isEmpty(chkMethods)) {
            for (TbYsjcChkMethodZzsb chkMethod : chkMethods) {
                this.chkMethodRids.add(chkMethod.getFkByMethodId().getRid().toString());
            }
        }
        RequestContext.getCurrentInstance().execute("PF('AbilityDetectionDialog').show()");
    }

    /**
     * <p>方法描述：选择检测项目</p>
     *
     * @MethodAuthor hsj 2025-04-02 15:16
     */
    public void selectCheckProAction() {

        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 660);
        options.put("contentWidth", 630);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("检测项目");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add("5516");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeRadioSelectListNew", options, paramMap);
    }

    /**
     * <p>方法描述：选择检测项目后</p>
     *
     * @MethodAuthor hsj 2025-04-02 15:17
     */
    public void onCheckProSearch(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TsSimpleCode code = selectedMap.get("selectPro") == null ? null
                    : (TsSimpleCode) selectedMap.get("selectPro");
            code = systemModuleService.findSimpleCodeByRid(code.getRid());
            this.tbYsjcChkAbilityRel.setFkByItemId(code);

        }
    }

    /**
     * <p>方法描述：选择检测依据弹窗</p>
     *
     * @MethodAuthor hsj 2025-04-02 15:17
     */
    public void selectCheckBasisAction() {

        if (null == this.typeNo) {
            JsfUtil.addErrorMessage("检测依据为空，请先维护检测依据！");
            return;
        }
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentWidth", 800);
        options.put("contentHeight", 480);
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("法律法规");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add(this.typeNo.intValue() + "");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/zzsb/badRsnCheckBasis", options, paramMap);

    }

    /**
     * <p>方法描述：检测依据选择</p>
     *
     * @MethodAuthor hsj 2025-04-02 15:20
     */
    public void onCheckBasisSearch(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TbTjLawCheckZzsb code = selectedMap.get("selectPro") == null ? null
                    : (TbTjLawCheckZzsb) selectedMap.get("selectPro");
            this.tbYsjcChkAbilityRel.setFkByJcBaseId(code);
            this.tbYsjcChkAbilityRel.setJcWay(code.getLawName());
        }
    }

    /**
     * <p>方法描述：检测能力-保存</p>
     *
     * @MethodAuthor hsj 2025-04-02 15:25
     */
    public void saveAbilityAction() {
        boolean flag = false;

        if (null == this.tbYsjcChkAbilityRel.getFkByItemId() || null == this.tbYsjcChkAbilityRel.getFkByItemId().getRid()) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("检测项目名称");
            flag = true;
        }
        if (null == this.tbYsjcChkAbilityRel.getFkByJcBaseId().getLawCode()) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("检测依据");
            flag = true;
        }

        if (StringUtils.isEmpty(this.tbYsjcChkAbilityRel.getJcWay())) {
            SystemMessageEnum.NOT_EMPTY.formatMessage("检测方法");
            flag = true;
        }
        if (StringUtils.isNotBlank(this.tbYsjcChkAbilityRel.getLimitDesc()) && this.tbYsjcChkAbilityRel.getLimitDesc().length() > 500) {
            SystemMessageEnum.LENGTH_VERIFY.formatMessage("限制说明", 500);
            flag = true;
        }
        if (null == this.tbYsjcChkAbilityRel.getLabApprovalTag()) {
            SystemMessageEnum.PLEASE_SELECT.formatMessage("实验室认可标记（CNAS）");
            flag = true;
        }

        if (null == this.tbYsjcChkAbilityRel.getQualApprovalTag()) {
            SystemMessageEnum.PLEASE_SELECT.formatMessage("资质认定标记（CMA）");
            flag = true;
        }

        if (null == this.tbYsjcChkAbilityRel.getOutTag()) {
            SystemMessageEnum.PLEASE_SELECT.formatMessage("外包标记");
            flag = true;
        }

        if (null == this.tbYsjcChkAbilityRel.getState()) {
            SystemMessageEnum.PLEASE_SELECT.formatMessage("状态");
            flag = true;
        }
        if (flag) {
            return;
        }
        try {
            this.zwIntellReportServiceImpl.addOrUpdateTdYsjcChkManage(this.tbYsjcChkAbilityRel, this.zwOrginfoEntity.getTsUnit(), this.chkMethodRids);
            SystemMessageEnum.SAVE_SUCCESS.showMessage();
            //关闭弹出框
            RequestContext currentInstance = RequestContext.getCurrentInstance();
            currentInstance.execute("PF('AbilityDetectionDialog').hide()");
            currentInstance.update("mainForm:tabView:chkAbilityTable");
        } catch (Exception e) {
            SystemMessageEnum.SAVE_FAIL.showMessage();
            e.printStackTrace();
        }
        this.initCheckCapability();
    }

    @Override
    public boolean chkAbilitySubmitValidate() {
        RequestContext.getCurrentInstance().update("mainForm:tabView:chkAbilityTable");
        return this.zwIntellReportServiceImpl.findChkAbilityCount(zwOrginfoEntity.getTsUnit().getRid());
    }

    @Override
    public void afterSubmitInit() {
        //检测能力初始化
        this.initCheckCapability();
        RequestContext.getCurrentInstance().update("mainForm:tabView:chkAbilityTable");
    }

    @Override
    public Integer getOrgInfoType() {
        return 3;
    }

    @Override
    public Class getZwIntellReportClass() {
        return TdZwOcchethInfoZzsb.class;
    }


    public Integer getModAbilityRelRid() {
        return modAbilityRelRid;
    }

    public void setModAbilityRelRid(Integer modAbilityRelRid) {
        this.modAbilityRelRid = modAbilityRelRid;
    }

    public TbYsjcChkAbilityRelZzsb getTbYsjcChkAbilityRel() {
        return tbYsjcChkAbilityRel;
    }

    public void setTbYsjcChkAbilityRel(TbYsjcChkAbilityRelZzsb tbYsjcChkAbilityRel) {
        this.tbYsjcChkAbilityRel = tbYsjcChkAbilityRel;
    }

    public List<String> getChkMethodRids() {
        return chkMethodRids;
    }

    public void setChkMethodRids(List<String> chkMethodRids) {
        this.chkMethodRids = chkMethodRids;
    }

    public List<TsSimpleCode> getChkMethodList() {
        return chkMethodList;
    }

    public void setChkMethodList(List<TsSimpleCode> chkMethodList) {
        this.chkMethodList = chkMethodList;
    }

    public Integer getTypeNo() {
        return typeNo;
    }

    public void setTypeNo(Integer typeNo) {
        this.typeNo = typeNo;
    }

    public List<Object[]> getChkAbilityList() {
        return chkAbilityList;
    }

    public void setChkAbilityList(List<Object[]> chkAbilityList) {
        this.chkAbilityList = chkAbilityList;
    }

    public String getSearchProjectName() {
        return searchProjectName;
    }

    public void setSearchProjectName(String searchProjectName) {
        this.searchProjectName = searchProjectName;
    }

    public String getSearchJcWay() {
        return searchJcWay;
    }

    public void setSearchJcWay(String searchJcWay) {
        this.searchJcWay = searchJcWay;
    }

    public List<String> getSearchStates() {
        return searchStates;
    }

    public void setSearchStates(List<String> searchStates) {
        this.searchStates = searchStates;
    }
}
