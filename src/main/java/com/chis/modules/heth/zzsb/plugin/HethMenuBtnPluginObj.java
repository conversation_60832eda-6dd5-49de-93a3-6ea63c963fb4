package com.chis.modules.heth.zzsb.plugin;

import com.chis.modules.system.entity.TsMenuBtn;

import java.util.HashSet;
import java.util.Set;

/**
 * 菜单按钮的插件
 * <AUTHOR>
 */
public class HethMenuBtnPluginObj {

	public static Set<TsMenuBtn> menuSet;
	
	static {
		menuSet = new HashSet<TsMenuBtn>();
		// 其中第一个参数,用来配置菜单级别，主要根据第二个参数来比较判断
		//资质机构查询【职业健康检查机构】【放射卫生技术服务机构】【职业病诊断机构】【职业卫生技术服务机构】按钮
		/*menuSet.add(new TsMenuBtn("heth_zzjgcx_zzsb","zzsb_zyjkjcjg","职业健康检查机构"));
		menuSet.add(new TsMenuBtn("heth_zzjgcx_zzsb","zzsb_fswsjsfwjg","放射卫生技术服务机构"));
		menuSet.add(new TsMenuBtn("heth_zzjgcx_zzsb","zzsb_zybzdjg","职业病诊断机构"));
		menuSet.add(new TsMenuBtn("heth_zzjgcx_zzsb","zzsb_zywsjsfwjg","职业卫生技术服务机构"));*/
		//menuSet.add(new TsMenuBtn("heth_zzjgcx_zzsb","zzsb_zcjg","技术支撑机构"));
		/*//资质机构备案申请
		menuSet.add(new TsMenuBtn("heth_zzjgbasq_zzsb","zzsb_zzjgbasq_zyjkjcjg","职业健康检查机构"));
		menuSet.add(new TsMenuBtn("heth_zzjgbasq_zzsb","zzsb_zzjgbasq_zybzdjg","职业病诊断机构"));
		//资质机构备案审核-初审
		menuSet.add(new TsMenuBtn("heth_zzjgbashcs_zzsb","zzsb_zzjgbashcs_zyjkjcjg","职业健康检查机构"));
		menuSet.add(new TsMenuBtn("heth_zzjgbashcs_zzsb","zzsb_zzjgbashcs_zybzdjg","职业病诊断机构"));
		//资质机构备案审核-终审
		menuSet.add(new TsMenuBtn("heth_zzjgbashzs_zzsb","zzsb_zzjgbashzs_zyjkjcjg","职业健康检查机构"));
		menuSet.add(new TsMenuBtn("heth_zzjgbashzs_zzsb","zzsb_zzjgbashzs_zybzdjg","职业病诊断机构"));*/
		//吉林职卫 【资质机构审核】菜单按钮【web-heth-zzsb】
		//menuSet.add(new TsMenuBtn("heth_zzjgsh_zzsb","heth_zzjgsh_zyjkjcjg","职业健康检查机构"));
		//吉林职卫 【专业技术人员档案检索】菜单按钮【web-heth-zzsb】
		//menuSet.add(new TsMenuBtn("heth_cxtj_zyjsrydajs","heth_zyjsrydajs_zyjkjcjg","职业健康检查机构"));
		//menuSet.add(new TsMenuBtn("heth_cxtj_zyjsrydajs","heth_zyjsrydajs_fswsjsfwjg","放射卫生技术服务机构"));
		//menuSet.add(new TsMenuBtn("heth_cxtj_zyjsrydajs","heth_zyjsrydajs_zybzdjg","职业病诊断机构"));
		//menuSet.add(new TsMenuBtn("heth_cxtj_zyjsrydajs","heth_zyjsrydajs_zywsjsfwjg","职业卫生技术服务机构"));
		//menuSet.add(new TsMenuBtn("heth_cxtj_zyjsrydajs","heth_zyjsrydajs_zcjg","技术支撑机构"));
	}
	
}