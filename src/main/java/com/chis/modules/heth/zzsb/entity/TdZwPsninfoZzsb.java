package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.google.common.collect.Lists;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 * 
 * <p>修订内容：新增收件人信息、资质信息中附件上传字段</p>
 * @ClassReviser qrr,2018年6月11日,TdZwPsninfo
 */
@Entity
@Table(name = "TD_ZW_PSNINFO")
@SequenceGenerator(name = "TdZwPsninfoSeq", sequenceName = "TD_ZW_PSNINFO_SEQ", allocationSize = 1)
public class TdZwPsninfoZzsb implements java.io.Serializable {
	private static final long serialVersionUID = 1654332130821789732L;

	private Integer rid;
	private TsUnit tsUnit;
	private String empName;
	private String sex;
	private String birthday;
	private String thetitle;
	private String position;
	private String prof;
	private Date createDate;
	private Integer createManid;
	private String idcCard;
	private String nationality;
	private TsSimpleCode fkByNation;
	private Integer nation;
	private String linkAddr;
	private String telPhone;
	private String mobileNo;
	private String postcode;
	private TsSimpleCode fkByEducationId;
	private Integer educationId;
	private String gradNo;
	private String certNo;
	private String doctCertNo;
	private Date doctCertDate;
	private TsSimpleCode fkByDoctLevelId;
	private Integer doctLevelId;
	private TsSimpleCode fkByDoctTypeId;
	private TsSimpleCode fkByDoctSpecId;
	private String doctPraNo;
	private String doctPraPlace;
	private Date doctPraDate;
	private String titleCertNo;
	private String techTitle;
	private Integer specialTag;
	private TsSimpleCode fkByTitleId;
	private Integer titleId;

    /**是否被选中*/
    private Boolean selected = false;
	
	private List<TdZwPsntjMainZzsb> tjMainList = Lists.newArrayList();
	private List<TdZwPsnZzlbZzsb> zzlbList=Lists.newArrayList();
	/**是否显示选择*/
	private boolean ifChoose;
	
	private String certNoStr;
	
	private Integer psnType;
	
	private String email;
	private Integer onDuty;
	private String beJob;
	private Integer beZyWorkYears;
	private Integer beFsWorkYears;
	private String doctOtherPlace;
	private String idcPath;
	private String idcAnnexName;
	private String doctQualPath;
	private String qualAnnexName;
	private String doctPraPath;
	private String praAnnxeName;
	private TsSimpleCode fkByDegreeId;
	private Integer degreeId;
	private String zzlbs;
	//培训合格证/学分证收件人姓名
	private String receiveEmpName;
	//收件地址
	private String receiveLinkAddr;
	//联系电话[收件]
	private String receiveMobileNo;
	//邮政编码[收件]
	private String receivePostcode;
	//毕业证书附件地址
	private String gradPath;
	//中级以上（含）专业技术任职资格证书
	private String holdCentAnnexName;
	//中级以上（含）专业技术任职资格证书附件地址
	private String holdCentPath;
	//毕业证书附件名称
	private String gradAnnxeName;
	//中级以上（含）专业技术任职聘任书附件地址
	private String employCentPath;
	//中级以上（含）专业技术任职聘任书附件名称
	private String employCentAnnexName;
	//职称级别
	private TsSimpleCode fkByTitleLevelId;
	private Integer titleLevelId;
	//职称证书附件地址
	private String titleCentPath;
	//职称证书附件名称
	private String titleCentAnnexName;
	//是否为鉴定专家
	private String ifCheckExpert;
	
	private String age;

    private String trainQualAnnexName;
    private String trainQualAnnexPath;
    private String fsQualAnnexName;
    private String fsQualAnnexPath;
	//资质人员来源
	private Integer addSource;
	
	private String psnTypes;
	/**从事项目--资质范围*/
	private String zzlbRange;
	/**劳动关系证明附件 路径*/
	private String  laborRelAnnexPath;
	/**劳动关系证明附件 名称*/
	private String laborRelAnnexName;
	/**
	 * 是否承担教学和科研任务
	 */
	private Integer ifScienTask;
	/**
	 * 从事岗位ID
	 */
	private TsSimpleCode fkByBeJobId;
	/**
	 * 从事岗位
	 */
	private Integer beJobId;
	/**
	 * 从事岗位名称
	 */
	private String beJobName;
	/**
	 * 从事岗位extends1
	 */
	private String beJobExtends1;
	private String beJobOther;
	/**
	 * 主数据时间
	 */
	private Date masterDataTime;

	public TdZwPsninfoZzsb() {
	}

	public TdZwPsninfoZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwPsninfoSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID")
	public TsUnit getTsUnit() {
		return this.tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}

	@Column(name = "EMP_NAME" , length = 100)
	public String getEmpName() {
		return this.empName;
	}

	public void setEmpName(String empName) {
		this.empName = empName;
	}

	@Column(name = "SEX" , length = 10)
	public String getSex() {
		return this.sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	@Column(name = "BIRTHDAY", length = 20)
	public String getBirthday() {
		return this.birthday;
	}

	public void setBirthday(String birthday) {
		this.birthday = birthday;
	}

	@Column(name = "THETITLE", length = 50)
	public String getThetitle() {
		return this.thetitle;
	}

	public void setThetitle(String thetitle) {
		this.thetitle = thetitle;
	}

	@Column(name = "POSITION", length = 50)
	public String getPosition() {
		return this.position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	@Column(name = "PROF", length = 50)
	public String getProf() {
		return this.prof;
	}

	public void setProf(String prof) {
		this.prof = prof;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	/** TdZwPsntjMainZzsb 仅江苏放射里有用到，其他项目无使用 20220217确认 */
	@Deprecated
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByEmpId", orphanRemoval = true)
	public List<TdZwPsntjMainZzsb> getTjMainList() {
		return tjMainList;
	}

	public void setTjMainList(List<TdZwPsntjMainZzsb> tjMainList) {
		this.tjMainList = tjMainList;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((rid == null) ? 0 : rid.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		TdZwPsninfoZzsb other = (TdZwPsninfoZzsb) obj;
		if (rid == null) {
			return false;
		} else if (!rid.equals(other.rid))
			return false;
		return true;
	}

	@Transient
	public Boolean getSelected() {
		return selected;
	}

	public void setSelected(Boolean selected) {
		this.selected = selected;
	}

	@Column(name = "IDC_CARD")	
	public String getIdcCard() {
		return idcCard;
	}

	public void setIdcCard(String idcCard) {
		this.idcCard = idcCard;
	}	
			
	@Column(name = "NATIONALITY")	
	public String getNationality() {
		return nationality;
	}

	public void setNationality(String nationality) {
		this.nationality = nationality;
	}	
			
	@ManyToOne
	@JoinColumn(name = "NATION")			
	public TsSimpleCode getFkByNation() {
		return fkByNation;
	}

	public void setFkByNation(TsSimpleCode fkByNation) {
		this.fkByNation = fkByNation;
	}

	@Transient
	public Integer getNation() {
		return nation;
	}

	public void setNation(Integer nation) {
		this.nation = nation;
	}

	@Column(name = "LINK_ADDR")
	public String getLinkAddr() {
		return linkAddr;
	}

	public void setLinkAddr(String linkAddr) {
		this.linkAddr = linkAddr;
	}	
			
	@Column(name = "TEL_PHONE")	
	public String getTelPhone() {
		return telPhone;
	}

	public void setTelPhone(String telPhone) {
		this.telPhone = telPhone;
	}	
			
	@Column(name = "MOBILE_NO")	
	public String getMobileNo() {
		return mobileNo;
	}

	public void setMobileNo(String mobileNo) {
		this.mobileNo = mobileNo;
	}	
			
	@Column(name = "POSTCODE")	
	public String getPostcode() {
		return postcode;
	}

	public void setPostcode(String postcode) {
		this.postcode = postcode;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EDUCATION_ID")			
	public TsSimpleCode getFkByEducationId() {
		return fkByEducationId;
	}

	public void setFkByEducationId(TsSimpleCode fkByEducationId) {
		this.fkByEducationId = fkByEducationId;
	}

	@Transient
	public Integer getEducationId() {
		return educationId;
	}

	public void setEducationId(Integer educationId) {
		this.educationId = educationId;
	}

	@Column(name = "GRAD_NO")
	public String getGradNo() {
		return gradNo;
	}

	public void setGradNo(String gradNo) {
		this.gradNo = gradNo;
	}	
			
	@Column(name = "CERT_NO")	
	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}	
			
	@Column(name = "DOCT_CERT_NO")	
	public String getDoctCertNo() {
		return doctCertNo;
	}

	public void setDoctCertNo(String doctCertNo) {
		this.doctCertNo = doctCertNo;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DOCT_CERT_DATE")			
	public Date getDoctCertDate() {
		return doctCertDate;
	}

	public void setDoctCertDate(Date doctCertDate) {
		this.doctCertDate = doctCertDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DOCT_LEVEL_ID")			
	public TsSimpleCode getFkByDoctLevelId() {
		return fkByDoctLevelId;
	}

	public void setFkByDoctLevelId(TsSimpleCode fkByDoctLevelId) {
		this.fkByDoctLevelId = fkByDoctLevelId;
	}

	@Transient
	public Integer getDoctLevelId() {
		return doctLevelId;
	}

	public void setDoctLevelId(Integer doctLevelId) {
		this.doctLevelId = doctLevelId;
	}

	@ManyToOne
	@JoinColumn(name = "DOCT_TYPE_ID")			
	public TsSimpleCode getFkByDoctTypeId() {
		return fkByDoctTypeId;
	}

	public void setFkByDoctTypeId(TsSimpleCode fkByDoctTypeId) {
		this.fkByDoctTypeId = fkByDoctTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DOCT_SPEC_ID")			
	public TsSimpleCode getFkByDoctSpecId() {
		return fkByDoctSpecId;
	}

	public void setFkByDoctSpecId(TsSimpleCode fkByDoctSpecId) {
		this.fkByDoctSpecId = fkByDoctSpecId;
	}	
			
	@Column(name = "DOCT_PRA_NO")	
	public String getDoctPraNo() {
		return doctPraNo;
	}

	public void setDoctPraNo(String doctPraNo) {
		this.doctPraNo = doctPraNo;
	}	
			
	@Column(name = "DOCT_PRA_PLACE")	
	public String getDoctPraPlace() {
		return doctPraPlace;
	}

	public void setDoctPraPlace(String doctPraPlace) {
		this.doctPraPlace = doctPraPlace;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "DOCT_PRA_DATE")			
	public Date getDoctPraDate() {
		return doctPraDate;
	}

	public void setDoctPraDate(Date doctPraDate) {
		this.doctPraDate = doctPraDate;
	}	
			
	@Column(name = "TITLE_CERT_NO")	
	public String getTitleCertNo() {
		return titleCertNo;
	}

	public void setTitleCertNo(String titleCertNo) {
		this.titleCertNo = titleCertNo;
	}	
			
	@Column(name = "TECH_TITLE")	
	public String getTechTitle() {
		return techTitle;
	}

	public void setTechTitle(String techTitle) {
		this.techTitle = techTitle;
	}	
			
	@Column(name = "SPECIAL_TAG")	
	public Integer getSpecialTag() {
		return specialTag;
	}

	public void setSpecialTag(Integer specialTag) {
		this.specialTag = specialTag;
	}

    @OrderBy(value = "sendDate desc")
	@OneToMany(cascade = CascadeType.REFRESH, fetch = FetchType.LAZY, mappedBy = "fkByPsnId")
	public List<TdZwPsnZzlbZzsb> getZzlbList() {
		return zzlbList;
	}

	public void setZzlbList(List<TdZwPsnZzlbZzsb> zzlbList) {
		this.zzlbList = zzlbList;
	}

	@Transient
	public boolean isIfChoose() {
		return ifChoose;
	}

	public void setIfChoose(boolean ifChoose) {
		this.ifChoose = ifChoose;
	}

	@ManyToOne
	@JoinColumn(name = "TITLE_ID")			
	public TsSimpleCode getFkByTitleId() {
		return fkByTitleId;
	}

	public void setFkByTitleId(TsSimpleCode fkByTitleId) {
		this.fkByTitleId = fkByTitleId;
	}

	@Transient
	public Integer getTitleId() {
		return titleId;
	}

	public void setTitleId(Integer titleId) {
		this.titleId = titleId;
	}

	@Transient
	public String getCertNoStr() {
		return certNoStr;
	}

	public void setCertNoStr(String certNoStr) {
		this.certNoStr = certNoStr;
	}

	@Column(name = "PSN_TYPE")
	public Integer getPsnType() {
		return psnType;
	}

	public void setPsnType(Integer psnType) {
		this.psnType = psnType;
	}	
	
	@Column(name = "EMAIL")	
	public String getEmail() {
	return email;
	}
	
	public void setEmail(String email) {
	this.email = email;
	}	
		
	@Column(name = "ON_DUTY")	
	public Integer getOnDuty() {
	return onDuty;
	}
	
	public void setOnDuty(Integer onDuty) {
	this.onDuty = onDuty;
	}	
		
	@Column(name = "BE_JOB")	
	public String getBeJob() {
	return beJob;
	}
	
	public void setBeJob(String beJob) {
	this.beJob = beJob;
	}	
		
	@Column(name = "BE_ZY_WORK_YEARS")	
	public Integer getBeZyWorkYears() {
	return beZyWorkYears;
	}
	
	public void setBeZyWorkYears(Integer beZyWorkYears) {
	this.beZyWorkYears = beZyWorkYears;
	}

	@Column(name = "BE_FS_WORK_YEARS")
	public Integer getBeFsWorkYears() {
		return beFsWorkYears;
	}

	public void setBeFsWorkYears(Integer beFsWorkYears) {
		this.beFsWorkYears = beFsWorkYears;
	}

	@Column(name = "DOCT_OTHER_PLACE")
	public String getDoctOtherPlace() {
	return doctOtherPlace;
	}
	
	public void setDoctOtherPlace(String doctOtherPlace) {
	this.doctOtherPlace = doctOtherPlace;
	}	
		
	@Column(name = "IDC_PATH")	
	public String getIdcPath() {
	return idcPath;
	}
	
	public void setIdcPath(String idcPath) {
	this.idcPath = idcPath;
	}	
		
	@Column(name = "IDC_ANNEX_NAME")	
	public String getIdcAnnexName() {
	return idcAnnexName;
	}
	
	public void setIdcAnnexName(String idcAnnexName) {
	this.idcAnnexName = idcAnnexName;
	}	
		
	@Column(name = "DOCT_QUAL_PATH")	
	public String getDoctQualPath() {
	return doctQualPath;
	}
	
	public void setDoctQualPath(String doctQualPath) {
	this.doctQualPath = doctQualPath;
	}	
		
	@Column(name = "QUAL_ANNEX_NAME")	
	public String getQualAnnexName() {
	return qualAnnexName;
	}
	
	public void setQualAnnexName(String qualAnnexName) {
	this.qualAnnexName = qualAnnexName;
	}	
		
	@Column(name = "DOCT_PRA_PATH")	
	public String getDoctPraPath() {
	return doctPraPath;
	}
	
	public void setDoctPraPath(String doctPraPath) {
	this.doctPraPath = doctPraPath;
	}	
		
	@Column(name = "PRA_ANNXE_NAME")	
	public String getPraAnnxeName() {
	return praAnnxeName;
	}
	
	public void setPraAnnxeName(String praAnnxeName) {
	this.praAnnxeName = praAnnxeName;
	}	
	@ManyToOne
	@JoinColumn(name = "DEGREE_ID")
	public TsSimpleCode getFkByDegreeId() {
		return fkByDegreeId;
	}

	public void setFkByDegreeId(TsSimpleCode fkByDegreeId) {
		this.fkByDegreeId = fkByDegreeId;
	}

	@Transient
	public Integer getDegreeId() {
		return degreeId;
	}

	public void setDegreeId(Integer degreeId) {
		this.degreeId = degreeId;
	}

	@Transient
	public String getZzlbs() {
		return zzlbs;
	}

	public void setZzlbs(String zzlbs) {
		this.zzlbs = zzlbs;
	}
	
	@Column(name = "RECEIVE_EMP_NAME")	
	public String getReceiveEmpName() {
		return receiveEmpName;
	}

	public void setReceiveEmpName(String receiveEmpName) {
		this.receiveEmpName = receiveEmpName;
	}	
			
	@Column(name = "RECEIVE_LINK_ADDR")	
	public String getReceiveLinkAddr() {
		return receiveLinkAddr;
	}

	public void setReceiveLinkAddr(String receiveLinkAddr) {
		this.receiveLinkAddr = receiveLinkAddr;
	}	
			
	@Column(name = "RECEIVE_MOBILE_NO")	
	public String getReceiveMobileNo() {
		return receiveMobileNo;
	}

	public void setReceiveMobileNo(String receiveMobileNo) {
		this.receiveMobileNo = receiveMobileNo;
	}	
			
	@Column(name = "RECEIVE_POSTCODE")	
	public String getReceivePostcode() {
		return receivePostcode;
	}

	public void setReceivePostcode(String receivePostcode) {
		this.receivePostcode = receivePostcode;
	}	
			
	@Column(name = "GRAD_PATH")	
	public String getGradPath() {
		return gradPath;
	}

	public void setGradPath(String gradPath) {
		this.gradPath = gradPath;
	}	
			
	@Column(name = "HOLD_CENT_ANNEX_NAME")	
	public String getHoldCentAnnexName() {
		return holdCentAnnexName;
	}

	public void setHoldCentAnnexName(String holdCentAnnexName) {
		this.holdCentAnnexName = holdCentAnnexName;
	}	
			
	@Column(name = "HOLD_CENT_PATH")	
	public String getHoldCentPath() {
		return holdCentPath;
	}

	public void setHoldCentPath(String holdCentPath) {
		this.holdCentPath = holdCentPath;
	}	
			
	@Column(name = "GRAD_ANNXE_NAME")	
	public String getGradAnnxeName() {
		return gradAnnxeName;
	}

	public void setGradAnnxeName(String gradAnnxeName) {
		this.gradAnnxeName = gradAnnxeName;
	}	
			
	@Column(name = "EMPLOY_CENT_PATH")	
	public String getEmployCentPath() {
		return employCentPath;
	}

	public void setEmployCentPath(String employCentPath) {
		this.employCentPath = employCentPath;
	}	
			
	@Column(name = "EMPLOY_CENT_ANNEX_NAME")	
	public String getEmployCentAnnexName() {
		return employCentAnnexName;
	}

	public void setEmployCentAnnexName(String employCentAnnexName) {
		this.employCentAnnexName = employCentAnnexName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "TITLE_LEVEL_ID")			
	public TsSimpleCode getFkByTitleLevelId() {
		return fkByTitleLevelId;
	}

	public void setFkByTitleLevelId(TsSimpleCode fkByTitleLevelId) {
		this.fkByTitleLevelId = fkByTitleLevelId;
	}	
			
	@Column(name = "TITLE_CENT_PATH")	
	public String getTitleCentPath() {
		return titleCentPath;
	}

	public void setTitleCentPath(String titleCentPath) {
		this.titleCentPath = titleCentPath;
	}	
			
	@Column(name = "TITLE_CENT_ANNEX_NAME")	
	public String getTitleCentAnnexName() {
		return titleCentAnnexName;
	}

	public void setTitleCentAnnexName(String titleCentAnnexName) {
		this.titleCentAnnexName = titleCentAnnexName;
	}	
			
	@Column(name = "IF_CHECK_EXPERT")	
	public String getIfCheckExpert() {
		return ifCheckExpert;
	}

	public void setIfCheckExpert(String ifCheckExpert) {
		this.ifCheckExpert = ifCheckExpert;
	}
	@Transient
	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

    @Column(name = "TRAIN_QUAL_ANNEX_NAME")
    public String getTrainQualAnnexName() {
        return trainQualAnnexName;
    }

    public void setTrainQualAnnexName(String trainQualAnnexName) {
        this.trainQualAnnexName = trainQualAnnexName;
    }

    @Column(name = "TRAIN_QUAL_ANNEX_PATH")
    public String getTrainQualAnnexPath() {
        return trainQualAnnexPath;
    }

    public void setTrainQualAnnexPath(String trainQualAnnexPath) {
        this.trainQualAnnexPath = trainQualAnnexPath;
    }

    @Column(name = "FS_QUAL_ANNEX_NAME")
    public String getFsQualAnnexName() {
        return fsQualAnnexName;
    }

    public void setFsQualAnnexName(String fsQualAnnexName) {
        this.fsQualAnnexName = fsQualAnnexName;
    }

    @Column(name = "FS_QUAL_ANNEX_PATH")
    public String getFsQualAnnexPath() {
        return fsQualAnnexPath;
    }

    public void setFsQualAnnexPath(String fsQualAnnexPath) {
        this.fsQualAnnexPath = fsQualAnnexPath;
    }

    @Column(name = "ADD_SOURCE")
	public Integer getAddSource() {
		return addSource;
	}

	public void setAddSource(Integer addSource) {
		this.addSource = addSource;
	}
	@Transient
	public String getPsnTypes() {
		return psnTypes;
	}

	public void setPsnTypes(String psnTypes) {
		this.psnTypes = psnTypes;
	}

	@Transient
    public String getZzlbRange() {
        return zzlbRange;
    }

    public void setZzlbRange(String zzlbRange) {
        this.zzlbRange = zzlbRange;
    }

	@Column(name = "LABOR_REL_ANNEX_PATH")
	public String getLaborRelAnnexPath() {
		return laborRelAnnexPath;
	}

	public void setLaborRelAnnexPath(String laborRelAnnexPath) {
		this.laborRelAnnexPath = laborRelAnnexPath;
	}

	@Column(name = "LABOR_REL_ANNEX_NAME")
	public String getLaborRelAnnexName() {
		return laborRelAnnexName;
	}

	public void setLaborRelAnnexName(String laborRelAnnexName) {
		this.laborRelAnnexName = laborRelAnnexName;
	}

	@Transient
	public Integer getTitleLevelId() {
		return titleLevelId;
	}

	public void setTitleLevelId(Integer titleLevelId) {
		this.titleLevelId = titleLevelId;
	}
	@Column(name = "IF_SCIEN_TASK")
	public Integer getIfScienTask() {
		return ifScienTask;
	}

	public void setIfScienTask(Integer ifScienTask) {
		this.ifScienTask = ifScienTask;
	}

	@ManyToOne
	@JoinColumn(name = "BE_JOB_ID")
	public TsSimpleCode getFkByBeJobId() {
		return fkByBeJobId;
	}

	public void setFkByBeJobId(TsSimpleCode fkByBeJobId) {
		this.fkByBeJobId = fkByBeJobId;
	}

	@Transient
	public Integer getBeJobId() {
		return beJobId;
	}

	public void setBeJobId(Integer beJobId) {
		this.beJobId = beJobId;
	}

	@Transient
	public String getBeJobName() {
		return beJobName;
	}

	public void setBeJobName(String beJobName) {
		this.beJobName = beJobName;
	}

	@Transient
	public String getBeJobExtends1() {
		return beJobExtends1;
	}

	public void setBeJobExtends1(String beJobExtends1) {
		this.beJobExtends1 = beJobExtends1;
	}

	@Column(name = "BE_JOB_OTHER")
	public String getBeJobOther() {
		return beJobOther;
	}

	public void setBeJobOther(String beJobOther) {
		this.beJobOther = beJobOther;
	}

	@Column(name = "MASTER_DATA_TIME")
	@Temporal(TemporalType.TIMESTAMP)
	public Date getMasterDataTime() {
		return masterDataTime;
	}

	public void setMasterDataTime(Date masterDataTime) {
		this.masterDataTime = masterDataTime;
	}

}