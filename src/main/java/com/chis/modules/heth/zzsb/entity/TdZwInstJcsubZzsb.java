package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2016-5-12
 */
@Entity
@Table(name = "TD_ZW_INST_JCSUB")
@SequenceGenerator(name = "TdZwInstJcsub", sequenceName = "TD_ZW_INST_JCSUB_SEQ", allocationSize = 1)
public class TdZwInstJcsubZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwInstJcmainZzsb fkByMainId;
	private String descr;
	private String rst;
	private String rstUnit;
	private Date createDate = new Date();
	private Integer createManid;
	
	public TdZwInstJcsubZzsb() {
	}

	public TdZwInstJcsubZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwInstJcsub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwInstJcmainZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwInstJcmainZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "DESCR")	
	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}	
			
	@Column(name = "RST")	
	public String getRst() {
		return rst;
	}

	public void setRst(String rst) {
		this.rst = rst;
	}	
			
	@Column(name = "RST_UNIT")	
	public String getRstUnit() {
		return rstUnit;
	}

	public void setRstUnit(String rstUnit) {
		this.rstUnit = rstUnit;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}