package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_TJINST")
@SequenceGenerator(name = "TdZwTjinstSeq", sequenceName = "TD_ZW_TJINST_SEQ", allocationSize = 1)
public class TdZwTjinstZzsb implements Serializable,IZwInstZzsb{
    private static final long serialVersionUID = -4565680166721416529L;

    private Integer rid;
    private TdZwTjorginfoZzsb tdZwTjorginfo;
    private TdZwInstinfoZzsb tdZwInstinfo;
    private Date createDate;
    private Integer createManid;

    public TdZwTjinstZzsb() {
    }

    public TdZwTjinstZzsb(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjinstSeq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID" )
    public TdZwTjorginfoZzsb getTdZwTjorginfo() {
        return tdZwTjorginfo;
    }

    public void setTdZwTjorginfo(TdZwTjorginfoZzsb tdZwTjorginfo) {
        this.tdZwTjorginfo = tdZwTjorginfo;
    }

    @ManyToOne
    @JoinColumn(name = "INST_ID" )
    public TdZwInstinfoZzsb getTdZwInstinfo() {
        return tdZwInstinfo;
    }

    public void setTdZwInstinfo(TdZwInstinfoZzsb tdZwInstinfo) {
        this.tdZwInstinfo = tdZwInstinfo;
    }

    @Column(name = "CREATE_DATE" )
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Transient
    @Override
    public IZwOrginfoZzsb getZwOrginfo() {
        return tdZwTjorginfo;
    }

    @Override
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
        this.tdZwTjorginfo = (TdZwTjorginfoZzsb)zwOrginfo;
    }
}
