package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.google.common.collect.Lists;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_INSTINFO")
@SequenceGenerator(name = "TdZwInstinfoSeq", sequenceName = "TD_ZW_INSTINFO_SEQ", allocationSize = 1)
public class TdZwInstinfoZzsb implements java.io.Serializable {
	private static final long serialVersionUID = 1932072515990146650L;

	private Integer rid;
	private TsUnit tsUnit;
	private Short num;
	private String instName;
	private String instModel;
	private String custNo;
	private String prduNo;
	private Short outter;
	private Date createDate;
	private Integer createManid;
	
	/**0：非放射 1：放射*/
	private Integer instType;
	private TsSimpleCode fkByInstKindId;
	private TsSimpleCode fkByInstKindDetalId;
	private String instFactory;
	private Date instProDate;
	private String whereStore;
	private String instCode;
	private Date acptDate;
	private String acptConclu;
	
	private Date lastAcptDate;
	private Integer lastAcptCircle;
	private Integer ifNeedAcpt;
	private Integer ifSuptOutChk;
	
	private List<TdZwInstJcmainZzsb> instJcMainList = Lists.newArrayList();
	private List<TdZwInstSbjcmainZzsb> instSbjcmainList = Lists.newArrayList();
	
	/**是否被选中*/
    private Boolean selected = false;
	/**+购置日期20210315*/
	private Date purchaseDate;
	/**+检定要求ID20210315*/
	private TsSimpleCode acptReqId;
	/**+仪器状态ID20210315*/
	private TsSimpleCode instStateId;
	/** +校准/检定证书附件20220620*/
	private String checkAnnexName;
	private BigDecimal lastAcptCircleDot;

	public TdZwInstinfoZzsb() {
	}

	public TdZwInstinfoZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwInstinfoSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID")
	public TsUnit getTsUnit() {
		return this.tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}

	@Column(name = "NUM", precision = 4, scale = 0)
	public Short getNum() {
		return this.num;
	}

	public void setNum(Short num) {
		this.num = num;
	}

	@Column(name = "INST_NAME" , length = 100)
	public String getInstName() {
		return this.instName;
	}

	public void setInstName(String instName) {
		this.instName = instName;
	}

	@Column(name = "INST_MODEL", length = 50)
	public String getInstModel() {
		return this.instModel;
	}

	public void setInstModel(String instModel) {
		this.instModel = instModel;
	}

	@Column(name = "CUST_NO", length = 50)
	public String getCustNo() {
		return this.custNo;
	}

	public void setCustNo(String custNo) {
		this.custNo = custNo;
	}

	@Column(name = "PRDU_NO", length = 50)
	public String getPrduNo() {
		return this.prduNo;
	}

	public void setPrduNo(String prduNo) {
		this.prduNo = prduNo;
	}

	@Column(name = "OUTTER", precision = 1, scale = 0)
	public Short getOutter() {
		return this.outter;
	}

	public void setOutter(Short outter) {
		this.outter = outter;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	
	
	@Column(name = "INST_TYPE")	
	public Integer getInstType() {
		return instType;
	}

	public void setInstType(Integer instType) {
		this.instType = instType;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INST_KIND_ID")			
	public TsSimpleCode getFkByInstKindId() {
		return fkByInstKindId;
	}

	public void setFkByInstKindId(TsSimpleCode fkByInstKindId) {
		this.fkByInstKindId = fkByInstKindId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INST_KIND_DETAL_ID")			
	public TsSimpleCode getFkByInstKindDetalId() {
		return fkByInstKindDetalId;
	}

	public void setFkByInstKindDetalId(TsSimpleCode fkByInstKindDetalId) {
		this.fkByInstKindDetalId = fkByInstKindDetalId;
	}	
			
	@Column(name = "INST_FACTORY")	
	public String getInstFactory() {
		return instFactory;
	}

	public void setInstFactory(String instFactory) {
		this.instFactory = instFactory;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "INST_PRO_DATE")			
	public Date getInstProDate() {
		return instProDate;
	}

	public void setInstProDate(Date instProDate) {
		this.instProDate = instProDate;
	}	
			
	@Column(name = "WHERE_STORE")	
	public String getWhereStore() {
		return whereStore;
	}

	public void setWhereStore(String whereStore) {
		this.whereStore = whereStore;
	}	
			
	@Column(name = "INST_CODE")	
	public String getInstCode() {
		return instCode;
	}

	public void setInstCode(String instCode) {
		this.instCode = instCode;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "ACPT_DATE")			
	public Date getAcptDate() {
		return acptDate;
	}

	public void setAcptDate(Date acptDate) {
		this.acptDate = acptDate;
	}	
			
	@Column(name = "ACPT_CONCLU")	
	public String getAcptConclu() {
		return acptConclu;
	}

	public void setAcptConclu(String acptConclu) {
		this.acptConclu = acptConclu;
	}	
	
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByInstId", orphanRemoval = true)
	public List<TdZwInstJcmainZzsb> getInstJcMainList() {
		return instJcMainList;
	}

	public void setInstJcMainList(List<TdZwInstJcmainZzsb> instJcMainList) {
		this.instJcMainList = instJcMainList;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByInstId", orphanRemoval = true)
	public List<TdZwInstSbjcmainZzsb> getInstSbjcmainList() {
		return instSbjcmainList;
	}

	public void setInstSbjcmainList(List<TdZwInstSbjcmainZzsb> instSbjcmainList) {
		this.instSbjcmainList = instSbjcmainList;
	}

	@Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((rid == null) ? 0 : rid.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        TdZwInstinfoZzsb other = (TdZwInstinfoZzsb) obj;
        if (rid == null) {
            return false;
        } else if (!rid.equals(other.getRid()))
            return false;
        return true;
    }
    
    @Temporal(TemporalType.DATE)		
	@Column(name = "LAST_ACPT_DATE")			
	public Date getLastAcptDate() {
		return lastAcptDate;
	}

	public void setLastAcptDate(Date lastAcptDate) {
		this.lastAcptDate = lastAcptDate;
	}	
			
	@Column(name = "LAST_ACPT_CIRCLE")	
	public Integer getLastAcptCircle() {
		return lastAcptCircle;
	}

	public void setLastAcptCircle(Integer lastAcptCircle) {
		this.lastAcptCircle = lastAcptCircle;
	}

	@Column(name = "IF_NEED_ACPT")
	public Integer getIfNeedAcpt() {
		return ifNeedAcpt;
	}

	public void setIfNeedAcpt(Integer ifNeedAcpt) {
		this.ifNeedAcpt = ifNeedAcpt;
	}

	@Column(name = "IF_SUPT_OUT_CHK")
	public Integer getIfSuptOutChk() {
		return ifSuptOutChk;
	}

	public void setIfSuptOutChk(Integer ifSuptOutChk) {
		this.ifSuptOutChk = ifSuptOutChk;
	}

	@Transient
	public Boolean getSelected() {
		return selected;
	}

	public void setSelected(Boolean selected) {
		this.selected = selected;
	}

	@Column(name = "PURCHASE_DATE")
	public Date getPurchaseDate() {
		return purchaseDate;
	}

	public void setPurchaseDate(Date purchaseDate) {
		this.purchaseDate = purchaseDate;
	}

	@ManyToOne
	@JoinColumn(name = "ACPT_REQ_ID")
	public TsSimpleCode getAcptReqId() {
		return acptReqId;
	}

	public void setAcptReqId(TsSimpleCode acptReqId) {
		this.acptReqId = acptReqId;
	}

	@ManyToOne
	@JoinColumn(name = "INST_STATE_ID")
	public TsSimpleCode getInstStateId() {
		return instStateId;
	}

	public void setInstStateId(TsSimpleCode instStateId) {
		this.instStateId = instStateId;
	}

	@Column(name = "CHECK_ANNEX_NAME")
	public String getCheckAnnexName() {
		return checkAnnexName;
	}

	public void setCheckAnnexName(String checkAnnexName) {
		this.checkAnnexName = checkAnnexName;
	}

	@Column(name = "LAST_ACPT_CIRCLE_DOT")
	public BigDecimal getLastAcptCircleDot() {
		return lastAcptCircleDot;
	}

	public void setLastAcptCircleDot(BigDecimal lastAcptCircleDot) {
		this.lastAcptCircleDot = lastAcptCircleDot;
	}
}