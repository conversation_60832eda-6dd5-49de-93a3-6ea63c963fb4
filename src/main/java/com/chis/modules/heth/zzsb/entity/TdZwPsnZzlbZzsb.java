package com.chis.modules.heth.zzsb.entity;

import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import com.chis.modules.heth.zzsb.logic.TrainHisPO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.google.common.collect.Lists;

/**
 * 
 * <AUTHOR>
 * @createTime 2017-5-26
 */
@Entity
@Table(name = "TD_ZW_PSN_ZZLB")
@SequenceGenerator(name = "TdZwPsnZzlb", sequenceName = "TD_ZW_PSN_ZZLB_SEQ", allocationSize = 1)
public class TdZwPsnZzlbZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwPsninfoZzsb fkByPsnId;
	private TsSimpleCode fkByQualId;
	private String centNo;
	private Date sendDate;
	private Date validDate;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	private List<TdZwZzlbRangeZzsb> rangeList=Lists.newArrayList();
	/**培训履历*/
	private List<TrainHisPO> trainHisList=Lists.newArrayList();
	private String rangeStr;
	private String[] selectRanges;
	private Integer type;
	
	private Date ageinSendDate;
	private Date ageinValidDate;
	private String annexPath;
	private String annexName;

    private Integer zzFetchWay;
    /**是否显示，资质申报显示对应资质类别的资质信息*/
    private boolean show = true;
	/**是否显示培训履历*/
	private boolean ifShowTran=Boolean.FALSE;
	/**是否显示*/
	private boolean ifShowRange=Boolean.TRUE;
	
	public TdZwPsnZzlbZzsb() {
	}

	public TdZwPsnZzlbZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwPsnZzlb")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "PSN_ID")			
	public TdZwPsninfoZzsb getFkByPsnId() {
		return fkByPsnId;
	}

	public void setFkByPsnId(TdZwPsninfoZzsb fkByPsnId) {
		this.fkByPsnId = fkByPsnId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "QUAL_ID")			
	public TsSimpleCode getFkByQualId() {
		return fkByQualId;
	}

	public void setFkByQualId(TsSimpleCode fkByQualId) {
		this.fkByQualId = fkByQualId;
	}		
			
	@Column(name = "CENT_NO")	
	public String getCentNo() {
		return centNo;
	}

	public void setCentNo(String centNo) {
		this.centNo = centNo;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "SEND_DATE")			
	public Date getSendDate() {
		return sendDate;
	}

	public void setSendDate(Date sendDate) {
		this.sendDate = sendDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "VALID_DATE")			
	public Date getValidDate() {
		return validDate;
	}

	public void setValidDate(Date validDate) {
		this.validDate = validDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
	public List<TdZwZzlbRangeZzsb> getRangeList() {
		return rangeList;
	}

	public void setRangeList(List<TdZwZzlbRangeZzsb> rangeList) {
		this.rangeList = rangeList;
	}

	@Transient
	public String getRangeStr() {
		return rangeStr;
	}

	public void setRangeStr(String rangeStr) {
		this.rangeStr = rangeStr;
	}

	@Transient
	public String[] getSelectRanges() {
		return selectRanges;
	}

	public void setSelectRanges(String[] selectRanges) {
		this.selectRanges = selectRanges;
	}

	@Transient
	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "AGEIN_SEND_DATE")		
	public Date getAgeinSendDate() {
		return ageinSendDate;
	}

	public void setAgeinSendDate(Date ageinSendDate) {
		this.ageinSendDate = ageinSendDate;
	}

	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "AGEIN_VALID_DATE")	
	public Date getAgeinValidDate() {
		return ageinValidDate;
	}

	public void setAgeinValidDate(Date ageinValidDate) {
		this.ageinValidDate = ageinValidDate;
	}
	@Column(name="ANNEX_PATH")
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}
	@Column(name="ANNEX_NAME")
	public String getAnnexName() {
		return annexName;
	}

	public void setAnnexName(String annexName) {
		this.annexName = annexName;
	}

    @Column(name = "ZZ_FETCH_WAY")
    public Integer getZzFetchWay() {
        return zzFetchWay;
    }

    public void setZzFetchWay(Integer zzFetchWay) {
        this.zzFetchWay = zzFetchWay;
    }

	@Transient
	public boolean isShow() {
		return show;
	}

	public void setShow(boolean show) {
		this.show = show;
	}

	@Transient
	public List<TrainHisPO> getTrainHisList() {
		return trainHisList;
	}

	public void setTrainHisList(List<TrainHisPO> trainHisList) {
		this.trainHisList = trainHisList;
	}
	@Transient
	public boolean isIfShowTran() {
		return ifShowTran;
	}

	public void setIfShowTran(boolean ifShowTran) {
		this.ifShowTran = ifShowTran;
	}
	@Transient
	public boolean isIfShowRange() {
		return ifShowRange;
	}

	public void setIfShowRange(boolean ifShowRange) {
		this.ifShowRange = ifShowRange;
	}
}