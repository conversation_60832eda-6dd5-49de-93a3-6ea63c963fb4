package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_TJORGGITEMS")
@SequenceGenerator(name = "TdZwTjorggitemsSeq", sequenceName = "TD_ZW_TJORGGITEMS_SEQ", allocationSize = 1)
public class TdZwTjorggitemsZzsb implements Serializable,IZwSvritmZzsb {
	private static final long serialVersionUID = 4113037228007770838L;

	private Integer rid;
	private TdZwTjorginfoZzsb tdZwTjorginfo;
	private Short itemCode;
	private Date createDate;
	private Integer createManid;
	private List<TdZwTjorgItmDetail> tjorgItmDetails = new LinkedList<TdZwTjorgItmDetail>();

	public TdZwTjorggitemsZzsb() {
	}

	public TdZwTjorggitemsZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorggitemsSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwTjorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwTjorginfo = (TdZwTjorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwTjorginfoZzsb getTdZwTjorginfo() {
		return this.tdZwTjorginfo;
	}

	public void setTdZwTjorginfo(TdZwTjorginfoZzsb tdZwTjorginfo) {
		this.tdZwTjorginfo = tdZwTjorginfo;
	}

	@Column(name = "ITEM_CODE" , precision = 1, scale = 0)
	public Short getItemCode() {
		return this.itemCode;
	}

	public void setItemCode(Short itemCode) {
		this.itemCode = itemCode;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	@Override
	public List<IZwSvritmSubZzsb> getZwSvritmSubList() {
		if(null != this.tjorgItmDetails && this.tjorgItmDetails.size() > 0) {
			List<IZwSvritmSubZzsb> list = new ArrayList<IZwSvritmSubZzsb>();
			for(IZwSvritmSubZzsb t : this.tjorgItmDetails) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwSvritmSubList(List<IZwSvritmSubZzsb> zwSvritmSubList) {
		if(null != zwSvritmSubList && zwSvritmSubList.size() > 0) {
			this.tjorgItmDetails = new ArrayList<TdZwTjorgItmDetail>();
			for(IZwSvritmSubZzsb t : zwSvritmSubList) {
				this.tjorgItmDetails.add((TdZwTjorgItmDetail) t);
			}
		}else if(null == zwSvritmSubList) {
			this.tjorgItmDetails = null;
		}else {
			this.tjorgItmDetails = new ArrayList<TdZwTjorgItmDetail>(0);
		}
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId",orphanRemoval = true)
	public List<TdZwTjorgItmDetail> getSrvorgitemsSubs() {
		return tjorgItmDetails;
	}

	public void setSrvorgitemsSubs(List<TdZwTjorgItmDetail> tjorgItmDetails) {
		this.tjorgItmDetails = tjorgItmDetails;
	}

}