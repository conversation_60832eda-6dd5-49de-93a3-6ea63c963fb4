package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-3-29
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_POSITIVE")
@SequenceGenerator(name = "TdZwSupportPositive", sequenceName = "TD_ZW_SUPPORT_POSITIVE_SEQ", allocationSize = 1)
public class TdZwSupportPositive implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwSupportOrgZzsb fkByMainId;
    private Integer positiveType;
    private Integer year;
    private String patentName;
    private TsSimpleCode fkByPatentId;
    private String otherPatentType;
    private String patentNo;
    private TsSimpleCode fkByStandardId;
    private String supportFilePath;
    private Date createDate;
    private Integer createManid;
    private Integer modifyManid;
    private Date modifyDate;
    private String psnName;
    private SupportResearchPsnSelVO psnSelVO;
    private List<TdZwSupportPositivePsns> positivePsnList;

    public TdZwSupportPositive() {
    }

    public TdZwSupportPositive(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportPositive")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwSupportOrgZzsb getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @Column(name = "POSITIVE_TYPE")
    public Integer getPositiveType() {
        return positiveType;
    }

    public void setPositiveType(Integer positiveType) {
        this.positiveType = positiveType;
    }

    @Column(name = "YEAR")
    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    @Column(name = "PATENT_NAME")
    public String getPatentName() {
        return patentName;
    }

    public void setPatentName(String patentName) {
        this.patentName = patentName;
    }

    @ManyToOne
    @JoinColumn(name = "PATENT_ID")
    public TsSimpleCode getFkByPatentId() {
        return fkByPatentId;
    }

    public void setFkByPatentId(TsSimpleCode fkByPatentId) {
        this.fkByPatentId = fkByPatentId;
    }

    @Column(name = "OTHER_PATENT_TYPE")
    public String getOtherPatentType() {
        return otherPatentType;
    }

    public void setOtherPatentType(String otherPatentType) {
        this.otherPatentType = otherPatentType;
    }

    @Column(name = "PATENT_NO")
    public String getPatentNo() {
        return patentNo;
    }

    public void setPatentNo(String patentNo) {
        this.patentNo = patentNo;
    }

    @ManyToOne
    @JoinColumn(name = "STANDARD_ID")
    public TsSimpleCode getFkByStandardId() {
        return fkByStandardId;
    }

    public void setFkByStandardId(TsSimpleCode fkByStandardId) {
        this.fkByStandardId = fkByStandardId;
    }

    @Column(name = "SUPPORT_FILE_PATH")
    public String getSupportFilePath() {
        return supportFilePath;
    }

    public void setSupportFilePath(String supportFilePath) {
        this.supportFilePath = supportFilePath;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Transient
    public String getPsnName() {
        return psnName;
    }

    public void setPsnName(String psnName) {
        this.psnName = psnName;
    }

    @Transient
    public SupportResearchPsnSelVO getPsnSelVO() {
        return psnSelVO;
    }

    public void setPsnSelVO(SupportResearchPsnSelVO psnSelVO) {
        this.psnSelVO = psnSelVO;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwSupportPositivePsns> getPositivePsnList() {
        return positivePsnList;
    }

    public void setPositivePsnList(List<TdZwSupportPositivePsns> positivePsnList) {
        this.positivePsnList = positivePsnList;
    }
}