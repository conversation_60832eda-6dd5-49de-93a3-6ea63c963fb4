package com.chis.modules.heth.zzsb.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述： </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年04月15日
 **/
@Service
@Transactional(readOnly = false)
public class ZwQualAgencyFilingAppService extends AbstractTemplate {

    /**
     * @Description: 通过所属机构ID以及机构资质 获取状态为待提交、待初审、待终审状态的备案主表数量
     *
     * @MethodAuthor pw,2022年04月28日
     */
    public int findCountInApplying(Integer unitRid,String ext1){
        StringBuilder builder=new StringBuilder();
        builder.append(" SELECT COUNT(1) FROM TD_ZW_TJORGINFO_APPLY T ")
                .append(" INNER JOIN TS_SIMPLE_CODE T1 ON T.QUAL_TYPE_ID=T1.RID ")
                .append(" WHERE T.ORG_ID=").append(unitRid).append(" AND T.STATE IN (0,1,3) ")
                .append(" AND T1.EXTENDS1='").append(ext1).append("' ");
        return this.findCountBySql(builder.toString(), null);
    }

    /**
     * <p>方法描述：Hql语句查询资质机构备案表</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-15
     **/
    public TdZwTjorginfoApply findQualOrgByHql(Integer qualTypeRid, Integer applyTypeRid, Integer unitId,boolean bool){
        StringBuilder hql=new StringBuilder();
        hql.append(" select T from TdZwTjorginfoApply T ");
        hql.append(" where T.fkByOrgId.rid=").append(unitId);
        hql.append(" and T.fkByQualTypeId.rid=").append(qualTypeRid);
        if(bool){
            hql.append(" and T.fkByApplyTypeId.extendS1=1");
        }else{
            hql.append(" and T.fkByApplyTypeId.rid=").append(applyTypeRid);
        }
        hql.append(" order by T.createDate desc ");
        return this.findOneByHql(hql.toString(), TdZwTjorginfoApply.class);
    }

    /**
     * <p>方法描述：获取资质基础信息表记录</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-15
     **/
    public List<Object[]> findOrgInfoStateByOrgId(String flag,Integer unitRid){
        StringBuilder sql=new StringBuilder();
        sql.append(" select rid,STATE from ");
        if("1".equals(flag)){
            sql.append(" TD_ZW_TJORGINFO ");
        }else if("2".equals(flag)){
            sql.append(" TD_ZW_DIAGORGINFO ");
        }
        sql.append(" where ORG_ID=").append(unitRid);
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * <p>方法描述：保存待提交状态的资质机构备案信息-初次备案</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-15
     **/
    public TdZwTjorginfoApply saveQualOrgInfo(Integer qualTypeRid, Integer applyTypeRid){
        TdZwTjorginfoApply qualOrgApply=new TdZwTjorginfoApply();
        qualOrgApply.setFkByOrgId(Global.getUser().getTsUnit());
        qualOrgApply.setFkByZoneId(Global.getUser().getTsUnit().getTsZone());
        qualOrgApply.setOrgName(Global.getUser().getTsUnit().getUnitname());
        qualOrgApply.setFkByQualTypeId(new TsSimpleCode(qualTypeRid));
        qualOrgApply.setFkByApplyTypeId(new TsSimpleCode(applyTypeRid));
        qualOrgApply.setState(0);
        qualOrgApply.setCreateDate(new Date());
        qualOrgApply.setCreateManid(Global.getUser().getRid());
        qualOrgApply.setOrgAddr(Global.getUser().getTsUnit().getUnitaddr());
        qualOrgApply.setOrgFz(Global.getUser().getTsUnit().getOrgFz());
        qualOrgApply.setOrgFzzw(Global.getUser().getTsUnit().getOrgFzzw());
        qualOrgApply.setLinkMan(Global.getUser().getTsUnit().getLinkMan());
        qualOrgApply.setLinkTel(Global.getUser().getTsUnit().getUnittel());
        qualOrgApply.setLinkMb(Global.getUser().getTsUnit().getOrgTel());
        qualOrgApply.setFax(Global.getUser().getTsUnit().getUnitfax());
        qualOrgApply.setZipcode(Global.getUser().getTsUnit().getUnitzip());
        qualOrgApply.setEmail(Global.getUser().getTsUnit().getUnitemail());
        return (TdZwTjorginfoApply)this.saveObj(qualOrgApply);
    }
    /**
     * <p>方法描述：保存待提交状态的资质机构备案信息-变更申请</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-15
     **/
    public TdZwTjorginfoApply saveQualOrgChange(Integer qualTypeRid, Integer applyTypeRid,String flag){
        List<Object> saveObjList = new ArrayList<>();
        TdZwTjorginfoApply qualOrgApply=new TdZwTjorginfoApply();
        qualOrgApply.setFkByOrgId(Global.getUser().getTsUnit());
        qualOrgApply.setFkByZoneId(Global.getUser().getTsUnit().getTsZone());
        qualOrgApply.setFkByQualTypeId(new TsSimpleCode(qualTypeRid));
        qualOrgApply.setFkByApplyTypeId(new TsSimpleCode(applyTypeRid));
        qualOrgApply.setState(0);
        preInsert(qualOrgApply);

        //人员信息 从事科室
        List<TdZwTjorgpsnsApply> psnApplyList = new ArrayList<>();
        List<TdZwWorkdeptApply> workdeptApplyList = new ArrayList<>();
        //外检车辆 外检车辆仪器
        List<TdZwTjorgOutcarApply> outcarApplyList = new ArrayList<>();
        List<TdZwTjorgOutcInstApy> outcInstApyList = new ArrayList<>();
        //仪器信息
        List<TdZwTjinstApply> instApplyList = new ArrayList<>();
        //服务项目 服务项目明细
        List<TdZwTjorggitemsApply> orgItemApplyList = new ArrayList<>();
        List<TdZwTjorgItmDetailApy> itmDetailApyList = new ArrayList<>();
        //检查项目开展情况
        List<TdZwTjitemsApply>  itemApplyList = new ArrayList<>();
        //质量管理体系附件
        List<TdZwTjorgQualAnnexApy> qualAnnexApyList = new ArrayList<>();
        //资质附件
        List<TdZwTjorgannexApply> annexApplyList = new ArrayList<>();
        StringBuilder hql=new StringBuilder();
        Integer orgRid = null;
        Integer unitId = null;
        //1 健康检查机构 2诊断机构
        int type = 1;
        if("1".equals(flag)){
            hql.append(" select T from  TdZwTjorginfoZzsb T where T.tsUnit.rid=").append(Global.getUser().getTsUnit().getRid());
            TdZwTjorginfoZzsb tjorginfoComm= this.findOneByHql(hql.toString(), TdZwTjorginfoZzsb.class);
            if(tjorginfoComm!=null){
                qualOrgApply.setOrgName(tjorginfoComm.getOrgName());
                qualOrgApply.setOrgAddr(tjorginfoComm.getOrgAddr());
                qualOrgApply.setOrgFz(tjorginfoComm.getOrgFz());
                qualOrgApply.setOrgFzzw(tjorginfoComm.getOrgFzzw());
                qualOrgApply.setLinkMan(tjorginfoComm.getLinkMan());
                qualOrgApply.setLinkMb(tjorginfoComm.getLinkMb());
                qualOrgApply.setLinkTel(tjorginfoComm.getLinkTel());
                qualOrgApply.setFax(tjorginfoComm.getFax());
                qualOrgApply.setZipcode(tjorginfoComm.getZipcode());
                qualOrgApply.setEmail(tjorginfoComm.getEmail());
                qualOrgApply.setCertNo(tjorginfoComm.getCertNo());
                qualOrgApply.setFirstGetday(tjorginfoComm.getFirstGetday());
                qualOrgApply.setFilingDate(tjorginfoComm.getFilingDate());
                //外出开展职业健康检查能力
                qualOrgApply.setOutWorkPower(tjorginfoComm.getOutWorkPower());
                orgRid = tjorginfoComm.getRid();
                unitId = null == tjorginfoComm.getTsUnit() ? null : tjorginfoComm.getTsUnit().getRid();
                this.fillOutCarAndOutCarInst(outcarApplyList,outcInstApyList,
                        qualOrgApply, orgRid);
                this.fillApplyItem(itemApplyList, qualOrgApply, orgRid);
                this.fillQualAnnexApy(qualAnnexApyList, qualOrgApply, orgRid);
            }
        }else if("2".equals(flag)){
            hql.append(" select T from  TdZwDiagorginfoZzsb T where T.tsUnit.rid=").append(Global.getUser().getTsUnit().getRid());
            TdZwDiagorginfoZzsb diagorginfoZzsb= this.findOneByHql(hql.toString(), TdZwDiagorginfoZzsb.class);
            if(diagorginfoZzsb!=null){
                qualOrgApply.setOrgName(diagorginfoZzsb.getOrgName());
                qualOrgApply.setOrgAddr(diagorginfoZzsb.getOrgAddr());
                qualOrgApply.setOrgFz(diagorginfoZzsb.getOrgFz());
                qualOrgApply.setOrgFzzw(diagorginfoZzsb.getOrgFzzw());
                qualOrgApply.setLinkMan(diagorginfoZzsb.getLinkMan());
                qualOrgApply.setLinkMb(diagorginfoZzsb.getLinkMb());
                qualOrgApply.setLinkTel(diagorginfoZzsb.getLinkTel());
                qualOrgApply.setFax(diagorginfoZzsb.getFax());
                qualOrgApply.setZipcode(diagorginfoZzsb.getZipcode());
                qualOrgApply.setEmail(diagorginfoZzsb.getEmail());
                qualOrgApply.setCertNo(diagorginfoZzsb.getCertNo());
                qualOrgApply.setFirstGetday(diagorginfoZzsb.getFirstGetday());
                qualOrgApply.setFilingDate(diagorginfoZzsb.getFilingDate());
                orgRid = diagorginfoZzsb.getRid();
                unitId = null == diagorginfoZzsb.getTsUnit() ? null : diagorginfoZzsb.getTsUnit().getRid();
                type = 2;
            }
        }
        if(null != orgRid){
            this.fillPsnAndWorkDept(psnApplyList, workdeptApplyList, qualOrgApply, orgRid, type);
            this.fillApplyInst(instApplyList, qualOrgApply, orgRid, type);
            this.fillOrgItemAndDetail(orgItemApplyList, itmDetailApyList, qualOrgApply, orgRid, type);
        }
        if(null != unitId){
            this.fillAnnexApply(annexApplyList, qualOrgApply, unitId, type);
        }
        saveObjList.add(qualOrgApply);
        if(!CollectionUtils.isEmpty(psnApplyList)){
            saveObjList.addAll(psnApplyList);
        }
        if(!CollectionUtils.isEmpty(workdeptApplyList)){
            saveObjList.addAll(workdeptApplyList);
        }
        if(!CollectionUtils.isEmpty(outcarApplyList)){
            saveObjList.addAll(outcarApplyList);
        }
        if(!CollectionUtils.isEmpty(outcInstApyList)){
            saveObjList.addAll(outcInstApyList);
        }
        if(!CollectionUtils.isEmpty(instApplyList)){
            saveObjList.addAll(instApplyList);
        }
        if(!CollectionUtils.isEmpty(orgItemApplyList)){
            saveObjList.addAll(orgItemApplyList);
        }
        if(!CollectionUtils.isEmpty(itmDetailApyList)){
            saveObjList.addAll(itmDetailApyList);
        }
        if(!CollectionUtils.isEmpty(itemApplyList)){
            saveObjList.addAll(itemApplyList);
        }
        if(!CollectionUtils.isEmpty(qualAnnexApyList)){
            saveObjList.addAll(qualAnnexApyList);
        }
        if(!CollectionUtils.isEmpty(annexApplyList)){
            saveObjList.addAll(annexApplyList);
        }
        this.saveBatchObjs(saveObjList);
        return qualOrgApply;
    }

    /**
     * @Description: 填充资质附件
     *
     * @MethodAuthor pw,2022年04月22日
     */
    @Transactional(readOnly = true)
    private void fillAnnexApply(List<TdZwTjorgannexApply> annexApplyList, TdZwTjorginfoApply qualOrgApply,
                                Integer fatherId, int type){
        if(null == fatherId){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.ANNEX_TYPE,T.FILE_NAME,T.FILE_PATH,T.REG_ZONE_ID,T.UNIT_LEVEL,T.UNIT_NAME FROM ")
                .append(" TD_ZW_JSFFJG_COMM_ANNEX ")
                .append(" T WHERE T.ORG_ID= ").append(fatherId);
        sqlBuffer.append(" AND T.ADD_SOURCE=").append(1 == type ? 0 : 2);
        //诊断机构的 同步1、2、3、7 不需要申请材料 注意 职业健康检查以及诊断机构 备案回执不需要同步
        sqlBuffer.append(" AND T.ANNEX_TYPE IN(").append(1 == type ? "1,2,3,20" : "1,2,3,7").append(") ");
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return;
        }
        for(Object[] objArr : queryResultList){
            Integer annexType = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == annexType){
                continue;
            }
            String fileName = null == objArr[1] ? null : objArr[1].toString();
            String filePath = null == objArr[2] ? null : objArr[2].toString();
            Integer regZoneId = null == objArr[3] ? null : Integer.parseInt(objArr[3].toString());
            Integer unitLevel = null == objArr[4] ? null : Integer.parseInt(objArr[4].toString());
            String unitName = null == objArr[5] ? null : objArr[5].toString();
            TdZwTjorgannexApply annexApply = new TdZwTjorgannexApply();
            annexApply.setFkByOrgId(qualOrgApply);
            annexApply.setAnnexType(annexType);
            annexApply.setFileName(fileName);
            annexApply.setFilePath(filePath);
            annexApply.setRegZoneId(regZoneId);
            annexApply.setUnitLevel(unitLevel);
            annexApply.setUnitName(unitName);
            preInsert(annexApply);
            annexApplyList.add(annexApply);
        }
    }

    /**
     * @Description: 填充质量管理体系附件
     *
     * @MethodAuthor pw,2022年04月22日
     */
    @Transactional(readOnly = true)
    private void fillQualAnnexApy(List<TdZwTjorgQualAnnexApy> qualAnnexApyList,TdZwTjorginfoApply qualOrgApply,
                                  Integer fatherId){
        if(null == fatherId){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.RULE_ID,T.OTHER_RULE_NAME,T.IF_HAS,T.ANNEX_PATH FROM TD_ZW_TJORG_QUAL_ANNEX T ")
                .append(" WHERE T.MAIN_ID = ").append(fatherId);
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return;
        }
        for(Object[] objArr : queryResultList){
            Integer ruleId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == ruleId){
                continue;
            }
            String otRule = null == objArr[1] ? null : objArr[1].toString();
            Integer ifHas = null == objArr[2] ? 0 : Integer.parseInt(objArr[2].toString());
            String annexPath = null == objArr[3] ? null : objArr[3].toString();
            TdZwTjorgQualAnnexApy annexApy = new TdZwTjorgQualAnnexApy();
            annexApy.setFkByMainId(qualOrgApply);
            annexApy.setFkByRuleId(new TsSimpleCode(ruleId));
            annexApy.setOtherRuleName(otRule);
            annexApy.setIfHas(ifHas);
            annexApy.setAnnexPath(annexPath);
            preInsert(annexApy);
            qualAnnexApyList.add(annexApy);
        }
    }

    /**
     * @Description: 填充检查项目开展情况
     *
     * @MethodAuthor pw,2022年04月22日
     */
    @Transactional(readOnly = true)
    private void fillApplyItem(List<TdZwTjitemsApply>  itemApplyList,TdZwTjorginfoApply qualOrgApply, Integer fatherId){
        if(null == fatherId){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.ITEM_ID,T.IS_OUTTER FROM TD_ZW_TJITEMS T WHERE T.ORG_ID = ").append(fatherId);
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return;
        }
        for(Object[] objArr : queryResultList){
            Integer itemId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == itemId){
                continue;
            }
            Integer outter = null == objArr[1] ? 0 : Integer.parseInt(objArr[1].toString());
            TdZwTjitemsApply itmApply = new TdZwTjitemsApply();
            itmApply.setFkByOrgId(qualOrgApply);
            itmApply.setFkByItemId(new TsSimpleCode(itemId));
            itmApply.setIsOutter(outter);
            preInsert(itmApply);
            itemApplyList.add(itmApply);
        }
    }

    /**
     * @Description: 填充备案服务项目 服务项目明细
     *
     * @MethodAuthor pw,2022年04月22日
     */
    @Transactional(readOnly = true)
    private void fillOrgItemAndDetail(List<TdZwTjorggitemsApply> orgItemApplyList,
                                      List<TdZwTjorgItmDetailApy> itmDetailApyList,
                                      TdZwTjorginfoApply qualOrgApply, Integer fatherId, int type){
        if(null == fatherId){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.RID,T1.RID AS SIMPLERID FROM ")
                .append(1 == type ? " TD_ZW_TJORGGITEMS " : " TD_ZW_DIAGITEMS ")
                .append(" T ")
                .append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T1.CODE_NO=T.ITEM_CODE ")
                .append(" INNER JOIN TS_CODE_TYPE T2 ON T1.CODE_TYPE_ID = T2.RID ")
                .append(" WHERE T.ORG_ID= ").append(fatherId)
                .append(" AND T2.CODE_TYPE_NAME='").append(1 == type ? "5018" : "5020").append("' ");
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return;
        }
        //key 机构服务项目rid
        Map<Integer,TdZwTjorggitemsApply> itemApplyMap = new HashMap<>();
        List<Integer> itemRidList = new ArrayList<>();
        for(Object[] objArr : queryResultList){
            Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            Integer simpleCodeRid = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
            if(null == rid || null == simpleCodeRid){
                continue;
            }
            TdZwTjorggitemsApply itemApply = new TdZwTjorggitemsApply();
            itemApply.setFkByItemId(new TsSimpleCode(simpleCodeRid));
            itemApply.setFkByOrgId(qualOrgApply);
            preInsert(itemApply);
            orgItemApplyList.add(itemApply);
            itemRidList.add(rid);
            itemApplyMap.put(rid, itemApply);
        }

        if(1 != type || CollectionUtils.isEmpty(itemRidList) || CollectionUtils.isEmpty(itemApplyMap)){
            return;
        }
        //服务项目明细
        sqlBuffer.setLength(0);
        sqlBuffer.append(" SELECT T.MAIN_ID,T.ITEM_DETAIL_ID,T.IF_EXTER_INSPECT FROM TD_ZW_TJORG_ITM_DETAIL T ")
                .append(" WHERE T.MAIN_ID IN (:mainRidList) ");
        Map<String,Object> paramMap = new HashMap<>();
        for(List<Integer> mainRidList : StringUtils.splitListProxy(itemRidList, 1000)){
            paramMap.put("mainRidList", mainRidList);
            List<Object[]> tmpList = this.findDataBySqlNoPage(sqlBuffer.toString(), paramMap);
            if(CollectionUtils.isEmpty(tmpList)){
                continue;
            }
            for(Object[] objArr : tmpList){
                Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                Integer itmDetailId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                if(null == rid || null == itmDetailId){
                    continue;
                }
                TdZwTjorggitemsApply itemApply = itemApplyMap.get(rid);
                if(null == itemApply){
                    continue;
                }
                Integer ifExter = null == objArr[2] ? 0 : Integer.parseInt(objArr[2].toString());
                TdZwTjorgItmDetailApy itmDetailApy = new TdZwTjorgItmDetailApy();
                itmDetailApy.setFkByMainId(itemApply);
                itmDetailApy.setFkByItemDetailId(new TsSimpleCode(itmDetailId));
                itmDetailApy.setIfExterInspect(ifExter);
                preInsert(itmDetailApy);
                itmDetailApyList.add(itmDetailApy);
            }
        }
    }

    /**
     * @Description: 填充备案仪器信息
     *
     * @MethodAuthor pw,2022年04月22日
     */
    @Transactional(readOnly = true)
    private void fillApplyInst(List<TdZwTjinstApply> instApplyList,TdZwTjorginfoApply qualOrgApply,Integer fatherId,
                               int type){
        if(null == fatherId){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT DISTINCT T.INST_ID FROM ")
                .append(1 == type?" TD_ZW_TJINST ":" TD_ZW_DIAGINST ")
                .append(" T WHERE T.ORG_ID = ")
                .append(fatherId);
        List<Object> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return;
        }
        for(Object obj : queryResultList){
            Integer instId = null == obj ? null : Integer.parseInt(obj.toString());
            if(null == instId){
                continue;
            }
            TdZwTjinstApply instApply = new TdZwTjinstApply();
            instApply.setFkByOrgId(qualOrgApply);
            instApply.setFkByInstId(new TdZwInstinfoZzsb(instId));
            preInsert(instApply);
            instApplyList.add(instApply);
        }
    }

    /**
     * @Description: 职业健康检查机构填充外检车辆 外检车辆仪器
     *
     * @MethodAuthor pw,2022年04月22日
     */
    @Transactional(readOnly = true)
    private void fillOutCarAndOutCarInst(List<TdZwTjorgOutcarApply> outcarApplyList,
                                         List<TdZwTjorgOutcInstApy> outcInstApyList,
                                         TdZwTjorginfoApply qualOrgApply,
                                         Integer fatherId){
        if(null == fatherId){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT ")
                .append(" T.RID,T.CAR_NO,T.CAR_BAND,T.CAR_STYLE,T.CAR_FRAME_NO,T.PRODUCTION_DATE,T.STATE_MARK ")
                .append(" ,T.DRIVING_ANNEX_PATH,T.CAR_PHOTO_ANNEX_PATH,T.CAR_DR_ANNEX_PATH ")
                .append(" FROM TD_ZW_TJORG_OUTCAR T ").append(" WHERE T.MAIN_ID= ").append(fatherId);
        List<Object[]> queryResultList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        if(CollectionUtils.isEmpty(queryResultList)){
            return;
        }
        //key 机构外检车辆rid value 备案外检车辆
        Map<Integer,TdZwTjorgOutcarApply> carApplyMap = new HashMap<>();
        List<Integer> outCarRidList = new ArrayList<>();
        for(Object[] objArr : queryResultList){
            Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            if(null == rid){
                continue;
            }
            String carNo = null == objArr[1] ? null : objArr[1].toString();
            String carBand = null == objArr[2] ? null : objArr[2].toString();
            String carStyle = null == objArr[3] ? null : objArr[3].toString();
            String carFrameNo = null == objArr[4] ? null : objArr[4].toString();
            Date productionDate = null == objArr[5] ? null : (Date) objArr[5];
            Integer stateMark = null == objArr[6] ? null : Integer.parseInt(objArr[6].toString());
            String drivingAnnexPath = StringUtils.objectToString(objArr[7]);
            String carPhotoAnnexPath = StringUtils.objectToString(objArr[8]);
            String carDrAnnexPath = StringUtils.objectToString(objArr[9]);
            TdZwTjorgOutcarApply outcarApply = new TdZwTjorgOutcarApply();
            outcarApply.setCarNo(carNo);
            outcarApply.setCarBand(carBand);
            outcarApply.setCarStyle(carStyle);
            outcarApply.setCarFrameNo(carFrameNo);
            outcarApply.setProductionDate(productionDate);
            outcarApply.setStateMark(stateMark);
            outcarApply.setFkByMainId(qualOrgApply);
            outcarApply.setDrivingAnnexPath(drivingAnnexPath);
            outcarApply.setCarPhotoAnnexPath(carPhotoAnnexPath);
            outcarApply.setCarDrAnnexPath(carDrAnnexPath);
            preInsert(outcarApply);
            outcarApplyList.add(outcarApply);
            outCarRidList.add(rid);
            carApplyMap.put(rid, outcarApply);
        }
        sqlBuffer.setLength(0);
        sqlBuffer.append(" SELECT T.CAR_ID,T.INST_ID FROM TD_ZW_TJORG_OUTC_INST T WHERE T.CAR_ID IN (:carRidList) ");
        Map<String,Object> paramMap = new HashMap<>();
        for(List<Integer> carRidList : StringUtils.splitListProxy(outCarRidList, 1000)){
            paramMap.put("carRidList", carRidList);
            List<Object[]> tmpList = this.findDataBySqlNoPage(sqlBuffer.toString(), paramMap);
            if(CollectionUtils.isEmpty(tmpList)){
                continue;
            }
            for(Object[] objArr : tmpList){
                Integer carId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                Integer instId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                if(null == carId || null == instId){
                    continue;
                }
                TdZwTjorgOutcarApply outcarApply = carApplyMap.get(carId);
                if(null == outcarApply){
                    continue;
                }
                TdZwTjorgOutcInstApy outcInstApy = new TdZwTjorgOutcInstApy();
                outcInstApy.setFkByCarId(outcarApply);
                outcInstApy.setFkByInstId(new TdZwInstinfoZzsb(instId));
                preInsert(outcInstApy);
                outcInstApyList.add(outcInstApy);
            }
        }
    }
    /**
     * @Description: 在职人员信息 从事科室填充
     *
     * @MethodAuthor pw,2022年04月22日
     */
    @Transactional(readOnly = true)
    private void fillPsnAndWorkDept(List<TdZwTjorgpsnsApply> psnApplyList, List<TdZwWorkdeptApply> workdeptApplyList,
                                    TdZwTjorginfoApply qualOrgApply, Integer fatherId,int type){
        if(null == fatherId){
            return;
        }
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.RID,T.EMP_ID FROM ")
                .append(1 == type ? " TD_ZW_TJORGPSNS " : " TD_ZW_DIAGPSNS ")
                .append(" T WHERE T.ON_DUTY=1 AND T.ORG_ID=")
                .append(fatherId);
        List<Object[]> empIdObjList = this.findDataBySqlNoPage(sqlBuffer.toString(), null);
        List<Integer> psnRidList = new ArrayList<>();
        //key 资质机构人员情况rid value 人员ID
        Map<Integer,Integer> psnEmpRidMap = new HashMap<>();
        for(Object[] obj : empIdObjList){
            Integer rid = null == obj[0] ? null : Integer.parseInt(obj[0].toString());
            Integer empId = null == obj[1] ? null : Integer.parseInt(obj[1].toString());
            if(null == rid || null == empId){
                continue;
            }
            psnRidList.add(rid);
            psnEmpRidMap.put(rid, empId);
        }
        if(CollectionUtils.isEmpty(psnRidList) || CollectionUtils.isEmpty(psnEmpRidMap)){
            return;
        }
        //key 资质机构人员情况rid value 科室rid集合
        Map<Integer,List<Integer>> psnDeptMap = new HashMap<>();
        if(1 == type){
            sqlBuffer.setLength(0);
            sqlBuffer.append(" SELECT T.EMP_ID,T.DEPT_ID FROM TD_ZW_WORKDEPT T WHERE T.EMP_ID IN (:psnRidList) ");
            List<Object[]> allDeptResultList = new ArrayList<>();
            List<List<Integer>> psnRidGroupList = StringUtils.splitListProxy(psnRidList, 1000);
            Map<String,Object> paramMap = new HashMap<>();
            for(List<Integer> ridList : psnRidGroupList){
                paramMap.put("psnRidList", ridList);
                List<Object[]> tmpList = this.findDataBySqlNoPage(sqlBuffer.toString(), paramMap);
                if(!CollectionUtils.isEmpty(tmpList)){
                    allDeptResultList.addAll(tmpList);
                }
            }
            if(!CollectionUtils.isEmpty(allDeptResultList)){
                for(Object[] objArr : allDeptResultList){
                    Integer psnId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                    Integer deptId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                    if(null == psnId || null == deptId){
                        continue;
                    }
                    List<Integer> deptList = psnDeptMap.get(psnId);
                    if(null == deptList){
                        deptList = new ArrayList<>();
                    }
                    if(!deptList.contains(deptId)){
                        deptList.add(deptId);
                    }
                    psnDeptMap.put(psnId, deptList);
                }
            }
        }

        for(Map.Entry<Integer,Integer> mapEntity : psnEmpRidMap.entrySet()){
            Integer rid = mapEntity.getKey();
            Integer empRid = mapEntity.getValue();
            if(null == rid || null == empRid){
                continue;
            }
            List<Integer> deptIdList = psnDeptMap.get(rid);
            TdZwTjorgpsnsApply psnApply = new TdZwTjorgpsnsApply();
            psnApply.setFkByOrgId(qualOrgApply);
            psnApply.setFkByEmpId(new TdZwPsninfoZzsb(empRid));
            psnApplyList.add(psnApply);
            if(!CollectionUtils.isEmpty(deptIdList)){
                for(Integer deptId : deptIdList){
                    TdZwWorkdeptApply deptApply = new TdZwWorkdeptApply();
                    deptApply.setFkByEmpId(psnApply);
                    deptApply.setFkByDeptId(new TsSimpleCode(deptId));
                    preInsert(deptApply);
                    workdeptApplyList.add(deptApply);
                }
            }
        }
    }
}
