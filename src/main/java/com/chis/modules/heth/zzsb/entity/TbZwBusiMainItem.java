package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2024-7-18
 */
@Entity
@Table(name = "TB_ZW_BUSI_MAIN_ITEM")
@SequenceGenerator(name = "TbZwBusiMainItem", sequenceName = "TB_ZW_BUSI_MAIN_ITEM_SEQ", allocationSize = 1)
public class TbZwBusiMainItem implements Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TsSimpleCode fkByBusId;
    private Integer checkType;
    private TsSimpleCode fkByItemId;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;

    public TbZwBusiMainItem() {
    }

    public TbZwBusiMainItem(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwBusiMainItem")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BUS_ID")
    public TsSimpleCode getFkByBusId() {
        return fkByBusId;
    }

    public void setFkByBusId(TsSimpleCode fkByBusId) {
        this.fkByBusId = fkByBusId;
    }

    @Column(name = "CHECK_TYPE")
    public Integer getCheckType() {
        return checkType;
    }

    public void setCheckType(Integer checkType) {
        this.checkType = checkType;
    }

    @ManyToOne
    @JoinColumn(name = "ITEM_ID")
    public TsSimpleCode getFkByItemId() {
        return fkByItemId;
    }

    public void setFkByItemId(TsSimpleCode fkByItemId) {
        this.fkByItemId = fkByItemId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

}