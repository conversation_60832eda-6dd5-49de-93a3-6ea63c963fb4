package com.chis.modules.heth.zzsb.entity.vo;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 支撑机构-仪器设备配置信息-VO
 */
public class SupportInstAbilityVO {
    /**
     * 页面显示序号
     */
    private String num;
    /**
     * 页面是否选择
     */
    private boolean sel;
    /**
     * 只有二级
     */
    private boolean onlyLevel2;
    /**
     * 对应码表
     */
    private TsSimpleCode simpleCode;
    /**
     * 对应下级
     */
    private List<SupportInstAbilityVO> childList;
    /**
     * 下级是否特殊
     */
    private boolean ifChildSpec;

    public SupportInstAbilityVO() {
        this.simpleCode = new TsSimpleCode();
        this.childList = new LinkedList<>();
    }

    public SupportInstAbilityVO(TsSimpleCode simpleCode) {
        this.simpleCode = simpleCode;
        this.childList = new LinkedList<>();
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public boolean getSel() {
        return sel;
    }

    public void setSel(boolean sel) {
        this.sel = sel;
    }

    public boolean getOnlyLevel2() {
        return onlyLevel2;
    }

    public void setOnlyLevel2(boolean onlyLevel2) {
        this.onlyLevel2 = onlyLevel2;
    }

    public TsSimpleCode getSimpleCode() {
        return simpleCode;
    }

    public void setSimpleCode(TsSimpleCode simpleCode) {
        this.simpleCode = simpleCode;
    }

    public List<SupportInstAbilityVO> getChildList() {
        return childList;
    }

    public void setChildList(List<SupportInstAbilityVO> childList) {
        this.childList = childList;
    }

    public void addChildList(SupportInstAbilityVO supportInstVO) {
        if (this.childList == null) {
            this.childList = new ArrayList<>();
        }
        this.childList.add(supportInstVO);
    }

    public boolean isIfChildSpec() {
        return ifChildSpec;
    }

    public void setIfChildSpec(boolean ifChildSpec) {
        this.ifChildSpec = ifChildSpec;
    }
}
