package com.chis.modules.heth.zzsb.logic;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * <p>类描述：人员属性对象</p>
 * @ClassAuthor qrr,2020年10月15日,TdZwPsnTypePO
 * */
public class TdZwPsnTypePO {
	/** 是否选择 */
    private boolean ifSelected = false;
	/**人员属性码表*/
	private TsSimpleCode fkByPsnType;
	/**是否鉴定专家*/
	private boolean ifJdExpert = false;
	/**其他内容*/
	private String otherType;
	private Integer zzjgType;
	
	public boolean isIfJdExpert() {
		return ifJdExpert;
	}
	public void setIfJdExpert(boolean ifJdExpert) {
		this.ifJdExpert = ifJdExpert;
	}
	public String getOtherType() {
		return otherType;
	}
	public void setOtherType(String otherType) {
		this.otherType = otherType;
	}
	public TsSimpleCode getFkByPsnType() {
		return fkByPsnType;
	}
	public void setFkByPsnType(TsSimpleCode fkByPsnType) {
		this.fkByPsnType = fkByPsnType;
	}
	public boolean isIfSelected() {
		return ifSelected;
	}
	public void setIfSelected(boolean ifSelected) {
		this.ifSelected = ifSelected;
	}

    public Integer getZzjgType() {
        return zzjgType;
    }

    public void setZzjgType(Integer zzjgType) {
        this.zzjgType = zzjgType;
    }
}
