package com.chis.modules.heth.zzsb.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.ObjectCopyUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.logic.TdZwPsnTypePO;
import com.chis.modules.heth.zzsb.logic.TdZwPsnTypeRow;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>
 * 类描述：资质人员培训统一业务层
 * </p>
 *
 * @ClassAuthor qrr, 2018-05-16, ZwPsnPlanServiceImpl
 */
@Service
@Transactional(readOnly = true)
public class ZzsbZwPsnPlanServiceImpl extends AbstractTemplate {
	private ZzsbZwIntellReportServiceImpl zwIntellReportServiceImpl = SpringContextHolder
			.getBean(ZzsbZwIntellReportServiceImpl.class);
	 /**
     * <p>方法描述：新增或者更新</p>
     *
     * @MethodAuthor qrr, 2018-04-25,saveOrUpdatePsninfo
     */
    @Transactional(readOnly = false)
    public <T> void saveOrUpdatePsninfoOld(TdZwPsninfoZzsb psnInfo, List<TdZwPsnTypeRow> psnTypeRows, Integer zwOrgType) {
		if (null==psnInfo.getFkByDoctSpecId().getRid()) {
			psnInfo.setFkByDoctSpecId(null);
		}
		if (null==psnInfo.getFkByDoctTypeId().getRid()) {
			psnInfo.setFkByDoctTypeId(null);
		}
		if (null==psnInfo.getTitleLevelId()) {
			psnInfo.setFkByTitleLevelId(null);
		}

		if (null == psnInfo.getDegreeId()) {
			psnInfo.setFkByDegreeId(null);
		} else {
			psnInfo.setFkByDegreeId(new TsSimpleCode(psnInfo.getDegreeId()));
		}
		if (null == psnInfo.getDoctLevelId()) {
			psnInfo.setFkByDoctLevelId(null);
		} else {
			psnInfo.setFkByDoctLevelId(new TsSimpleCode(psnInfo.getDoctLevelId()));
		}
		if (null == psnInfo.getEducationId()) {
			psnInfo.setFkByEducationId(null);
		} else {
			psnInfo.setFkByEducationId(new TsSimpleCode(psnInfo.getEducationId()));
		}
		if (null == psnInfo.getNation()) {
			psnInfo.setFkByNation(null);
		} else {
			psnInfo.setFkByNation(new TsSimpleCode(psnInfo.getNation()));
		}
		if (null == psnInfo.getTitleId()) {
			psnInfo.setFkByTitleId(null);
		} else {
			psnInfo.setFkByTitleId(new TsSimpleCode(psnInfo.getTitleId()));
		}
		if (null == psnInfo.getBeJobId()) {
			psnInfo.setFkByBeJobId(null);
		} else {
			psnInfo.setFkByBeJobId(new TsSimpleCode(psnInfo.getBeJobId()));
		}
		if (null != psnInfo.getRid()) {
			updateObj(psnInfo);
		} else {
			preInsert(psnInfo);
			saveObj(psnInfo);
		}
         zwIntellReportServiceImpl.deletePsnType(psnInfo.getRid(), zwOrgType);
		if (CollectionUtils.isEmpty(psnTypeRows)) {
			return;
		}
		for (TdZwPsnTypeRow row : psnTypeRows) {
			List<TdZwPsnTypePO> typePOs = row.getTypePOs();
			for (TdZwPsnTypePO t : typePOs) {
				if (!t.isIfSelected()) {
					continue;
				}
				TdZwPsnTypeZzsb tdZwPsnType = new TdZwPsnTypeZzsb();
				tdZwPsnType.setFkByMainId(psnInfo);
				tdZwPsnType.setFkByPsnType(t.getFkByPsnType());
				tdZwPsnType.setZzjgType(t.getZzjgType());
				if ("4".equals(t.getFkByPsnType().getExtendS3())) {
					tdZwPsnType.setOtherPsnType(t.getOtherType());
				}
				preInsert(tdZwPsnType);
				zwIntellReportServiceImpl.saveOrUpdatePsnType(tdZwPsnType);
			}
		}
    }
	 /**
     * <p>方法描述：新增或者更新</p>
     *
     * @MethodAuthor qrr, 2018-04-25,saveOrUpdatePsninfo
     */
    @Transactional(readOnly = false)
    public <T> void saveOrUpdatePsninfo(TdZwPsninfoZzsb psnInfo, List<TdZwPsnTypeRow> psnTypeRows, Integer zwOrgType) {
    	 if (null==psnInfo.getFkByDegreeId().getRid()) {
         	psnInfo.setFkByDegreeId(null);
		 }
         if (null==psnInfo.getFkByDoctLevelId().getRid()) {
         	psnInfo.setFkByDoctLevelId(null);
		 }
         if (null==psnInfo.getFkByDoctSpecId().getRid()) {
         	psnInfo.setFkByDoctSpecId(null);
		 }
         if (null==psnInfo.getFkByDoctTypeId().getRid()) {
         	psnInfo.setFkByDoctTypeId(null);
		 }
         if (null==psnInfo.getFkByEducationId().getRid()) {
         	psnInfo.setFkByEducationId(null);
		 }
         if (null==psnInfo.getFkByNation().getRid()) {
         	psnInfo.setFkByNation(null);
		 }
         if (null==psnInfo.getFkByTitleId().getRid()) {
         	psnInfo.setFkByTitleId(null);
		 }
         if (null==psnInfo.getFkByTitleLevelId().getRid()) {
          	psnInfo.setFkByTitleLevelId(null);
 		 }

         if (null!=psnInfo.getRid()) {
			updateObj(psnInfo);
         }else {
             preInsert(psnInfo);
             saveObj(psnInfo);
         }
         zwIntellReportServiceImpl.deletePsnType(psnInfo.getRid(), zwOrgType);
         if (!CollectionUtils.isEmpty(psnTypeRows)) {
        	 for (TdZwPsnTypeRow row : psnTypeRows) {
        		 List<TdZwPsnTypePO> typePOs = row.getTypePOs();
        		 for (TdZwPsnTypePO t : typePOs) {
        			 if (t.isIfSelected()) {
        				 TdZwPsnTypeZzsb tdZwPsnType = new TdZwPsnTypeZzsb();
        				 tdZwPsnType.setFkByMainId(psnInfo);
        				 tdZwPsnType.setFkByPsnType(t.getFkByPsnType());
                         tdZwPsnType.setZzjgType(t.getZzjgType());
        				 if ("4".equals(t.getFkByPsnType().getExtendS3())) {
        					 tdZwPsnType.setOtherPsnType(t.getOtherType());
        				 }
        				 preInsert(tdZwPsnType);
        				 zwIntellReportServiceImpl.saveOrUpdatePsnType(tdZwPsnType);
					}
        		 }
			}
		}
    }
	/**
 	 * <p>方法描述：获取人员资质类别</p>
 	 * @MethodAuthor qrr,2018年5月16日,findTdZwPsnZzlb
	 * */
	public TdZwPsnZzlbZzsb findTdZwPsnZzlb(Integer rid){
		String hql=" SELECT T FROM TdZwPsnZzlbZzsb T WHERE T.rid = "+rid;
		TdZwPsnZzlbZzsb psnZzlb = findOneByHql(hql,TdZwPsnZzlbZzsb.class);
		if (null!=psnZzlb) {
			psnZzlb.getRangeList().size();
			return psnZzlb;
		}
		return null;
	}
	 @Transactional(readOnly = false)
	 public void saveOrUpdatePsnZzlb(TdZwPsnZzlbZzsb entity) {
    	if (null!=entity.getRid()) {
    		super.executeSql("DELETE FROM TD_ZW_ZZLB_RANGE WHERE  MAIN_ID = "+entity.getRid());
		}
    	if(null != entity.getSelectRanges() && entity.getSelectRanges().length>0){
			List<TdZwZzlbRangeZzsb> rangeList=new ArrayList<>();
			for(String str:entity.getSelectRanges()){
				TdZwZzlbRangeZzsb range=new TdZwZzlbRangeZzsb();
				range.setFkByMainId(entity);
				range.setFkByZzRangeId(new TsSimpleCode(Integer.valueOf(str)));
				preInsert(range);
				rangeList.add(range);
			}
			entity.setRangeList(rangeList);
		}else {
			entity.setRangeList(new ArrayList<TdZwZzlbRangeZzsb>());
		}
		if (null != entity.getFkByQualId()
				&& null == entity.getFkByQualId().getRid()) {
			entity.setFkByQualId(null);
		}
    	upsertEntity(entity);
	 }

	 /**
	  * @Description: 新增或修改人员资质信息并删除清空的资质信息列表
	  *
	  * @MethodAuthor pw,2022年02月15日
	  */
	@Transactional(readOnly = false)
	public void saveOrUpdatePsnZzlbAndRemoveList(TdZwPsnZzlbZzsb entity, List<TdZwPsnZzlbZzsb> removeZzlbList) {
		if(!CollectionUtils.isEmpty(removeZzlbList)){
			for(TdZwPsnZzlbZzsb zzlb : removeZzlbList){
				if(null == zzlb.getRid()){
					continue;
				}
				super.executeSql("DELETE FROM TD_ZW_ZZLB_RANGE WHERE  MAIN_ID = "+zzlb.getRid());
				super.executeSql("DELETE FROM TD_ZW_PSN_ZZLB WHERE  RID = "+zzlb.getRid());
			}
		}
		if (null!=entity.getRid()) {
			super.executeSql("DELETE FROM TD_ZW_ZZLB_RANGE WHERE  MAIN_ID = "+entity.getRid());
		}
		if(null != entity.getSelectRanges() && entity.getSelectRanges().length>0){
			List<TdZwZzlbRangeZzsb> rangeList=new ArrayList<>();
			for(String str:entity.getSelectRanges()){
				TdZwZzlbRangeZzsb range=new TdZwZzlbRangeZzsb();
				range.setFkByMainId(entity);
				range.setFkByZzRangeId(new TsSimpleCode(Integer.valueOf(str)));
				preInsert(range);
				rangeList.add(range);
			}
			entity.setRangeList(rangeList);
		}else {
			entity.setRangeList(new ArrayList<TdZwZzlbRangeZzsb>());
		}
		if (null != entity.getFkByQualId()
				&& null == entity.getFkByQualId().getRid()) {
			entity.setFkByQualId(null);
		}
		upsertEntity(entity);
	}

	 /**
 	 * <p>方法描述：删除人员资质</p>
 	 * @MethodAuthor qrr,2018年5月16日,delPsnZzlb
	  * */
	 @Transactional(readOnly = false)
	 public void delPsnZzlb(TdZwPsnZzlbZzsb entity) {
    	if (null!=entity.getRid()) {
    		super.delete(TdZwPsnZzlbZzsb.class, entity.getRid());
		}
	 }

	 /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年5月19日,findPsninfoList
	 * */
	public List<TdZwPsninfoZzsb> findPsninfoList(String rids){
		String hql=" SELECT T FROM TdZwPsninfoZzsb T WHERE T.rid in ("+rids+")";
		List<TdZwPsninfoZzsb> list = findByHql(hql, TdZwPsninfoZzsb.class);
		if (null!=list && list.size()>0) {
			for (TdZwPsninfoZzsb t : list) {
				t.getZzlbList().size();
			}
		}
		return list;
	}

	//任晨炯
	 /**
	  * <p>方法描述：根据身份证去查询是否存在资质人员,存在则为老用户，不存在则为新用户</p>
 	 * TD_ZW_PSNINFO
 	 * @MethodAuthor rcj,2018年5月17日,findPsnByIdCard
	  * @return
	  */
	@Transactional(readOnly = true)
	public TdZwPsninfoZzsb findPsnByIdCard(String idcCard) {
		String hql = "SELECT T FROM TdZwPsninfoZzsb T WHERE upper(T.idcCard) = upper('"+idcCard+"')";
		TdZwPsninfoZzsb tdZwPsninfo = findOneByHql(hql, TdZwPsninfoZzsb.class);
		
		return tdZwPsninfo;
	}
	 /**
 	 * <p>方法描述：合格更新人员资质类别</p>
 	 * @MethodAuthor qrr,2018年5月25日,updateZzlb
	  * */
	 @Transactional(readOnly = false)
	 public void updateZzlb(Integer psnId,TsSimpleCode trainType,Object[] obj,Map<String,List<Object[]>> ifhgMap){
		 List<Integer> zzlbList = new ArrayList<>();
		 if (null!=ifhgMap.get(obj[1].toString()+(null!=obj[4]?"@@"+obj[4].toString()+"@@":""))) {
			 List<Object[]> list = ifhgMap.get(obj[1].toString()+(null!=obj[4]?"@@"+obj[4].toString()+"@@":""));
			 if ("1001".equals(trainType.getCodeNo())) {
				 Map<String, List<Integer>> map = findDocZzlb();
				 for (Object[] objs : list) {
					 if (null!=map.get(objs[22].toString())) {
						 zzlbList.addAll(map.get(objs[22].toString()));
					 }
				 }
				
			 }else {
				 for (Object[] objs : list) {
					 if (null!=objs[19]) {
						 zzlbList.add(Integer.valueOf(objs[19].toString()));
					 }
				 }
			 }
		 }
		 StringBuffer psn = new StringBuffer();
		 psn.append(" select t1.rid,wm_concat(t2.ZZ_RANGE_ID) from TD_ZW_PSN_ZZLB t1");
		 psn.append(" left join TD_ZW_ZZLB_RANGE t2 on t2.MAIN_ID = t1.rid");
		 psn.append(" where t1.PSN_ID = ").append(psnId);
		 psn.append(" and t1.QUAL_ID = ").append(trainType.getRid());//培训类型
		 psn.append(" group by t1.rid");
		 List<Object[]> list = super.findSqlResultList(psn.toString());
		 //是否更新资质类别
		 boolean flag = false;
		 if (null!=list && list.size()>0) {
			if ("1001".equals(trainType.getCodeNo())
					|| "1002".equals(trainType.getCodeNo())
					|| "1006".equals(trainType.getCodeNo())) {
				for (Object[] ss : list) {
					if (null!=ss[1]&& zzlbList.size()>0) {
						String s = ss[1].toString();
						String[] split = s.split(",");
						StringBuffer sb = new StringBuffer();
						sb.append(",").append(s).append(",");
						//有专业类别，存在相同的资质范围
						if (split.length == zzlbList.size()) {
							//存在不同的资质范围
							boolean flag1 = false;
							for (Integer zzlbId : zzlbList) {
								if (!sb.toString().contains(","+zzlbId+",")) {
									flag1 = true;
								}
							}
							if (!flag1) {
								flag = true;
							}
						}
					}else {
						flag = true;
					}
				}
			}else {
				//无专业类别
				flag = true;
			}
		 }
		 //更新
		 if (flag) {
			 StringBuffer zzlb = new StringBuffer();
			 zzlb.append("UPDATE TD_ZW_PSN_ZZLB SET CENT_NO ='").append(obj[4]).append("'");
			 zzlb.append(",SEND_DATE = ").append("to_date('").append(DateUtils.formatDate((Date)obj[7], "yyyy-MM-dd")).append("','yyyy-MM-dd')");
			 if (null!=obj[6]) {
				 zzlb.append(",VALID_DATE = ").append("to_date('").append(DateUtils.formatDate((Date)obj[6], "yyyy-MM-dd")).append("','yyyy-MM-dd')");
			 }
			 zzlb.append(",AGEIN_SEND_DATE = ").append("to_date('").append(DateUtils.formatDate((Date)obj[8], "yyyy-MM-dd")).append("','yyyy-MM-dd')");
			 zzlb.append(",ANNEX_PATH = '").append(obj[9]).append("'");
			 zzlb.append(",ANNEX_NAME = '").append(obj[10]).append("'");
			 zzlb.append(" where PSN_ID =").append(psnId).append(" and QUAL_ID =").append(trainType.getRid());
			 super.executeSql(zzlb.toString());
		 }else {
			//保存
			 TdZwPsnZzlbZzsb psnZzlb = new TdZwPsnZzlbZzsb();
			 psnZzlb.setFkByPsnId(new TdZwPsninfoZzsb(psnId));
			 psnZzlb.setFkByQualId(trainType);
			 psnZzlb.setCentNo(null!=obj[4]?obj[4].toString():"");
			 psnZzlb.setAgeinSendDate((Date)obj[8]);
			 psnZzlb.setSendDate((Date)obj[7]);
			 psnZzlb.setValidDate((Date)obj[6]);
			 psnZzlb.setAnnexPath(null!=obj[9]?obj[9].toString():"");
			 psnZzlb.setAnnexName(null!=obj[10]?obj[10].toString():"");
			 
			 //有专业类别
			 if ("1001".equals(trainType.getCodeNo())
						|| "1002".equals(trainType.getCodeNo())
						|| "1006".equals(trainType.getCodeNo())) {
				 if (null!=zzlbList && zzlbList.size()>0) {
					for (Integer zzlb : zzlbList) {
						 TdZwZzlbRangeZzsb range = new TdZwZzlbRangeZzsb();
						 range.setFkByZzRangeId(new TsSimpleCode(zzlb));
						 range.setFkByMainId(psnZzlb);
						 super.preInsert(range);
						 psnZzlb.getRangeList().add(range);
					}
				 }
			 }
			 super.upsertEntity(psnZzlb);
		}
	 }
	 /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年6月14日,findDocZzlb
	  * */
	 private Map<String, List<Integer>> findDocZzlb(){
		 List<TsSimpleCode> majorList = zwIntellReportServiceImpl.findUpCodeList("5026");
		 Map<String, List<Integer>> map = new HashMap<>();
		 if (null!=majorList && majorList.size()>0) {
			for (TsSimpleCode t : majorList) {
				if (StringUtils.isNotBlank(t.getExtendS3())) {
					if (null==map.get(t.getExtendS3())) {
						List<Integer> list = new ArrayList<>();
						list.add(t.getRid());
						map.put(t.getExtendS3(), list);
					}else {
						List<Integer> list = map.get(t.getExtendS3());
						list.add(t.getRid());
					}
				}
			}
		 }
		 return map;
	 }

	 /**
	 * @Description : 根据人员ID、资质机构类型获取人员属性集合
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/18 10:56
	 **/
	 public List<TdZwPsnTypeZzsb> findTdZwPsnTypeListByMainIdAndZzjgType(Integer mainId, Integer zzjgType) {
         StringBuilder sb = new StringBuilder();
         sb.append(" select t from TdZwPsnTypeZzsb t where 1=1 ");
         sb.append(" and t.fkByMainId.rid = '").append(mainId).append("' ");
         sb.append(" and t.zzjgType = '").append(zzjgType).append("' ");
         return em.createQuery(sb.toString()).getResultList();
     }



	/**
	 * @Description: 新增或修改人员资质信息列表，删除需要丢弃的资质信息
	 *
	 * @MethodAuthor pw,2022年02月16日
	 */
	@Transactional(readOnly = false)
	public void saveOrUpdatePsnZzlbAndRemoveList(List<TdZwPsnZzlbZzsb> zzlbList, List<TdZwPsnZzlbZzsb> removeZzlbList) {
		if(!CollectionUtils.isEmpty(removeZzlbList)){
			for(TdZwPsnZzlbZzsb zzlb : removeZzlbList){
				if(null == zzlb.getRid()){
					continue;
				}
				super.executeSql("DELETE FROM TD_ZW_ZZLB_RANGE WHERE  MAIN_ID = "+zzlb.getRid());
				super.executeSql("DELETE FROM TD_ZW_PSN_ZZLB WHERE  RID = "+zzlb.getRid());
			}
		}
		if(CollectionUtils.isEmpty(zzlbList)){
			return;
		}
		List<TdZwPsnZzlbZzsb> saveObjList = new ArrayList<>();
		List<TdZwPsnZzlbZzsb> updateObjList = new ArrayList<>();
		for(TdZwPsnZzlbZzsb entity : zzlbList){
			if (null!=entity.getRid()) {
				super.executeSql("DELETE FROM TD_ZW_ZZLB_RANGE WHERE  MAIN_ID = "+entity.getRid());
			}
			if(null != entity.getSelectRanges() && entity.getSelectRanges().length>0){
				List<TdZwZzlbRangeZzsb> rangeList=new ArrayList<>();
				for(String str:entity.getSelectRanges()){
					TdZwZzlbRangeZzsb range=new TdZwZzlbRangeZzsb();
					range.setFkByMainId(entity);
					range.setFkByZzRangeId(new TsSimpleCode(Integer.valueOf(str)));
					preInsert(range);
					rangeList.add(range);
				}
				entity.setRangeList(rangeList);
			}else {
				entity.setRangeList(new ArrayList<TdZwZzlbRangeZzsb>());
			}
			if (null != entity.getFkByQualId()
					&& null == entity.getFkByQualId().getRid()) {
				entity.setFkByQualId(null);
			}
			if(null == entity.getRid()){
				preInsert(entity);
				saveObjList.add(entity);
			}else{
				preUpdate(entity);
				updateObjList.add(entity);
			}
		}
		if(!CollectionUtils.isEmpty(saveObjList)){
			this.saveBatchObjs(saveObjList);
		}
		if(!CollectionUtils.isEmpty(updateObjList)){
			this.updateBatchObjs(updateObjList);
		}
	}


	/**
	 * @Description: 通过身份证号 获取当前单位的资质人员
	 *
	 * @MethodAuthor pw,2022年02月17日
	 */
	public TdZwPsninfoZzsb findCurrentUnitPsnInfo(String idc){
		Integer unitRid = Global.getUser().getTsUnit().getRid();
		if(null == unitRid || StringUtils.isBlank(idc)){
			return null;
		}
		StringBuffer hqlBuffer = new StringBuffer();
		Map<String,Object> paramMap = new HashMap<>(5);
		hqlBuffer.append("SELECT T FROM TdZwPsninfoZzsb T WHERE 1=1 ");
		hqlBuffer.append(" AND T.idcCard= :idcCard ");
		paramMap.put("idcCard",idc.trim());
		hqlBuffer.append(" AND (");
		//若只有资质申报模块这一个途径添加资质人员信息 这一个条件可以去掉
		hqlBuffer.append(" T.tsUnit.rid=").append(unitRid);
		hqlBuffer.append(" OR EXISTS (SELECT 1 FROM TdZwTjorgpsnsZzsb T1 WHERE T1.tdZwPsninfo.rid = T.rid AND T1.tdZwTjorginfo.tsUnit.rid=").append(unitRid).append(")");
		hqlBuffer.append(" OR EXISTS (SELECT 1 FROM TdZwSrvorgpsnsZzsb T1 WHERE T1.tdZwPsninfo.rid = T.rid AND T1.tdZwSrvorginfo.tsUnit.rid=").append(unitRid).append(")");
		hqlBuffer.append(" OR EXISTS (SELECT 1 FROM TdZwDiagpsnsZzsb T1 WHERE T1.tdZwPsninfo.rid = T.rid AND T1.tdZwDiagorginfo.tsUnit.rid=").append(unitRid).append(")");
		hqlBuffer.append(" OR EXISTS (SELECT 1 FROM TdZwOcchethPsnsZzsb T1 WHERE T1.tdZwPsninfo.rid = T.rid AND T1.fkByOrgId.tsUnit.rid=").append(unitRid).append(")");
		hqlBuffer.append(" OR EXISTS (SELECT 1 FROM TdZwTjorgpsnsApply T1 WHERE T1.fkByEmpId.rid = T.rid AND T1.fkByOrgId.fkByOrgId.rid=").append(unitRid).append(")");
		hqlBuffer.append(" OR EXISTS (SELECT 1 FROM TdZwSupportPsnsZzsb T1 WHERE T1.tdZwPsninfo.rid = T.rid AND T1.tdZwSupportOrg.tsUnit.rid=").append(unitRid).append(")");
		hqlBuffer.append(")");
		List<TdZwPsninfoZzsb> resultList = this.findDataByHqlNoPage(hqlBuffer.toString(), paramMap);
		if(!CollectionUtils.isEmpty(resultList)){
			for(TdZwPsninfoZzsb psninfoZzsb : resultList){
				psninfoZzsb.getTjMainList().size();
				psninfoZzsb.getZzlbList().size();
				if(null !=psninfoZzsb.getZzlbList() && psninfoZzsb.getZzlbList().size()>0){
					for(TdZwPsnZzlbZzsb zzlb:psninfoZzsb.getZzlbList()){
						zzlb.getRangeList().size();
						if(null != zzlb.getRangeList() && zzlb.getRangeList().size()>0){
							String[] str=new String[zzlb.getRangeList().size()];
							for(int i=0;i<zzlb.getRangeList().size();i++){
								TdZwZzlbRangeZzsb range=zzlb.getRangeList().get(i);
								str[i]=range.getFkByZzRangeId().getRid().toString();
							}
							zzlb.setSelectRanges(str);
						}
					}
				}
				return psninfoZzsb;
			}
		}
		return null;
	}

	/**
	 * @Description: 获取当前登录用户机构下对应身份证的人员数量
	 *
	 * @MethodAuthor pw,2022年02月17日
	 */
	public int findIdcCountCurrentOrgExist(String idc, Integer zwOrgType, Integer exceptRid){
		if(StringUtils.isBlank(idc) || null == zwOrgType){
			return 0;
		}
		StringBuffer sqlBuffer = new StringBuffer();
		Map<String,Object> paramMap = new HashMap<>(5);
		sqlBuffer.append("SELECT COUNT(*) FROM ");
		if (0 == zwOrgType) {
			sqlBuffer.append(" TD_ZW_TJORGPSNS ");
		} else if (1 == zwOrgType) {
			sqlBuffer.append(" TD_ZW_SRVORGPSNS ");
		} else if (2 == zwOrgType) {
			sqlBuffer.append(" TD_ZW_DIAGPSNS ");
		}else if(4 == zwOrgType){
			sqlBuffer.append(" TD_ZW_SUPPORT_PSNS ");
		} else {
			sqlBuffer.append(" TD_ZW_OCCHETH_PSNS ");
		}
		sqlBuffer.append(" T ");
		sqlBuffer.append(" INNER JOIN TD_ZW_PSNINFO T1 ON T1.RID=T.EMP_ID ");
		sqlBuffer.append(" INNER JOIN ");
		if (0 == zwOrgType) {
			sqlBuffer.append(" TD_ZW_TJORGINFO T2 ON T2.RID = T.ORG_ID  ");
		} else if (1 == zwOrgType) {
			sqlBuffer.append(" TD_ZW_SRVORGINFO T2 ON T2.RID = T.ORG_ID ");
		} else if (2 == zwOrgType) {
			sqlBuffer.append(" TD_ZW_DIAGORGINFO T2 ON T2.RID = T.ORG_ID ");
		}else if(4 == zwOrgType){
			sqlBuffer.append(" TD_ZW_SUPPORT_ORG T2 ON T2.RID = T.ORG_ID ");
		} else {
			sqlBuffer.append(" TD_ZW_OCCHETH_INFO T2 ON T2.RID = T.ORG_ID ");
		}
		sqlBuffer.append(" WHERE 1=1 ");
		sqlBuffer.append(" AND T1.IDC_CARD = :idcCard ");
		paramMap.put("idcCard",idc.trim());
		sqlBuffer.append(" AND T2.ORG_ID= ").append(Global.getUser().getTsUnit().getRid());
		if(null != exceptRid){
			sqlBuffer.append(" AND T.RID != ").append(exceptRid);
		}
		return this.findCountBySql(sqlBuffer.toString(), paramMap);
	}

	/**
	 * 获取当前备案机构下对应身份证的人员数量
	 *
	 * @param idc       身份证号
	 * @param orgType   备案机构类型
	 * @param exceptRid 备案机构人员关系RID
	 * @param orgRid    备案机构rid
	 * @return 当前备案机构下对应身份证的人员数量
	 */
	public int findIdcCountCurrentOrgExistNew(String idc, Integer orgType, Integer exceptRid, Integer orgRid) {
		if (StringUtils.isBlank(idc) || null == orgType) {
			return 0;
		}
		StringBuilder sql = new StringBuilder();
		Map<String, Object> paramMap = new HashMap<>(5);
		sql.append("SELECT COUNT(*) FROM ");
		sql.append(" TD_ZW_TJORGPSNS_APPLY ");
		sql.append("  T ");
		sql.append(" INNER JOIN TD_ZW_PSNINFO T1 ON T1.RID=T.EMP_ID ");
		sql.append(" INNER JOIN TD_ZW_TJORGINFO_APPLY T2 ON T2.RID = T.ORG_ID ");
		sql.append(" WHERE 1=1 ");
		sql.append(" AND T1.IDC_CARD = :idcCard ");
		paramMap.put("idcCard", idc.trim());
		sql.append(" AND T.ORG_ID = ").append(orgRid);
		if (null != exceptRid) {
			sql.append(" AND T.RID != ").append(exceptRid);
		}
		return this.findCountBySql(sql.toString(), paramMap);
	}


	/**
	 * @Description: 保存或更新资质人员和关系
	 *
	 * @MethodAuthor pw,2022年02月17日
	 */
	@Transactional(readOnly = false)
	public void saveOrUpdatePsninfoAndRelation(TdZwPsninfoZzsb psnInfo, List<TdZwPsnTypeRow> psnTypeRows, Integer zwOrgType,
											   IZwOrgPsnsZzsb orgPsnsZzsb) {
		if (null==psnInfo.getFkByDoctSpecId() || null==psnInfo.getFkByDoctSpecId().getRid()) {
			psnInfo.setFkByDoctSpecId(null);
		}
		if (null==psnInfo.getFkByDoctTypeId() || null==psnInfo.getFkByDoctTypeId().getRid()) {
			psnInfo.setFkByDoctTypeId(null);
		}
		if (null==psnInfo.getTitleLevelId()) {
			psnInfo.setFkByTitleLevelId(null);
		}

		if (null == psnInfo.getDegreeId()) {
			psnInfo.setFkByDegreeId(null);
		} else {
			psnInfo.setFkByDegreeId(new TsSimpleCode(psnInfo.getDegreeId()));
		}
		if (null == psnInfo.getDoctLevelId()) {
			psnInfo.setFkByDoctLevelId(null);
		} else {
			psnInfo.setFkByDoctLevelId(new TsSimpleCode(psnInfo.getDoctLevelId()));
		}
		if (null == psnInfo.getEducationId()) {
			psnInfo.setFkByEducationId(null);
		} else {
			psnInfo.setFkByEducationId(new TsSimpleCode(psnInfo.getEducationId()));
		}
		if (null == psnInfo.getNation()) {
			psnInfo.setFkByNation(null);
		} else {
			psnInfo.setFkByNation(new TsSimpleCode(psnInfo.getNation()));
		}
		if (null == psnInfo.getTitleId()) {
			psnInfo.setFkByTitleId(null);
		} else {
			psnInfo.setFkByTitleId(new TsSimpleCode(psnInfo.getTitleId()));
		}
		if (null == psnInfo.getBeJobId()) {
			psnInfo.setFkByBeJobId(null);
		} else {
			psnInfo.setFkByBeJobId(new TsSimpleCode(psnInfo.getBeJobId()));
		}
		//copy
		TdZwPsninfoZzsbCopy psninfoZzsbCopy = new TdZwPsninfoZzsbCopy();
		try {
			ObjectCopyUtil.copyProperties(psnInfo, psninfoZzsbCopy);
		}catch (Exception e){
			e.printStackTrace();
		}
		if (null!=psninfoZzsbCopy.getRid()) {
			updateObj(psninfoZzsbCopy);
		}else {
			preInsert(psninfoZzsbCopy);
			saveObj(psninfoZzsbCopy);
		}
		psnInfo.setRid(psninfoZzsbCopy.getRid());
		zwIntellReportServiceImpl.deletePsnType(psnInfo.getRid(), zwOrgType);
		if (!CollectionUtils.isEmpty(psnTypeRows)) {
			for (TdZwPsnTypeRow row : psnTypeRows) {
				List<TdZwPsnTypePO> typePOs = row.getTypePOs();
				for (TdZwPsnTypePO t : typePOs) {
					if (t.isIfSelected()) {
						TdZwPsnTypeZzsb tdZwPsnType = new TdZwPsnTypeZzsb();
						tdZwPsnType.setFkByMainId(psnInfo);
						tdZwPsnType.setFkByPsnType(t.getFkByPsnType());
						tdZwPsnType.setZzjgType(t.getZzjgType());
						if ("4".equals(t.getFkByPsnType().getExtendS3())) {
							tdZwPsnType.setOtherPsnType(t.getOtherType());
						}
						preInsert(tdZwPsnType);
						zwIntellReportServiceImpl.saveOrUpdatePsnType(tdZwPsnType);
					}
				}
			}
		}
		if(null == orgPsnsZzsb.getRid()){
			save(orgPsnsZzsb);
		}else{
			updateObj(orgPsnsZzsb);
		}
	}

	/**
	 * @Description: 保存或更新资质人员和关系
	 *
	 * @MethodAuthor pw,2022年02月17日
	 */
	@Transactional(readOnly = false)
	public void saveOrUpdatePsninfoAndRelationNew(TdZwPsninfoZzsb psnInfo, List<TdZwPsnTypeRow> psnTypeRows, Integer zwOrgType,
												  TdZwTjorgpsnsApply tjorgpsnsApply) {
		if (null == psnInfo.getFkByDegreeId() || null == psnInfo.getFkByDegreeId().getRid()) {
			psnInfo.setFkByDegreeId(null);
		}
		if (null == psnInfo.getFkByDoctLevelId() || null == psnInfo.getFkByDoctLevelId().getRid()) {
			psnInfo.setFkByDoctLevelId(null);
		}
		if (null == psnInfo.getFkByDoctSpecId().getRid()) {
			psnInfo.setFkByDoctSpecId(null);
		}
		if (null == psnInfo.getFkByDoctTypeId().getRid()) {
			psnInfo.setFkByDoctTypeId(null);
		}
		if (null == psnInfo.getFkByEducationId().getRid()) {
			psnInfo.setFkByEducationId(null);
		}
		if (null == psnInfo.getFkByNation().getRid()) {
			psnInfo.setFkByNation(null);
		}
		if (null == psnInfo.getFkByTitleId().getRid()) {
			psnInfo.setFkByTitleId(null);
		}
		if (null == psnInfo.getFkByTitleLevelId().getRid()) {
			psnInfo.setFkByTitleLevelId(null);
		}
		//copy
		TdZwPsninfoZzsbCopy psninfoZzsbCopy = new TdZwPsninfoZzsbCopy();
		try {
			ObjectCopyUtil.copyProperties(psnInfo, psninfoZzsbCopy);
		}catch (Exception e){
			e.printStackTrace();
		}
		if (null!=psninfoZzsbCopy.getRid()) {
			updateObj(psninfoZzsbCopy);
		}else {
			preInsert(psninfoZzsbCopy);
			saveObj(psninfoZzsbCopy);
		}
		psnInfo.setRid(psninfoZzsbCopy.getRid());
		zwIntellReportServiceImpl.deletePsnType(psnInfo.getRid(), zwOrgType);
		if (!CollectionUtils.isEmpty(psnTypeRows)) {
			for (TdZwPsnTypeRow row : psnTypeRows) {
				List<TdZwPsnTypePO> typePOs = row.getTypePOs();
				for (TdZwPsnTypePO t : typePOs) {
					if (t.isIfSelected()) {
						TdZwPsnTypeZzsb tdZwPsnType = new TdZwPsnTypeZzsb();
						tdZwPsnType.setFkByMainId(psnInfo);
						tdZwPsnType.setFkByPsnType(t.getFkByPsnType());
						tdZwPsnType.setZzjgType(t.getZzjgType());
						if ("4".equals(t.getFkByPsnType().getExtendS3())) {
							tdZwPsnType.setOtherPsnType(t.getOtherType());
						}
						preInsert(tdZwPsnType);
						zwIntellReportServiceImpl.saveOrUpdatePsnType(tdZwPsnType);
					}
				}
			}
		}
		if (null == tjorgpsnsApply.getRid()) {
			saveObj(tjorgpsnsApply);
		}
	}
}
