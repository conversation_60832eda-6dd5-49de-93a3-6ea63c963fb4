package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.TbZwBusiRelItem;
import com.chis.modules.heth.zzsb.service.BusScopeItemConfigServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 业务范围与检测项目关系配置
 */
@ManagedBean(name = "busScopeItemConfigBean")
@ViewScoped
public class BusScopeItemConfigBean extends FacesEditBean {

    private BusScopeItemConfigServiceImpl busScopeItemConfigService = SpringContextHolder.getBean(BusScopeItemConfigServiceImpl.class);
    private Object[] busiRelItem;
    private List<TbZwBusiRelItem> busiRelItemList;

    private TbZwBusiRelItem relItem;

    public BusScopeItemConfigBean() {
        this.ifSQL = true;
        this.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sql = new StringBuilder();
        sql.append(" with table1 as (select T.RID, T.CODE_NAME, T.EXTENDS2, T.NUM ,T.CODE_NO ");
        sql.append("         from TS_SIMPLE_CODE T ");
        sql.append("         left join TS_CODE_TYPE T1 on T.CODE_TYPE_ID = T1.RID ");
        sql.append("         where T1.CODE_TYPE_NAME = '5320' and T.IF_REVEAL=1 ");
        sql.append("         and INSTR(CODE_LEVEL_NO, '.') = 0 ");
        sql.append("         order by T.NUM,T.CODE_NO ), ");
        sql.append("         table2 as (select T.RID, CODE_NAME, T.NUM, T.CODE_NO, 1 as type ");
        sql.append("         from TS_SIMPLE_CODE T ");
        sql.append("         left join TS_CODE_TYPE T1 on T.CODE_TYPE_ID = T1.RID ");
        sql.append("         where T1.CODE_TYPE_NAME = '5625' and T.IF_REVEAL=1 ");
        sql.append("         order by T.NUM, T.CODE_NO) ");
        sql.append(" select T.RID, T.CODE_NAME, T1.RID as itemBRid, T1.CODE_NAME as itemBName ");
        sql.append(" from table1 T ");
        sql.append(" left join table2 T1 on T1.type = T.EXTENDS2 ");
        sql.append(" order by T.NUM, T.CODE_NO, T1.NUM, T1.CODE_NO ");
        String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        return new String[]{sql.toString(), h2};
    }


    /**
     * <p>Description：添加检测项目 </p>
     * <p>Author： yzz 2024-07-17 </p>
     */
    public void addItemAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList;
        paramList = new ArrayList<>(1);
        // 标题
        paramList.add("检测项目");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>(1);
        // 码表类型
        paramList.add("5516");
        paramMap.put("typeNo", paramList);
        // 查询条件名称
        paramList = new ArrayList<>(1);
        paramList.add("检测项目名称");
        paramMap.put("searchName", paramList);
        // 列名
        paramList = new ArrayList<>(1);
        paramList.add("检测项目");
        paramMap.put("colName", paramList);

        // 全选按钮
        paramList = new ArrayList<>(1);
        paramList.add("true");
        paramMap.put("showAllSel", paramList);

        // 已选数据不显示
        paramList = new ArrayList<>(1);
        paramList.add("1");
        paramMap.put("noContainsSelected", paramList);

        // 去除已选数据
        paramList = new ArrayList<>(1);
        paramList.add("1");
        paramMap.put("ifBusScopeItemConfig", paramList);

        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }

    /**
     * <p>Description：人员岗位 选中事件 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.isEmpty()) {
            return;
        }
        List<TsSimpleCode> postList = (List<TsSimpleCode>) selectedMap.get("selectPros");
        if (CollectionUtils.isEmpty(postList)) {
            return;
        }
        List<TbZwBusiRelItem> list = new ArrayList<>();
        for (TsSimpleCode tsSimpleCode : postList) {
            TbZwBusiRelItem item = new TbZwBusiRelItem();
            item.setFkByBusId(new TsSimpleCode(Integer.valueOf(busiRelItem[0].toString())));
            item.setFkByParentItemId(busiRelItem[2] == null ? null : new TsSimpleCode(Integer.valueOf(busiRelItem[2].toString())));
            item.setFkByItemId(new TsSimpleCode(tsSimpleCode.getRid()));
            item.setCreateDate(new Date());
            item.setCreateManid(Global.getUser().getRid());
            list.add(item);
        }
        try{
            this.busScopeItemConfigService.saveItems(list);
            this.busiRelItemList = this.busScopeItemConfigService.findItems(this.busiRelItem);
            JsfUtil.addSuccessMessage("检测项目添加成功！");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("检测项目添加失败！");
        }

    }

    /**
    * <p>Description：删除前验证 </p>
    * <p>Author： yzz 2024-07-18 </p>
    */
    public void beforeDelAction(){
        if (this.relItem == null) {
            return;
        }
        //查询是否被关联
        int count = this.busScopeItemConfigService.findMainItemByRid(relItem.getFkByItemId().getRid());
        if(count>0){
            JsfUtil.addErrorMessage("当前数据已被关联，不可删除！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('DeleteDialog').show();");
    }

    /**
     * <p>Description：删除项目 </p>
     * <p>Author： yzz 2024-07-17 </p>
     */
    public void delItemAction() {
        if (this.relItem == null) {
            return;
        }
        try {
            this.busScopeItemConfigService.delItemByRid(this.relItem.getRid());
            this.busiRelItemList = this.busScopeItemConfigService.findItems(this.busiRelItem);
            RequestContext.getCurrentInstance().update("tabView:editForm:itemTables");
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }


    @Override
    public void addInit() {

    }
    @Override
    public void viewInit() {

    }
    @Override
    public void modInit() {
        this.busiRelItemList = this.busScopeItemConfigService.findItems(this.busiRelItem);
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:itemTables");
        dataTable.setFirst(0);
        dataTable.setRows(20);
    }
    @Override
    public void saveAction() {

    }

    public Object[] getBusiRelItem() {
        return busiRelItem;
    }
    public void setBusiRelItem(Object[] busiRelItem) {
        this.busiRelItem = busiRelItem;
    }

    public List<TbZwBusiRelItem> getBusiRelItemList() {
        return busiRelItemList;
    }
    public void setBusiRelItemList(List<TbZwBusiRelItem> busiRelItemList) {
        this.busiRelItemList = busiRelItemList;
    }
    public TbZwBusiRelItem getRelItem() {
        return relItem;
    }
    public void setRelItem(TbZwBusiRelItem relItem) {
        this.relItem = relItem;
    }
}
