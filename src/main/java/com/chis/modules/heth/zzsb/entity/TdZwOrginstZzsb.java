package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_ORGINST")
@SequenceGenerator(name = "TdZwOrginstSeq", sequenceName = "TD_ZW_ORGINST_SEQ", allocationSize = 1)
public class TdZwOrginstZzsb implements Serializable,IZwInstZzsb {
	private static final long serialVersionUID = 4303563298749261378L;

	private Integer rid;
	private TdZwInstinfoZzsb tdZwInstinfo;
	private TdZwSrvorginfoZzsb tdZwSrvorginfo;
	private Date createDate;
	private Integer createManid;

	public TdZwOrginstZzsb() {
	}

	public TdZwOrginstZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOrginstSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwSrvorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwSrvorginfo = (TdZwSrvorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwInstinfoZzsb getTdZwInstinfo() {
		return this.tdZwInstinfo;
	}

	public void setTdZwInstinfo(TdZwInstinfoZzsb tdZwInstinfo) {
		this.tdZwInstinfo = tdZwInstinfo;
	}

	@ManyToOne
	@JoinColumn(name = "INST_ID" )
	public TdZwSrvorginfoZzsb getTdZwSrvorginfo() {
		return this.tdZwSrvorginfo;
	}

	public void setTdZwSrvorginfo(TdZwSrvorginfoZzsb tdZwSrvorginfo) {
		this.tdZwSrvorginfo = tdZwSrvorginfo;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}