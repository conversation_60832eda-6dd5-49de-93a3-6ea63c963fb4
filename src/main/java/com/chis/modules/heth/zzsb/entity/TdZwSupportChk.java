package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-10-24
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_CHK")
@SequenceGenerator(name = "TdZwSupportChk", sequenceName = "TD_ZW_SUPPORT_CHK_SEQ", allocationSize = 1)
public class TdZwSupportChk implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSupportOrgZzsb fkByMainId;
	private Date startDate;
	private Date endDate;
	private Integer crptNum;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwSupportChk() {
	}

	public TdZwSupportChk(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportChk")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSupportOrgZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "START_DATE")			
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "END_DATE")			
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}	
			
	@Column(name = "CRPT_NUM")	
	public Integer getCrptNum() {
		return crptNum;
	}

	public void setCrptNum(Integer crptNum) {
		this.crptNum = crptNum;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}