package com.chis.modules.heth.zzsb.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.logic.OutCarInstPO;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Description: 资质机构备案Service
 * 
 * @ClassAuthor pw,2022年04月14日,TdZwQualOrgApplyService
 */
@Service
@Transactional(readOnly = false, rollbackFor = Throwable.class)
public class TdZwQualOrgApplyService extends AbstractTemplate {

    @Transactional(readOnly = true)
    public <T> List<T> findEntityListByOtMainId(Class<T> t,String otMainIdParamName, Integer mainId) {
        if(StringUtils.isBlank(otMainIdParamName) || null == mainId){
            return Collections.EMPTY_LIST;
        }
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT T FROM ").append(t.getSimpleName()).append(" T WHERE ")
                .append("T.").append(otMainIdParamName).append(".rid = '").append(mainId).append("'");
        return findByHql(sb.toString(), t);
    }

    /**
     * @Description: 保存或更新机构相关备案信息
     * 附件删除 不考虑文件的删除避免文件删除了但事务回滚了的情况
     * 人员信息 仪器信息以及外检车辆那些 真保存单独处理
     * 当前方法还有检查项目开展情况、质量管理体系附件未处理 处理时加入并删除这一行注释
     * @MethodAuthor pw,2022年04月15日
     */
    public void saveOrUpdateTjorginfo(TdZwTjorginfoApply tjorginfoApply,
                                      List<Integer> itemRemoveRidList,
                                      List<TdZwTjorgannexApply> tjorgannexApplyList,
                                      List<Integer> annexApplyRemoveRidList,
                                      int type, Map<Integer, List<TdZwTjorgQualAnnexApy>> qualAnnexMap){
         if(null == tjorginfoApply){
            return;
        }
        List<Object> saveObjList = new ArrayList<>();
        List<Object> updateObjList = new ArrayList<>();
        //type 0 添加或者修改 1 初审 3 终审
        //修改备案主表
        preUpdate(tjorginfoApply);
        updateObjList.add(tjorginfoApply);
        if(0 == type){
            //新增或修改检查项目开展情况、质量管理体系附件
            //检查项目
            //通过职业健康检查机构服务项目rid集合删除检查项目
            String deleteByOrgIdSql = " DELETE FROM TD_ZW_TJITEMS_APPLY WHERE ORG_ID = "+tjorginfoApply.getRid();
            this.executeSql(deleteByOrgIdSql,null );
            if(!CollectionUtils.isEmpty(tjorginfoApply.getTjitemsApplies())){
                for(TdZwTjitemsApply tdZwTjitemsApply:tjorginfoApply.getTjitemsApplies()){
                    tdZwTjitemsApply.setRid(null);
                    saveObjList.add(tdZwTjitemsApply);
                }
            }
            //删除质量管理体系记录
            String deleteQualAnnexSql = " DELETE FROM TD_ZW_TJORG_QUAL_ANNEX_APY WHERE MAIN_ID ="+tjorginfoApply.getRid();
            this.executeSql(deleteQualAnnexSql,null );
            //保存-质量管理体系
            if(qualAnnexMap!=null&&!qualAnnexMap.isEmpty()){
                for (Map.Entry<Integer, List<TdZwTjorgQualAnnexApy>> integerListEntry : qualAnnexMap.entrySet()) {
                    for(TdZwTjorgQualAnnexApy qualAnnexApy:integerListEntry.getValue()){
                        if(qualAnnexApy.getIfHas()!=null&&0==qualAnnexApy.getIfHas()){
                            qualAnnexApy.setAnnexPath(null);
                        }
                        qualAnnexApy.setRid(null);
                        qualAnnexApy.setFkByMainId(tjorginfoApply);
                        preInsert(qualAnnexApy);
                    }
                    saveObjList.addAll(integerListEntry.getValue());
                }
            }
        }
        if(0 == type || 3 == type){
            //修改机构资质附件  资质附件不存在更新的情况
            if(!CollectionUtils.isEmpty(tjorgannexApplyList)){
                for(TdZwTjorgannexApply annexApply : tjorgannexApplyList){
                    if(null == annexApply.getRid()){
                        preInsert(annexApply);
                        saveObjList.add(annexApply);
                    }
                }
            }
        }

        //修改检查机构服务项目
        if(!CollectionUtils.isEmpty(tjorginfoApply.getTjorggitemsApplyList())){
            for(TdZwTjorggitemsApply itemApply : tjorginfoApply.getTjorggitemsApplyList()){
                //机构服务项目不存在更新的情况 更新忽略
                if(null == itemApply.getRid()){
                    preInsert(itemApply);
                    saveObjList.add(itemApply);
                }
            }
        }
        //新增或修改服务项目明细
        if(!CollectionUtils.isEmpty(tjorginfoApply.getTjorgItmDetailApies())){
            for(TdZwTjorgItmDetailApy detailApy : tjorginfoApply.getTjorgItmDetailApies()){
                //服务项目明细
                saveObjList.add(detailApy);
            }
        }

        //通过资质单位rig删除服务项目明细
        String deleteDetailByOrgIdSql = "  DELETE FROM TD_ZW_TJORG_ITM_DETAIL_APY WHERE RID IN (SELECT T.RID FROM TD_ZW_TJORG_ITM_DETAIL_APY T LEFT JOIN TD_ZW_TJORGGITEMS_APPLY T1 ON T.MAIN_ID = T1.RID WHERE T1.ORG_ID = "+tjorginfoApply.getRid() +")";
        this.executeSql(deleteDetailByOrgIdSql,null );
        //通过rid集合删除职业健康检查机构服务项目
        String deleteItemByRidSql = " DELETE FROM TD_ZW_TJORGGITEMS_APPLY WHERE RID IN (:ridList) ";
        //通过rid集合删除资质附件
        String deleteAnnexApplyByRidSql = " DELETE FROM TD_ZW_TJORGANNEX_APPLY WHERE RID IN (:ridList) ";
        Map<String,Object> paramMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(itemRemoveRidList)){
            List<List<Integer>> tmpList = StringUtils.splitListProxy(itemRemoveRidList, 1000);
            for(List<Integer> ridList : tmpList){
                paramMap.put("ridList", ridList);
                this.executeSql(deleteItemByRidSql,paramMap );
            }
        }
        if(!CollectionUtils.isEmpty(annexApplyRemoveRidList)){
            List<List<Integer>> tmpList = StringUtils.splitListProxy(annexApplyRemoveRidList, 1000);
            for(List<Integer> ridList : tmpList){
                paramMap.put("ridList", ridList);
                this.executeSql(deleteAnnexApplyByRidSql,paramMap );
            }
        }

        //保存对象
        if(!CollectionUtils.isEmpty(saveObjList)){
            this.saveBatchObjs(saveObjList);
        }
        //更新对象
        if(!CollectionUtils.isEmpty(updateObjList)){
            this.updateBatchObjs(updateObjList);
        }
    }

    /**
     * 获取人员汇总
     *
     * @param orgId 备案机构RID
     * @return 人员汇总
     */
    public List<Object[]> findPsnTypeSummary(Integer orgId) {
        if (null == orgId) {
            return null;
        }
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT T.RID,T.CODE_NAME,COUNT(CASE WHEN T4.RID IS NOT NULL THEN T3.rid ELSE NULL END) ");
        sql.append(" FROM TS_SIMPLE_CODE T");
        sql.append(" LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID");
        sql.append(" LEFT JOIN TD_ZW_PSN_TYPE T2 ON T2.PSN_TYPE = T.rid");
        sql.append(" LEFT JOIN TD_ZW_PSNINFO T3 ON T3.RID= T2.MAIN_ID");
        sql.append(" LEFT JOIN TD_ZW_TJORGPSNS_APPLY T4 ON T4.EMP_ID= T3.RID ");
        sql.append(" AND T4.ORG_ID =").append(orgId);
        sql.append(" WHERE T1.code_type_name = '5308' and t.IF_REVEAL =1");
        sql.append(" GROUP BY t.rid,t.CODE_NAME,t.num,t.CODE_NO");
        sql.append(" ORDER BY t.num,t.CODE_NO ");
        return this.findSqlResultList(sql.toString());
    }

    /**
     * 查询人员信息
     *
     * @param orgType 备案审核类型
     * @param orgId   机构RID
     * @param psnId   人员RID
     * @return 人员信息
     */
    @Transactional(readOnly = true)
    public List<Object[]> findPersonnelListByOrgId(Integer orgType, Integer orgId, Integer psnId) {
        Map<String, Object> paramMap = new HashMap<>(16);
        psnId = psnId == null ? -1 : psnId;
        StringBuilder sql = new StringBuilder();
        sql.append("WITH GENERAL_TABLE AS ( ")
                .append("    SELECT T.RID P0, T.EMP_NAME P1, T.SEX P2, T.TITLE_ID P3, SC.CODE_NAME P4, OP.RID P5 ")
                .append("    FROM TD_ZW_PSNINFO T ")
                .append("        INNER JOIN TD_ZW_TJORGPSNS_APPLY OP ON T.RID = OP.EMP_ID ")
                .append("        LEFT JOIN TS_SIMPLE_CODE SC ON T.TITLE_ID = SC.RID ")
                //.append("        LEFT JOIN TD_ZW_PSN_TYPE PT ON T.RID = PT.MAIN_ID AND PT.ZZJG_TYPE = :orgType ")
                .append("    WHERE OP.ORG_ID = :orgId ")
                .append(psnId != -1 ? " AND T.RID = :psnId " : "")
                .append("), O_PERSONNEL_ATTRIBUTES_TABLE AS ( ")
                .append("    SELECT T.P0, ")
                .append("           CASE ")
                .append("               WHEN SC.EXTENDS3 = 4 THEN SC.CODE_NAME || '(' || PT.OTHER_PSN_TYPE || ')' ")
                .append("               ELSE SC.CODE_NAME ")
                .append("               END AS P1, ")
                .append("           SC.NUM  AS P2 ")
                .append("    FROM GENERAL_TABLE T ")
                .append("             LEFT JOIN TD_ZW_PSN_TYPE PT ON T.P0 = PT.MAIN_ID AND PT.ZZJG_TYPE = :orgType ")
                .append("             LEFT JOIN TS_SIMPLE_CODE SC ON PT.PSN_TYPE = SC.RID ")
                .append("), PERSONNEL_ATTRIBUTES_TABLE AS ( ")
                .append("    SELECT T.P0, LISTAGG(T.P1, '#@，@#') WITHIN GROUP ( ORDER BY T.P2) AS P1 ")
                .append("    FROM O_PERSONNEL_ATTRIBUTES_TABLE T ")
                .append("    GROUP BY T.P0 ")
                .append("), ORIGINAL_CERTIFICATE_TABLE AS ( ")
                .append("    SELECT T.P0, PZ.RID, PZ.CENT_NO AS P1, PZ.SEND_DATE AS P2 ")
                .append("    FROM GENERAL_TABLE T ")
                .append("        LEFT JOIN TD_ZW_PSN_ZZLB PZ ON T.P0 = PZ.PSN_ID ")
                .append("        INNER JOIN TS_SIMPLE_CODE SC ")
                .append("            ON PZ.QUAL_ID = SC.RID AND NVL(SC.CODE_DESC, :orgType) LIKE :orgType ")
                .append("), CERTIFICATE_TABLE AS ( ")
                .append("    SELECT T.P0, LISTAGG(T.P1, '#@，@#') WITHIN GROUP ( ORDER BY T.P2 DESC) AS P1 ")
                .append("    FROM ORIGINAL_CERTIFICATE_TABLE T ")
                .append("    GROUP BY T.P0 ")
                .append("), PROJECT_TABLE AS ( ")
                .append("    SELECT T.P0, LISTAGG(SC.CODE_NAME, '#@，@#') WITHIN GROUP ( ORDER BY SC.NUM) AS P1 ")
                .append("    FROM ORIGINAL_CERTIFICATE_TABLE T ")
                .append("        LEFT JOIN TD_ZW_ZZLB_RANGE ZR ON ZR.MAIN_ID = T.RID ")
                .append("        LEFT JOIN TS_SIMPLE_CODE SC ON ZR.ZZ_RANGE_ID = SC.RID ")
                .append("    GROUP BY T.P0 ")
                .append("), DEPARTMENTS_TABLE AS ( ")
                .append("    SELECT T.P5, LISTAGG(SC.CODE_NAME, '#@，@#') WITHIN GROUP ( ORDER BY SC.CODE_NO) AS P1 ")
                .append("    FROM GENERAL_TABLE T ")
                .append("        LEFT JOIN TD_ZW_WORKDEPT_APPLY WE ON T.P5 = WE.EMP_ID ")
                .append("        LEFT JOIN TS_SIMPLE_CODE SC ON WE.DEPT_ID = SC.RID ")
                .append("    GROUP BY T.P5 ")
                .append("), DEPARTMENTS_TABLE1 AS ( ")
                .append("    SELECT T.P5, LISTAGG(SC.RID, '#@，@#') WITHIN GROUP ( ORDER BY SC.CODE_NO) AS P1 ")
                .append("    FROM GENERAL_TABLE T ")
                .append("             LEFT JOIN TD_ZW_WORKDEPT_APPLY WE ON T.P5 = WE.EMP_ID ")
                .append("             LEFT JOIN TS_SIMPLE_CODE SC ON WE.DEPT_ID = SC.RID ")
                .append("    GROUP BY T.P5 ")
                .append(") ")
                .append("SELECT T.P0, T.P1, T.P2, T.P3, T.P4, T.P5, PAT.P1 AS P6, CT.P1 AS P7, PT.P1 AS P8, DT.P1 AS P9, DT1.P1 AS P10 ")
                .append("FROM GENERAL_TABLE T ")
                .append("    LEFT JOIN PERSONNEL_ATTRIBUTES_TABLE PAT ON T.P0 = PAT.P0 ")
                .append("    LEFT JOIN CERTIFICATE_TABLE CT ON T.P0 = CT.P0 ")
                .append("    LEFT JOIN PROJECT_TABLE PT ON T.P0 = PT.P0 ")
                .append("    LEFT JOIN DEPARTMENTS_TABLE DT ON T.P5 = DT.P5 ")
                .append("    LEFT JOIN DEPARTMENTS_TABLE1 DT1 ON T.P5 = DT1.P5 ")
                .append("ORDER BY NLSSORT(T.P1, 'NLS_SORT = SCHINESE_PINYIN_M') ");
        paramMap.put("orgType", orgType);
        paramMap.put("orgId", orgId);
        if (psnId != -1) {
            paramMap.put("psnId", psnId);
        }
        return findDataBySqlNoPage(sql.toString(), paramMap);
    }

    /**
     * 获取人员信息
     *
     * @param psnId 人员RID
     * @return 人员信息
     */
    @Transactional(readOnly = true)
    public TdZwPsninfoZzsb findTdZwPsnInfo(Integer psnId) {
        TdZwPsninfoZzsb psn=new TdZwPsninfoZzsb();
        StringBuilder sb = new StringBuilder();
        sb.append("select t from TdZwPsninfoZzsb t where t.rid = ").append(psnId);
        List<TdZwPsninfoZzsb> list = em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            psn=list.get(0);
            psn.getTjMainList().size();
            psn.getZzlbList().size();
            if(null !=psn.getZzlbList() && psn.getZzlbList().size()>0){
                for(TdZwPsnZzlbZzsb zzlb:psn.getZzlbList()){
                    zzlb.getRangeList().size();
                }
            }
        }
        return psn;
    }

    /**
     * 删除机构人员
     *
     * @param orgPsnApplyId 机构人员RID
     */
    @Transactional(rollbackFor = Exception.class)
    public void delOrgPsnApply(Integer orgPsnApplyId) {
        delPsnWorkOfficeList(orgPsnApplyId);
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("orgPsnApplyId", orgPsnApplyId);
        String delSql = "DELETE TD_ZW_TJORGPSNS_APPLY WHERE RID = :orgPsnApplyId";
        executeSql(delSql, paramMap);
    }

    /**
     * 保存机构人员从事科室
     *
     * @param orgPsnApplyId     机构人员
     * @param workDeptApplyList 科室列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePsnWorkOfficeList(Integer orgPsnApplyId, List<TdZwWorkdeptApply> workDeptApplyList) {
        delPsnWorkOfficeList(orgPsnApplyId);
        saveBatchObjs(workDeptApplyList);
    }

    /**
     * 删除机构人员从事科室
     *
     * @param orgPsnApplyId 机构人员
     */
    @Transactional(rollbackFor = Exception.class)
    public void delPsnWorkOfficeList(Integer orgPsnApplyId) {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("orgPsnApplyId", orgPsnApplyId);
        String delSql = "DELETE TD_ZW_WORKDEPT_APPLY WHERE EMP_ID = :orgPsnApplyId";
        executeSql(delSql, paramMap);
    }

    /**
     * 获取机构仪器信息
     *
     * @param rid 机构RID
     * @return 机构仪器信息
     */
    @Transactional(readOnly = true)
    public List<Object[]> findInstrumentListByOrgId(Integer rid) {
        Map<String, Object> paramMap = new HashMap<>(16);
        String sql = "" +
                " SELECT " +
                "   OP.RID P0, " +
                "   T.RID P1, " +
                "   T.INST_NAME P2, " +
                "   T.INST_MODEL P3, " +
                "   T.INST_CODE P4, " +
                "   T.LAST_ACPT_DATE P5, " +
                "   T.LAST_ACPT_CIRCLE_DOT P6, " +
                "   NVL(T.OUTTER, 0) P7, " +
                "   NVL(T.IF_SUPT_OUT_CHK, 0) P8 " +
                " FROM TD_ZW_INSTINFO T INNER JOIN TD_ZW_TJINST_APPLY OP ON T.RID = OP.INST_ID " +
                " WHERE OP.ORG_ID = :rid " +
                " ORDER BY NLSSORT(T.INST_NAME, 'NLS_SORT = SCHINESE_PINYIN_M') ";
        paramMap.put("rid", rid);
        return findDataBySqlNoPage(sql, paramMap);
    }

    /**
     * 删除机构仪器
     *
     * @param rid 机构仪器ApplyRID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrgInstApply(Integer rid) {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("rid", rid);
        String delSql = "DELETE TD_ZW_TJINST_APPLY WHERE RID = :rid ";
        executeSql(delSql, paramMap);
    }

    /**
     * 添加机构仪器
     *
     * @param orgRid  机构RID
     * @param instRid 仪器RID
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertOrgInstApply(Integer orgRid, Integer instRid) {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("orgRid", orgRid);
        paramMap.put("instRid", instRid);
        paramMap.put("createDate", DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        paramMap.put("createManid", Global.getUser().getRid());
        String delSql = "" +
                "INSERT INTO TD_ZW_TJINST_APPLY " +
                "SELECT TD_ZW_TJINST_APPLY_SEQ.NEXTVAL RID, T.* " +
                "FROM (SELECT :orgRid ORG_ID, :instRid INST_ID, TO_DATE(:createDate, 'YYYY-MM-DD HH24:MI:SS') CREATE_DATE, :createManid CREATE_MANID " +
                "      FROM DUAL) T " +
                " ";
        return executeSql(delSql, paramMap);
    }

    /**
     * 获取机构仪器信息
     *
     * @param searchInfo 查询信息
     * @param orgId 备案机构RID
     * @return 机构仪器信息
     */
    @Transactional(readOnly = true)
    public List<Object[]> findInstInfoListBySearchInfo(String[] searchInfo, Integer orgId) {
        Map<String, Object> paramMap = new HashMap<>(16);
        String sql = "" +
                " SELECT * FROM ( " +
                " SELECT " +
                "   1 P0, " +
                "   T.RID P1, " +
                "   T.INST_NAME P2, " +
                "   T.INST_MODEL P3, " +
                "   T.INST_CODE P4, " +
                "   NVL(T.OUTTER, 0) P5, " +
                "   NVL2(OP.RID, 1, 0) P6 " +
                " FROM TD_ZW_INSTINFO T LEFT JOIN TD_ZW_TJINST_APPLY OP ON T.RID = OP.INST_ID AND OP.ORG_ID = :orgId " +
                " WHERE T.ORG_ID = :unitRid ";
        paramMap.put("unitRid", Global.getUser().getTsUnit());
        paramMap.put("orgId", orgId);
        if (StringUtils.isNotBlank(searchInfo[0])) {
            sql += " AND T.INST_NAME like :instName escape '\\\' ";
            paramMap.put("instName", "%"+StringUtils.convertBFH(searchInfo[0].trim()) + "%");
        }
        if (StringUtils.isNotBlank(searchInfo[1])) {
            sql += " AND T.INST_MODEL like :instModel escape '\\\' ";
            paramMap.put("instModel", "%"+StringUtils.convertBFH(searchInfo[1].trim()) + "%");
        }
        sql += " ) ";
        sql += " GROUP BY P0, P1, P2, P3, P4, P5, P6 ";
        sql += " ORDER BY NLSSORT(P2, 'NLS_SORT = SCHINESE_PINYIN_M') ";
        return findDataBySqlNoPage(sql, paramMap);
    }

    /**
     * 根据RID获取仪器信息
     *
     * @param rid 仪器RID
     * @return 仪器信息
     */
    public TdZwInstinfoZzsb findInstInfoByRid(Integer rid) {
        return find(TdZwInstinfoZzsb.class, rid);
    }

    /**
     * 根据仪器RID、机构单位RID、仪器编码找出机构下非仪器RID的指定仪器编码的仪器
     *
     * @param instCode 仪器编码
     * @param instRid  仪器RID
     * @return 仪器数
     */
    public int findExistsInstNo(String instCode, Integer instRid) {
        Map<String, Object> paramMap = new HashMap<>(16);
        String sql = "" +
                " SELECT 1 from TD_ZW_INSTINFO T " +
                " WHERE T.INST_CODE =:instCode AND T.ORG_ID = :unitRid ";
        if (instRid != null) {
            sql += " AND T.RID != :instRid ";
            paramMap.put("instRid", instRid);
        }
        paramMap.put("instCode", instCode);
        paramMap.put("unitRid", Global.getUser().getTsUnit().getRid());
        List list = findDataBySqlNoPage(sql, paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        } else {
            return list.size();
        }
    }

    /**
     * 保存或添加仪器信息
     *
     * @param instInfo 仪器信息
     */
    public void saveOrUpdateInstInfo(TdZwInstinfoZzsb instInfo) {
        if (instInfo.getRid() == null) {
            saveObj(instInfo);
        } else {
            updateObj(instInfo);
        }
    }

    /**
     * 删除仪器
     *
     * @param rid 仪器RID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteInstInfo(Integer rid) {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("rid", rid);
        String delSql = "DELETE TD_ZW_INSTINFO WHERE RID = :rid ";
        executeSql(delSql, paramMap);
    }

    /**
     * <p>方法描述：外检车辆列表初始化</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-18
     **/
    public List<Object[]> getOutcarApplyList(Integer rid){
        if(rid==null){
            return null;
        }
        StringBuilder sql=new StringBuilder();
        sql.append(" with table1 as ( ");
        sql.append(" select T.RID,T.CAR_NO,T.CAR_BAND,T.CAR_STYLE,T.CAR_FRAME_NO,T.PRODUCTION_DATE,T.STATE_MARK,T.MAIN_ID, T.DRIVING_ANNEX_PATH, T.CAR_PHOTO_ANNEX_PATH, T.CAR_DR_ANNEX_PATH ");
        sql.append(" from TD_ZW_TJORG_OUTCAR_APPLY T ");
        sql.append(" LEFT join TD_ZW_TJORGINFO_APPLY T1 on T.MAIN_ID=T1.RID ");
        sql.append(" ),table2 as( ");
        sql.append(" select T.RID,listagg(T3.INST_NAME,'，') within group (order by T3.INST_NAME ) as INST_NAME ");
        sql.append(" from TD_ZW_TJORG_OUTCAR_APPLY T ");
        sql.append(" left join TD_ZW_TJORG_OUTC_INST_APY T2 on T.RID=T2.CAR_ID ");
        sql.append(" left join TD_ZW_INSTINFO T3 on T2.INST_ID=T3.RID ");
        sql.append(" group by T.RID )");
        sql.append("  select a.RID, a.CAR_NO, a.CAR_BAND, a.CAR_STYLE, a.CAR_FRAME_NO, a.PRODUCTION_DATE, a.STATE_MARK, a.MAIN_ID,b.INST_NAME, a.DRIVING_ANNEX_PATH, a.CAR_PHOTO_ANNEX_PATH, a.CAR_DR_ANNEX_PATH from  table1 a left join table2 b on a.RID=b.RID ");
        sql.append(" where a.MAIN_ID= ").append(rid);
        sql.append(" order by a.CAR_NO ");
        return em.createNativeQuery(sql.toString()).getResultList();

    }

    /**
     * <p>方法描述：获取附件列表</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public List<Object[]> findAnnexList(Integer orgApplyRid) {

        StringBuilder sql=new StringBuilder();
        sql.append("select T.rid,T.FILE_NAME,T.ANNEX_TYPE,T.FILE_PATH,T1.ZONE_NAME, ");
        sql.append(" case when T.UNIT_LEVEL=2 then '省级' when T.UNIT_LEVEL=3 then '市级' else '区县级' end as levelName,T.UNIT_NAME,REG_ZONE_ID,T.UNIT_LEVEL ");
        sql.append(" from TD_ZW_TJORGANNEX_APPLY T ");
        sql.append(" left join TS_ZONE T1 on T.REG_ZONE_ID=T1.RID ");
        sql.append(" where T.ORG_ID=").append(orgApplyRid);
        sql.append(" order by  T.ANNEX_TYPE,T.FILE_NAME ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * <p>方法描述：删除资质附件</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-20
     **/
    public void deleteAnnexRec(Integer rid) {
        String sql="delete from TD_ZW_TJORGANNEX_APPLY where rid="+rid;
        em.createNativeQuery(sql).executeUpdate();
    }

    /**
     * <p>方法描述：保存许可证信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-21
     **/
    public void saveYlAnnex(TdZwTjorgannexApply ylAnnex) {
        this.saveObj(ylAnnex);
    }


    /**
     * <p>方法描述：保存/更新 外检车辆</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void insetUpOutCarRec(Integer orgId,TdZwTjorgOutcarApply outcarZzsb, List<Integer> outCarInstSelectRidList) {
        List<TdZwTjorgOutcInstApy> tdZwTjorgOutcInstApyList=new ArrayList<>();
        if(outcarZzsb.getRid()==null){
            //保存操作
            outcarZzsb.setFkByMainId(new TdZwTjorginfoApply(orgId));
            outcarZzsb.setCreateDate(new Date());
            outcarZzsb.setCreateManid(Global.getUser().getRid());
            this.saveObj(outcarZzsb);
        }else{
            //更新操作
            this.updateObj(outcarZzsb);
            //删除中间表
            String sql="delete from TD_ZW_TJORG_OUTC_INST_APY where CAR_ID="+outcarZzsb.getRid();
            em.createNativeQuery(sql).executeUpdate();
        }
        if(!CollectionUtils.isEmpty(outCarInstSelectRidList)){
            for(Integer rid:outCarInstSelectRidList){
                TdZwTjorgOutcInstApy tdZwTjorgOutcInstApy=new TdZwTjorgOutcInstApy();
                tdZwTjorgOutcInstApy.setFkByCarId(outcarZzsb);
                tdZwTjorgOutcInstApy.setFkByInstId(new TdZwInstinfoZzsb(rid));
                tdZwTjorgOutcInstApy.setCreateDate(new Date());
                tdZwTjorgOutcInstApy.setCreateManid(Global.getUser().getRid());
                tdZwTjorgOutcInstApyList.add(tdZwTjorgOutcInstApy);
            }
        }
        //批量保存中间表
        if(!CollectionUtils.isEmpty(tdZwTjorgOutcInstApyList)){
            this.saveBatchObjs(tdZwTjorgOutcInstApyList);
        }
    }

    /**
     * <p>方法描述：删除外键车辆信息</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public void deleteOutCarRec(Integer rid) {
        //删除关联表
        String sql="delete from TD_ZW_TJORG_OUTC_INST_APY where CAR_ID="+rid;
        em.createNativeQuery(sql).executeUpdate();
        //删除外检车辆表
        String sql1="delete from TD_ZW_TJORG_OUTCAR_APPLY where rid="+rid;
        em.createNativeQuery(sql1).executeUpdate();
    }

    /**
     * <p>方法描述：通过rid获取外检车辆记录-Hql</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public TdZwTjorgOutcarApply findOutcarApply(Integer rid) {
        TdZwTjorgOutcarApply outcarZzsb=this.find(TdZwTjorgOutcarApply.class,rid);
        if(outcarZzsb!=null){
            outcarZzsb.getOutcInstZzsbList().size();
        }
        return outcarZzsb;
    }


    /**
     * <p>方法描述：获取仪器信息列表</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-19
     **/
    public List<OutCarInstPO> findInstInfoByOrgRid(Integer rid, String searchOutCarInstName, String searchOutCarModel) {
        List<OutCarInstPO> outCarInstPOList = new ArrayList<>();
        Map<java.lang.String, Object> paramMap = new HashMap<java.lang.String, Object>();
        StringBuilder sql=new StringBuilder();
        sql.append(" select T.rid,T2.INST_NAME,T2.INST_MODEL,T2.INST_CODE,T2.rid as instRid ");
        sql.append(" from TD_ZW_TJINST_APPLY T " );
        sql.append(" left join TD_ZW_INSTINFO T2 on T.INST_ID=T2.RID " );
        sql.append(" where T.ORG_ID= ").append(rid);

        if(StringUtils.isNotBlank(searchOutCarInstName)){
            sql.append("AND T2.INST_NAME LIKE :INST_NAME  ");
            paramMap.put("INST_NAME", "%"+searchOutCarInstName+"%");
        }
        if(StringUtils.isNotBlank(searchOutCarModel)){
            sql.append("AND T2.INST_MODEL LIKE :INST_MODEL  ");
            paramMap.put("INST_MODEL", "%"+searchOutCarModel+"%");
        }
        List<Object[]> list = this.findDataBySqlNoPage(sql.toString(),paramMap);
        if (!CollectionUtils.isEmpty(list)) {
            for (Object[] obj : list) {
                OutCarInstPO outCarInstPO = new OutCarInstPO();
                outCarInstPO.setRid(Integer.valueOf(obj[0].toString()));
                outCarInstPO.setInstName(obj[1].toString());
                outCarInstPO.setInstModel(obj[2] == null ? null : obj[2].toString());
                outCarInstPO.setInstCode(obj[3] == null ? null : obj[3].toString());
                outCarInstPO.setSelected(Boolean.FALSE);
                outCarInstPO.setInstRid(Integer.valueOf(obj[4].toString()));
                outCarInstPOList.add(outCarInstPO);
            }
        }
        return outCarInstPOList;
    }
    /**
     *  <p>方法描述：终审操作</p>
     *  orgType：资质类型
     *  tjorginfoApply：资质对象
     * @MethodAuthor hsj 2022/4/24 18:32
     */
    public void finalAuditInfo(Integer orgType, TdZwTjorginfoApply tjorginfoApply) {
        if (null == tjorginfoApply) {
            return;
        }
        StringBuffer updateBuffer = new StringBuffer();
        //更新状态 终审日期 终审机构ID 终审人员ID
        updateBuffer.append(" UPDATE  TD_ZW_TJORGINFO_APPLY  SET STATE = 5 ")
                .append(",FINAL_CHK_DATE=TO_DATE('").append(DateUtils.formatDate(tjorginfoApply.getFinalChkDate())).append("','yyyy-MM-dd')")
                .append(",FINAL_CHK_ORG_ID=").append(tjorginfoApply.getFkByFinalChkOrgId().getRid())
                .append(",FINAL_CHK_PSN_ID=").append(tjorginfoApply.getFkByFinalChkPsnId().getRid());
        updateBuffer.append(" WHERE RID = ").append(tjorginfoApply.getRid());
        this.executeSql(updateBuffer.toString(),null);
        //数据的同步
        saveOrUpdateBase(orgType,tjorginfoApply);
    }
    /**
     *  <p>方法描述：数据的封装</p>
     * @MethodAuthor hsj 2022/4/25 8:59
     */
    private void saveOrUpdateBase(Integer orgType, TdZwTjorginfoApply tjorginfoApply) {
        //基本信息封装处理
        IZwOrginfoZzsb zwOrginfoZzsb = getIZwOrginfoZzsb(tjorginfoApply,orgType);
        Integer orgId = null;
        List<Object> saveObjList = new ArrayList<>();
        List<Object> updateObjList = new ArrayList<>();
        if(1 == orgType){
            orgId = zwOrginfoZzsb.getTsUnit().getRid();
            //根据orgId查询是否存在
            List<TdZwTjorginfoZzsb> tdZwTjorginfoList = findEntityListByOtMainId(TdZwTjorginfoZzsb.class,
                    "tsUnit", orgId);
            TdZwTjorginfoZzsb tdZwTjorginfo = (TdZwTjorginfoZzsb) zwOrginfoZzsb;
            if(!CollectionUtils.isEmpty(tdZwTjorginfoList)){
                tdZwTjorginfo.setRid(tdZwTjorginfoList.get(0).getRid());
                tdZwTjorginfo.setCreateDate(tdZwTjorginfoList.get(0).getCreateDate());
                tdZwTjorginfo.setCreateManid(tdZwTjorginfoList.get(0).getCreateManid());
                tdZwTjorginfo.setTdZwTjorgpsnses(tdZwTjorginfoList.get(0).getTdZwTjorgpsnses());
            }
            //新增/更新基本信息
            upsertEntityNoMod(tdZwTjorginfo);
            updateUint(orgId,tjorginfoApply.getFkByZoneId());
            //人员信息、从事科室封装处理
            saveOrUpdatePsns(tjorginfoApply,saveObjList, tdZwTjorginfo,zwOrginfoZzsb);
            //检查仪器的封装处理
            saveOrUpdateInst(tjorginfoApply,saveObjList, tdZwTjorginfo,null,orgType);
            //检查项目
            saveOrUpdateItems(tjorginfoApply,saveObjList,tdZwTjorginfo);
            //服务明细
            saveOrUpdateItmDetail(tjorginfoApply,saveObjList,tdZwTjorginfo);
            //外检车辆
            saveOrUpdateOutcar(tjorginfoApply,saveObjList,tdZwTjorginfo);
            //质量管理体系
            saveOrUpdateQualAnnex(tjorginfoApply,saveObjList,tdZwTjorginfo);
        }else if(2 == orgType){
            orgId = zwOrginfoZzsb.getTsUnit().getRid();
            List<TdZwDiagorginfoZzsb> tdZwTjorginfoList = findEntityListByOtMainId(TdZwDiagorginfoZzsb.class,
                    "tsUnit", orgId);
            TdZwDiagorginfoZzsb  tdZwDiagorginfo = (TdZwDiagorginfoZzsb) zwOrginfoZzsb;
            if(!CollectionUtils.isEmpty(tdZwTjorginfoList)){
                tdZwDiagorginfo.setRid(tdZwTjorginfoList.get(0).getRid());
                tdZwDiagorginfo.setCreateDate(tdZwTjorginfoList.get(0).getCreateDate());
                tdZwDiagorginfo.setCreateManid(tdZwTjorginfoList.get(0).getCreateManid());
                tdZwDiagorginfo.setTdZwDiagpsnses(tdZwTjorginfoList.get(0).getTdZwDiagpsnses());
            }
            //新增/更新基本信息
            upsertEntityNoMod(tdZwDiagorginfo);
            updateUint(orgId,tjorginfoApply.getFkByZoneId());
            //人员信息、从事科室封装处理
//            saveOrUpdatePsnsByDiagorg(tjorginfoApply,saveObjList, tdZwDiagorginfo);
            //检查仪器的封装处理
            saveOrUpdateInst(tjorginfoApply,saveObjList, null,tdZwDiagorginfo,orgType);
        }
        //资质附件封装处理
        saveOrUpdateAnnex(tjorginfoApply, saveObjList,orgType);
        //保存对象
        if(!CollectionUtils.isEmpty(saveObjList)){
            this.saveBatchObjs(saveObjList);
        }
        //更新对象
        if(!CollectionUtils.isEmpty(updateObjList)){
            this.updateBatchObjs(updateObjList);
        }
    }
    /**
     *  <p>方法描述：人员信息</p>
     * @MethodAuthor hsj 2022/4/25 17:40
     */
    private void saveOrUpdatePsns(TdZwTjorginfoApply tjorginfoApply, List<Object> saveObjList, TdZwTjorginfoZzsb tdZwTjorginfo, IZwOrginfoZzsb zwOrginfoZzsb) {
        String extendS1 =tjorginfoApply.getFkByApplyTypeId().getExtendS1();
        List<TdZwTjorgpsnsApply> tjorgpsnsApplyList = tjorginfoApply.getTjorgpsnsApplyList();
        //需要删除的人员
        List<Integer> delPsnIds = new ArrayList<>();
        //需要更新为在职人员
        List<Integer> updatePsnIds = new ArrayList<>();
        //需要新增在职人员
        List<Integer> addPsnIds = new ArrayList<>();
        //需要删除的科室
        List<Integer> delDeptPsnIds = new ArrayList<>();
        //申报的人员信息
        List<TdZwTjorgpsnsZzsb> psnList = new ArrayList<>();
        if("2".equals(extendS1)){
            psnList = tdZwTjorginfo.getTdZwTjorgpsnses();
        }
        if(!CollectionUtils.isEmpty(tjorgpsnsApplyList)){
            //人员新增对象(数据的封装)
            List<TdZwTjorgpsnsZzsb> psnLists = new ArrayList<>();
            Map<Integer,TdZwTjorgpsnsZzsb> psnsMap = new HashMap<>();
            Map<Integer,List<TdZwWorkdeptZzsb>> deptMap = new HashMap<>();
            for(TdZwTjorgpsnsApply psnsApply:tjorgpsnsApplyList){
                TdZwTjorgpsnsZzsb zwTjorgpsnsZzsb = new TdZwTjorgpsnsZzsb();
                zwTjorgpsnsZzsb.setTdZwTjorginfo(tdZwTjorginfo);
                zwTjorgpsnsZzsb.setTdZwPsninfo(psnsApply.getFkByEmpId());
                zwTjorgpsnsZzsb.setOnDuty("1");
                psnLists.add(zwTjorgpsnsZzsb);
                psnsMap.put(psnsApply.getFkByEmpId().getRid(),zwTjorgpsnsZzsb);
                //科室
                List<TdZwWorkdeptApply> zwWorkdeptApplies = findEntityListByOtMainId(TdZwWorkdeptApply.class,
                                "fkByEmpId", psnsApply.getRid());
                if(!CollectionUtils.isEmpty(zwWorkdeptApplies)){
                    List<TdZwWorkdeptZzsb> zwWorkdeptZzsbs = new ArrayList<>();
                    for(TdZwWorkdeptApply zwWorkdeptApply:zwWorkdeptApplies){
                        TdZwWorkdeptZzsb zwWorkdeptZzsb = new TdZwWorkdeptZzsb();
                        zwWorkdeptZzsb.setTdZwTjorgpsns(zwTjorgpsnsZzsb);
                        zwWorkdeptZzsb.setTsSimpleCode(zwWorkdeptApply.getFkByDeptId());
                        zwWorkdeptZzsb.setCreateDate(new Date());
                        zwWorkdeptZzsb.setCreateManid(Global.getUser().getRid());
                        zwWorkdeptZzsbs.add(zwWorkdeptZzsb);
                    }
                    deptMap.put(psnsApply.getFkByEmpId().getRid(),zwWorkdeptZzsbs);
                }
            }

            if(!CollectionUtils.isEmpty(psnList)){
                //当原来已存在时
                //已处理过的人员
                List<Integer> psnRids =new ArrayList<>();
                //原来的数数据和现在的数据对比
                for(TdZwTjorgpsnsZzsb zwTjorgpsnsZzsb:psnList){
                    TdZwTjorgpsnsZzsb tjorgpsnsZzsb =  psnsMap.get(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid());
                    if(null == tjorgpsnsZzsb || null == tjorgpsnsZzsb.getTdZwPsninfo() || null == tjorgpsnsZzsb.getTdZwPsninfo().getRid()){
                        //变更人员不存在
                        if("1".equals(zwTjorgpsnsZzsb.getOnDuty())){
                            //当前人员在职,删除
                            delPsnIds.add(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid());
                            delDeptPsnIds.add(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid());
                        }
                    }else{
                        psnRids.add(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid());
                        //变更人员存在
                        //科室删除
                        delDeptPsnIds.add(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid());
                        if(!"1".equals(zwTjorgpsnsZzsb.getOnDuty())){
                            //当前人员离职，改为在职
                            addPsnIds.add(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid());
                            updatePsnIds.add(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid());
                        }
                        //科室新增
                        if(null != deptMap && !CollectionUtils.isEmpty(deptMap.get(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid()))){
                            for(TdZwWorkdeptZzsb dept:deptMap.get(zwTjorgpsnsZzsb.getTdZwPsninfo().getRid())){
                                dept.setTdZwTjorgpsns(zwTjorgpsnsZzsb);
                                saveObjList.add(dept);
                            }
                        }
                    }
                }
                for(TdZwTjorgpsnsZzsb psn : psnLists){
                    if(!psnRids.contains(psn.getTdZwPsninfo().getRid())){
                        //原来不存在，再次变更中存在，新增
                        addPsnIds.add(psn.getTdZwPsninfo().getRid());
                        saveObjList.add(psn);
                        if(null != deptMap && !CollectionUtils.isEmpty(deptMap.get(psn.getTdZwPsninfo().getRid()))){
                            for(TdZwWorkdeptZzsb dept:deptMap.get(psn.getTdZwPsninfo().getRid())){
                                dept.setTdZwTjorgpsns(psn);
                                saveObjList.add(dept);
                            }
                        }
                    }

                }
            }else{
                //原来不存在时
                for(TdZwTjorgpsnsZzsb psn : psnLists){
                    addPsnIds.add(psn.getTdZwPsninfo().getRid());
                    saveObjList.add(psn);
                    if(null != deptMap && !CollectionUtils.isEmpty(deptMap.get(psn.getTdZwPsninfo().getRid()))){
                        for(TdZwWorkdeptZzsb dept:deptMap.get(psn.getTdZwPsninfo().getRid())){
                            dept.setTdZwTjorgpsns(psn);
                            saveObjList.add(dept);
                        }
                    }

                }
            }
        }else {
            //申请为空时,删除原来的数据
            if(!CollectionUtils.isEmpty(psnList)){
               for(TdZwTjorgpsnsZzsb zwTjorgpsnsZzsb:psnList){
                   delPsnIds.add(zwTjorgpsnsZzsb.getRid());
                   delDeptPsnIds.add(zwTjorgpsnsZzsb.getRid());
               }
            }
        }
        //数据删除或更新
        if(!CollectionUtils.isEmpty(delDeptPsnIds)){
            //删除科室
            String delItmByEmpIdSql = " DELETE FROM TD_ZW_WORKDEPT WHERE EMP_ID IN (SELECT RID FROM TD_ZW_TJORGPSNS WHERE EMP_ID IN (:delDeptPsnIds) )" ;
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("delDeptPsnIds", delDeptPsnIds);
            this.executeSql(delItmByEmpIdSql,paramMap );
        }
        if(!CollectionUtils.isEmpty(delPsnIds)){
            //删除人员
            String delItmByIdSql = " DELETE FROM TD_ZW_TJORGPSNS WHERE  EMP_ID IN (:delPsnIds)";
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("delPsnIds", delPsnIds);
            this.executeSql(delItmByIdSql,paramMap );
            //人员orgId:判断4类资质中是否存在，不存在清空
            clearPsnInfoOrgId(delPsnIds,zwOrginfoZzsb,tdZwTjorginfo.getTsUnit().getRid());
        }
        if(!CollectionUtils.isEmpty(updatePsnIds)){
            //更新人员
            String updateIdByIdSql = " UPDATE  TD_ZW_TJORGPSNS  SET ON_DUTY = 1 WHERE EMP_ID IN (:updatePsnIds)";
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("updatePsnIds", updatePsnIds);
            this.executeSql(updateIdByIdSql,paramMap );
        }
        if(!CollectionUtils.isEmpty(addPsnIds)){
            //新增人员，需更新orgId
            String updateIdByIdSql = " UPDATE  TD_ZW_PSNINFO  SET ORG_ID =:orgId  WHERE RID IN (:addPsnIds) AND ORG_ID IS  NULL";
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("orgId", tdZwTjorginfo.getTsUnit().getRid());
            paramMap.put("addPsnIds", addPsnIds);
            this.executeSql(updateIdByIdSql,paramMap );
        }

    }
    /**
     *  <p>方法描述：人员orgId:判断4类资质中是否存在，不存在清空</p>
     * @MethodAuthor hsj 2022/4/27 17:07
     */
    public void clearPsnInfoOrgId(List<Integer> psnInfoRidList, IZwOrginfoZzsb zwOrginfoEntity, Integer orgId){
        if(CollectionUtils.isEmpty(psnInfoRidList) || null == orgId){
            return;
        }
        List<List<Integer>> groupList = StringUtils.splitListProxy(psnInfoRidList, 1000);
        StringBuffer sb = new StringBuffer();
        sb.append("update TD_ZW_PSNINFO t set t.ORG_ID = null where t.rid in (:ridList)");
        if (!(zwOrginfoEntity instanceof TdZwTjorginfoZzsb)) {
            sb.append(" and not exists(select 1 from TD_ZW_TJORGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid ");
            sb.append(" and t.org_id = ").append(orgId).append(")");
        }
        if (!(zwOrginfoEntity instanceof TdZwSrvorginfoZzsb)) {
            sb.append(" and not exists(select 1 from TD_ZW_SRVORGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid");
            sb.append(" and t.org_id = ").append(orgId).append(")");
        }
        if (!(zwOrginfoEntity instanceof TdZwDiagorginfoZzsb)) {
            sb.append(" and not exists(select 1 from TD_ZW_DIAGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid");
            sb.append(" and t.org_id = ").append(orgId).append(")");
        }
        if(!(zwOrginfoEntity instanceof TdZwOcchethInfoZzsb)){
            sb.append(" and not exists(select 1 from TD_ZW_OCCHETH_PSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid");
            sb.append(" and t.org_id = ").append(orgId).append(")");
        }
        for(List<Integer> ridList : groupList){
            Map<String,Object> paramsMap = new HashMap<>();
            paramsMap.put("ridList", ridList);
            this.executeSql(sb.toString(), paramsMap);
        }
    }
    /**
     *  <p>方法描述：外检车辆</p>
     * @MethodAuthor hsj 2022/4/25 9:56
     */
    private void saveOrUpdateOutcar(TdZwTjorginfoApply tjorginfoApply, List<Object> saveObjList, TdZwTjorginfoZzsb tdZwTjorginfo) {
        String extendS1 =tjorginfoApply.getFkByApplyTypeId().getExtendS1();
        List<Object[]> tjorgOutcarApplyList = tjorginfoApply.getTjorgOutcarApplyList();
        if(!CollectionUtils.isEmpty(tjorgOutcarApplyList)){
            for(Object[] obj:tjorgOutcarApplyList){
                TdZwTjorgOutcarZzsb tdZwTjorgOutcarZzsb = new TdZwTjorgOutcarZzsb();
                tdZwTjorgOutcarZzsb.setFkByMainId(tdZwTjorginfo);
                //车牌号
                tdZwTjorgOutcarZzsb.setCarNo(obj[1] == null ? null :obj[1].toString());
                //品牌
                tdZwTjorgOutcarZzsb.setCarBand(obj[2] == null ? null :obj[2].toString());
                //型号
                tdZwTjorgOutcarZzsb.setCarStyle(obj[3] == null ? null :obj[3].toString());
                //车架号
                tdZwTjorgOutcarZzsb.setCarFrameNo(obj[4] == null ? null :obj[4].toString());
                //出厂日期
                if(null != obj[5]){
                    tdZwTjorgOutcarZzsb.setProductionDate(DateUtils.parseDate(obj[5].toString()));
                }
                tdZwTjorgOutcarZzsb.setDrivingAnnexPath(StringUtils.objectToString(obj[9]));
                tdZwTjorgOutcarZzsb.setCarPhotoAnnexPath(StringUtils.objectToString(obj[10]));
                tdZwTjorgOutcarZzsb.setCarDrAnnexPath(StringUtils.objectToString(obj[11]));
                //状态
                tdZwTjorgOutcarZzsb.setStateMark(obj[6] == null ? null :Integer.valueOf(obj[6].toString()));
                tdZwTjorgOutcarZzsb.setCreateDate(new Date());
                tdZwTjorgOutcarZzsb.setCreateManid(Global.getUser().getRid());
                saveObjList.add(tdZwTjorgOutcarZzsb);
                //外检车辆仪器
                List<TdZwTjorgOutcInstApy>  outcInstZzsbs = findEntityListByOtMainId(TdZwTjorgOutcInstApy.class,
                        "fkByCarId", Integer.valueOf(obj[0].toString()));
                if(!CollectionUtils.isEmpty(outcInstZzsbs)){
                    for(TdZwTjorgOutcInstApy outcInst:outcInstZzsbs){
                        TdZwTjorgOutcInstZzsb tjorgOutcInstZzsb = new TdZwTjorgOutcInstZzsb();
                        tjorgOutcInstZzsb.setFkByCarId(tdZwTjorgOutcarZzsb);
                        tjorgOutcInstZzsb.setFkByInstId(outcInst.getFkByInstId());
                        tjorgOutcInstZzsb.setCreateDate(new Date());
                        tjorgOutcInstZzsb.setCreateManid(Global.getUser().getRid());
                        saveObjList.add(tjorgOutcInstZzsb);
                    }
                }
            }
        }
        if("2".equals(extendS1)){
            //根据ORG_ID删除
            String delDetailsByOrgIdSql = " DELETE FROM TD_ZW_TJORG_OUTC_INST WHERE CAR_ID IN (SELECT T.RID FROM TD_ZW_TJORG_OUTCAR T  WHERE T.MAIN_ID ="+ tdZwTjorginfo.getRid()+")";
            this.executeSql(delDetailsByOrgIdSql,null );

            String delItmByOrgIdSql = " DELETE FROM TD_ZW_TJORG_OUTCAR WHERE MAIN_ID ="+ tdZwTjorginfo.getRid();
            this.executeSql(delItmByOrgIdSql,null );
        }
    }

    /**
     *  <p>方法描述：质量管理体系</p>
     * @MethodAuthor hsj 2022/4/25 9:56
     */
    private void saveOrUpdateQualAnnex(TdZwTjorginfoApply tjorginfoApply, List<Object> saveObjList, TdZwTjorginfoZzsb tdZwTjorginfo) {
        String extendS1 =tjorginfoApply.getFkByApplyTypeId().getExtendS1();
        List<TdZwTjorgQualAnnexApy> tjorgQualAnnexApies = tjorginfoApply.getTjorgQualAnnexApies();
        if(!CollectionUtils.isEmpty(tjorgQualAnnexApies)){
            for(TdZwTjorgQualAnnexApy qualAnnexApy:tjorgQualAnnexApies){
                TdZwTjorgQualAnnexZzsb zwTjorgQualAnnexZzsb = new TdZwTjorgQualAnnexZzsb();
                zwTjorgQualAnnexZzsb.setFkByMainId(tdZwTjorginfo);
                zwTjorgQualAnnexZzsb.setFkByRuleId(qualAnnexApy.getFkByRuleId());
                zwTjorgQualAnnexZzsb.setOtherRuleName(qualAnnexApy.getOtherRuleName());
                zwTjorgQualAnnexZzsb.setIfHas(qualAnnexApy.getIfHas());
                zwTjorgQualAnnexZzsb.setAnnexPath(qualAnnexApy.getAnnexPath());
                zwTjorgQualAnnexZzsb.setCreateManid(Global.getUser().getRid());
                zwTjorgQualAnnexZzsb.setCreateDate(new Date());
                saveObjList.add(zwTjorgQualAnnexZzsb);
            }
        }
        if("2".equals(extendS1) && null != tdZwTjorginfo.getRid()){
            //变更申请
            //根据ORG_ID删除
            String delItmByMainIdSql = " DELETE FROM TD_ZW_TJORG_QUAL_ANNEX WHERE MAIN_ID ="+ tdZwTjorginfo.getRid();
            this.executeSql(delItmByMainIdSql,null );
        }
    }

    /**
     *  <p>方法描述：服务明细</p>
     * @MethodAuthor hsj 2022/4/25 9:12
     */
    private void saveOrUpdateItmDetail(TdZwTjorginfoApply tjorginfoApply, List<Object> saveObjList, TdZwTjorginfoZzsb tdZwTjorginfo) {
        String extendS1 =tjorginfoApply.getFkByApplyTypeId().getExtendS1();
        List<TdZwTjorggitemsApply> tjorggitemsApplyList = tjorginfoApply.getTjorggitemsApplyList();
        List<TdZwTjorgItmDetailApy> tjorgItmDetailApies = tjorginfoApply.getTjorgItmDetailApies();
        if(!CollectionUtils.isEmpty(tjorggitemsApplyList) && !CollectionUtils.isEmpty(tjorgItmDetailApies)){
            for(TdZwTjorggitemsApply itemsApply:tjorggitemsApplyList){
                TdZwTjorggitemsZzsb zwTjorggitemsZzsb = new TdZwTjorggitemsZzsb();
                zwTjorggitemsZzsb.setTdZwTjorginfo(tdZwTjorginfo);
                zwTjorggitemsZzsb.setItemCode(Short.valueOf(itemsApply.getFkByItemId().getCodeNo()));
                zwTjorggitemsZzsb.setCreateDate(new Date());
                zwTjorggitemsZzsb.setCreateManid(Global.getUser().getRid());
                for(TdZwTjorgItmDetailApy detailApy:tjorgItmDetailApies){
                    if(!detailApy.getFkByMainId().getRid().toString().equals(itemsApply.getRid().toString())){
                       continue;
                    }
                    TdZwTjorgItmDetail zwTjorgItmDetail = new TdZwTjorgItmDetail();
                    zwTjorgItmDetail.setFkByMainId(zwTjorggitemsZzsb);
                    zwTjorgItmDetail.setFkBySubItemId(detailApy.getFkByItemDetailId());
                    zwTjorgItmDetail.setIfExterInspect(detailApy.getIfExterInspect());
                    zwTjorgItmDetail.setCreateDate(new Date());
                    zwTjorgItmDetail.setCreateManid(Global.getUser().getRid());
                    saveObjList.add(zwTjorgItmDetail);
                }
                saveObjList.add(zwTjorggitemsZzsb);
            }
        }
        if("2".equals(extendS1) && null != tdZwTjorginfo.getRid()){
            //变更申请
            //根据ORG_ID删除
            String delDetailsByOrgIdSql = " DELETE FROM TD_ZW_TJORG_ITM_DETAIL WHERE MAIN_ID IN (SELECT T.RID FROM TD_ZW_TJORGGITEMS T  WHERE T.ORG_ID ="+ tdZwTjorginfo.getRid()+")";
            this.executeSql(delDetailsByOrgIdSql,null );

            String delItmByOrgIdSql = " DELETE FROM TD_ZW_TJORGGITEMS WHERE ORG_ID ="+ tdZwTjorginfo.getRid();
            this.executeSql(delItmByOrgIdSql,null );
        }

    }


    /**
     *  <p>方法描述：检查项目的处理</p>
     *  zwTjitemsZzsbs
     * @MethodAuthor hsj 2022/4/24 19:06
     */
    private void saveOrUpdateItems(TdZwTjorginfoApply tjorginfoApply,List<Object> saveObjList, TdZwTjorginfoZzsb zwOrginfoZzsb) {
        String extendS1 =tjorginfoApply.getFkByApplyTypeId().getExtendS1();
        List<TdZwTjitemsApply> tjitemsApplies = tjorginfoApply.getTjitemsApplies();
        if(!CollectionUtils.isEmpty(tjitemsApplies)){
            for(TdZwTjitemsApply item :tjitemsApplies){
                TdZwTjitemsZzsb zwItemsZzsb =new TdZwTjitemsZzsb();
                //资质单位ID
                zwItemsZzsb.setTdZwTjorginfo(zwOrginfoZzsb);
                //项目ID
                zwItemsZzsb.setTsSimpleCode(item.getFkByItemId());
                //是否外包
                zwItemsZzsb.setIsOutter(item.getIsOutter());
                zwItemsZzsb.setCreateDate(new Date());
                zwItemsZzsb.setCreateManid(Global.getUser().getRid());
                saveObjList.add(zwItemsZzsb);
            }
        }
        if("2".equals(extendS1)){
            //变更申请
            //根据ORG_ID删除
            String delItemsByOrgIdSql = " DELETE FROM TD_ZW_TJITEMS WHERE ORG_ID = "+ zwOrginfoZzsb.getRid();
            this.executeSql(delItemsByOrgIdSql,null );
        }
    }

    /**
     *  <p>方法描述：资质附件的处理</p>
     * @MethodAuthor hsj 2022/4/24 18:51
     */
    private void saveOrUpdateAnnex(TdZwTjorginfoApply tjorginfoApply, List<Object> saveObjList, Integer orgType) {
          List<Object[]> annexList = tjorginfoApply.getAnnexList();
        String extendS1 =tjorginfoApply.getFkByApplyTypeId().getExtendS1();
          if(!CollectionUtils.isEmpty(annexList)){
              for(Object[] obj : annexList){
                  TdZwJsffjgCommAnnexZzsb zzsb = new TdZwJsffjgCommAnnexZzsb();
                  //系统单位
                  zzsb.setFkByOrgId(tjorginfoApply.getFkByOrgId());
                  //附件类型
                  zzsb.setAnnexType(obj[2] == null ? null : Integer.valueOf(obj[2].toString()));
                  //附件文件名
                  zzsb.setFileName(obj[1] == null ? null :obj[1].toString());
                  //附件路径
                  zzsb.setFilePath(obj[3] == null ? null :obj[3].toString());
                  //注册地点
                  zzsb.setRegZoneId(obj[7] == null ? null :Integer.valueOf(obj[7].toString()));
                  //发证单位级别
                  zzsb.setUnitLevel(obj[8] == null ? null :Integer.valueOf(obj[8].toString()));
                  //发证单位
                  zzsb.setUnitName(obj[6] == null ? null :obj[6].toString());
                  zzsb.setCreateDate(new Date());
                  zzsb.setCreateManid(Global.getUser().getRid());
                  if(1 == orgType){
                        zzsb.setAddSource(0);
                  }else if(2 == orgType){
                      zzsb.setAddSource(2);
                  }
                  saveObjList.add(zzsb);
              }
          }
        if("2".equals(extendS1)){
            //变更申请
            //根据ORG_ID删除
            String delIAnnexByOrgIdSql = " DELETE FROM TD_ZW_JSFFJG_COMM_ANNEX WHERE ORG_ID = "+ tjorginfoApply.getFkByOrgId().getRid();
            if(1 == orgType){
                delIAnnexByOrgIdSql +=" AND ADD_SOURCE = 0 ";
            }else if(2 == orgType){
                delIAnnexByOrgIdSql +=" AND ADD_SOURCE = 2";
            }
            this.executeSql(delIAnnexByOrgIdSql,null );
        }
    }


    /**
     *  <p>方法描述：仪器信息的处理</p>
     * @MethodAuthor hsj 2022/4/24 18:49
     */
    private void saveOrUpdateInst(TdZwTjorginfoApply tjorginfoApply,List<Object> saveObjList, TdZwTjorginfoZzsb zwOrginfoZzsb,TdZwDiagorginfoZzsb  tdZwDiagorginfo,Integer orgType) {
        String extendS1 =tjorginfoApply.getFkByApplyTypeId().getExtendS1();
        List<TdZwTjinstApply> tjinstApplyList = tjorginfoApply.getTjinstApplyList();
        if(!CollectionUtils.isEmpty(tjinstApplyList)){
            for(TdZwTjinstApply item :tjinstApplyList){
                //资质单位ID
                if(1 == orgType){
                    TdZwTjinstZzsb zwTjinstZzsb =new TdZwTjinstZzsb();
                    zwTjinstZzsb.setTdZwTjorginfo(zwOrginfoZzsb);
                    //仪器ID
                    zwTjinstZzsb.setTdZwInstinfo(item.getFkByInstId());
                    zwTjinstZzsb.setCreateDate(new Date());
                    zwTjinstZzsb.setCreateManid(Global.getUser().getRid());
                    saveObjList.add(zwTjinstZzsb);
                }else if(2 == orgType){
                    TdZwDiaginstZzsb zwDiaginstZzsb =new TdZwDiaginstZzsb();
                    zwDiaginstZzsb.setZwOrginfo(tdZwDiagorginfo);
                    //仪器ID
                    zwDiaginstZzsb.setTdZwInstinfo(item.getFkByInstId());
                    zwDiaginstZzsb.setCreateDate(new Date());
                    zwDiaginstZzsb.setCreateManid(Global.getUser().getRid());
                    saveObjList.add(zwDiaginstZzsb);
                }
            }
        }
        if("2".equals(extendS1)){
            //变更申请
            //根据ORG_ID删除
            String delInstByOrgIdSql = " DELETE FROM TD_ZW_TJINST WHERE ORG_ID = "+ zwOrginfoZzsb.getRid();
            this.executeSql(delInstByOrgIdSql,null );
        }
    }

    private void updateUint(Integer orgId, TsZone fkByZoneId) {
        //更新体检机构的系统单位行政地区和业务管辖地区为备案申请页面的地区
        TsUnit tsUnit = super.find(TsUnit.class, orgId);
        if(null !=tsUnit){
            tsUnit.setTsZone(fkByZoneId);
            tsUnit.setFkByManagedZoneId(fkByZoneId);
            upsertEntity(tsUnit);
        }
    }

    /**
     *  <p>方法描述：基本信息的封装</p>
     * @MethodAuthor hsj 2022/4/24 16:47
     */
    private IZwOrginfoZzsb getIZwOrginfoZzsb(TdZwTjorginfoApply tjorginfoApply, Integer orgType) {
        IZwOrginfoZzsb  zwOrginfoZzsb = new TdZwTjorginfoZzsb();
        if(2 == orgType){
            zwOrginfoZzsb = new TdZwDiagorginfoZzsb();
        }
        //所属机构
        zwOrginfoZzsb.setTsUnit(tjorginfoApply.getFkByOrgId());
        //单位名称
        zwOrginfoZzsb.setOrgName(tjorginfoApply.getOrgName());
        //地址
        zwOrginfoZzsb.setOrgAddr(tjorginfoApply.getOrgAddr());
        //法定代表人
        zwOrginfoZzsb.setOrgFz(tjorginfoApply.getOrgFz());
        //法定代表人职务
        zwOrginfoZzsb.setOrgFzzw(tjorginfoApply.getOrgFzzw());
        //联系人
        zwOrginfoZzsb.setLinkMan(tjorginfoApply.getLinkMan());
        //联系人手机
        zwOrginfoZzsb.setLinkMb(tjorginfoApply.getLinkMb());
        //电话
        zwOrginfoZzsb.setLinkTel(tjorginfoApply.getLinkTel());
        //传真
        zwOrginfoZzsb.setFax(tjorginfoApply.getFax());
        //邮政编码
        zwOrginfoZzsb.setZipcode(tjorginfoApply.getZipcode());
        //电子邮件
        zwOrginfoZzsb.setEmail(tjorginfoApply.getEmail());
        //证书编号
        zwOrginfoZzsb.setCertNo(tjorginfoApply.getCertNo());
        //发证日期/备案日期
        zwOrginfoZzsb.setFirstGetday(tjorginfoApply.getFirstGetday());
        //备案结束日期（陕西）20220412
        zwOrginfoZzsb.setFilingDate(tjorginfoApply.getFilingDate());
        //状态
        zwOrginfoZzsb.setState((short) 1);
        //+注销状态
        zwOrginfoZzsb.setCancelState(0);
        //+外出开展职业健康检查工作能力20200221
        zwOrginfoZzsb.setOutWorkPower(tjorginfoApply.getOutWorkPower());
        return zwOrginfoZzsb;
    }

    /**
     *  <p>方法描述：更细基本信息和服务明细</p>
     * @MethodAuthor hsj 2022/4/24 16:39
     */
    private void saveOrUpdateInfo(TdZwTjorginfoApply tjorginfoApply, List<Integer> resultList) {
        List<Object> saveObjList = new ArrayList<>();
        List<Object> updateObjList = new ArrayList<>();
        //更新基本信息和明细
        preUpdate(tjorginfoApply);
        updateObjList.add(tjorginfoApply);
        //修改检查机构服务项目
        if(!CollectionUtils.isEmpty(tjorginfoApply.getTjorggitemsApplyList())){
            for(TdZwTjorggitemsApply itemApply : tjorginfoApply.getTjorggitemsApplyList()){
                //机构服务项目不存在更新的情况 更新忽略
                if(null == itemApply.getRid()){
                    preInsert(itemApply);
                    saveObjList.add(itemApply);
                }
            }
        }
        //新增或修改服务项目明细
        if(!CollectionUtils.isEmpty(tjorginfoApply.getTjorgItmDetailApies())){
            for(TdZwTjorgItmDetailApy detailApy : tjorginfoApply.getTjorgItmDetailApies()){
                //服务项目明细
                saveObjList.add(detailApy);
            }
        }

        //通过资质单位rig删除服务项目明细
        String deleteDetailByOrgIdSql = "  DELETE FROM TD_ZW_TJORG_ITM_DETAIL_APY WHERE MAIN_ID IN (SELECT T.RID FROM TD_ZW_TJORGGITEMS_APPLY T WHERE T.ORG_ID = "+tjorginfoApply.getRid() +")";
        this.executeSql(deleteDetailByOrgIdSql,null );
        //通过rid集合删除职业健康检查机构服务项目
        String deleteItemByRidSql = " DELETE FROM TD_ZW_TJORGGITEMS_APPLY WHERE RID IN (:ridList) ";
        Map<String,Object> paramMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(resultList)){
            List<List<Integer>> tmpList = StringUtils.splitListProxy(resultList, 1000);
            for(List<Integer> ridList : tmpList){
                paramMap.put("ridList", ridList);
                this.executeSql(deleteItemByRidSql,paramMap );
            }
        }

        //保存对象
        if(!CollectionUtils.isEmpty(saveObjList)){
            this.saveBatchObjs(saveObjList);
        }
        //更新对象
        if(!CollectionUtils.isEmpty(updateObjList)){
            this.updateBatchObjs(updateObjList);
        }
    }

    /**
     * <p>方法描述：更新资质机构备案主表</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-26
     **/
    public void updateFstChkApply(TdZwTjorginfoApply tjorginfoApply){
        //驳回时值更新状态和驳回信息
        Integer state = tjorginfoApply.getState();
        if(null == state && 2 != state && 4 != state){
            return ;
        }
        StringBuilder strSql = new StringBuilder();
        Map<String,Object> paramMap = new HashMap<>();
        strSql.append("UPDATE TD_ZW_TJORGINFO_APPLY SET STATE =:state");
        paramMap.put("state",state);
        if(2 == state){
            //初审退回
            strSql.append(" ,FST_CHK_DATE =:fstChkDate , FST_CHK_ORG_ID =:fstChkOrgId , FST_CHK_PSN_ID =:fstChkPsnId ,FST_BACK_RSN =:fstBackRsn");
            paramMap.put("fstChkDate",new Date());
            paramMap.put("fstChkOrgId",Global.getUser().getTsUnit().getRid());
            paramMap.put("fstChkPsnId",Global.getUser().getRid());
            paramMap.put("fstBackRsn",tjorginfoApply.getFstBackRsn());
        }else if(4 == state){
            strSql.append(" ,FINAL_CHK_DATE =:finalChkDate , FINAL_CHK_ORG_ID =:finalChkOrgId , FINAL_CHK_PSN_ID =:finalChkPsnId ,FINAL_BACK_RSN =:finalBackRsn");
            paramMap.put("finalChkDate",new Date());
            paramMap.put("finalChkOrgId",Global.getUser().getTsUnit().getRid());
            paramMap.put("finalChkPsnId",Global.getUser().getRid());
            paramMap.put("finalBackRsn",tjorginfoApply.getFinalBackRsn());
        }
        strSql.append(" WHERE RID =:rid");
        paramMap.put("rid",tjorginfoApply.getRid());
        this.executeSql(strSql.toString(),paramMap );
    }

}
