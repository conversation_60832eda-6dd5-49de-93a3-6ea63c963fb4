package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

import javax.persistence.*;
import java.util.*;

import static com.chis.modules.heth.zzsb.entity.TdZwSrvorginfoZzsb.sortZwOrgPsnsZzsbs;

/**
 * 职业卫生技术服务机构基本信息
 *
 * <AUTHOR>
 * @createTime 2019-9-24
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_INFO")
@SequenceGenerator(name = "TdZwOcchethInfo", sequenceName = "TD_ZW_OCCHETH_INFO_SEQ", allocationSize = 1)
public class TdZwOcchethInfoZzsb implements java.io.Serializable,IZwOrginfoZzsb {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsUnit tsUnit;
	private String orgName;
	private String orgAddr;
	private String orgFz;
	private String orgFzzw;
	private String linkMan;
	private String linkMb;
	private String linkTel;
	private String fax;
	private String zipcode;
	private String email;
	private String certNo;
	private Date firstGetday;
	private Date validDate;
	private Short state;
	private Integer cancelState;
	private Date cancelDate;
	private TsSimpleCode fkByLevelId;
	private Date createDate;
	private Integer createManid;
	private List<TdZwOcchethItemsZzsb> tdZwOcchethItems = new LinkedList<TdZwOcchethItemsZzsb>();
	private List<TdZwOcchethInstZzsb> tdZwOcchethInsts = new LinkedList<TdZwOcchethInstZzsb>();
	private List<TdZwOcchethPsnsZzsb> tdZwOcchethPsns = new LinkedList<TdZwOcchethPsnsZzsb>();
	private List<TdZwOcchethAnnexZzsb> tdZwOcchethAnnexs = new LinkedList<TdZwOcchethAnnexZzsb>();
	/**+单位类型20210227*/
    private TsSimpleCode fkByUnitTypeId;
	/**+实验室地址20210227*/
    private String labAddr;
    /**+通讯地址20210227*/
    private String communiAddr;
	
	
    /**+外出开展职业健康检查工作能力20200221*/
    private Integer outWorkPower;
    /**+外出开展职业健康检查工作能力范围20200211*/
    private List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges = new ArrayList<TdZwTjorgOutRangeZzsb>(0);

	/**
	 * 备案管理机构ID
	 */
	private Integer rcdOrgId;

	/*****************临时字段***************/
	private String delPsnIds;
	/**
	 * +资质批准单位20220822
	 * */
	private String rcdUnitName;

	/**审核信息-审核结果*/
	private Integer checkRst;
	/**审核信息-审核意见*/
	private String auditAdv;
	/**审核信息-审核人*/
	private TsUserInfo fkByChkPsnId;
	/**主数据时间戳*/
	private Date masterDataTime;
	private Date modifyDate;
	private Integer modifyManid;
	public TdZwOcchethInfoZzsb() {
	}

	public TdZwOcchethInfoZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethInfo")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")
	public TsUnit getTsUnit() {
		return tsUnit;
	}
	
	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}
			
	@Column(name = "ORG_NAME")	
	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}	
			
	@Column(name = "ORG_ADDR")	
	public String getOrgAddr() {
		return orgAddr;
	}

	public void setOrgAddr(String orgAddr) {
		this.orgAddr = orgAddr;
	}	
			
	@Column(name = "ORG_FZ")	
	public String getOrgFz() {
		return orgFz;
	}

	public void setOrgFz(String orgFz) {
		this.orgFz = orgFz;
	}	
			
	@Column(name = "ORG_FZZW")	
	public String getOrgFzzw() {
		return orgFzzw;
	}

	public void setOrgFzzw(String orgFzzw) {
		this.orgFzzw = orgFzzw;
	}	
			
	@Column(name = "LINK_MAN")	
	public String getLinkMan() {
		return linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}	
			
	@Column(name = "LINK_MB")	
	public String getLinkMb() {
		return linkMb;
	}

	public void setLinkMb(String linkMb) {
		this.linkMb = linkMb;
	}	
			
	@Column(name = "LINK_TEL")	
	public String getLinkTel() {
		return linkTel;
	}

	public void setLinkTel(String linkTel) {
		this.linkTel = linkTel;
	}	
			
	@Column(name = "FAX")	
	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}	
			
	@Column(name = "ZIPCODE")	
	public String getZipcode() {
		return zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}	
			
	@Column(name = "EMAIL")	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}	
			
	@Column(name = "CERT_NO")	
	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FIRST_GETDAY")			
	public Date getFirstGetday() {
		return firstGetday;
	}

	public void setFirstGetday(Date firstGetday) {
		this.firstGetday = firstGetday;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "VALID_DATE")			
	public Date getValidDate() {
		return validDate;
	}

	public void setValidDate(Date validDate) {
		this.validDate = validDate;
	}	
			
	@Column(name = "STATE")	
	public Short getState() {
		return state;
	}

	public void setState(Short state) {
		this.state = state;
	}	
			
	@Column(name = "CANCEL_STATE")	
	public Integer getCancelState() {
		return cancelState;
	}

	public void setCancelState(Integer cancelState) {
		this.cancelState = cancelState;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CANCEL_DATE")			
	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "LEVEL_ID")			
	public TsSimpleCode getFkByLevelId() {
		return fkByLevelId;
	}

	public void setFkByLevelId(TsSimpleCode fkByLevelId) {
		this.fkByLevelId = fkByLevelId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	@Override
	@Column(name = "RCD_UNIT_NAME")
	public String getRcdUnitName() {
		return rcdUnitName;
	}

	@Override
	public void setRcdUnitName(String rcdUnitName) {
		this.rcdUnitName = rcdUnitName;
	}
	@Transient
	@Override
	public List<IZwAnnexZzsb> getZwAnnex() {
		if(null != this.tdZwOcchethAnnexs && this.tdZwOcchethAnnexs.size() > 0) {
			List<IZwAnnexZzsb> list = new ArrayList<IZwAnnexZzsb>();
			for(TdZwOcchethAnnexZzsb t : this.tdZwOcchethAnnexs) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwAnnex(List<IZwAnnexZzsb> zwAnnexList) {
		if(null != zwAnnexList && zwAnnexList.size() > 0) {
			this.tdZwOcchethAnnexs = new ArrayList<TdZwOcchethAnnexZzsb>();
			for(IZwAnnexZzsb t : zwAnnexList) {
				this.tdZwOcchethAnnexs.add((TdZwOcchethAnnexZzsb) t);
			}
		}else if(null == zwAnnexList) {
			this.tdZwOcchethAnnexs = null;
		}else {
			this.tdZwOcchethAnnexs = new ArrayList<TdZwOcchethAnnexZzsb>(0);
		}
	}
	@Transient
	@Override
	public List<IZwInstZzsb> getZwInst() {
		if(null != this.tdZwOcchethInsts && this.tdZwOcchethInsts.size() > 0) {
			List<IZwInstZzsb> list = new ArrayList<IZwInstZzsb>();
			for(TdZwOcchethInstZzsb t : this.tdZwOcchethInsts) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwInst(List<IZwInstZzsb> zwInstList) {
		if(null != zwInstList && zwInstList.size() > 0) {
			this.tdZwOcchethInsts = new ArrayList<TdZwOcchethInstZzsb>();
			for(IZwInstZzsb t : zwInstList) {
				this.tdZwOcchethInsts.add((TdZwOcchethInstZzsb) t);
			}
		}else if(null == zwInstList) {
			this.tdZwOcchethInsts = null;
		}else {
			this.tdZwOcchethInsts = new ArrayList<TdZwOcchethInstZzsb>(0);
		}
	}
	@Transient
	@Override
	public List<IZwItemsZzsb> getZwItems() {
		return null;
	}

	@Override
	public void setZwItems(List<IZwItemsZzsb> zwItemsList) {
		
	}
	@Transient
	@Override
	public List<IZwOrgPsnsZzsb> getZwOrgPsns() {
		if(null != this.tdZwOcchethPsns && this.tdZwOcchethPsns.size() > 0) {
			List<IZwOrgPsnsZzsb> list = new ArrayList<IZwOrgPsnsZzsb>(this.tdZwOcchethPsns);
			return sortZwOrgPsnsZzsbs(list);
		}else {
			return null;
		}
	}

	@Override
	public void setZwOrgPsns(List<IZwOrgPsnsZzsb> zwOrgPsnsList) {
		if(null != zwOrgPsnsList && zwOrgPsnsList.size() > 0) {
			this.tdZwOcchethPsns = new ArrayList<TdZwOcchethPsnsZzsb>();
			for(IZwOrgPsnsZzsb t : zwOrgPsnsList) {
				this.tdZwOcchethPsns.add((TdZwOcchethPsnsZzsb) t);
			}
		}else if(null == zwOrgPsnsList) {
			this.tdZwOcchethPsns = null;
		}else {
			this.tdZwOcchethPsns = new ArrayList<TdZwOcchethPsnsZzsb>(0);
		}
	}
	@Transient
	@Override
	public List<IZwSvritmZzsb> getZwSvritm() {
		if(null != this.tdZwOcchethItems && this.tdZwOcchethItems.size() > 0) {
			List<IZwSvritmZzsb> list = new ArrayList<IZwSvritmZzsb>();
			for(TdZwOcchethItemsZzsb t : this.tdZwOcchethItems) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwSvritm(List<IZwSvritmZzsb> zwSvritmList) {
		if(null != zwSvritmList && zwSvritmList.size() > 0) {
			this.tdZwOcchethItems = new ArrayList<TdZwOcchethItemsZzsb>();
			for(IZwSvritmZzsb t : zwSvritmList) {
				this.tdZwOcchethItems.add((TdZwOcchethItemsZzsb) t);
			}
		}else if(null == zwSvritmList) {
			this.tdZwOcchethItems = null;
		}else {
			this.tdZwOcchethItems = new ArrayList<TdZwOcchethItemsZzsb>(0);
		}
	}
	@Transient
	@Override
	public String getJcItems() {
		return null;
	}

	@Override
	public void setJcItems(String jcItems) {
		
	}
	@Transient
	@Override
	public String getDelPsnIds() {
		return delPsnIds;
	}

	@Override
	public void setDelPsnIds(String delPsnIds) {
		this.delPsnIds = delPsnIds;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByOrgId",orphanRemoval=true)
	public List<TdZwOcchethItemsZzsb> getTdZwOcchethItems() {
		return tdZwOcchethItems;
	}

	public void setTdZwOcchethItems(List<TdZwOcchethItemsZzsb> tdZwOcchethItems) {
		this.tdZwOcchethItems = tdZwOcchethItems;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByOrgId",orphanRemoval=true)
	public List<TdZwOcchethInstZzsb> getTdZwOcchethInsts() {
		return tdZwOcchethInsts;
	}

	public void setTdZwOcchethInsts(List<TdZwOcchethInstZzsb> tdZwOcchethInsts) {
		this.tdZwOcchethInsts = tdZwOcchethInsts;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByOrgId",orphanRemoval=true)
	public List<TdZwOcchethPsnsZzsb> getTdZwOcchethPsns() {
		return tdZwOcchethPsns;
	}

	public void setTdZwOcchethPsns(List<TdZwOcchethPsnsZzsb> tdZwOcchethPsns) {
		this.tdZwOcchethPsns = tdZwOcchethPsns;
	}
	@OneToMany(cascade=CascadeType.ALL,fetch=FetchType.LAZY,mappedBy="fkByOrgId",orphanRemoval=true)
	public List<TdZwOcchethAnnexZzsb> getTdZwOcchethAnnexs() {
		return tdZwOcchethAnnexs;
	}

	public void setTdZwOcchethAnnexs(List<TdZwOcchethAnnexZzsb> tdZwOcchethAnnexs) {
		this.tdZwOcchethAnnexs = tdZwOcchethAnnexs;
	}
	@Transient
	@Override
	public String getCreditCode() {
		return null;
	}

	@Override
	public void setCreditCode(String creditCode) {
		
	}

	@Transient
    @Override
    public Integer getOutWorkPower() {
        return outWorkPower;
    }

    @Override
    public void setOutWorkPower(Integer outWorkPower) {
        this.outWorkPower = outWorkPower;
    }

    @Transient
    @Override
    public List<IZwOrgOutRangeZzsb> getZwOrgOutRanges() {
        return null;
    }

    @Override
    public void setZwOrgOutRanges(List<IZwOrgOutRangeZzsb> zwOrgOutRangeList) {

    }
    @Column(name = "LAB_ADDR")
	public String getLabAddr() {
		return labAddr;
	}

	public void setLabAddr(String labAddr) {
		this.labAddr = labAddr;
	}
	@Column(name = "COMMUNI_ADDR")
	public String getCommuniAddr() {
		return communiAddr;
	}

	public void setCommuniAddr(String communiAddr) {
		this.communiAddr = communiAddr;
	}
	@ManyToOne
	@JoinColumn(name = "UNIT_TYPE_ID")
	public TsSimpleCode getFkByUnitTypeId() {
		return fkByUnitTypeId;
	}

	public void setFkByUnitTypeId(TsSimpleCode fkByUnitTypeId) {
		this.fkByUnitTypeId = fkByUnitTypeId;
	}
	@Transient
	@Override
	public Date getFilingDate() {return null;}
	@Override
	public void setFilingDate(Date filingDate) {}

	@Transient
	@Override
	public Integer getRcdOrgId() {
		return rcdOrgId;
	}

	@Override
	public void setRcdOrgId(Integer rcdOrgId) {
		this.rcdOrgId = rcdOrgId;
	}

	@Transient
	@Override
	public List<TdZwTjorgOutRangeZzsb> getTdZwTjorgOutRanges() {
		return null;
	}

	@Override
	public void setTdZwTjorgOutRanges(List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges) {
	}

	@Transient
	@Override
	public String getZoneApproves() {
		return null;
	}

	@Override
	public void setZoneApproves(String zoneApproves) {

	}
	@Column(name = "CHECK_RST")
	@Override
	public Integer getCheckRst() {
		return checkRst;
	}

	@Override
	public void setCheckRst(Integer checkRst) {
		this.checkRst = checkRst;
	}
	@Column(name = "AUDIT_ADV")
	@Override
	public String getAuditAdv() {
		return auditAdv;
	}

	@Override
	public void setAuditAdv(String auditAdv) {
		this.auditAdv = auditAdv;
	}

	@Override
	@ManyToOne
	@JoinColumn(name = "CHK_PSN_ID")
	public TsUserInfo getFkByChkPsnId() {
		return fkByChkPsnId;
	}

	@Override
	public void setFkByChkPsnId(TsUserInfo fkByChkPsnId) {
		this.fkByChkPsnId = fkByChkPsnId;
	}

	@Transient
	@Override
	public Date getLastSmtDate() {
		return null;
	}

	@Override
	public void setLastSmtDate(Date lastSmtDate) {
	}
	@Transient
	@Override
	public List<TdZwSupportQual> getSupportQualList() {
		return null;
	}

	@Override
	public void setSupportQualList(List<TdZwSupportQual> supportQualList) {

	}

	@Transient
	@Override
	public List<TdZwSupportChk> getSupportChkList() {
		return null;
	}

	@Override
	public void setSupportChkList(List<TdZwSupportChk> supportChkList) {

	}

	@Transient
	@Override
	public List<TdZwSupportFund> getSupportFundList() {
		return null;
	}

	@Override
	public void setSupportFundList(List<TdZwSupportFund> supportFundList) {

	}
	@Transient
	@Override
	public String getOrgTypeName() {
		return null;
	}

	@Override
	public void setOrgTypeName(String orgTypeName) {

	}
	@Transient
	@Override
	public String getQuaTypeName() {
		return null;
	}

	@Override
	public void setQuaTypeName(String quaTypeName) {

	}
	@Transient
	@Override
	public TdZwSupportPlace getSupportPlaces() {
		return null;
	}

	@Override
	public void setSupportPlaces(TdZwSupportPlace supportPlaces) {

	}

	@Transient
	@Override
	public TsSimpleCode getFkByTypeId() {
		return null;
	}

	@Override
	public void setFkByTypeId(TsSimpleCode fkByTypeId) {

	}
	@Transient
	@Override
	public List<TdZwSupportTech> getSupportTechList() {
		return null;
	}
	@Override
	public void setSupportTechList(List<TdZwSupportTech> supportTechList) {

	}
	@Column(name = "MASTER_DATA_TIME")
	@Override
	public Date getMasterDataTime() {
		return masterDataTime;
	}
	@Override
	public void setMasterDataTime(Date masterDataTime) {
		this.masterDataTime=masterDataTime;
	}
	@Column(name = "MODIFY_DATE")
	@Override
	public Date getModifyDate() {
		return modifyDate;
	}
	@Override
	public void setModifyDate(Date modifyDate) {
		this.modifyDate=modifyDate;
	}
	@Column(name = "MODIFY_MANID")
	@Override
	public Integer getModifyManid() {
		return modifyManid;
	}
	@Override
	public void setModifyManid(Integer modifyManid) {
		this.modifyManid=modifyManid;
	}
}