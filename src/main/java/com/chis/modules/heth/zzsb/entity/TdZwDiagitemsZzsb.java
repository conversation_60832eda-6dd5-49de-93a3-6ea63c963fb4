package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_DIAGITEMS")
@SequenceGenerator(name = "TdZwDiagitemsSeq", sequenceName = "TD_ZW_DIAGITEMS_SEQ", allocationSize = 1)
public class TdZwDiagitemsZzsb implements Serializable,IZwSvritmZzsb {
	private static final long serialVersionUID = 190303778345806667L;

	private Integer rid;
	private TdZwDiagorginfoZzsb tdZwDiagorginfo;
	private Short itemCode;
	private Date createDate;
	private Integer createManid;

	public TdZwDiagitemsZzsb() {
	}

	public TdZwDiagitemsZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwDiagitemsSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwDiagorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwDiagorginfo = (TdZwDiagorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwDiagorginfoZzsb getTdZwDiagorginfo() {
		return this.tdZwDiagorginfo;
	}

	public void setTdZwDiagorginfo(TdZwDiagorginfoZzsb tdZwDiagorginfo) {
		this.tdZwDiagorginfo = tdZwDiagorginfo;
	}

	@Column(name = "ITEM_CODE" , precision = 1, scale = 0)
	public Short getItemCode() {
		return this.itemCode;
	}

	public void setItemCode(Short itemCode) {
		this.itemCode = itemCode;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	@Override
	public List<IZwSvritmSubZzsb> getZwSvritmSubList() {
		return null;
	}

	@Override
	public void setZwSvritmSubList(List<IZwSvritmSubZzsb> zwSvritmSubList) {

	}

}