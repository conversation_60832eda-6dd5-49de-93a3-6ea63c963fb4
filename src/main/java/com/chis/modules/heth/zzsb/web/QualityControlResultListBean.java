package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.TdZwCheckRst;
import com.chis.modules.heth.zzsb.logic.ZwPsnInfoDTO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.math.BigDecimal;
import java.util.*;

/**
 * 质量控制结果录入
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "qualityControlResultListBean")
@ViewScoped
public class QualityControlResultListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    private Integer mainRid;

    /**
     * 查询条件：地区集合
     */
    private List<TsZone> searchZoneList = new ArrayList<>();
    /**
     * 查询条件：地区编码
     */
    private String searchZoneGb;
    /**
     * 查询条件：地区名称
     */
    private String searchZoneName;
    /**
     * 查询条件：机构名称
     */
    private String searchOrgName;
    /**
     * 查询条件：质控日期-开始日期
     */
    private Date searchQcBeginDate;
    /**
     * 查询条件：质控日期-结束日期
     */
    private Date searchQcEndDate;
    /**
     * 查询条件：质控结论
     */
    private String selectQcConclusionNames;
    private String selectQcConclusionIds;
    private List<TsSimpleCode> qcConclusionSimpleCodeList;
    /**
     * 查询条件：整改结果
     */
    private String selectRectificationResultNames;
    private String selectRectificationResultIds;
    private List<TsSimpleCode> rectificationResultSimpleCodeList;
    /**
     * 查询条件：状态
     */
    private String[] states;
    private List<SelectItem> stateList = new ArrayList<>();
    /** 质量录入结果页面对象*/
    private TdZwCheckRst checkRst;
    private List<TsSimpleCode> checkTypeList;
    private Map<Integer,TsSimpleCode> checkTypeMap = new HashMap<>();
    private Date today;
    /** 人员弹出框类型 1专家组组长，2专家组组员*/
    private String psnSelectType;
    /** 附件上传类型1质控结果告知书，2整改报告*/
    private String uploadType;



    public QualityControlResultListBean() {
        init();
        this.ifSQL = true;
        this.searchAction();
    }

    private void init() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        //地区
        if (null == this.searchZoneList || this.searchZoneList.size() <= 0) {
            this.searchZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            this.searchZoneGb = this.searchZoneList.get(0).getZoneCode();
            this.searchZoneName = this.searchZoneList.get(0).getZoneName();
        }
        //质控日期
        this.searchQcEndDate = new Date();
        this.searchQcBeginDate = DateUtils.getYearFirstDay(this.searchQcEndDate);
        //质控结论
        this.qcConclusionSimpleCodeList = this.commService.findNumSimpleCodesByTypeId("5568");
        //整改结果
        this.rectificationResultSimpleCodeList = this.commService.findNumSimpleCodesByTypeId("5569");
        //状态
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("1", "已提交"));
        this.states = new String[]{"0"};
        //考核类型
        this.checkTypeList = this.commService.findNumSimpleCodesByTypeId("5554");
        if(ObjectUtil.isNotEmpty(checkTypeList)){
            checkTypeMap = new HashMap<>();
            for (TsSimpleCode t : checkTypeList){
                checkTypeMap.put(t.getRid(),t);
            }
        }
        today = new Date();
    }

    @Override
    public void searchAction() {
        super.searchAction();

        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:mainForm:dataTable");
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        for (Object object : list) {
            Object[] o = (Object[]) object;
            String fullName = StringUtils.objectToString(o[1]);
            o[1] = fullName.substring(fullName.indexOf("_") + 1);
            if (BigDecimal.ZERO.equals(o[7])) {
                o[7] = "待提交";
            } else if (BigDecimal.ONE.equals(o[7])) {
                o[7] = "已提交";
            } else {
                o[7] = "";
            }
        }
    }

    /**
     * <p>描述 考核类型切换事件</p>
     *
     * @MethodAuthor gongzhe,2022/7/12 16:43,changeCheckType
     * @return void
     */
    public void changeCheckType(){
        //清空机构名称
        checkRst.setFkUnitId(new TsUnit());
    }

    /**
     * <p>描述 机构选择</p>
     *  
     * @MethodAuthor gongzhe,2022/7/12 16:52,selectOrgList
     * @return void
     */
    public void selectOrgList() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,370);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        tmpList.add(this.checkTypeMap.get(this.checkRst.getFkCheckTypeId().getRid()).getExtendS1());
        paramMap.put("checkType", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/zzsb/selectCheckOrgList", options, paramMap);
    }
    /**
     * <p>方法描述：选择机构</p>
     * @MethodAuthor qrr,2021年6月28日,onOrgSelect
     * */
    public void onOrgSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            Object[] obj = (Object[]) selectedMap.get("selected");
            TsUnit unit = new TsUnit();
            unit.setRid(new Integer(obj[0].toString()));
            unit.setUnitname(StringUtils.objectToString(obj[2]));
            this.checkRst.setFkUnitId(unit);
        }
    }
    /**
     * <p>描述 用人单位</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:03,selectPsnList
     * @return void
     */
    public void selectPsnList() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,760,null,470);
        Map<String, List<String>> paramMap = new HashMap<>();
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/zzsb/selectCheckPsnList", options, paramMap);
    }

    /**
     * <p>描述 用人单位选择</p>
     *
     * @param event
     * @MethodAuthor gongzhe,2022/7/13 11:03,onPsnSelect
     * @return void
     */
    public void onPsnSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            List<ZwPsnInfoDTO> obj = (List<ZwPsnInfoDTO>) selectedMap.get("selected");
            StringBuilder psnName = new StringBuilder();
            for(ZwPsnInfoDTO psnInfo : obj){
                psnName.append("，").append(psnInfo.getEmpName());
            }
            String name = psnName.substring(1);
            if("1".equals(psnSelectType)){
                String expertLeaders = this.checkRst.getExpertLeaders();
                this.checkRst.setExpertLeaders(StringUtils.isBlank(expertLeaders) ? name : expertLeaders + "，" + name);
            }else if("2".equals(psnSelectType)){
                String expertMembers = this.checkRst.getExpertMembers();
                this.checkRst.setExpertMembers(StringUtils.isBlank(expertMembers) ? name : expertMembers + "，" + name);
            }
        }
    }

    /**
     * <p>描述 清空专家组长</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:03,clearSelectLeaders
     * @return void
     */
    public void clearSelectLeaders(){
        this.checkRst.setExpertLeaders(null);
    }

    /**
     * <p>描述 清空专家组员</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:04,clearSelectMembers
     * @return void
     */
    public void clearSelectMembers(){
        this.checkRst.setExpertMembers(null);
    }

    /**
     * <p>描述 删除质控结果告知书</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:04,delNoticeFile
     * @return void
     */
    public void delNoticeFile(){
        if(StringUtils.isNotBlank(this.checkRst.getNoticeFilePath())){
            FileUtils.delFile(JsfUtil.getAbsolutePath()+this.checkRst.getNoticeFilePath());
            this.checkRst.setNoticeFilePath(null);
        }
    }

    /**
     * <p>描述 删除整改报告</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:04,delZgFile
     * @return void
     */
    public void delZgFile(){
        if(StringUtils.isNotBlank(this.checkRst.getZgFilePath())){
            FileUtils.delFile(JsfUtil.getAbsolutePath()+this.checkRst.getZgFilePath());
            this.checkRst.setZgFilePath(null);
        }
    }

    /**
     * <p>描述 切换整改结果</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:04,changeZgRst
     * @return void
     */
    public void changeZgRst(){
        if(this.checkRst.getFkZgRstId().getRid() == null){
            delZgFile();
        }
    }

    /**
     * <p>描述 附件上传</p>
     *
     * @param event
     * @MethodAuthor gongzhe,2022/7/13 11:11,fileUpload
     * @return void
     */
    public void fileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String fileName = file.getFileName();
                String contentType = file.getContentType();
                String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String uuid = java.util.UUID.randomUUID().toString()
                        .replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = new StringBuffer("heth/zworgannex/")
                        .append(uuid)
                        .append(fileName.substring(fileName
                                .lastIndexOf("."))).toString();
                // 文件路径
                String filePath = new StringBuilder(path).append(
                        relativePath).toString();
                if("1".equals(this.uploadType)){
                    checkRst.setNoticeFilePath(relativePath);
                }else if("2".equals(this.uploadType)){
                    checkRst.setZgFilePath(relativePath);
                }
                FileUtils.copyFile(filePath, file.getInputstream());

                RequestContext.getCurrentInstance().execute(
                        "PF('FileDialog').hide()");
                RequestContext.getCurrentInstance().update(
                        "tabView:editForm");
                JsfUtil.addSuccessMessage("上传成功！");
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public void addInit() {
        checkRst = new TdZwCheckRst();
        checkRst.setFkCheckTypeId(new TsSimpleCode());
        checkRst.setFkUnitId(new TsUnit());
        checkRst.setFkCheckRstId(new TsSimpleCode());
        checkRst.setCheckDate(new Date());
        checkRst.setFkZgRstId(new TsSimpleCode());
        checkRst.setFkCheckUnitId(Global.getUser().getTsUnit());
        checkRst.setState(0);
        checkRst.setDelMark(0);
    }

    @Override
    public void viewInit() {
        this.checkRst = commService.find(TdZwCheckRst.class,this.mainRid);
    }

    @Override
    public void modInit() {
        this.checkRst = commService.find(TdZwCheckRst.class,this.mainRid);
        if(this.checkRst.getFkCheckRstId() == null || this.checkRst.getFkCheckRstId().getRid()==null){
            this.checkRst.setFkCheckRstId(new TsSimpleCode());
        }
        if(this.checkRst.getFkZgRstId() == null || this.checkRst.getFkZgRstId().getRid()==null){
            this.checkRst.setFkZgRstId(new TsSimpleCode());
        }
    }

    @Override
    public void saveAction() {
        try{
            if(!verifySave()) {
                return;
            }
            if(this.checkRst.getFkCheckRstId().getRid()==null){
                this.checkRst.setFkCheckRstId(null);
            }
            if(this.checkRst.getFkZgRstId().getRid()==null){
                this.checkRst.setFkZgRstId(null);
            }
            commService.upsertEntity(this.checkRst);
            this.mainRid = checkRst.getRid();
            this.modInit();
            JsfUtil.addSuccessMessage("暂存成功！");
            this.searchAction();
        }catch (Exception e){
            JsfUtil.addErrorMessage("暂存失败！");
            e.printStackTrace();
        }
    }

    public void submitAction() {
        try{
            if(!verifySubmit()){
                return;
            }else{
                this.checkRst.setState(1);
                if(this.checkRst.getFkZgRstId().getRid()==null){
                    this.checkRst.setFkZgRstId(null);
                }
                commService.upsertEntity(this.checkRst);
                this.mainRid = checkRst.getRid();
                this.viewInit();
                this.forwardViewPage();
                JsfUtil.addSuccessMessage("提交成功！");
                RequestContext.getCurrentInstance().update("tabView");
                this.searchAction();
            }
        }catch (Exception e){
            JsfUtil.addErrorMessage("提交失败！");
            e.printStackTrace();
        }
    }

    /**
     * <p>描述 暂存验证</p>
     *
     * @MethodAuthor gongzhe,2022/7/13 11:12,verifySave
     * @return boolean
     */
    private boolean verifySave(){
        boolean flag = true;
        if(this.checkRst.getFkCheckTypeId().getRid() == null){
            JsfUtil.addErrorMessage("考核类型不能为空！");
            flag = false;
        }
        if(this.checkRst.getFkUnitId().getRid() == null){
            JsfUtil.addErrorMessage("机构名称不能为空！");
            flag = false;
        }
        if(this.checkRst.getExpertLeaders()!=null && this.checkRst.getExpertLeaders().length()>200){
            JsfUtil.addErrorMessage("专家组组长超出限制的200个字符！");
            flag = false;
        }
        if(this.checkRst.getExpertMembers()!=null && this.checkRst.getExpertMembers().length()>200){
            JsfUtil.addErrorMessage("专家组组员超出限制的200个字符！");
            flag = false;
        }
        if(this.checkRst.getProblems()!=null && this.checkRst.getProblems().length()>1000){
            JsfUtil.addErrorMessage("发现的问题超出限制的1000个字符！");
            flag = false;
        }
        return flag;
    }

    private boolean verifySubmit(){
        boolean flag = true;
        if(this.checkRst.getFkCheckTypeId().getRid() == null){
            JsfUtil.addErrorMessage("考核类型不能为空！");
            flag = false;
        }
        if(this.checkRst.getFkUnitId().getRid() == null){
            JsfUtil.addErrorMessage("机构名称不能为空！");
            flag = false;
        }
        if(this.checkRst.getCheckDate() == null){
            JsfUtil.addErrorMessage("质控日期不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.checkRst.getExpertLeaders())){
            JsfUtil.addErrorMessage("专家组组长不能为空！");
            flag = false;
        }else if(this.checkRst.getExpertLeaders().length()>200){
            JsfUtil.addErrorMessage("专家组组长超出限制的200个字符！");
            flag = false;
        }
        if(StringUtils.isBlank(this.checkRst.getExpertMembers())){
            JsfUtil.addErrorMessage("专家组组员不能为空！");
            flag = false;
        }else if(this.checkRst.getExpertMembers().length()>200){
            JsfUtil.addErrorMessage("专家组组员超出限制的200个字符！");
            flag = false;
        }
        if(this.checkRst.getProblems()!=null && this.checkRst.getProblems().length()>1000){
            JsfUtil.addErrorMessage("发现的问题超出限制的1000个字符！");
            flag = false;
        }
        if(this.checkRst.getFkCheckRstId().getRid()== null){
            JsfUtil.addErrorMessage("质控结论不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.checkRst.getNoticeFilePath())){
            JsfUtil.addErrorMessage("质控结果告知书必须上传！");
            flag = false;
        }
        if(this.checkRst.getFkZgRstId().getRid()!=null && StringUtils.isBlank(this.checkRst.getZgFilePath())){
            JsfUtil.addErrorMessage("整改报告必须上传！");
            flag = false;
        }
        return flag;
    }

    public void cancelAction(){
        try{
            this.checkRst.setState(0);
            commService.upsertEntity(this.checkRst);
            this.mainRid = checkRst.getRid();
            modInitAction();
            JsfUtil.addSuccessMessage("撤销成功！");
            RequestContext.getCurrentInstance().update("tabView");
            this.searchAction();
        }catch (Exception e){
            JsfUtil.addErrorMessage("撤销失败！");
            e.printStackTrace();
        }
    }

    @Override
    public String[] buildHqls() {
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("SELECT CR.RID P0, Z.FULL_NAME P1, U.UNITNAME P2, SC1.CODE_NAME P3, SC2.CODE_NAME P4, SC3.CODE_NAME P5, CR.CHECK_DATE P6, CR.STATE P7, Z.ZONE_GB P8 " +
                "FROM TD_ZW_CHECK_RST CR " +
                "         LEFT JOIN TS_UNIT U ON CR.UNIT_ID = U.RID " +
                "         LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC1 ON CR.CHECK_TYPE_ID = SC1.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC2 ON CR.CHECK_RST_ID = SC2.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC3 ON CR.ZG_RST_ID = SC3.RID " +
                "WHERE NVL(CR.DEL_MARK,0) = 0 ");
        //质控机构为本单位
        Integer unitRid = Global.getUser().getTsUnit().getRid();
        baseSql.append("    AND CR.CHECK_UNIT_ID = :unitRid ");
        super.paramMap.put("unitRid", unitRid);
        //地区
        if (ObjectUtil.isNotEmpty(this.searchZoneGb)) {
            baseSql.append("   AND Z.ZONE_GB LIKE :searchZoneGb escape '\\\' ");
            this.paramMap.put("searchZoneGb", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneGb)).trim()) + "%");
        }
        //机构名称
        if (ObjectUtil.isNotEmpty(this.searchOrgName)) {
            baseSql.append("   AND U.UNITNAME LIKE :searchOrgName escape '\\\' ");
            this.paramMap.put("searchOrgName", "%" + StringUtils.convertBFH(StringUtils.objectToString(this.searchOrgName).trim()) + "%");
        }
        //质控日期
        if (ObjectUtil.isNotEmpty(this.searchQcBeginDate)) {
            baseSql.append("   AND CR.CHECK_DATE >= TO_DATE(:searchQcBeginDate, 'YYYY-MM-DD HH24:MI:SS')");
            super.paramMap.put("searchQcBeginDate", DateUtils.formatDate(this.searchQcBeginDate) + " 00:00:00");
        }
        if (ObjectUtil.isNotEmpty(this.searchQcEndDate)) {
            baseSql.append("   AND CR.CHECK_DATE <= TO_DATE(:searchQcEndDate, 'YYYY-MM-DD HH24:MI:SS')");
            super.paramMap.put("searchQcEndDate", DateUtils.formatDate(this.searchQcEndDate) + " 23:59:59");
        }
        //质控结论
        if (ObjectUtil.isNotEmpty(this.selectQcConclusionIds)) {
            baseSql.append("   AND CR.CHECK_RST_ID IN (:selectQcConclusionIds)");
            super.paramMap.put("selectQcConclusionIds", StringUtils.string2list(this.selectQcConclusionIds, ","));
        }
        //整改结果
        if (ObjectUtil.isNotEmpty(this.selectRectificationResultIds)) {
            baseSql.append("   AND CR.ZG_RST_ID IN (:selectRectificationResultIds)");
            super.paramMap.put("selectRectificationResultIds", StringUtils.string2list(this.selectRectificationResultIds, ","));
        }
        //状态
        if (ObjectUtil.isNotEmpty(this.states)) {
            baseSql.append("   AND CR.STATE IN (:states)");
            super.paramMap.put("states", Arrays.asList(this.states));
        }

        String sql1 = "SELECT * FROM (" + baseSql + ")AA ORDER BY AA.P8, AA.P2, AA.P6 DESC";
        String sql2 = "SELECT COUNT(1) FROM (" + baseSql + ")";
        return new String[]{sql1, sql2};
    }

    public void deleteAction() {
        try {
            Map<String, Object> paramMap = new HashMap<>(16);
            String sql = "UPDATE TD_ZW_CHECK_RST SET DEL_MARK = 1 WHERE RID = :mainRid";
            paramMap.put("mainRid", this.mainRid);
            this.commService.executeSql(sql, paramMap);
            this.searchAction();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    public Integer getMainRid() {
        return mainRid;
    }

    public void setMainRid(Integer mainRid) {
        this.mainRid = mainRid;
    }

    public List<TsZone> getSearchZoneList() {
        return searchZoneList;
    }

    public void setSearchZoneList(List<TsZone> searchZoneList) {
        this.searchZoneList = searchZoneList;
    }

    public String getSearchZoneGb() {
        return searchZoneGb;
    }

    public void setSearchZoneGb(String searchZoneGb) {
        this.searchZoneGb = searchZoneGb;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public Date getSearchQcBeginDate() {
        return searchQcBeginDate;
    }

    public void setSearchQcBeginDate(Date searchQcBeginDate) {
        this.searchQcBeginDate = searchQcBeginDate;
    }

    public Date getSearchQcEndDate() {
        return searchQcEndDate;
    }

    public void setSearchQcEndDate(Date searchQcEndDate) {
        this.searchQcEndDate = searchQcEndDate;
    }

    public String getSelectQcConclusionNames() {
        return selectQcConclusionNames;
    }

    public void setSelectQcConclusionNames(String selectQcConclusionNames) {
        this.selectQcConclusionNames = selectQcConclusionNames;
    }

    public String getSelectQcConclusionIds() {
        return selectQcConclusionIds;
    }

    public void setSelectQcConclusionIds(String selectQcConclusionIds) {
        this.selectQcConclusionIds = selectQcConclusionIds;
    }

    public List<TsSimpleCode> getQcConclusionSimpleCodeList() {
        return qcConclusionSimpleCodeList;
    }

    public void setQcConclusionSimpleCodeList(List<TsSimpleCode> qcConclusionSimpleCodeList) {
        this.qcConclusionSimpleCodeList = qcConclusionSimpleCodeList;
    }

    public String getSelectRectificationResultNames() {
        return selectRectificationResultNames;
    }

    public void setSelectRectificationResultNames(String selectRectificationResultNames) {
        this.selectRectificationResultNames = selectRectificationResultNames;
    }

    public String getSelectRectificationResultIds() {
        return selectRectificationResultIds;
    }

    public void setSelectRectificationResultIds(String selectRectificationResultIds) {
        this.selectRectificationResultIds = selectRectificationResultIds;
    }

    public List<TsSimpleCode> getRectificationResultSimpleCodeList() {
        return rectificationResultSimpleCodeList;
    }

    public void setRectificationResultSimpleCodeList(List<TsSimpleCode> rectificationResultSimpleCodeList) {
        this.rectificationResultSimpleCodeList = rectificationResultSimpleCodeList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public TdZwCheckRst getCheckRst() {
        return checkRst;
    }

    public void setCheckRst(TdZwCheckRst checkRst) {
        this.checkRst = checkRst;
    }

    public List<TsSimpleCode> getCheckTypeList() {
        return checkTypeList;
    }

    public void setCheckTypeList(List<TsSimpleCode> checkTypeList) {
        this.checkTypeList = checkTypeList;
    }

    public Date getToday() {
        return today;
    }

    public void setToday(Date today) {
        this.today = today;
    }

    public String getPsnSelectType() {
        return psnSelectType;
    }

    public void setPsnSelectType(String psnSelectType) {
        this.psnSelectType = psnSelectType;
    }

    public String getUploadType() {
        return uploadType;
    }

    public void setUploadType(String uploadType) {
        this.uploadType = uploadType;
    }
}
