package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-24
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_PSNS")
@SequenceGenerator(name = "TdZwOcchethPsns", sequenceName = "TD_ZW_OCCHETH_PSNS_SEQ", allocationSize = 1)
public class TdZwOcchethPsnsZzsb implements java.io.Serializable,IZwOrgPsnsZzsb {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwOcchethInfoZzsb fkByOrgId;
	private TdZwPsninfoZzsb tdZwPsninfo;
	private String certNo;
	private String onDuty;

	public TdZwOcchethPsnsZzsb() {
	}

	public TdZwOcchethPsnsZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethPsns")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwOcchethInfoZzsb getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwOcchethInfoZzsb fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Column(name = "CERT_NO")	
	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}	
			
	@Column(name = "ON_DUTY")	
	public String getOnDuty() {
		return onDuty;
	}

	public void setOnDuty(String onDuty) {
		this.onDuty = onDuty;
	}
	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return fkByOrgId;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		this.fkByOrgId = (TdZwOcchethInfoZzsb) zwOrginfo;
	}
	@ManyToOne
	@JoinColumn(name = "EMP_ID")	
	public TdZwPsninfoZzsb getTdZwPsninfo() {
		return tdZwPsninfo;
	}

	public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
		this.tdZwPsninfo = tdZwPsninfo;
	}
	@Transient
	@Override
	public List<TdZwWorkdeptZzsb> getTdZwWorkdepts() {
		return null;
	}

	@Override
	public void setTdZwWorkdepts(List<TdZwWorkdeptZzsb> tdZwWorkdepts) {
		
	}
	@Transient
	@Override
	public List<TdZwPsnitemsZzsb> getTdZwPsnitemses() {
		return null;
	}

	@Override
	public void setTdZwPsnitemses(List<TdZwPsnitemsZzsb> tdZwPsnitemses) {
		
	}
	@Transient
	@Override
	public String getWordOfficeName() {
		return null;
	}

	@Override
	public void setWordOfficeName(String wordOfficeName) {
		
	}
	@Transient
	@Override
	public String getWordItems() {
		return null;
	}

	@Override
	public void setWordItems(String wordItems) {
		
	}
	@Transient
	@Override
	public String getWordItemStr() {
		return null;
	}

	@Override
	public void setWordItemStr(String wordItemStr) {
		
	}
	@Transient
	@Override
	public String getJlCode() {
		return null;
	}

	@Override
	public void setJlCode(String jlCode) {
		
	}
	@Transient
	@Override
	public String getLjJl() {
		return null;
	}

	@Override
	public void setLjJl(String ljJl) {
		
	}
}