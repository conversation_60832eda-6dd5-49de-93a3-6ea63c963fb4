package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

@Entity
@Table(name = "TD_ZW_SUPPORT_PSNS")
@SequenceGenerator(name = "TdZwSupportPsnsZzsbSeq", sequenceName = "TD_ZW_SUPPORT_PSNS_SEQ", allocationSize = 1)
public class TdZwSupportPsnsZzsb implements Serializable,IZwOrgPsnsZzsb{
    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwSupportOrgZzsb tdZwSupportOrg;
    private TdZwPsninfoZzsb tdZwPsninfo;
    private String onDuty;
    private String certNo;
    private String wordOfficeName;
    private String wordItems;

    public TdZwSupportPsnsZzsb(){}
    public TdZwSupportPsnsZzsb(Integer rid){
        this.rid = rid;
    }

    @Override
    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportPsnsZzsbSeq")
    public Integer getRid() {
        return this.rid;
    }

    @Override
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID")
    public TdZwSupportOrgZzsb getTdZwSupportOrg() {
        return tdZwSupportOrg;
    }

    public void setTdZwSupportOrg(TdZwSupportOrgZzsb tdZwSupportOrg) {
        this.tdZwSupportOrg = tdZwSupportOrg;
    }

    @Transient
    @Override
    public IZwOrginfoZzsb getZwOrginfo() {
        return tdZwSupportOrg;
    }

    @Override
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
        tdZwSupportOrg = (TdZwSupportOrgZzsb) zwOrginfo;
    }


    @Override
    @ManyToOne
    @JoinColumn(name = "EMP_ID" )
    public TdZwPsninfoZzsb getTdZwPsninfo() {
        return this.tdZwPsninfo;
    }

    @Override
    public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
        this.tdZwPsninfo = tdZwPsninfo;
    }
    @Override
    @Transient
    public String getCertNo() {
        return this.certNo;
    }

    @Override
    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    @Override
    @Transient
    public List<TdZwWorkdeptZzsb> getTdZwWorkdepts() {
        return null;
    }

    @Override
    public void setTdZwWorkdepts(List<TdZwWorkdeptZzsb> tdZwWorkdepts) {

    }

    @Override
    @Transient
    public List<TdZwPsnitemsZzsb> getTdZwPsnitemses() {
        return null;
    }

    @Override
    public void setTdZwPsnitemses(List<TdZwPsnitemsZzsb> tdZwPsnitemses) {

    }

    @Transient
    @Override
    public String getWordOfficeName() {
        return wordOfficeName;
    }

    @Override
    public void setWordOfficeName(String wordOfficeName) {
        this.wordOfficeName = wordOfficeName;
    }

    @Transient
    @Override
    public String getWordItems() {
        return wordItems;
    }

    @Override
    public void setWordItems(String wordItems) {
        this.wordItems = wordItems;
    }

    @Override
    @Transient
    public String getJlCode() {
        return null;
    }

    @Override
    public void setJlCode(String jlCode) {
    }

    @Override
    @Transient
    public String getLjJl() {
        return null;
    }

    @Override
    public void setLjJl(String ljJl) {

    }

    @Transient
    @Override
    public String getWordItemStr() {
        return null;
    }

    @Override
    public void setWordItemStr(String wordItemStr) {

    }
    @Override
    @Column(name="ON_DUTY")
    public String getOnDuty() {
        return onDuty;
    }

    @Override
    public void setOnDuty(String onDuty) {
        this.onDuty = onDuty;
    }
}
