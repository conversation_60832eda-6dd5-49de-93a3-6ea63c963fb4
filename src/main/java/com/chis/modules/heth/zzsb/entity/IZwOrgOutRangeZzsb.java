package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsZone;

import java.util.Date;

/**
* @Description : +外出开展职业健康检查工作能力范围20200211
* @MethodAuthor: anjing
* @Date : 2020/2/26 15:43
**/
public interface IZwOrgOutRangeZzsb {
    public Integer getRid();
    public void setRid(Integer rid);

    public IZwOrginfoZzsb getZwOrginfo();
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo);

    public TsZone getFkByZoneId();
    public void setFkByZoneId(TsZone fkByZoneId);

    public Date getCreateDate();
    public void setCreateDate(Date createDate);

    public Integer getCreateManid();
    public void setCreateManid(Integer createManid);

    public Date getModifyDate();
    public void setModifyDate(Date modifyDate);

    public Integer getModifyManid();
    public void setModifyManid(Integer modifyManid);
}
