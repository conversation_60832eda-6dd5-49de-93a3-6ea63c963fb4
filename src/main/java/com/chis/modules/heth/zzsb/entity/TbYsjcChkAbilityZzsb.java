package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-5-21
 */
@Entity
@Table(name = "TB_YSJC_CHK_ABILITY")
@SequenceGenerator(name = "TbYsjcChkAbility", sequenceName = "TB_YSJC_CHK_ABILITY_SEQ", allocationSize = 1)
public class TbYsjcChkAbilityZzsb implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsUnit fkByOrgId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TbYsjcChkAbilityZzsb() {
	}

	public TbYsjcChkAbilityZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbYsjcChkAbility")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TsUnit getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TsUnit fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}