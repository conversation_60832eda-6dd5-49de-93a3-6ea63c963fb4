package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2024-7-4
 */
@Entity
@Table(name = "TB_YSJC_CHK_METHOD")
@SequenceGenerator(name = "TbYsjcChkMethod", sequenceName = "TB_YSJC_CHK_METHOD_SEQ", allocationSize = 1)
public class TbYsjcChkMethodZzsb implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbYsjcChkAbilityRelZzsb fkByMainId;
	private TsSimpleCode fkByMethodId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	public TbYsjcChkMethodZzsb() {
	}

	public TbYsjcChkMethodZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbYsjcChkMethod")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbYsjcChkAbilityRelZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbYsjcChkAbilityRelZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "METHOD_ID")			
	public TsSimpleCode getFkByMethodId() {
		return fkByMethodId;
	}

	public void setFkByMethodId(TsSimpleCode fkByMethodId) {
		this.fkByMethodId = fkByMethodId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}