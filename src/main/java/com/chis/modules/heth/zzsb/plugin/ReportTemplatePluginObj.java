package com.chis.modules.heth.zzsb.plugin;

import com.chis.modules.system.entity.TsRpt;
import com.chis.modules.system.enumn.SystemType;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * @Description: 报告模板插件
 * 
 * @ClassAuthor pw,2022年04月19日,ReportTemplatePluginObj
 */
public class ReportTemplatePluginObj {
    public static Set<TsRpt> dataSet;
    static {
        dataSet = new HashSet<TsRpt>();
        TsRpt rpt = new TsRpt();
        rpt.setCreateDate(new Date());
        rpt.setCreateManid(1);
        rpt.setRmk("职业健康检查机构备案回执");
        rpt.setRptCod("HETH_BAHZ_1001");
        rpt.setRptnam("职业健康检查机构备案回执");
        rpt.setRptver(1);
        rpt.setSystemType(SystemType.HETH_ZZSB);
        rpt.setRptpath("/rptWord/heth/HETH_BAHZ_1001.docx");
        dataSet.add(rpt);
    }
}
