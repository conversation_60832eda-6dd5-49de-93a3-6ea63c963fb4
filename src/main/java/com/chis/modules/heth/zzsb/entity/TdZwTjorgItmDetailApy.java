package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-14
 */
@Entity
@Table(name = "TD_ZW_TJORG_ITM_DETAIL_APY")
@SequenceGenerator(name = "TdZwTjorgItmDetailApy", sequenceName = "TD_ZW_TJORG_ITM_DETAIL_APY_SEQ", allocationSize = 1)
public class TdZwTjorgItmDetailApy implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorggitemsApply fkByMainId;
	private TsSimpleCode fkByItemDetailId;
	private Integer ifExterInspect;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwTjorgItmDetailApy() {
	}

	public TdZwTjorgItmDetailApy(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgItmDetailApy")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwTjorggitemsApply getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwTjorggitemsApply fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_DETAIL_ID")			
	public TsSimpleCode getFkByItemDetailId() {
		return fkByItemDetailId;
	}

	public void setFkByItemDetailId(TsSimpleCode fkByItemDetailId) {
		this.fkByItemDetailId = fkByItemDetailId;
	}	
			
	@Column(name = "IF_EXTER_INSPECT")	
	public Integer getIfExterInspect() {
		return ifExterInspect;
	}

	public void setIfExterInspect(Integer ifExterInspect) {
		this.ifExterInspect = ifExterInspect;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}