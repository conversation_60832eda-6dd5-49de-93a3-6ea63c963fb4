package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-24
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_ITEMS")
@SequenceGenerator(name = "TdZwOcchethItems", sequenceName = "TD_ZW_OCCHETH_ITEMS_SEQ", allocationSize = 1)
public class TdZwOcchethItemsZzsb implements java.io.Serializable,IZwSvritmZzsb {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwOcchethInfoZzsb fkByOrgId;
	private Short itemCode;
	private Date createDate;
	private Integer createManid;
	
	public TdZwOcchethItemsZzsb() {
	}

	public TdZwOcchethItemsZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethItems")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwOcchethInfoZzsb getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwOcchethInfoZzsb fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Column(name = "ITEM_CODE")	
	public Short getItemCode() {
		return itemCode;
	}

	public void setItemCode(Short itemCode) {
		this.itemCode = itemCode;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	@Override
	public List<IZwSvritmSubZzsb> getZwSvritmSubList() {
		return null;
	}

	@Override
	public void setZwSvritmSubList(List<IZwSvritmSubZzsb> zwSvritmSubList) {

	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return fkByOrgId;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		this.fkByOrgId = (TdZwOcchethInfoZzsb) zwOrginfo;
	}
}