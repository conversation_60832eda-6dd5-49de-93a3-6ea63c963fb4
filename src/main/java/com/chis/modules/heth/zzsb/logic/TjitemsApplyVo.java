package com.chis.modules.heth.zzsb.logic;

/**
 *  <p>类描述：检查项目</p>
 * @ClassAuthor hsj 2022/4/18 17:44
 */
public class TjitemsApplyVo {
    /**序号*/
    private Integer xh;
    /**项目大类*/
    private String itemPro;
    /**项目小类*/
    private String itemName;
    /**是否外包*/
    private Boolean isOutter;

    private String rid;
    /**合并*/
    private Integer rowspan;
    /**是否为第一行*/
    private Boolean isFirst;

    public Integer getXh() {
        return xh;
    }

    public void setXh(Integer xh) {
        this.xh = xh;
    }

    public String getItemPro() {
        return itemPro;
    }

    public void setItemPro(String itemPro) {
        this.itemPro = itemPro;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }


    public void setIsOutter(Boolean isOutter) {
        this.isOutter = isOutter;
    }

    public Boolean getIsOutter() {
        return isOutter;
    }

    public String getRid() {
        return rid;
    }

    public void setRid(String rid) {
        this.rid = rid;
    }

    public Integer getRowspan() {
        return rowspan;
    }

    public void setRowspan(Integer rowspan) {
        this.rowspan = rowspan;
    }

    public Boolean getIsFirst() {
        return isFirst;
    }

    public void setIsFirst(Boolean first) {
        isFirst = first;
    }
}
