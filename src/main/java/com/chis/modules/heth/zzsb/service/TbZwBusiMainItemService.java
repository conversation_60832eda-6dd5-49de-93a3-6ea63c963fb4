package com.chis.modules.heth.zzsb.service;

import com.chis.common.utils.ObjectUtil;
import com.chis.modules.heth.zzsb.entity.TbZwBusiMainItem;
import com.chis.modules.heth.zzsb.logic.BusiItemInfoDTO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 业务范围与重点、一般检测项目关系维护<p>类描述：</p>
 *
 * @ClassAuthor hsj 2024-07-18 13:46
 */
@Service
@Transactional(readOnly = false)
public class TbZwBusiMainItemService extends AbstractTemplate {
    @Transactional(readOnly = true)
    public List<TbZwBusiMainItem> findTbZwBusiMainItemByBusId(Integer simpleId, Integer type) {
        if (ObjectUtil.isNull(simpleId)) {
            return new ArrayList<>();
        }
        String hql = " SELECT t FROM TbZwBusiMainItem t WHERE t.fkByBusId.rid = " + simpleId;
        if (!ObjectUtil.isNull(type)) {
            hql += " AND t.checkType = " + type;
        }
        return this.findByHql(hql, TbZwBusiMainItem.class);
    }

    @Transactional(readOnly = false)
    public void saveTbZwBusiMainItemList(List<BusiItemInfoDTO> list, Integer simpleId, Integer type) {
        boolean f = CollectionUtils.isEmpty(list) || ObjectUtil.isNull(simpleId) || ObjectUtil.isNull(type);
        if (f) {
            return;
        }
        List<TbZwBusiMainItem> dtos = new ArrayList<>();
        for (BusiItemInfoDTO itemInfo : list) {
            TbZwBusiMainItem item = new TbZwBusiMainItem();
            item.setFkByBusId(new TsSimpleCode(simpleId));
            item.setCheckType(type);
            item.setFkByItemId(new TsSimpleCode(itemInfo.getRid()));
            item.setCreateDate(new Date());
            item.setCreateManid(Global.getUser().getRid());
            dtos.add(item);
        }
        this.saveBatchObjs(dtos);
    }

    /**
     * <p>方法描述：删除</p>
     *
     * @MethodAuthor hsj 2024-07-18 14:36
     */
    @Transactional(readOnly = false)
    public void delTbZwBusiMainItemByItemId(Integer busiId) {
        if (ObjectUtil.isNull(busiId)) {
            return;
        }
        String sql = "DELETE TB_ZW_BUSI_MAIN_ITEM T WHERE T.rid = " + busiId;
        this.executeSql(sql, null);
    }
}
