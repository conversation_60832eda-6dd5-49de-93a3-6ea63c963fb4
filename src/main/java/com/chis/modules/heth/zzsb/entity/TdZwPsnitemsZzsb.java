package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_PSNITEMS")
@SequenceGenerator(name = "TdZwPsnitemsSeq", sequenceName = "TD_ZW_PSNITEMS_SEQ", allocationSize = 1)
public class TdZwPsnitemsZzsb implements java.io.Serializable {
	private static final long serialVersionUID = 7680587031422427709L;

	private Integer rid;
	private TdZwTjorgpsnsZzsb tdZwTjorgpsns;
	private Short itemCode;

	public TdZwPsnitemsZzsb() {
	}

	public TdZwPsnitemsZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwPsnitemsSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ID" )
	public TdZwTjorgpsnsZzsb getTdZwTjorgpsns() {
		return this.tdZwTjorgpsns;
	}

	public void setTdZwTjorgpsns(TdZwTjorgpsnsZzsb tdZwTjorgpsns) {
		this.tdZwTjorgpsns = tdZwTjorgpsns;
	}

	@Column(name = "ITEM_CODE" , precision = 1, scale = 0)
	public Short getItemCode() {
		return this.itemCode;
	}

	public void setItemCode(Short itemCode) {
		this.itemCode = itemCode;
	}

}