package com.chis.modules.heth.zzsb.logic;

import java.io.Serializable;
import java.util.Objects;

/**
 * <p>类描述：业务范围与重点、一般检测项目关系维护项目选择对象</p>
 *
 * @ClassAuthor hsj 2024-07-18 14:10
 */
public class BusiItemInfoDTO implements Serializable {

    private Integer rid;
    private String name;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BusiItemInfoDTO that = (BusiItemInfoDTO) o;
        return Objects.equals(rid, that.rid);
    }
}
