package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.logic.QualOrgApplyParamPO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <p>描述 资质机构备案审核查询</p>
 * 
 * <AUTHOR>
 * @ClassAuthor gongzhe,2022/4/20 9:09,ZwTjorginfoApplyListBean
 */
@ManagedBean(name = "zwTjorginfoApplyListBean")
@ViewScoped
public class ZwTjorginfoApplyListBean extends FacesEditBean {
	private static final long serialVersionUID = 1L;

	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
	/**判断是否是详情页 1 详情 0 编辑*/
	private Integer ifView;
	/**记录rid*/
	private Integer rid;
	/** 菜单标记 0初审1终审*/
	private String tag;
	/** 初审标记*/
	private Boolean firstTrial;
	/** 终审标记*/
	private Boolean finalTrial;
	/** 查询条件以下↓*************************/
	private List<TsZone> zoneList;
	/** 备案单位地区*/
	private String searchZoneCode;
	private String searchZoneName;
	/** 备案单位*/
	private String searchOrgName;
	/** 申请日期*/
	private Date searchApplyDateStart;
	private Date searchApplyDateEnd;
	/** 机构资质*/
	private String searchQualType;
	private List<SelectItem> searchQualTypeList;
	/** 申请类型*/
	private String searchApplyTypeState;
	private String selectApplyTypeNames;
	private List<TsSimpleCode> applyTypeList;
	/** 审核状态*/
	private String[] states;
	private List<SelectItem> stateList;

	/** 查询条件以上↑*************************/
	/** 保存或者修改 编辑页的Bean */
	private TdZwQualOrgApplyInfoBaseBean applyInfoBaseBean;
	/** 是否显示外出开展职业健康检查工作能力地区(外检车辆) 无配置参数返回null */
	private Boolean ifHethFj;
	/** 职业健康检查机构是否有人员汇总及填报说明人员汇总 */
	private Boolean ifPersonSummary;
	/** 检查机构是否有质量管理体系 */
	private Boolean ifQualAnnex;
	/** 资质备案有无有效期限 */
	private Boolean ifValidityPeriod;
	/**驳回理由*/
	private String backRsn;
	/** 机构类型 1：职业健康检查机构 2：职业病诊断机构 */
	private Integer orgType;


	public ZwTjorginfoApplyListBean() {
		this.applyInfoBaseBean = new TdZwQualOrgApplyInfoBaseBean();
		super.ifSQL = true;
		tag = JsfUtil.getRequestParameter("tag");
		firstTrial = "0".equals(tag);
		finalTrial = "1".equals(tag);
		if(!firstTrial && !finalTrial){
			return;
		}
		initParam();
		initPropertiesParam();
	}

	private void initParam(){
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		// 地区初始化
		if (null == this.zoneList || this.zoneList.size() <= 0) {
				this.searchZoneCode = tsZone.getZoneGb();
				this.searchZoneName = tsZone.getZoneName();
				this.zoneList = this.systemModuleService.findZoneListICanSee(false, tsZone.getZoneGb());
		}
		Date now = new Date();
		// 默认：当前日期往前推一年
		this.searchApplyDateStart = DateUtils.addYears(now,-1);
		// 默认：今天
		this.searchApplyDateEnd = now;
		initQualType();
		initApplyType();
		initState();
		this.searchAction();
	}

	/**
	 * @Description: 初始化配置文件参数
	 *
	 * @MethodAuthor pw,2022年04月16日
	 */
	private void initPropertiesParam(){
		this.ifHethFj = true;
		this.ifPersonSummary = false;
		this.ifQualAnnex = true;
		this.ifValidityPeriod = true;
	}

	/**
	 * <p>描述 初始化机构资质</p>
	 *
	 * @MethodAuthor gongzhe,2022/4/20 9:37,initQualType
	 * @return void
	 */
	private void initQualType(){
		String extends1 = "";
		if(firstTrial){
			//初审
			if(Global.getBtnSet().contains("zzsb_zzjgbashcs_zyjkjcjg")){
				extends1 += ",1";
			}
			if(Global.getBtnSet().contains("zzsb_zzjgbashcs_zybzdjg")){
				extends1 += ",2";
			}
		}else if(finalTrial){
			//终审
			if(Global.getBtnSet().contains("zzsb_zzjgbashzs_zyjkjcjg")){
				extends1 += ",1";
			}
			if(Global.getBtnSet().contains("zzsb_zzjgbashzs_zybzdjg")){
				extends1 += ",2";
			}
		}
		if(StringUtils.isNotBlank(extends1)){
			List<TsSimpleCode> list = commService.findSimpleCodesByTypeIdAndExtends1("5562",extends1.substring(1));
			if(!CollectionUtils.isEmpty(list)){
				searchQualTypeList = new ArrayList<>();
				if(list.size()>1){
					searchQualTypeList.add(new SelectItem("","--全部--"));
				}
				for (TsSimpleCode c:list) {
					searchQualTypeList.add(new SelectItem(c.getRid(),c.getCodeName()));
				}
			}
		}
	}
	/**
	 * <p>描述 初始化申请类型</p>
	 *
	 * @MethodAuthor gongzhe,2022/4/20 9:37,initApplyType
	 * @return void
	 */
	private void initApplyType(){
		applyTypeList = commService.findLevelSimpleCodesByTypeId("5563");
	}
	/**
	 * <p>描述 初始化审核状态</p>
	 *
	 * @MethodAuthor gongzhe,2022/4/20 9:37,initState
	 * @return void
	 */
	private void initState(){
		stateList = new ArrayList<>();
		if(firstTrial){
			stateList.add(new SelectItem("1","待初审"));
			stateList.add(new SelectItem("2","初审驳回"));
			stateList.add(new SelectItem("3","初审通过"));
			stateList.add(new SelectItem("4","终审驳回"));
			stateList.add(new SelectItem("5","终审通过"));
			states = new String[]{"1"};
		}else if(finalTrial){
			stateList.add(new SelectItem("3","待终审"));
			stateList.add(new SelectItem("4","终审驳回"));
			stateList.add(new SelectItem("5","终审通过"));
			states = new String[]{"3"};
		}
	}


	@Override
	public void addInit() {

	}

	@Override
	public void viewInit() {

	}

	public void preModAction(){
		QualOrgApplyParamPO paramPO = new QualOrgApplyParamPO();
		paramPO.setOrgApplyRid(this.rid);
		paramPO.setIfHethFj(this.ifHethFj);
		paramPO.setIfQualAnnex(this.ifQualAnnex);
		paramPO.setIfPersonSummary(this.ifPersonSummary);
		//是否为终审
		paramPO.setIfFinalAudit(this.finalTrial);
		paramPO.setIfValidityPeriod(this.ifValidityPeriod);
		paramPO.setIfView(null == this.ifView || 1 == this.ifView);
		this.applyInfoBaseBean = new TdZwQualOrgApplyInfoBaseBean(paramPO);
		if(null == this.applyInfoBaseBean.getTjorginfoApply()){
			JsfUtil.addErrorMessage("操作失败！");
			return;
		}
		this.modInitAction();
		RequestContext.getCurrentInstance().update("tabView");
	}
	@Override
	public void modInit() {

	}

	@Override
	public void saveAction() {

	}

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		sb.append(" FROM TD_ZW_TJORGINFO_APPLY T ");
		sb.append(" LEFT JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID ");
		sb.append(" LEFT JOIN TS_UNIT T2 ON T.ORG_ID = T2.RID  ");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.QUAL_TYPE_ID = T3.RID ");
		sb.append(" LEFT JOIN TS_SIMPLE_CODE T4 ON T.APPLY_TYPE_ID = T4.RID ");
		sb.append(" WHERE 1=1 ");
		if (StringUtils.isNotBlank(this.searchZoneCode)) {
			sb.append(" AND T1.ZONE_GB LIKE :zonecode escape '\\\'");
			this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
		}
		if (StringUtils.isNotBlank(this.searchOrgName)) {
			sb.append(" AND T.ORG_NAME LIKE :orgName escape '\\\'");
			this.paramMap.put("orgName", "%" + StringUtils.convertBFH(this.searchOrgName.trim()) + "%");
		}
		if (null != this.searchApplyDateStart) {
			sb.append(" AND T.APPLY_DATE>= TO_DATE('").append(DateUtils.formatDate(this.searchApplyDateStart))
					.append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
		}
		if (null != this.searchApplyDateEnd) {
			sb.append(" AND T.APPLY_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchApplyDateEnd))
					.append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		if (StringUtils.isNotBlank(this.searchQualType)) {
			sb.append(" AND T.QUAL_TYPE_ID = ").append(this.searchQualType);
		}
		if (StringUtils.isNotBlank(this.searchApplyTypeState)) {
			sb.append(" AND T.APPLY_TYPE_ID in (").append(this.searchApplyTypeState).append(")");
		}
		if (null != this.states && this.states.length>0) {
			sb.append(" AND T.STATE in(").append(StringUtils.join(this.states,",")).append(")");
		}else{
			if(firstTrial){
				//初审
				sb.append(" AND T.STATE >=1 ");
			}else if(finalTrial){
				//终审
				sb.append(" AND T.STATE >=3 ");
			}
		}

		String h2 = "SELECT COUNT(*) " + sb;
		String h1 = " SELECT T.RID, CASE WHEN T1.ZONE_TYPE >2 THEN SUBSTR(T1.FULL_NAME, INSTR(T1.FULL_NAME,'_')+1) ELSE T1.FULL_NAME END ZONE_NAME,T.ORG_NAME"
				+ ", T3.CODE_NAME qualType, T4.CODE_NAME applyType, T.APPLY_DATE, T.STATE, T.FST_BACK_RSN, T.FINAL_BACK_RSN,T3.EXTENDS1  "
				+ sb + " ORDER BY T.STATE,T.APPLY_DATE,T1.ZONE_GB,T.ORG_NAME ";
		return new String[] { h1, h2 };
	}

	/**
	 * <p>方法描述：通过确认弹框</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-04-26
	 **/
	public void openConfirmDialog(){
		if(this.applyInfoBaseBean.submitValidate()){
			return;
		}else{
			if(firstTrial){
				RequestContext.getCurrentInstance().execute("PF('cd').show();");
			}else if(finalTrial){
				//终审通过要验证
				if(null == applyInfoBaseBean.getReportReceipt() || StringUtils.isBlank(this.applyInfoBaseBean.getReportReceipt().getFilePath())){
					JsfUtil.addErrorMessage("备案回执不允许为空！");
					return;
				}
				if(null == this.orgType || null == this.applyInfoBaseBean.getOrgInfoState()){
					return;
				}else if(1 == this.orgType || 2 == this.orgType){
					RequestContext.getCurrentInstance().execute("PF('zs').show();");
				}else{
					return;
				}
			}

		}
	}

	/**
	 * <p>方法描述：审核通过</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-04-21
	 **/
	public void beforeSaveAction(){
		if(null == this.applyInfoBaseBean || null == this.applyInfoBaseBean.getTjorginfoApply()){
			return;
		}

		if(firstTrial){
			this.applyInfoBaseBean.getTjorginfoApply().setFstChkDate(new Date());
			this.applyInfoBaseBean.getTjorginfoApply().setFkByFstChkOrgId(Global.getUser().getTsUnit());
			this.applyInfoBaseBean.getTjorginfoApply().setFkByFstChkPsnId(Global.getUser());
			//初审
			this.applyInfoBaseBean.getTjorginfoApply().setState(3);
			if(!this.applyInfoBaseBean.submitAction()){
				return;
			}
			this.ifView=1;
		}else if(finalTrial){
			this.applyInfoBaseBean.getTjorginfoApply().setFinalChkDate(new Date());
			this.applyInfoBaseBean.getTjorginfoApply().setFkByFinalChkOrgId(Global.getUser().getTsUnit());
			this.applyInfoBaseBean.getTjorginfoApply().setFkByFinalChkPsnId(Global.getUser());
			//终审 更新状态 终审日期 终审机构ID 终审人员ID 由sql执行
			this.finalAuditAction();
		}
		RequestContext.getCurrentInstance().update("tabView");
		this.searchAction();
	}
	/**
	 *  <p>方法描述：终审</p>
	 * @MethodAuthor hsj 2022/4/24 16:29
	 */
	public void finalAuditAction() {
		try{
			this.applyInfoBaseBean.orgApplyService.finalAuditInfo(this.orgType,this.applyInfoBaseBean.getTjorginfoApply());
			this.applyInfoBaseBean.getTjorginfoApply().setState(5);
			this.ifView = 1;
			JsfUtil.addSuccessMessage("终审成功！");
		}catch(Exception e){
			e.printStackTrace();
			JsfUtil.addErrorMessage("终审失败！");
		}
	}

	/**
	 * <p>方法描述：打开驳回理由弹框</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-04-26
	 **/
	public void openReasonDialog(){
		//驳回理由置空
		this.backRsn = null;
		RequestContext.getCurrentInstance().execute("PF('ReasonDialog').show();");
		RequestContext.getCurrentInstance().update("tabView:editForm:reasonDialog");
	}
	/**
	 * <p>方法描述：驳回理由</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-04-21
	 **/
	public void returnAction(){
		if(StringUtils.isBlank(this.backRsn)){
			JsfUtil.addErrorMessage("驳回理由不能为空！");
			return;
		}
		if(firstTrial){
			this.applyInfoBaseBean.getTjorginfoApply().setState(2);
			this.applyInfoBaseBean.getTjorginfoApply().setFstBackRsn(this.backRsn);
		}else if(finalTrial){
			//终审驳回只需更新状态和驳回信息
			this.applyInfoBaseBean.getTjorginfoApply().setState(4);
			this.applyInfoBaseBean.getTjorginfoApply().setFinalBackRsn(this.backRsn);
		}
		try {
			if (!this.applyInfoBaseBean.updateFstChkApply()) {
				return;
			}
			//驳回成功后跳转详情页
			this.ifView = 1;
			//服务明细初始化
			applyInfoBaseBean.setIfView(true);
			this.applyInfoBaseBean.initJkItemRowList();
			RequestContext.getCurrentInstance().execute("PF('ReasonDialog').hide();");
			RequestContext.getCurrentInstance().update("tabView");
			this.searchAction();
			JsfUtil.addSuccessMessage("驳回成功！");
		}catch (Exception e){
			JsfUtil.addErrorMessage("驳回失败！");
		}
	}

	/**
	 * <p>方法描述：初审撤销</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-04-29
	 **/
	public void applyCancel(){
		if(null == this.applyInfoBaseBean || null == this.applyInfoBaseBean.getTjorginfoApply()){
			return;
		}
		this.ifView=0;
		this.applyInfoBaseBean.getTjorginfoApply().setState(1);
		this.applyInfoBaseBean.cancelAction();
		this.searchAction();
	}

	public Boolean getFirstTrial() {
		return firstTrial;
	}

	public void setFirstTrial(Boolean firstTrial) {
		this.firstTrial = firstTrial;
	}

	public Boolean getFinalTrial() {
		return finalTrial;
	}

	public void setFinalTrial(Boolean finalTrial) {
		this.finalTrial = finalTrial;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchOrgName() {
		return searchOrgName;
	}

	public void setSearchOrgName(String searchOrgName) {
		this.searchOrgName = searchOrgName;
	}

	public Date getSearchApplyDateStart() {
		return searchApplyDateStart;
	}

	public void setSearchApplyDateStart(Date searchApplyDateStart) {
		this.searchApplyDateStart = searchApplyDateStart;
	}

	public Date getSearchApplyDateEnd() {
		return searchApplyDateEnd;
	}

	public void setSearchApplyDateEnd(Date searchApplyDateEnd) {
		this.searchApplyDateEnd = searchApplyDateEnd;
	}

	public String getSearchQualType() {
		return searchQualType;
	}

	public void setSearchQualType(String searchQualType) {
		this.searchQualType = searchQualType;
	}

	public List<SelectItem> getSearchQualTypeList() {
		return searchQualTypeList;
	}

	public void setSearchQualTypeList(List<SelectItem> searchQualTypeList) {
		this.searchQualTypeList = searchQualTypeList;
	}

	public String getSearchApplyTypeState() {
		return searchApplyTypeState;
	}

	public List<TsSimpleCode> getApplyTypeList() {
		return applyTypeList;
	}

	public void setApplyTypeList(List<TsSimpleCode> applyTypeList) {
		this.applyTypeList = applyTypeList;
	}

	public void setSearchApplyTypeState(String searchApplyTypeState) {
		this.searchApplyTypeState = searchApplyTypeState;
	}

	public String getSelectApplyTypeNames() {
		return selectApplyTypeNames;
	}

	public void setSelectApplyTypeNames(String selectApplyTypeNames) {
		this.selectApplyTypeNames = selectApplyTypeNames;
	}

	public String[] getStates() {
		return states;
	}

	public void setStates(String[] states) {
		this.states = states;
	}

	public List<SelectItem> getStateList() {
		return stateList;
	}

	public void setStateList(List<SelectItem> stateList) {
		this.stateList = stateList;
	}

	public Integer getIfView() {
		return ifView;
	}

	public void setIfView(Integer ifView) {
		this.ifView = ifView;
	}

	public TdZwQualOrgApplyInfoBaseBean getApplyInfoBaseBean() {
		return applyInfoBaseBean;
	}

	public void setApplyInfoBaseBean(TdZwQualOrgApplyInfoBaseBean applyInfoBaseBean) {
		this.applyInfoBaseBean = applyInfoBaseBean;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	public String getBackRsn() {
		return backRsn;
	}

	public void setBackRsn(String backRsn) {
		this.backRsn = backRsn;
	}

	public Integer getOrgType() {
		return orgType;
	}

	public void setOrgType(Integer orgType) {
		this.orgType = orgType;
	}
}
