package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 质量管理体系
 * <AUTHOR>
 * @createTime 2020-10-17
 */
@Entity
@Table(name = "TD_ZW_TJORG_QUAL_ANNEX")
@SequenceGenerator(name = "TdZwTjorgQualAnnex", sequenceName = "TD_ZW_TJORG_QUAL_ANNEX_SEQ", allocationSize = 1)
public class TdZwTjorgQualAnnexZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	/** 主表 */
	private TdZwTjorginfoZzsb fkByMainId;
	/** 制度 */
	private TsSimpleCode fkByRuleId;
	/** 其他制度名称 */
	private String otherRuleName;
	/** 有无 有1 无0 */
	private Integer ifHas;
	/** 附件路径 */
	private String annexPath;
	/** 创建日期 */
	private Date createDate;
	/** 创建人 */
	private Integer createManid;
	/** 修改日期 */
	private Date modifyDate;
	/** 修改人 */
	private Integer modifyManid;
	
	public TdZwTjorgQualAnnexZzsb() {
	}

	public TdZwTjorgQualAnnexZzsb(Integer rid) {
		this.rid = rid;
	}

	public TdZwTjorgQualAnnexZzsb(TsSimpleCode fkByRuleId){
		this.fkByRuleId = fkByRuleId;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgQualAnnex")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwTjorginfoZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwTjorginfoZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "RULE_ID")			
	public TsSimpleCode getFkByRuleId() {
		return fkByRuleId;
	}

	public void setFkByRuleId(TsSimpleCode fkByRuleId) {
		this.fkByRuleId = fkByRuleId;
	}	
			
	@Column(name = "OTHER_RULE_NAME")	
	public String getOtherRuleName() {
		return otherRuleName;
	}

	public void setOtherRuleName(String otherRuleName) {
		this.otherRuleName = otherRuleName;
	}	
			
	@Column(name = "IF_HAS")	
	public Integer getIfHas() {
		return ifHas;
	}

	public void setIfHas(Integer ifHas) {
		this.ifHas = ifHas;
	}	
			
	@Column(name = "ANNEX_PATH")	
	public String getAnnexPath() {
		return annexPath;
	}

	public void setAnnexPath(String annexPath) {
		this.annexPath = annexPath;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}