package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.service.OrgZzCheckServiceImpl;
import com.chis.modules.heth.zzsb.service.ZzsbZwIntellReportServiceImpl;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * 资质机构审核Bean
 *
 * <AUTHOR>
 * @version 1.1
 */
@ManagedBean(name = "orgZzCheckListBean")
@ViewScoped
public class OrgZzCheckListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    private final OrgZzCheckServiceImpl orgZzCheckService = SpringContextHolder.getBean(OrgZzCheckServiceImpl.class);

    private ZzsbZwIntellReportServiceImpl zwIntellReportServiceImpl = SpringContextHolder
            .getBean(ZzsbZwIntellReportServiceImpl.class);
    /**
     * 机构资质RID
     */
    private Integer rid;
    /**
     * 机构资质类型
     */
    private Integer zzType;
    /**
     * 查询条件：机构地区集合
     */
    private List<TsZone> orgZoneList = new ArrayList<>();
    /**
     * 查询条件：机构地区编码
     */
    private String searchOrgZoneCode;
    /**
     * 查询条件：机构地区名称
     */
    private String searchOrgZoneName;
    /**
     * 查询条件：机构名称
     */
    private String searchOrgName;
    /**
     * 查询条件：资质类型
     */
    private String searchZzType;
    private List<SelectItem> zzTypeList;
    private Map<String, String> zzTypeMap;
    private Map<String, String> zzTypeTableNameMap;
    /**
     * 查询条件：状态
     * <pre>2：待审核<pre>3：已退回<pre>1：已审核
     */
    private String[] states;
    private List<SelectItem> stateList = new ArrayList<>();
    private Map<String, String> stateMap;

    /**默认审核意见*/
    private String defaultAuditAdv;
    /** 审核页审核结果 */
    private Integer editCheckRst;
    /** 审核页审核意见 */
    private String editCheckAudit;
    /** 资质机构信息托管Bean */
    private OrgInfoViewBean infoViewBean;

    /** 人员汇总 */
    private List<List<Object[]>> personSummaryList;
    /** 人员汇总显示 */
    private List<List<Object[]>> showPersonSummaryList;
    /** 人员汇总 0不显示更多和收起 1显示更多 2显示收起 */
    private Integer psnSummaryState;
    /** 仪器汇总 */
    private List<List<Object[]>> instSummaryList;
    /** 仪器汇总显示 */
    private List<List<Object[]>> showInstSummaryList;
    /** 仪器汇总 0不显示更多和收起 1显示更多 2显示收起 */
    private Integer instSummaryState;
    /** 未具备的必检项目 */
    private String unHaveItems;
    /** 未具备的制度 */
    private String unHaveQualAnnex;
    /** 是否有检查项目预警配置 */
    private Boolean ifHaveItemWarn;
    /** 是否有未具备的制度 */
    private Boolean ifHaveQualAnnexWarn;

    public OrgZzCheckListBean() {
        init();
        this.searchAction();
    }
    private void init() {
        this.ifSQL = true;
        //地区
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        //查询条件：地区
        if (ObjectUtil.isEmpty(this.orgZoneList)) {
            this.orgZoneList = this.commService.findZoneListByGbAndType(tsZone.getZoneGb(), true, "", "");
            if (ObjectUtil.isNotEmpty(this.orgZoneList)) {
                this.searchOrgZoneName = this.orgZoneList.get(0).getZoneName();
                this.searchOrgZoneCode = this.orgZoneList.get(0).getZoneCode();
            }
        }
        //查询条件：资质类型
        this.zzTypeList = new ArrayList<>();
        this.zzTypeMap = new HashMap<>(16);
        this.zzTypeMap.put("0", "职业健康检查机构");
        this.zzTypeTableNameMap = new HashMap<>(16);
        this.zzTypeTableNameMap.put("0", "TD_ZW_TJORGINFO");
        this.zzTypeTableNameMap.put("1", "TD_ZW_SRVORGINFO");
        this.zzTypeTableNameMap.put("2", "TD_ZW_DIAGORGINFO");
        this.zzTypeTableNameMap.put("3", "TD_ZW_OCCHETH_INFO");

        Set<String> btnSet = Global.getBtnSet();
        if (btnSet.contains("heth_zzjgsh_zyjkjcjg")) {
            this.zzTypeList.add(new SelectItem(0, "职业健康检查机构"));
        }
        //查询条件：状态
        this.states = new String[]{"2"};
        this.stateList.add(new SelectItem("2", "待审核"));
        this.stateList.add(new SelectItem("3", "已退回"));
        this.stateList.add(new SelectItem("1", "已审核"));
        this.stateMap = new HashMap<>(16);
        this.stateMap.put("2", "待审核");
        this.stateMap.put("3", "已退回");
        this.stateMap.put("1", "已审核");

        //取参数
        this.defaultAuditAdv = PropertyUtils.getValue("defaultAuditAdv");
        this.infoViewBean = new OrgInfoViewBean();
        this.psnSummaryState = 0;
        this.instSummaryState = 0;
        this.initHaveWarn();

    }

    @Override
    public void searchAction() {
        if (ObjectUtil.isEmpty(this.zzTypeList)) {
            JsfUtil.addErrorMessage("无可查询资质类型！");
            return;
        }
        super.searchAction();
        RequestContext currentInstance = RequestContext.getCurrentInstance();
        currentInstance.update("tabView:mainForm:dataTable");
    }

    @Override
    public String[] buildHqls() {
        String sql = buildSql(1);
        String h1 = "SELECT * FROM (" + sql + ")AA ORDER BY AA.ZONE_GB, AA.ORG_NAME, AA.ZZ_TYPE";
        String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        return new String[]{h1, h2};
    }

    /**
     * 生成SQL
     *
     * @param type SQL类型<pre>1：查询<pre>2：全部审核
     * @return SQL
     */
    private String buildSql(int type) {
        this.paramMap = new HashMap<>();
        Map<String, String> sqlBaseMap = new HashMap<>(16);
        //职业健康检查机构
        StringBuilder sqlBase = new StringBuilder();
        sqlBase.append("SELECT ");
        if (type == 2) {
            sqlBase.append("   T.RID, 0 AS ORG_TYPE ");
        } else {
            sqlBase.append("   T.RID, Z.FULL_NAME ZONE_NAME, T.ORG_NAME, '' ZZ_TYPE_NAME, T.STATE AS STATESTR, T.AUDIT_ADV, '0' ZZ_TYPE, Z.ZONE_GB, T.STATE AS STATE, 0 AS ORG_TYPE ");
        }
        sqlBase.append("FROM TD_ZW_TJORGINFO T ");
        sqlBase.append("    LEFT JOIN TS_UNIT U ON T.ORG_ID = U.RID ");
        sqlBase.append("    LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID ");
        sqlBase.append("WHERE 1 = 1 ");
        sqlBaseMap.put("0", sqlBase.toString());
        //查询条件
        StringBuilder querySb = new StringBuilder();
        //机构地区
        if (StringUtils.isNotBlank(this.searchOrgZoneCode)) {
            querySb.append(" AND Z.ZONE_GB LIKE :searchOrgZoneCode ");
            this.paramMap.put("searchOrgZoneCode", ZoneUtil.zoneSelect(this.searchOrgZoneCode) + "%");
        }
        //机构名称
        if (StringUtils.isNotBlank(this.searchOrgName)) {
            querySb.append(" AND T.ORG_NAME LIKE :searchOrgName escape '\\\' ");
            this.paramMap.put("searchOrgName", "%" + StringUtils.convertBFH(this.searchOrgName.trim()) + "%");
        }
        //状态
        if (type == 2) {
            querySb.append(" AND T.STATE = '2' ");
        } else {
            List<String> stateValueList;
            if (ObjectUtil.isNotEmpty(this.states)) {
                stateValueList = Arrays.asList(this.states);
                querySb.append(" AND T.STATE IN (:stateList)");
                this.paramMap.put("stateList", stateValueList);
            } else {
                stateValueList = new ArrayList<>(this.stateMap.keySet());
            }
            querySb.append(" AND T.STATE IN (:stateList)");
            this.paramMap.put("stateList", stateValueList);
        }

        StringBuilder sql = new StringBuilder();
        if (ObjectUtil.isNotEmpty(this.searchZzType)) {
            sql.append(sqlBaseMap.get(this.searchZzType)).append(querySb);
        } else {
            for (SelectItem selectItem : this.zzTypeList) {
                if (sqlBaseMap.containsKey(StringUtils.objectToString(selectItem.getValue()))) {
                    if (ObjectUtil.isNotEmpty(sql.toString())) {
                        sql.append(" UNION ALL ");
                    }
                    sql.append(sqlBaseMap.get(StringUtils.objectToString(selectItem.getValue()))).append(querySb);
                }
            }
        }
        return sql.toString();
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[1]);
            obj[1] = fullName.substring(fullName.indexOf("_") + 1);
            //资质类型
            if (this.zzTypeMap.containsKey(StringUtils.objectToString(obj[6]))) {
                obj[3] = this.zzTypeMap.get(StringUtils.objectToString(obj[6]));
            } else {
                obj[3] = "";
            }
            //状态
            if (this.stateMap.containsKey(StringUtils.objectToString(obj[4]))) {
                obj[4] = this.stateMap.get(StringUtils.objectToString(obj[4]));
            } else {
                obj[4] = "";
            }
            //退回原因
            if (!"已退回".equals(obj[4])) {
                obj[5] = "";
            }
        }
    }

    /**
     * 审核弹出框(用于全部审核)
     *
     */
    public void openCheckConfirmDialog() {
        //默认通过
        this.editCheckRst = 1;
        this.editCheckAudit = StringUtils.objectToString(this.defaultAuditAdv);
        //页面没有选择待审核状态时直接提示无待审核的数据！
        if (ObjectUtil.isNotEmpty(this.states) && !Arrays.asList(this.states).contains("2")) {
            JsfUtil.addErrorMessage("无待审核的数据！");
            return;
        }
        //全部审核
        String sql = "SELECT COUNT(*) FROM (" + buildSql(2) + ")";
        int count = this.commService.findCountBySql(sql, this.paramMap);
        if (count == 0) {
            JsfUtil.addErrorMessage("无待审核的数据！");
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('CheckConfirmDialog').show()");
        RequestContext.getCurrentInstance().update("tabView:mainForm:checkConfirmDialog");
    }

    public void checkBatchAction() {
        boolean flag = false;
        if(null == this.editCheckRst){
            JsfUtil.addErrorMessage("请选择审核结果！");
            flag = true;
        }
        if(null != this.editCheckRst && 2 == this.editCheckRst && StringUtils.isBlank(this.editCheckAudit)){
            JsfUtil.addErrorMessage("审核意见不允许为空！");
            flag = true;
        }
        if(flag){
            return;
        }
        Map<String, List<Integer>> orgCheckListMap = new HashMap<>(16);
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(buildSql(2), this.paramMap));
        for (Object[] data : dataList) {
            Integer rid = ObjectUtil.convert(Integer.class, data[0]);
            String type = StringUtils.objectToString(data[1]);
            if (ObjectUtil.isEmpty(rid) || ObjectUtil.isEmpty(type) || !this.zzTypeTableNameMap.containsKey(type)) {
                continue;
            }
            String tableName = this.zzTypeTableNameMap.get(type);
            if (!orgCheckListMap.containsKey(tableName)) {
                orgCheckListMap.put(tableName, new ArrayList<Integer>());
            }
            orgCheckListMap.get(tableName).add(rid);
        }
        Short state = (short) (1 == this.editCheckRst ? 1 : 3);
        try {
            int count = this.orgZzCheckService.batchOrgCheck(
                    orgCheckListMap, state, this.editCheckRst, this.editCheckAudit, Global.getUser().getRid()
            );
            JsfUtil.addSuccessMessage("全部审核成功" + count + "条数据。");
            RequestContext.getCurrentInstance().execute("PF('CheckConfirmDialog').hide();");
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("全部审核失败！");
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {
        this.editCheckRst = this.infoViewBean.getZwOrginfoEntity().getCheckRst();
        this.editCheckAudit = this.infoViewBean.getZwOrginfoEntity().getAuditAdv();
    }

    @Override
    public void modInit() {
        //审核结果默认通过
        if(null == this.infoViewBean.getZwOrginfoEntity().getCheckRst()){
            this.editCheckRst = 1;
            this.editCheckAudit = this.defaultAuditAdv;
        }else{
            this.editCheckRst = this.infoViewBean.getZwOrginfoEntity().getCheckRst();
            this.editCheckAudit = this.infoViewBean.getZwOrginfoEntity().getAuditAdv();
        }
    }

    /**
     * <p>方法描述： 审核以及详情跳转 </p>
     * @MethodAuthor： pw 2022/10/31
     **/
    public void modAndViewComm(int ifView){
        this.executeInitOrgInfo();
        if(null == this.infoViewBean.getZwOrginfoEntity()){
            JsfUtil.addErrorMessage("未找到对应的资质机构信息！");
            return;
        }
        if(1 == ifView){
            this.viewInitAction();
        }else{
            this.modInitAction();
        }
    }

    @Override
    public void saveAction() {

    }

    /**
     * <p>方法描述： 提交 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    public void submitAction(){
        boolean flag = false;
        if(null == this.editCheckRst){
            JsfUtil.addErrorMessage("请选择审核结果！");
            flag = true;
        }
        if(null != this.editCheckRst && 2 == this.editCheckRst && StringUtils.isBlank(this.editCheckAudit)){
            JsfUtil.addErrorMessage("审核意见不允许为空！");
            flag = true;
        }
        if(flag || null == this.infoViewBean.getZwOrginfoEntity()){
            return;
        }
        this.infoViewBean.getZwOrginfoEntity().setState((short)(1 == this.editCheckRst ? 1 : 3));
        this.infoViewBean.getZwOrginfoEntity().setCheckRst(this.editCheckRst);
        this.infoViewBean.getZwOrginfoEntity().setAuditAdv(this.editCheckAudit);
        this.infoViewBean.getZwOrginfoEntity().setFkByChkPsnId(Global.getUser());
        try{
            this.orgZzCheckService.auditOrCancelOrgInfo(this.zzType, this.infoViewBean.getZwOrginfoEntity());
            JsfUtil.addSuccessMessage("审核成功！");
            this.modAndViewComm(1);
            this.searchAction();
            RequestContext.getCurrentInstance().update("tabView");
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("审核失败！");
        }
    }

    /**
     * <p>方法描述： 撤销 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    public void cancelAction(){
        if(null == this.infoViewBean.getZwOrginfoEntity()){
            return;
        }
        this.infoViewBean.getZwOrginfoEntity().setState((short)2);
        this.infoViewBean.getZwOrginfoEntity().setFkByChkPsnId(null);
        try{
            this.orgZzCheckService.auditOrCancelOrgInfo(this.zzType, this.infoViewBean.getZwOrginfoEntity());
            JsfUtil.addSuccessMessage("撤销成功！");
            this.modAndViewComm(0);
            this.searchAction();
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
     * <p>方法描述： 审核页 切换审核状态 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    public void changeCheckState(){
        if(StringUtils.isNotBlank(this.defaultAuditAdv)){
            if(null != this.editCheckRst && 1 == this.editCheckRst){
                if(StringUtils.isBlank(this.editCheckAudit)){
                    this.editCheckAudit = this.defaultAuditAdv;
                }
            }else if(StringUtils.isNotBlank(this.editCheckAudit) &&
                    this.defaultAuditAdv.trim().equals(this.editCheckAudit.trim())){
                this.editCheckAudit = null;
            }
        }
    }

    /**
     * <p>方法描述： 修改 更多与收起</p>
     * @MethodAuthor： pw 2022/11/2
     **/
    public void changeShowState(int type){
        if(0 == type){
            if(1 == this.psnSummaryState){
                this.fillShowPersonSummaryList(true);
                this.psnSummaryState = 2;
            }else{
                this.fillShowPersonSummaryList(false);
                this.psnSummaryState = 1;
            }
        }else if(1 == type){
            if(1 == this.instSummaryState){
                this.fillShowInstSummaryList(true);
                this.instSummaryState = 2;
            }else{
                this.fillShowInstSummaryList(false);
                this.instSummaryState = 1;
            }
        }
    }

    /**
     * <p>方法描述： 执行获取机构信息 </p>
     * @MethodAuthor： pw 2022/10/29
     **/
    private void executeInitOrgInfo(){
        this.psnSummaryState = 0;
        if(null == this.rid || null == this.zzType){
            this.infoViewBean = new OrgInfoViewBean();
            return;
        }

        //不需要原生的人员汇总
        this.infoViewBean.setIfPersonSummary("0");
        //基本信息 联系电话不需要加密
        this.infoViewBean.setIfBaseInfoEncry(Boolean.FALSE);
        this.infoViewBean.fillParams(this.zzType, this.rid);
        this.infoViewBean.executeQuery();
        if(null == this.infoViewBean.getZwOrginfoEntity() || null == this.infoViewBean.getZwOrginfoEntity().getRid()){
            return;
        }
        Map<Integer,List<Object[]>> warnItemMap = this.orgZzCheckService
                .findWarnTypeIdByOrgTypeAndRidGroupByWarnType(this.zzType, this.rid);
        this.initPersonSummary(warnItemMap.get(1));
        this.initInstSummary(warnItemMap.get(2));
        this.initUnHaveItems(warnItemMap.get(3));
        this.initUnHaveQualAnnex(warnItemMap.get(4));
    }
    /**
     * <p>方法描述： 未具备的制度初始化 </p>
     * @MethodAuthor： pw 2022/11/2
     **/
    private void initUnHaveQualAnnex(List<Object[]> warnSimpleCodeList){
        this.unHaveQualAnnex = null;
        if(!this.ifHaveQualAnnexWarn){
            return;
        }
        if(CollectionUtils.isEmpty(warnSimpleCodeList)){
            this.unHaveQualAnnex = "无";
            return;
        }
        StringBuffer buffer = new StringBuffer();
        for(Object[] objArr : warnSimpleCodeList){
            String tip = StringUtils.objectToString(objArr[2]);
            if(StringUtils.isNotBlank(tip)){
                buffer.append("，").append(tip);
            }
        }
        this.unHaveQualAnnex = buffer.length() > 0 ? buffer.substring(1) : "";
    }

    /**
     * <p>方法描述： 未具备的必检项目初始化 </p>
     * @MethodAuthor： pw 2022/11/2
     **/
    private void initUnHaveItems(List<Object[]> warnSimpleCodeList){
        this.unHaveItems = null;
        if(!this.ifHaveItemWarn){
            return;
        }
        if(CollectionUtils.isEmpty(warnSimpleCodeList)){
            this.unHaveItems = "无";
            return;
        }
        StringBuffer buffer = new StringBuffer();
        for(Object[] objArr : warnSimpleCodeList){
            String tip = StringUtils.objectToString(objArr[2]);
            if(StringUtils.isNotBlank(tip)){
                buffer.append("，").append(tip);
            }
        }
        this.unHaveItems = buffer.length() > 0 ? buffer.substring(1) : "";
    }

    /**
     * <p>方法描述： 仪器类型汇总初始化 </p>
     * @MethodAuthor： pw 2022/11/2
     **/
    private void initInstSummary(List<Object[]> warnSimpleCodeList){
        this.instSummaryList = new ArrayList<>();
        this.showInstSummaryList = new ArrayList<>();
        //查询结果
        List<Object[]> resultList = this.zwIntellReportServiceImpl.findInstTypeSummaryOrderByCountNum(this.zzType, this.rid, Boolean.TRUE);
        List<Object[]> warnList = new ArrayList<>();
        List<Object[]> normalList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(warnSimpleCodeList)){
            for(Object[] objArr : warnSimpleCodeList){
                String tip = StringUtils.objectToString(objArr[2]);
                if(StringUtils.isNotBlank(tip)){
                    Object[] arr = new Object[2];
                    arr[0]=tip+"：0台";
                    arr[1] = 1;
                    warnList.add(arr);
                }
            }
        }
        if(!CollectionUtils.isEmpty(resultList)){
            for(Object[] objArr : resultList){
                String tip = StringUtils.objectToString(objArr[1]);
                Integer count = null == objArr[2] ? 0 : Integer.parseInt(objArr[2].toString());
                if(StringUtils.isNotBlank(tip)){
                    Object[] arr = new Object[2];
                    arr[0]=tip+"："+count+"台";
                    arr[1] = 0;
                    normalList.add(arr);
                }
            }
        }
        if(!CollectionUtils.isEmpty(normalList)){
            warnList.addAll(normalList);
        }
        List<List<Object[]>> tmpList = StringUtils.splitListProxy(warnList, 7);
        if(!CollectionUtils.isEmpty(tmpList)){
            this.instSummaryList.addAll(tmpList);
        }
        if(this.instSummaryList.size() > 3){
            this.instSummaryState = 1;
            this.fillShowInstSummaryList(false);
        }else{
            this.showInstSummaryList.addAll(this.instSummaryList);
            this.instSummaryState = 0;
        }

    }

    /**
     * <p>方法描述： 人员汇总初始化</p>
     * @MethodAuthor： pw 2022/11/2
     **/
    private void initPersonSummary(List<Object[]> warnSimpleCodeList){
        this.personSummaryList = new ArrayList<>();
        this.showPersonSummaryList = new ArrayList<>();
        //查询的结果 已经包含当前资质的全码表结果
        List<Object[]> summaryList = this.zwIntellReportServiceImpl.findPsnTypeSummaryOrderByCountNum(this.zzType, this.rid, Boolean.TRUE);
        List<Object[]> warnList = new ArrayList<>();
        List<Object[]> normalList = new ArrayList<>();
        List<Integer> containList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(warnSimpleCodeList)){
            for (Object[] objArr : warnSimpleCodeList){
                containList.add(Integer.parseInt(objArr[1].toString()));
            }
        }
        for(Object[] objArr : summaryList){
            if(objArr.length < 3 || null == objArr[0] || null == objArr[1] || null == objArr[2]){
                continue;
            }
            Integer simpleRid = Integer.parseInt(objArr[0].toString());
            String codeName = objArr[1].toString();
            Integer count = Integer.parseInt(objArr[2].toString());
            Object[] arr = new Object[2];
            arr[0]=codeName+"："+count+"人";
            if(!CollectionUtils.isEmpty(containList) && containList.contains(simpleRid)){
                arr[1] = 1;
                warnList.add(arr);
            }else{
                arr[1] = 0;
                normalList.add(arr);
            }
        }
        if(!CollectionUtils.isEmpty(normalList)){
            warnList.addAll(normalList);
        }
        List<List<Object[]>> tmpList = StringUtils.splitListProxy(warnList, 7);
        if(!CollectionUtils.isEmpty(tmpList)){
            this.personSummaryList.addAll(tmpList);
        }
        if(this.personSummaryList.size() > 3){
            this.psnSummaryState = 1;
            this.fillShowPersonSummaryList(false);
        }else{
            this.showPersonSummaryList.addAll(this.personSummaryList);
            this.psnSummaryState = 0;
        }
    }

    /**
     * <p>方法描述： 填充显示的人员汇总 </p>
     * @MethodAuthor： pw 2022/11/2
     **/
    private void fillShowPersonSummaryList(boolean ifAddAll){
        this.showPersonSummaryList.clear();
        if(ifAddAll || (!CollectionUtils.isEmpty(this.personSummaryList) && this.personSummaryList.size() <= 3)){
            this.showPersonSummaryList.addAll(this.personSummaryList);
        }else{
            for(int i=0;i<3;i++){
                this.showPersonSummaryList.add(this.personSummaryList.get(i));
            }
        }
    }

    /**
     * <p>方法描述： 填充显示的仪器汇总 </p>
     * @MethodAuthor： pw 2022/11/2
     **/
    private void fillShowInstSummaryList(boolean ifAddAll){
        this.showInstSummaryList.clear();
        if(ifAddAll || (!CollectionUtils.isEmpty(this.instSummaryList) && this.instSummaryList.size() <= 3)){
            this.showInstSummaryList.addAll(this.instSummaryList);
        }else{
            for(int i=0;i<3;i++){
                this.showInstSummaryList.add(this.instSummaryList.get(i));
            }
        }
    }

    /**
     * <p>方法描述： </p>
     * @MethodAuthor： pw 2022/11/2
     **/
    private void initHaveWarn(){
        Map<Integer,Integer> map = this.orgZzCheckService.findWarnConfigGroupByWarnType();
        Integer count = map.get(3);
        this.ifHaveItemWarn = null != count && count > 0;
        count = map.get(4);
        this.ifHaveQualAnnexWarn = null != count && count > 0;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public Integer getZzType() {
        return zzType;
    }

    public void setZzType(Integer zzType) {
        this.zzType = zzType;
    }

    public List<TsZone> getOrgZoneList() {
        return orgZoneList;
    }

    public void setOrgZoneList(List<TsZone> orgZoneList) {
        this.orgZoneList = orgZoneList;
    }

    public String getSearchOrgZoneCode() {
        return searchOrgZoneCode;
    }

    public void setSearchOrgZoneCode(String searchOrgZoneCode) {
        this.searchOrgZoneCode = searchOrgZoneCode;
    }

    public String getSearchOrgZoneName() {
        return searchOrgZoneName;
    }

    public void setSearchOrgZoneName(String searchOrgZoneName) {
        this.searchOrgZoneName = searchOrgZoneName;
    }

    public String getSearchOrgName() {
        return searchOrgName;
    }

    public void setSearchOrgName(String searchOrgName) {
        this.searchOrgName = searchOrgName;
    }

    public String getSearchZzType() {
        return searchZzType;
    }

    public void setSearchZzType(String searchZzType) {
        this.searchZzType = searchZzType;
    }

    public List<SelectItem> getZzTypeList() {
        return zzTypeList;
    }

    public void setZzTypeList(List<SelectItem> zzTypeList) {
        this.zzTypeList = zzTypeList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String getDefaultAuditAdv() {
        return defaultAuditAdv;
    }

    public void setDefaultAuditAdv(String defaultAuditAdv) {
        this.defaultAuditAdv = defaultAuditAdv;
    }

    public OrgInfoViewBean getInfoViewBean() {
        return infoViewBean;
    }

    public void setInfoViewBean(OrgInfoViewBean infoViewBean) {
        this.infoViewBean = infoViewBean;
    }

    public Integer getEditCheckRst() {
        return editCheckRst;
    }

    public void setEditCheckRst(Integer editCheckRst) {
        this.editCheckRst = editCheckRst;
    }

    public String getEditCheckAudit() {
        return editCheckAudit;
    }

    public void setEditCheckAudit(String editCheckAudit) {
        this.editCheckAudit = editCheckAudit;
    }

    public List<List<Object[]>> getPersonSummaryList() {
        return personSummaryList;
    }

    public void setPersonSummaryList(List<List<Object[]>> personSummaryList) {
        this.personSummaryList = personSummaryList;
    }

    public Integer getPsnSummaryState() {
        return psnSummaryState;
    }

    public void setPsnSummaryState(Integer psnSummaryState) {
        this.psnSummaryState = psnSummaryState;
    }

    public List<List<Object[]>> getShowPersonSummaryList() {
        return showPersonSummaryList;
    }

    public void setShowPersonSummaryList(List<List<Object[]>> showPersonSummaryList) {
        this.showPersonSummaryList = showPersonSummaryList;
    }

    public List<List<Object[]>> getInstSummaryList() {
        return instSummaryList;
    }

    public void setInstSummaryList(List<List<Object[]>> instSummaryList) {
        this.instSummaryList = instSummaryList;
    }

    public List<List<Object[]>> getShowInstSummaryList() {
        return showInstSummaryList;
    }

    public void setShowInstSummaryList(List<List<Object[]>> showInstSummaryList) {
        this.showInstSummaryList = showInstSummaryList;
    }

    public Integer getInstSummaryState() {
        return instSummaryState;
    }

    public void setInstSummaryState(Integer instSummaryState) {
        this.instSummaryState = instSummaryState;
    }

    public String getUnHaveItems() {
        return unHaveItems;
    }

    public void setUnHaveItems(String unHaveItems) {
        this.unHaveItems = unHaveItems;
    }

    public String getUnHaveQualAnnex() {
        return unHaveQualAnnex;
    }

    public void setUnHaveQualAnnex(String unHaveQualAnnex) {
        this.unHaveQualAnnex = unHaveQualAnnex;
    }

    public Boolean getIfHaveItemWarn() {
        return ifHaveItemWarn;
    }

    public void setIfHaveItemWarn(Boolean ifHaveItemWarn) {
        this.ifHaveItemWarn = ifHaveItemWarn;
    }

    public Boolean getIfHaveQualAnnexWarn() {
        return ifHaveQualAnnexWarn;
    }

    public void setIfHaveQualAnnexWarn(Boolean ifHaveQualAnnexWarn) {
        this.ifHaveQualAnnexWarn = ifHaveQualAnnexWarn;
    }

}
