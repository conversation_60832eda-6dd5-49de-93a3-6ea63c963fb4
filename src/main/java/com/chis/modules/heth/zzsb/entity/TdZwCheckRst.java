package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_CHECK_RST")
@SequenceGenerator(name = "TdZwCheckRstSeq", sequenceName = "TD_ZW_CHECK_RST_SEQ", allocationSize = 1)
public class TdZwCheckRst implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TsSimpleCode fkCheckTypeId;
    private TsUnit fkUnitId;
    private String expertLeaders;
    private String expertMembers;
    private String problems;
    private TsSimpleCode fkCheckRstId;
    private Date checkDate;
    private TsSimpleCode fkZgRstId;
    private String noticeFilePath;
    private String zgFilePath;
    private TsUnit fkCheckUnitId;
    private Integer state;
    private Integer delMark;
    private Integer createManid;
    private Date createDate;
    private Date modifyDate;
    private Integer modifyManid;

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwCheckRstSeq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "CHECK_TYPE_ID")
    public TsSimpleCode getFkCheckTypeId() {
        return fkCheckTypeId;
    }

    public void setFkCheckTypeId(TsSimpleCode fkCheckTypeId) {
        this.fkCheckTypeId = fkCheckTypeId;
    }

    @ManyToOne
    @JoinColumn(name = "UNIT_ID")
    public TsUnit getFkUnitId() {
        return fkUnitId;
    }

    public void setFkUnitId(TsUnit fkUnitId) {
        this.fkUnitId = fkUnitId;
    }

    @Column(name = "EXPERT_LEADERS")
    public String getExpertLeaders() {
        return expertLeaders;
    }

    public void setExpertLeaders(String expertLeaders) {
        this.expertLeaders = expertLeaders;
    }

    @Column(name = "EXPERT_MEMBERS")
    public String getExpertMembers() {
        return expertMembers;
    }

    public void setExpertMembers(String expertMembers) {
        this.expertMembers = expertMembers;
    }

    @Column(name = "PROBLEMS")
    public String getProblems() {
        return problems;
    }

    public void setProblems(String problems) {
        this.problems = problems;
    }

    @ManyToOne
    @JoinColumn(name = "CHECK_RST_ID")
    public TsSimpleCode getFkCheckRstId() {
        return fkCheckRstId;
    }

    public void setFkCheckRstId(TsSimpleCode fkCheckRstId) {
        this.fkCheckRstId = fkCheckRstId;
    }

    @Column(name = "CHECK_DATE")
    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    @ManyToOne
    @JoinColumn(name = "ZG_RST_ID")
    public TsSimpleCode getFkZgRstId() {
        return fkZgRstId;
    }

    public void setFkZgRstId(TsSimpleCode fkZgRstId) {
        this.fkZgRstId = fkZgRstId;
    }

    @Column(name = "NOTICE_FILE_PATH")
    public String getNoticeFilePath() {
        return noticeFilePath;
    }

    public void setNoticeFilePath(String noticeFilePath) {
        this.noticeFilePath = noticeFilePath;
    }

    @Column(name = "ZG_FILE_PATH")
    public String getZgFilePath() {
        return zgFilePath;
    }

    public void setZgFilePath(String zgFilePath) {
        this.zgFilePath = zgFilePath;
    }

    @ManyToOne
    @JoinColumn(name = "CHECK_UNIT_ID")
    public TsUnit getFkCheckUnitId() {
        return fkCheckUnitId;
    }

    public void setFkCheckUnitId(TsUnit fkCheckUnitId) {
        this.fkCheckUnitId = fkCheckUnitId;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Column(name = "DEL_MARK")
    public Integer getDelMark() {
        return delMark;
    }

    public void setDelMark(Integer delMark) {
        this.delMark = delMark;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
}
