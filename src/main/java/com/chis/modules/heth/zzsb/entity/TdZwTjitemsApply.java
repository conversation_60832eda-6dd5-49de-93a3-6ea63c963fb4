package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-14
 */
@Entity
@Table(name = "TD_ZW_TJITEMS_APPLY")
@SequenceGenerator(name = "TdZwTjitemsApply", sequenceName = "TD_ZW_TJITEMS_APPLY_SEQ", allocationSize = 1)
public class TdZwTjitemsApply implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorginfoApply fkByOrgId;
	private TsSimpleCode fkByItemId;
	private Integer isOutter;
	private Date createDate;
	private Integer createManid;
	
	public TdZwTjitemsApply() {
	}

	public TdZwTjitemsApply(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjitemsApply")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwTjorginfoApply getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwTjorginfoApply fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TsSimpleCode getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TsSimpleCode fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Column(name = "IS_OUTTER")	
	public Integer getIsOutter() {
		return isOutter;
	}

	public void setIsOutter(Integer isOutter) {
		this.isOutter = isOutter;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}