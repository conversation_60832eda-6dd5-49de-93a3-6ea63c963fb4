package com.chis.modules.heth.zzsb.entity;

import java.util.Date;

/**
 * 仪器
 * Created by wlj on 2015-04-20.
 */
public interface IZwInstZzsb {

    public Integer getRid();
    public void setRid(Integer rid);

    public IZwOrginfoZzsb getZwOrginfo();
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo);

    public TdZwInstinfoZzsb getTdZwInstinfo();
    public void setTdZwInstinfo(TdZwInstinfoZzsb tdZwInstinfo);

    public Date getCreateDate();
    public void setCreateDate(Date createDate);

    public Integer getCreateManid();
    public void setCreateManid(Integer createManid);
}
