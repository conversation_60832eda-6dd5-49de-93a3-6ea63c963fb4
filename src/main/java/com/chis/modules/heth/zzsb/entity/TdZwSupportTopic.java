package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-3-29
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_TOPIC")
@SequenceGenerator(name = "TdZwSupportTopic", sequenceName = "TD_ZW_SUPPORT_TOPIC_SEQ", allocationSize = 1)
public class TdZwSupportTopic implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSupportOrgZzsb fkByMainId;
	private Integer topicType;
	private Integer year;
	private String topicName;
	private String prizeName;
	private String awardDepartment;
	private TsSimpleCode fkByPrizeLevelId;
	private String supportFilePath;
	private Date createDate;
	private Integer createManid;
	private Integer modifyManid;
	private Date modifyDate;
	private SupportResearchPsnSelVO psnSelVO;
	private List<TdZwSupportTopicPsns> supportTopicPsns;
	private String responsible;

	public TdZwSupportTopic() {
	}

	public TdZwSupportTopic(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportTopic")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSupportOrgZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "TOPIC_TYPE")	
	public Integer getTopicType() {
		return topicType;
	}

	public void setTopicType(Integer topicType) {
		this.topicType = topicType;
	}	
			
	@Column(name = "YEAR")	
	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}	
			
	@Column(name = "TOPIC_NAME")	
	public String getTopicName() {
		return topicName;
	}

	public void setTopicName(String topicName) {
		this.topicName = topicName;
	}	
			
	@Column(name = "PRIZE_NAME")	
	public String getPrizeName() {
		return prizeName;
	}

	public void setPrizeName(String prizeName) {
		this.prizeName = prizeName;
	}	
			
	@Column(name = "AWARD_DEPARTMENT")	
	public String getAwardDepartment() {
		return awardDepartment;
	}

	public void setAwardDepartment(String awardDepartment) {
		this.awardDepartment = awardDepartment;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PRIZE_LEVEL_ID")			
	public TsSimpleCode getFkByPrizeLevelId() {
		return fkByPrizeLevelId;
	}

	public void setFkByPrizeLevelId(TsSimpleCode fkByPrizeLevelId) {
		this.fkByPrizeLevelId = fkByPrizeLevelId;
	}	
			
	@Column(name = "SUPPORT_FILE_PATH")	
	public String getSupportFilePath() {
		return supportFilePath;
	}

	public void setSupportFilePath(String supportFilePath) {
		this.supportFilePath = supportFilePath;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}
	@Transient
	public SupportResearchPsnSelVO getPsnSelVO() {
		return psnSelVO;
	}

	public void setPsnSelVO(SupportResearchPsnSelVO psnSelVO) {
		this.psnSelVO = psnSelVO;
	}
	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
	public List<TdZwSupportTopicPsns> getSupportTopicPsns() {
		return supportTopicPsns;
	}

	public void setSupportTopicPsns(List<TdZwSupportTopicPsns> supportTopicPsns) {
		this.supportTopicPsns = supportTopicPsns;
	}
	@Transient
	public String getResponsible() {
		return responsible;
	}

	public void  setResponsible(String responsible) {
		this.responsible = responsible;
	}
}