package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_WORKDEPT")
@SequenceGenerator(name = "TdZwWorkdeptSeq", sequenceName = "TD_ZW_WORKDEPT_SEQ", allocationSize = 1)
public class TdZwWorkdeptZzsb implements java.io.Serializable {
	private static final long serialVersionUID = -6959563765599733099L;

	private Integer rid;
	private TsSimpleCode tsSimpleCode;
	private TdZwTjorgpsnsZzsb tdZwTjorgpsns;
	private Date createDate;
	private Integer createManid;

	public TdZwWorkdeptZzsb() {
	}

	public TdZwWorkdeptZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwWorkdeptSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "DEPT_ID" )
	public TsSimpleCode getTsSimpleCode() {
		return this.tsSimpleCode;
	}

	public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
		this.tsSimpleCode = tsSimpleCode;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ID" )
	public TdZwTjorgpsnsZzsb getTdZwTjorgpsns() {
		return this.tdZwTjorgpsns;
	}

	public void setTdZwTjorgpsns(TdZwTjorgpsnsZzsb tdZwTjorgpsns) {
		this.tdZwTjorgpsns = tdZwTjorgpsns;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

}