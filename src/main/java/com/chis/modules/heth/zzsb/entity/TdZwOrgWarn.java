package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-10-24
 */
@Entity
@Table(name = "TD_ZW_ORG_WARN")
@SequenceGenerator(name = "TdZwOrgWarn", sequenceName = "TD_ZW_ORG_WARN_SEQ", allocationSize = 1)
public class TdZwOrgWarn implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer busType;
	private Integer busId;
	private TbZwOrgWarnConfig fkByWarnId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwOrgWarn() {
	}

	public TdZwOrgWarn(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOrgWarn")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BUS_TYPE")	
	public Integer getBusType() {
		return busType;
	}

	public void setBusType(Integer busType) {
		this.busType = busType;
	}	
			
	@Column(name = "BUS_ID")	
	public Integer getBusId() {
		return busId;
	}

	public void setBusId(Integer busId) {
		this.busId = busId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "WARN_ID")			
	public TbZwOrgWarnConfig getFkByWarnId() {
		return fkByWarnId;
	}

	public void setFkByWarnId(TbZwOrgWarnConfig fkByWarnId) {
		this.fkByWarnId = fkByWarnId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}