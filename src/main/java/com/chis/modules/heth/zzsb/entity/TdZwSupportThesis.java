package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;

import com.chis.modules.system.entity.TsSimpleCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-3-29
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_THESIS")
@SequenceGenerator(name = "TdZwSupportThesis", sequenceName = "TD_ZW_SUPPORT_THESIS_SEQ", allocationSize = 1)
public class TdZwSupportThesis implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwSupportOrgZzsb fkByMainId;
    private Integer year;
    private String thesisName;
    private TsSimpleCode fkByPublicationId;
    private String publicationName;
    private BigDecimal impactFactors;
    private String supportFilePath;
    private Date createDate;
    private Integer createManid;
    private Integer modifyManid;
    private Date modifyDate;
    private String author;
    private SupportResearchPsnSelVO psnSelVO;
    private List<TdZwSupportThesisPsns> thesisPsnList;

    public TdZwSupportThesis() {
    }

    public TdZwSupportThesis(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportThesis")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwSupportOrgZzsb getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @Column(name = "YEAR")
    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    @Column(name = "THESIS_NAME")
    public String getThesisName() {
        return thesisName;
    }

    public void setThesisName(String thesisName) {
        this.thesisName = thesisName;
    }

    @ManyToOne
    @JoinColumn(name = "PUBLICATION_ID")
    public TsSimpleCode getFkByPublicationId() {
        return fkByPublicationId;
    }

    public void setFkByPublicationId(TsSimpleCode fkByPublicationId) {
        this.fkByPublicationId = fkByPublicationId;
    }

    @Column(name = "PUBLICATION_NAME")
    public String getPublicationName() {
        return publicationName;
    }

    public void setPublicationName(String publicationName) {
        this.publicationName = publicationName;
    }

    @Column(name = "IMPACT_FACTORS")
    public BigDecimal getImpactFactors() {
        return impactFactors;
    }

    public void setImpactFactors(BigDecimal impactFactors) {
        this.impactFactors = impactFactors;
    }

    @Column(name = "SUPPORT_FILE_PATH")
    public String getSupportFilePath() {
        return supportFilePath;
    }

    public void setSupportFilePath(String supportFilePath) {
        this.supportFilePath = supportFilePath;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Transient
    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    @Transient
    public SupportResearchPsnSelVO getPsnSelVO() {
        return psnSelVO;
    }

    public void setPsnSelVO(SupportResearchPsnSelVO psnSelVO) {
        this.psnSelVO = psnSelVO;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwSupportThesisPsns> getThesisPsnList() {
        return thesisPsnList;
    }

    public void setThesisPsnList(List<TdZwSupportThesisPsns> thesisPsnList) {
        this.thesisPsnList = thesisPsnList;
    }
}