package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-14
 */
@Entity
@Table(name = "TD_ZW_TJORGPSNS_APPLY")
@SequenceGenerator(name = "TdZwTjorgpsnsApply", sequenceName = "TD_ZW_TJORGPSNS_APPLY_SEQ", allocationSize = 1)
public class TdZwTjorgpsnsApply implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorginfoApply fkByOrgId;
	private TdZwPsninfoZzsb fkByEmpId;
	/**
	 * 从事科室
	 */
	private String wordOfficeRids;
	private String wordOfficeNames;

	public TdZwTjorgpsnsApply() {
	}

	public TdZwTjorgpsnsApply(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgpsnsApply")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwTjorginfoApply getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwTjorginfoApply fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EMP_ID")			
	public TdZwPsninfoZzsb getFkByEmpId() {
		return fkByEmpId;
	}

	public void setFkByEmpId(TdZwPsninfoZzsb fkByEmpId) {
		this.fkByEmpId = fkByEmpId;
	}

	@Transient
	public String getWordOfficeRids() {
		return wordOfficeRids;
	}

	public void setWordOfficeRids(String wordOfficeRids) {
		this.wordOfficeRids = wordOfficeRids;
	}

	@Transient
	public String getWordOfficeNames() {
		return wordOfficeNames;
	}

	public void setWordOfficeNames(String wordOfficeNames) {
		this.wordOfficeNames = wordOfficeNames;
	}
}