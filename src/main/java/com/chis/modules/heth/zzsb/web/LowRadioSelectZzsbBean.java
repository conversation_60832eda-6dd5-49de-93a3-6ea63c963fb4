package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.TbTjLawCheckZzsb;
import com.chis.modules.heth.zzsb.service.ZzsbZwIntellReportServiceImpl;
import com.chis.modules.system.web.FacesBean;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <p>
 * 类描述：检测依据弹窗
 * </p>
 *
 * @ClassAuthor yph, 2021年05月27日, lowRadioSelectZzsbBean
 */
@ManagedBean(name = "lowRadioSelectZzsbBean")
@ViewScoped
public class LowRadioSelectZzsbBean extends FacesBean {

    private static final long serialVersionUID = -738445454536625063L;
    private ZzsbZwIntellReportServiceImpl zzsbZwIntellReportService = SpringContextHolder.getBean(ZzsbZwIntellReportServiceImpl.class);

    /**
     * 检测依据名称
     */
    private String searchNamOrPy;
    /**
     * 标题
     */
    private String titleName;
    /**
     * 选择的对象
     */
    private TbTjLawCheckZzsb selectPro;
    /**
     * 传入ID
     */
    private String typeNo;
    /**
     * 查询列集合
     */
    private List<TbTjLawCheckZzsb> displayList;

    /**
     * 所有集合
     */
    private List<TbTjLawCheckZzsb> allList;

    /**
     * <p>方法描述：初始化数据</p>
     *
     * @MethodAuthor yph, 2018年4月9日, CodeRadioSelectBean
     */
    public LowRadioSelectZzsbBean() {
        this.titleName = JsfUtil.getRequest().getParameter("titleName");
        String typeNo = JsfUtil.getRequest().getParameter("typeNo");
        this.typeNo = typeNo;
        this.init();
    }

    /**
     * <p>方法描述：查询码表数据</p>
     *
     * @MethodAuthor yph, 2018年4月9日, CodeRadioSelectBean
     */
    private void init() {
        this.displayList = new ArrayList<TbTjLawCheckZzsb>();
        this.allList = new ArrayList<TbTjLawCheckZzsb>();
        //typeNo 传递过来的--查询法律法规
        List<Object[]> lowList = zzsbZwIntellReportService.findLowBadRsnList(typeNo);
        if (null != lowList && lowList.size() > 0) {
            for (Object[] obj : lowList) {
                TbTjLawCheckZzsb code = new TbTjLawCheckZzsb();
                code.setRid(null != obj[0] ? Integer.valueOf(obj[0].toString()) : null);
                code.setLawCode(null != obj[1] ? obj[1].toString() : null);
                code.setLawName(null != obj[2] ? obj[2].toString() : null);
                code.setDecreeDate(null != obj[3] ? (Date) obj[3] : null);
                allList.add(code);
            }
        }

        if (null != allList && allList.size() > 0) {
            this.displayList.addAll(allList);
        }

    }

    /**
     * <p>方法描述：根据名称、拼音码过滤数据</p>
     *
     * @MethodAuthor yph, 2018年4月9日, searchAction
     */
    public void searchAction() {
        //初始化展示页面的疫苗数据集
        this.displayList = new ArrayList<TbTjLawCheckZzsb>();
        if (null != allList && allList.size() > 0) {
            if (StringUtils.isNotBlank(searchNamOrPy)) {
                for (TbTjLawCheckZzsb t : allList) {
                    //名称
                    String codeName = t.getLawName() == null ? "" : t.getLawName();
                    if (codeName.indexOf(searchNamOrPy) != -1) {
                        this.displayList.add(t);
                    }
                }
            } else {
                this.displayList.addAll(allList);
            }
        }

    }

    /**
     * <p>方法描述：选择确定方法</p>
     *
     * @MethodAuthor yph, 2018年4月9日, selectAction
     */
    public void selectAction() {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selectPro", selectPro);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * <p>方法描述：关闭</p>
     *
     * @MethodAuthor yph, 2018年4月9日, dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }


    public String getSearchNamOrPy() {
        return searchNamOrPy;
    }

    public void setSearchNamOrPy(String searchNamOrPy) {
        this.searchNamOrPy = searchNamOrPy;
    }


    public TbTjLawCheckZzsb getSelectPro() {
        return selectPro;
    }

    public void setSelectPro(TbTjLawCheckZzsb selectPro) {
        this.selectPro = selectPro;
    }

    public List<TbTjLawCheckZzsb> getDisplayList() {
        return displayList;
    }

    public void setDisplayList(List<TbTjLawCheckZzsb> displayList) {
        this.displayList = displayList;
    }

    public List<TbTjLawCheckZzsb> getAllList() {
        return allList;
    }

    public void setAllList(List<TbTjLawCheckZzsb> allList) {
        this.allList = allList;
    }

    public String getTypeNo() {
        return typeNo;
    }

    public void setTypeNo(String typeNo) {
        this.typeNo = typeNo;
    }
}
