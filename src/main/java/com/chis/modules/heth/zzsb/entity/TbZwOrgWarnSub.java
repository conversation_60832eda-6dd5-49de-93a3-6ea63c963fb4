package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-10-24
 */
@Entity
@Table(name = "TB_ZW_ORG_WARN_SUB")
@SequenceGenerator(name = "TbZwOrgWarnSub", sequenceName = "TB_ZW_ORG_WARN_SUB_SEQ", allocationSize = 1)
public class TbZwOrgWarnSub implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TbZwOrgWarnConfig fkByMainId;
	private TsSimpleCode fkByWarnTypeId;
	private Integer xh;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbZwOrgWarnSub() {
	}

	public TbZwOrgWarnSub(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwOrgWarnSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TbZwOrgWarnConfig getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TbZwOrgWarnConfig fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "WARN_TYPE_ID")			
	public TsSimpleCode getFkByWarnTypeId() {
		return fkByWarnTypeId;
	}

	public void setFkByWarnTypeId(TsSimpleCode fkByWarnTypeId) {
		this.fkByWarnTypeId = fkByWarnTypeId;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}