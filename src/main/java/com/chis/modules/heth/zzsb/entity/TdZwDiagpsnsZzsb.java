package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_DIAGPSNS")
@SequenceGenerator(name = "TdZwDiagpsnsSeq", sequenceName = "TD_ZW_DIAGPSNS_SEQ", allocationSize = 1)
public class TdZwDiagpsnsZzsb implements Serializable,IZwOrgPsnsZzsb {
	private static final long serialVersionUID = 2560229095971592536L;

	private Integer rid;
	private TdZwDiagorginfoZzsb tdZwDiagorginfo;
	private TdZwPsninfoZzsb tdZwPsninfo;
	private String certNo;
	private String wordOfficeName;
	private String wordItems;
	private String onDuty;

	public TdZwDiagpsnsZzsb() {
	}

	public TdZwDiagpsnsZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwDiagpsnsSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwDiagorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwDiagorginfo = (TdZwDiagorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwDiagorginfoZzsb getTdZwDiagorginfo() {
		return this.tdZwDiagorginfo;
	}

	public void setTdZwDiagorginfo(TdZwDiagorginfoZzsb tdZwDiagorginfo) {
		this.tdZwDiagorginfo = tdZwDiagorginfo;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ID" )
	public TdZwPsninfoZzsb getTdZwPsninfo() {
		return this.tdZwPsninfo;
	}

	public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
		this.tdZwPsninfo = tdZwPsninfo;
	}

	@Column(name = "CERT_NO", length = 50)
	public String getCertNo() {
		return this.certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	@Transient
	@Override
	public List<TdZwWorkdeptZzsb> getTdZwWorkdepts() {
		return null;
	}

	@Override
	public void setTdZwWorkdepts(List<TdZwWorkdeptZzsb> tdZwWorkdepts) {

	}

	@Transient
	@Override
	public List<TdZwPsnitemsZzsb> getTdZwPsnitemses() {
		return null;
	}

	@Override
	public void setTdZwPsnitemses(List<TdZwPsnitemsZzsb> tdZwPsnitemses) {

	}

	@Transient
	@Override
	public String getWordOfficeName() {
		return wordOfficeName;
	}

	@Override
	public void setWordOfficeName(String wordOfficeName) {
		this.wordOfficeName = wordOfficeName;
	}

	@Transient
	@Override
	public String getWordItems() {
		return wordItems;
	}

	@Override
	public void setWordItems(String wordItems) {
		this.wordItems = wordItems;
	}
	
	@Override
	@Transient
	public String getJlCode() {
		return null;
	}

	@Override
	public void setJlCode(String jlCode) {
	}

	@Override
	@Transient
	public String getLjJl() {
		return null;
	}

	@Override
	public void setLjJl(String ljJl) {
		
	}

	@Transient
	@Override
	public String getWordItemStr() {
		return null;
	}

	@Override
	public void setWordItemStr(String wordItemStr) {
		
	}
	@Column(name="ON_DUTY")
	public String getOnDuty() {
		return onDuty;
	}

	public void setOnDuty(String onDuty) {
		this.onDuty = onDuty;
	}
}