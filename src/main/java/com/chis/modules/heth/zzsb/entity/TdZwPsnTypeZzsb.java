package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by 裔照丹 on 2019/5/16
 */
@Entity
@Table(name = "TD_ZW_PSN_TYPE")
@SequenceGenerator(name = "TdZwPsnTypeSeq", sequenceName = "TD_ZW_PSN_TYPE_SEQ", allocationSize = 1)
public class TdZwPsnTypeZzsb implements java.io.Serializable{
    private static final long serialVersionUID = 7680587031422427709L;

    private Integer rid;
    private TdZwPsninfoZzsb fkByMainId;
    private TsSimpleCode fkByPsnType;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private String otherPsnType;
    private Integer zzjgType;

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwPsnTypeSeq")
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwPsninfoZzsb getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwPsninfoZzsb fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @ManyToOne
    @JoinColumn(name = "PSN_TYPE")
    public TsSimpleCode getFkByPsnType() {
        return fkByPsnType;
    }

    public void setFkByPsnType(TsSimpleCode fkByPsnType) {
        this.fkByPsnType = fkByPsnType;
    }

    @Column(name = "CREATE_DATE" )
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "MODIFY_DATE" )
    @Temporal(TemporalType.TIMESTAMP)
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID" )
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }
    @Column(name = "OTHER_PSN_TYPE" )
	public String getOtherPsnType() {
		return otherPsnType;
	}

	public void setOtherPsnType(String otherPsnType) {
		this.otherPsnType = otherPsnType;
	}

    @Column(name = "ZZJG_TYPE" )
    public Integer getZzjgType() {
        return zzjgType;
    }

    public void setZzjgType(Integer zzjgType) {
        this.zzjgType = zzjgType;
    }
}
