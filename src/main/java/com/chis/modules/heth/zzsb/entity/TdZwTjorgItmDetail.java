package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-6-29
 */
@Entity
@Table(name = "TD_ZW_TJORG_ITM_DETAIL")
@SequenceGenerator(name = "TdZwTjorgItmDetail", sequenceName = "TD_ZW_TJORG_ITM_DETAIL_SEQ", allocationSize = 1)
public class TdZwTjorgItmDetail implements java.io.Serializable,IZwSvritmSubZzsb{
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorggitemsZzsb fkByMainId;
	private TsSimpleCode fkBySubItemId;
	private Integer ifExterInspect;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwTjorgItmDetail() {
	}

	public TdZwTjorgItmDetail(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgItmDetail")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwSvritmZzsb getZwSvritm() {
		return fkByMainId;
	}

	@Override
	public void setZwSvritm(IZwSvritmZzsb zwSvritm) {
		fkByMainId = (TdZwTjorggitemsZzsb) zwSvritm;
	}

	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwTjorggitemsZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwTjorggitemsZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_DETAIL_ID")			
	public TsSimpleCode getFkBySubItemId() {
		return fkBySubItemId;
	}

	public void setFkBySubItemId(TsSimpleCode fkBySubItemId) {
		this.fkBySubItemId = fkBySubItemId;
	}

	@Column(name = "IF_EXTER_INSPECT")
	public Integer getIfExterInspect() {
		return ifExterInspect;
	}

	public void setIfExterInspect(Integer ifExterInspect) {
		this.ifExterInspect = ifExterInspect;
	}

	@Column(name = "CREATE_MANID")
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	



}