package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.PropertyUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.logic.ZwJkItemPO;
import com.chis.modules.heth.zzsb.logic.ZwJkItemRowPO;
import com.chis.modules.heth.zzsb.service.ZzsbZwIntellReportServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.service.CommServiceImpl;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述： 公共类 用于资质机构信息显示
 * 与页面orgInfoView一起使用
 * </p>
 * @ClassAuthor： pw 2022/10/28
 **/
public class OrgInfoViewBean {

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private ZzsbZwIntellReportServiceImpl zwIntellReportServiceImpl = SpringContextHolder
            .getBean(ZzsbZwIntellReportServiceImpl.class);
    private int pageSize = Constants.PAGE_SIZE;
    private String perPageSize = "20,50,100";
    private TdzwPsnInfoZzsbBase psnInfoBase;
    private boolean ifHethFj;
    private String ifZoneRecord;
    private String ifPersonSummary;
    private String ifValidityPeriod;
    private String ifReviewProcess;
    private String ifServiceDetails;
    private String zkCheckRst;
    private boolean ifShowRcdOrg;
    private boolean ifZoneApprove;
    private String outCarVersion;
    private String ifShowCheckOrgAnaly;
    private String ifShowApproveUnit;
    private String ifHaveHouseAnnex;
    private String ifQualAnnex;
    private boolean ifBaseInfoEncry = Boolean.TRUE;
    private boolean ifPsnInfoEncry = Boolean.TRUE;
    /**填报说明*/
    private String fillDesc;

    private List<TsSimpleCode> rcdOrgList;
    private Map<Integer, String> rcdOrgMap;
    /** 资质机构信息 */
    private IZwOrginfoZzsb zwOrginfoEntity;
    private TdZwPsnZzlbZzsb psnZzlbNew;
    /** 批准的执业区域 */
    private String zoneApproveName;
    /** 外出开展职业健康检查工作能力范围地区 */
    private String zoneNames;
    /** 服务项目小类 */
    private List<ZwJkItemPO> jkItemList= new ArrayList<>();
    private List<ZwJkItemRowPO> jkItemRowList= new ArrayList<>();
    /**职业健康服务项目*/
    private List<TsSimpleCode> jkList=new ArrayList<TsSimpleCode>();
    /**放射服务项目*/
    private List<TsSimpleCode> fsList=new ArrayList<TsSimpleCode>();
    /**职业病诊断服务项目*/
    private List<TsSimpleCode> zyList=new ArrayList<TsSimpleCode>();
    /**职业卫生技术服务机构业务范围*/
    private List<TsSimpleCode> zwjsList=new ArrayList<TsSimpleCode>();
    /** 服务项目 */
    private List<String> serviceList;
    /** key5018大类 codeNo value对应的服务项目小类 */
    private Map<String,List<TsSimpleCode>> fatherServerWithChildMap;
    /** 附件 */
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList1 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList2 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList3 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList4 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList5 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList6 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList7 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList8 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList9 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    private List<TdZwJsffjgCommAnnexZzsb> zwAnnexList20 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
    /** 页面展示项目集合 */
    private List<Object[]> showItemList;
    /** 页面展示码表Map */
    Map<TsSimpleCode, List<TsSimpleCode>> diagMap;
    /** 外检车辆列表 */
    private List<TdZwTjorgOutcarZzsb> tjorgOutcarZzsbList;
    /** 备案记录 */
    private List<TdZwTjorgRecordZzsb> tjorgRecords;
    /**人员汇总信息*/
    private List<List<String>> personSummaryList;
    /** 质量管理体系制度集合  码表5507 num排序 需初始化*/
    private List<TsSimpleCode> institutionList;
    /** 质量管理体系制度 顶级制度集合 */
    private List<TsSimpleCode> topOfInstitutionList;
    /** 质量管理体系制度 顶级制度对应Map */
    private Map<Integer, TsSimpleCode> topInstitutionMap;
    /** 质量管理体系制度 顶级制度下的分制度 */
    private Map<Integer, List<TsSimpleCode>> institutionMap;
    /** 质量管理体系制度 各制度对应的List */
    private Map<Integer,List<TdZwTjorgQualAnnexZzsb>> qualAnnexMap;
    /** 质量控制结果 */
    private List<Object[]> zwCheckRstList ;

    /** 资质机构类别 0职业健康检查机构 1放射卫生技术服务机构 2职业病诊断机构 3职业卫生技术服务机构 */
    private Integer orgType;
    /** 资质机构rid */
    private Integer rid;

    private TdZwPsninfoZzsb tdZwPsninfo = new TdZwPsninfoZzsb();

    public OrgInfoViewBean(){
        psnInfoBase = new TdzwPsnInfoZzsbBase();
        psnInfoBase.setValidPsnInfoId(false);
        TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
        psnInfoBase.setPsnInfo(psninfo);
        psnInfoBase.setPsnZzlb(new TdZwPsnZzlbZzsb());
        psnInfoBase.setPsnZzlbNew(new TdZwPsnZzlbZzsb());
        this.ifHethFj = "true".equals(PropertyUtils.getValue("zzsb.ifHethFj"));
        this.ifZoneRecord = PropertyUtils.getValue("zzsb.ifZoneRecord");
        this.ifPersonSummary = PropertyUtils.getValue("zzsb.ifPersonSummary");

        ifValidityPeriod = PropertyUtils.getValueWithoutException("zzsb.ifValidityPeriod");
        ifReviewProcess = PropertyUtils.getValueWithoutException("zzsb.ifReviewProcess");
        ifServiceDetails = PropertyUtils.getValueWithoutException("zzsb.ifServiceDetails");
        this.zkCheckRst = PropertyUtils.getValueWithoutException("zzsb.zkCheckRst");
        //备案管理机构
        this.ifShowRcdOrg = "1".equals(PropertyUtils.getValueWithoutException("zzsb.ifShowRcdOrg"));
        this.rcdOrgList = this.commService.findLevelSimpleCodesByTypeId("5565");
        this.rcdOrgMap = new HashMap<>();
        for (TsSimpleCode simpleCode : this.rcdOrgList) {
            this.rcdOrgMap.put(simpleCode.getRid(), simpleCode.getCodeName());
        }
        this.ifZoneApprove = "1".equals(PropertyUtils.getValueWithoutException("zzsb.ifZoneApprove"));

        this.outCarVersion = StringUtils.objectToString(PropertyUtils.getValueWithoutException("zzsb.outCarVersion"));
        this.ifShowCheckOrgAnaly = PropertyUtils.getValueWithoutException("zzsb.ifShowCheckOrgAnaly");
        this.ifShowApproveUnit = PropertyUtils.getValueWithoutException("zzsb.ifShowApproveUnit");
        this.ifHaveHouseAnnex = PropertyUtils.getValueWithoutException("zzsb.ifHaveHouseAnnex");
        this.fatherServerWithChildMap = new HashMap<>();
    }

    public void fillParams(Integer orgType, Integer rid){
        this.orgType = orgType;
        this.rid = rid;
    }

    public void executeQuery(){
        if(null == this.orgType || null == this.rid){
            return;
        }
        this.psnZzlbNew=new TdZwPsnZzlbZzsb();
        this.zwOrginfoEntity = this.zwIntellReportServiceImpl.findZwOrgInfoById(this.orgType,
                this.rid);
        //手机号以及电话号码加密
        if(this.ifBaseInfoEncry){
            // 手机号码加密
            String telMbNo = String.valueOf(null == this.zwOrginfoEntity.getLinkMb() ? ""
                    : this.zwOrginfoEntity.getLinkMb());
            if (StringUtils.isNotBlank(telMbNo)) {
                String num = "***************".substring(0, telMbNo.length() - 4);
                this.zwOrginfoEntity
                        .setLinkMb(num.concat(telMbNo.substring(telMbNo.length() - 4)));
            }

            // 电话号码加密
            String telNo = String.valueOf(null == this.zwOrginfoEntity.getLinkTel() ? ""
                    : this.zwOrginfoEntity.getLinkTel());
            if (StringUtils.isNotBlank(telNo)) {
                String num = "***************".substring(0, telNo.length() - 4);
                this.zwOrginfoEntity
                        .setLinkTel(num.concat(telNo.substring(telNo.length() - 4)));
            }
        }
        //批准的执业区域
        this.zoneApproveName = "";
        List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges = this.zwOrginfoEntity.getTdZwTjorgOutRanges();
        List<String> zoneApproveNameList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(tdZwTjorgOutRanges)) {
            for (TdZwTjorgOutRangeZzsb tdZwTjorgOutRange : tdZwTjorgOutRanges) {
                TsZone fkByZoneId = tdZwTjorgOutRange.getFkByZoneId();
                zoneApproveNameList.add(fkByZoneId.getZoneName());
            }
            if (!CollectionUtils.isEmpty(zoneApproveNameList)) {
                this.zoneApproveName = StringUtils.list2string(zoneApproveNameList, "，");
            }
        }

        // 服务项目
        this.initServiceItem();
        // 附件
        this.initAnnex();
        // 检查项目
        if (0 == this.orgType) {
            this.initZwJcItems();
            //服务明细
            initSerItem(this.jkItemList);
            this.jkItemRowList = this.convertZwJkItemPOToRowPO(this.jkItemList);
            //外检车辆
            this.initOutcar();
        }
        //获取人员证书编号
        if(null != this.zwOrginfoEntity.getZwOrgPsns() && this.zwOrginfoEntity.getZwOrgPsns().size()>0){
            for(IZwOrgPsnsZzsb t : this.zwOrginfoEntity.getZwOrgPsns()){
                if(null != t.getTdZwPsninfo().getZzlbList() && t.getTdZwPsninfo().getZzlbList().size()>0){
                    StringBuilder sb=new StringBuilder();
                    for(TdZwPsnZzlbZzsb zz:t.getTdZwPsninfo().getZzlbList()){
                        sb.append(",").append(zz.getCentNo());
                    }
                    t.getTdZwPsninfo().setCertNoStr(sb.deleteCharAt(0).toString());
                }
            }
        }

        this.zoneNames = "";
        if(!CollectionUtils.isEmpty(this.zwOrginfoEntity.getZwOrgOutRanges())) {
            StringBuilder nameSb = new StringBuilder();
            for(IZwOrgOutRangeZzsb orgOutRange : this.zwOrginfoEntity.getZwOrgOutRanges()) {
                nameSb.append("，").append(orgOutRange.getFkByZoneId().getZoneName());
            }
            this.zoneNames = nameSb.substring(1);
        }
        //按地区备案
        if ("1".equals(this.ifZoneRecord) && 0 == this.orgType) {
            this.initTjorgRecords();
        }
        if ("1".equals(this.ifPersonSummary)) {
            this.fillDesc = this.commService.findParamValue("PSNINFO_FILL_DESC");
            this.initPersonSummary();
        }
        this.initPsnType();
        this.initInstitutionList();
        this.initCheckRst();
        //选中第一个tab
        RequestContext.getCurrentInstance().execute("PF('OrgInfoViewTableView').select(0)");
    }

    /**
     * <p>方法描述： 人员信息 点击人员姓名 </p>
     * @MethodAuthor： pw 2022/10/29
     **/
    public void codeViewAction(){
        this.tdZwPsninfo=zwIntellReportServiceImpl.findTdZwPsnInfo(this.tdZwPsninfo.getRid());
        if(this.ifPsnInfoEncry){
            // 隐藏身份证
            String idcCard = this.tdZwPsninfo.getIdcCard();
            String encryptIdc = StringUtils.encryptIdc(idcCard);
            this.tdZwPsninfo.setIdcCard(encryptIdc);
            // 加密手机号码
            String mobileNo = this.tdZwPsninfo.getMobileNo();
            if (StringUtils.isNotBlank(mobileNo)) {
                if(mobileNo.length() > 4){
                    String num = "***************".substring(0, mobileNo.length() - 4);
                    this.tdZwPsninfo.setMobileNo(num.concat(mobileNo.substring(mobileNo
                            .length() - 4)));
                }
            }

            // 加密手机号码
            String telPhone = this.tdZwPsninfo.getTelPhone();
            if (StringUtils.isNotBlank(telPhone)) {
                if(telPhone.length() > 4){
                    String num = "***************".substring(0, telPhone.length() - 4);
                    this.tdZwPsninfo.setTelPhone(num.concat(telPhone.substring(telPhone
                            .length() - 4)));
                }
            }

            // 加密收件人手机号码
            String receiveMobileNo = this.tdZwPsninfo.getReceiveMobileNo();
            if (StringUtils.isNotBlank(receiveMobileNo)) {
                if(receiveMobileNo.length() > 4){
                    String num = "***************".substring(0, receiveMobileNo.length() - 4);
                    this.tdZwPsninfo.setReceiveMobileNo(num.concat(receiveMobileNo.substring(receiveMobileNo
                            .length() - 4)));
                }
            }
            /**出生年月**/
            if (StringUtils.isNotBlank(this.tdZwPsninfo.getBirthday())) {
                this.tdZwPsninfo.setBirthday("**********");
            }
        }
        this.psnInfoBase.setZwOrgType(this.orgType);
        this.psnInfoBase.setPsnInfo(this.tdZwPsninfo);
        this.psnInfoBase.initPsnInfo();
        this.psnInfoBase.initPsnZzlb();
        RequestContext.getCurrentInstance().execute("disabledInput()");
    }

    /**
     * <p>方法描述： 人员专业资质信息查看 需要禁用组件 </p>
     * @MethodAuthor： pw 2022/10/31
     **/
    public void zzlbEditAction(){
        this.psnInfoBase.zzlbEditAction();
        RequestContext.getCurrentInstance().execute("disabledZzlbInput()");
    }

    /**
     * <p>方法描述：质量控制结果初始化 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    private void initCheckRst() {
        this.zwCheckRstList = new ArrayList<>();
        //职业健康检查资质申报/职业病诊断资质申报
        if("1".equals(this.zkCheckRst) && ( this.orgType == 0 || this.orgType == 2 )){
            StringBuilder excuteSqlStr = new StringBuilder();
            excuteSqlStr.append(" SELECT T.EXPERT_LEADERS,T.EXPERT_MEMBERS,T.PROBLEMS,tsc.CODE_NAME AS CHECK_RST_NAME , T1.CODE_NAME AS ZG_RST_NAME ")
                    .append(" ,T.CHECK_DATE,T2.UNITNAME ,T.NOTICE_FILE_PATH ,T.ZG_FILE_PATH FROM  TD_ZW_CHECK_RST T ")
                    .append(" LEFT JOIN TS_SIMPLE_CODE tsc  ON T.CHECK_RST_ID = tsc.RID  ")
                    .append(" LEFT JOIN TS_SIMPLE_CODE T1  ON T.ZG_RST_ID = T1.RID ")
                    .append(" LEFT JOIN TS_UNIT T2 ON T.CHECK_UNIT_ID = T2.RID ")
                    .append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.CHECK_TYPE_ID = T3.RID ")
                    .append(" WHERE T.STATE =1 AND T.DEL_MARK = 0 AND T.UNIT_ID = ").append(this.zwOrginfoEntity.getTsUnit().getRid());
            if(this.orgType == 0){
                excuteSqlStr.append("AND  T3.EXTENDS1 = 1") ;
            }else {
                excuteSqlStr.append("AND  T3.EXTENDS1 = 2") ;
            }
            excuteSqlStr.append(" ORDER BY T.CHECK_DATE DESC,T.RID DESC");
            this.zwCheckRstList = this.commService.getSqlList(excuteSqlStr.toString());
        }
    }

    /**
     * <p>方法描述：初始化质量管理体系 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    public void initInstitutionList() {
        this.ifQualAnnex = PropertyUtils.getValue("zzsb.ifQualAnnex");
        if(!"1".equals(ifQualAnnex)){
            return;
        }
        this.topOfInstitutionList = new ArrayList<>();
        this.topInstitutionMap = new HashMap<>();
        this.institutionMap = new HashMap<>();
        this.qualAnnexMap = new HashMap<>();

        if(null != this.zwOrginfoEntity && this.zwOrginfoEntity instanceof TdZwTjorginfoZzsb){
            if(CollectionUtils.isEmpty(this.institutionList)){
                this.institutionList= this.commService.findLevelSimpleCodesByTypeId("5507");
            }
            this.initInstitutionListAndMap(this.zwOrginfoEntity.getRid());
        }
    }

    /** 查询数据 组合数据 */
    private void initInstitutionListAndMap(Integer mainId){
        if(CollectionUtils.isEmpty(this.institutionList)){
            return;// 码表无值 停止
        }
        //初始化顶级
        for(TsSimpleCode tsCode : this.institutionList){
            if(null != tsCode.getCodeLevelNo() && tsCode.getCodeLevelNo().indexOf(".") < 0){
                //码表扩展字段2无值 或者不是1 或者具备外出开展职业健康检查工作能力
                boolean flag = null == tsCode.getExtendS2() || 1 != tsCode.getExtendS2() ||
                        (null != this.zwOrginfoEntity.getOutWorkPower() && 1 == this.zwOrginfoEntity.getOutWorkPower());
                if(flag){
                    this.topOfInstitutionList.add(tsCode);
                    this.topInstitutionMap.put(tsCode.getRid(),tsCode);
                }
            }
        }
        for(TsSimpleCode tsChCode : this.institutionList){
            for(TsSimpleCode top : this.topOfInstitutionList){
                if(null != tsChCode.getCodeLevelNo() && tsChCode.getCodeLevelNo().indexOf(".") > -1 &&
                        tsChCode.getCodeLevelNo().startsWith(top.getCodeLevelNo())){
                    List<TsSimpleCode> tsList = this.institutionMap.get(top.getRid());
                    if(null == tsList){
                        tsList = new ArrayList<>();
                    }
                    tsList.add(tsChCode);
                    this.institutionMap.put(top.getRid(),tsList);
                }
            }
        }
        if(!CollectionUtils.isEmpty(this.institutionMap)){
            for(Map.Entry<Integer, List<TsSimpleCode>> mapTmp : this.institutionMap.entrySet()){
                Integer mapKey = mapTmp.getKey();
                List<TsSimpleCode> tsTmpList = mapTmp.getValue();
                List<TdZwTjorgQualAnnexZzsb> annexList = this.qualAnnexMap.get(mapKey);
                if(null == annexList){
                    annexList = new ArrayList<>();
                }
                if(null != tsTmpList && tsTmpList.size() > 0){
                    for(TsSimpleCode tsCode : tsTmpList){
                        annexList.add(new TdZwTjorgQualAnnexZzsb(tsCode));
                    }
                }
                this.qualAnnexMap.put(mapKey,annexList);
            }
        }
        if(null != mainId){
            String hql = " from TdZwTjorgQualAnnexZzsb t where t.fkByMainId.rid = " + mainId.intValue();
            List<TdZwTjorgQualAnnexZzsb> annexList = commService.findData(hql, null);
            if(annexList != null && annexList.size() > 0){
                //只要保存了就会存在基础数据 不需要判断 基础数据不存在的问题
                for(TdZwTjorgQualAnnexZzsb annex : annexList){
                    if(!CollectionUtils.isEmpty(this.topInstitutionMap) &&
                            null != this.topInstitutionMap.get(annex.getFkByRuleId().getRid())){
                        List<TdZwTjorgQualAnnexZzsb> tmpAnnexList = this.qualAnnexMap.get(annex.getFkByRuleId().getRid());
                        if(null == tmpAnnexList){
                            tmpAnnexList = new ArrayList<>();
                        }
                        tmpAnnexList.add(annex);
                        this.qualAnnexMap.put(annex.getFkByRuleId().getRid(), tmpAnnexList);
                        continue;
                    }

                    for(Map.Entry<Integer,List<TdZwTjorgQualAnnexZzsb>> tmpMap : this.qualAnnexMap.entrySet()){
                        List<TdZwTjorgQualAnnexZzsb> tmpList = tmpMap.getValue();
                        boolean flag = false;
                        if(null != tmpList && tmpList.size() > 0){
                            for(TdZwTjorgQualAnnexZzsb qualTmp : tmpList){
                                if(qualTmp.getFkByRuleId().getRid() ==
                                        annex.getFkByRuleId().getRid().intValue()){
                                    this.fillTjorgQualAnnexData(qualTmp, annex);
                                    flag = true;
                                    break;
                                }
                            }
                            if(flag){
                                break;
                            }
                        }
                        if(flag){
                            break;
                        }
                    }
                }
            }else{
                this.qualAnnexMap = new HashMap<>();
            }
        }else{
            this.qualAnnexMap = new HashMap<>();
        }
    }

    /**
     * <p>方法描述： 给对象赋值 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    public void fillTjorgQualAnnexData(TdZwTjorgQualAnnexZzsb tmp, TdZwTjorgQualAnnexZzsb qualAnnex){
        tmp.setRid(qualAnnex.getRid());
        tmp.setFkByMainId(qualAnnex.getFkByMainId());
        tmp.setFkByRuleId(qualAnnex.getFkByRuleId());
        tmp.setOtherRuleName(qualAnnex.getOtherRuleName());
        tmp.setIfHas(qualAnnex.getIfHas());
        tmp.setAnnexPath(qualAnnex.getAnnexPath());
        tmp.setCreateDate(qualAnnex.getCreateDate());
        tmp.setCreateManid(qualAnnex.getCreateManid());
        tmp.setModifyManid(qualAnnex.getModifyManid());
        tmp.setModifyDate(qualAnnex.getModifyDate());
    }

    /**
     * <p>方法描述： 初始化人员属性 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    private void initPsnType() {
        List<IZwOrgPsnsZzsb> zwOrgPsns = this.zwOrginfoEntity.getZwOrgPsns();
        if (!CollectionUtils.isEmpty(zwOrgPsns)) {
            List<Integer> psnIds = new ArrayList<>();
            for (IZwOrgPsnsZzsb t : zwOrgPsns){
                TdZwPsninfoZzsb psnInfo = t.getTdZwPsninfo();
                if (null!=psnInfo && null!=psnInfo.getRid()){
                    psnIds.add(psnInfo.getRid().intValue());
                }
            }

            Map<Integer,List<TsSimpleCode>> simpleCodeMap = new HashMap<>();// key TdZwPsninfo rid
            Map<Integer,List<TdZwZzlbRangeZzsb>> orgPsnRidsWithZzlbRangeMap = new HashMap<>();//key TD_ZW_TJORGPSNS rid
            Map<Integer,List<TdZwPsnZzlbZzsb>> orgPsnRidsWithCentNoMap = new HashMap<>();//key TD_ZW_TJORGPSNS rid
            if(!CollectionUtils.isEmpty(psnIds)){
                List<Object[]> resultList = new ArrayList<>();
                List<List<Integer>> psnIdList = StringUtils.splitListProxy(psnIds, 1000);
                StringBuilder excuteSqlStr = new StringBuilder();
                for(List<Integer> psnId : psnIdList){
                    excuteSqlStr = new StringBuilder();
                    excuteSqlStr.append(" SELECT T2.EXTENDS2,T2.EXTENDS3,T1.RID, T2.CODE_NAME,T2.RID AS CODERID,T.OTHER_PSN_TYPE FROM TD_ZW_PSN_TYPE T ")
                            .append(" INNER JOIN TD_ZW_PSNINFO T1 ON T.MAIN_ID = T1.RID ")
                            .append(" INNER JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.PSN_TYPE ")
                            .append(" WHERE T1.RID IN (:psnId)")
                            .append(" AND T.ZZJG_TYPE = '").append(this.orgType).append("' ");
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("psnId", psnId);
                    List<Object[]> results = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
                    if(CollectionUtils.isEmpty(results)){
                        continue;
                    }
                    resultList.addAll(results);
                }
                if(!CollectionUtils.isEmpty(resultList)){
                    for(Object[] objArr : resultList){
                        Integer rid = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
                        String otherPsnType = null == objArr[5] ? null : objArr[5].toString();
                        Integer codeRid = null == objArr[4] ? null : Integer.parseInt(objArr[4].toString());
                        if(null == rid || null == codeRid){
                            continue;
                        }
                        Integer extend2 = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                        String extend3 = null == objArr[1] ? null : objArr[1].toString();
                        String codeName = null == objArr[3] ? null : objArr[3].toString();
                        List<TsSimpleCode> simpleList = simpleCodeMap.get(rid.intValue());
                        if(null == simpleList){
                            simpleList = new ArrayList<>();
                        }
                        TsSimpleCode tsSimpleCode = new TsSimpleCode(codeRid);
                        tsSimpleCode.setCodeName(codeName);
                        tsSimpleCode.setExtendS2(extend2);
                        tsSimpleCode.setExtendS3(extend3);
                        tsSimpleCode.setExtendS4(otherPsnType);//将OtherPsnType 放到extends4字段中 由后边使用
                        simpleList.add(tsSimpleCode);
                        simpleCodeMap.put(rid.intValue(), simpleList);
                    }
                }

                // 从事项目
                List<Object[]> rangeObjArrList = new ArrayList<>();
                for(List<Integer> psnId : psnIdList){
                    excuteSqlStr = new StringBuilder();
                    excuteSqlStr.append(" SELECT DISTINCT T.ZZ_RANGE_ID, T2.RID AS EMP_ID, T4.CODE_NO, T4.CODE_NAME ");
                    excuteSqlStr.append(" FROM TD_ZW_ZZLB_RANGE T ");
                    excuteSqlStr.append(" INNER JOIN TD_ZW_PSN_ZZLB T1 ON T.MAIN_ID = T1.RID ");
                    excuteSqlStr.append(" INNER JOIN TD_ZW_PSNINFO T2 ON T1.PSN_ID = T2.RID ");
                    excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T4 ON T.ZZ_RANGE_ID = T4.RID ");
                    excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T5 ON T1.QUAL_ID = T5.RID ");
                    excuteSqlStr.append(" WHERE T2.RID IN (:psnId)");
                    excuteSqlStr.append(" AND (T5.CODE_DESC IS NULL OR T5.CODE_DESC LIKE '%").append(this.orgType).append("%') ");
                    excuteSqlStr.append(" GROUP BY T.ZZ_RANGE_ID, T2.RID, T4.CODE_NO, T4.CODE_NAME ");
                    excuteSqlStr.append(" ORDER BY T2.RID, T4.CODE_NO ");
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("psnId", psnId);
                    List<Object[]> rangeObjs = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
                    if(CollectionUtils.isEmpty(rangeObjs)){
                        continue;
                    }
                    rangeObjArrList.addAll(rangeObjs);
                }
                if(!CollectionUtils.isEmpty(rangeObjArrList)) {
                    for(Object[] objArr : rangeObjArrList) {
                        Integer rangeId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                        Integer empId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                        String codeNo = null == objArr[2] ? null : objArr[2].toString();
                        String codeName = null == objArr[3] ? null : objArr[3].toString();
                        TdZwZzlbRangeZzsb zzlbRange = new TdZwZzlbRangeZzsb();
                        TsSimpleCode rangeSimpleCode = new TsSimpleCode(rangeId);
                        rangeSimpleCode.setCodeName(codeName);
                        rangeSimpleCode.setCodeNo(codeNo);
                        zzlbRange.setFkByZzRangeId(rangeSimpleCode);
                        List<TdZwZzlbRangeZzsb> zwZzlbRangeList = orgPsnRidsWithZzlbRangeMap.get(empId.intValue());
                        if(null == zwZzlbRangeList) {
                            zwZzlbRangeList = new ArrayList<>();
                        }
                        zwZzlbRangeList.add(zzlbRange);
                        orgPsnRidsWithZzlbRangeMap.put(empId.intValue(), zwZzlbRangeList);
                    }
                }

                // 证书编号
                List<Object[]> psnZzlbObjArrList = new ArrayList<>();
                for(List<Integer> psnId : psnIdList){
                    excuteSqlStr = new StringBuilder();
                    excuteSqlStr.append(" SELECT T.RID, T.PSN_ID, T.QUAL_ID, T.CENT_NO, T.SEND_DATE, T.VALID_DATE, T.AGEIN_SEND_DATE, T.AGEIN_VALID_DATE ");
                    excuteSqlStr.append(" FROM TD_ZW_PSN_ZZLB T ");
                    excuteSqlStr.append(" INNER JOIN TD_ZW_PSNINFO T1 ON T.PSN_ID = T1.RID ");
                    excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.QUAL_ID = T2.RID ");
                    excuteSqlStr.append(" WHERE T1.RID IN (:psnId)");
                    excuteSqlStr.append(" AND (T2.CODE_DESC IS NULL OR T2.CODE_DESC LIKE '%").append(this.orgType).append("%') ");
                    excuteSqlStr.append(" ORDER BY T.SEND_DATE DESC ");
                    Map<String, Object> paramMap = new HashMap<>();
                    paramMap.put("psnId", psnId);
                    List<Object[]> psnZzlbObjs = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
                    if(CollectionUtils.isEmpty(psnZzlbObjs)){
                        continue;
                    }
                    psnZzlbObjArrList.addAll(psnZzlbObjs);
                }
                if(!CollectionUtils.isEmpty(psnZzlbObjArrList)) {
                    for(Object[] objArr : psnZzlbObjArrList) {
                        Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                        Integer psnId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                        Integer qualId = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
                        String codeNo = null == objArr[3] ? null : objArr[3].toString();
                        TdZwPsnZzlbZzsb tdZwPsnZzlb = new TdZwPsnZzlbZzsb(rid);
                        tdZwPsnZzlb.setFkByPsnId(new TdZwPsninfoZzsb(psnId));
                        TsSimpleCode tsSimpleCode = new TsSimpleCode(qualId);
                        tsSimpleCode.setCodeDesc(this.orgType.toString());
                        tdZwPsnZzlb.setFkByQualId(tsSimpleCode);
                        tdZwPsnZzlb.setCentNo(codeNo);
                        List<TdZwPsnZzlbZzsb> zwPsnZzlbList = orgPsnRidsWithCentNoMap.get(psnId.intValue());
                        if(null == zwPsnZzlbList) {
                            zwPsnZzlbList = new ArrayList<>();
                        }
                        zwPsnZzlbList.add(tdZwPsnZzlb);
                        orgPsnRidsWithCentNoMap.put(psnId.intValue(), zwPsnZzlbList);
                    }
                }
            }
            for (IZwOrgPsnsZzsb t : zwOrgPsns) {
                TdZwPsninfoZzsb psnInfo = t.getTdZwPsninfo();
                //给人员属性赋值
                if (null!=psnInfo && null!=psnInfo.getRid()) {
                    List<TsSimpleCode> simpleList = simpleCodeMap.get(psnInfo.getRid().intValue());
                    if(!CollectionUtils.isEmpty(simpleList)){
                        StringBuilder sb = new StringBuilder();
                        for(TsSimpleCode psnType : simpleList){
                            sb.append("，").append(psnType.getCodeName());
                            if (null!=psnType.getExtendS2() && 1==psnType.getExtendS2()) {
                                if ("1".equals(psnInfo.getIfCheckExpert())) {
                                    sb.append("（鉴定专家）");
                                }
                            }
                            if ("4".equals(psnType.getExtendS3())) {
                                //注意 查询的时候将OtherPsnType 放到extends4字段中 避免实例化多个类型的对象
                                if (StringUtils.isNotBlank(psnType.getExtendS4())) {
                                    sb.append("（").append(psnType.getExtendS4()).append("）");
                                }
                            }
                        }
                        psnInfo.setPsnTypes(sb.substring(1));
                    }

                    // 从事项目
                    List<TdZwZzlbRangeZzsb> zwZzlbRanges = orgPsnRidsWithZzlbRangeMap.get(psnInfo.getRid().intValue());
                    if(!CollectionUtils.isEmpty(zwZzlbRanges)) {
                        StringBuilder name = new StringBuilder();
                        for(TdZwZzlbRangeZzsb items : zwZzlbRanges) {
                            name.append("，").append(items.getFkByZzRangeId().getCodeName());
                        }
                        psnInfo.setZzlbRange(name.deleteCharAt(0).toString());
                    }

                    // 证书编号
                    List<TdZwPsnZzlbZzsb> zwPsnZzlbList = orgPsnRidsWithCentNoMap.get(psnInfo.getRid().intValue());
                    if(!CollectionUtils.isEmpty(zwPsnZzlbList)) {
                        StringBuilder name = new StringBuilder();
                        for(TdZwPsnZzlbZzsb items : zwPsnZzlbList) {
                            name.append(",").append(items.getCentNo());
                        }
                        psnInfo.setCertNoStr(name.deleteCharAt(0).toString());
                    }else{
                        psnInfo.setCertNoStr(null);
                    }
                }
            }
        }
    }

    /**
     * <p>方法描述： 人员汇总 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    private void initPersonSummary() {
        this.personSummaryList = new ArrayList<>();
        if (null != this.zwOrginfoEntity.getRid() && 0 == this.orgType) {
            List<Object[]> list = this.zwIntellReportServiceImpl.findPsnTypeSummary(this.orgType , this.zwOrginfoEntity.getRid());
            if (!CollectionUtils.isEmpty(list)) {
                List<String> row = new ArrayList<>();
                int i = 0;
                for (Object[] obj : list) {
                    if (i%7==0) {
                        row = new ArrayList<>();
                        this.personSummaryList.add(row);
                    }
                    row.add(obj[1]+"："+obj[2]+"人");
                    i++;
                }
            }
        }
    }

    /**
     * <p>方法描述：初始化备案记录 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    private void initTjorgRecords() {
        this.tjorgRecords = this.zwIntellReportServiceImpl.findTjorgRecords(this.zwOrginfoEntity.getRid());
        if (!CollectionUtils.isEmpty(this.tjorgRecords)) {
            for (TdZwTjorgRecordZzsb t : this.tjorgRecords) {
                List<TdZwTjorgRcdItemZzsb> rcdItems = t.getRcdItems();
                if (!CollectionUtils.isEmpty(rcdItems)) {
                    List<String> serviceObj = new ArrayList<>();
                    StringBuffer sb = new StringBuffer();
                    for (TdZwTjorgRcdItemZzsb itm : rcdItems) {
                        sb.append("，").append(itm.getFkByItemId().getCodeName());
                        serviceObj.add(itm.getFkByItemId().getRid().toString());
                    }
                    t.setServiceItems(sb.substring(1));
                    t.setServiceObj(serviceObj);
                }
            }
        }
    }

    /**
     * <p>描述 转换ZwJkItemPO-->ZwJkItemRowPO</p>
     *
     * @param zwJkItemPOS
     * @MethodAuthor gongzhe,2022/4/15 15:43,convertZwJkItemPOToRowPO
     * @return java.util.List<com.chis.modules.heth.zzsb.logic.ZwJkItemRowPO>
     */
    public List<ZwJkItemRowPO> convertZwJkItemPOToRowPO(List<ZwJkItemPO> zwJkItemPOS){
        boolean out = this.zwOrginfoEntity!=null && this.zwOrginfoEntity.getOutWorkPower()!=null && this.zwOrginfoEntity.getOutWorkPower()==1;
        List<ZwJkItemRowPO> result = new ArrayList<>();
        int start = 0;
        Map<Integer,Integer> rowspanMap = new HashMap<>();
        if(this.zwOrginfoEntity.getState()==1){
            //查看时，需过滤掉停用的码表，计算需要合并的行
            for (int i = 0; i < zwJkItemPOS.size(); i++) {
                ZwJkItemPO itemPO = zwJkItemPOS.get(i);
                TsSimpleCode jcItem = itemPO.getJcItem();
                List<TsSimpleCode> jcItemList = itemPO.getJcItemList();
                //已勾选的服务小类
                List<String> selectedSerItmDetail = itemPO.getSelectedSerItmDetail()==null ? null: Arrays.asList(itemPO.getSelectedSerItmDetail());
                if(CollectionUtils.isEmpty(selectedSerItmDetail)){
                    rowspanMap.put(jcItem.getRid(),0);
                    continue;
                }
                //未停用码表数量
                int count = 0;
                for (int j = 0; j < jcItemList.size(); j++) {
                    TsSimpleCode c = jcItemList.get(j);
                    if(selectedSerItmDetail.contains(c.getRid().toString())){
                        count++;
                    }
                }
                rowspanMap.put(jcItem.getRid(),count);
            }
        }
        for (int i = 0; i < zwJkItemPOS.size(); i++) {
            ZwJkItemPO itemPO = zwJkItemPOS.get(i);
            TsSimpleCode jcItem = itemPO.getJcItem();
            List<TsSimpleCode> jcItemList = itemPO.getJcItemList();
            if(i>0){
                if(this.zwOrginfoEntity.getState()==1){
                    start += rowspanMap.get(zwJkItemPOS.get(i-1).getJcItem().getRid());
                }else{
                    start += zwJkItemPOS.get(i-1).getJcItemList().size();
                }
            }
            //已勾选的服务小类
            List<String> selectedSerItmDetail = itemPO.getSelectedSerItmDetail()==null ? null: Arrays.asList(itemPO.getSelectedSerItmDetail());
            List<String> selectedIfExterInspect = itemPO.getSelectedIfExterInspect()==null ? null: Arrays.asList(itemPO.getSelectedIfExterInspect());
            for (int j = 0; j < jcItemList.size(); j++) {
                TsSimpleCode c = jcItemList.get(j);
                if(this.zwOrginfoEntity.getState()==1 && (selectedSerItmDetail == null || !selectedSerItmDetail.contains(c.getRid().toString()))) {
                    //如果是查看，且勾选的里面未包含该子项，则跳过
                    continue;
                }
                ZwJkItemRowPO row = new ZwJkItemRowPO();
                row.setJcItem(jcItem);
                row.setSerItem(c);
                if(this.zwOrginfoEntity.getState()==1){
                    row.setRowspan(rowspanMap.get(jcItem.getRid()));
                }else{
                    row.setRowspan(jcItemList.size());
                }
                row.setRowspanStart(start);
                if(selectedSerItmDetail!=null && selectedSerItmDetail.contains(c.getRid().toString())){
                    row.setSelectedSerItmDetail(new String[]{c.getRid().toString()});
                }
                if(out){
                    row.setIfExterInspect("0");
                }
                if(selectedIfExterInspect!=null && selectedIfExterInspect.contains(c.getRid().toString())){
                    row.setSelectedIfExterInspect(new String[]{c.getRid().toString()});
                    row.setIfExterInspect("1");
                }
                result.add(row);
            }
        }
        return result;
    }

    /**
     *  <p>方法描述：外检车辆</p>
     * @MethodAuthor hsj 2022/3/2 14:52
     */
    private void initOutcar() {
        this.tjorgOutcarZzsbList = this.zwIntellReportServiceImpl.findTjorgOutcarZzsbListByMainRid(this.zwOrginfoEntity.getRid());
        sortTjorgOutcarZzsbList();
    }
    /**
     * @Description: 按车牌号排序
     *
     * @MethodAuthor pw,2021年06月29日
     */
    private void sortTjorgOutcarZzsbList(){
        if(!CollectionUtils.isEmpty(this.tjorgOutcarZzsbList)){
            Collections.sort(this.tjorgOutcarZzsbList, new Comparator<TdZwTjorgOutcarZzsb>() {
                @Override
                public int compare(TdZwTjorgOutcarZzsb o1, TdZwTjorgOutcarZzsb o2) {
                    String carNo1 = o1.getCarNo();
                    String carNo2 = o2.getCarNo();
                    if(null == carNo1 && null == carNo2){
                        return 0;
                    }else if(null == carNo1){
                        return -1;
                    }else if(null == carNo2){
                        return 1;
                    }
                    return carNo1.compareTo(carNo2);
                }
            });
        }
    }

    /**
     * <p>方法描述： 初始化服务明细 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    public void initSerItem(List<ZwJkItemPO> zwJkItemPOS){
        Map<String,String[]> selectItems=new HashMap<String,String[]>();
        Map<String,String> selectItemName=new HashMap<String,String>();
        Map<String,String[]> selectItemsExter=new HashMap<String,String[]>();
        List<String> zwSvritmNoArray=new ArrayList<>();
        if(this.zwOrginfoEntity.getZwSvritm() != null && this.zwOrginfoEntity.getZwSvritm().size() > 0){
            List<IZwSvritmZzsb> list = this.zwOrginfoEntity.getZwSvritm();

            for(int i = 0 ; i < list.size();i++){
                IZwSvritmZzsb t = list.get(i);
                zwSvritmNoArray.add(t.getItemCode().toString());
                List<IZwSvritmSubZzsb> zwSvritmSubList = t.getZwSvritmSubList();
                if(!CollectionUtils.isEmpty(zwSvritmSubList)) {
                    String[] itemSubs = new String[zwSvritmSubList.size()];
                    List<String> itemSubsExter = new ArrayList<>();
                    StringBuffer itemNames=new StringBuffer();
                    for(int j = 0 ; j<zwSvritmSubList.size(); j++){
                        IZwSvritmSubZzsb sub = zwSvritmSubList.get(j);
                        if (null == sub.getFkBySubItemId()) {
                            continue;
                        }
                        itemSubs[j] = sub.getFkBySubItemId().getRid().toString();
                        itemNames.append(sub.getFkBySubItemId().getCodeName()).append("，");
                        if(sub.getIfExterInspect()!=null && sub.getIfExterInspect()==1){
                            itemSubsExter.add(itemSubs[j]);
                        }
                    }
                    selectItems.put(String.valueOf(t.getItemCode()),itemSubs);
                    selectItemName.put(String.valueOf(t.getItemCode()),itemNames.toString().substring(0,itemNames.toString().length()-1));
                    selectItemsExter.put(String.valueOf(t.getItemCode()),itemSubsExter.toArray(new String[0]));
                }
            }

        }
        List<ZwJkItemPO> newZwJkItemPO=new ArrayList<>();
        for(ZwJkItemPO itemPO:zwJkItemPOS){
            String key=itemPO.getJcItem().getCodeNo();
            if(null!=selectItems&&null!=selectItems.get(key)&&selectItems.get(key).length>0){
                itemPO.setSelectedSerItmDetail(selectItems.get(key));
                itemPO.setSelectedItemDetail(selectItemName.get(key));
            }
            itemPO.setSelectedIfExterInspect(selectItemsExter.get(key));
            if(zwSvritmNoArray.contains(itemPO.getJcItem().getCodeNo())){
                newZwJkItemPO.add(itemPO);
            }
        }
        for(ZwJkItemPO itemPO: newZwJkItemPO){
            if(itemPO.getSelectedSerItmDetail()!=null&&itemPO.getSelectedSerItmDetail().length==0){
                itemPO.setSelectedItemDetail(null);
            }
        }
        zwJkItemPOS.clear();
        zwJkItemPOS.addAll(newZwJkItemPO);
    }

    /**
     * <p>方法描述： 初始化检查项目 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    private void initZwJcItems() {
        this.showItemList = new ArrayList<Object[]>();
        String jcItems = this.zwOrginfoEntity.getJcItems();
        if (StringUtils.isNotBlank(jcItems)) {
            // 初始化选择检查项目
            this.initJcItemList();
            // 封装页面展示集合
            Iterator<TsSimpleCode> iterator = this.diagMap.keySet().iterator();
            // 序号
            int xh = 1;
            while (iterator.hasNext()) {
                // 存放子集数据
                List<String[]> subList = new ArrayList<String[]>();
                TsSimpleCode next = iterator.next();
                List<TsSimpleCode> list = this.diagMap.get(next);
                if (null != list && list.size() > 0) {
                    for (TsSimpleCode tscode : list) {
                        String rid = tscode.getRid().toString();
                        if (StringUtils.contains("," + jcItems + ",", "," + rid + ",")) {// 如当前项目被选中，则展示集合中增加该元素
                            String[] str=new String[2];
                            str[0]=tscode.getCodeName();
                            str[1]=rid;
                            subList.add(str);
                        }
                    }
                    if (subList.size() > 0) {
                        for (int i = 0; i < subList.size(); i++) {
                            String[] str = subList.get(i);
                            if(null != this.zwOrginfoEntity.getZwItems() && this.zwOrginfoEntity.getZwItems().size()>0){
                                for(IZwItemsZzsb item : this.zwOrginfoEntity.getZwItems()){
                                    if(null != str[1] && str[1].toString().equals(item.getTsSimpleCode().getRid().toString())){
                                        if(null != item.getIsOutter() && item.getIsOutter().intValue()==1){
                                            this.showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),true,str[1].toString() });
                                            continue;
                                        }else{
                                            this.showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),false,str[1].toString() });
                                            continue;
                                        }
                                    }
                                }
                            }else{
                                this.showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),false,str[1].toString() });
                            }
                            xh++;
                        }
                        subList.clear();
                    }
                }
            }
        }
    }

    private void initJcItemList() {
        if (null == this.diagMap) {// 5015
            List<TsSimpleCode> list = this.commService.findLevelSimpleCodesByTypeId("5015");
            // 选中的检查项目ids
            String jcItems = this.zwOrginfoEntity.getJcItems();
            if (null != list && list.size() > 0) {
                // 页面展示码表Map
                this.diagMap = new LinkedHashMap<TsSimpleCode, List<TsSimpleCode>>();
                for (TsSimpleCode code : list) {
                    if (!StringUtils.contains(code.getCodeLevelNo(), ".")) {
                        this.diagMap.put(code, new ArrayList<TsSimpleCode>());
                    }
                }
                Iterator<TsSimpleCode> it = this.diagMap.keySet().iterator();
                while (it.hasNext()) {
                    // 父级编码
                    TsSimpleCode parentCode = it.next();
                    String parentC = parentCode.getCodeNo();
                    // 清空选择和子集合
                    parentCode.setIfSelected(false);
                    parentCode.setSelCodeRids(null);
                    // 选中的Id
                    List<Integer> selList = new ArrayList<Integer>();
                    for (TsSimpleCode code : list) {
                        if (code.getCodeLevelNo().startsWith(parentC + ".")) {
                            this.diagMap.get(parentCode).add(code);
                            if (StringUtils.isNotBlank(jcItems)
                                    && StringUtils.contains("," + jcItems + ",", "," + code.getRid() + ",")) {
                                selList.add(code.getRid());
                            }
                        }
                    }

                    if (selList.size() > 0) {
                        String[] rids = new String[selList.size()];
                        for (int i = 0; i < selList.size(); i++) {
                            rids[i] = String.valueOf(selList.get(i));
                        }
                        parentCode.setSelCodeRids(rids);
                        if (rids.length == this.diagMap.get(parentCode).size()) {
                            parentCode.setIfSelected(true);
                        }
                    }
                }
            }
        }
    }

    /**
     * <p>方法描述： 初始化附件 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    private void initAnnex() {
        this.zwAnnexList1 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList2 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList3 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList4 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList5 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList6 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList7 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList8 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList9 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        this.zwAnnexList20 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
        List<TdZwJsffjgCommAnnexZzsb> list = this.zwIntellReportServiceImpl.findTdZwJsffjgCommAnnexListByOrgId(this.zwOrginfoEntity.getTsUnit().getRid().toString());
        if (list != null && list.size() > 0) {
            for(TdZwJsffjgCommAnnexZzsb t : list) {
                if(null != t.getRegZoneId()) {
                    TsZone tsZone = this.zwIntellReportServiceImpl.find(TsZone.class, t.getRegZoneId());
                    if(null != tsZone) {
                        t.setFkByRegZoneId(tsZone);
                    }
                }
                if (t.getAnnexType().equals(1)) {
                    this.zwAnnexList1.add(t);
                } else if(t.getAnnexType().equals(2)){
                    this.zwAnnexList2.add(t);
                }else if(t.getAnnexType().equals(3)){
                    this.zwAnnexList3.add(t);
                }else if(t.getAnnexType().equals(4)){
                    this.zwAnnexList4.add(t);
                }else if(t.getAnnexType().equals(5)){
                    this.zwAnnexList5.add(t);
                }else if(t.getAnnexType().equals(6)){
                    this.zwAnnexList6.add(t);
                }else if(t.getAnnexType().equals(7)){
                    this.zwAnnexList7.add(t);
                }else if(t.getAnnexType().equals(8)){
                    this.zwAnnexList8.add(t);
                }else if(t.getAnnexType().equals(11)){
                    this.zwAnnexList9.add(t);
                }else if(t.getAnnexType().equals(20)){
                    this.zwAnnexList20.add(t);
                }
            }
        }
    }

    /**
     * <p>方法描述： 初始化服务项目 </p>
     * @MethodAuthor： pw 2022/10/28
     **/
    private void initServiceItem() {
        //职业健康服务项目
        this.jkList = CollectionUtils.isEmpty(this.jkList) ?
                this.commService.findNumSimpleCodesByTypeId("5018") : this.jkList;
        //放射机构服务项目
        this.fsList = CollectionUtils.isEmpty(this.fsList) ?
                this.commService.findallSimpleCodesByTypeId("5019") : this.fsList;
        //诊断服务项目
        this.zyList = CollectionUtils.isEmpty(this.zyList) ?
                this.commService.findallSimpleCodesByTypeId("5020") : this.zyList;
        //职卫技术服务机构业务范围
        this.zwjsList = CollectionUtils.isEmpty(this.zwjsList) ?
                this.commService.findallSimpleCodesByTypeIdOrderByNum("5320") : this.zwjsList;
        this.serviceList = new ArrayList<String>();
        List<IZwSvritmZzsb> list = zwOrginfoEntity.getZwSvritm();
        String itemStr=null;
        if(list != null && list.size() > 0){
            if(3 == this.orgType){
                Map<String, String> itemCodeMap = new HashMap<String, String>();
                for (IZwSvritmZzsb t : list) {
                    String itemCode = t.getItemCode().toString();
                    itemCodeMap.put(itemCode, itemCode);
                }
                Map<String, TsSimpleCode> simpleMap = new HashMap<String,TsSimpleCode>();
                Map<String, List<String>> childMap = new LinkedHashMap<String, List<String>>();
                for(TsSimpleCode t : this.zwjsList){
                    simpleMap.put(t.getCodeNo(), t);
                    if (null!=itemCodeMap.get(t.getCodeNo())) {
                        String codeLevelNo = t.getCodeLevelNo();
                        String[] val = codeLevelNo.split("\\.");
                        if (null==childMap.get(val[0])) {
                            List<String> childList = new ArrayList<>();
                            childList.add(t.getCodeNo());
                            childMap.put(val[0], childList);
                        }else {
                            List<String> childList = childMap.get(val[0]);
                            childList.add(t.getCodeNo());
                        }
                    }
                }
                for (Map.Entry<String, List<String>> entry : childMap.entrySet()) {
                    String key = entry.getKey();
                    TsSimpleCode first = simpleMap.get(key);
                    StringBuffer sb = new StringBuffer();
                    sb.append(first.getCodeName());
                    sb.append("（");
                    List<String> childList = entry.getValue();
                    StringBuffer childSb = new StringBuffer();
                    for (String s : childList) {
                        TsSimpleCode child = simpleMap.get(s);
                        childSb.append("，").append(child.getCodeName());
                    }
                    sb.append(childSb.substring(1)).append("）");
                    serviceList.add(sb.toString());
                }

            }else if(1 == this.orgType){
                for(int i = 0 ; i < list.size();i++){
                    IZwSvritmZzsb t = list.get(i);
                    for (TsSimpleCode ts : this.fsList) {
                        if (ts.getCodeNo().equals(t.getItemCode().toString())) {
                            itemStr = ts.getCodeName();
                            List<IZwSvritmSubZzsb> zwSvritmSubList = t.getZwSvritmSubList();
                            if (!CollectionUtils.isEmpty(zwSvritmSubList)) {
                                StringBuffer subItemNames = new StringBuffer();
                                for (int j = 0; j < zwSvritmSubList.size(); j++) {
                                    IZwSvritmSubZzsb sub = zwSvritmSubList.get(j);
                                    if (null == sub.getFkBySubItemId()) {
                                        continue;
                                    }
                                    subItemNames.append("，").append(sub.getFkBySubItemId().getCodeName());
                                }
                                if (subItemNames.length() > 0) {
                                    itemStr = itemStr + "（" + subItemNames.substring(1) + "）";
                                }
                            }
                            serviceList.add(itemStr);
                        }
                    }
                }
            }else {
                for(int i = 0 ; i < list.size();i++){
                    IZwSvritmZzsb t = list.get(i);
                    if(0 == this.orgType){
                        for(TsSimpleCode ts : this.jkList){
                            if(ts.getCodeNo().equals(t.getItemCode().toString())){
                                itemStr=ts.getCodeName();
                            }
                        }
                    }
                    if(2 == this.orgType){
                        for(TsSimpleCode ts : this.zyList){
                            if(ts.getCodeNo().equals(t.getItemCode().toString())){
                                itemStr=ts.getCodeName();
                            }
                        }
                    }
                    this.serviceList.add(itemStr);
                }
            }
        }
        this.jkItemList = new ArrayList<>();
        if(0 == this.orgType && !CollectionUtils.isEmpty(this.jkList)){
            for(TsSimpleCode simpleCode : this.jkList){
                List<TsSimpleCode> jkItems = this.fatherServerWithChildMap.get(simpleCode.getCodeNo());
                if(CollectionUtils.isEmpty(jkItems)){
                    jkItems = this.commService.findallSimpleCodesByTypeIdAndExtends2("5527",simpleCode.getCodeNo());
                }
                if(!CollectionUtils.isEmpty(jkItems)){
                    this.fatherServerWithChildMap.put(simpleCode.getCodeNo(), jkItems);
                }
                ZwJkItemPO zwJkItemPO=new ZwJkItemPO();
                zwJkItemPO.setJcItem(simpleCode);
                zwJkItemPO.setJcItemList(new ArrayList<TsSimpleCode>());
                if(!CollectionUtils.isEmpty(jkItems)){
                    this.fatherServerWithChildMap.put(simpleCode.getCodeNo(), jkItems);
                    zwJkItemPO.getJcItemList().addAll(jkItems);
                }
                this.jkItemList.add(zwJkItemPO);
            }
        }
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getPerPageSize() {
        return perPageSize;
    }

    public void setPerPageSize(String perPageSize) {
        this.perPageSize = perPageSize;
    }

    public TdzwPsnInfoZzsbBase getPsnInfoBase() {
        return psnInfoBase;
    }

    public void setPsnInfoBase(TdzwPsnInfoZzsbBase psnInfoBase) {
        this.psnInfoBase = psnInfoBase;
    }

    public boolean isIfHethFj() {
        return ifHethFj;
    }

    public void setIfHethFj(boolean ifHethFj) {
        this.ifHethFj = ifHethFj;
    }

    public String getIfZoneRecord() {
        return ifZoneRecord;
    }

    public void setIfZoneRecord(String ifZoneRecord) {
        this.ifZoneRecord = ifZoneRecord;
    }

    public String getIfPersonSummary() {
        return ifPersonSummary;
    }

    public void setIfPersonSummary(String ifPersonSummary) {
        this.ifPersonSummary = ifPersonSummary;
    }

    public String getIfValidityPeriod() {
        return ifValidityPeriod;
    }

    public void setIfValidityPeriod(String ifValidityPeriod) {
        this.ifValidityPeriod = ifValidityPeriod;
    }

    public String getIfReviewProcess() {
        return ifReviewProcess;
    }

    public void setIfReviewProcess(String ifReviewProcess) {
        this.ifReviewProcess = ifReviewProcess;
    }

    public String getIfServiceDetails() {
        return ifServiceDetails;
    }

    public void setIfServiceDetails(String ifServiceDetails) {
        this.ifServiceDetails = ifServiceDetails;
    }

    public String getZkCheckRst() {
        return zkCheckRst;
    }

    public void setZkCheckRst(String zkCheckRst) {
        this.zkCheckRst = zkCheckRst;
    }

    public boolean isIfShowRcdOrg() {
        return ifShowRcdOrg;
    }

    public void setIfShowRcdOrg(boolean ifShowRcdOrg) {
        this.ifShowRcdOrg = ifShowRcdOrg;
    }

    public boolean isIfZoneApprove() {
        return ifZoneApprove;
    }

    public void setIfZoneApprove(boolean ifZoneApprove) {
        this.ifZoneApprove = ifZoneApprove;
    }

    public String getOutCarVersion() {
        return outCarVersion;
    }

    public void setOutCarVersion(String outCarVersion) {
        this.outCarVersion = outCarVersion;
    }

    public String getIfShowCheckOrgAnaly() {
        return ifShowCheckOrgAnaly;
    }

    public void setIfShowCheckOrgAnaly(String ifShowCheckOrgAnaly) {
        this.ifShowCheckOrgAnaly = ifShowCheckOrgAnaly;
    }

    public String getIfShowApproveUnit() {
        return ifShowApproveUnit;
    }

    public void setIfShowApproveUnit(String ifShowApproveUnit) {
        this.ifShowApproveUnit = ifShowApproveUnit;
    }

    public String getIfHaveHouseAnnex() {
        return ifHaveHouseAnnex;
    }

    public void setIfHaveHouseAnnex(String ifHaveHouseAnnex) {
        this.ifHaveHouseAnnex = ifHaveHouseAnnex;
    }

    public String getIfQualAnnex() {
        return ifQualAnnex;
    }

    public void setIfQualAnnex(String ifQualAnnex) {
        this.ifQualAnnex = ifQualAnnex;
    }

    public String getFillDesc() {
        return fillDesc;
    }

    public void setFillDesc(String fillDesc) {
        this.fillDesc = fillDesc;
    }

    public List<TsSimpleCode> getRcdOrgList() {
        return rcdOrgList;
    }

    public void setRcdOrgList(List<TsSimpleCode> rcdOrgList) {
        this.rcdOrgList = rcdOrgList;
    }

    public Map<Integer, String> getRcdOrgMap() {
        return rcdOrgMap;
    }

    public void setRcdOrgMap(Map<Integer, String> rcdOrgMap) {
        this.rcdOrgMap = rcdOrgMap;
    }

    public IZwOrginfoZzsb getZwOrginfoEntity() {
        return zwOrginfoEntity;
    }

    public void setZwOrginfoEntity(IZwOrginfoZzsb zwOrginfoEntity) {
        this.zwOrginfoEntity = zwOrginfoEntity;
    }

    public TdZwPsnZzlbZzsb getPsnZzlbNew() {
        return psnZzlbNew;
    }

    public void setPsnZzlbNew(TdZwPsnZzlbZzsb psnZzlbNew) {
        this.psnZzlbNew = psnZzlbNew;
    }

    public String getZoneApproveName() {
        return zoneApproveName;
    }

    public void setZoneApproveName(String zoneApproveName) {
        this.zoneApproveName = zoneApproveName;
    }

    public String getZoneNames() {
        return zoneNames;
    }

    public void setZoneNames(String zoneNames) {
        this.zoneNames = zoneNames;
    }

    public List<ZwJkItemPO> getJkItemList() {
        return jkItemList;
    }

    public void setJkItemList(List<ZwJkItemPO> jkItemList) {
        this.jkItemList = jkItemList;
    }

    public List<ZwJkItemRowPO> getJkItemRowList() {
        return jkItemRowList;
    }

    public void setJkItemRowList(List<ZwJkItemRowPO> jkItemRowList) {
        this.jkItemRowList = jkItemRowList;
    }

    public List<TsSimpleCode> getJkList() {
        return jkList;
    }

    public void setJkList(List<TsSimpleCode> jkList) {
        this.jkList = jkList;
    }

    public List<TsSimpleCode> getFsList() {
        return fsList;
    }

    public void setFsList(List<TsSimpleCode> fsList) {
        this.fsList = fsList;
    }

    public List<TsSimpleCode> getZyList() {
        return zyList;
    }

    public void setZyList(List<TsSimpleCode> zyList) {
        this.zyList = zyList;
    }

    public List<TsSimpleCode> getZwjsList() {
        return zwjsList;
    }

    public void setZwjsList(List<TsSimpleCode> zwjsList) {
        this.zwjsList = zwjsList;
    }

    public List<String> getServiceList() {
        return serviceList;
    }

    public void setServiceList(List<String> serviceList) {
        this.serviceList = serviceList;
    }

    public Map<String, List<TsSimpleCode>> getFatherServerWithChildMap() {
        return fatherServerWithChildMap;
    }

    public void setFatherServerWithChildMap(Map<String, List<TsSimpleCode>> fatherServerWithChildMap) {
        this.fatherServerWithChildMap = fatherServerWithChildMap;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList1() {
        return zwAnnexList1;
    }

    public void setZwAnnexList1(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList1) {
        this.zwAnnexList1 = zwAnnexList1;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList2() {
        return zwAnnexList2;
    }

    public void setZwAnnexList2(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList2) {
        this.zwAnnexList2 = zwAnnexList2;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList3() {
        return zwAnnexList3;
    }

    public void setZwAnnexList3(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList3) {
        this.zwAnnexList3 = zwAnnexList3;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList4() {
        return zwAnnexList4;
    }

    public void setZwAnnexList4(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList4) {
        this.zwAnnexList4 = zwAnnexList4;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList5() {
        return zwAnnexList5;
    }

    public void setZwAnnexList5(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList5) {
        this.zwAnnexList5 = zwAnnexList5;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList6() {
        return zwAnnexList6;
    }

    public void setZwAnnexList6(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList6) {
        this.zwAnnexList6 = zwAnnexList6;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList7() {
        return zwAnnexList7;
    }

    public void setZwAnnexList7(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList7) {
        this.zwAnnexList7 = zwAnnexList7;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList8() {
        return zwAnnexList8;
    }

    public void setZwAnnexList8(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList8) {
        this.zwAnnexList8 = zwAnnexList8;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList9() {
        return zwAnnexList9;
    }

    public void setZwAnnexList9(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList9) {
        this.zwAnnexList9 = zwAnnexList9;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList20() {
        return zwAnnexList20;
    }

    public void setZwAnnexList20(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList20) {
        this.zwAnnexList20 = zwAnnexList20;
    }

    public List<Object[]> getShowItemList() {
        return showItemList;
    }

    public void setShowItemList(List<Object[]> showItemList) {
        this.showItemList = showItemList;
    }

    public Map<TsSimpleCode, List<TsSimpleCode>> getDiagMap() {
        return diagMap;
    }

    public void setDiagMap(Map<TsSimpleCode, List<TsSimpleCode>> diagMap) {
        this.diagMap = diagMap;
    }

    public List<TdZwTjorgOutcarZzsb> getTjorgOutcarZzsbList() {
        return tjorgOutcarZzsbList;
    }

    public void setTjorgOutcarZzsbList(List<TdZwTjorgOutcarZzsb> tjorgOutcarZzsbList) {
        this.tjorgOutcarZzsbList = tjorgOutcarZzsbList;
    }

    public List<TdZwTjorgRecordZzsb> getTjorgRecords() {
        return tjorgRecords;
    }

    public void setTjorgRecords(List<TdZwTjorgRecordZzsb> tjorgRecords) {
        this.tjorgRecords = tjorgRecords;
    }

    public List<List<String>> getPersonSummaryList() {
        return personSummaryList;
    }

    public void setPersonSummaryList(List<List<String>> personSummaryList) {
        this.personSummaryList = personSummaryList;
    }

    public List<TsSimpleCode> getInstitutionList() {
        return institutionList;
    }

    public void setInstitutionList(List<TsSimpleCode> institutionList) {
        this.institutionList = institutionList;
    }

    public List<TsSimpleCode> getTopOfInstitutionList() {
        return topOfInstitutionList;
    }

    public void setTopOfInstitutionList(List<TsSimpleCode> topOfInstitutionList) {
        this.topOfInstitutionList = topOfInstitutionList;
    }

    public Map<Integer, TsSimpleCode> getTopInstitutionMap() {
        return topInstitutionMap;
    }

    public void setTopInstitutionMap(Map<Integer, TsSimpleCode> topInstitutionMap) {
        this.topInstitutionMap = topInstitutionMap;
    }

    public Map<Integer, List<TsSimpleCode>> getInstitutionMap() {
        return institutionMap;
    }

    public void setInstitutionMap(Map<Integer, List<TsSimpleCode>> institutionMap) {
        this.institutionMap = institutionMap;
    }

    public Map<Integer, List<TdZwTjorgQualAnnexZzsb>> getQualAnnexMap() {
        return qualAnnexMap;
    }

    public void setQualAnnexMap(Map<Integer, List<TdZwTjorgQualAnnexZzsb>> qualAnnexMap) {
        this.qualAnnexMap = qualAnnexMap;
    }

    public List<Object[]> getZwCheckRstList() {
        return zwCheckRstList;
    }

    public void setZwCheckRstList(List<Object[]> zwCheckRstList) {
        this.zwCheckRstList = zwCheckRstList;
    }

    public Integer getOrgType() {
        return orgType;
    }

    public void setOrgType(Integer orgType) {
        this.orgType = orgType;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwPsninfoZzsb getTdZwPsninfo() {
        return tdZwPsninfo;
    }

    public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
        this.tdZwPsninfo = tdZwPsninfo;
    }

    public boolean isIfBaseInfoEncry() {
        return ifBaseInfoEncry;
    }

    public void setIfBaseInfoEncry(boolean ifBaseInfoEncry) {
        this.ifBaseInfoEncry = ifBaseInfoEncry;
    }

    public boolean isIfPsnInfoEncry() {
        return ifPsnInfoEncry;
    }

    public void setIfPsnInfoEncry(boolean ifPsnInfoEncry) {
        this.ifPsnInfoEncry = ifPsnInfoEncry;
    }
}
