package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * 服务小类
 * Created by wlj on 2015-04-20.
 */
public interface IZwSvritmSubZzsb {

    public Integer getRid();
    public void setRid(Integer rid);

    public IZwSvritmZzsb getZwSvritm();
    public void setZwSvritm(IZwSvritmZzsb zwSvritm);

    public TsSimpleCode getFkBySubItemId();
    public void setFkBySubItemId(TsSimpleCode fkBySubItemId);
    public Integer getIfExterInspect();

    public void setIfExterInspect(Integer ifExterInspect);

    public Date getCreateDate();
    public void setCreateDate(Date createDate);

    public Integer getCreateManid();
    public void setCreateManid(Integer createManid);

    public Date getModifyDate();
    public void setModifyDate(Date modifyDate);

    public Integer getModifyManid();
    public void setModifyManid(Integer modifyManid);
}
