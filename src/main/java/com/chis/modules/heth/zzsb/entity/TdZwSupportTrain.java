package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-3-29
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_TRAIN")
@SequenceGenerator(name = "TdZwSupportTrain", sequenceName = "TD_ZW_SUPPORT_TRAIN_SEQ", allocationSize = 1)
public class TdZwSupportTrain implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSupportOrgZzsb fkByMainId;
	private Integer year;
	private TsSimpleCode fkByTrainId;
	private Integer partakeNumber;
	private String evidenceFilePath;
	private Date createDate;
	private Integer createManid;
	private Integer modifyManid;
	private Date modifyDate;
	
	public TdZwSupportTrain() {
	}

	public TdZwSupportTrain(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportTrain")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSupportOrgZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "YEAR")	
	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}	
			
	@ManyToOne
	@JoinColumn(name = "TRAIN_ID")			
	public TsSimpleCode getFkByTrainId() {
		return fkByTrainId;
	}

	public void setFkByTrainId(TsSimpleCode fkByTrainId) {
		this.fkByTrainId = fkByTrainId;
	}	
			
	@Column(name = "PARTAKE_NUMBER")	
	public Integer getPartakeNumber() {
		return partakeNumber;
	}

	public void setPartakeNumber(Integer partakeNumber) {
		this.partakeNumber = partakeNumber;
	}	
			
	@Column(name = "EVIDENCE_FILE_PATH")	
	public String getEvidenceFilePath() {
		return evidenceFilePath;
	}

	public void setEvidenceFilePath(String evidenceFilePath) {
		this.evidenceFilePath = evidenceFilePath;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
}