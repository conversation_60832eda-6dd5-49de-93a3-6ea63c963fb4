package com.chis.modules.heth.zzsb.entity;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

import javax.persistence.*;
import java.io.Serializable;
import java.text.Collator;
import java.util.*;

/**
 * 放射卫生技术服务机构基本信息
 *
 * <AUTHOR>
 * @createTime 2015-04-20
 * <p>修订内容：添加属性清空资质人员的所属机构的人员Id</p>
 * @ClassReviser qrr,2018年6月25日,TdZwDiagorginfo
 */
@Entity
@Table(name = "TD_ZW_SRVORGINFO")
@SequenceGenerator(name = "TdZwSrvorginfoSeq", sequenceName = "TD_ZW_SRVORGINFO_SEQ", allocationSize = 1)
public class TdZwSrvorginfoZzsb implements Serializable,IZwOrginfoZzsb {
	private static final long serialVersionUID = -2734284432353369122L;

	private Integer rid;
	private TsUnit tsUnit;
	private String orgName;
	private String orgAddr;
	private String orgFz;
	private String orgFzzw;
	private String linkMan;
	private String linkMb;
	private String linkTel;
	private String fax;
	private String zipcode;
	private String email;
	private String certNo;
	private Date firstGetday;
	private Date createDate;
	private Integer createManid;
	private List<TdZwOrgannexZzsb> tdZwOrgannexes = new LinkedList<TdZwOrgannexZzsb>();
	private List<TdZwSrvorgpsnsZzsb> tdZwSrvorgpsnses = new LinkedList<TdZwSrvorgpsnsZzsb>();
	private List<TdZwSrvorgitemsZzsb> tdZwSrvorgitemses = new LinkedList<TdZwSrvorgitemsZzsb>();
	private List<TdZwOrginstZzsb> tdZwOrginsts = new LinkedList<TdZwOrginstZzsb>();
    private Short state;

	private String jcItems;
	
	private Integer cancelState;
	private Date cancelDate;
	private Date validDate;
	//添加属性清空资质人员的所属机构的人员Id
	private String delPsnIds;
	//社会信用代码
	private String creditCode;
    /**+外出开展职业健康检查工作能力20200221*/
    private Integer outWorkPower;

	/**
	 * 备案管理机构ID
	 */
	private Integer rcdOrgId;
	/**
	 * +资质批准单位20220822
	 * */
	private String rcdUnitName;
	/**审核信息-审核结果*/
	private Integer checkRst;
	/**审核信息-审核意见*/
	private String auditAdv;
	/**审核信息-审核人*/
	private TsUserInfo fkByChkPsnId;
	/**主数据时间戳*/
	private Date masterDataTime;

	private Date modifyDate;
	private Integer modifyManid;
	public TdZwSrvorginfoZzsb() {
	}

	public TdZwSrvorginfoZzsb(Integer rid) {
		this.rid = rid;
	}
	public TdZwSrvorginfoZzsb(Integer rid, String orgName) {
		this.rid = rid;
		this.orgName = orgName;
	}
	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSrvorginfoSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TsUnit getTsUnit() {
		return this.tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}

	@Column(name = "ORG_NAME", length = 100)
	public String getOrgName() {
		return this.orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	@Column(name = "ORG_ADDR", length = 200)
	public String getOrgAddr() {
		return this.orgAddr;
	}

	public void setOrgAddr(String orgAddr) {
		this.orgAddr = orgAddr;
	}

	@Column(name = "ORG_FZ", length = 50)
	public String getOrgFz() {
		return this.orgFz;
	}

	public void setOrgFz(String orgFz) {
		this.orgFz = orgFz;
	}

	@Column(name = "ORG_FZZW", length = 50)
	public String getOrgFzzw() {
		return this.orgFzzw;
	}

	public void setOrgFzzw(String orgFzzw) {
		this.orgFzzw = orgFzzw;
	}

	@Column(name = "LINK_MAN", length = 50)
	public String getLinkMan() {
		return this.linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}

	@Column(name = "LINK_MB", length = 50)
	public String getLinkMb() {
		return this.linkMb;
	}

	public void setLinkMb(String linkMb) {
		this.linkMb = linkMb;
	}

	@Column(name = "LINK_TEL", length = 50)
	public String getLinkTel() {
		return this.linkTel;
	}

	public void setLinkTel(String linkTel) {
		this.linkTel = linkTel;
	}

	@Column(name = "FAX", length = 50)
	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	@Column(name = "ZIPCODE", length = 10)
	public String getZipcode() {
		return this.zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	@Column(name = "EMAIL", length = 50)
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Column(name = "CERT_NO", length = 50)
	public String getCertNo() {
		return this.certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "FIRST_GETDAY", length = 7)
	public Date getFirstGetday() {
		return this.firstGetday;
	}

	public void setFirstGetday(Date firstGetday) {
		this.firstGetday = firstGetday;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	@Override
	@Column(name = "RCD_UNIT_NAME")
	public String getRcdUnitName() {
		return rcdUnitName;
	}

	@Override
	public void setRcdUnitName(String rcdUnitName) {
		this.rcdUnitName = rcdUnitName;
	}

	@Transient
	@Override
	public List<IZwAnnexZzsb> getZwAnnex() {
		if(null != this.tdZwOrgannexes && this.tdZwOrgannexes.size() > 0) {
			List<IZwAnnexZzsb> list = new ArrayList<IZwAnnexZzsb>();
			for(TdZwOrgannexZzsb t : this.tdZwOrgannexes) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwAnnex(List<IZwAnnexZzsb> zwAnnexList) {
		if(null != zwAnnexList && zwAnnexList.size() > 0) {
			this.tdZwOrgannexes = new ArrayList<TdZwOrgannexZzsb>();
			for(IZwAnnexZzsb t : zwAnnexList) {
				this.tdZwOrgannexes.add((TdZwOrgannexZzsb) t);
			}
		}else if(null == zwAnnexList) {
			this.tdZwOrgannexes = null;
		}else {
			this.tdZwOrgannexes = new ArrayList<TdZwOrgannexZzsb>(0);
		}
	}

	@Transient
	@Override
	public List<IZwInstZzsb> getZwInst() {
		if(null != this.tdZwOrginsts && this.tdZwOrginsts.size() > 0) {
			List<IZwInstZzsb> list = new ArrayList<IZwInstZzsb>();
			for(TdZwOrginstZzsb t : this.tdZwOrginsts) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwInst(List<IZwInstZzsb> zwInstList) {
		if(null != zwInstList && zwInstList.size() > 0) {
			this.tdZwOrginsts = new ArrayList<TdZwOrginstZzsb>();
			for(IZwInstZzsb t : zwInstList) {
				this.tdZwOrginsts.add((TdZwOrginstZzsb) t);
			}
		}else if(null == zwInstList) {
			this.tdZwOrginsts = null;
		}else {
			this.tdZwOrginsts = new ArrayList<TdZwOrginstZzsb>(0);
		}
	}

	@Transient
	@Override
	public List<IZwItemsZzsb> getZwItems() {
		return null;
	}

	@Override
	public void setZwItems(List<IZwItemsZzsb> zwItemsList) {

	}

	@Transient
	@Override
	public List<IZwOrgPsnsZzsb> getZwOrgPsns() {
		if(null != this.tdZwSrvorgpsnses && this.tdZwSrvorgpsnses.size() > 0) {
			List<IZwOrgPsnsZzsb> list = new ArrayList<IZwOrgPsnsZzsb>(this.tdZwSrvorgpsnses);
			return sortZwOrgPsnsZzsbs(list);
		}else {
			return null;
		}
	}

	static List<IZwOrgPsnsZzsb> sortZwOrgPsnsZzsbs(List<IZwOrgPsnsZzsb> list) {
		Collections.sort(list, new Comparator<IZwOrgPsnsZzsb>() {
			@Override
			public int compare(IZwOrgPsnsZzsb o1, IZwOrgPsnsZzsb o2) {
				if (o1 == null || o2 == null) {
					return 0;
				}
				String obj1 = o1.getTdZwPsninfo() == null ? "" : StringUtils.objectToString(o1.getTdZwPsninfo().getEmpName());
				String obj2 = o2.getTdZwPsninfo() == null ? "" : StringUtils.objectToString(o2.getTdZwPsninfo().getEmpName());
				Collator com = Collator.getInstance(Locale.CHINA);
				return com.compare(obj1, obj2);
			}
		});
		return list;
	}

	@Override
	public void setZwOrgPsns(List<IZwOrgPsnsZzsb> zwOrgPsnsList) {
		if(null != zwOrgPsnsList && zwOrgPsnsList.size() > 0) {
			this.tdZwSrvorgpsnses = new ArrayList<TdZwSrvorgpsnsZzsb>();
			for(IZwOrgPsnsZzsb t : zwOrgPsnsList) {
				this.tdZwSrvorgpsnses.add((TdZwSrvorgpsnsZzsb) t);
			}
		}else if(null == zwOrgPsnsList) {
			this.tdZwSrvorgpsnses = null;
		}else {
			this.tdZwSrvorgpsnses = new ArrayList<TdZwSrvorgpsnsZzsb>(0);
		}
	}

	@Transient
	@Override
	public List<IZwSvritmZzsb> getZwSvritm() {
		if(null != this.tdZwSrvorgitemses && this.tdZwSrvorgitemses.size() > 0) {
			List<IZwSvritmZzsb> list = new ArrayList<IZwSvritmZzsb>();
			for(TdZwSrvorgitemsZzsb t : this.tdZwSrvorgitemses) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwSvritm(List<IZwSvritmZzsb> zwSvritmList) {
		if(null != zwSvritmList && zwSvritmList.size() > 0) {
			this.tdZwSrvorgitemses = new ArrayList<TdZwSrvorgitemsZzsb>();
			for(IZwSvritmZzsb t : zwSvritmList) {
				this.tdZwSrvorgitemses.add((TdZwSrvorgitemsZzsb) t);
			}
		}else if(null == zwSvritmList) {
			this.tdZwSrvorgitemses = null;
		}else {
			this.tdZwSrvorgitemses = new ArrayList<TdZwSrvorgitemsZzsb>(0);
		}
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwSrvorginfo",orphanRemoval = true)
	public List<TdZwOrgannexZzsb> getTdZwOrgannexes() {
		return tdZwOrgannexes;
	}

	public void setTdZwOrgannexes(List<TdZwOrgannexZzsb> tdZwOrgannexes) {
		this.tdZwOrgannexes = tdZwOrgannexes;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwSrvorginfo",orphanRemoval = true)
	public List<TdZwSrvorgpsnsZzsb> getTdZwSrvorgpsnses() {
		return tdZwSrvorgpsnses;
	}

	public void setTdZwSrvorgpsnses(List<TdZwSrvorgpsnsZzsb> tdZwSrvorgpsnses) {
		this.tdZwSrvorgpsnses = tdZwSrvorgpsnses;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwSrvorginfo",orphanRemoval = true)
	public List<TdZwSrvorgitemsZzsb> getTdZwSrvorgitemses() {
		return tdZwSrvorgitemses;
	}

	public void setTdZwSrvorgitemses(List<TdZwSrvorgitemsZzsb> tdZwSrvorgitemses) {
		this.tdZwSrvorgitemses = tdZwSrvorgitemses;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwSrvorginfo",orphanRemoval = true)
	public List<TdZwOrginstZzsb> getTdZwOrginsts() {
		return tdZwOrginsts;
	}

	public void setTdZwOrginsts(List<TdZwOrginstZzsb> tdZwOrginsts) {
		this.tdZwOrginsts = tdZwOrginsts;
	}
	
	@Transient
	@Override
	public String getJcItems() {
		return this.jcItems;
	}

	@Override
	public void setJcItems(String jcItems) {
		this.jcItems = jcItems;
	}

    @Column(name = "STATE")
    public Short getState() {
        return state;
    }

    public void setState(Short state) {
        this.state = state;
    }
    
    @Column(name = "CANCEL_STATE")	
	public Integer getCancelState() {
		return cancelState;
	}

	public void setCancelState(Integer cancelState) {
		this.cancelState = cancelState;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CANCEL_DATE")			
	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "VALID_DATE")			
	public Date getValidDate() {
		return validDate;
	}

	public void setValidDate(Date validDate) {
		this.validDate = validDate;
	}
	@Transient
	@Override
	public String getDelPsnIds() {
		return delPsnIds;
	}

	@Override
	public void setDelPsnIds(String delPsnIds) {
		this.delPsnIds = delPsnIds;
	}
	@Column(name = "CREDIT_CODE", length = 50)
	@Override
	public String getCreditCode() {
		return creditCode;
	}

	@Override
	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}
	@Transient
    @Override
    public TsSimpleCode getFkByLevelId() {
        return null;
    }

    @Override
    public void setFkByLevelId(TsSimpleCode fkByLevelId) {

    }

    @Transient
    @Override
    public Integer getOutWorkPower() {
        return outWorkPower;
    }

    @Override
    public void setOutWorkPower(Integer outWorkPower) {
        this.outWorkPower = outWorkPower;
    }

    @Transient
    @Override
    public List<IZwOrgOutRangeZzsb> getZwOrgOutRanges() {
        return null;
    }

    @Override
    public void setZwOrgOutRanges(List<IZwOrgOutRangeZzsb> zwOrgOutRangeList) {

    }
    @Transient
	@Override
	public String getLabAddr() {
		return null;
	}
	@Override
	public void setLabAddr(String labAddr) {
		
	}
	@Transient
	@Override
	public String getCommuniAddr() {
		return null;
	}
	@Override
	public void setCommuniAddr(String communiAddr) {
		
	}
	@Transient
	@Override
	public TsSimpleCode getFkByUnitTypeId() {
		return null;
	}
	@Override
	public void setFkByUnitTypeId(TsSimpleCode fkByUnitTypeId) {
		
	}
	@Transient
	@Override
	public Date getFilingDate() {return null;}
	@Override
	public void setFilingDate(Date filingDate) {}

	@Transient
	@Override
	public Integer getRcdOrgId() {
		return rcdOrgId;
	}

	@Override
	public void setRcdOrgId(Integer rcdOrgId) {
		this.rcdOrgId = rcdOrgId;
	}

	@Transient
	@Override
	public List<TdZwTjorgOutRangeZzsb> getTdZwTjorgOutRanges() {
		return null;
	}

	@Override
	public void setTdZwTjorgOutRanges(List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges) {
	}

	@Transient
	@Override
	public String getZoneApproves() {
		return null;
	}

	@Override
	public void setZoneApproves(String zoneApproves) {

	}
	@Column(name = "CHECK_RST")
	@Override
	public Integer getCheckRst() {
		return checkRst;
	}

	@Override
	public void setCheckRst(Integer checkRst) {
		this.checkRst = checkRst;
	}
	@Column(name = "AUDIT_ADV")
	@Override
	public String getAuditAdv() {
		return auditAdv;
	}

	@Override
	public void setAuditAdv(String auditAdv) {
		this.auditAdv = auditAdv;
	}

	@Override
	@ManyToOne
	@JoinColumn(name = "CHK_PSN_ID")
	public TsUserInfo getFkByChkPsnId() {
		return fkByChkPsnId;
	}

	@Override
	public void setFkByChkPsnId(TsUserInfo fkByChkPsnId) {
		this.fkByChkPsnId = fkByChkPsnId;
	}

	@Transient
	@Override
	public Date getLastSmtDate() {
		return null;
	}

	@Override
	public void setLastSmtDate(Date lastSmtDate) {
	}
	@Transient
	@Override
	public List<TdZwSupportQual> getSupportQualList() {
		return null;
	}

	@Override
	public void setSupportQualList(List<TdZwSupportQual> supportQualList) {

	}

	@Transient
	@Override
	public List<TdZwSupportChk> getSupportChkList() {
		return null;
	}

	@Override
	public void setSupportChkList(List<TdZwSupportChk> supportChkList) {

	}

	@Transient
	@Override
	public List<TdZwSupportFund> getSupportFundList() {
		return null;
	}

	@Override
	public void setSupportFundList(List<TdZwSupportFund> supportFundList) {

	}
	@Transient
	@Override
	public String getOrgTypeName() {
		return null;
	}

	@Override
	public void setOrgTypeName(String orgTypeName) {

	}
	@Transient
	@Override
	public String getQuaTypeName() {
		return null;
	}

	@Override
	public void setQuaTypeName(String quaTypeName) {

	}
	@Transient
	@Override
	public TdZwSupportPlace getSupportPlaces() {
		return null;
	}

	@Override
	public void setSupportPlaces(TdZwSupportPlace supportPlaces) {

	}

	@Transient
	@Override
	public TsSimpleCode getFkByTypeId() {
		return null;
	}

	@Override
	public void setFkByTypeId(TsSimpleCode fkByTypeId) {

	}
	@Transient
	@Override
	public List<TdZwSupportTech> getSupportTechList() {
		return null;
	}
	@Override
	public void setSupportTechList(List<TdZwSupportTech> supportTechList) {

	}
	@Column(name = "MASTER_DATA_TIME")
	@Override
	public Date getMasterDataTime() {
		return masterDataTime;
	}
	@Override
	public void setMasterDataTime(Date masterDataTime) {
		this.masterDataTime=masterDataTime;
	}
	@Column(name = "MODIFY_DATE")
	@Override
	public Date getModifyDate() {
		return modifyDate;
	}
	@Override
	public void setModifyDate(Date modifyDate) {
		this.modifyDate=modifyDate;
	}
	@Column(name = "MODIFY_MANID")
	@Override
	public Integer getModifyManid() {
		return modifyManid;
	}
	@Override
	public void setModifyManid(Integer modifyManid) {
		this.modifyManid=modifyManid;
	}
}