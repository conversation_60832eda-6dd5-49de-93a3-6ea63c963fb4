package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-3-29
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_MOMOGRAPH")
@SequenceGenerator(name = "TdZwSupportMomograph", sequenceName = "TD_ZW_SUPPORT_MOMOGRAPH_SEQ", allocationSize = 1)
public class TdZwSupportMomograph implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TdZwSupportOrgZzsb fkByMainId;
    private Integer year;
    private String momographName;
    private String pressName;
    private String supportFilePath;
    private Date createDate;
    private Integer createManid;
    private Integer modifyManid;
    private Date modifyDate;
    private List<TdZwSupportMomographPsns> momographPsnList;
    /**
     * 所有主编姓名
     */
    private String psnName1;
    /**
     * 主编人员选择用
     */
    private SupportResearchPsnSelVO psnSelVO1;
    /**
     * 所有副主编姓名
     */
    private String psnName2;
    /**
     * 副主编人员选择用
     */
    private SupportResearchPsnSelVO psnSelVO2;
    /**
     * 所有参编姓名
     */
    private String psnName3;
    /**
     * 参编人员选择用
     */
    private SupportResearchPsnSelVO psnSelVO3;

    public TdZwSupportMomograph() {
    }

    public TdZwSupportMomograph(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportMomograph")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwSupportOrgZzsb getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @Column(name = "YEAR")
    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    @Column(name = "MOMOGRAPH_NAME")
    public String getMomographName() {
        return momographName;
    }

    public void setMomographName(String momographName) {
        this.momographName = momographName;
    }

    @Column(name = "PRESS_NAME")
    public String getPressName() {
        return pressName;
    }

    public void setPressName(String pressName) {
        this.pressName = pressName;
    }

    @Column(name = "SUPPORT_FILE_PATH")
    public String getSupportFilePath() {
        return supportFilePath;
    }

    public void setSupportFilePath(String supportFilePath) {
        this.supportFilePath = supportFilePath;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    public List<TdZwSupportMomographPsns> getMomographPsnList() {
        return momographPsnList;
    }

    public void setMomographPsnList(List<TdZwSupportMomographPsns> momographPsnList) {
        this.momographPsnList = momographPsnList;
    }

    @Transient
    public String getPsnName1() {
        return psnName1;
    }

    public void setPsnName1(String psnName1) {
        this.psnName1 = psnName1;
    }

    @Transient
    public SupportResearchPsnSelVO getPsnSelVO1() {
        return psnSelVO1;
    }

    public void setPsnSelVO1(SupportResearchPsnSelVO psnSelVO1) {
        this.psnSelVO1 = psnSelVO1;
    }

    @Transient
    public String getPsnName2() {
        return psnName2;
    }

    public void setPsnName2(String psnName2) {
        this.psnName2 = psnName2;
    }

    @Transient
    public SupportResearchPsnSelVO getPsnSelVO2() {
        return psnSelVO2;
    }

    public void setPsnSelVO2(SupportResearchPsnSelVO psnSelVO2) {
        this.psnSelVO2 = psnSelVO2;
    }

    @Transient
    public String getPsnName3() {
        return psnName3;
    }

    public void setPsnName3(String psnName3) {
        this.psnName3 = psnName3;
    }

    @Transient
    public SupportResearchPsnSelVO getPsnSelVO3() {
        return psnSelVO3;
    }

    public void setPsnSelVO3(SupportResearchPsnSelVO psnSelVO3) {
        this.psnSelVO3 = psnSelVO3;
    }
}