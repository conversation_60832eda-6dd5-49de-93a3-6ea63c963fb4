package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_TJITEMS")
@SequenceGenerator(name = "TdZwTjitemsSeq", sequenceName = "TD_ZW_TJITEMS_SEQ", allocationSize = 1)
public class TdZwTjitemsZzsb implements Serializable,IZwItemsZzsb {
	private static final long serialVersionUID = -5905433897141121614L;

	private Integer rid;
	private TsSimpleCode tsSimpleCode;
	private TdZwTjorginfoZzsb tdZwTjorginfo;
	private Date createDate;
	private Integer createManid;
	private Integer isOutter;

	public TdZwTjitemsZzsb() {
	}

	public TdZwTjitemsZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjitemsSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "ITEM_ID" )
	public TsSimpleCode getTsSimpleCode() {
		return this.tsSimpleCode;
	}

	public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
		this.tsSimpleCode = tsSimpleCode;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwTjorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		this.tdZwTjorginfo = (TdZwTjorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwTjorginfoZzsb getTdZwTjorginfo() {
		return this.tdZwTjorginfo;
	}

	public void setTdZwTjorginfo(TdZwTjorginfoZzsb tdZwTjorginfo) {
		this.tdZwTjorginfo = tdZwTjorginfo;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Column(name = "IS_OUTTER")
	public Integer getIsOutter() {
		return isOutter;
	}

	public void setIsOutter(Integer isOutter) {
		this.isOutter = isOutter;
	}

}