package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.javabean.FileItemBean;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.logic.TdZwPsnTypePO;
import com.chis.modules.heth.zzsb.logic.TdZwPsnTypeRow;
import com.chis.modules.heth.zzsb.service.ZzsbZwIntellReportServiceImpl;
import com.chis.modules.heth.zzsb.service.ZzsbZwPsnPlanServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import org.apache.commons.fileupload.FileItem;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.text.ParseException;
import java.util.*;

public class TdzwPsnInfoZzsbBaseNew {
	protected ZzsbZwPsnPlanServiceImpl  psnPlanServiceImpl = SpringContextHolder.getBean(ZzsbZwPsnPlanServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	protected SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	protected ZzsbZwIntellReportServiceImpl zwIntellReportServiceImpl = SpringContextHolder
			.getBean(ZzsbZwIntellReportServiceImpl.class);
	//资质人员
	protected TdZwPsninfoZzsb psnInfo;
	//资质类别
	private TdZwPsnZzlbZzsb psnZzlb;
	//民族
	private List<TsSimpleCode> nationList;
	//学历
	private List<TsSimpleCode> educationList;
	//学位
	private List<TsSimpleCode> degreeList;
	//职称
	private List<TsSimpleCode> titleList;
	//职务
	private List<TsSimpleCode> zcList;
	//医师资格级别
	private List<TsSimpleCode> docLevList;
	//医师资格类别
	private List<TsSimpleCode> docSpecSelList;
	//医师执业类别
	private List<TsSimpleCode> doctTypeList;
	//资质类别
	private List<TsSimpleCode> qualList;
	private Map<Integer, TsSimpleCode> qualMap;
	//资质范围
	private List<TsSimpleCode> rangeList;
	//1:身份证附件2：医师资格证书附:3：医师执业证书附件4.专业资质附件上传5：毕业证书证书附件6.职称证书附件7：中级以上（含）专业技术任职资格证书附件8：中级以上（含）专业技术任职聘任书附件 9.培训合格证 10.放射卫生检测与评价技术考核合格证
	private String fileIndex;
	//true:需验证资质人员为空的情况false:无需验证资质人员为空的情况
	private boolean validPsnInfoId = true;

	private String titleCentflag = "1";

	//职称级别
	private List<TsSimpleCode> titleLevelList;

    private Integer zwOrgType;
    /**是否显示收件人信息*/
    private boolean ifShowReceiveInfo;
    /**人员属性*/
    private List<TdZwPsnTypeRow> psnTypeRows;
    /**1：检查医生、鉴定医师*/
    private String psnType;
    /**资质申报对应资质是否有数据*/
    private boolean ifHaveZz = false;
    private Integer psnTypeNum;
    /** 修改时对应的机构与资质人员关系表的rid */
    private Integer orgPsnInfoRid;
    /** 身份证号 用于判断身份证号是否改变 若未改变 则不需要更新信息 */
    private String cachIdc;
	/**
	 * 备案机构RID
	 */
    private Integer orgRid;

	/**是否医师资格证书附件 1：必填*/
	private String ifQualAnnex;
	/**是否医师执业证书附件 1：必填*/
	private String ifPraAnnex;
	/**显示护师还是医师 1：护师  2 or null：医师*/
	private String ifNurseOrDoctor;
	/**资质人员的资质信息是否陕西版本*/
	private String ifRequiredAnnex;
	/**陕西版本 职业病诊断资格证书附件 */
	private Boolean ifHoldCent=false;
	/**陕西版本 专业人员任命文件附件*/
	private Boolean ifEmployCent=false;
	/**劳动关系证明附件是否必填*/
	private Boolean ifLaborAnnex=false;

	public TdzwPsnInfoZzsbBaseNew(){
	}
	/**
 	 * <p>方法描述：初始化资质人员信息</p>
 	 * @MethodAuthor qrr,2018年5月15日,initPsnInfo
	 * */
	protected void initPsnInfo(){
		orgPsnInfoRid = null;
        if (null!=psnInfo && psnInfo.getFkByTitleId() != null && psnInfo.getFkByTitleId().getRid() != null) {
            TsSimpleCode tsSimpleCode = psnPlanServiceImpl.find(TsSimpleCode.class,psnInfo.getFkByTitleId().getRid());
            titleCentflag = tsSimpleCode.getExtendS1();
        }

		if (null==psnInfo) {
			psnInfo = new TdZwPsninfoZzsb();
		}
		this.cachIdc = null == this.psnInfo.getIdcCard() ? null : this.psnInfo.getIdcCard().trim();
		initByIdc();
		initSimpleCode();
		//民族
		if(CollectionUtils.isEmpty(nationList)){
			nationList = commService.findallSimpleCodesByTypeId("1002");
		}
		//学历
		if(CollectionUtils.isEmpty(educationList)){
			educationList=commService.findallSimpleCodesByTypeId("1001");
		}
		//学位
		if(CollectionUtils.isEmpty(degreeList)){
			degreeList=commService.findallSimpleCodesByTypeId("2009");
		}
		//职称
		if(CollectionUtils.isEmpty(titleList)){
			titleList=commService.findSimpleCodesByTypeId("2003");
		}
		//职务
		if(CollectionUtils.isEmpty(zcList)){
			zcList = commService.findSimpleCodesByTypeId("2002");
		}
		//医师资格级别
		if(CollectionUtils.isEmpty(docLevList)){
			docLevList=commService.findallSimpleCodesByTypeId("5021");
		}
		//医师资格类别
		if(CollectionUtils.isEmpty(docSpecSelList)){
			docSpecSelList=commService.findallSimpleCodesByTypeId("5023");
			initDocSpec(docSpecSelList);
		}
		if(CollectionUtils.isEmpty(doctTypeList)){
			doctTypeList=commService.findallSimpleCodesByTypeId("5023");
			initDocSpec(doctTypeList);
		}
		
		qualList = new ArrayList<>();
		qualMap = new HashMap<Integer, TsSimpleCode>();
		List<TsSimpleCode> allQualList=commService.findLevelSimpleCodesByTypeId("5025");
		if (null!=allQualList && allQualList.size()>0) {
			for (TsSimpleCode t : allQualList) {
				String codeDesc = t.getCodeDesc();
				boolean match = StringUtils.isNotBlank(codeDesc) && null!=this.zwOrgType && codeDesc.contains(this.zwOrgType.toString());
				if (StringUtils.isBlank(codeDesc)||match) {
					qualList.add(t);
					qualMap.put(t.getRid(), t);
				}
			}
		}

		if(CollectionUtils.isEmpty(titleLevelList)){
			titleLevelList = commService.findallSimpleCodesByTypeId("5062");
		}
        if(0 == this.zwOrgType) {
            this.psnTypeNum = Integer.valueOf(PropertyUtils.getValue("zzsb.tjorginfoPsnTypeNum"));
        } else if(2 == this.zwOrgType) {
            this.psnTypeNum = Integer.valueOf(PropertyUtils.getValue("zzsb.diagorginfoPsnTypeNum"));
        }
		ifRequiredAnnex= PropertyUtils.getValueWithoutException("zzsb.ifRequiredAnnex");
		initPsnTypePOs();
		changePsnType();
		String ifShowReceiveInfo = PropertyUtils.getValue("zzsb.ifShowReceiveInfo");
		this.ifShowReceiveInfo = "1".equals(ifShowReceiveInfo);

		this.judgeIfHaveZz();
	}

	/**
 	 * <p>方法描述：判断资质申报对应资质类别是否有数据</p>
 	 * @MethodAuthor qrr,2021年3月17日,judgeIfHaveZz
	 * */
	private void judgeIfHaveZz() {
		ifHaveZz = false;
        //资质申报对应已选择的资质类别
        List<TdZwPsnZzlbZzsb> zzlbList = psnInfo.getZzlbList();
        if (!CollectionUtils.isEmpty(zzlbList)) {
			for (TdZwPsnZzlbZzsb t : zzlbList) {
				TsSimpleCode fkByQualId = t.getFkByQualId();
				String codeDesc = fkByQualId.getCodeDesc();
				boolean match = StringUtils.isNotBlank(codeDesc) && null!=this.zwOrgType && codeDesc.contains(this.zwOrgType.toString());
				if (StringUtils.isBlank(codeDesc)||match) {
					t.setShow(true);
					ifHaveZz = true;
				}else {
					t.setShow(false);
				}
			}
		}
	}
	/**
 	 * <p>方法描述：初始化人员属性</p>
 	 * @MethodAuthor qrr,2020年10月15日,initPsnTypePOs
	 * */
	private void initPsnTypePOs() {
		Map<Integer, TdZwPsnTypeZzsb> psnTypeMap = new HashMap<>();
		if (null!=psnInfo.getRid()) {
			List<TdZwPsnTypeZzsb> list = this.psnPlanServiceImpl.findTdZwPsnTypeListByMainIdAndZzjgType(this.psnInfo.getRid(), this.zwOrgType);
			if (!CollectionUtils.isEmpty(list)) {
				for (TdZwPsnTypeZzsb t : list) {
					psnTypeMap.put(t.getFkByPsnType().getRid(), t);
				}
			}
		}
		List<TsSimpleCode> psnTypeList = commService.findSimpleCodesByTypeIdAndCodeDesc("5308", this.zwOrgType.toString());
		psnTypeRows = new ArrayList<>();
		if (!CollectionUtils.isEmpty(psnTypeList)) {
			int i = 0;
            int num;
			List<TdZwPsnTypePO> typePOs = new ArrayList<>();
			for (TsSimpleCode t : psnTypeList) {
                if(null != this.psnTypeNum) {
                    num = i % this.psnTypeNum;
                } else {
                    num = i % 4;
                }
                if(num==0) {
                    TdZwPsnTypeRow row = new TdZwPsnTypeRow();
                    typePOs = new ArrayList<>();
                    row.setTypePOs(typePOs);
                    psnTypeRows.add(row);
                }

				TdZwPsnTypePO psnTypePO = new TdZwPsnTypePO();
				psnTypePO.setFkByPsnType(t);
                psnTypePO.setZzjgType(this.zwOrgType);
				if (null!=psnTypeMap.get(t.getRid())) {
					psnTypePO.setIfSelected(true);
					TdZwPsnTypeZzsb type = psnTypeMap.get(t.getRid());
					psnTypePO.setOtherType(type.getOtherPsnType());
				}
				if ("1".equals(psnInfo.getIfCheckExpert())) {
					psnTypePO.setIfJdExpert(true);
				}
				typePOs.add(psnTypePO);
				//页面显示医师还是护师
				if (zwOrgType != null && (zwOrgType == 0 || zwOrgType == 2)) {
					if (StringUtils.isNotBlank(t.getExtendS6())) {
						if (StringUtils.isBlank(ifNurseOrDoctor)) {
							ifNurseOrDoctor = t.getExtendS6();
						} else if (!ifNurseOrDoctor.equals(t.getExtendS6())) {
							ifNurseOrDoctor = null;
						}
					}
				}
				i++;
			}
		}
	}
	/**
 	 * <p>方法描述：码表层级关系处理</p>
 	 * @MethodAuthor qrr,2018年5月16日,initDocSpec
	 * */
	public void initDocSpec(List<TsSimpleCode> list){
    	if(null != list && list.size() > 0) {
    		for(TsSimpleCode t : list) {
    			if(StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
    				t.setLevelIndex("1");
    			}
    		}
    	}
    }
	/**
 	 * <p>方法描述：初始化发证资质信息</p>
 	 * @MethodAuthor qrr,2018年5月15日,initPsnZzlb
	 * */
	protected void initPsnZzlb(){
		psnZzlb = new TdZwPsnZzlbZzsb();
		psnZzlb.setFkByQualId(new TsSimpleCode());
		rangeList = new ArrayList<>();
	}
	/**
 	 * <p>方法描述：根据身份证号刷新性别</p>
 	 * @MethodAuthor qrr,2018年5月16日,findFlowByIdc
 	 * 
 	 * <p>修订内容：初始化出生日期</p>
 	 * @MethodReviser qrr,2018年5月22日,findFlowByIdc
	 * */
	public void initByIdc(){
		if (StringUtils.isNotBlank(psnInfo.getIdcCard())) {
			String checkIDC = IdcUtils.checkIDC(psnInfo.getIdcCard());
			if (StringUtils.isBlank(checkIDC)) {
				if (StringUtils.isBlank(psnInfo.getSex())) {
					calSex();
				}
//				if (StringUtils.isBlank(psnInfo.getBirthday())) {
//					calBrithDay();
//				}
			}
			
		}
	}
	/**
 	 * <p>方法描述：根据身份证号刷新性别</p>
 	 * @MethodAuthor qrr,2018年5月16日,findFlowByIdc
 	 * 
 	 * <p>修订内容：初始化出生日期</p>
 	 * @MethodReviser qrr,2018年5月22日,findFlowByIdc
	 * */
	public void findFlowByIdc(){
		/***
		 * 当人员身份证号为空或者当前身份证号与缓存的身份证号不一致时候
		 * 清空资质人员rid以及资质列表
		 * 因检索成功后 如果检索到人员信息 会重新赋值
		 * 如果不清空 就会出现 检索成功后 删除身份证号 或者检索成功后 输入错误的身份证号或当前机构已经存在的人员身份证 还可以添加资质
		 */
		if(StringUtils.isBlank(psnInfo.getIdcCard()) ||
				!this.psnInfo.getIdcCard().trim().equals(this.cachIdc)){
			//清空资质人员rid以及资质列表
			this.psnInfo.setRid(null);
			if(!CollectionUtils.isEmpty(this.psnInfo.getZzlbList())){
				this.psnInfo.setZzlbList(new ArrayList<TdZwPsnZzlbZzsb>());
			}
		}
		if (StringUtils.isNotBlank(psnInfo.getIdcCard())) {
			psnInfo.setIdcCard(psnInfo.getIdcCard().trim());
			String checkIDC = IdcUtils.checkIDC(psnInfo.getIdcCard());
			if (StringUtils.isBlank(checkIDC)) {
				int count = findIdcCountCurrentOrgExist();
				if (count>0) {
					this.cachIdc = null == this.psnInfo.getIdcCard() ? null : this.psnInfo.getIdcCard().trim();
					JsfUtil.addErrorMessage("该人员已存在！");
					return;
				}
				calSex();
				try {
					calBrithDay();
					if(!this.psnInfo.getIdcCard().trim().equals(this.cachIdc)){
						//不考虑TdZwPsntjMainZzsb的相关信息
						TdZwPsninfoZzsb psninfo = findCurrentUnitPsnInfo();
						if(null != psninfo){
							this.psnInfo = psninfo;
							JsfUtil.addSuccessMessage("人员检索成功！");
						}
						this.initPsnInfo();
					}
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}else{
				this.cachIdc = null == this.psnInfo.getIdcCard() ? null : this.psnInfo.getIdcCard().trim();
				JsfUtil.addErrorMessage(checkIDC);
			}
		}else{
			this.cachIdc = null == this.psnInfo.getIdcCard() ? null : this.psnInfo.getIdcCard().trim();
		}
	}

	/**
	 * @Description: 获取当前单位下的身份证对应的资质人员信息
	 *
	 * @MethodAuthor pw,2022年02月16日
	 */
	public TdZwPsninfoZzsb findCurrentUnitPsnInfo(){
		return this.psnPlanServiceImpl.findCurrentUnitPsnInfo(this.psnInfo.getIdcCard());
	}

	/**
	 * @Description: 获取 当前机构中 存在的该身份证的人员关系数
	 *
	 * @MethodAuthor pw,2022年02月14日
	 */
	private int findIdcCountCurrentOrgExist() {
		return this.psnPlanServiceImpl.findIdcCountCurrentOrgExistNew(this.psnInfo.getIdcCard(),
				this.zwOrgType, this.orgPsnInfoRid, this.orgRid);
	}

    public void findTitleCentInfo(){
        TsSimpleCode tsSimpleCode = null;
        if(psnInfo.getFkByTitleId().getRid() != null){
            tsSimpleCode = psnPlanServiceImpl.find(TsSimpleCode.class,psnInfo.getFkByTitleId().getRid());
			psnInfo.setFkByTitleId(tsSimpleCode);
        }
       if(tsSimpleCode != null){
           titleCentflag = tsSimpleCode.getExtendS1();
       }else{
           titleCentflag = "1";
       }
    }
	/**
 	 * <p>方法描述：计算性别</p>
 	 * @MethodAuthor qrr,2018年5月22日,calSex
	 * */
	private void calSex(){
		if (Integer.valueOf(psnInfo.getIdcCard().substring(
				psnInfo.getIdcCard().length() - 2,
				psnInfo.getIdcCard().length() - 1)) % 2 == 0) {
			psnInfo.setSex("女");
		} else {
			psnInfo.setSex("男");
		}//性别	
	}
	/**
 	 * <p>方法描述：计算出生日期</p>
 	 * @MethodAuthor qrr,2018年5月22日,calSex
	 * */
	private void calBrithDay() throws ParseException{
		Date calBirthday = IdcUtils.calBirthday(psnInfo.getIdcCard());
		psnInfo.setBirthday(DateUtils.formatDate(calBirthday,"yyyy-MM-dd"));
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年5月16日,beforeFileUpload
	 * */
	 public void beforeFileUpload(){
		 if (validPsnInfoId) {
			 if (null==psnInfo.getRid()) {
				 JsfUtil.addErrorMessage("请先注册资质人员！");
				 return;
			 }
		}
		 RequestContext.getCurrentInstance().execute("PF('FileDialog').show();");
		 RequestContext.getCurrentInstance().update("mainForm:fileDialog");
		 RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:fileDialog");
	 }
	/**
     * <p>方法描述：上传附件</p>
     *
     * @MethodAuthor qrr, 2018年5月16日, fileUpload
     */
    public void fileUpload(FileUploadEvent event) {
        UploadedFile file = event.getFile();
        FileItem fileItem = new FileItemBean(file);
        String contentType = file.getContentType().toLowerCase();
		try {
			String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, file.getFileName(), "3");
			if (StringUtils.isNotBlank(errorMsg)) {
				JsfUtil.addErrorMessage(errorMsg);
				if (!validPsnInfoId) {
					//无需验证资质人员是否为空，则代表资质申报添加资质人员的界面
					RequestContext.getCurrentInstance().update("mainForm:fileDialog");
				} else {
					RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:fileDialog");
				}
				return;
			}
		} catch (IOException e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage(PropertyUtils.getValue("上传失败"));
			return;
		}
		String uuid = UUID.randomUUID().toString().replaceAll("-", "");
		Integer dotIndex = file.getFileName().lastIndexOf(".");
		String filePath = "10/" + uuid
				+ file.getFileName().substring(dotIndex);
		try {
			FileUtils.uploadFile(filePath, fileItem);
		} catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage(PropertyUtils.getValue("上传失败"));
            return;
        }
        if ("1".equals(this.fileIndex)) {
			//身份证附件
        	psnInfo.setIdcPath(filePath);
        	psnInfo.setIdcAnnexName(file.getFileName());
		}else if ("2".equals(this.fileIndex)) {
			//医师资格证书附件
			psnInfo.setDoctQualPath(filePath);
        	psnInfo.setQualAnnexName(file.getFileName());
		}else if ("3".equals(this.fileIndex)) {
			//医师执业证书附件
			psnInfo.setDoctPraPath(filePath);
        	psnInfo.setPraAnnxeName(file.getFileName());
		}else if ("4".equals(this.fileIndex)) {
			//资质人员附件
			psnZzlb.setAnnexPath(filePath);
			psnZzlb.setAnnexName(file.getFileName());
		}else if ("5".equals(this.fileIndex)) {
			//毕业证书附件
			psnInfo.setGradPath(filePath);
			psnInfo.setGradAnnxeName(file.getFileName());
		}else if ("6".equals(this.fileIndex)) {
			//职称证书附件
			psnInfo.setTitleCentPath(filePath);
			psnInfo.setTitleCentAnnexName(file.getFileName());
		}else if ("7".equals(this.fileIndex)) {
			//中级以上（含）专业技术任职资格证书附件
			psnInfo.setHoldCentPath(filePath);
			psnInfo.setHoldCentAnnexName(file.getFileName());
		}else if ("8".equals(this.fileIndex)) {
			//中级以上（含）专业技术任职聘任书附件
			psnInfo.setEmployCentPath(filePath);
			psnInfo.setEmployCentAnnexName(file.getFileName());
		}else if ("9".equals(this.fileIndex)) {
            //培训合格证附件
            psnInfo.setTrainQualAnnexPath(filePath);
            psnInfo.setTrainQualAnnexName(file.getFileName());
        }else if ("10".equals(this.fileIndex)) {
            //放射卫生检测与评价技术考核合格证附件
            psnInfo.setFsQualAnnexPath(filePath);
            psnInfo.setFsQualAnnexName(file.getFileName());
        }else if("11".equals(this.fileIndex)){
			//劳动关系证明
			psnInfo.setLaborRelAnnexPath(filePath);
			psnInfo.setLaborRelAnnexName(file.getFileName());
		}
        RequestContext.getCurrentInstance().execute("PF('FileDialog').hide();");
        if (!validPsnInfoId) {
        	//无需验证资质人员是否为空，则代表资质申报添加资质人员的界面
        	if (!"4".equals(this.fileIndex)) {
        		//添加资质上传附件更新
        		RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:codeEditGrid");
			}else {
				RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:zzlbGrid");
				RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:qualificationDataTable");
			}
        	//RequestContext.getCurrentInstance().update("mainForm:fileDialog");
		}else {
			if (!"4".equals(this.fileIndex)) {
				RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:codeEditGrid");
			}else {
				RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:zzlbGrid");
				RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:qualificationDataTable");
			}
			RequestContext.getCurrentInstance().update("tabView:editForm:orgTjApplyTabView:fileDialog");
		}
        JsfUtil.addSuccessMessage("上传成功！");
    }
    /**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年5月16日,initSimpleCode
     * */
	private void initSimpleCode() {
		if (null == psnInfo.getFkByDegreeId()) {
			psnInfo.setFkByDegreeId(new TsSimpleCode());
		}
		if (null == psnInfo.getFkByDoctLevelId()) {
			psnInfo.setFkByDoctLevelId(new TsSimpleCode());
		}
		if (null == psnInfo.getFkByDoctSpecId()) {
			psnInfo.setFkByDoctSpecId(new TsSimpleCode());
		}
		if (null == psnInfo.getFkByDoctTypeId()) {
			psnInfo.setFkByDoctTypeId(new TsSimpleCode());
		}
		if (null == psnInfo.getFkByEducationId()) {
			psnInfo.setFkByEducationId(new TsSimpleCode());
		}
		if (null == psnInfo.getFkByNation()) {
			psnInfo.setFkByNation(new TsSimpleCode());
		}
		if (null == psnInfo.getFkByTitleId()) {
			psnInfo.setFkByTitleId(new TsSimpleCode());
		}
		if (null == psnInfo.getFkByTitleLevelId()) {
			psnInfo.setFkByTitleLevelId(new TsSimpleCode());
		}
	}
    /**
 	 * <p>方法描述：删除附件</p>
 	 * @MethodAuthor qrr,2018年5月16日,delAnnex
     * */
    public void  delAnnex(){
    	
    	if ("1".equals(this.fileIndex)) {
    		psnInfo.setIdcPath(null);
    		psnInfo.setIdcAnnexName(null);
		}else if ("2".equals(this.fileIndex)) {
			psnInfo.setDoctQualPath(null);
        	psnInfo.setQualAnnexName(null);
		}else if ("3".equals(this.fileIndex)) {
			psnInfo.setDoctPraPath(null);
        	psnInfo.setPraAnnxeName(null);
		}else if ("4".equals(this.fileIndex)) {
			psnZzlb.setAnnexPath(null);
			psnZzlb.setAnnexName(null);
		}else if ("5".equals(this.fileIndex)) {
			psnInfo.setGradPath(null);
        	psnInfo.setGradAnnxeName(null);
		}else if ("6".equals(this.fileIndex)) {
			psnInfo.setTitleCentPath(null);
        	psnInfo.setTitleCentAnnexName(null);
		}else if ("7".equals(this.fileIndex)) {
			psnInfo.setHoldCentPath(null);
        	psnInfo.setHoldCentAnnexName(null);
		}else if ("8".equals(this.fileIndex)) {
			psnInfo.setEmployCentPath(null);
        	psnInfo.setEmployCentAnnexName(null);
		}else if ("9".equals(this.fileIndex)) {
            psnInfo.setTrainQualAnnexName(null);
            psnInfo.setTrainQualAnnexPath(null);
        }else if ("10".equals(this.fileIndex)) {
            psnInfo.setFsQualAnnexName(null);
            psnInfo.setFsQualAnnexPath(null);
        }else if("11".equals(this.fileIndex)){
			psnInfo.setLaborRelAnnexName(null);
			psnInfo.setLaborRelAnnexPath(null);
		}
    	if (null==psnInfo||null==psnInfo.getRid()) {
			return;
		}
    	if (null==psnZzlb||null==psnZzlb.getRid()) {
			return;
		}
    	try {
    		if ("4".equals(fileIndex)) {
				toSaveZzlb();
			}
    		JsfUtil.addSuccessMessage("删除成功！");
		} catch (Exception e) {
			JsfUtil.addErrorMessage("删除失败！");
			e.printStackTrace();
		}
    }
	/**
 	 * <p>方法描述：保存</p>
 	 * @MethodAuthor qrr,2018年5月16日,saveAction
	 * */
	public void saveAction(){
		if (validPsnInfoId) {
			if (null==psnInfo||null==psnInfo.getRid()) {
				JsfUtil.addErrorMessage("请先注册资质人员！");
				return;
			}
		}
		if (!beforeSave()) {
			return;
		}
		//toSave();
		JsfUtil.addSuccessMessage("保存成功！");
	}
	/**
 	 * <p>方法描述：保存资质人员信息</p>
 	 * @MethodAuthor qrr,2018年6月4日,toSave
	 * */
	protected void toSave(){
		preSave();
        psnPlanServiceImpl.saveOrUpdatePsninfo(psnInfo,this.psnTypeRows, this.zwOrgType);
        afterSave();
	}

	/**
	 * @Description: 执行保存前执行的方法
	 *
	 * @MethodAuthor pw,2022年02月15日
	 */
	public void preSave(){
		boolean ifJdDoc = false;
		boolean ifJdExpert = false;
		if (!CollectionUtils.isEmpty(psnTypeRows)) {
			for (TdZwPsnTypeRow row : psnTypeRows) {
				List<TdZwPsnTypePO> typePOs = row.getTypePOs();
				for (TdZwPsnTypePO t : typePOs) {
					if (null != t.getFkByPsnType().getExtendS2()
							&& 1 == t.getFkByPsnType().getExtendS2()) {
						if (t.isIfSelected()) {
							ifJdDoc = true;
						}
						if (t.isIfJdExpert()) {//鉴定专家
							ifJdExpert = true;
						}
						break;
					}
				}
			}
		}
		if (ifJdDoc) {//勾选鉴定医师
			psnInfo.setIfCheckExpert(ifJdExpert?"1":"0");
		}else {
			psnInfo.setIfCheckExpert(null);
		}
		// 处理 技术人员培训管理中 集体报名 完善人员信息后 报名人员职称级别未更新异常问题(20210204)
		if(null != psnInfo.getFkByTitleLevelId() && null != psnInfo.getFkByTitleLevelId().getRid()
				&& !CollectionUtils.isEmpty(titleLevelList)){
			for(TsSimpleCode titleLevel : titleLevelList){
				if(titleLevel.getRid().intValue() == psnInfo.getFkByTitleLevelId().getRid()){
					psnInfo.setFkByTitleLevelId(titleLevel);
					break;
				}
			}
		}
        resetPsnInfoRid();
	}

	/**
	 * @Description: 重置资质人员rid
	 *
	 * @MethodAuthor pw,2022年02月17日
	 */
	public void resetPsnInfoRid(){
		TdZwPsninfoZzsb psninfoZzsb = findCurrentUnitPsnInfo();
		Integer psnRid = null == psninfoZzsb ? null : psninfoZzsb.getRid();
		//避免无用的数据导致异常 这里清空
		if(null == psnRid){
			this.psnInfo.setTjMainList(new ArrayList<TdZwPsntjMainZzsb>());
		}
		this.psnInfo.setRid(psnRid);
	}

	/**
	 * @Description: 执行保存后执行的方法
	 *
	 * @MethodAuthor pw,2022年02月15日
	 */
	public void afterSave(){
		initSimpleCode();
	}
	/**
 	 * <p>方法描述：保存之前验证</p>
 	 * @MethodAuthor qrr,2018年5月16日,beforeSave
 	 * 
 	 * <p>修订内容：新增收件人信息验证</p>
 	 * @MethodReviser qrr,2018年6月11日,beforeSave
 	 * 
 	 * <p>修订内容：去除在岗状态的判断</p>
 	 * @MethodReviser qrr,2018年6月20日,beforeSave
	 * */
	protected boolean beforeSave(){
		boolean flag = true;
		//姓名
		if (StringUtils.isBlank(psnInfo.getEmpName())) {
			JsfUtil.addErrorMessage("姓名不能为空！");
			flag = false;
		}
		//身份证号
		if (StringUtils.isBlank(psnInfo.getIdcCard())) {
			JsfUtil.addErrorMessage("身份证号不能为空！");
			flag = false;
		}else {
			String checkIDC = IdcUtils.checkIDC(psnInfo.getIdcCard());
			if (StringUtils.isBlank(checkIDC)) {
				int count = findIdcCountCurrentOrgExist();
				if (count>0) {
					JsfUtil.addErrorMessage("该人员已存在！");
					flag = false;
				}
			}else {
				JsfUtil.addErrorMessage(checkIDC);
				flag = false;
			}
		}
		//性别
		if (StringUtils.isBlank(psnInfo.getSex())) {
			JsfUtil.addErrorMessage("请选择性别！");
			flag = false;
		}
        //民族
        /*if (null==psnInfo.getFkByNation().getRid()) {
            JsfUtil.addErrorMessage("民族不能为空！");
            flag = false;
        }*/
		//手机号码
		if (StringUtils.isBlank(psnInfo.getMobileNo())) {
			JsfUtil.addErrorMessage("手机号码不能为空！");
			flag = false;
		}else {
			if (!StringUtils.vertyMobilePhone(psnInfo.getMobileNo())) {
				JsfUtil.addErrorMessage("手机号码格式不正确！");
				flag = false;
			}
			
		}
		//电子邮箱
		if (StringUtils.isNotBlank(psnInfo.getEmail())) {
			if (!StringUtils.checkEmail(psnInfo.getEmail())) {
				JsfUtil.addErrorMessage("电子邮箱格式不正确！");
				flag = false;
			}
		}
		//固定电话
		if (StringUtils.isNotBlank(psnInfo.getTelPhone())) {
			if (!StringUtils.vertyFixedPhone(psnInfo.getTelPhone())) {
				JsfUtil.addErrorMessage("固定电话格式不正确！");
				flag = false;
			}
		}
		//邮政编码
		if (StringUtils.isNotBlank(psnInfo.getPostcode())) {
			if (!StringUtils.vertyPost(psnInfo.getPostcode())) {
				JsfUtil.addErrorMessage("邮政编码格式不正确！");
				flag = false;
			}
		}
		//通讯地址
		if (StringUtils.isBlank(psnInfo.getLinkAddr())) {
			JsfUtil.addErrorMessage("通讯地址不能为空！");
			flag = false;
		}

        //学历
        if (null==psnInfo.getFkByEducationId().getRid()) {
            JsfUtil.addErrorMessage("学历不能为空！");
            flag = false;
        }

		//职称
		if (null==psnInfo.getFkByTitleId().getRid()) {
			JsfUtil.addErrorMessage("请选择职称！");
			flag = false;
		}

        //职称证书附件
        if (StringUtils.isBlank(psnInfo.getTitleCentPath()) && (titleCentflag == null || !titleCentflag.equals("1"))) {
            JsfUtil.addErrorMessage("职称证书附件不能为空！");
            flag = false;
        }

        //毕业证书附件
        /*if (StringUtils.isBlank(psnInfo.getGradPath())) {
            JsfUtil.addErrorMessage("毕业证书附件不能为空！");
            flag = false;
        }*/
        
		//在职状态
//		if (null==psnInfo.getOnDuty()) {
//			JsfUtil.addErrorMessage("请选择在职状态！");
//			flag = false;
//		}
		//人员属性
        boolean psnTypeNull = true;//默认人员属性为空
		if (!CollectionUtils.isEmpty(psnTypeRows)) {
			for (TdZwPsnTypeRow row : psnTypeRows) {
				List<TdZwPsnTypePO> typePOs = row.getTypePOs();
				for (TdZwPsnTypePO t : typePOs) {
					if (t.isIfSelected()) {
						psnTypeNull = false;
					}
					if ("4".equals(t.getFkByPsnType().getExtendS3())) {
						if (t.isIfSelected() && StringUtils.isBlank(t.getOtherType())) {
							JsfUtil.addErrorMessage("人员属性："+t.getFkByPsnType().getCodeName()+"不能为空！");
			    			flag = false;
						}
					}
				}
			}
		}
		if (psnTypeNull) {
			JsfUtil.addErrorMessage("请选择人员属性！");
			flag = false;
		}
		String name="医师";
		if(zwOrgType!=null&&(this.zwOrgType==0||this.zwOrgType==2)){
			if("1".equals(ifNurseOrDoctor)){
				name="护师";
			}
		}
		if(zwOrgType!=null&&(this.zwOrgType==0||this.zwOrgType==2)){
			if(StringUtils.isNotBlank(this.ifQualAnnex)&&"1".equals(this.ifQualAnnex)&&StringUtils.isBlank(psnInfo.getDoctQualPath())){
				JsfUtil.addErrorMessage(name+"资格证书附件不能为空！");
				flag = false;
			}
			if(StringUtils.isNotBlank(this.ifQualAnnex)&&"1".equals(this.ifPraAnnex)&&StringUtils.isBlank(psnInfo.getDoctPraPath())){
				JsfUtil.addErrorMessage(name+"执业证书附件不能为空！");
				flag = false;
			}
		}

		if ("1".equals(psnType)) {

			if (StringUtils.isBlank(psnInfo.getDoctCertNo())) {
				JsfUtil.addErrorMessage(name+"资格证编号不能为空！");
				flag = false;
			}
			if (null==psnInfo.getDoctCertDate()) {
				JsfUtil.addErrorMessage("请选择"+name+"资格证发证日期！");
				flag = false;
			}
			if (null==psnInfo.getFkByDoctSpecId().getRid()) {
				JsfUtil.addErrorMessage("请选择"+name+"资格类别！");
				flag = false;
			}
			if (null==psnInfo.getFkByDoctTypeId().getRid()) {
				JsfUtil.addErrorMessage("请选择"+name+"执业类别！");
				flag = false;
			}
			if (StringUtils.isBlank(psnInfo.getDoctPraPlace())) {
				JsfUtil.addErrorMessage("主执业地点不能为空！");
				flag = false;
			}
		}
		//医师执业证发证日期
		if (null!=psnInfo.getDoctPraDate() && null!=psnInfo.getDoctCertDate()) {
			if (psnInfo.getDoctCertDate().after(psnInfo.getDoctPraDate())) {
				JsfUtil.addErrorMessage(name+"执业证发证日期（"+DateUtils.formatDate(
						psnInfo.getDoctPraDate(), "yyyy-MM-dd")+"）应大于等于"+name+"资格证发证日期（"+DateUtils.formatDate(
								psnInfo.getDoctCertDate(), "yyyy-MM-dd")+"）！");
				flag = false;
			}
		}
		//陕西版本必填校验
		if(ifHoldCent){
			if(StringUtils.isBlank(psnInfo.getHoldCentPath())){
				JsfUtil.addErrorMessage("职业病诊断资格证书附件不能为空！");
				flag = false;
			}
		}

		if(ifEmployCent){
			if(StringUtils.isBlank(psnInfo.getEmployCentPath())){
				JsfUtil.addErrorMessage("专业人员任命文件附件不能为空！");
				flag = false;
			}
		}
		if(ifLaborAnnex){
			if(StringUtils.isBlank(psnInfo.getLaborRelAnnexPath())){
				JsfUtil.addErrorMessage("劳动关系证明不能为空！");
				flag = false;
			}
		}
		if (ifShowReceiveInfo) {
			//收件人信息
			if (StringUtils.isBlank(psnInfo.getReceiveEmpName())) {
				JsfUtil.addErrorMessage("收件人姓名不能为空！");
				flag = false;
			}
			if (StringUtils.isBlank(psnInfo.getReceiveMobileNo())) {
				JsfUtil.addErrorMessage("收件人联系电话不能为空！");
				flag = false;
			}else {
				if (!StringUtils.vertyPhone(psnInfo.getReceiveMobileNo())) {
					JsfUtil.addErrorMessage("收件人联系电话格式不正确！");
					flag = false;
				}
			}
			if (StringUtils.isNotBlank(psnInfo.getReceivePostcode())) {
				if (!StringUtils.vertyPost(psnInfo.getReceivePostcode())) {
					JsfUtil.addErrorMessage("收件人邮编格式不正确！");
					flag = false;
				}
			}
			if (StringUtils.isBlank(psnInfo.getReceiveLinkAddr())) {
				JsfUtil.addErrorMessage("收件人收件地址不能为空！");
				flag = false;
			}
		}
		return flag;
	}
	/**
 	 * <p>方法描述：添加资质</p>
 	 * @MethodAuthor qrr,2018年5月15日,addZzlbAction
	 * */
	public void addZzlbAction(){
		if(null == this.psnInfo.getRid()){
			JsfUtil.addErrorMessage("请先保存人员基本信息！");
			return;
		}
		initPsnZzlb();
	}
	/**
 	 * <p>方法描述：修改资质</p>
 	 * @MethodAuthor qrr,2018年5月15日,zzlbEditAction
	 * */
	public void zzlbEditAction(){
		psnZzlb = psnPlanServiceImpl.findTdZwPsnZzlb(psnZzlb.getRid());
		if (null==psnZzlb) {
			psnZzlb = new TdZwPsnZzlbZzsb();
		}
		if (null==psnZzlb.getFkByQualId()) {
			psnZzlb.setFkByQualId(new TsSimpleCode());
		}
		onQualChange();
		if(null != psnZzlb.getRangeList() && psnZzlb.getRangeList().size()>0){
			String[] str=new String[psnZzlb.getRangeList().size()];
			for(int i=0;i<psnZzlb.getRangeList().size();i++){
				TdZwZzlbRangeZzsb range=psnZzlb.getRangeList().get(i);
				str[i]=range.getFkByZzRangeId().getRid().toString();
			}
			psnZzlb.setSelectRanges(str);
		}else {
			psnZzlb.setSelectRanges(new String[]{});
		}
	}
	private boolean veryPsnZzlb(){
		boolean flag = true;
		if(null == psnZzlb.getFkByQualId().getRid()){
			JsfUtil.addErrorMessage("请选择资质类别！");
			flag = false;
		}
		String centName = "证书";
		String centDateName = "发证";
		TsSimpleCode simpleCode = qualMap.get(psnZzlb.getFkByQualId().getRid());
		if(null!=simpleCode){
			Integer extendS2 = simpleCode.getExtendS2();
			if (null!=extendS2 && 8 == extendS2.intValue()) {
				centName = "发文";
			}
			if (null!=extendS2 && 8 == extendS2.intValue()) {
				centDateName = "发文";
			}
		}
		if(StringUtils.isBlank(psnZzlb.getCentNo())){
			JsfUtil.addErrorMessage(centName+"编号不能为空！");
			flag = false;
		}
		if(null == psnZzlb.getSendDate()){
			JsfUtil.addErrorMessage(centDateName+"日期不能为空！");
			flag = false;
		}
		if(null != psnZzlb.getValidDate() && null != psnZzlb.getSendDate() &&
				psnZzlb.getSendDate().after(psnZzlb.getValidDate())){
			JsfUtil.addErrorMessage("失效日期（"+DateUtils.formatDate(
					psnZzlb.getValidDate(), "yyyy-MM-dd")+"）应大于等于"+centDateName+"日期（"+DateUtils.formatDate(
							psnZzlb.getSendDate(), "yyyy-MM-dd")+"）！");
			flag = false;
		}
		if(null != psnZzlb.getAgeinSendDate() && null != psnZzlb.getSendDate()
				&& psnZzlb.getSendDate().after(psnZzlb.getAgeinSendDate())){
			JsfUtil.addErrorMessage("下次培训日期（"+DateUtils.formatDate(
					psnZzlb.getAgeinSendDate(), "yyyy-MM-dd")+"）应大于等于"+centDateName+"日期（"+DateUtils.formatDate(
							psnZzlb.getSendDate(), "yyyy-MM-dd")+"）！");
			flag = false;
		}
		if (null!=psnZzlb.getFkByQualId().getRid()) {
			if (StringUtils.isNotBlank(simpleCode.getExtendS5())) {
				if (null==psnZzlb.getSelectRanges()||psnZzlb.getSelectRanges().length==0) {
					JsfUtil.addErrorMessage("资质范围不能为空！");
					flag = false;
				}
			}
		}
		if (StringUtils.isBlank(psnZzlb.getAnnexPath())) {
			JsfUtil.addErrorMessage("请上传附件！");
			flag = false;
		}
		return flag;
	}
	/**
 	 * <p>方法描述：保存已发证的资质信息</p>
 	 * @MethodAuthor qrr,2018年5月15日,saveZzlbAction 
	 * */
	public void saveZzlbAction(){
		if (validPsnInfoId) {
			if (null==psnInfo.getRid()) {
				if (null==psnInfo||null==psnInfo.getRid()) {
					JsfUtil.addErrorMessage("请先注册资质人员！");
					return;
				}
			}
		}
		if (!veryPsnZzlb()) {
			return;
		}
		toSaveZzlb();
		this.judgeIfHaveZz();
		RequestContext.getCurrentInstance().execute("PF('ZzlbInfoDialog').hide();");
		JsfUtil.addSuccessMessage("保存成功！");
	}
	/**
 	 * <p>方法描述：保存人员资质信息</p>
 	 * @MethodAuthor qrr,2018年6月4日,toSaveZzlb
	 * */
	private void toSaveZzlb(){
		if (null!=qualMap.get(psnZzlb.getFkByQualId().getRid())) {
			TsSimpleCode simpleCode = qualMap.get(psnZzlb.getFkByQualId().getRid());
			TsSimpleCode from = new TsSimpleCode();
			try {
				ObjectCopyUtil.copyProperties(simpleCode, from);
			} catch (Exception e) {
				e.printStackTrace();
			}
			psnZzlb.setFkByQualId(from);
		}
		if(null==psnZzlb.getRid()){
			if(null == psnInfo.getZzlbList() || psnInfo.getZzlbList().size()==0){
				psnInfo.setZzlbList(new ArrayList<TdZwPsnZzlbZzsb>());
			}
			psnInfo.getZzlbList().add(psnZzlb);
		}else {
			if (null!=psnInfo.getZzlbList() && psnInfo.getZzlbList().size()>0) {
				for (TdZwPsnZzlbZzsb t : psnInfo.getZzlbList()) {
					if (t.getRid().equals(psnZzlb.getRid())) {
						try {
							ObjectCopyUtil.copyProperties(psnZzlb, t);
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
				}
			}
		}
		psnZzlb.setFkByPsnId(psnInfo);
		psnPlanServiceImpl.saveOrUpdatePsnZzlbAndRemoveList(psnZzlb, null);
		if (null==psnZzlb.getFkByQualId()) {
			psnZzlb.setFkByQualId(new TsSimpleCode());
		}
	}
	/**
 	 * <p>方法描述：删除资质</p>
 	 * @MethodAuthor qrr,2018年5月15日,zzlbDelAction
	 * */
	public void zzlbDelAction(){
		if (null!=psnInfo.getZzlbList() && psnInfo.getZzlbList().size()>0) {
			psnInfo.getZzlbList().remove(psnZzlb);
		}
		this.judgeIfHaveZz();
		try {
			psnPlanServiceImpl.delPsnZzlb(psnZzlb);
			JsfUtil.addSuccessMessage("删除成功！");
		} catch (Exception e) {
			JsfUtil.addErrorMessage("删除失败！");
			e.printStackTrace();
		}
		
	}
	/**
 	 * <p>方法描述：修改资质类别</p>
 	 * @MethodAuthor qrr,2018年5月15日,onQualChange
	 * */
	public void onQualChange(){
		rangeList = new ArrayList<>();
		if (null!=qualMap.get(psnZzlb.getFkByQualId().getRid())) {
			TsSimpleCode simpleCode = qualMap.get(psnZzlb.getFkByQualId().getRid());
			String codeTypeName = simpleCode.getExtendS5();
			if (StringUtils.isNotBlank(codeTypeName)) {
				rangeList=zwIntellReportServiceImpl.findEnableUpCodeList(codeTypeName);
			}
		}
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年5月16日,selectDoctTypeAction
	 * */
	public void selectDoctTypeAction(String type){
		Map<String, Object> options = new HashMap<String, Object>();
		options.put("modal", true);
		options.put("draggable", true);
		options.put("resizable", false);
		options.put("contentWidth", 625);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>();
		if ("1".equals(type)) {
			paramList.add("医师资格类别");
		}else if ("2".equals(type)) {
			paramList.add("医师执业类别");
		}
		paramMap.put("titleName",paramList);
		paramList = new ArrayList<String>();
		paramList.add("5023");
		paramMap.put("typeNo",paramList);
		paramList = new ArrayList<String>();
		if("1".equals(type)) {
            paramList.add("true");
        } else if("2".equals(type)) {
            paramList.add("false");
        }
		paramMap.put("ifAllSelect",paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/system/codeRadioSelectListNew", options, paramMap);
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年5月16日,onDoctTypeAction
	 * */
	public void onDoctTypeAction(SelectEvent event){
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			TsSimpleCode simpleCode = (TsSimpleCode) selectedMap.get("selectPro");
			if (null!=simpleCode) {
				psnInfo.setFkByDoctTypeId(simpleCode);
			}
		}
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2018年5月16日,onDoctTypeAction
	 * */
	public void onDoctSpecAction(SelectEvent event){
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			TsSimpleCode simpleCode = (TsSimpleCode) selectedMap.get("selectPro");
			if (null!=simpleCode) {
				psnInfo.setFkByDoctSpecId(simpleCode);
			}
		}
	}

	/**
 	 * <p>方法描述：清空医师执业类别</p>
 	 * @MethodAuthor qrr,2018年5月22日,clearDoctType
	 * */
	public void clearDoctType(){
		psnInfo.setFkByDoctTypeId(new TsSimpleCode());
	}
	/**
 	 * <p>方法描述：清空医师资格类别</p>
 	 * @MethodAuthor qrr,2018年5月22日,clearDoctType
	 * */
	public void clearDoctSpec(){
		psnInfo.setFkByDoctSpecId(new TsSimpleCode());
	}
	public void changePsnType() {
		this.psnType = null;
		this.ifQualAnnex=null;
		this.ifPraAnnex=null;
		this.ifNurseOrDoctor=null;
		this.ifHoldCent=false;
		this.ifEmployCent=false;
		this.ifLaborAnnex=false;
		for (TdZwPsnTypeRow row : psnTypeRows) {
			List<TdZwPsnTypePO> typePOs = row.getTypePOs();
			for (TdZwPsnTypePO t : typePOs) {
				if (t.isIfSelected()) {
					if ("1".equals(t.getFkByPsnType().getExtendS1())) {
						this.psnType = "1";//检查医生
					}
					if(zwOrgType!=null&&(zwOrgType==0||zwOrgType==2)){
						if (StringUtils.isNotBlank(t.getFkByPsnType().getExtendS5())) {
							if (StringUtils.string2list(t.getFkByPsnType().getExtendS5(), ",").contains("1")) {
								//医师资格证书附件  必填
								this.ifQualAnnex = "1";
							}
							if(StringUtils.string2list(t.getFkByPsnType().getExtendS5(), ",").contains("2")){
								//医师执业证书附件  必填
								this.ifPraAnnex = "1";
							}
							if(!ifHoldCent&&"1".equals(this.ifRequiredAnnex)&&StringUtils.string2list(t.getFkByPsnType().getExtendS5(),",").contains("4")){
								ifHoldCent=true;
							}
							if(!ifEmployCent&&"1".equals(this.ifRequiredAnnex)&&StringUtils.string2list(t.getFkByPsnType().getExtendS5(),",").contains("5")){
								ifEmployCent=true;
							}
							if(StringUtils.string2list(t.getFkByPsnType().getExtendS5(), ",").contains("3")){
								//劳动关系证明  必填
								this.ifLaborAnnex = true;
							}

						}
						//取扩展字段6 权重最大的值
						if(StringUtils.isNotBlank(t.getFkByPsnType().getExtendS6())){
							if(StringUtils.isNotBlank(ifNurseOrDoctor)){
								if(Integer.parseInt(t.getFkByPsnType().getExtendS6())>Integer.parseInt(ifNurseOrDoctor)){
									ifNurseOrDoctor=t.getFkByPsnType().getExtendS6();
								}
							}else{
								ifNurseOrDoctor=t.getFkByPsnType().getExtendS6();
							}
						}
					}
				}
			}
		}
	}
	public TdZwPsninfoZzsb getPsnInfo() {
		return psnInfo;
	}

	public void setPsnInfo(TdZwPsninfoZzsb psnInfo) {
		this.psnInfo = psnInfo;
	}
	public TdZwPsnZzlbZzsb getPsnZzlb() {
		return psnZzlb;
	}
	public void setPsnZzlb(TdZwPsnZzlbZzsb psnZzlb) {
		this.psnZzlb = psnZzlb;
	}
	public List<TsSimpleCode> getQualList() {
		return qualList;
	}
	public void setQualList(List<TsSimpleCode> qualList) {
		this.qualList = qualList;
	}
	public List<TsSimpleCode> getRangeList() {
		return rangeList;
	}
	public void setRangeList(List<TsSimpleCode> rangeList) {
		this.rangeList = rangeList;
	}
	public List<TsSimpleCode> getNationList() {
		return nationList;
	}
	public void setNationList(List<TsSimpleCode> nationList) {
		this.nationList = nationList;
	}
	public List<TsSimpleCode> getEducationList() {
		return educationList;
	}
	public void setEducationList(List<TsSimpleCode> educationList) {
		this.educationList = educationList;
	}
	public List<TsSimpleCode> getTitleList() {
		return titleList;
	}
	public void setTitleList(List<TsSimpleCode> titleList) {
		this.titleList = titleList;
	}
	public List<TsSimpleCode> getZcList() {
		return zcList;
	}
	public void setZcList(List<TsSimpleCode> zcList) {
		this.zcList = zcList;
	}
	public List<TsSimpleCode> getDocLevList() {
		return docLevList;
	}
	public void setDocLevList(List<TsSimpleCode> docLevList) {
		this.docLevList = docLevList;
	}
	public List<TsSimpleCode> getDocSpecSelList() {
		return docSpecSelList;
	}
	public void setDocSpecSelList(List<TsSimpleCode> docSpecSelList) {
		this.docSpecSelList = docSpecSelList;
	}
	public List<TsSimpleCode> getDoctTypeList() {
		return doctTypeList;
	}
	public void setDoctTypeList(List<TsSimpleCode> doctTypeList) {
		this.doctTypeList = doctTypeList;
	}
	public String getFileIndex() {
		return fileIndex;
	}
	public void setFileIndex(String fileIndex) {
		this.fileIndex = fileIndex;
	}
	public List<TsSimpleCode> getDegreeList() {
		return degreeList;
	}
	public void setDegreeList(List<TsSimpleCode> degreeList) {
		this.degreeList = degreeList;
	}
	public boolean isValidPsnInfoId() {
		return validPsnInfoId;
	}
	public void setValidPsnInfoId(boolean validPsnInfoId) {
		this.validPsnInfoId = validPsnInfoId;
	}
	public List<TsSimpleCode> getTitleLevelList() {
		return titleLevelList;
	}
	public void setTitleLevelList(List<TsSimpleCode> titleLevelList) {
		this.titleLevelList = titleLevelList;
	}

    public String getTitleCentflag() {
        return titleCentflag;
    }

    public void setTitleCentflag(String titleCentflag) {
        this.titleCentflag = titleCentflag;
    }
    public Integer getZwOrgType() {
        return zwOrgType;
    }

    public void setZwOrgType(Integer zwOrgType) {
        this.zwOrgType = zwOrgType;
    }
	public boolean isIfShowReceiveInfo() {
		return ifShowReceiveInfo;
	}
	public void setIfShowReceiveInfo(boolean ifShowReceiveInfo) {
		this.ifShowReceiveInfo = ifShowReceiveInfo;
	}
	public String getPsnType() {
		return psnType;
	}
	public void setPsnType(String psnType) {
		this.psnType = psnType;
	}
	public List<TdZwPsnTypeRow> getPsnTypeRows() {
		return psnTypeRows;
	}
	public void setPsnTypeRows(List<TdZwPsnTypeRow> psnTypeRows) {
		this.psnTypeRows = psnTypeRows;
	}
	public boolean isIfHaveZz() {
		return ifHaveZz;
	}
	public void setIfHaveZz(boolean ifHaveZz) {
		this.ifHaveZz = ifHaveZz;
	}
	public Map<Integer, TsSimpleCode> getQualMap() {
		return qualMap;
	}
	public void setQualMap(Map<Integer, TsSimpleCode> qualMap) {
		this.qualMap = qualMap;
	}

    public Integer getPsnTypeNum() {
        return psnTypeNum;
    }

    public void setPsnTypeNum(Integer psnTypeNum) {
        this.psnTypeNum = psnTypeNum;
    }

	public Integer getOrgPsnInfoRid() {
		return orgPsnInfoRid;
	}

	public void setOrgPsnInfoRid(Integer orgPsnInfoRid) {
		this.orgPsnInfoRid = orgPsnInfoRid;
	}

	public String getCachIdc() {
		return cachIdc;
	}

	public void setCachIdc(String cachIdc) {
		this.cachIdc = cachIdc;
	}

	public Integer getOrgRid() {
		return orgRid;
	}

	public void setOrgRid(Integer orgRid) {
		this.orgRid = orgRid;
	}

	public String getIfQualAnnex() {
		return ifQualAnnex;
	}

	public void setIfQualAnnex(String ifQualAnnex) {
		this.ifQualAnnex = ifQualAnnex;
	}

	public String getIfPraAnnex() {
		return ifPraAnnex;
	}

	public void setIfPraAnnex(String ifPraAnnex) {
		this.ifPraAnnex = ifPraAnnex;
	}

	public String getIfNurseOrDoctor() {
		return ifNurseOrDoctor;
	}

	public void setIfNurseOrDoctor(String ifNurseOrDoctor) {
		this.ifNurseOrDoctor = ifNurseOrDoctor;
	}

	public String getIfRequiredAnnex() {
		return ifRequiredAnnex;
	}

	public void setIfRequiredAnnex(String ifRequiredAnnex) {
		this.ifRequiredAnnex = ifRequiredAnnex;
	}

	public Boolean getIfHoldCent() {
		return ifHoldCent;
	}

	public void setIfHoldCent(Boolean ifHoldCent) {
		this.ifHoldCent = ifHoldCent;
	}

	public Boolean getIfEmployCent() {
		return ifEmployCent;
	}

	public void setIfEmployCent(Boolean ifEmployCent) {
		this.ifEmployCent = ifEmployCent;
	}

	public Boolean getIfLaborAnnex() {
		return ifLaborAnnex;
	}

	public void setIfLaborAnnex(Boolean ifLaborAnnex) {
		this.ifLaborAnnex = ifLaborAnnex;
	}
}
