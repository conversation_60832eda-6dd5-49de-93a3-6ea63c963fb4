package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.Global;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * 检查机构资质申报
 * Created by wlj on 2015-04-22.
 */
@ManagedBean(name = "zwTjOrgInfoEditZzsbBean")
@ViewScoped
public class ZwTjOrgInfoEditZzsbBean extends ZwOrgInfoBaseZzsbBean {
    private static final long serialVersionUID = -1881709071592259342L;

    /** 是否有质量管理体系 0否 1是 */
    private String ifQualAnnex;

    private TdZwTjorgRecordZzsb record;
	/** 原始备案信息 避免出现备案信息异常，但因修改了实体属性，导致的原数据异常，比如服务信息未选择，保存提示请选择服务信息，直接关闭备案信息编辑框，但列表的信息更新了 */
    private TdZwTjorgRecordZzsb sourceRecord;
    private List<TdZwTjorgRecordZzsb> tjorgRecords;
    /**市级地区*/
    private List<TsZone> recordZoneList;
    private Map<Integer, TsZone> recordZoneMap;
    private Map<Integer, TsSimpleCode> serviceItemMap;


	/** 质量管理体系制度集合  码表5507 num排序 需初始化*/
	private List<TsSimpleCode> institutionList;

	/** 质量管理体系制度 顶级制度集合 */
	private List<TsSimpleCode> topOfInstitutionList;
	/** 质量管理体系制度 顶级制度对应Map */
	private Map<Integer, TsSimpleCode> topInstitutionMap;
	/** 质量管理体系制度 顶级制度下的分制度 */
	private Map<Integer, List<TsSimpleCode>> institutionMap;
	/** 质量管理体系制度 各制度对应的List */
	private Map<Integer,List<TdZwTjorgQualAnnexZzsb>> qualAnnexMap;
	/** 质量管理体系制度 添加用的codeId */
	private Integer codeId;
	/** 质量管理体系制度 上传附件 删除传递对象 */
	private TdZwTjorgQualAnnexZzsb annexTmp;
	/** 质量管理体系制度 组合动态id 用于文件上传插件 更新对应表格数据 */
	private String topInsPanelStr;

	/** 外检车辆列表 */
	private List<TdZwTjorgOutcarZzsb> tjorgOutcarZzsbList;
	/** 当前操作的外检车辆 */
	private TdZwTjorgOutcarZzsb outcarZzsb;
	private TdZwTjorgOutcarZzsb modOutcarZzsb;

	/** 外检车辆仪器弹出框列表 */
	private List<TdZwInstinfoZzsb> outCarInstList = new ArrayList<>();
	/** 外检车辆已选择仪器Rid */
	private List<Integer> outCarInstSelectRidList = new ArrayList<>();
	/** 外检车辆已选择仪器名称 */
	private String outCarInstNames;
	/** 外检车辆选择仪器弹出框查询条件 仪器名称 */
	private String searchOutCarInstName;
	/** 外检车辆选择仪器弹出框查询条件 仪器型号 */
	private String searchOutCarModel;
	/**
	 * 外检车辆操作附件类型，1 行驶证复印件 ; 2 车辆照片 ; 3 车载DR照片
	 */
	private Integer outCarOpFile;

    @PostConstruct
    public void pageinit(){
        super.init();
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
        	tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		if ("1".equals(ifZoneRecord)) {
			this.recordZoneList = commService.findZoneList(tsZone.getZoneGb().substring(0,2), 2);
		}
        this.recordZoneMap = new HashMap<Integer, TsZone>();
        if (!CollectionUtils.isEmpty(recordZoneList)) {
			for (TsZone t : recordZoneList) {
				this.recordZoneMap.put(t.getRid(), t);
			}
		}
        this.serviceItemMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(jkList)) {
			for (TsSimpleCode t : jkList) {
				this.serviceItemMap.put(t.getRid(), t);
			}
		}
        this.record = new TdZwTjorgRecordZzsb();
    	record.setFkByZoneId(new TsZone());
    	if ("1".equals(ifZoneRecord)) {
    		initTjorgRecords();
		}
		initOutcar();
		//状态修改
		if(this.zwOrginfoEntity.getState() != null && this.zwOrginfoEntity.getState() == 3){
			this.zwOrginfoEntity.setState((short) 0);
		}
    }


	@Override
	public void initOutcar() {
		if(0 == zwOrgType){
			this.outcarZzsb = new TdZwTjorgOutcarZzsb();
			tjorgOutcarZzsbList = zwIntellReportServiceImpl.findTjorgOutcarZzsbListByMainRid(this.zwOrginfoEntity.getRid());
			sortTjorgOutcarZzsbList();
		}
	}

	@Override
	public void addOutcar() {
		outCarInstNames = null;
    	this.outcarZzsb = new TdZwTjorgOutcarZzsb();
		outcarZzsb.setFkByMainId((TdZwTjorginfoZzsb)this.zwOrginfoEntity);
		outcarZzsb.setOutcInstZzsbList(new ArrayList<TdZwTjorgOutcInstZzsb>());
		outcarZzsb.setStateMark(1);
	}

	@Override
	public void modOutcar() {
		outCarInstNames = null;
		outcarZzsb = new TdZwTjorgOutcarZzsb();
		try{
			if(null != modOutcarZzsb){
				ObjectCopyUtil.copyProperties(modOutcarZzsb, outcarZzsb);
				outcarZzsb.setOutcInstZzsbList(new ArrayList<TdZwTjorgOutcInstZzsb>());
				if(!CollectionUtils.isEmpty(modOutcarZzsb.getOutcInstZzsbList())){
					outcarZzsb.getOutcInstZzsbList().addAll(modOutcarZzsb.getOutcInstZzsbList());
				}
			}
		}catch (Exception e){
			e.printStackTrace();
		}
    	if(null != outcarZzsb){
			outCarInstNames = outcarZzsb.getInstNames();
		}
		RequestContext.getCurrentInstance().execute("PF('OutCarEditDialog').show();");
	}

	@Override
	public void submitOutcar() {
		if (this.outcarZzsb == null) {
			JsfUtil.addErrorMessage("保存失败！");
			return;
		}
		boolean filed = false;
		if (ObjectUtil.isEmpty(this.outcarZzsb.getCarNo())) {
			JsfUtil.addErrorMessage("车牌号不允许为空！");
			filed = true;
		}
		if ("1".equals(this.outCarVersion)) {
			if (ObjectUtil.isEmpty(this.outcarZzsb.getCarBand())) {
				JsfUtil.addErrorMessage("品牌不允许为空！");
				filed = true;
			}
			if (ObjectUtil.isEmpty(this.outcarZzsb.getCarStyle())) {
				JsfUtil.addErrorMessage("型号不允许为空！");
				filed = true;
			}
			if (ObjectUtil.isEmpty(this.outcarZzsb.getCarFrameNo())) {
				JsfUtil.addErrorMessage("车架号不允许为空！");
				filed = true;
			}
			if (ObjectUtil.isEmpty(this.outcarZzsb.getProductionDate())) {
				JsfUtil.addErrorMessage("购置日期不允许为空！");
				filed = true;
			}
			if (ObjectUtil.isEmpty(this.outcarZzsb.getOutcInstZzsbList())) {
				JsfUtil.addErrorMessage("配套仪器不允许为空！");
				filed = true;
			}
			if (ObjectUtil.isEmpty(this.outcarZzsb.getDrivingAnnexPath())) {
				JsfUtil.addErrorMessage("请上传行驶证复印件！");
				filed = true;
			}
			if (ObjectUtil.isEmpty(this.outcarZzsb.getCarPhotoAnnexPath())) {
				JsfUtil.addErrorMessage("请上传车辆照片！");
				filed = true;
			}
			if (ObjectUtil.isEmpty(this.outcarZzsb.getCarDrAnnexPath())) {
				JsfUtil.addErrorMessage("请上传车载DR照片！");
				filed = true;
			}
		}
		if (ObjectUtil.isEmpty(this.outcarZzsb.getStateMark())) {
			JsfUtil.addErrorMessage("状态不允许为空！");
			filed = true;
		}
		if (filed) {
			return;
		}
		try{
			boolean flag = null == outcarZzsb.getRid() ? true : false;
			this.commService.upsertEntity(outcarZzsb);
			if(flag){
				if(null == tjorgOutcarZzsbList){
					tjorgOutcarZzsbList = new ArrayList<>();
				}
				tjorgOutcarZzsbList.add(outcarZzsb);
			}else{
				ObjectCopyUtil.copyProperties(outcarZzsb,modOutcarZzsb);
			}
			sortTjorgOutcarZzsbList();
			JsfUtil.addSuccessMessage("保存成功！");
			RequestContext.getCurrentInstance().execute("PF('OutCarEditDialog').hide();");
		}catch (Exception e){
			JsfUtil.addErrorMessage("保存失败！");
			e.printStackTrace();
		}
	}

	@Override
	public void delOutcar() {
		if(null == outcarZzsb || null == outcarZzsb.getRid()){
			return;
		}
    	try{
    		this.commService.deleteEntity(TdZwTjorgOutcarZzsb.class,outcarZzsb.getRid());
    		if(!CollectionUtils.isEmpty(tjorgOutcarZzsbList)){
				tjorgOutcarZzsbList.remove(outcarZzsb);
				sortTjorgOutcarZzsbList();
			}
			JsfUtil.addSuccessMessage("删除成功！");
		}catch(Exception e){
			JsfUtil.addErrorMessage("删除失败！");
			e.printStackTrace();
		}
	}

	@Override
	public void initOutCarInstAction() {
		searchOutCarInstName = null;
		searchOutCarModel = null;
		initOutCarInstSelectRidList();
		initOutCarInstList();
		initOutCarInstNames();
	}


	/**
	 * @Description: 按车牌号排序
	 *
	 * @MethodAuthor pw,2021年06月29日
	 */
	private void sortTjorgOutcarZzsbList(){
		if(!CollectionUtils.isEmpty(tjorgOutcarZzsbList)){
			Collections.sort(tjorgOutcarZzsbList, new Comparator<TdZwTjorgOutcarZzsb>() {
				@Override
				public int compare(TdZwTjorgOutcarZzsb o1, TdZwTjorgOutcarZzsb o2) {
					String carNo1 = o1.getCarNo();
					String carNo2 = o2.getCarNo();
					if(null == carNo1 && null == carNo2){
						return 0;
					}else if(null == carNo1){
						return -1;
					}else if(null == carNo2){
						return 1;
					}
					return carNo1.compareTo(carNo2);
				}
			});
		}
	}

	private void initOutCarInstSelectRidList(){
		outCarInstSelectRidList = new ArrayList<>();
		List<TdZwTjorgOutcInstZzsb> outcInstZzsbList = null != outcarZzsb ? outcarZzsb.getOutcInstZzsbList() : null;
		if(!CollectionUtils.isEmpty(outcInstZzsbList)){
			for(TdZwTjorgOutcInstZzsb instZzsb : outcInstZzsbList){
				if(null != instZzsb.getFkByInstId() && null != instZzsb.getFkByInstId().getRid()){
					outCarInstSelectRidList.add(instZzsb.getFkByInstId().getRid());
				}
			}
		}
	}

	private void initOutCarInstNames(){
		outCarInstNames = null;
		if(!CollectionUtils.isEmpty(outCarInstSelectRidList)){
			List<TdZwInstinfoZzsb> instinfoZzsbs = initAllOutCarInstList();
			StringBuffer buffer = new StringBuffer();
			for(TdZwInstinfoZzsb instinfoZzsb : instinfoZzsbs){
				if(null != instinfoZzsb.getRid() && outCarInstSelectRidList.contains(instinfoZzsb.getRid())){
					buffer.append("，").append(instinfoZzsb.getInstName());
				}
			}
			outCarInstNames = buffer.toString();
			if(StringUtils.isNotBlank(outCarInstNames)){
				outCarInstNames = outCarInstNames.substring(1);
			}
		}
	}

	private List<TdZwInstinfoZzsb> initAllOutCarInstList(){
		List<TdZwInstinfoZzsb> list = new ArrayList<>();
		TdZwTjorginfoZzsb tjorginfoZzsb = (TdZwTjorginfoZzsb)zwOrginfoEntity;
		List<TdZwTjinstZzsb> tdZwTjinstZzsbList = null != tjorginfoZzsb && !CollectionUtils.isEmpty(tjorginfoZzsb.getTdZwTjinsts()) ? tjorginfoZzsb.getTdZwTjinsts() : null;
		if(!CollectionUtils.isEmpty(tdZwTjinstZzsbList)){
			for(TdZwTjinstZzsb tjinstZzsb : tdZwTjinstZzsbList){
				TdZwInstinfoZzsb instinfoZzsb = tjinstZzsb.getTdZwInstinfo();
				if(null != instinfoZzsb){
					TdZwInstinfoZzsb tmpInstInfo = new TdZwInstinfoZzsb();
					try{
						ObjectCopyUtil.copyProperties(instinfoZzsb,tmpInstInfo);
					}catch(Exception e){
						e.printStackTrace();
					}
					if(!CollectionUtils.isEmpty(outCarInstSelectRidList) && outCarInstSelectRidList.contains(tmpInstInfo.getRid())){
						tmpInstInfo.setSelected(true);
					}else{
						tmpInstInfo.setSelected(false);
					}
					list.add(tmpInstInfo);
				}
			}
		}
		return list;
	}

	private void initOutCarInstList(){
		outCarInstList = new ArrayList<>();
		List<TdZwInstinfoZzsb> instinfoZzsbs = initAllOutCarInstList();
		if(!CollectionUtils.isEmpty(instinfoZzsbs)){
			for(TdZwInstinfoZzsb instinfoZzsb : instinfoZzsbs){
				if(StringUtils.isNotBlank(searchOutCarInstName) &&
						!instinfoZzsb.getInstName().contains(searchOutCarInstName.trim())){
					continue;
				}
				if(StringUtils.isNotBlank(searchOutCarModel) &&
						!instinfoZzsb.getInstModel().contains(searchOutCarModel.trim())){
					continue;
				}
				outCarInstList.add(instinfoZzsb);
			}
		}
	}

	@Override
	public void clearOutCarInst() {
		outCarInstNames = null;
		outCarInstSelectRidList = new ArrayList<>();
		if(null != outcarZzsb){
			outcarZzsb.setOutcInstZzsbList(new ArrayList<TdZwTjorgOutcInstZzsb>());
		}
	}

	@Override
	public void sureAddOutCarInst() {
    	if(!CollectionUtils.isEmpty(outCarInstSelectRidList)){
			List<TdZwTjorgOutcInstZzsb> outcInstZzsbList = null != outcarZzsb ? outcarZzsb.getOutcInstZzsbList() : null;
			List<TdZwInstinfoZzsb> instinfoZzsbs = initAllOutCarInstList();
			List<Integer> exitsRidList = new ArrayList<>();
			if(!CollectionUtils.isEmpty(outcInstZzsbList)){
				List<TdZwTjorgOutcInstZzsb> removeOutcInstZzsbList = new ArrayList<>();
				for(TdZwTjorgOutcInstZzsb instZzsb : outcInstZzsbList){
					if(null == instZzsb.getFkByInstId() || !outCarInstSelectRidList.contains(instZzsb.getFkByInstId().getRid())){
						removeOutcInstZzsbList.add(instZzsb);
					}else{
						exitsRidList.add(instZzsb.getFkByInstId().getRid());
					}
				}
				if(!CollectionUtils.isEmpty(removeOutcInstZzsbList)){
					outcInstZzsbList.removeAll(removeOutcInstZzsbList);
				}
			}
			if(null == outcInstZzsbList){
				outcInstZzsbList = new ArrayList<>();
			}
			if(!CollectionUtils.isEmpty(instinfoZzsbs)){
				Map<Integer,TdZwInstinfoZzsb> tmpMap = new HashMap<>();
				for(TdZwInstinfoZzsb instinfoZzsb : instinfoZzsbs){
					if(null != instinfoZzsb.getRid()){
						tmpMap.put(instinfoZzsb.getRid(),instinfoZzsb);
					}
				}
				for(Integer rid : outCarInstSelectRidList){
					if(!exitsRidList.contains(rid)){
						TdZwInstinfoZzsb instinfoZzsb = tmpMap.get(rid);
						if(null == instinfoZzsb || null == instinfoZzsb.getRid()){
							continue;
						}
						TdZwTjorgOutcInstZzsb outcInstZzsb = new TdZwTjorgOutcInstZzsb();
						outcInstZzsb.setFkByCarId(outcarZzsb);
						outcInstZzsb.setFkByInstId(instinfoZzsb);
						outcInstZzsb.setCreateManid(Global.getUser().getRid());
						outcInstZzsb.setCreateDate(new Date());
						outcInstZzsbList.add(outcInstZzsb);
						exitsRidList.add(rid);
					}
				}
			}
			if(null != outcarZzsb){
				outcarZzsb.setOutcInstZzsbList(outcInstZzsbList);
			}
		}else{
			outcarZzsb.setOutcInstZzsbList(new ArrayList<TdZwTjorgOutcInstZzsb>());
		}
		RequestContext.getCurrentInstance().execute("PF('AddOutCarInstDialog').hide();");
	}

	@Override
	public void searchOutCarInst() {
		initOutCarInstList();
	}

	@Override
	public void selectCarInstListAction(TdZwInstinfoZzsb instinfo) {
    	if(null != instinfo.getSelected() && instinfo.getSelected()){
			outCarInstSelectRidList.add(instinfo.getRid());
		}else{
			outCarInstSelectRidList.remove(instinfo.getRid());
		}
		initOutCarInstNames();
	}

	@Override
	public boolean validateOutCarList() {
    	if(null != zwOrginfoEntity.getOutWorkPower() && 1 == zwOrginfoEntity.getOutWorkPower() &&
				CollectionUtils.isEmpty(tjorgOutcarZzsbList)){
    		return true;
		}
		return false;
	}

	/**
 	 * <p>方法描述：初始化备案记录</p>
 	 * @MethodAuthor qrr,2020年10月15日,initTjorgRecords
     * */
    private void initTjorgRecords() {
    	this.tjorgRecords = zwIntellReportServiceImpl.findTjorgRecords(zwOrginfoEntity.getRid());
    	if (CollectionUtils.isEmpty(this.tjorgRecords)) {
    		this.tjorgRecords = new ArrayList<>();
    	}
    	for (TdZwTjorgRecordZzsb t : this.tjorgRecords) {
			List<TdZwTjorgRcdItemZzsb> rcdItems = t.getRcdItems();
			if (!CollectionUtils.isEmpty(rcdItems)) {
				List<String> serviceObj = new ArrayList<>();
				StringBuffer sb = new StringBuffer();
				for (TdZwTjorgRcdItemZzsb itm : rcdItems) {
					sb.append("，").append(itm.getFkByItemId().getCodeName());
					serviceObj.add(itm.getFkByItemId().getRid().toString());
				}
				t.setServiceItems(sb.substring(1));
				t.setServiceObj(serviceObj);

				//地区
				t.setZoneId(t.getFkByZoneId().getRid());
			}
		}
    }


    @Override
    public Class getZwIntellReportClass() {
        return TdZwTjorginfoZzsb.class;
    }

    @Override
    public Integer getOrgInfoType() {
        return 0;
    }
    /**
 	 * <p>方法描述：添加备案</p>
 	 * @MethodAuthor qrr,2020年10月14日,addRecordsAction
     * */
    public void addRecordInit() {
    	this.record = new TdZwTjorgRecordZzsb();
    	record.setFkByZoneId(new TsZone());
    	if (CollectionUtils.isEmpty(tjorgRecords)) {
    		tjorgRecords = new ArrayList<>();
		}
		this.sourceRecord = this.record;
	}

	/**
	 * <p>方法描述： 修改备案信息 </p>
	 * @MethodAuthor： pw 2022/10/17
	 **/
	public void updateRecordInit(){
		this.record = new TdZwTjorgRecordZzsb();
		this.record.setCertDate(this.sourceRecord.getCertDate());
		this.record.setFkByZoneId(this.sourceRecord.getFkByZoneId());
		this.record.setFkByMainId(this.sourceRecord.getFkByMainId());
		this.record.setLogoutDate(this.sourceRecord.getLogoutDate());
		this.record.setRcdNo(this.sourceRecord.getRcdNo());
		this.record.setServiceItems(this.sourceRecord.getServiceItems());
		List<String> serviceObjeList = new ArrayList<>();
		if(!CollectionUtils.isEmpty(this.sourceRecord.getServiceObj())){
			serviceObjeList.addAll(this.sourceRecord.getServiceObj());
		}
		this.record.setServiceObj(serviceObjeList);
		this.record.setStateMark(this.sourceRecord.getStateMark());
		this.record.setUnitName(this.sourceRecord.getUnitName());
		this.record.setZoneId(this.sourceRecord.getZoneId());

	}
    /**
 	 * <p>方法描述：验证备案信息</p>
 	 * @MethodAuthor qrr,2020年10月14日,beforeSaveRecord
     * */
    private boolean beforeSaveRecord() {
    	boolean flag = false;
    	if (null==this.record.getZoneId()) {
			JsfUtil.addErrorMessage("备案地区不能为空！");
			flag = true;
		}else {
			if (null != this.record.getStateMark()
					&& 0 == this.record.getStateMark()) {//正常
				if (!CollectionUtils.isEmpty(tjorgRecords)) {
					for (TdZwTjorgRecordZzsb t : tjorgRecords) {
						if (!t.equals(this.sourceRecord)) {
							Integer stateMark = t.getStateMark();
							if (null==stateMark||1==stateMark) {
								continue;
							}
							if (null==t.getZoneId()||null==this.record.getZoneId()) {
								continue;
							}
							if (t.getZoneId().intValue() == this.record.getZoneId().intValue()) {
								JsfUtil.addErrorMessage("备案地区重复！");
								flag = true;
								break;
							}
						}
					}
				}
			}
		}
		if(StringUtils.isBlank(this.record.getUnitName())){
			JsfUtil.addErrorMessage("备案单位名称不能为空！");
			flag = true;
		}
    	if (StringUtils.isBlank(this.record.getRcdNo())) {
    		JsfUtil.addErrorMessage("备案编号不能为空！");
			flag = true;
		}
    	if (null==this.record.getCertDate()) {
    		JsfUtil.addErrorMessage("备案日期不能为空！");
			flag = true;
		}
    	if (CollectionUtils.isEmpty(this.record.getServiceObj())) {
    		JsfUtil.addErrorMessage("服务项目不能为空！");
			flag = true;
		}else {
			StringBuffer sb = new StringBuffer();
			for (String val : this.record.getServiceObj()) {
				TsSimpleCode t = this.serviceItemMap.get(new Integer(val));
				if (null!=t) {
					sb.append("，").append(t.getCodeName());
				}
			}
			this.record.setServiceItems(sb.substring(1));
		}
    	if (null==this.record.getStateMark()) {
    		JsfUtil.addErrorMessage("状态不能为空！");
			flag = true;
		}else {
			if (1==this.record.getStateMark() && null==this.record.getLogoutDate()) {
				JsfUtil.addErrorMessage("注销日期不能为空！");
				flag = true;
			}
			if (0==this.record.getStateMark()) {
				this.record.setLogoutDate(null);
			}
		}
    	//日期判断
    	if (null!=record.getCertDate() && null!=record.getLogoutDate()) {
			if (record.getLogoutDate().before(record.getCertDate())) {
				JsfUtil.addErrorMessage("注销日期应大于等于备案日期！");
				flag = true;
			}
		}
    	return flag;
	}

    /**
 	 * <p>方法描述：添加弹出框保存备案信息</p>
 	 * @MethodAuthor qrr,2020年10月14日,addRecordAction
     * */
    public void saveRecordAction() {
    	if (beforeSaveRecord()) {
			return;
		}
		this.sourceRecord.setCertDate(this.record.getCertDate());
		this.sourceRecord.setFkByZoneId(this.record.getFkByZoneId());
		this.sourceRecord.setFkByMainId(this.record.getFkByMainId());
		this.sourceRecord.setLogoutDate(this.record.getLogoutDate());
		this.sourceRecord.setRcdNo(this.record.getRcdNo());
		this.sourceRecord.setServiceItems(this.record.getServiceItems());
		this.sourceRecord.setServiceObj(this.record.getServiceObj());
		this.sourceRecord.setStateMark(this.record.getStateMark());
		this.sourceRecord.setUnitName(this.record.getUnitName());
		this.sourceRecord.setZoneId(this.record.getZoneId());
    	if (!tjorgRecords.contains(this.sourceRecord)) {
    		tjorgRecords.add(this.sourceRecord);
		}
    	JsfUtil.addSuccessMessage("保存成功！");
    	RequestContext.getCurrentInstance().execute("PF('RecordDialog').hide()");
	}
    /**
 	 * <p>方法描述：删除备案信息</p>
 	 * @MethodAuthor qrr,2020年10月14日,delRecord
     * */
    public void delRecord() {
    	this.tjorgRecords.remove(record);
    	JsfUtil.addSuccessMessage("删除成功！");
	}
    /**
 	 * <p>方法描述：验证备案信息</p>
 	 * @MethodAuthor qrr,2020年10月15日,veryOtherBeforeSave
     * */
    @Override
    protected boolean veryOtherBeforeSave() {
    	if (!"1".equals(ifZoneRecord)) {
			return false;
		}
    	boolean flag = false;
    	if (CollectionUtils.isEmpty(tjorgRecords)) {
    		JsfUtil.addErrorMessage("备案信息不能为空！");
			flag = true;
    	}else {
    		Map<Integer, Integer> map = new HashMap<>();
    		int i = 1;
			for (TdZwTjorgRecordZzsb t : tjorgRecords) {
				if (null==t.getZoneId()) {
					JsfUtil.addErrorMessage("备案信息第"+i+"行备案地区不能为空！");
					flag = true;
				}else {
					if (null!=t.getStateMark() && 0==t.getStateMark()) {
						if (null!=map.get(t.getZoneId())) {
							JsfUtil.addErrorMessage("备案地区重复！");
							flag = true;
						}
						map.put(t.getZoneId(), t.getZoneId());
					}
				}
		    	if (StringUtils.isBlank(t.getRcdNo())) {
		    		JsfUtil.addErrorMessage("备案信息第"+i+"行备案编号不能为空！");
					flag = true;
				}
		    	if (null==t.getCertDate()) {
		    		JsfUtil.addErrorMessage("备案信息第"+i+"行备案日期不能为空！");
					flag = true;
				}
		    	if (CollectionUtils.isEmpty(t.getServiceObj())) {
		    		JsfUtil.addErrorMessage("备案信息第"+i+"行服务项目不能为空！");
					flag = true;
		    	}
		    	if (null==t.getStateMark()) {
		    		JsfUtil.addErrorMessage("备案信息第"+i+"行状态不能为空！");
					flag = true;
				}else {
					if (1==t.getStateMark() && null==t.getLogoutDate()) {
						JsfUtil.addErrorMessage("备案信息第"+i+"行注销日期不能为空！");
						flag = true;
					}
				}
		    	//日期判断
		    	if (null!=t.getCertDate() && null!=t.getLogoutDate()) {
					if (t.getLogoutDate().before(t.getCertDate())) {
						JsfUtil.addErrorMessage("备案信息第"+i+"行注销日期应大于等于备案日期！");
						flag = true;
					}
				}
		    	i++;
			}
		}
    	return flag;
    }
    /**
 	 * <p>方法描述：保存机构信息</p>
 	 * @MethodAuthor qrr,2020年10月15日,saveOrgInfo
     * */
    @Override
    protected void saveOrgInfo(List<TdZwJsffjgCommAnnexZzsb> annexList) {
    	if ("1".equals(ifZoneRecord)) {//按地区备案
			zwOrginfoEntity = zwIntellReportServiceImpl.saveTjOrgInfo(zwOrginfoEntity, tjorgRecords, annexList);
		}else {
			zwOrginfoEntity = zwIntellReportServiceImpl.saveOrUpdateZwOrginfoEntity(zwOrginfoEntity, annexList);
		}
		//zwOrginfoEntity = zwIntellReportServiceImpl.findZwOrgInfoByEntityRid(zwOrginfoEntity);
    	if(0 == zwOrgType && "1".equals(ifQualAnnex)){ //检查机构资质申报
			institutionSaveOrUpdate();
			initInstitutionListAndMap(zwOrginfoEntity.getRid());
		}
		//增加最新提交时间
		if(zwOrginfoEntity.getState()==(short)1 || zwOrginfoEntity.getState()==(short)2){
			zwIntellReportServiceImpl.updateOrgInfoLastDate(zwOrginfoEntity.getRid());
		}
    }

	/**
	 *  质量管理体系
	 *  初始化质量管理体系制度集合
	 */
	@Override
	public void initInstitutionList() {
        this.ifQualAnnex = PropertyUtils.getValue("zzsb.ifQualAnnex");
	    if(!"1".equals(ifQualAnnex)){
	        return;
        }
		institutionList= this.commService.findLevelSimpleCodesByTypeId("5507");
		initInstitutionListAndMap(null == zwOrginfoEntity || null == zwOrginfoEntity.getRid() ? null : zwOrginfoEntity.getRid());
	}

	/** 查询数据 组合数据 */
	private void initInstitutionListAndMap(Integer mainId){
		if(null == institutionList || institutionList.size() == 0){
			return;// 码表无值 停止
		}
		topOfInstitutionList = new ArrayList<>();
		topInstitutionMap = new HashMap<>();
		institutionMap = new HashMap<>();
		qualAnnexMap = new HashMap<>();
		StringBuilder builder = new StringBuilder("");
		//初始化顶级
		for(TsSimpleCode tsCode : institutionList){
			if(null != tsCode.getCodeLevelNo() && tsCode.getCodeLevelNo().indexOf(".") < 0){
				//码表扩展字段2无值 或者不是1 或者具备外出开展职业健康检查工作能力
				boolean flag = null == tsCode.getExtendS2() || 1 != tsCode.getExtendS2() ||
						(null != zwOrginfoEntity.getOutWorkPower() && 1 == zwOrginfoEntity.getOutWorkPower());
				if(flag){
					topOfInstitutionList.add(tsCode);
					topInstitutionMap.put(tsCode.getRid(),tsCode);
					builder.append(",topInsPanel").append(tsCode.getRid());
				}
			}
		}
		topInsPanelStr = builder.toString();
		for(TsSimpleCode tsChCode : institutionList){
			for(TsSimpleCode top : topOfInstitutionList){
				if(null != tsChCode.getCodeLevelNo() && tsChCode.getCodeLevelNo().indexOf(".") > -1 &&
						tsChCode.getCodeLevelNo().startsWith(top.getCodeLevelNo())){
					List<TsSimpleCode> tsList = institutionMap.get(top.getRid());
					if(null == tsList){
						tsList = new ArrayList<>();
					}
					tsList.add(tsChCode);
					institutionMap.put(top.getRid(),tsList);
				}
			}
		}
		if(null != institutionMap && !institutionMap.isEmpty()){
			for(Map.Entry<Integer, List<TsSimpleCode>> mapTmp : institutionMap.entrySet()){
				Integer mapKey = mapTmp.getKey();
				List<TsSimpleCode> tsTmpList = mapTmp.getValue();
				List<TdZwTjorgQualAnnexZzsb> annexList = qualAnnexMap.get(mapKey);
				if(null == annexList){
					annexList = new ArrayList<>();
				}
				if(null != tsTmpList && tsTmpList.size() > 0){
					for(TsSimpleCode tsCode : tsTmpList){
						annexList.add(new TdZwTjorgQualAnnexZzsb(tsCode));
					}
				}
				qualAnnexMap.put(mapKey,annexList);
			}
		}
		if(null != mainId){
			String hql = " from TdZwTjorgQualAnnexZzsb t where t.fkByMainId.rid = " + mainId.intValue();
			List<TdZwTjorgQualAnnexZzsb> annexList = commService.findData(hql, null);
			if(annexList != null && annexList.size() > 0){
				for(TdZwTjorgQualAnnexZzsb annex : annexList){
					if(null != topInstitutionMap && !topInstitutionMap.isEmpty() &&
							null != topInstitutionMap.get(annex.getFkByRuleId().getRid())){
						List<TdZwTjorgQualAnnexZzsb> tmpAnnexList = qualAnnexMap.get(annex.getFkByRuleId().getRid());
						if(null == tmpAnnexList){
							tmpAnnexList = new ArrayList<>();
						}
						tmpAnnexList.add(annex);
						qualAnnexMap.put(annex.getFkByRuleId().getRid(), tmpAnnexList);
						continue;
					}

					for(Map.Entry<Integer,List<TdZwTjorgQualAnnexZzsb>> tmpMap : qualAnnexMap.entrySet()){
						List<TdZwTjorgQualAnnexZzsb> tmpList = tmpMap.getValue();
						boolean flag = false;
						if(null != tmpList && tmpList.size() > 0){
							for(TdZwTjorgQualAnnexZzsb qualTmp : tmpList){
								if(qualTmp.getFkByRuleId().getRid() ==
										annex.getFkByRuleId().getRid().intValue()){
									fillTjorgQualAnnexData(qualTmp, annex);
									flag = true;
									break;
								}
							}
							if(flag){
								break;
							}
						}
						if(flag){
							break;
						}
					}
				}
			}
		}
	}

	/** 给对象赋值 */
	public void fillTjorgQualAnnexData(TdZwTjorgQualAnnexZzsb tmp, TdZwTjorgQualAnnexZzsb qualAnnex){
		tmp.setRid(qualAnnex.getRid());
		tmp.setFkByMainId(qualAnnex.getFkByMainId());
		tmp.setFkByRuleId(qualAnnex.getFkByRuleId());
		tmp.setOtherRuleName(qualAnnex.getOtherRuleName());
		tmp.setIfHas(qualAnnex.getIfHas());
		tmp.setAnnexPath(qualAnnex.getAnnexPath());
		tmp.setCreateDate(qualAnnex.getCreateDate());
		tmp.setCreateManid(qualAnnex.getCreateManid());
		tmp.setModifyManid(qualAnnex.getModifyManid());
		tmp.setModifyDate(qualAnnex.getModifyDate());
	}

	/**
	 * 质量管理体系 文件上传
	 * @param event
	 */
	public void institutionAnnexFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			try {
				String fileName = file.getFileName();
				String contentType = file.getContentType().toLowerCase();
				String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
				if (StringUtils.isNotBlank(errorMsg)) {
					JsfUtil.addErrorMessage(errorMsg);
					return;
				}
				String uuid = UUID.randomUUID().toString()
						.replaceAll("-", "");
				String path = JsfUtil.getAbsolutePath();
				//rpt文件夹
				String relativePath = new StringBuffer("tjorgannex/")
						.append(uuid)
						.append(fileName.substring(fileName
								.lastIndexOf("."))).toString();
				// 文件路径
				String filePath = new StringBuilder(path).append(
						relativePath).toString();

				FileUtils.copyFile(filePath, file.getInputstream());
				RequestContext.getCurrentInstance().execute(
						"PF('TjorgAnnexFileDialog').hide()");
				if(null != annexTmp && null != qualAnnexMap && !qualAnnexMap.isEmpty()){
					boolean flag = false;
					for(Map.Entry<Integer,List<TdZwTjorgQualAnnexZzsb>> mapTmp : qualAnnexMap.entrySet()){
						Integer key = mapTmp.getKey();
						List<TdZwTjorgQualAnnexZzsb> list = mapTmp.getValue();
						for(TdZwTjorgQualAnnexZzsb annex : list){
							if(annex.equals(annexTmp)){
								annex.setAnnexPath(relativePath);
								flag = true;
								break;
							}
						}
						if(flag){
							break;
						}
					}
				}
				JsfUtil.addSuccessMessage("上传成功！");
			} catch (Exception e) {
				e.printStackTrace();
				JsfUtil.addErrorMessage("上传失败！");
				throw new RuntimeException(e);
			}
		}
	}

	/**
	 * 质量管理体系保存验证
	 * 保存不需要验证
	 * @return
	 */
	@Override
	public boolean institutionSaveValidate(){
		boolean flag = false;
		if(!"1".equals(ifQualAnnex)){
			return flag;
		}

		return flag;
	}

	/**
	 * 质量管理体系提交验证
	 * 1.有无附件
	 * 2.有附件时判断链接
	 * 3.手动添加制度名称不可以空
	 * @return
	 */
	@Override
	public boolean institutionSubmitValidate(){
		boolean flag = false;
		if(!"1".equals(ifQualAnnex)){
			return flag;
		}
		if(null == zwOrginfoEntity){
			JsfUtil.addErrorMessage("机构异常！");
			flag = true;
		}
		if(null != topOfInstitutionList && topOfInstitutionList.size() > 0 && null != qualAnnexMap && !qualAnnexMap.isEmpty()){
			//必须顺序遍历再验证 否则提示顺序会错误
			for(TsSimpleCode simpleCode : topOfInstitutionList){
				List<TdZwTjorgQualAnnexZzsb> list = qualAnnexMap.get(simpleCode.getRid());
				if(null == simpleCode){
					continue;
				}
				if(null != list && list.size() > 0){
					int i = 1;
					for(TdZwTjorgQualAnnexZzsb top : list){
						if(top.getFkByRuleId().getRid() == simpleCode.getRid().intValue() &&
								StringUtils.isEmpty(top.getOtherRuleName())){
							JsfUtil.addErrorMessage(new StringBuilder(simpleCode.getCodeName()).
									append("第").append(i).append("行手动添加制度未填写制度名称！").toString());

							flag = true;
						}
						if(null == top.getIfHas()){
							JsfUtil.addErrorMessage(new StringBuilder(simpleCode.getCodeName()).
									append("第").append(i).append("行未选择有无！").toString());
							flag = true;
						}
						if(null != top.getIfHas() && top.getIfHas() == 1 && StringUtils.isEmpty(top.getAnnexPath())){
							JsfUtil.addErrorMessage(new StringBuilder(simpleCode.getCodeName()).
									append("第").append(i).append("行选择有附件，但未上传附件！").toString());
							flag = true;
						}
						i++;
					}
				}
			}
		}else{
			flag = true;
		}
		return flag;
	}

	/**
	 * 质量管理体系 修改或者保存方法
	 */
	public void institutionSaveOrUpdate(){
        if(!"1".equals(ifQualAnnex)){
            return;
        }
		if(null == zwOrginfoEntity || null == zwOrginfoEntity.getRid()){
			return;
		}
		if(null != qualAnnexMap && !qualAnnexMap.isEmpty()){
			List<TdZwTjorgQualAnnexZzsb> list = new ArrayList<>();
			for(Map.Entry<Integer,List<TdZwTjorgQualAnnexZzsb>> tmp : qualAnnexMap.entrySet()){
				list.addAll(tmp.getValue());
			}
			if(null != list && list.size() > 0){
				List<TdZwTjorgQualAnnexZzsb> saveList = new ArrayList<>();
				List<TdZwTjorgQualAnnexZzsb> updateList = new ArrayList<>();
				List<TdZwTjorgQualAnnexZzsb> deleteList = new ArrayList<>();
				String hql = " from TdZwTjorgQualAnnexZzsb t where t.fkByMainId.rid = " + zwOrginfoEntity.getRid();
				List<TdZwTjorgQualAnnexZzsb> annexList = commService.findData(hql, null);
				if(null != list && list.size() > 0){
					for(TdZwTjorgQualAnnexZzsb tmp : list){
						if(null != tmp.getIfHas() && tmp.getIfHas() == 0 && StringUtils.isNotEmpty(tmp.getAnnexPath())){
							tmp.setAnnexPath(null);
						}
						if(null == tmp.getFkByMainId()){
							tmp.setFkByMainId(new TdZwTjorginfoZzsb(zwOrginfoEntity.getRid()));
						}
						if(null == tmp.getRid()){
							tmp.setCreateDate(new Date());
							tmp.setCreateManid(sessionData.getUser().getRid());
							saveList.add(tmp);
						}else{
							tmp.setModifyDate(new Date());
							tmp.setModifyManid(sessionData.getUser().getRid());
							updateList.add(tmp);
						}
					}
					if(null != updateList && updateList.size() > 0){
						if(null != annexList && annexList.size() > 0){
							for(TdZwTjorgQualAnnexZzsb tmp : annexList){
								boolean flag = true;
								for(TdZwTjorgQualAnnexZzsb updateObj : updateList){
									if(tmp.getRid() == updateObj.getRid().intValue()){
										flag = false;
										break;
									}
								}
								if(flag){// 删除前端删除的列
									deleteList.add(tmp);
								}
							}
						}
					}
					this.zwIntellReportServiceImpl.institutionSaveOrUpdate(saveList, updateList, deleteList);
				}
			}
		}
	}

	/** 质量管理体系  添加按钮 */
	public void institutionAddOne(){
		if(null != codeId && null != qualAnnexMap && !qualAnnexMap.isEmpty()
				&& null != topInstitutionMap && !topInstitutionMap.isEmpty()){
			TsSimpleCode top = topInstitutionMap.get(codeId);
			if(null == top){
				return;
			}
			List<TdZwTjorgQualAnnexZzsb> list = qualAnnexMap.get(codeId);
			if(null == list){
				list = new ArrayList<>();
			}
			TdZwTjorgQualAnnexZzsb tmp = new TdZwTjorgQualAnnexZzsb(top);
			tmp.setIfHas(1);
			list.add(tmp);
			codeId = null;
		}
	}

	/** 质量管理体系 删除 */
	public void institutionDeleteOne(){
		if(null != annexTmp && qualAnnexMap != null && !qualAnnexMap.isEmpty()){
			for(Map.Entry<Integer,List<TdZwTjorgQualAnnexZzsb>> map : qualAnnexMap.entrySet()){
				Integer key = map.getKey();
				List<TdZwTjorgQualAnnexZzsb> list = map.getValue();
				if(null != list && list.size() > 0){
					if(list.contains(annexTmp)){
						list.remove(annexTmp);
						annexTmp = null;
						break;
					}
				}
			}
		}
	}

	/** 质量管理体系 附件删除 */
	public void delInsFile(){
		if(null != annexTmp){
			boolean flag = false;
			for(Map.Entry<Integer,List<TdZwTjorgQualAnnexZzsb>> map : qualAnnexMap.entrySet()){
				if(flag){
					break;
				}
				Integer key = map.getKey();
				List<TdZwTjorgQualAnnexZzsb> list = map.getValue();
				if(null != list && list.size() > 0){
					for(TdZwTjorgQualAnnexZzsb tmp : list){
						if(tmp.equals(annexTmp)){
							tmp.setAnnexPath(null);
							flag = true;
							break;
						}
					}
				}
			}
		}
	}

	public void showOutCarFileUpload() {
		RequestContext.getCurrentInstance().execute("PF('OutCarFileDialog').show();");
	}

	/**
	 * 外检车辆上传附件
	 */
	public void outCarFileUpload(FileUploadEvent event) {
		if (ObjectUtil.isEmpty(this.outCarOpFile) || event == null) {
			return;
		}
		UploadedFile file = event.getFile();
		try {
			// 文件名称
			String fileName = file.getFileName();
			String contentType = file.getContentType().toLowerCase();
			String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
			if (StringUtils.isNotBlank(errorMsg)) {
				JsfUtil.addErrorMessage(errorMsg);
				return;
			}
			String uuid = UUID.randomUUID().toString()
					.replaceAll("-", "");
			String relativePath = "heth/zworgannex/" + uuid + fileName.substring(fileName.lastIndexOf("."));
			String filePath = JsfUtil.getAbsolutePath() + relativePath;
			FileUtils.copyFile(filePath, file.getInputstream());
			RequestContext.getCurrentInstance().execute(
					"PF('OutCarFileDialog').hide()");
			switch (this.outCarOpFile) {
				case 1:
					this.outcarZzsb.setDrivingAnnexPath(relativePath);
					break;
				case 2:
					this.outcarZzsb.setCarPhotoAnnexPath(relativePath);
					break;
				case 3:
					this.outcarZzsb.setCarDrAnnexPath(relativePath);
					break;
				default:
					break;
			}
			JsfUtil.addSuccessMessage("上传成功！");
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("上传失败！");
			throw new RuntimeException(e);
		}
	}

	/**
	 * 外检车辆删除附件
	 */
	public void outCarFileDel() {
		if (ObjectUtil.isEmpty(this.outCarOpFile)) {
			return;
		}
		switch (this.outCarOpFile) {
			case 1:
				this.outcarZzsb.setDrivingAnnexPath("");
				break;
			case 2:
				this.outcarZzsb.setCarPhotoAnnexPath("");
				break;
			case 3:
				this.outcarZzsb.setCarDrAnnexPath("");
				break;
			default:
				break;
		}
	}
	public List<TdZwTjorgRecordZzsb> getTjorgRecords() {
		return tjorgRecords;
	}

	public void setTjorgRecords(List<TdZwTjorgRecordZzsb> tjorgRecords) {
		this.tjorgRecords = tjorgRecords;
	}

	public TdZwTjorgRecordZzsb getRecord() {
		return record;
	}

	public void setRecord(TdZwTjorgRecordZzsb record) {
		this.record = record;
	}

	public List<TsZone> getRecordZoneList() {
		return recordZoneList;
	}

	public void setRecordZoneList(List<TsZone> recordZoneList) {
		this.recordZoneList = recordZoneList;
	}

	public Map<Integer, TsZone> getRecordZoneMap() {
		return recordZoneMap;
	}

	public void setRecordZoneMap(Map<Integer, TsZone> recordZoneMap) {
		this.recordZoneMap = recordZoneMap;
	}

	public List<TsSimpleCode> getInstitutionList() {
		return institutionList;
	}

	public void setInstitutionList(List<TsSimpleCode> institutionList) {
		this.institutionList = institutionList;
	}

    public String getIfQualAnnex() {
        return ifQualAnnex;
    }

    public void setIfQualAnnex(String ifQualAnnex) {
        this.ifQualAnnex = ifQualAnnex;
    }

	public List<TsSimpleCode> getTopOfInstitutionList() {
		return topOfInstitutionList;
	}

	public void setTopOfInstitutionList(List<TsSimpleCode> topOfInstitutionList) {
		this.topOfInstitutionList = topOfInstitutionList;
	}

	public Map<Integer, TsSimpleCode> getTopInstitutionMap() {
		return topInstitutionMap;
	}

	public void setTopInstitutionMap(Map<Integer, TsSimpleCode> topInstitutionMap) {
		this.topInstitutionMap = topInstitutionMap;
	}

	public Map<Integer, List<TsSimpleCode>> getInstitutionMap() {
		return institutionMap;
	}

	public void setInstitutionMap(Map<Integer, List<TsSimpleCode>> institutionMap) {
		this.institutionMap = institutionMap;
	}

	public Map<Integer, List<TdZwTjorgQualAnnexZzsb>> getQualAnnexMap() {
		return qualAnnexMap;
	}

	public void setQualAnnexMap(Map<Integer, List<TdZwTjorgQualAnnexZzsb>> qualAnnexMap) {
		this.qualAnnexMap = qualAnnexMap;
	}

	public Integer getCodeId() {
		return codeId;
	}

	public void setCodeId(Integer codeId) {
		this.codeId = codeId;
	}

	public TdZwTjorgQualAnnexZzsb getAnnexTmp() {
		return annexTmp;
	}

	public void setAnnexTmp(TdZwTjorgQualAnnexZzsb annexTmp) {
		this.annexTmp = annexTmp;
	}

	public String getTopInsPanelStr() {
		return topInsPanelStr;
	}

	public void setTopInsPanelStr(String topInsPanelStr) {
		this.topInsPanelStr = topInsPanelStr;
	}

	@Override
	public List<TdZwTjorgOutcarZzsb> getTjorgOutcarZzsbList() {
		return tjorgOutcarZzsbList;
	}

	@Override
	public void setTjorgOutcarZzsbList(List<TdZwTjorgOutcarZzsb> tjorgOutcarZzsbList) {
		this.tjorgOutcarZzsbList = tjorgOutcarZzsbList;
	}

	@Override
	public TdZwTjorgOutcarZzsb getOutcarZzsb() {
		return outcarZzsb;
	}

	@Override
	public void setOutcarZzsb(TdZwTjorgOutcarZzsb outcarZzsb) {
		this.outcarZzsb = outcarZzsb;
	}

	@Override
	public List<TdZwInstinfoZzsb> getOutCarInstList() {
		return outCarInstList;
	}

	@Override
	public void setOutCarInstList(List<TdZwInstinfoZzsb> outCarInstList) {
		this.outCarInstList = outCarInstList;
	}

	@Override
	public List<Integer> getOutCarInstSelectRidList() {
		return outCarInstSelectRidList;
	}

	@Override
	public void setOutCarInstSelectRidList(List<Integer> outCarInstSelectRidList) {
		this.outCarInstSelectRidList = outCarInstSelectRidList;
	}

	@Override
	public String getOutCarInstNames() {
		return outCarInstNames;
	}

	@Override
	public void setOutCarInstNames(String outCarInstNames) {
		this.outCarInstNames = outCarInstNames;
	}

	@Override
	public String getSearchOutCarInstName() {
		return searchOutCarInstName;
	}

	@Override
	public void setSearchOutCarInstName(String searchOutCarInstName) {
		this.searchOutCarInstName = searchOutCarInstName;
	}

	@Override
	public String getSearchOutCarModel() {
		return searchOutCarModel;
	}

	@Override
	public void setSearchOutCarModel(String searchOutCarModel) {
		this.searchOutCarModel = searchOutCarModel;
	}

	@Override
	public TdZwTjorgOutcarZzsb getModOutcarZzsb() {
		return modOutcarZzsb;
	}

	@Override
	public void setModOutcarZzsb(TdZwTjorgOutcarZzsb modOutcarZzsb) {
		this.modOutcarZzsb = modOutcarZzsb;
	}

	@Override
	public Integer getOutCarOpFile() {
		return outCarOpFile;
	}

	@Override
	public void setOutCarOpFile(Integer outCarOpFile) {
		this.outCarOpFile = outCarOpFile;
	}

	public TdZwTjorgRecordZzsb getSourceRecord() {
		return sourceRecord;
	}

	public void setSourceRecord(TdZwTjorgRecordZzsb sourceRecord) {
		this.sourceRecord = sourceRecord;
	}
}
