package com.chis.modules.heth.zzsb.logic;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.List;

/**
 * <p>类描述：职业卫生技术服务机构业务范围</p>
 * @ClassAuthor qrr,2021年2月28日,TdZwOcchethItemsShow
 * */
public class TdZwOcchethItemsShow {
	private TsSimpleCode item;
	private List<String> selectItems;
	private List<TsSimpleCode> childList;
	public TsSimpleCode getItem() {
		return item;
	}
	public void setItem(TsSimpleCode item) {
		this.item = item;
	}
	public List<TsSimpleCode> getChildList() {
		return childList;
	}
	public void setChildList(List<TsSimpleCode> childList) {
		this.childList = childList;
	}
	public List<String> getSelectItems() {
		return selectItems;
	}
	public void setSelectItems(List<String> selectItems) {
		this.selectItems = selectItems;
	}
}
