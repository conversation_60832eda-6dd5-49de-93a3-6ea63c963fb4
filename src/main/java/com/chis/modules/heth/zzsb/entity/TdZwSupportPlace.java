package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-10-25
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_PLACE")
@SequenceGenerator(name = "TdZwSupportPlace", sequenceName = "TD_ZW_SUPPORT_PLACE_SEQ", allocationSize = 1)
public class TdZwSupportPlace implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSupportOrgZzsb fkByMainId;
	private Integer ifZwLab;
	private BigDecimal zwLabArea;
	private Integer ifFsLab;
	private BigDecimal fsLabArea;
	private Integer ifScienTask;
	private BigDecimal teachArea;
	private BigDecimal scienArea;
	private Integer ifTj;
	private BigDecimal tjLabArea;
	private Integer ifDiag;
	private BigDecimal diagLabArea;
	private Integer diagBed;
	private TsSimpleCode fkByUnitStdId;
	private Integer unitStdId;
	private TsSimpleCode fkByPsnStdId;
	private Integer psnStdId;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;

	private List<TdZwSupportOffice>  supportOfficeList=new ArrayList<>();

	private List<String> selOffices;

	private String selOfficeName;
	
	public TdZwSupportPlace() {
	}

	public TdZwSupportPlace(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportPlace")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSupportOrgZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IF_ZW_LAB")	
	public Integer getIfZwLab() {
		return ifZwLab;
	}

	public void setIfZwLab(Integer ifZwLab) {
		this.ifZwLab = ifZwLab;
	}	
			
	@Column(name = "ZW_LAB_AREA")	
	public BigDecimal getZwLabArea() {
		return zwLabArea;
	}

	public void setZwLabArea(BigDecimal zwLabArea) {
		this.zwLabArea = zwLabArea;
	}	
			
	@Column(name = "IF_FS_LAB")	
	public Integer getIfFsLab() {
		return ifFsLab;
	}

	public void setIfFsLab(Integer ifFsLab) {
		this.ifFsLab = ifFsLab;
	}	
			
	@Column(name = "FS_LAB_AREA")	
	public BigDecimal getFsLabArea() {
		return fsLabArea;
	}

	public void setFsLabArea(BigDecimal fsLabArea) {
		this.fsLabArea = fsLabArea;
	}	
			
	@Column(name = "IF_SCIEN_TASK")	
	public Integer getIfScienTask() {
		return ifScienTask;
	}

	public void setIfScienTask(Integer ifScienTask) {
		this.ifScienTask = ifScienTask;
	}	
			
	@Column(name = "TEACH_AREA")	
	public BigDecimal getTeachArea() {
		return teachArea;
	}

	public void setTeachArea(BigDecimal teachArea) {
		this.teachArea = teachArea;
	}	
			
	@Column(name = "SCIEN_AREA")	
	public BigDecimal getScienArea() {
		return scienArea;
	}

	public void setScienArea(BigDecimal scienArea) {
		this.scienArea = scienArea;
	}	
			
	@Column(name = "IF_TJ")	
	public Integer getIfTj() {
		return ifTj;
	}

	public void setIfTj(Integer ifTj) {
		this.ifTj = ifTj;
	}	
			
	@Column(name = "TJ_LAB_AREA")	
	public BigDecimal getTjLabArea() {
		return tjLabArea;
	}

	public void setTjLabArea(BigDecimal tjLabArea) {
		this.tjLabArea = tjLabArea;
	}	
			
	@Column(name = "IF_DIAG")	
	public Integer getIfDiag() {
		return ifDiag;
	}

	public void setIfDiag(Integer ifDiag) {
		this.ifDiag = ifDiag;
	}	
			
	@Column(name = "DIAG_LAB_AREA")	
	public BigDecimal getDiagLabArea() {
		return diagLabArea;
	}

	public void setDiagLabArea(BigDecimal diagLabArea) {
		this.diagLabArea = diagLabArea;
	}	
			
	@Column(name = "DIAG_BED")	
	public Integer getDiagBed() {
		return diagBed;
	}

	public void setDiagBed(Integer diagBed) {
		this.diagBed = diagBed;
	}	
			
	@ManyToOne
	@JoinColumn(name = "UNIT_STD_ID")			
	public TsSimpleCode getFkByUnitStdId() {
		return fkByUnitStdId;
	}

	public void setFkByUnitStdId(TsSimpleCode fkByUnitStdId) {
		this.fkByUnitStdId = fkByUnitStdId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PSN_STD_ID")			
	public TsSimpleCode getFkByPsnStdId() {
		return fkByPsnStdId;
	}

	public void setFkByPsnStdId(TsSimpleCode fkByPsnStdId) {
		this.fkByPsnStdId = fkByPsnStdId;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId",orphanRemoval = true)
	public List<TdZwSupportOffice> getSupportOfficeList() {
		return supportOfficeList;
	}

	public void setSupportOfficeList(List<TdZwSupportOffice> supportOfficeList) {
		this.supportOfficeList = supportOfficeList;
	}

	@Transient
	public List<String> getSelOffices() {
		return selOffices;
	}

	public void setSelOffices(List<String> selOffices) {
		this.selOffices = selOffices;
	}

	@Transient
	public String getSelOfficeName() {
		return selOfficeName;
	}

	public void setSelOfficeName(String selOfficeName) {
		this.selOfficeName = selOfficeName;
	}

	@Transient
	public Integer getUnitStdId() {
		return unitStdId;
	}

	public void setUnitStdId(Integer unitStdId) {
		this.unitStdId = unitStdId;
	}

	@Transient
	public Integer getPsnStdId() {
		return psnStdId;
	}

	public void setPsnStdId(Integer psnStdId) {
		this.psnStdId = psnStdId;
	}
}