package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-24
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_INST")
@SequenceGenerator(name = "TdZwOcchethInst", sequenceName = "TD_ZW_OCCHETH_INST_SEQ", allocationSize = 1)
public class TdZwOcchethInstZzsb implements java.io.Serializable,IZwInstZzsb {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwOcchethInfoZzsb fkByOrgId;
	private TdZwInstinfoZzsb tdZwInstinfo;
	private Date createDate;
	private Integer createManid;
	
	public TdZwOcchethInstZzsb() {
	}

	public TdZwOcchethInstZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethInst")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwOcchethInfoZzsb getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwOcchethInfoZzsb fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return fkByOrgId;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		this.fkByOrgId = (TdZwOcchethInfoZzsb) zwOrginfo;
	}
	@ManyToOne
	@JoinColumn(name = "INST_ID")
	public TdZwInstinfoZzsb getTdZwInstinfo() {
		return tdZwInstinfo;
	}

	public void setTdZwInstinfo(TdZwInstinfoZzsb tdZwInstinfo) {
		this.tdZwInstinfo = tdZwInstinfo;
	}
			
}