package com.chis.modules.heth.zzsb.web;

import com.chis.modules.heth.zzsb.entity.TdZwDiagorginfoZzsb;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

/**
 * 诊断机构资质申报
 * Created by wlj on 2015-04-22.
 */
@ManagedBean(name = "zwDiagOrgInfoEditZzsbBean")
@ViewScoped
public class ZwDiagOrgInfoEditZzsbBean extends ZwOrgInfoBaseZzsbBean {
    private static final long serialVersionUID = 2968287285620149238L;

    @PostConstruct
    private void pageInit() {
        super.init();


    }

    @Override
    public Class getZwIntellReportClass() {
        return TdZwDiagorginfoZzsb.class;
    }

    @Override
    public Integer getOrgInfoType() {
        return 2;
    }
}
