package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-16
 */
@Entity
@Table(name = "TD_ZW_TJORGINFO_APPLY")
@SequenceGenerator(name = "TdZwTjorginfoApply", sequenceName = "TD_ZW_TJORGINFO_APPLY_SEQ", allocationSize = 1)
public class TdZwTjorginfoApply implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsUnit fkByOrgId;
	private TsZone fkByZoneId;
	private String orgName;
	private TsSimpleCode fkByQualTypeId;
	private TsSimpleCode fkByApplyTypeId;
	private Date applyDate;
	private Integer state;
	private Date fstChkDate;
	private TsUnit fkByFstChkOrgId;
	private TsUserInfo fkByFstChkPsnId;
	private String fstBackRsn;
	private Date finalChkDate;
	private TsUnit fkByFinalChkOrgId;
	private TsUserInfo fkByFinalChkPsnId;
	private String finalBackRsn;
	private String orgAddr;
	private String orgFz;
	private String orgFzzw;
	private String linkMan;
	private String linkMb;
	private String linkTel;
	private String fax;
	private String zipcode;
	private String email;
	private Integer outWorkPower;
	private String certNo;
	private Date firstGetday;
	private Date filingDate;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	/**检查项目*/
	private List<TdZwTjitemsApply> tjitemsApplies;
	/**服务明细*/
	private List<TdZwTjorggitemsApply> tjorggitemsApplyList;
	private List<TdZwTjorgItmDetailApy> tjorgItmDetailApies;
	/**外检车辆*/
	private List<Object[]> tjorgOutcarApplyList;
	/**质量体系*/
	private List<TdZwTjorgQualAnnexApy> tjorgQualAnnexApies;
	/**附件*/
	private List<Object[]> annexList;
	/**仪器*/
	private List<TdZwTjinstApply> tjinstApplyList;
	/**人员*/
	private List<TdZwTjorgpsnsApply> tjorgpsnsApplyList;
	public TdZwTjorginfoApply() {
	}

	public TdZwTjorginfoApply(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorginfoApply")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TsUnit getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TsUnit fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Column(name = "ORG_NAME")	
	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}	
			
	@ManyToOne
	@JoinColumn(name = "QUAL_TYPE_ID")			
	public TsSimpleCode getFkByQualTypeId() {
		return fkByQualTypeId;
	}

	public void setFkByQualTypeId(TsSimpleCode fkByQualTypeId) {
		this.fkByQualTypeId = fkByQualTypeId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "APPLY_TYPE_ID")			
	public TsSimpleCode getFkByApplyTypeId() {
		return fkByApplyTypeId;
	}

	public void setFkByApplyTypeId(TsSimpleCode fkByApplyTypeId) {
		this.fkByApplyTypeId = fkByApplyTypeId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "APPLY_DATE")			
	public Date getApplyDate() {
		return applyDate;
	}

	public void setApplyDate(Date applyDate) {
		this.applyDate = applyDate;
	}	
			
	@Column(name = "STATE")	
	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FST_CHK_DATE")			
	public Date getFstChkDate() {
		return fstChkDate;
	}

	public void setFstChkDate(Date fstChkDate) {
		this.fstChkDate = fstChkDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FST_CHK_ORG_ID")			
	public TsUnit getFkByFstChkOrgId() {
		return fkByFstChkOrgId;
	}

	public void setFkByFstChkOrgId(TsUnit fkByFstChkOrgId) {
		this.fkByFstChkOrgId = fkByFstChkOrgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FST_CHK_PSN_ID")			
	public TsUserInfo getFkByFstChkPsnId() {
		return fkByFstChkPsnId;
	}

	public void setFkByFstChkPsnId(TsUserInfo fkByFstChkPsnId) {
		this.fkByFstChkPsnId = fkByFstChkPsnId;
	}	
			
	@Column(name = "FST_BACK_RSN")	
	public String getFstBackRsn() {
		return fstBackRsn;
	}

	public void setFstBackRsn(String fstBackRsn) {
		this.fstBackRsn = fstBackRsn;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FINAL_CHK_DATE")			
	public Date getFinalChkDate() {
		return finalChkDate;
	}

	public void setFinalChkDate(Date finalChkDate) {
		this.finalChkDate = finalChkDate;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FINAL_CHK_ORG_ID")			
	public TsUnit getFkByFinalChkOrgId() {
		return fkByFinalChkOrgId;
	}

	public void setFkByFinalChkOrgId(TsUnit fkByFinalChkOrgId) {
		this.fkByFinalChkOrgId = fkByFinalChkOrgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "FINAL_CHK_PSN_ID")			
	public TsUserInfo getFkByFinalChkPsnId() {
		return fkByFinalChkPsnId;
	}

	public void setFkByFinalChkPsnId(TsUserInfo fkByFinalChkPsnId) {
		this.fkByFinalChkPsnId = fkByFinalChkPsnId;
	}	
			
	@Column(name = "FINAL_BACK_RSN")	
	public String getFinalBackRsn() {
		return finalBackRsn;
	}

	public void setFinalBackRsn(String finalBackRsn) {
		this.finalBackRsn = finalBackRsn;
	}	
			
	@Column(name = "ORG_ADDR")	
	public String getOrgAddr() {
		return orgAddr;
	}

	public void setOrgAddr(String orgAddr) {
		this.orgAddr = orgAddr;
	}	
			
	@Column(name = "ORG_FZ")	
	public String getOrgFz() {
		return orgFz;
	}

	public void setOrgFz(String orgFz) {
		this.orgFz = orgFz;
	}	
			
	@Column(name = "ORG_FZZW")	
	public String getOrgFzzw() {
		return orgFzzw;
	}

	public void setOrgFzzw(String orgFzzw) {
		this.orgFzzw = orgFzzw;
	}	
			
	@Column(name = "LINK_MAN")	
	public String getLinkMan() {
		return linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}	
			
	@Column(name = "LINK_MB")	
	public String getLinkMb() {
		return linkMb;
	}

	public void setLinkMb(String linkMb) {
		this.linkMb = linkMb;
	}	
			
	@Column(name = "LINK_TEL")	
	public String getLinkTel() {
		return linkTel;
	}

	public void setLinkTel(String linkTel) {
		this.linkTel = linkTel;
	}	
			
	@Column(name = "FAX")	
	public String getFax() {
		return fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}	
			
	@Column(name = "ZIPCODE")	
	public String getZipcode() {
		return zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}	
			
	@Column(name = "EMAIL")	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}	
			
	@Column(name = "OUT_WORK_POWER")	
	public Integer getOutWorkPower() {
		return outWorkPower;
	}

	public void setOutWorkPower(Integer outWorkPower) {
		this.outWorkPower = outWorkPower;
	}	
			
	@Column(name = "CERT_NO")	
	public String getCertNo() {
		return certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FIRST_GETDAY")			
	public Date getFirstGetday() {
		return firstGetday;
	}

	public void setFirstGetday(Date firstGetday) {
		this.firstGetday = firstGetday;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "FILING_DATE")			
	public Date getFilingDate() {
		return filingDate;
	}

	public void setFilingDate(Date filingDate) {
		this.filingDate = filingDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	public List<TdZwTjitemsApply> getTjitemsApplies() {
		return tjitemsApplies;
	}

	public void setTjitemsApplies(List<TdZwTjitemsApply> tjitemsApplies) {
		this.tjitemsApplies = tjitemsApplies;
	}
	@Transient
	public List<TdZwTjorggitemsApply> getTjorggitemsApplyList() {
		return tjorggitemsApplyList;
	}

	public void setTjorggitemsApplyList(List<TdZwTjorggitemsApply> tjorggitemsApplyList) {
		this.tjorggitemsApplyList = tjorggitemsApplyList;
	}
	@Transient
	public List<TdZwTjorgItmDetailApy> getTjorgItmDetailApies() {
		return tjorgItmDetailApies;
	}

	public void setTjorgItmDetailApies(List<TdZwTjorgItmDetailApy> tjorgItmDetailApies) {
		this.tjorgItmDetailApies = tjorgItmDetailApies;
	}

	@Transient
	public List<Object[]> getTjorgOutcarApplyList() {
		return tjorgOutcarApplyList;
	}

	public void setTjorgOutcarApplyList(List<Object[]> tjorgOutcarApplyList) {
		this.tjorgOutcarApplyList = tjorgOutcarApplyList;
	}

	@Transient
	public List<TdZwTjorgQualAnnexApy> getTjorgQualAnnexApies() {
		return tjorgQualAnnexApies;
	}

	public void setTjorgQualAnnexApies(List<TdZwTjorgQualAnnexApy> tjorgQualAnnexApies) {
		this.tjorgQualAnnexApies = tjorgQualAnnexApies;
	}

	@Transient
	public List<Object[]> getAnnexList() {
		return annexList;
	}

	public void setAnnexList(List<Object[]> annexList) {
		this.annexList = annexList;
	}

	@Transient
	public List<TdZwTjinstApply> getTjinstApplyList() {
		return tjinstApplyList;
	}

	public void setTjinstApplyList(List<TdZwTjinstApply> tjinstApplyList) {
		this.tjinstApplyList = tjinstApplyList;
	}

	@Transient
	public List<TdZwTjorgpsnsApply> getTjorgpsnsApplyList() {
		return tjorgpsnsApplyList;
	}

	public void setTjorgpsnsApplyList(List<TdZwTjorgpsnsApply> tjorgpsnsApplyList) {
		this.tjorgpsnsApplyList = tjorgpsnsApplyList;
	}
}