package com.chis.modules.heth.zzsb.service;

import com.chis.common.utils.ObjectUtil;
import com.chis.modules.heth.zzsb.entity.TdZwPsnZzlbZzsb;
import com.chis.modules.heth.zzsb.entity.TdZwPsninfoZzsb;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 专业技术人员档案检索
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(readOnly = false)
public class TdZwPsnInfoZzsbServiceImpl extends AbstractTemplate {

    /**
     * 根据RID获取资质人员信息
     *
     * @param rid      资质人员RID
     * @param lazyLoad 是否懒加载（资质类别）
     * @return 资质人员信息
     */
    @Transactional(readOnly = true)
    public TdZwPsninfoZzsb findTdZwPsnInfoByRid(Integer rid, boolean lazyLoad) {
        TdZwPsninfoZzsb psnInfo = find(TdZwPsninfoZzsb.class, rid);
        if (psnInfo == null) {
            return new TdZwPsninfoZzsb();
        }
        if (!lazyLoad) {
            return psnInfo;
        }
        psnInfo.getZzlbList().size();
        if (ObjectUtil.isNotEmpty(psnInfo.getZzlbList())) {
            for (TdZwPsnZzlbZzsb psnZzlb : psnInfo.getZzlbList()) {
                psnZzlb.getRangeList().size();
            }
        }
        return psnInfo;
    }
}
