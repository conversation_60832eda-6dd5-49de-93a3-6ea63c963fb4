package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-6-24
 */
@Entity
@Table(name = "TD_ZW_TJORG_OUTC_INST")
@SequenceGenerator(name = "TdZwTjorgOutcInst", sequenceName = "TD_ZW_TJORG_OUTC_INST_SEQ", allocationSize = 1)
public class TdZwTjorgOutcInstZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorgOutcarZzsb fkByCarId;
	private TdZwInstinfoZzsb fkByInstId;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwTjorgOutcInstZzsb() {
	}

	public TdZwTjorgOutcInstZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgOutcInst")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "CAR_ID")			
	public TdZwTjorgOutcarZzsb getFkByCarId() {
		return fkByCarId;
	}

	public void setFkByCarId(TdZwTjorgOutcarZzsb fkByCarId) {
		this.fkByCarId = fkByCarId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INST_ID")			
	public TdZwInstinfoZzsb getFkByInstId() {
		return fkByInstId;
	}

	public void setFkByInstId(TdZwInstinfoZzsb fkByInstId) {
		this.fkByInstId = fkByInstId;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}