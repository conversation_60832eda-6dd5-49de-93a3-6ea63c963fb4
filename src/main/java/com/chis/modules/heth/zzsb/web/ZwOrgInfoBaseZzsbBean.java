package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.entity.vo.TechTypeVo;
import com.chis.modules.heth.zzsb.logic.*;
import com.chis.modules.heth.zzsb.utils.ExportExcelUtil;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.ImportExcelUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.primefaces.context.RequestContext;
import org.primefaces.event.*;
import org.primefaces.model.*;
import org.springframework.util.CollectionUtils;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.faces.model.SelectItemGroup;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 资质申报基类 Created by wlj on 2015-04-22.
 *
 * <p>修订内容：在岗状态修改、人员选择添加人员类型、身份证号的查询条件</p>
 * @ClassReviser qrr,2018年6月20日,ZwOrgInfoBaseBean
 */
public abstract class ZwOrgInfoBaseZzsbBean extends ZwQualBaseInfoZzsbBean {
	private static final long serialVersionUID = -7220995257978472971L;

	/**
	 * 机构类型 0：检查机构 1：放射机构 2：诊断机构 3：职业卫生技术服务机构 4：支撑机构
	 */
	protected Integer zwOrgType;
	/**是否按地区备案，0否1是*/
	protected String ifZoneRecord;
	protected List<TdZwOcchethItemsShow> itemsShows;
	/* 附件 */
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList1 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList2 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList3 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList4 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList5 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList6 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList7 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList8 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList9 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList20 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected TdZwJsffjgCommAnnexZzsb selectZwAnnex;
	protected Integer selectZwAnnexType;
	private List<String> deleteFilepath = new ArrayList<String>();
	/* 虚拟路径 */
	protected String xnPath = JsfUtil.getAbsolutePath();
	/* 仪器信息 */
	protected IZwInstZzsb selectOrgInst;
	protected List<IZwInstZzsb> zwInstInfoList;

	/** 人员实体 修改和查看时传递的对象 */
	protected IZwOrgPsnsZzsb selectZwOrgPsn;
	/** 从事科室集合 */
	protected List<TsSimpleCode> workOfficeList = new ArrayList<TsSimpleCode>();
	/** 选中从事科室集合 */
	protected String[] workOffices = null;
	/** 从事科室集合缓存 */
	protected Map<String, String> workOfficeMap = new HashMap<String, String>();
	/**放射仪器类别*/
	protected Integer svrTypeId = null;
	/**服务项目小类*/
	private List<TsSimpleCode> smallServiceList=new ArrayList<TsSimpleCode>();
	/**检测机构服务项目小类*/
	private List<TsSimpleCode> smalljcServiceList=new ArrayList<TsSimpleCode>();

	/** 放射仪器类别*/
	protected List<SelectItemGroup> svrTypeList = Lists.newArrayList();
	/** +检定要求ID20210315*/
	protected List<TsSimpleCode> acptReqList = Lists.newArrayList();
	/** +仪器状态ID20210315*/
	protected List<TsSimpleCode> instStateList = Lists.newArrayList();
	/**放射仪器类别（放射资质模块）*/
	protected  List<SelectItem> svrTypeFsNewList = Lists.newArrayList();

	/**放射仪器类别（放射资质模块）*/
	protected  List<SelectItem> fsSvrTypeList = Lists.newArrayList();

	/**小类别ID， 大类别ID*/
	private Map<Integer, Integer> cacheSvrTypeMap = Maps.newHashMap();

	/** 页面展示码表Map */
	Map<TsSimpleCode, List<TsSimpleCode>> diagMap = null;
	List<TsSimpleCode> diagList = null;
	/** 当前选中父类Id */
	private String selItemId;
	/** 页面展示项目集合 */
	private List<Object[]> showItemList;
	/* 仪器名称 */
	private String itmName;
	/* 仪器名称 */
	private String itmmodle;
	/* 仪器编号 */
	private String itmCode;
	/**是否放射仪器*/
	private String[] svrInstArr;
	/* 仪器 */
	private List<TdZwInstinfoZzsb> srcList = new ArrayList<TdZwInstinfoZzsb>();
	private List<TdZwInstinfoZzsb> displayList = new ArrayList<TdZwInstinfoZzsb>();
	/* 选中仪器 */
	private TdZwInstinfoZzsb tdZwInstinfo = new TdZwInstinfoZzsb();
	

	/*********** 人员选择框换成包含于当前页面中 *****************/
	/** 人员名称 */
	private String itmDName;
	/** 页面展示数据集 */
//	private List<TdZwPsninfo> srcDList = new ArrayList<TdZwPsninfo>();
	private List<TdZwPsninfoZzsb> displayDList = new ArrayList<TdZwPsninfoZzsb>();
	/** 选中人员 */
	protected TdZwPsninfoZzsb tdZwPsninfo = new TdZwPsninfoZzsb();
    /**从事项目*/
    protected Map<String, String> csProMap;
    /** 选中从事项目集合 */
    protected  String[] selectCsPro = null;
	/* tabName */
	private String tabTitle = null;
    /**放射项目*/
    private List<TsSimpleCode> fsList=new ArrayList<TsSimpleCode>();
    /**职业病*/
    private List<TsSimpleCode> zyList=new ArrayList<TsSimpleCode>();
    /**职业卫生技术服务项目*/
    protected List<TsSimpleCode> jsfwList = new ArrayList<TsSimpleCode>();
   
    
    private List<TsSimpleCode> svrList=new ArrayList<TsSimpleCode>();
    private List<TsSimpleCode> acptList=new ArrayList<TsSimpleCode>();
    
    private String selectPsntIds;
    
    private String selectInstIds;
    /**1-修改；2-查看*/
    private Integer psnDialogType;
    
    private Integer searchZoneId;
    private String searchZoneCode;
    private String searchZoneName;
    private List<TsZone> zoneList;
    private Integer unitLevel;
    private String unitName;
    /**医疗附件*/
    private TdZwJsffjgCommAnnexZzsb ylAnnex;
	/**预警日期*/
	private String waringDate;

	private TdzwPsnInfoZzsbBase psnInfoBase;
	//身份证号
	private String itmDIdc;
	//人员类型1：本单位2：非本单位
	private String itmDType;
	//1：在职2：离职
	private String dutySate;
    /**是否福建职卫*/
    private boolean ifHethFj;
    private TdZwInstinfoZzsb instinfo = new TdZwInstinfoZzsb();
    /**是否有人员汇总及填报说明人员汇总0否1是*/
    private String ifPersonSummary;
    /**人员汇总信息*/
    private List<List<String>> personSummaryList;
    /**填报说明*/
    private String fillDesc;

	/** 质量管理体系制度 顶级制度集合 */
	private List<TsSimpleCode> topOfInstitutionList =new ArrayList<>();
	/** 质量管理体系制度 顶级制度对应Map */
	private Map<Integer, TsSimpleCode> topInstitutionMap = new HashMap<>();
	/** 质量管理体系制度 顶级制度下的分制度 */
	private Map<Integer, List<TsSimpleCode>> institutionMap = new HashMap<>();
	/** 质量管理体系制度 各制度对应的List */
	private Map<Integer,List<TdZwTjorgQualAnnexZzsb>> qualAnnexMap = new HashMap<>();
	/** 质量管理体系制度 添加用的codeId */
	private Integer codeId;
	/** 质量管理体系制度 上传附件 删除传递对象 */
	private TdZwTjorgQualAnnexZzsb annexTmp;
	/** 质量管理体系制度 组合动态id 用于文件上传插件 更新对应表格数据 */
	private String topInsPanelStr;

	/*********** 人员导入 *****************/
	/**人员导入-模板下载*/
	private StreamedContent psnfile;
	/**人员导入-表头*/
	private Row psnHeadRow;
	/**人员导入-错误数据*/
	private List<List<Object>> psnErrorList;
	/**人员导入-错误数据按钮是否显示*/
	private Integer psnIfDownload = 0;


	/** 仪器导入 模板下载 */
	private StreamedContent deviceFile;
	/** 仪器导入 表头 */
	private Row deviceHeadRow;
	/** 仪器导入 错误数据 */
	private List<List<Object>> deviceErrorList;
	/** 仪器导入 错误数据按钮是否显示*/
	private Integer deviceIfDownLoad = 0;
	/** 仪器导入 检定要求Map */
	private Map<String, TsSimpleCode> acptReqMap;
	/** 仪器导入 仪器状态Map */
	private Map<String,TsSimpleCode> instStateMap;
	/** 仪器导入 仪器类型Map */
	private Map<String, TsSimpleCode> instTypeMap;
	/** 仪器导入 检查机构仪器类型Map */
	private Map<String, TsSimpleCode> checkInstTypeMap;

	private Boolean showInstAcpt = false;

	private String  psnInfoForm;

	/**
	 * 外检车辆版本，1：陕西
	 */
	protected String outCarVersion;
	/**
	 * 外检车辆操作附件类型，1 行驶证复印件 ; 2 车辆照片 ; 3 车载DR照片
	 */
	private Integer outCarOpFile;
	/** 外检车辆列表 */
	private List<TdZwTjorgOutcarZzsb> tjorgOutcarZzsbList = new ArrayList<>();
	/** 当前操作的外检车辆 */
	private TdZwTjorgOutcarZzsb outcarZzsb = new TdZwTjorgOutcarZzsb();
	/** 修改的外检车辆 */
	private TdZwTjorgOutcarZzsb modOutcarZzsb = new TdZwTjorgOutcarZzsb();

	/** 外检车辆仪器弹出框列表 */
	private List<TdZwInstinfoZzsb> outCarInstList = new ArrayList<>();
	/** 外检车辆已选择仪器Rid */
	private List<Integer> outCarInstSelectRidList = new ArrayList<>();
	/** 外检车辆已选择仪器名称 */
	private String outCarInstNames;
	/** 外检车辆选择仪器弹出框查询条件 仪器名称 */
	private String searchOutCarInstName;
	/** 外检车辆选择仪器弹出框查询条件 仪器型号 */
	private String searchOutCarModel;


	/*服务项目大类存储*/
	private Map<String,TsSimpleCode> jkMap=new HashMap<>();

	/*服务项目小类存储*/
	private Map<Integer,TsSimpleCode> jkJcMap=new HashMap<>();

    /*选中的服务项目小类*/
	private Map<String,TsSimpleCode> jkItemSelectedMap=new HashMap<>();

	/*资质申报信息中是否保存*/
	private Boolean ifZzsbSave=false;
	/** 服务明细服务大类Rid*/
	private String jcItemRid;

	/**
	 * 是否显示备案管理机构
	 */
	private Boolean ifShowRcdOrg = Boolean.FALSE;
	/**
	 * 备案管理机构
	 */
	private List<TsSimpleCode> rcdOrgList;
	private Map<Integer, String> rcdOrgMap;
	/**
	 * 检查机构资质有无批准的执业区域
	 */
	private Boolean ifZoneApprove = Boolean.FALSE;
	private String selectZoneApproveName;
	private String selectZoneApproveRid;
	private List<TsZone> allZoneApproveList;
	protected TreeNode zoneApproveTree;
	protected TreeNode[] selectZoneApproves;
	/**是否显示【质量控制结果】0：不显示；1：显示*/
	private String zkCheckRst;
	List<Object[]> zwCheckRstList ;
	/** 缓存仪器类型 */
	private Map<Integer,TsSimpleCode> cacheInstSimpleCodeMap;

	private List<TdZwPsnZzlbZzsb>  removeZzlbList;

	/** 用于跟踪人员状态变化的Map，key为人员RID，value为原始在职状态 */
	private Map<Integer, String> originalDutyStateMap = new HashMap<>();

	/** 用于跟踪被删除人员的Set */
	private Set<Integer> deletedPsnRidSet = new HashSet<>();

    /**
     * 支撑机构-有无科研、学术信息
     */
    protected Boolean hasResearchAndAcademicInfo = Boolean.FALSE;
    /**
     * 支撑机构-科研课题信息-人员选择弹出框用
     */
    protected SupportResearchPsnSelVO supportResearchPsnSelVO;
    /**
     * 支撑机构-科研课题信息-人员选择-人员输入框ID
     */
    protected String psnInputId;
    /**
     * 支撑机构-科研课题信息-上传文件操作类型
     * <pre>1 论文情况</pre>
     * <pre>2 专著情况</pre>
     * <pre>3 科研成果</pre>
     * <pre>5 课题情况</pre>
     */
    protected Integer fileOptType;
    /**
     * 支撑机构-科研课题信息-论文情况
     */
    protected TdZwSupportThesis supportThesis;
    protected List<TdZwSupportThesis> thesisList;
	protected int supportOptIndex;
    protected List<TsSimpleCode> publicationList;
    /**
     * 支撑机构-科研课题信息-专著情况
     */
    protected TdZwSupportMomograph supportMomograph;
    protected List<TdZwSupportMomograph> monographList;

	/**
	 * 科研成果情况-专利
	 */
	protected List<Object[]> patentList;

	/**
	 * 科研成果情况-标准
	 */
	protected List<Object[]> standardList;

	/**
	 * 科研成果情况-实体
	 */
	protected TdZwSupportPositive supportPositive;

	/**
	 * 完成人
	 */
	protected String empPsns;

	/**
	 * 专利类型
	 */
	protected List<TsSimpleCode> patentTypelist;
	protected HashMap<Integer,TsSimpleCode> patentTypeMap;
	/**
	 * 支撑机构-科研课题信息-课题情况
	 */
	protected TdZwSupportTopic supportTopic;
	protected List<TdZwSupportTopic> supportTopics1;
	protected List<TdZwSupportTopic> supportTopics2;
	protected List<TdZwSupportTopic> supportTopics3;
	//等级
	protected List<TsSimpleCode> prizeLeveList;

	/**
	 *是否显示其他专利类型
	 */
	protected boolean isShowPatentType=Boolean.FALSE;
	/**
	 *标准等级
	 */
	protected List<TsSimpleCode> standardTypelist;
	protected HashMap<Integer,TsSimpleCode> standardTypeMap;

	/**
	 * 支撑机构-先进适宜技术筛选推广应用
	 */
	protected TdZwSupportAdvance supportAdvance;
	protected List<TdZwSupportAdvance> advanceList;

	/**支撑机构-职业防治院-机构类型*/
	protected List<TsSimpleCode> unitTypeList;
	/**支撑机构-职业防治院-技术支撑能力*/
	protected List<TsSimpleCode> techTypeList;
	protected List<TechTypeVo> techTypesList;
	/**具备职业健康技术服务相关资质或能力的情况*/
	protected List<TsSimpleCode> qualTypeList1;
	protected List<TsSimpleCode> qualTypeList2;
	protected Map<Integer,TsSimpleCode> qualTypeMap;
	/**支撑机构-选中的技术支撑能力名称*/

	/**
	 * 支撑机构-科技攻关能力
	 */
	protected TdZwSupportTrc supportTrc;

	/**科室*/
	protected List<TsSimpleCode> OfficeList;
	/**建房标准*/
	protected List<TsSimpleCode> stdList;
	protected Map<Integer,TsSimpleCode> stdMap;
	protected List<TsSimpleCode> stdList1;
	protected Map<Integer,TsSimpleCode> stdMap1;

	/** 年份列表 */
	protected List<Integer> yearList;

	public ZwOrgInfoBaseZzsbBean() {

		this.itemsShows = new ArrayList<>();
		//设备类别
		svrList= this.commService.findSimpleCodesByTypeNo("5017");
		//放射机构服务项目
		fsList=commService.findallSimpleCodesByTypeIdOrderByNum("5019");
		//诊断服务项目
		zyList=commService.findallSimpleCodesByTypeIdOrderByNum("5020");
		//仪器类别
		acptList=commService.findallSimpleCodesByTypeId("5024");
		// 业务范围
		jsfwList=commService.findLevelSimpleCodesByTypeId("5320");

		// 服务项目小类
		smallServiceList = commService.findNumSimpleCodesByTypeId("5323");
		// 服务项目小类
		smalljcServiceList = commService.findNumSimpleCodesByTypeId("5527");

		//备案管理机构
		this.rcdOrgList = new ArrayList<>();
		this.rcdOrgList = this.commService.findLevelSimpleCodesByTypeId("5565");
		//机构类型
		this.unitTypeList=new ArrayList<>();
		this.unitTypeList=this.commService.findLevelSimpleCodesByTypeId("5604");

		//科室
		this.OfficeList=new ArrayList<>();
		this.OfficeList=this.commService.findLevelSimpleCodesByTypeId("5609");
		//建设标准
		this.stdList=new ArrayList<>();
		this.stdList1=new ArrayList<>();
		this.stdMap=new HashMap<>();
		this.stdMap1=new HashMap<>();
		this.stdList=this.commService.findLevelSimpleCodesByTypeId("5610");
		if(!CollectionUtils.isEmpty(stdList)){
			for (TsSimpleCode tsSimpleCode : stdList) {
				stdMap.put(tsSimpleCode.getRid(),tsSimpleCode);
			}
			stdList1 = ObjectCopyUtil.deepCopy(this.stdList);
			for (TsSimpleCode tsSimpleCode : stdList1) {
				stdMap1.put(tsSimpleCode.getRid(),tsSimpleCode);
			}
		}

		//具备职业健康技术服务相关资质或能力的情况
		List<TsSimpleCode> qualTypeList=new ArrayList<>();
		this.qualTypeList1=new ArrayList<>();
		this.qualTypeList2=new ArrayList<>();
		this.qualTypeMap=new HashMap<>();
		qualTypeList=this.commService.findLevelSimpleCodesByTypeId("5606");
		if(!CollectionUtils.isEmpty(qualTypeList)){
			for (TsSimpleCode tsSimpleCode : qualTypeList) {
				if("1".equals(tsSimpleCode.getExtendS3())){
					this.qualTypeList1.add(tsSimpleCode);
				}else if("2".equals(tsSimpleCode.getExtendS3())){
					this.qualTypeList2.add(tsSimpleCode);
				}else{
					this.qualTypeList1.add(tsSimpleCode);
					this.qualTypeList2.add(tsSimpleCode);
				}
				qualTypeMap.put(tsSimpleCode.getRid(),tsSimpleCode);
			}
		}

		this.rcdOrgMap = new HashMap<>();
		for (TsSimpleCode simpleCode : this.rcdOrgList) {
			this.rcdOrgMap.put(simpleCode.getRid(), simpleCode.getCodeName());
		}

		/*职业健康服务项目  小类*/
		for(TsSimpleCode simpleCode:jkList){
			jkMap.put(simpleCode.getCodeNo(),simpleCode);
			List<TsSimpleCode>  jkItems= commService.findallSimpleCodesByTypeIdAndExtends2("5527",simpleCode.getCodeNo());
			ZwJkItemPO zwJkItemPO=new ZwJkItemPO();
            zwJkItemPO.setJcItem(simpleCode);
            zwJkItemPO.setJcItemList(jkItems);
            jkItemList.add(zwJkItemPO);
			for(TsSimpleCode jkJc:jkItems){
				jkJcMap.put(jkJc.getRid(),jkJc);
			}

		}

		String curZoneGb = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2) + "00000000";
		List<TsZone> allTsZoneList = this.systemModuleService.findZoneListWithAllZoneByFlag(true,curZoneGb);
		this.zoneList = new ArrayList<>();
		if(!CollectionUtils.isEmpty(allTsZoneList)){
			for(TsZone tsZone : allTsZoneList){
				if(null != tsZone.getRealZoneType() && tsZone.getRealZoneType() >= 2 && tsZone.getRealZoneType() <= 5){
					this.zoneList.add(tsZone);
				}
			}
		}
		this.csProMap = Maps.newLinkedHashMap();
		if(null != jkList && jkList.size()>0){
			for(TsSimpleCode t:jkList){
				if("1".equals(t.getIfReveal().toString())){
					this.csProMap.put(t.getCodeName(),t.getCodeNo());
				}
			}
		}
		psnInfoBase = new TdzwPsnInfoZzsbBase();
		psnInfoBase.setValidPsnInfoId(false);
		TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
		psnInfoBase.setZwOrgType(this.zwOrgType);
		psnInfoBase.setPsnInfo(psninfo);
		psnInfoBase.setPsnZzlb(new TdZwPsnZzlbZzsb());

		itmDType ="1";
		this.zkCheckRst = PropertyUtils.getValueWithoutException("zzsb.zkCheckRst");
		String sIfHethFj = PropertyUtils.getValue("zzsb.ifHethFj");
		if("true".equals(sIfHethFj)){
			this.ifHethFj = true;
		}else{
			this.ifHethFj = false;
		}
		this.ifPersonSummary = PropertyUtils.getValue("zzsb.ifPersonSummary");
		if ("1".equals(ifPersonSummary)) {
			this.fillDesc = commService.findParamValue("PSNINFO_FILL_DESC");
		}
		String ifShowRcdOrgString = "";
		try {
			ifShowRcdOrgString = PropertyUtils.getValueWithoutException("zzsb.ifShowRcdOrg");
		} catch (Exception e) {
			e.printStackTrace();
		}
		this.ifShowRcdOrg = "1".equals(ifShowRcdOrgString);

		String ifZoneApproveString = "";
		try {
			ifZoneApproveString = PropertyUtils.getValueWithoutException("zzsb.ifZoneApprove");
		} catch (Exception e) {
			e.printStackTrace();
		}
		this.ifZoneApprove = "1".equals(ifZoneApproveString);
		this.allZoneApproveList = new ArrayList<>();

		this.outCarVersion = "";
		try {
			this.outCarVersion = StringUtils.objectToString(PropertyUtils.getValue("zzsb.outCarVersion"));
		} catch (Exception ignored) {
		}
		this.ifZoneRecord = PropertyUtils.getValueWithoutException("zzsb.ifZoneRecord");

		String hasResearchAndAcademiaInfoStr = PropertyUtils.getValueWithoutException("zzsb.hasResearchAndAcademiaInfo");
		this.hasResearchAndAcademicInfo = "1".equals(hasResearchAndAcademiaInfoStr);
		this.supportHealth = new TdZwSupportHealth();
		this.initZoneApproveTree();

		this.initPsnInfoForImport();
		this.initInstInfoForInport();
		this.initTechTypeTree();
	}
	/**
	 * <p>Description：初始化技术支撑能力 下拉单选 带层级 </p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	private void initTechTypeTree() {
		//技术支撑能力 树形
		this.techTypeList=new ArrayList<>();
		this.techTypesList=new ArrayList<>();

		// 初始化业务分类
		//this.techTypeTree = new DefaultTreeNode("root", null);
		this.techTypeList=this.commService.findSimpleCodeListOrderByLevelNumNo("5605");

		if (!CollectionUtils.isEmpty(techTypeList)) {
			// 只有第一层
			Set<String> firstLevelNoSet = new LinkedHashSet<String>();
			// 所有类别
			Map<String, TsSimpleCode> menuMap = new HashMap<String, TsSimpleCode>();
			for (TsSimpleCode t : techTypeList) {
				menuMap.put(t.getCodeLevelNo(), t);
				if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
					if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
						firstLevelNoSet.add(t.getCodeLevelNo());
					}
				}
			}
			// 由第一层开始遍历
			for (String ln : firstLevelNoSet) {
				TechTypeVo techTypeVo=new TechTypeVo();
				techTypeVo.setCodeName(menuMap.get(ln).getCodeName());
				techTypeVo.setSelNums(0);
				List<TsSimpleCode> techType=new ArrayList<>();
				for (TsSimpleCode tsSimpleCode : techTypeList) {
					if(tsSimpleCode.getCodeLevelNo().contains(ln+".")){
						techType.add(tsSimpleCode);
					}
				}
				techTypeVo.setTechTypes(techType);
				techTypesList.add(techTypeVo);
			}
		}
	}

	/**
	 * 构建类别树
	 *
	 * @param levelNo
	 *            类别层级编码
	 * @param levelNoSet
	 *            二级以及以上的菜单的类别编码集合
	 * @param menuMap
	 *            类别map
	 * @param parentNode
	 *            上级树节点
	 */
	private void addChildNode(String levelNo, Set<String> levelNoSet, Map<String, TsSimpleCode> menuMap,
							  TreeNode parentNode) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				TreeNode node = new DefaultTreeNode(menuMap.get(ln), parentNode);
				this.addChildNode(ln, levelNoSet, menuMap, node);
			}
		}
	}

	/**
	 *  <p>方法描述：质量控制结果初始化</p>
	 * @MethodAuthor hsj 2022-07-12 10:20
	 */
	private void initCheckRst() {
		zwCheckRstList = new ArrayList<>();
		//职业健康检查资质申报/职业病诊断资质申报
		if("1".equals(zkCheckRst) && ( zwOrgType == 0 || zwOrgType == 2 )){
			StringBuilder excuteSqlStr = new StringBuilder();
			excuteSqlStr.append(" SELECT T.EXPERT_LEADERS,T.EXPERT_MEMBERS,T.PROBLEMS,tsc.CODE_NAME AS CHECK_RST_NAME , T1.CODE_NAME AS ZG_RST_NAME ")
					.append(" ,T.CHECK_DATE,T2.UNITNAME ,T.NOTICE_FILE_PATH ,T.ZG_FILE_PATH FROM  TD_ZW_CHECK_RST T ")
					.append(" LEFT JOIN TS_SIMPLE_CODE tsc  ON T.CHECK_RST_ID = tsc.RID  ")
					.append(" LEFT JOIN TS_SIMPLE_CODE T1  ON T.ZG_RST_ID = T1.RID ")
					.append(" LEFT JOIN TS_UNIT T2 ON T.CHECK_UNIT_ID = T2.RID ")
					.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.CHECK_TYPE_ID = T3.RID ")
					.append(" WHERE T.STATE =1 AND T.DEL_MARK = 0 AND T.UNIT_ID = ").append(Global.getUser().getTsUnit().getRid());
			if(zwOrgType == 0){
				excuteSqlStr.append("AND T3.EXTENDS1 = 1") ;
			}else {
				excuteSqlStr.append("AND T3.EXTENDS1 = 2") ;
			}
			excuteSqlStr.append(" ORDER BY T.CHECK_DATE DESC,T.RID DESC");
			 this.zwCheckRstList = commService.getSqlList(excuteSqlStr.toString());
		}
	}

	public void initZoneApproveTree() {
		if (null == this.zoneApproveTree || this.zoneApproveTree.getChildCount() <= 0) {
			this.zoneApproveTree = new DefaultTreeNode("root", null);
			this.zoneApproveTree.setExpanded(true);
			String zoneCode = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0, 2);
			List<TsZone> list = this.commService.findZoneList(false, zoneCode, "2", "3");
			if (null != list && list.size() > 0) {
				Map<String, TreeNode> map = new HashMap<String, TreeNode>();
				TsZone top = list.get(0);

				Short topLvl = top.getZoneType();
				TreeNode temNode = new DefaultTreeNode(top, this.zoneApproveTree);
				temNode.setExpanded(true);
				map.put(top.getZoneCode(), temNode);

				if (list.size() > 1) {
					for (int i = 1; i < list.size(); i++) {
						TsZone t = list.get(i);
						if (topLvl.equals(t.getZoneType())) {
							TreeNode node = new DefaultTreeNode(t, this.zoneApproveTree);
							node.setExpanded(true);
							map.put(t.getZoneCode(), node);
						} else {
							String parentCode = ZoneUtil.getParentCode(t.getZoneCode());
							if (StringUtils.isNotBlank(parentCode)) {
								TreeNode parentNode = map.get(parentCode);
								if (null != parentNode) {
									TreeNode node = new DefaultTreeNode(t, parentNode);
									node.setExpanded(true);
									map.put(t.getZoneCode(), node);
								}
							}
						}
					}
				}
				map.clear();
			}
		}
	}

	public void hideZoneApproveAction() {
		// 遍历选择的危害因素，获取选择的id与名称
		this.selectZoneApproveName = null;
		this.selectZoneApproveRid = null;
		this.allZoneApproveList = new ArrayList<>();

		List<TreeNode> children = this.zoneApproveTree.getChildren();
		if(null != children && children.size()>0) {
			for(TreeNode node : children) {
				boolean flag = true;
				List<TreeNode> children2 = node.getChildren();
				if(null != children2 && children2.size()>0) {
					for(TreeNode node2 : children2) {
						if(!node2.isSelected()) {
							flag = false;
							break;
						}
					}
				}
				if(flag) {
					TsZone tsZone = (TsZone) node.getData();
					this.allZoneApproveList.add(tsZone);
				} else {
					if(null != allZoneApproveList && children2.size()>0) {
						for(TreeNode node2 : children2) {
							if(node2.isSelected()) {
								TsZone tsZone = (TsZone) node2.getData();
								this.allZoneApproveList.add(tsZone);
							}
						}
					}
				}
			}
		}

		if(!CollectionUtils.isEmpty(this.allZoneApproveList)) {
			StringBuilder nameSb = new StringBuilder();
			StringBuilder idSb = new StringBuilder();
			for(TsZone tsZone : this.allZoneApproveList) {
				nameSb.append("，").append(tsZone.getZoneName());
				idSb.append(",").append(tsZone.getRid().toString());
			}
			this.selectZoneApproveName = nameSb.substring(1);
			this.selectZoneApproveRid = idSb.substring(1);
		}
	}

	public void clearSelectZoneApproves() {
		this.selectZoneApproveName = null;
		this.selectZoneApproveRid = null;
		this.selectZoneApproves = null;
		this.allZoneApproveList = new ArrayList<>();
		List<TreeNode> children = this.zoneApproveTree.getChildren();
		if(null != children && children.size()>0) {
			for(TreeNode node : children) {
				node.setSelected(false);
				List<TreeNode> children2 = node.getChildren();
				if(null != children2 && children2.size()>0) {
					for(TreeNode node2 : children2) {
						node2.setSelected(false);
					}
				}
			}
		}
	}

	public void saveItemAction() {
		if (!psnInfoBase.beforeSave()) {
			return;
		}
		psnInfoBase.toSave();
		TdZwPsninfoZzsb tdZwPsninfo = new TdZwPsninfoZzsb();
		try {
			ObjectCopyUtil.copyProperties(psnInfoBase.getPsnInfo(), tdZwPsninfo);
		} catch (Exception e) {
			e.printStackTrace();
		}
        List<IZwOrgPsnsZzsb> zwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
        if (zwOrgPsns != null && zwOrgPsns.size() > 0) {
            for (IZwOrgPsnsZzsb t : zwOrgPsns) {
                if(t.getTdZwPsninfo().getRid().equals(tdZwPsninfo.getRid())){
                    t.setTdZwPsninfo(tdZwPsninfo);
                }
            }
        }
		initPsnType();
		searchPsnDAction();
		RequestContext currentInstance = RequestContext.getCurrentInstance();
		currentInstance.execute("PF('CodeEditDialog').hide()");
		currentInstance.update(":mainForm:tabView:psnDatatable");
		JsfUtil.addSuccessMessage("保存成功！");
	}

	/**
	 * @Description: 人员弹出框保存按钮调用
	 * 用于保存或更新资质人员以及资质人员与机构关系
	 *
	 * @MethodAuthor pw,2022年02月15日
	 */
	public void executeSavePsnInfo(){
		try{
			// 验证
			if (!this.psnInfoBase.beforeSave()) {
				return;
			}
			this.psnInfoBase.preSave();
			boolean ifSave = null == this.selectZwOrgPsn;
			if(ifSave){
				if (0 == this.zwOrgType) {
					this.selectZwOrgPsn = new TdZwTjorgpsnsZzsb();
				} else if (1 == this.zwOrgType) {
					this.selectZwOrgPsn = new TdZwSrvorgpsnsZzsb();
				} else if (2 == this.zwOrgType) {
					this.selectZwOrgPsn = new TdZwDiagpsnsZzsb();
				}else if(4 == this.zwOrgType){
					this.selectZwOrgPsn = new TdZwSupportPsnsZzsb();
				} else {
					this.selectZwOrgPsn = new TdZwOcchethPsnsZzsb();
				}
				this.selectZwOrgPsn.setZwOrginfo(zwOrginfoEntity);
				this.selectZwOrgPsn.setOnDuty("1");
			}
			//存储
			this.selectZwOrgPsn.setTdZwPsninfo(this.psnInfoBase.getPsnInfo());
			// 人员添加弹框中的保存操作时，更新MASTER_DATA_TIME字段为当前日期
			this.psnInfoBase.getPsnInfo().setMasterDataTime(new Date());
			this.psnPlanServiceImpl.saveOrUpdatePsninfoAndRelation(this.psnInfoBase.getPsnInfo(),
					this.psnInfoBase.getPsnTypeRows(), this.psnInfoBase.getZwOrgType(), this.selectZwOrgPsn);
			this.psnPlanServiceImpl.saveOrUpdatePsnZzlbAndRemoveList(this.psnInfoBase.getPsnInfo().getZzlbList(), this.removeZzlbList);
			this.psnInfoBase.afterSave();
			//保存完更新
			this.psnInfoBase.setOrgPsnInfoRid(this.selectZwOrgPsn.getRid());
			if(ifSave){
				List<IZwOrgPsnsZzsb> orgZwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
				if (null == orgZwOrgPsns) {
					orgZwOrgPsns = new ArrayList<IZwOrgPsnsZzsb>();
				}
				orgZwOrgPsns.add(this.selectZwOrgPsn);
				zwOrginfoEntity.setZwOrgPsns(orgZwOrgPsns);
			}
			//为了防止不关闭弹出框多次修改
			psnInfoEditAction();
			initPsnType();
			initPersonSummary();
			JsfUtil.addSuccessMessage("保存成功！");
		}catch(Exception e){
			e.printStackTrace();
			JsfUtil.addErrorMessage("保存失败");
		}
	}

	/**
	 * 添加人员
	 */
	public void addPsnInitAction() {
		List<IZwOrgPsnsZzsb> zwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
		selectPsntIds = null;
		if (zwOrgPsns != null && zwOrgPsns.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (IZwOrgPsnsZzsb t : zwOrgPsns) {
				sb.append(",").append(t.getTdZwPsninfo().getRid().toString());
			}
			selectPsntIds = sb.toString().substring(1);
		}
//		srcDList = zwIntellReportServiceImpl
//				.findTdZwPsnInfos(selectPsntIds, sessionData.getUser().getTsUnit().getRid());
		this.itmDType ="1";
		this.itmDName =null;
		this.itmDIdc =null;

		this.psnErrorList = new ArrayList<>();
		this.psnIfDownload = 0;
		searchPsnDAction();
	}

	/**
	 * 人员选择查询
	 *
	 * <p>修订内容：人员类型为非本单位时，身份证号不能为空</p>
	 * @MethodReviser qrr,2018年6月20日,searchPsnDAction
	 */
	public void searchPsnDAction() {
		if ("2".equals(itmDType)) {
			if (StringUtils.isBlank(itmDIdc)) {
				JsfUtil.addErrorMessage("请输入身份证号！");
				displayDList = new ArrayList<TdZwPsninfoZzsb>();
				return;
			}
		}
		displayDList = zwIntellReportServiceImpl
				.findTdZwPsnInfosByParam(itmDName,itmDIdc,itmDType, sessionData.getUser().getTsUnit().getRid(),this.zwOrgType);
		if (null!=displayDList && displayDList.size()>0) {
			for (TdZwPsninfoZzsb t : displayDList) {
				if(StringUtils.isNotBlank(selectPsntIds) &&
						this.selectPsntIds.contains(t.getRid().toString())){
					t.setIfChoose(false);
				}else{
					t.setIfChoose(true);
				}
			}
		}
	}
	/**
	 * <p>修订内容：与TdzwPsnInfoBase对应的页面公用初始化</p>
	 * @MethodReviser qrr,2018年5月23日,additmDAction
	 *
	 * <p>修订内容：赋值所属机构</p>
	 * @MethodReviser qrr,2018年6月27日,additmDAction
	 * */
	public void additmDAction() {
		this.removeZzlbList=new ArrayList<>();
		this.psnDialogType=1;
		this.selectZwOrgPsn = null;
		psnInfoBase = new TdzwPsnInfoZzsbBase();
		TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
		psninfo.setTsUnit(this.sessionData.getUser().getTsUnit());
		/**设置资质人员来源*/
		psninfo.setAddSource(zwOrgType);
		psnInfoBase.setZwOrgType(this.zwOrgType);
		psnInfoBase.setPsnInfo(psninfo);
		psnInfoBase.setValidPsnInfoId(false);
		psnInfoBase.initPsnInfo();
		psnInfoBase.initPsnZzlb();
	}

	/**
	 * 人员选择
	 *
	 * <p>修订内容：默认"在职"</p>
	 * @MethodReviser qrr,2018年6月22日,selectPsnAction
	 */
    public void selectPsnAction() {
        List<IZwOrgPsnsZzsb> orgZwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
        if (null == orgZwOrgPsns) {
            orgZwOrgPsns = new ArrayList<IZwOrgPsnsZzsb>();
        }

        IZwOrgPsnsZzsb zwPsn;
        if (0 == zwOrgType) {
            zwPsn = new TdZwTjorgpsnsZzsb();
        } else if (1 == zwOrgType) {
            zwPsn = new TdZwSrvorgpsnsZzsb();
        } else if (2 == zwOrgType) {
            zwPsn = new TdZwDiagpsnsZzsb();
        } else {
            zwPsn = new TdZwOcchethPsnsZzsb();
        }
        tdZwPsninfo.setTsUnit(this.sessionData.getUser().getTsUnit());
        tdZwPsninfo.setIfChoose(false);
        //选择的同时保存已选择的rids
        if(null!=selectPsntIds ){
            selectPsntIds = selectPsntIds+","+tdZwPsninfo.getRid();
        }else{
            selectPsntIds = String.valueOf(tdZwPsninfo.getRid());
        }
        zwPsn.setTdZwPsninfo(tdZwPsninfo);
        zwPsn.setZwOrginfo(zwOrginfoEntity);
        zwPsn.setOnDuty("1");
        orgZwOrgPsns.add(zwPsn);
        zwOrginfoEntity.setZwOrgPsns(orgZwOrgPsns);
        initPsnType();
    }

	public void deletePsnAction() {
		//人员刚选择 然后直接点删除后 关掉人员弹框保存或者提交 会异常 因为人员已经被删除 在保存的时候 关联那个人员rid失败 导致异常
		List<IZwOrgPsnsZzsb> orgZwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
		boolean flag = false;
		if(!CollectionUtils.isEmpty(orgZwOrgPsns)){
			for(IZwOrgPsnsZzsb psn : orgZwOrgPsns){
				if(null != psn.getTdZwPsninfo() && null != psn.getTdZwPsninfo().getRid()
						&& psn.getTdZwPsninfo().getRid().intValue() == tdZwPsninfo.getRid()){
					flag = true;
				}
			}
		}
		String msg = null;
		if(flag){
			msg = "该人员已被选择，无法删除！";
		}else{
			msg = zwIntellReportServiceImpl.deleteZwPsnInfo(tdZwPsninfo.getRid());
		}
		if (StringUtils.isNotBlank(msg)) {
			JsfUtil.addErrorMessage(msg);
		} else {
			JsfUtil.addSuccessMessage("删除成功！");
//			srcDList.remove(tdZwPsninfo);
			searchPsnDAction();
		}
	}

	/**
	 * 初始化申报信息方法
	 */
	protected void init() {
		// 获取类型
		zwOrgType = getOrgInfoType();
		// 初始化基础下拉集合
		initBaseList();
		// 初始化资质申报信息
		initReportData();
		// 初始化服务项目
		initServiceObj();
		// 初始化职务列表
		initCareerList();
		// 初始化资质附件
		initZwAnnex();
		// 初始化仪器
		initZwInst();
		// 初始检查项目
		initZwJcItems();
		// 初始化资质等级列表
		initLevelList();
		// 初始化外出地区
		//this.initZoneTree();
		//this.initZwOrgOutRangeZoneList();
		initPsnType();
		if ("1".equals(ifPersonSummary)) {
			initPersonSummary();
		}
		// 初始化质量管理体系制度集合 仅检查机构资质申报需要
		if(0 == zwOrgType){
			initInstitutionList();
		}
		if(ifHethFj){
			initOutcar();
		}
		//质量控制结果初始化
		this.initCheckRst();
		//科技攻关能力
		this.initSupportTrcInfo();
		//科研课题信息
		initResearchInfo();
		//初始化人员状态跟踪
		this.initPersonnelStateTracking();
	}

	/**
	 * <p>方法描述：初始化人员属性</p>
	 * @MethodAuthor qrr,2020年10月16日,initPsnType
	 * */
	public void initPsnType() {
		List<IZwOrgPsnsZzsb> zwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
        if (!CollectionUtils.isEmpty(zwOrgPsns)) {
			List<Integer> psnIds = new ArrayList<>();
			for (IZwOrgPsnsZzsb t : zwOrgPsns){
				TdZwPsninfoZzsb psnInfo = t.getTdZwPsninfo();
				if (null!=psnInfo && null!=psnInfo.getRid()){
					psnIds.add(psnInfo.getRid().intValue());
				}
			}

			Map<Integer,List<TsSimpleCode>> simpleCodeMap = new HashMap<>();// key TdZwPsninfo rid
            Map<Integer,List<TdZwZzlbRangeZzsb>> orgPsnRidsWithZzlbRangeMap = new HashMap<>();//key TD_ZW_TJORGPSNS rid
            Map<Integer,List<TdZwPsnZzlbZzsb>> orgPsnRidsWithCentNoMap = new HashMap<>();//key TD_ZW_TJORGPSNS rid
			if(!CollectionUtils.isEmpty(psnIds)){
				List<Object[]> resultList = new ArrayList<>();
				List<List<Integer>> psnIdList = StringUtils.splitListProxy(psnIds, 1000);
				StringBuilder excuteSqlStr = new StringBuilder();
				for(List<Integer> psnId : psnIdList){
					excuteSqlStr = new StringBuilder();
					excuteSqlStr.append(" SELECT T2.EXTENDS2,T2.EXTENDS3,T1.RID, T2.CODE_NAME,T2.RID AS CODERID,T.OTHER_PSN_TYPE FROM TD_ZW_PSN_TYPE T ")
							.append(" INNER JOIN TD_ZW_PSNINFO T1 ON T.MAIN_ID = T1.RID ")
							.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.PSN_TYPE ")
							.append(" WHERE T1.RID IN (:psnId)")
							.append(" AND T.ZZJG_TYPE = '").append(this.zwOrgType).append("' ");
					Map<String, Object> paramMap = new HashMap<>();
					paramMap.put("psnId", psnId);
					List<Object[]> results = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
					if(CollectionUtils.isEmpty(results)){
						continue;
					}
					resultList.addAll(results);
				}

				if(!CollectionUtils.isEmpty(resultList)){
					for(Object[] objArr : resultList){
						Integer rid = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
						String otherPsnType = null == objArr[5] ? null : objArr[5].toString();
						Integer codeRid = null == objArr[4] ? null : Integer.parseInt(objArr[4].toString());
						if(null == rid || null == codeRid){
							continue;
						}
						Integer extend2 = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
						String extend3 = null == objArr[1] ? null : objArr[1].toString();
						String codeName = null == objArr[3] ? null : objArr[3].toString();
						List<TsSimpleCode> simpleList = simpleCodeMap.get(rid.intValue());
						if(null == simpleList){
							simpleList = new ArrayList<>();
						}
						TsSimpleCode tsSimpleCode = new TsSimpleCode(codeRid);
						tsSimpleCode.setCodeName(codeName);
						tsSimpleCode.setExtendS2(extend2);
						tsSimpleCode.setExtendS3(extend3);
						tsSimpleCode.setExtendS4(otherPsnType);//将OtherPsnType 放到extends4字段中 由后边使用
						simpleList.add(tsSimpleCode);
						simpleCodeMap.put(rid.intValue(), simpleList);
					}
				}

                // 从事项目
				List<Object[]> rangeObjArrList = new ArrayList<>();
				for(List<Integer> psnId : psnIdList){
					excuteSqlStr = new StringBuilder();
					excuteSqlStr.append(" SELECT DISTINCT T.ZZ_RANGE_ID, T2.RID AS EMP_ID, T4.CODE_NO, T4.CODE_NAME ");
					excuteSqlStr.append(" FROM TD_ZW_ZZLB_RANGE T ");
					excuteSqlStr.append(" INNER JOIN TD_ZW_PSN_ZZLB T1 ON T.MAIN_ID = T1.RID ");
					excuteSqlStr.append(" INNER JOIN TD_ZW_PSNINFO T2 ON T1.PSN_ID = T2.RID ");
					excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T4 ON T.ZZ_RANGE_ID = T4.RID ");
					excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T5 ON T1.QUAL_ID = T5.RID ");
					excuteSqlStr.append(" WHERE T2.RID IN (:psnId)");
					excuteSqlStr.append(" AND (T5.CODE_DESC IS NULL OR T5.CODE_DESC LIKE '%").append(this.zwOrgType).append("%') ");
					excuteSqlStr.append(" GROUP BY T.ZZ_RANGE_ID, T2.RID, T4.CODE_NO, T4.CODE_NAME ");
					excuteSqlStr.append(" ORDER BY T2.RID, T4.CODE_NO ");
					Map<String, Object> paramMap = new HashMap<>();
					paramMap.put("psnId", psnId);
					List<Object[]> rangeObjs = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
					if(CollectionUtils.isEmpty(rangeObjs)){
						continue;
					}
					rangeObjArrList.addAll(rangeObjs);
                }
                if(!CollectionUtils.isEmpty(rangeObjArrList)) {
                    for(Object[] objArr : rangeObjArrList) {
                        Integer rangeId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                        Integer empId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                        String codeNo = null == objArr[2] ? null : objArr[2].toString();
                        String codeName = null == objArr[3] ? null : objArr[3].toString();
                        TdZwZzlbRangeZzsb zzlbRange = new TdZwZzlbRangeZzsb();
                        TsSimpleCode rangeSimpleCode = new TsSimpleCode(rangeId);
                        rangeSimpleCode.setCodeName(codeName);
                        rangeSimpleCode.setCodeNo(codeNo);
                        zzlbRange.setFkByZzRangeId(rangeSimpleCode);
                        List<TdZwZzlbRangeZzsb> zwZzlbRangeList = orgPsnRidsWithZzlbRangeMap.get(empId.intValue());
                        if(null == zwZzlbRangeList) {
                            zwZzlbRangeList = new ArrayList<>();
                        }
                        zwZzlbRangeList.add(zzlbRange);
                        orgPsnRidsWithZzlbRangeMap.put(empId.intValue(), zwZzlbRangeList);
                    }
                }
				// 证书编号
				List<Object[]> psnZzlbObjArrList = new ArrayList<>();
				for(List<Integer> psnId : psnIdList){
					excuteSqlStr = new StringBuilder();
					excuteSqlStr.append(" SELECT T.RID, T.PSN_ID, T.QUAL_ID, T.CENT_NO, T.SEND_DATE, T.VALID_DATE, T.AGEIN_SEND_DATE, T.AGEIN_VALID_DATE ");
					excuteSqlStr.append(" FROM TD_ZW_PSN_ZZLB T ");
					excuteSqlStr.append(" INNER JOIN TD_ZW_PSNINFO T1 ON T.PSN_ID = T1.RID ");
					excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.QUAL_ID = T2.RID ");
					excuteSqlStr.append(" WHERE T1.RID IN (:psnId)");
					excuteSqlStr.append(" AND (T2.CODE_DESC IS NULL OR T2.CODE_DESC LIKE '%").append(this.zwOrgType).append("%') ");
					excuteSqlStr.append(" ORDER BY T.SEND_DATE DESC ");
					Map<String, Object> paramMap = new HashMap<>();
					paramMap.put("psnId", psnId);
					List<Object[]> psnZzlbObjs = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
					if(CollectionUtils.isEmpty(psnZzlbObjs)){
						continue;
					}
					psnZzlbObjArrList.addAll(psnZzlbObjs);
                }
                if(!CollectionUtils.isEmpty(psnZzlbObjArrList)) {
                    for(Object[] objArr : psnZzlbObjArrList) {
                        Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                        Integer psnId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                        Integer qualId = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
                        String codeNo = null == objArr[3] ? null : objArr[3].toString();
                        TdZwPsnZzlbZzsb tdZwPsnZzlb = new TdZwPsnZzlbZzsb(rid);
                        tdZwPsnZzlb.setFkByPsnId(new TdZwPsninfoZzsb(psnId));
                        TsSimpleCode tsSimpleCode = new TsSimpleCode(qualId);
                        tsSimpleCode.setCodeDesc(this.zwOrgType.toString());
                        tdZwPsnZzlb.setFkByQualId(tsSimpleCode);
                        tdZwPsnZzlb.setCentNo(codeNo);
                        List<TdZwPsnZzlbZzsb> zwPsnZzlbList = orgPsnRidsWithCentNoMap.get(psnId.intValue());
                        if(null == zwPsnZzlbList) {
                            zwPsnZzlbList = new ArrayList<>();
                        }
                        zwPsnZzlbList.add(tdZwPsnZzlb);
                        orgPsnRidsWithCentNoMap.put(psnId.intValue(), zwPsnZzlbList);
                    }
                }
			}
			for (IZwOrgPsnsZzsb t : zwOrgPsns) {
				TdZwPsninfoZzsb psnInfo = t.getTdZwPsninfo();
				//给人员属性赋值
				if (null!=psnInfo && null!=psnInfo.getRid()) {
					List<TsSimpleCode> simpleList = simpleCodeMap.get(psnInfo.getRid().intValue());
					if(!CollectionUtils.isEmpty(simpleList)){
						StringBuilder sb = new StringBuilder();
						for(TsSimpleCode psnType : simpleList){
							sb.append("，").append(psnType.getCodeName());
							if (null!=psnType.getExtendS2() && 1==psnType.getExtendS2()) {
								if ("1".equals(psnInfo.getIfCheckExpert())) {
									sb.append("（鉴定专家）");
								}
							}
							if ("4".equals(psnType.getExtendS3())) {
								//注意 查询的时候将OtherPsnType 放到extends4字段中 避免实例化多个类型的对象
								if (StringUtils.isNotBlank(psnType.getExtendS4())) {
									sb.append("（").append(psnType.getExtendS4()).append("）");
								}
							}
						}
						psnInfo.setPsnTypes(sb.substring(1));
					}else{
						psnInfo.setPsnTypes(null);
					}

					// 从事项目
                    List<TdZwZzlbRangeZzsb> zwZzlbRanges = orgPsnRidsWithZzlbRangeMap.get(psnInfo.getRid().intValue());
                    if(!CollectionUtils.isEmpty(zwZzlbRanges)) {
                        StringBuilder name = new StringBuilder();
                        for(TdZwZzlbRangeZzsb items : zwZzlbRanges) {
                            name.append("，").append(items.getFkByZzRangeId().getCodeName());
                        }
                        psnInfo.setZzlbRange(name.deleteCharAt(0).toString());
                    }else{
						psnInfo.setZzlbRange(null);
					}

                    // 证书编号
                    List<TdZwPsnZzlbZzsb> zwPsnZzlbList = orgPsnRidsWithCentNoMap.get(psnInfo.getRid().intValue());
                    if(!CollectionUtils.isEmpty(zwPsnZzlbList)) {
                        StringBuilder name = new StringBuilder();
                        for(TdZwPsnZzlbZzsb items : zwPsnZzlbList) {
                            name.append(",").append(items.getCentNo());
                        }
                        psnInfo.setCertNoStr(name.deleteCharAt(0).toString());
                    }else{
						psnInfo.setCertNoStr(null);
					}
				}
			}
		}
	}

	private void initPersonSummary() {
		this.personSummaryList = new ArrayList<>();
		if (null!=zwOrginfoEntity.getRid() && 0==zwOrgType) {
			List<Object[]> list = zwIntellReportServiceImpl.findPsnTypeSummary(zwOrgType, zwOrginfoEntity.getRid());
			if (!CollectionUtils.isEmpty(list)) {
				List<String> row = new ArrayList<>();
				int i = 0;
				for (Object[] obj : list) {
					if (i%7==0) {
						row = new ArrayList<>();
						this.personSummaryList.add(row);
					}
					row.add(obj[1]+"："+obj[2]+"人");
					i++;
				}
			}
		}
	}
	/**
	 * 初始化检查项目页面展示集合
	 */
	public void initZwJcItems() {
		showItemList = new ArrayList<Object[]>();
		String jcItems = zwOrginfoEntity.getJcItems();
		if (StringUtils.isNotBlank(jcItems)) {
			// 初始化选择检查项目
			this.initJcItemList();
			// 封装页面展示集合
			if (diagMap != null) {
				Iterator<TsSimpleCode> iterator = diagMap.keySet().iterator();
				// 序号
				int xh = 1;
				while (iterator.hasNext()) {
					// 存放子集数据
					List<String[]> subList = new ArrayList<String[]>();
					TsSimpleCode next = iterator.next();
					List<TsSimpleCode> list = diagMap.get(next);
					if (null != list && list.size() > 0) {
						for (TsSimpleCode tscode : list) {
							String rid = tscode.getRid().toString();
							if (StringUtils.contains("," + jcItems + ",", "," + rid + ",")) {// 如当前项目被选中，则展示集合中增加该元素
								String[] str=new String[2];
								str[0]=tscode.getCodeName();
								str[1]=rid;
								subList.add(str);
							}
						}
						if (subList.size() > 0) {
							for (int i = 0; i < subList.size(); i++) {
								String[] str = subList.get(i);
								if(null != zwOrginfoEntity.getZwItems() && zwOrginfoEntity.getZwItems().size()>0){
									for(IZwItemsZzsb item:zwOrginfoEntity.getZwItems()){
										if(null != str[1] && str[1].toString().equals(item.getTsSimpleCode().getRid().toString())){
											if(null != item.getIsOutter() && item.getIsOutter().intValue()==1){
												showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),true,str[1].toString() });
												continue;
											}else{
												showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),false,str[1].toString() });
												continue;
											}
										}
									}
								}else{
									showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),false,str[1].toString() });
								}
								xh++;
							}
							subList.clear();
						}
					}
				}
			}
		}
	}

	/**
	 * 检查项目添加
	 */
	public void addItemInitAction() {
		this.initJcItemList();
	}

	/**
	 * 初始化检查项目
	 */
	private void initJcItemList() {
        if (ObjectUtil.isEmpty(this.diagList)) {
			this.diagList = this.commService.findLevelSimpleCodesByTypeId("5015");
        }
			// 选中的检查项目ids
			String jcItems = zwOrginfoEntity.getJcItems();
        if (ObjectUtil.isEmpty(diagList)) {
            return;
        }
				// 页面展示码表Map
        diagMap = new LinkedHashMap<>();
        for (TsSimpleCode code : diagList) {
					if (!StringUtils.contains(code.getCodeLevelNo(), ".")) {
						diagMap.put(code, new ArrayList<TsSimpleCode>());
					}
				}
        for (TsSimpleCode parentCode : diagMap.keySet()) {
					// 父级编码
					String parentC = parentCode.getCodeNo();
					// 清空选择和子集合
					parentCode.setIfSelected(false);
					parentCode.setSelCodeRids(null);
					// 选中的Id
            List<Integer> selList = new ArrayList<>();
            for (TsSimpleCode code : diagList) {
                if (!code.getCodeLevelNo().startsWith(parentC + ".")) {
                    continue;
                }
							diagMap.get(parentCode).add(code);
							if (StringUtils.isNotBlank(jcItems)
									&& StringUtils.contains("," + jcItems + ",", "," + code.getRid() + ",")) {
								selList.add(code.getRid());
							}
						}

            if (ObjectUtil.isEmpty(selList)) {
                continue;
					}
						String[] rids = new String[selList.size()];
						for (int i = 0; i < selList.size(); i++) {
							rids[i] = String.valueOf(selList.get(i));
						}
						parentCode.setSelCodeRids(rids);
            parentCode.setIfSelected(rids.length == diagMap.get(parentCode).size());
			}
	}

	/**
	 * 选中检查项目
	 */
	public void selectItem() {
		Iterator<TsSimpleCode> it = diagMap.keySet().iterator();
		while (it.hasNext()) {
			// 父级编码
			TsSimpleCode parentCode = it.next();
			if (parentCode.getRid().toString().equals(selItemId)) {
				List<TsSimpleCode> list = diagMap.get(parentCode);
				String[] selRids = new String[list.size()];
				int i = 0;
				for (TsSimpleCode sc : list) {
					if (parentCode.isIfSelected()) {
						selRids[i] = sc.getRid().toString();
						i++;
					}
				}
				parentCode.setSelCodeRids(selRids);
				return;
			}
		}
	}

	/**
	 * 检查项目保存
	 */
	public void checkItemSaveAction() {
		// 保存检查项目时，进行处理检查项目
		this.handleTjItemSave();
		this.initZwJcItems();
	}

	/**
	 * <p>方法描述：有无资质机构备案审核流程 为1时，提示</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-04-16
	 **/
	public Boolean verifyPoint(){
		if(this.ifReviewProcess&&this.zwOrgType==0){
			List<Object[]> obj= zwIntellReportServiceImpl.findOrgInfoApply();
			if(!CollectionUtils.isEmpty(obj)){
				if(obj.get(0)[1]==null){
					JsfUtil.addErrorMessage("备案申请流程状态异常！");
					return true;
				}else{
					if(this.ifZzsbSave&&("0".equals(obj.get(0)[1].toString())||"1".equals(obj.get(0)[1].toString())||"3".equals(obj.get(0)[1].toString()))){
						JsfUtil.addErrorMessage("备案申请流程进行中，不能修改！");
						return true;
					}
				}
			}
		}
		return false;
	}

	/**
	 * 人员从事科室选择方法
	 */
	public void selWordOfficeAction() {
		workOffices = null;
		List<TdZwWorkdeptZzsb> tdZwWorkdepts = selectZwOrgPsn.getTdZwWorkdepts();
		if (null != tdZwWorkdepts && tdZwWorkdepts.size() > 0) {
			workOffices = new String[tdZwWorkdepts.size()];
			for (int i = 0; i < tdZwWorkdepts.size(); i++) {
				workOffices[i] = String.valueOf(tdZwWorkdepts.get(i).getTsSimpleCode().getRid());
			}
		}
	}

	/**
	 * 从事科室选择保存方法
	 */
	public void workOfficeSaveAction() {
		List<TdZwWorkdeptZzsb> tdZwWorkdepts = new ArrayList<TdZwWorkdeptZzsb>();
		selectZwOrgPsn.setWordOfficeName(null);
		if (null != workOffices && workOffices.length > 0) {
			// 科室名称
			StringBuilder names = new StringBuilder();
			for (String s : workOffices) {
				TdZwWorkdeptZzsb tdZwWorkdept = new TdZwWorkdeptZzsb();
				tdZwWorkdept.setTsSimpleCode(new TsSimpleCode(Integer.valueOf(s)));
				tdZwWorkdept.setTdZwTjorgpsns((TdZwTjorgpsnsZzsb) selectZwOrgPsn);
				tdZwWorkdept.setCreateDate(new Date());
				tdZwWorkdept.setCreateManid(sessionData.getUser().getRid());
				tdZwWorkdepts.add(tdZwWorkdept);
				names.append("，").append(workOfficeMap.get(s));
			}
			selectZwOrgPsn.setWordOfficeName(names.substring(1));
		}
		selectZwOrgPsn.setTdZwWorkdepts(tdZwWorkdepts);
	}

	/**
	 * 人员从事项目选择方法
	 */
	public void selCsProAction(){
		this.selectCsPro = null;
		String wi = this.selectZwOrgPsn.getWordItems();
		if(StringUtils.isNotBlank(wi)) {
			this.selectCsPro = wi.split(",");
		}
	}

	/**
	 * 从事项目选择保存方法
	 */
	public void csProSaveAction(){
		this.selectZwOrgPsn.setWordItems(null);
		this.selectZwOrgPsn.setWordItemStr(null);

		if(null != this.selectCsPro && this.selectCsPro.length > 0) {
			StringBuilder ids = new StringBuilder();
			StringBuilder titles = new StringBuilder();
			for(String s : this.selectCsPro) {
				ids.append(",").append(s);
				titles.append(",").append(MapUtils.findKey(this.csProMap, s));
			}

			this.selectZwOrgPsn.setWordItems(ids.toString().replaceFirst(",", ""));
			this.selectZwOrgPsn.setWordItemStr(titles.toString().replaceFirst(",", ""));
		}else{
			this.selectZwOrgPsn.setWordItems(null);
			this.selectZwOrgPsn.setWordItemStr(null);
		}
	}

	/**
	 * 初始化页面下拉集合
	 */
	public void initBaseList() {
		List<TsSimpleCode> list = commService.findSimpleCodesByTypeId("5014");
		if (list != null && list.size() > 0) {
			workOfficeList = list;
			for (TsSimpleCode code : workOfficeList) {
				workOfficeMap.put(code.getRid().toString(), code.getCodeName());
			}
		}
	}

	/**
	 * @Description: 专业资质类型保存和更新
	 * 操作完更新人员列表页面内的信息 如果用remoteCommand方式 会导致保存成功的提示信息被刷新掉
	 *
	 * @MethodAuthor pw,2022年02月19日
	 */
	public void executeSaveZzlbAction() throws Exception {
		this.psnInfoBase.saveZzlbAction(psnInfoForm);
		this.initPsnType();

	}

	/**
	 * @Description: 专业资质类型删除
	 *
	 * @MethodAuthor pw,2022年02月19日
	 */
	public void executeZzlbDelAction(){
		if(!removeZzlbList.contains(this.psnInfoBase.getPsnZzlb())){
			this.removeZzlbList.add(this.psnInfoBase.getPsnZzlb());
		}
		this.psnInfoBase.zzlbDelAction();
		this.initPsnType();
	}
	/**
	 * 初始化资质申报信息
	 */
	private void initReportData() {
		initReportData(zwOrgType,1);
		// 资质等级
		if(null == zwOrginfoEntity.getFkByLevelId()) {
			zwOrginfoEntity.setFkByLevelId(new TsSimpleCode());
		}
		if(null == zwOrginfoEntity.getFkByUnitTypeId()) {
			zwOrginfoEntity.setFkByUnitTypeId(new TsSimpleCode());
		}
		if(zwOrginfoEntity.getRid()==null){
			this.ifZzsbSave=false;
		}else{
			this.ifZzsbSave=true;
		}

		//初始化赋值批准的执业区域
		List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges = zwOrginfoEntity.getTdZwTjorgOutRanges();
		List<String> zoneApproveNameList = new ArrayList<>();
		List<String> zoneApproveRidList = new ArrayList<>();
		if (!CollectionUtils.isEmpty(tdZwTjorgOutRanges)) {
			for (TdZwTjorgOutRangeZzsb tdZwTjorgOutRange : tdZwTjorgOutRanges) {
				TsZone fkByZoneId = tdZwTjorgOutRange.getFkByZoneId();
				zoneApproveNameList.add(fkByZoneId.getZoneName());
				zoneApproveRidList.add(fkByZoneId.getRid().toString());
			}
			if (!CollectionUtils.isEmpty(zoneApproveNameList)) {
				this.selectZoneApproveName = StringUtils.list2string(zoneApproveNameList, "，");
			}
			if (!CollectionUtils.isEmpty(zoneApproveRidList)) {
				this.selectZoneApproveRid = StringUtils.list2string(zoneApproveRidList, ",");
				List<TreeNode> children = this.zoneApproveTree.getChildren();
				if(null != children && children.size()>0) {
					for (TreeNode node : children) {
						TsZone zone = (TsZone) node.getData();
						boolean selectAll = false;
						if (zoneApproveRidList.contains(zone.getRid().toString())) {
							node.setSelected(true);
							selectAll = true;
						}
						List<TreeNode> children1 = node.getChildren();
						if(null != children1 && children1.size()>0) {
							for (TreeNode node1 : children1) {
								TsZone zone1 = (TsZone) node1.getData();
								if (zoneApproveRidList.contains(zone1.getRid().toString()) || selectAll) {
									node1.setSelected(true);
								}
							}
						}
					}
				}
			}
		}
	}

	private void initServiceObj() {
		if(zwOrgType.equals(0)){
			initServiceObjList(jkList);
			initService(jkList);
			initSerItem(this.jkItemList);
			this.jkItemRowList = convertZwJkItemPOToRowPO(jkItemList);
			changeSerItmDetail();
		}else if(zwOrgType.equals(1)){
			initServiceObjList(fsList, smallServiceList);
			initFsService();
			//initService(fsList);
		}else if(zwOrgType.equals(2)){
			initServiceObjList(zyList);
			initService(zyList);
		}else if(zwOrgType.equals(3)){
			initServiceObjList(jsfwList);
			initService(jsfwList);
		}
	}


	private void initZwInst() {
		if (zwOrginfoEntity.getZwInst() != null) {
			zwInstInfoList = zwOrginfoEntity.getZwInst();
		} else {
			zwInstInfoList = new ArrayList<IZwInstZzsb>();
		}
	}

	private void initZwAnnex() {
		zwAnnexList1 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList2 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList3 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList4 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList5 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList6 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList7 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList8 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList9 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList20 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();

		List<TdZwJsffjgCommAnnexZzsb> list = this.zwIntellReportServiceImpl.findTdZwJsffjgCommAnnexListByOrgId(this.zwOrginfoEntity.getTsUnit().getRid().toString());
		if(list != null && list.size() > 0) {
			for(TdZwJsffjgCommAnnexZzsb annex : list) {
				if(null != annex.getRegZoneId()) {
					TsZone tsZone = this.zwIntellReportServiceImpl.find(TsZone.class, annex.getRegZoneId());
					if(null != tsZone) {
						annex.setFkByRegZoneId(tsZone);
					}
				}
				if (annex.getAnnexType().equals(1)) {
					zwAnnexList1.add(annex);
				} else if(annex.getAnnexType().equals(2)){
					zwAnnexList2.add(annex);
				}else if(annex.getAnnexType().equals(3)){
					zwAnnexList3.add(annex);
				}else if(annex.getAnnexType().equals(4)){
					zwAnnexList4.add(annex);
				}else if(annex.getAnnexType().equals(5)){
					zwAnnexList5.add(annex);
				}else if(annex.getAnnexType().equals(6)){
					zwAnnexList6.add(annex);
				}else if(annex.getAnnexType().equals(7)){
					zwAnnexList7.add(annex);
				}else if(annex.getAnnexType().equals(8)){
					zwAnnexList8.add(annex);
				}else if(annex.getAnnexType().equals(11)){
					zwAnnexList9.add(annex);
				}else if(annex.getAnnexType().equals(20)){
					zwAnnexList20.add(annex);
				}
			}
		}
	}


	/**
	 * 传入类型
	 *
	 * @return
	 */
	public abstract Integer getOrgInfoType();

	public boolean verifyBaseInfo() {
		if (zwOrginfoEntity.getFirstGetday() == null) {
			JsfUtil.addErrorMessage("初次取得资质时间不允许为空！");
			return true;
		}
		// 初次取得资质时间
		Date firstGetday = zwOrginfoEntity.getFirstGetday();
		if (firstGetday.after(new Date())) {
			JsfUtil.addErrorMessage("初次取得资质时间不允许超过当前日期！");
			return true;
		}
		return false;
	}
	/**
	 * <p>方法描述：保存前验证</p>
	 * @MethodAuthor qrr,2018年8月27日,veryBeforeSave
	 * */
	private boolean veryBeforeSave(){
		boolean flag = false;
		if (3==this.zwOrgType.intValue()) {
			if (null == zwOrginfoEntity.getFkByUnitTypeId()
					|| null == zwOrginfoEntity.getFkByUnitTypeId().getRid()) {
				JsfUtil.addErrorMessage("请选择单位类型！");
				zwOrginfoEntity.setState((short) 0);
				return true;
			}
		}
		if (veryBaseInfo(this.zwOrgType)) {
			flag = true;
		}
		if (this.zwOrgType == 0 && this.ifZoneApprove && StringUtils.isBlank(this.selectZoneApproveRid)) {
			JsfUtil.addErrorMessage("批准的执业区域不允许为空！");
			flag = true;
		}
		if (null!=zwOrginfoEntity.getZwOrgPsns()
				&& zwOrginfoEntity.getZwOrgPsns().size()>0) {
			for (IZwOrgPsnsZzsb t : zwOrginfoEntity.getZwOrgPsns()) {
				if (StringUtils.isBlank(t.getOnDuty())) {
					JsfUtil.addErrorMessage("请选择"+t.getTdZwPsninfo().getEmpName()+"的在岗状态！");
					zwOrginfoEntity.setState((short) 0);
					return true;
				}
			}
		}
		//若资质人员为“在职”验证资质人员是否在其他单位且“在职”
		if (null!=zwOrginfoEntity.getZwOrgPsns()
				&& zwOrginfoEntity.getZwOrgPsns().size()>0) {
			for (IZwOrgPsnsZzsb t : zwOrginfoEntity.getZwOrgPsns()) {
				if ("1".equals(t.getOnDuty())) {
					StringBuffer sb = new StringBuffer();
					sb.append("select 1 from TD_ZW_PSNINFO t ");
					sb.append(" where t.rid =").append(t.getTdZwPsninfo().getRid());
					sb.append(" and (exists(select 1 from TD_ZW_TJORGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid ");
					sb.append(" and t.org_id != ").append(this.sessionData.getUser().getTsUnit().getRid()).append(")");
					sb.append(" or exists(select 1 from TD_ZW_SRVORGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid ");
					sb.append(" and t.org_id != ").append(this.sessionData.getUser().getTsUnit().getRid()).append(")");
					sb.append(" or exists(select 1 from TD_ZW_DIAGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid ");
					sb.append(" and t.org_id != ").append(this.sessionData.getUser().getTsUnit().getRid()).append(")");

					//加入职业卫生技术服务机构人员与支撑机构人员
					sb.append(" or exists(select 1 from TD_ZW_OCCHETH_PSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid ");
					sb.append(" and t.org_id != ").append(this.sessionData.getUser().getTsUnit().getRid()).append(")");
					sb.append(" or exists(select 1 from TD_ZW_SUPPORT_PSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid ");
					sb.append(" and t.org_id != ").append(this.sessionData.getUser().getTsUnit().getRid()).append(")");
					sb.append(")");
					int count = zwIntellReportServiceImpl.findCountBySql(sb.toString());
					if (count>0) {
						JsfUtil.addErrorMessage(t.getTdZwPsninfo().getEmpName()+"在其他单位中存在在职的情况，无法保存！");
						zwOrginfoEntity.setState((short) 0);
						return true;
					}
				}
			}
		}
		return flag;
	}

	/**
	 * 保存方法
	 *
	 * <p>修订内容：若资质人员为“在职”，验证资质人员是否在其他单位且“在职”</p>
	 * @MethodReviser qrr,2018年6月25日,saveAction
	 */
	public void saveAction() {
		if (verifyPoint()||veryBeforeSave()||veryOtherBeforeSave() || verifySupportChk(false)) {
			return;
		}
		if(0 == zwOrgType && institutionSaveValidate()){ //检查机构资质申报质量管理体系保存验证
			return;
		}
		this.ifZzsbSave=true;
		toSave();
		JsfUtil.addSuccessMessage("保存成功！");
	}
	/**
	 * <p>Description：验证 基本信息 职业病因素监测开展情况，场所建设  </p>
	 * <p>Author： yzz 2023-10-25 </p>
	 */
	private boolean verifySupportChk(boolean flag) {
		if(StringUtils.isBlank(this.unitType) && this.zwOrgType!=4){
			return false;
		}
		boolean bool=false;
		if(flag && (this.zwOrginfoEntity.getFkByTypeId()==null || this.zwOrginfoEntity.getFkByTypeId().getRid()==null)&& "2".equals(this.unitType)){
			JsfUtil.addErrorMessage("基本信息-请选择机构类型！");
			bool=true;
		}
		if(flag && !CollectionUtils.isEmpty(this.techTypeList)){
			boolean bool2=false;
			for (TsSimpleCode tsSimpleCode : this.techTypeList) {
				if(tsSimpleCode.isIfSelected()){
					bool2=true;
					break;
				}
			}
			if(!bool2){
				JsfUtil.addErrorMessage("基本信息-请选择技术支撑能力！");
				bool=true;
			}
		}
		boolean bool1=false;
		if(flag && "1".equals(this.unitType) && !CollectionUtils.isEmpty(this.qualTypeList1)){
			for (TsSimpleCode tsSimpleCode : qualTypeList1) {
				if(tsSimpleCode.isIfSelected()){
					bool1=true;
					break;
				}
			}
		}else if(flag &&"2".equals(this.unitType) && !CollectionUtils.isEmpty(this.qualTypeList2)){
			for (TsSimpleCode tsSimpleCode : qualTypeList2) {
				if(tsSimpleCode.isIfSelected()){
					bool1=true;
					break;
				}
			}
		}
		if(flag &&!bool1){
			JsfUtil.addErrorMessage("基本信息-请选择具备职业健康技术服务相关资质或能力的情况！");
			bool=true;
		}

		//验证职业病因素监测开展情况
		if(flag && CollectionUtils.isEmpty(zwOrginfoEntity.getSupportChkList())){
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("基本信息-至少有一条职业病因素监测开展情况！");
			bool=true;
		}

		for (int i = 0; i < zwOrginfoEntity.getSupportChkList().size(); i++) {
			TdZwSupportChk supportChk = zwOrginfoEntity.getSupportChkList().get(i);
			if(flag && supportChk.getStartDate()==null){
				JsfUtil.addErrorMessage("基本信息-第"+(i+1)+"行监测周期开始日期不能为空！");
				bool=true;
			}
			if(flag && supportChk.getEndDate()==null){
				JsfUtil.addErrorMessage("基本信息-第"+(i+1)+"行监测周期结束日期不能为空！");
				bool=true;
			}
			if(supportChk.getStartDate()!=null && supportChk.getEndDate()!=null && supportChk.getStartDate().after(supportChk.getEndDate())){
				JsfUtil.addErrorMessage("基本信息-第"+(i+1)+"行监测周期开始日期应小于等于结束日期！");
				bool=true;
			}
			if(flag && supportChk.getCrptNum()==null){
				JsfUtil.addErrorMessage("基本信息-第"+(i+1)+"行累计开展职业病危害因素监测企业数（家）不能为空！");
				bool=true;
			}
		}
		//验证财政投入情况
		if(flag && "2".equals(this.unitType) && CollectionUtils.isEmpty(zwOrginfoEntity.getSupportFundList())){
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("基本信息-至少有一条财政投入情况！");
			bool=true;
		}
		if("2".equals(this.unitType)  && !CollectionUtils.isEmpty(zwOrginfoEntity.getSupportFundList())){
			for (int i = 0; i < zwOrginfoEntity.getSupportFundList().size(); i++) {
				TdZwSupportFund supportFund = zwOrginfoEntity.getSupportFundList().get(i);
				if(flag && supportFund.getYear()==null){
					JsfUtil.addErrorMessage("基本信息-第"+(i+1)+"行年份不能为空！");
					bool=true;
				}
				if(flag && supportFund.getFund()==null){
					JsfUtil.addErrorMessage("基本信息-第"+(i+1)+"行投入情况（万元）不能为空！");
					bool=true;
				}
				if(supportFund.getFund()!=null && supportFund.getFund().compareTo(BigDecimal.ZERO)==0){
					JsfUtil.addErrorMessage("基本信息-第"+(i+1)+"行投入情况（万元）应大于0！");
					bool=true;
				}
			}
		}

		//场所建设 验证
		if(flag && zwOrginfoEntity.getRid()!=null && zwOrginfoEntity.getSupportPlaces().getIfZwLab()==null){
			JsfUtil.addErrorMessage("场所建设-是否设有职业卫生检测分析实验室不能为空！");
			bool=true;
		}
		if(flag && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfZwLab()) && zwOrginfoEntity.getSupportPlaces().getZwLabArea()==null){
			JsfUtil.addErrorMessage("场所建设-是否设有职业卫生检测分析实验室为“是”时，实验室建筑面积（平方米）不能为空！");
			bool=true;
		}
		if(flag && zwOrginfoEntity.getRid()!=null && zwOrginfoEntity.getSupportPlaces().getIfFsLab()==null){
			JsfUtil.addErrorMessage("场所建设-是否设有放射卫生检测分析实验室不能为空！");
			bool=true;
		}
		if(flag && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfFsLab()) && zwOrginfoEntity.getSupportPlaces().getFsLabArea()==null){
			JsfUtil.addErrorMessage("场所建设-是否设有放射卫生检测分析实验室为“是”时，实验室建筑面积（平方米）不能为空！");
			bool=true;
		}
		if(flag && zwOrginfoEntity.getRid()!=null && zwOrginfoEntity.getSupportPlaces().getIfScienTask()==null){
			JsfUtil.addErrorMessage("场所建设-是否承担教学和科研任务不能为空！");
			bool=true;
		}
		if(flag && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfScienTask()) && zwOrginfoEntity.getSupportPlaces().getTeachArea()==null){
			JsfUtil.addErrorMessage("场所建设-是否承担教学和科研任务为“是”时，教学用房（平方米）不能为空！");
			bool=true;
		}
		if(flag && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfScienTask()) && zwOrginfoEntity.getSupportPlaces().getScienArea()==null){
			JsfUtil.addErrorMessage("场所建设-是否承担教学和科研任务为“是”时，科研用房（平方米）不能为空！");
			bool=true;
		}
		if(flag && zwOrginfoEntity.getRid()!=null && zwOrginfoEntity.getSupportPlaces().getIfTj()==null){
			JsfUtil.addErrorMessage("场所建设-是否设有职业健康检查中心不能为空！");
			bool=true;
		}
		if(flag && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfTj()) && zwOrginfoEntity.getSupportPlaces().getTjLabArea()==null){
			JsfUtil.addErrorMessage("场所建设-是否设有职业健康检查中心为“是”时，工作用房建筑总面积（平方米）不能为空！");
			bool=true;
		}

		if(flag && zwOrginfoEntity.getRid()!=null && zwOrginfoEntity.getSupportPlaces().getIfDiag()==null){
			JsfUtil.addErrorMessage("场所建设-"+("1".equals(this.unitType)?"是否开展职业病诊断工作不能为空！":"是否开展职业病诊断救治工作不能为空！"));
			bool=true;
		}
		if(flag && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfDiag()) && zwOrginfoEntity.getSupportPlaces().getDiagLabArea()==null){
			JsfUtil.addErrorMessage("场所建设-"+("1".equals(this.unitType)?"是否开展职业病诊断工作":"是否开展职业病诊断救治工作")+"为”是“时，工作用房建筑总面积（平方米）不能为空！");
			bool=true;
		}
		if(flag && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfDiag()) && zwOrginfoEntity.getSupportPlaces().getDiagBed()==null){
			JsfUtil.addErrorMessage("场所建设-"+("1".equals(this.unitType)?"是否开展职业病诊断工作":"是否开展职业病诊断救治工作")+"为”是“时，床位（张）不能为空！");
			bool=true;
		}
		if(flag && "2".equals(this.unitType) && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfDiag()) && zwOrginfoEntity.getSupportPlaces().getUnitStdId()==null){
			JsfUtil.addErrorMessage("场所建设-"+("1".equals(this.unitType)?"是否开展职业病诊断工作":"是否开展职业病诊断救治工作")+"为”是“时，本单位场所建设情况相当于达到以下标准不能为空！");
			bool=true;
		}
		if(flag && "2".equals(this.unitType) && new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfDiag()) && zwOrginfoEntity.getSupportPlaces().getPsnStdId()==null){
			JsfUtil.addErrorMessage("场所建设-"+("1".equals(this.unitType)?"是否开展职业病诊断工作":"是否开展职业病诊断救治工作")+"为”是“时，开展职业病救治工作的专业人员配置相当于达到以下标准不能为空！");
			bool=true;
		}
		return bool;
	}
	/**
	 * <p>方法描述：各模块其他特殊信息验证</p>
	 * @MethodAuthor qrr,2020年10月15日,veryOtherBeforeSave
	 * */
	protected boolean veryOtherBeforeSave(){
		return false;
	}
	/**
	 * <p>方法描述：保存</p>
	 * @MethodAuthor qrr,2018年8月27日,toSave
	 * */
	private void toSave(){
		//检查项目
		if(null != showItemList && showItemList.size()>0){
			for(Object[] obj:showItemList){
				for(IZwItemsZzsb item:zwOrginfoEntity.getZwItems()){
					if(null != obj[6] && obj[6].toString().equals(item.getTsSimpleCode().getRid().toString())){
						if(null != obj[5] && "true".equals(obj[5].toString())){
							item.setIsOutter(1);
						}else if(null != obj[5] && "false".equals(obj[5].toString())){
							item.setIsOutter(0);
						}
					}
				}
			}
		}
		// 附件
		List<TdZwJsffjgCommAnnexZzsb> list = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		/*if (zwOrginfoEntity.getZwAnnex() != null && zwOrginfoEntity.getZwAnnex().size() > 0) {
			list = zwOrginfoEntity.getZwAnnex();
			list.clear();
		}*/
		list.addAll(zwAnnexList1);
		list.addAll(zwAnnexList2);
		list.addAll(zwAnnexList3);
		list.addAll(zwAnnexList4);
		list.addAll(zwAnnexList5);
		list.addAll(zwAnnexList6);
		list.addAll(zwAnnexList8);
		list.addAll(zwAnnexList7);
		list.addAll(zwAnnexList9);
		list.addAll(zwAnnexList20);
		// 仪器设置
		zwOrginfoEntity.setZwInst(zwInstInfoList);

		// 人员保存时，处理人员
		if (!this.handlePsnSave()) {
			zwOrginfoEntity.setState((short) 0);
			return;
		}
		if (null == zwOrginfoEntity.getFkByUnitTypeId()
				|| null == zwOrginfoEntity.getFkByUnitTypeId().getRid()) {
			zwOrginfoEntity.setFkByUnitTypeId(null);
		}
		//支撑机构 处理机构类型、 具备职业健康技术服务相关资质或能力的情况
		if(zwOrgType==4){
			if (null == zwOrginfoEntity.getFkByTypeId()
					|| null == zwOrginfoEntity.getFkByTypeId().getRid()) {
				zwOrginfoEntity.setFkByTypeId(null);
			}
			//保存支撑能力
			if(!CollectionUtils.isEmpty(this.techTypeList)){
				List<TdZwSupportTech> supportTechList=new ArrayList<>();
				for (TsSimpleCode tsSimpleCode : techTypeList) {
					if(tsSimpleCode.isIfSelected()){
						TdZwSupportTech supportTech=new TdZwSupportTech();
						supportTech.setFkByMainId((TdZwSupportOrgZzsb) zwOrginfoEntity);
						supportTech.setCreateManid(Global.getUser().getRid());
						supportTech.setCreateDate(new Date());
						supportTech.setFkByTechTypeId(tsSimpleCode);
						supportTechList.add(supportTech);
					}
				}
				zwOrginfoEntity.setSupportTechList(supportTechList);
			}
			if("1".equals(this.unitType)){
				packageSupportQualList(this.qualTypeList1);
			} else if ("2".equals(this.unitType)) {
				packageSupportQualList(this.qualTypeList2);
			}
			//初始化 场所建设中未选中的值清空
			if(!new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfZwLab())){
				zwOrginfoEntity.getSupportPlaces().setZwLabArea(null);
			}
			if(!new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfFsLab())){
				zwOrginfoEntity.getSupportPlaces().setFsLabArea(null);
			}
			if(!new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfScienTask())){
				zwOrginfoEntity.getSupportPlaces().setTeachArea(null);
				zwOrginfoEntity.getSupportPlaces().setScienArea(null);
			}
			if(!new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfTj())){
				zwOrginfoEntity.getSupportPlaces().setTjLabArea(null);
				zwOrginfoEntity.getSupportPlaces().setSelOffices(new ArrayList<String>());
				zwOrginfoEntity.getSupportPlaces().setSupportOfficeList(new ArrayList<TdZwSupportOffice>());
			}

			if(!new Integer("1").equals(zwOrginfoEntity.getSupportPlaces().getIfDiag())){
				zwOrginfoEntity.getSupportPlaces().setDiagLabArea(null);
				zwOrginfoEntity.getSupportPlaces().setDiagBed(null);
				zwOrginfoEntity.getSupportPlaces().setFkByUnitStdId(new TsSimpleCode());
				zwOrginfoEntity.getSupportPlaces().setFkByPsnStdId(new TsSimpleCode());
			}
			//场所建设  科室
			List<TdZwSupportOffice> supportOfficeList=new ArrayList<>();
			if(!CollectionUtils.isEmpty(zwOrginfoEntity.getSupportPlaces().getSelOffices())){
				for (String rid : zwOrginfoEntity.getSupportPlaces().getSelOffices()) {
					TdZwSupportOffice supportOffice=new TdZwSupportOffice();
					supportOffice.setFkByMainId(zwOrginfoEntity.getSupportPlaces());
					supportOffice.setCreateDate(new Date());
					supportOffice.setCreateManid(Global.getUser().getRid());
					supportOffice.setFkByOfficeId(new TsSimpleCode(Integer.parseInt(rid)));
					supportOfficeList.add(supportOffice);
				}
			}
			zwOrginfoEntity.getSupportPlaces().setSupportOfficeList(supportOfficeList);

			if(zwOrginfoEntity.getSupportPlaces().getFkByPsnStdId()!=null && zwOrginfoEntity.getSupportPlaces().getFkByPsnStdId().getRid()==null){
				zwOrginfoEntity.getSupportPlaces().setFkByPsnStdId(null);
			}
			if(zwOrginfoEntity.getSupportPlaces().getFkByUnitStdId()!=null && zwOrginfoEntity.getSupportPlaces().getFkByUnitStdId().getRid()==null){
				zwOrginfoEntity.getSupportPlaces().setFkByUnitStdId(null);
			}
			//职业病因素监测开展情况  排序
			if(!CollectionUtils.isEmpty(zwOrginfoEntity.getSupportChkList())){
				// 遍历原始列表并将元素逐个添加到新列表的开头
				ArrayList<TdZwSupportChk> reversedList = new ArrayList<>();
				for (TdZwSupportChk supportChk : zwOrginfoEntity.getSupportChkList()) {
					supportChk.setRid(null);
					reversedList.add(supportChk);
				}
				zwOrginfoEntity.setSupportChkList(reversedList);
			}
			//财政投入情况
			if(!CollectionUtils.isEmpty(zwOrginfoEntity.getSupportFundList())){
				ArrayList<TdZwSupportFund> reversedList = new ArrayList<>();
				for (TdZwSupportFund supportFund : zwOrginfoEntity.getSupportFundList()) {
					supportFund.setRid(null);
					reversedList.add(supportFund);
				}
				zwOrginfoEntity.setSupportFundList(reversedList);
			}
			TdZwSupportPlace supportPlace = zwOrginfoEntity.getSupportPlaces();
			if(supportPlace!=null){
				if(supportPlace.getUnitStdId()==null){
					supportPlace.setFkByUnitStdId(null);
				}else{
					supportPlace.setFkByUnitStdId(new TsSimpleCode(supportPlace.getUnitStdId()));
				}
				if(supportPlace.getPsnStdId()==null){
					supportPlace.setFkByPsnStdId(null);
				}else{
					supportPlace.setFkByPsnStdId(new TsSimpleCode(supportPlace.getPsnStdId()));
				}
			}
		}
		if(zwOrgType == 0){

			toSaveBaseInfo2(jkList,zwOrgType,jkItemRowList,list,jkJcMap,this.selectZoneApproveRid);
		}else if(zwOrgType==1){

			toSaveBaseInfo1(fsList,zwOrgType, smallServiceList,list);
			initFsService();
		}else if(zwOrgType==2){
			toSaveBaseInfo(zyList,zwOrgType, list);
		}else if(zwOrgType == 3){
			toSaveBaseInfo(jsfwList,zwOrgType, list);
		}else if(zwOrgType == 4){
			toSaveBaseInfo(null,zwOrgType, null);
			this.initTechType();
			this.initSupportPlace(zwOrginfoEntity);
		}
		if (null==zwOrginfoEntity.getFkByUnitTypeId()) {
			zwOrginfoEntity.setFkByUnitTypeId(new TsSimpleCode());
		}
		// 更新页面信息
		initZwAnnex();
		initZwInst();
		//initReportData();
		//清空需要删除所属机构的资质人员
		zwOrginfoEntity.setDelPsnIds(null);

		if(instinfo.getAcptReqId()==null){
			instinfo.setAcptReqId(new TsSimpleCode());
		}
		if(instinfo.getInstStateId()==null){
			instinfo.setInstStateId(new TsSimpleCode());
		}
		this.initPsnType();
		if ("1".equals(ifPersonSummary)) {
			this.initPersonSummary();
		}
		// 保存完成后重新初始化人员状态跟踪，为下次操作做准备
		this.initPersonnelStateTracking();
	}


	private void packageSupportQualList(List<TsSimpleCode> list) {
		List<TdZwSupportQual> supportQualList=new ArrayList<>();
		for (TsSimpleCode tsSimpleCode : list) {
			if(tsSimpleCode.isIfSelected()){
				TdZwSupportQual supportQual=new TdZwSupportQual();
				supportQual.setFkByMainId((TdZwSupportOrgZzsb) zwOrginfoEntity);
				supportQual.setCreateManid(Global.getUser().getRid());
				supportQual.setCreateDate(new Date());
				supportQual.setFkByTypeId(tsSimpleCode);
				supportQualList.add(supportQual);
			}
		}
		zwOrginfoEntity.setSupportQualList(supportQualList);
	}
	/**
	 * <p>修订内容： 信息提交时判断人员信息不允许为空</p>
	 *
	 * @MethodReviser rcj,2018年7月3日,submitAction
	 */
	public void submitAction() {
		// 基本信息验证
//		if (verifyBaseInfo()) {
//			zwOrginfoEntity.setState((short) 0);
//			return;
//		}
		if (verifyPoint() || verifySupportChk(true)) {
			return;
		}
		if(0 == zwOrgType && institutionSubmitValidate()){ //检查机构资质申报质量管理体系提交验证
			return;
		}
		// 附件验证
		if ((zwAnnexList1 == null || zwAnnexList1.size() == 0) && zwOrgType != 4) {
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("单位法人证明材料（营业执照）不允许为空！");
			return;
		}
		if((zwAnnexList2 == null || zwAnnexList2.size() == 0)
				&& (zwOrgType == 0 || zwOrgType == 2)) {
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("医疗机构执业许可证不允许为空！");
			return;
		}
		if(zwOrgType == 0 && (zwAnnexList6 == null || zwAnnexList6.size() == 0)) {
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("职业健康检查机构备案回执不允许为空！");
			return;
		}
		if(zwOrgType == 1 && (zwAnnexList8 == null || zwAnnexList8.size() == 0)) {
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("放射卫生技术服务机构批准证书不允许为空！");
			return;
		}
		if(zwOrgType == 2 && (zwAnnexList7 == null || zwAnnexList7.size() == 0)) {
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("职业病诊断机构备案回执不允许为空！");
			return;
		}
		if(zwOrgType == 3 && (zwAnnexList9 == null || zwAnnexList9.size() == 0)) {
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("职业卫生技术服务机构资质证书不允许为空！");
			return;
		}
		if(zwOrgType == 0 && (zwAnnexList20 == null || zwAnnexList20.size() == 0) && !CollectionUtils.isEmpty(showItemList)) {
			for(Object[] v : showItemList){
				if(v[5]!=null && (boolean)v[5]){
					zwOrginfoEntity.setState((short) 0);
					JsfUtil.addErrorMessage("委托协议和委托项目明细不允许为空！");
					return;
				}
			}
		}

		//人员信息验证  信息提交时判断人员信息不允许为空
		if(null == zwOrginfoEntity.getZwOrgPsns()){
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("人员信息不允许为空！");
			return;
		}
		//仪器信息验证  信息提交时判断仪器信息不允许为空
		if(CollectionUtils.isEmpty(zwInstInfoList) && zwOrgType != 4){
			JsfUtil.addErrorMessage("仪器信息不允许为空！");
			return;
		}

		boolean flag = veryBeforeSave()||veryOtherBeforeSave();
		if("1".equals(this.ifShowApproveUnit) && StringUtils.isBlank(zwOrginfoEntity.getRcdUnitName()) && 4 != zwOrgType){
			if(0 != zwOrgType){
				flag = true;
				JsfUtil.addErrorMessage(2 == zwOrgType ? "备案单位名称不允许为空！" : "资质批准单位不允许为空！");
			}
		}
		if (flag) {
			return;
		}

		if(zwOrgType == 0 &&
				validateOutCarList()){
			zwOrginfoEntity.setState((short) 0);
			JsfUtil.addErrorMessage("至少有一辆外检车辆！");
			return;
		}
		/*校验服务明细中的项目*/
		if(zwOrgType==0 && !"1".equals(this.ifZoneRecord)){
            if(null==serviceObj||serviceObj.length==0){
                JsfUtil.addErrorMessage("服务项目不能为空！");
                return;
            }
			if("1".equals(this.ifServiceDetails)){
				for(ZwJkItemPO zwItemPO : this.jkItemList) {
					//服务类别
					Integer jcItemRid = zwItemPO.getJcItem().getRid();
					boolean jcHasSubFlag = false;
					for(ZwJkItemRowPO zwItemRowPO : this.jkItemRowList) {
						//服务小类
						if(jcItemRid.equals(zwItemRowPO.getJcItem().getRid())){
							if(zwItemRowPO.getSelectedSerItmDetail()!=null && zwItemRowPO.getSelectedSerItmDetail().length==1){
								//该服务类别有小类
								jcHasSubFlag = true;
								break;
							}
						}
					}
					if(!jcHasSubFlag){
						JsfUtil.addErrorMessage("每个服务类别中至少选择一项服务项目！");
						return;
					}
				}
			}
		}
		if(zwOrgType==2){
			if(null==serviceObj||serviceObj.length==0){
				JsfUtil.addErrorMessage("服务项目不能为空！");
				return;
			}
		}
		//支撑机构信息验证
		if(zwOrgType==4 && supportSubmitValidate()){
			return;
		}
		//职业卫生技术验证
		if(zwOrgType==3 && !chkAbilitySubmitValidate()){
			JsfUtil.addErrorMessage("至少有一条检测能力信息记录！");
			return;
		}
		if("1".equals(ifCheckOrg) && zwOrgType == 0){
			//存在审核功能
			zwOrginfoEntity.setState((short) 2);
			//审核信息清空
			zwOrginfoEntity.setCheckRst(null);
			zwOrginfoEntity.setAuditAdv(null);
			zwOrginfoEntity.setFkByChkPsnId(null);
			//保存
			this.toSave();
			try {
				this.zwIntellReportServiceImpl.toSaveAndAudit(zwOrgType,zwOrginfoEntity.getRid());
			}catch (Exception e){
				e.printStackTrace();
				JsfUtil.addErrorMessage("提交失败！");
				//状态撤回
				zwOrginfoEntity.setState((short) 0);
				this.zwIntellReportServiceImpl.updateToCancelState(zwOrginfoEntity);
				return;
			}

		}else {
			zwOrginfoEntity.setState((short) 1);
			//保存
			this.toSave();
		}
		JsfUtil.addSuccessMessage("提交成功！");
		initOrgTypeAndTechType();
		initSerItem(this.jkItemList);
		this.jkItemRowList = convertZwJkItemPOToRowPO(jkItemList);
		this.afterSubmitInit();
	}

	protected  boolean chkAbilitySubmitValidate() {
		return false;
	}

	/**
	 * 保存支撑机构前操作
	 */
	public void saveSupportBeforeAction(TdZwSupportOrgZzsb supportOrg) {

	}
	public boolean validateOutCarList(){
		return false;
	}
	/**
	 * <p>方法描述：撤销功能</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-04-26
	 **/
	public void cancelAction() {
		if(zwOrgType==0 && this.ifReviewProcess){
			List<Object[]> obj= zwIntellReportServiceImpl.findOrgInfoApply();
			if(!CollectionUtils.isEmpty(obj)){
				if(obj.get(0)[1]==null){
					JsfUtil.addErrorMessage("备案申请流程状态异常！");
					return;
				}else if("0".equals(obj.get(0)[1].toString())||"1".equals(obj.get(0)[1].toString())||"3".equals(obj.get(0)[1].toString())){
					JsfUtil.addErrorMessage("备案申请流程进行中，无法撤销！");
					return;
				}
			}
		}
		toCancel();
		if(zwOrgType==0){
			zwIntellReportServiceImpl.updateOrgInfoLastDate(zwOrginfoEntity.getRid());
			initReportData();
		}

		// 更新页面信息
		initZwAnnex();
		initZwInst();
		initPsnType();
		initSerItem(this.jkItemList);
		this.jkItemRowList = convertZwJkItemPOToRowPO(jkItemList);
		changeSerItmDetail();
		this.afterSubmitInit();
		JsfUtil.addSuccessMessage("撤销成功！");
	}

	/**
	 * 保存人员信息前处理
	 */
	public boolean handlePsnSave() {
		List<IZwOrgPsnsZzsb> zwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
		if (null != zwOrgPsns && zwOrgPsns.size() > 0) {
			for (IZwOrgPsnsZzsb psns : zwOrgPsns) {
				/*
				 * if (StringUtils.isBlank(psns.getCertNo())) {
				 * JsfUtil.addErrorMessage("人员信息中证书编号不允许为空！"); return false; }
				 */

				// 检查在职状态是否发生变化，如果发生变化则更新MASTER_DATA_TIME
				Integer psnRid = psns.getTdZwPsninfo().getRid();
				if (psnRid != null && originalDutyStateMap.containsKey(psnRid)) {
					String originalDutyState = originalDutyStateMap.get(psnRid);
					String currentDutyState = psns.getOnDuty();
					// 检查在职状态是否发生变化（离职变在职或在职变离职）
					if (!StringUtils.equals(originalDutyState, currentDutyState)) {
						psns.getTdZwPsninfo().setMasterDataTime(new Date());
					}
				}

				// 从事项目
				String wordItems = psns.getWordItems();
				if (StringUtils.isNotBlank(wordItems)) {
					List<TdZwPsnitemsZzsb> tdZwPsnitemses = new ArrayList<TdZwPsnitemsZzsb>();

					String[] arr = wordItems.split(",");
					for(String s : arr) {
						TdZwPsnitemsZzsb tdZwPsnitems = new TdZwPsnitemsZzsb();
						tdZwPsnitems.setItemCode(Short.valueOf(s));
						tdZwPsnitems.setTdZwTjorgpsns((TdZwTjorgpsnsZzsb) psns);
						tdZwPsnitemses.add(tdZwPsnitems);
					}
					psns.setTdZwPsnitemses(tdZwPsnitemses);
				}else{
					if(null != psns.getTdZwPsnitemses()){
						psns.getTdZwPsnitemses().clear();
					}
				}
			}
		}

		// 处理被删除的人员，更新其MASTER_DATA_TIME
		this.updateMasterDataTimeForDeletedPersons();

		return true;
	}

	/**
	 * 保存检查项目
	 */
	public void handleTjItemSave() {
		List<IZwItemsZzsb> simList = new ArrayList<IZwItemsZzsb>();
		// 项目字符串
		StringBuilder itemStr = new StringBuilder();
		if (null != diagMap && diagMap.size() > 0) {
			StringBuilder listRids=new StringBuilder();
			StringBuilder listSelRids=new StringBuilder();
			if(null != showItemList && showItemList.size()>0){
				for(Object[] obj:showItemList){
					listRids.append(",").append(obj[6].toString());
					if(null != obj[5] && "true".equals(obj[5].toString())){
						listSelRids.append(",").append(obj[5].toString());
					}
				}
				listRids.deleteCharAt(0);
				if(null != listSelRids && StringUtils.isNotBlank(listSelRids.toString())){
					listSelRids.deleteCharAt(0);
				}
			}
			Iterator<TsSimpleCode> iterator = diagMap.keySet().iterator();
			TdZwTjitemsZzsb tdZwTjitems = null;
			while (iterator.hasNext()) {
				TsSimpleCode next = iterator.next();
				String[] selCodeRids = next.getSelCodeRids();
				if (null != selCodeRids && selCodeRids.length > 0) {
					for (String s : selCodeRids) {
						if(listRids.toString().contains(s)){
							for(Object[] obj:showItemList){
								if(s.equals(obj[6].toString())){
									if(null != obj[5] && "true".equals(obj[5].toString())){
										tdZwTjitems = new TdZwTjitemsZzsb();
										tdZwTjitems.setTdZwTjorginfo((TdZwTjorginfoZzsb) zwOrginfoEntity);
										tdZwTjitems.setCreateDate(new Date());
										tdZwTjitems.setCreateManid(sessionData.getUser().getRid());
										tdZwTjitems.setTsSimpleCode(new TsSimpleCode(Integer.valueOf(s)));
										tdZwTjitems.setIsOutter(1);
										simList.add(tdZwTjitems);

										itemStr.append(",").append(s);
										continue;
									}else{
										tdZwTjitems = new TdZwTjitemsZzsb();
										tdZwTjitems.setTdZwTjorginfo((TdZwTjorginfoZzsb) zwOrginfoEntity);
										tdZwTjitems.setCreateDate(new Date());
										tdZwTjitems.setCreateManid(sessionData.getUser().getRid());
										tdZwTjitems.setTsSimpleCode(new TsSimpleCode(Integer.valueOf(s)));
										simList.add(tdZwTjitems);

										itemStr.append(",").append(s);
										continue;
									}
								}
							}
						}else if(null != listSelRids && listSelRids.toString().contains(s)){
							tdZwTjitems = new TdZwTjitemsZzsb();
							tdZwTjitems.setTdZwTjorginfo((TdZwTjorginfoZzsb) zwOrginfoEntity);
							tdZwTjitems.setCreateDate(new Date());
							tdZwTjitems.setCreateManid(sessionData.getUser().getRid());
							tdZwTjitems.setTsSimpleCode(new TsSimpleCode(Integer.valueOf(s)));
							tdZwTjitems.setIsOutter(1);
							simList.add(tdZwTjitems);

							itemStr.append(",").append(s);
						}else{
							tdZwTjitems = new TdZwTjitemsZzsb();
							tdZwTjitems.setTdZwTjorginfo((TdZwTjorginfoZzsb) zwOrginfoEntity);
							tdZwTjitems.setCreateDate(new Date());
							tdZwTjitems.setCreateManid(sessionData.getUser().getRid());
							tdZwTjitems.setTsSimpleCode(new TsSimpleCode(Integer.valueOf(s)));
							simList.add(tdZwTjitems);

							itemStr.append(",").append(s);
						}
					}
				}
			}
		}
		if (itemStr.length() > 1) {
			itemStr = itemStr.deleteCharAt(0);
		}
		zwOrginfoEntity.setJcItems(itemStr.toString());
		zwOrginfoEntity.setZwItems(simList);
	}

	/**
	 * 仪器选择初始化
	 */
	public void addInstInitAction() {
	    //清空查询参数
		this.itmName = null;
		this.itmmodle = null;
		this.itmCode = null;
		// 已选仪器ID
		selectInstIds = null;
		if (zwInstInfoList != null && zwInstInfoList.size() > 0) {
			StringBuilder sb = new StringBuilder();
			for (IZwInstZzsb t : zwInstInfoList) {
				sb.append(",").append(t.getTdZwInstinfo().getRid().toString());
			}
			selectInstIds = sb.toString().substring(1);
		}
		// 所有可选仪器
		srcList = zwIntellReportServiceImpl
				.findTdZwInsfInfos(selectInstIds, sessionData.getUser().getTsUnit().getRid());
		// 查询
		instSearch();

		//初始化放射仪器类别
		this.initSrvInstType();
		this.svrTypeId = null;
	}


	public void instTypeChg() {
		initSrvInstType();
	}

	public void modInstInitAction(){
		this.tdZwInstinfo = zwIntellReportServiceImpl.findTdZwInstInfo(this.tdZwInstinfo.getRid());

		this.instinfo = this.tdZwInstinfo;
		if(null == instinfo.getAcptReqId()){
			instinfo.setAcptReqId(new TsSimpleCode());
		}
		if(null == instinfo.getInstStateId()){
			instinfo.setInstStateId(new TsSimpleCode());
		}
		this.svrTypeId = null;
		if(null != this.instinfo && null != this.instinfo.getFkByInstKindDetalId()) {
			this.svrTypeId = this.instinfo.getFkByInstKindDetalId().getRid();
		}
		this.initSrvInstType();
		this.tdZwInstinfo.setInstType(0);
		this.instinfo.setInstType(0);
		instAcptChangeListener();
	}

	private void initSrvInstType() {
		onDateValueChange(this.instinfo.getLastAcptDate(), this.instinfo.getLastAcptCircleDot());


//		if(zwOrgType!=null&zwOrgType ==1){
//			this.fsSvrTypeList = Lists.newArrayList();
//			List<TsSimpleCode> list = this.zwIntellReportServiceImpl.findSimpleCodesByTypeNo("5042");
//			for (TsSimpleCode t : list) {
//				fsSvrTypeList.add(new SelectItem(t.getRid(),t.getCodeName()));
//			}
//			return;
//		}
//		//放射资质模块
		this.showInstAcpt = false;
		acptReqList = this.commService.findLevelSimpleCodesByTypeId("5841");
		instStateList = this.zwIntellReportServiceImpl.findSimpleCodesByTypeNo("5842");

		this.cacheInstSimpleCodeMap = new HashMap<>();
		this.cacheSvrTypeMap = Maps.newHashMap();
		if(zwOrgType!=null&(zwOrgType ==1 || zwOrgType ==3)){
			svrTypeFsNewList = new ArrayList<>();
			List<TsSimpleCode> list = this.zwIntellReportServiceImpl.findSimpleCodesByTypeNo("5042");
			if( null != list && list.size() > 0 )	{
				for (TsSimpleCode t : list) {
					this.cacheInstSimpleCodeMap.put(t.getRid(), t);
					svrTypeFsNewList.add(new SelectItem(t.getRid(),t.getCodeName()));
					this.cacheSvrTypeMap.put(t.getRid(), t.getRid());
				}
			}
			return;
		}



		this.svrTypeList = Lists.newArrayList();

		List<TsSimpleCode> list=new ArrayList<>();
		if(null == tdZwInstinfo.getInstType() || tdZwInstinfo.getInstType().equals(0)){
			list = this.zwIntellReportServiceImpl.findSimpleCodesByTypeNo("5024");
		}else if(tdZwInstinfo.getInstType().equals(1)){
			list = this.zwIntellReportServiceImpl.findSimpleCodesByTypeNo("5017");
		}

		for(TsSimpleCode t : list) {
			this.cacheInstSimpleCodeMap.put(t.getRid(), t);
			if(StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
				SelectItemGroup group = new SelectItemGroup();
				group.setLabel(t.getCodeName());
				group.setValue(t.getRid());
				this.svrTypeList.add(group);
			}else {
				SelectItemGroup group = this.svrTypeList.get(this.svrTypeList.size() - 1);
				SelectItem[] selectItems = group.getSelectItems();
				SelectItem[] newArray = null;
				if(null == selectItems) {
					newArray = new SelectItem[1];
					newArray[0] = new SelectItem(t.getRid(), t.getCodeName());
				}else {
					newArray = Arrays.copyOf(selectItems, selectItems.length + 1);
					newArray[selectItems.length] = new SelectItem(t.getRid(), t.getCodeName());
				}
				group.setSelectItems(newArray);

				this.cacheSvrTypeMap.put(t.getRid(), (Integer)group.getValue());
			}
		}
	}

	/**
	 * 计算预警日期
	 * @param event
	 */
	public void onDateSelect(SelectEvent event){
		Date date = (Date) event.getObject();
		onDateValueChange(date, tdZwInstinfo.getLastAcptCircleDot());
	}

	/**
	 * @Description:
	 *
	 * 修订内容: 修改显示的预警日期
	 *
	 * @MethodReviser pw,2021年04月23日
	 */
	public void onDateValueChange(Date lastAcptDate, BigDecimal lastAcptCircleDot){
		if(lastAcptDate!=null&&lastAcptCircleDot!=null){
			Calendar theCa = Calendar.getInstance();
			theCa.setTime(lastAcptDate);
			//小数位数值<=0.5则预警日期增加6个月（统一折算为半年），小数位数值>0.5则预警日期增加1年
			//五舍六入后的值
			int num = lastAcptCircleDot.setScale(0,BigDecimal.ROUND_HALF_DOWN).intValue();
			//向下取整
			int floor = lastAcptCircleDot.setScale(0,BigDecimal.ROUND_DOWN).intValue();
			if(num > floor){
				//说明lastAcptCircleDot小数位大于0.5
				theCa.add(Calendar.YEAR,num);
			} else if(lastAcptCircleDot.compareTo(new BigDecimal(floor)) > 0){
				theCa.add(Calendar.YEAR,floor);
				theCa.add(Calendar.MONTH,6);
			} else {
				theCa.add(Calendar.YEAR,floor);
			}
			String formatDate = DateUtils.formatDate(theCa.getTime(), "yyyy-MM-dd");
			if(StringUtils.isNoneBlank(formatDate)&&formatDate.split("-").length>2){
				String[] split = formatDate.split("-");
				this.waringDate ="预警日期："+ split[0]+"-"+split[1]+"-"+split[2];
			}else{
				this.waringDate = null;
			}
		}else{
			this.waringDate = null;
		}
	}
	/**
	 * @Description: 修改显示的预警日期
	 * js 中 2020-02-29 增加一年 会变成 2021-03-01
	 * 在java中 2020-02-29 增加一年 会变成2021-02-28
	 * 需要统一 所以这边统一用Java方式
	 * @MethodAuthor pw,2021年04月23日
	 */
	public void changeWaringDate(){
		onDateValueChange(this.instinfo.getLastAcptDate(),this.tdZwInstinfo.getLastAcptCircleDot());
	}

	/**
	 * 页面查询方法
	 */
	public void instSearch(){
		displayList= new ArrayList<TdZwInstinfoZzsb>();
		if(srcList != null && srcList.size() > 0){
			if(StringUtils.isBlank(itmName) && StringUtils.isBlank(itmmodle) && StringUtils.isBlank(itmCode) && (null == this.svrInstArr || this.svrInstArr.length == 0)){
				displayList.addAll(srcList);
			}else{
				boolean boo;
				for(TdZwInstinfoZzsb t : srcList){
					boo = true;
					if(null != this.svrInstArr && this.svrInstArr.length > 0) {
						String conStr = ","+StringUtils.array2string(this.svrInstArr, ",")+",";
						if(null != t.getInstType() && conStr.contains(","+t.getInstType()+",")) {
							boo = true;
						}else {
							boo = false;
						}
					}
					if(StringUtils.isNotBlank(itmName) && !StringUtils.contains(t.getInstName(),itmName)){
						boo = false;
					}
					if(StringUtils.isNotBlank(itmmodle) && !StringUtils.contains(t.getInstModel(), itmmodle)){
						boo = false;
					}
					if(StringUtils.isNotBlank(itmCode) && !StringUtils.contains(t.getInstCode(), itmCode)){
						boo = false;
					}
					if(boo){
						displayList.add(t);
					}
				}
			}
			String ids = "";
			if(null != zwInstInfoList && zwInstInfoList.size()>0){
				for(IZwInstZzsb i :zwInstInfoList){
					ids += i.getTdZwInstinfo().getRid()+";";
				}
			}
			if(null != displayList && displayList.size()>0){
				for(TdZwInstinfoZzsb t:displayList){
					if(ids.contains(t.getRid().toString())){
						t.setSelected(true);
					}else{
						t.setSelected(false);
					}
				}
			}
//            if(null != displayList && displayList.size()>0){
//            	for(TdZwInstinfo t:displayList){
//                	if(null != selectInstIds && StringUtils.isNotBlank(selectInstIds)
//                			&& selectInstIds.contains(t.getRid().toString())){
//                		t.setSelected(true);
//                	}else{
//                		t.setSelected(false);
//                	}
//                }
//            }
		}
	}

	/**
	 * 添加仪器
	 */
	public void additmAction() {
		this.waringDate = null;
		/**默认数据**/
		svrTypeId = null;
		tdZwInstinfo = new TdZwInstinfoZzsb();
		tdZwInstinfo.setCreateDate(new Date());
		tdZwInstinfo.setCreateManid(sessionData.getUser().getRid());
		tdZwInstinfo.setTsUnit(sessionData.getUser().getTsUnit());
		tdZwInstinfo.setInstType(0);
		tdZwInstinfo.setOutter((short) 0);
		tdZwInstinfo.setIfNeedAcpt(1);
		tdZwInstinfo.setAcptReqId(new TsSimpleCode());
		tdZwInstinfo.setInstStateId(new TsSimpleCode());
		this.instinfo = this.tdZwInstinfo;
		instAcptChangeListener();
	}

	/**
	 * 保存仪器
	 */
	public void saveInstAction() {

		boolean flag = false;
		if( null  == svrTypeId){
			JsfUtil.addErrorMessage("请选择仪器类型！");
			flag = true;
		}

		if(StringUtils.isBlank(instinfo.getInstName())){
			JsfUtil.addErrorMessage("仪器名称不允许为空！");
			flag = true;
		}
		if(StringUtils.isBlank(instinfo.getInstModel())){
			JsfUtil.addErrorMessage("仪器型号不允许为空！");
			flag = true;
		}
		if(StringUtils.isBlank(instinfo.getInstCode())){
			JsfUtil.addErrorMessage("仪器编号不允许为空！");
			flag = true;
		}

		if(null != instinfo.getInstProDate() && null != instinfo.getPurchaseDate() &&
				DateUtils.isDateAfter(instinfo.getInstProDate(),instinfo.getPurchaseDate())){
			JsfUtil.addErrorMessage("购置日期应大于等于生产日期！");
			flag = true;
		}

		if(null == instinfo.getOutter()){
			JsfUtil.addErrorMessage("是否外包不允许为空！");
			flag = true;
		}

		if(null == instinfo.getAcptReqId() || null == instinfo.getAcptReqId().getRid()){
			JsfUtil.addErrorMessage("请选择检定要求！");
			flag = true;
		}else if(showInstAcpt && StringUtils.isBlank(instinfo.getCheckAnnexName())){
			String acptName = "";
			for (TsSimpleCode simpleCode:acptReqList){
				if(simpleCode.getRid().equals(instinfo.getAcptReqId().getRid())){
					acptName = simpleCode.getCodeName();
					break;
				}
			}
			JsfUtil.addErrorMessage("请上传"+acptName+"证书！");
			flag = true;
		}
		if(null != zwOrgType && zwOrgType == 0 && null == instinfo.getIfSuptOutChk()){
			JsfUtil.addErrorMessage("是否支持外检不能为空！");
			flag = true;
		}
		if(null == instinfo.getIfNeedAcpt()){
			JsfUtil.addErrorMessage("是否需要校验不能为空！");
			flag = true;
		}
		if(null != instinfo.getIfNeedAcpt() && instinfo.getIfNeedAcpt() == 1){
			if(null == instinfo.getLastAcptDate()){
				JsfUtil.addErrorMessage("最近校验日期不能为空！");
				flag = true;
			}
			if(null != instinfo.getLastAcptDate() && null != instinfo.getPurchaseDate() &&
					DateUtils.isDateAfter(instinfo.getPurchaseDate(),instinfo.getLastAcptDate())){
				JsfUtil.addErrorMessage("最近校验日期应大于等于购置日期！");
				flag = true;
			}
			if(null != instinfo.getLastAcptDate() && null != instinfo.getInstProDate() &&
					DateUtils.isDateAfter(instinfo.getInstProDate(),instinfo.getLastAcptDate())){
				JsfUtil.addErrorMessage("最近校验日期应大于等于生产日期！");
				flag = true;
			}
			if(null ==instinfo.getLastAcptCircleDot()){
				JsfUtil.addErrorMessage("校验周期不能为空！");
				flag = true;
			}
		}
		if(null == instinfo.getInstStateId() || null == instinfo.getInstStateId().getRid()){
			JsfUtil.addErrorMessage("仪器状态不允许为空！");
			flag = true;
		}

		// 仪器编号本单位唯一
		if(StringUtils.isNotBlank(this.instinfo.getInstCode())) {
			Integer count = zwIntellReportServiceImpl.findExistsCustNo(instinfo.getInstCode(), instinfo.getRid(), instinfo.getTsUnit().getRid());
			if(count>0) {
				JsfUtil.addErrorMessage("本单位下仪器编号重复！");
				flag = true;
			}
		}
		if(flag){
			return;
		}else if(null != instinfo.getIfNeedAcpt() && instinfo.getIfNeedAcpt() == 0){
			instinfo.setLastAcptDate(null);
			instinfo.setLastAcptCircle(null);
			instinfo.setLastAcptCircleDot(null);
		}


//    	if(null == this.instinfo.getInstType() || this.instinfo.getInstType().intValue() == 0) {
//    		this.instinfo.setFkByInstKindDetalId(null);
//    		this.instinfo.setFkByInstKindId(null);
//    		this.instinfo.setInstFactory(null);
//    		this.instinfo.setInstProDate(null);
//    		this.instinfo.setWhereStore(null);
//    		this.instinfo.setAcptDate(null);
//    		this.instinfo.setAcptConclu(null);
//    		if(null != this.svrTypeId) {
//    			this.instinfo.setFkByInstKindDetalId(new TsSimpleCode(this.svrTypeId));
//    			this.instinfo.setFkByInstKindId(new TsSimpleCode(this.cacheSvrTypeMap.get(this.svrTypeId)));
//    		}
//    	}else {
//            instinfo.setLastAcptDate(null);
//            instinfo.setLastAcptCircle(null);
//
//    	}
		if(!showInstAcpt){
			//不需要上传证书的，该字段清空
			this.instinfo.setCheckAnnexName(null);
		}
		if(null != this.svrTypeId) {
			this.instinfo.setFkByInstKindDetalId(this.cacheInstSimpleCodeMap.get(this.svrTypeId));
			this.instinfo.setFkByInstKindId(new TsSimpleCode(this.cacheSvrTypeMap.get(this.svrTypeId)));
		}
		instinfo = zwIntellReportServiceImpl.saveOrUpdateInstInfo(instinfo);
		this.tdZwInstinfo = this.instinfo;
		if(null != srcList && srcList.size()>0){
			for(TdZwInstinfoZzsb t: srcList){
				if(tdZwInstinfo.getRid().equals(t.getRid())){
					this.srcList.remove(t);
					this.srcList.add(this.tdZwInstinfo);
					break;
				}
			}
		}
		if (!srcList.contains(tdZwInstinfo)) {
			srcList.add(tdZwInstinfo);
		}
		instSearch();
		if(null != zwInstInfoList && zwInstInfoList.size()>0){
			for(IZwInstZzsb t:zwInstInfoList){
				if(tdZwInstinfo.getRid().equals(t.getTdZwInstinfo().getRid())){
					t.setTdZwInstinfo(tdZwInstinfo);
				}
			}
		}

		RequestContext currentInstance = RequestContext.getCurrentInstance();
		currentInstance.execute("PF('InstEditDialog').hide()");
		currentInstance.update(":mainForm:instinfoDataTable");
		currentInstance.update(":mainForm:tabView:instDatatable");
		JsfUtil.addSuccessMessage("保存成功！");
	}

	/**
	 * 仪器删除
	 */
	public void deleteInstinfoAction() {
		if(tdZwInstinfo.getSelected()){
			JsfUtil.addErrorMessage("该仪器已被引用，无法删除！");
			return;
		}
		String msg = zwIntellReportServiceImpl.deleteZwInstInfo(tdZwInstinfo.getRid());
		if (StringUtils.isNotBlank(msg)) {
			JsfUtil.addErrorMessage(msg);
		} else {
			JsfUtil.addSuccessMessage("删除成功！");
			srcList.remove(tdZwInstinfo);
			instSearch();
		}
	}

	/**
	 * 仪器选择
	 */
	public void selectInstAction() {
		// 添加
		IZwInstZzsb zwInst;
		if (zwOrgType.equals(0)) {
			zwInst = new TdZwTjinstZzsb();
		} else if (zwOrgType.equals(1)) {
			zwInst = new TdZwOrginstZzsb();
		} else if (zwOrgType.equals(2)){
			zwInst = new TdZwDiaginstZzsb();
		}else {
			zwInst = new TdZwOcchethInstZzsb();
		}
		tdZwInstinfo.setSelected(true);
		zwInst.setTdZwInstinfo(tdZwInstinfo);
		zwInst.setZwOrginfo(zwOrginfoEntity);
		zwInst.setCreateDate(new Date());
		zwInst.setCreateManid(sessionData.getUser().getRid());
		zwInstInfoList.add(zwInst);
		// 可选的删除
//		srcList.remove(tdZwInstinfo);
//		instSearch();
	}

	/**
	 * 删除已选择的仪器
	 */
	public void deleteOrgInstAction() {
		zwInstInfoList.remove(selectOrgInst);
	}

	/**
	 * <p>修订内容：删除人员</p>
	 * @MethodReviser qrr,2018年6月23日,deletePsnInstAction
	 *
	 * <p>修订内容：查询人员在职状态是否为“在职”</p>
	 * @MethodReviser qrr,2018年6月27日,saveOrUpdateZwOrginfoEntity
	 */
	public void deletePsnInstAction() {
		List<IZwOrgPsnsZzsb> zwOrgPsns = zwOrginfoEntity.getZwOrgPsns();

		// 记录被删除的人员RID，用于后续更新MASTER_DATA_TIME
		Integer psnRid = selectZwOrgPsn.getTdZwPsninfo().getRid();
		if (psnRid != null) {
			deletedPsnRidSet.add(psnRid);
		}

		zwOrgPsns.remove(selectZwOrgPsn);
		zwOrginfoEntity.setZwOrgPsns(zwOrgPsns);
		StringBuffer sb = new StringBuffer();
		if (StringUtils.isNotBlank(zwOrginfoEntity.getDelPsnIds())) {
			sb.append(zwOrginfoEntity.getDelPsnIds());
		}
		//正常离职后保存完再删除 不会出现未清空资质人员所属机构的情况
		//但由于离职在职以及删除是假保存 只有保存和提交的时候才会更新到数据库
		//会出现 先离职未保存再点删除 导致对应的资质人员所属机构未清空的问题 这里去掉判断是否在职
		sb.append(",").append(selectZwOrgPsn.getTdZwPsninfo().getRid());
		zwOrginfoEntity.setDelPsnIds(sb.toString());
	}
	/**
	 * <p>方法描述：在岗、离岗的操作</p>
	 * @MethodAuthor qrr,2018年7月3日,changeDutyAction
	 * */
	public void changeDutyAction() {
		// 记录原始在职状态，用于后续判断是否发生变化
		Integer psnRid = selectZwOrgPsn.getTdZwPsninfo().getRid();
		if (psnRid != null && !originalDutyStateMap.containsKey(psnRid)) {
			originalDutyStateMap.put(psnRid, selectZwOrgPsn.getOnDuty());
		}
		selectZwOrgPsn.setOnDuty(dutySate);
	}

	public void ylHandleFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			String fileName = file.getFileName();
			String contentType = file.getContentType().toLowerCase();
			try {
				String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
				if (StringUtils.isNotBlank(errorMsg)) {
					JsfUtil.addErrorMessage(errorMsg);
					return;
				}
			} catch (IOException e) {
				FacesMessage msg = new FacesMessage("上传失败", fileName + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
				return;
			}
			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
			String filePath = xnPath + "heth/zworgannex/" + uuid + "." + hz;
			String showDir = "/heth/zworgannex/" + uuid + "." + hz;
			// if (zwOrgType.equals(0)) {
			// 	ylAnnex = new TdZwJsffjgCommAnnexZzsb();
			// } else if (zwOrgType.equals(1)) {
			// 	ylAnnex = new TdZwJsffjgCommAnnexZzsb();
			// } else {
			// 	ylAnnex = new TdZwJsffjgCommAnnexZzsb();
			// }
			ylAnnex = new TdZwJsffjgCommAnnexZzsb();
			ylAnnex.setFkByOrgId(this.zwOrginfoEntity.getTsUnit());
			ylAnnex.setFilePath(showDir);
			ylAnnex.setFileName(fileName);
			ylAnnex.setAnnexType(selectZwAnnexType);
			ylAnnex.setCreateDate(new Date());
			ylAnnex.setCreateManid(sessionData.getUser().getRid());
			ylAnnex.setAddSource(this.zwOrgType);
			try {
				FileUtils.copyFile(filePath, file.getInputstream());
				JsfUtil.addSuccessMessage("上传成功！");
				RequestContext.getCurrentInstance().execute("YlFileUId.hide();");
			} catch (IOException e) {
				FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
			}
		}
	}
	public void handleAcptFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			String fileName = file.getFileName();
			String contentType = file.getContentType().toLowerCase();
			try {
				String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
				if (StringUtils.isNotBlank(errorMsg)) {
					JsfUtil.addErrorMessage(errorMsg);
					return;
				}
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
				String filePath = xnPath + "heth/zworgannex/" + uuid + "." + hz;
				String showDir = "/heth/zworgannex/" + uuid + "." + hz;
				this.instinfo.setCheckAnnexName(showDir);
				FileUtils.copyFile(filePath, file.getInputstream());
				JsfUtil.addSuccessMessage("上传成功！");
				RequestContext.getCurrentInstance().execute("InstAcptFileUId.hide();");
			} catch (IOException e) {
				FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
			}
		}
	}



	public void handleFileUpload(FileUploadEvent event) {
		if (null != event) {
			UploadedFile file = event.getFile();
			String fileName = file.getFileName();
			String contentType = file.getContentType().toLowerCase();
			try {
				String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
				if (StringUtils.isNotBlank(errorMsg)) {
					JsfUtil.addErrorMessage(errorMsg);
					return;
				}
			} catch (IOException e) {
				FacesMessage msg = new FacesMessage("上传失败", fileName + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
				return;
			}
			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
			String filePath = xnPath + "heth/zworgannex/" + uuid + "." + hz;
			String showDir = "/heth/zworgannex/" + uuid + "." + hz;
			/*IZwAnnex annex = null;
			if (zwOrgType.equals(0)) {
				annex = new TdZwTjorgannex();
			} else if (zwOrgType.equals(1)) {
				annex = new TdZwOrgannex();
			} else if(zwOrgType.equals(2)){
				annex = new TdZwDiagannex();
			} else {
			    annex = new TdZwOcchethAnnex();
            }*/
			TdZwJsffjgCommAnnexZzsb annex = new TdZwJsffjgCommAnnexZzsb();
			annex.setFkByOrgId(this.zwOrginfoEntity.getTsUnit());
			annex.setFilePath(showDir);
			annex.setFileName(fileName);
			annex.setAnnexType(selectZwAnnexType);
			annex.setCreateDate(new Date());
			annex.setCreateManid(sessionData.getUser().getRid());
			annex.setAddSource(this.zwOrgType);
			if (selectZwAnnexType.equals(1)) {
				zwAnnexList1.add(annex);
			} else if((selectZwAnnexType.equals(2))){
				zwAnnexList2.add(annex);
			}else if((selectZwAnnexType.equals(3))){
				zwAnnexList3.add(annex);
			}else if((selectZwAnnexType.equals(4))){
				zwAnnexList4.add(annex);
			}else if((selectZwAnnexType.equals(5))){
				zwAnnexList5.add(annex);
			}else if((selectZwAnnexType.equals(6))){
				zwAnnexList6.add(annex);
			}else if((selectZwAnnexType.equals(7))){
				zwAnnexList7.add(annex);
			}else if((selectZwAnnexType.equals(8))){
				zwAnnexList8.add(annex);
			}else if((selectZwAnnexType.equals(11))){
				zwAnnexList9.add(annex);
			}else if((selectZwAnnexType.equals(20))){
				zwAnnexList20.add(annex);
			}
			try {
				FileUtils.copyFile(filePath, file.getInputstream());
				JsfUtil.addSuccessMessage("上传成功！");
				RequestContext.getCurrentInstance().execute("fileUIdVar.hide();");
			} catch (IOException e) {
				FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
				FacesContext.getCurrentInstance().addMessage("上传失败", msg);
				e.printStackTrace();
			}
		}
	}

	public void deleteYlAnnex(){
		this.ylAnnex = null;
	}
	public void deleteCheckAnnex(){
		this.instinfo.setCheckAnnexName(null);
	}

	public void deleteAnnex() {
		deleteFilepath.add(selectZwAnnex.getFilePath());
		if (selectZwAnnexType.equals(1)) {
			zwAnnexList1.remove(selectZwAnnex);
		} else if(selectZwAnnexType.equals( 2)){
			zwAnnexList2.remove(selectZwAnnex);
		}else if(selectZwAnnexType.equals(3)){
			zwAnnexList3.remove(selectZwAnnex);
		}else if(selectZwAnnexType.equals(4)){
			zwAnnexList4.remove(selectZwAnnex);
		}else if(selectZwAnnexType.equals(5)){
			zwAnnexList5.remove(selectZwAnnex);
		}else if(selectZwAnnexType.equals(6)){
			zwAnnexList6.remove(selectZwAnnex);
		}else if(selectZwAnnexType.equals(7)){
			zwAnnexList7.remove(selectZwAnnex);
		}else if(selectZwAnnexType.equals(8)){
			zwAnnexList8.remove(selectZwAnnex);
		}else if(selectZwAnnexType.equals(11)){
			zwAnnexList9.remove(selectZwAnnex);
		}else if(selectZwAnnexType.equals(20)){
			zwAnnexList20.remove(selectZwAnnex);
		}
	}

	public void downLoadDiskFile() {
		String path = selectZwAnnex.getFilePath();
		path = xnPath + path.substring(1);
		FileInputStream fis = null;
		try {
			File file = new File(path);
			fis = new FileInputStream(file);
			String fileString = DownLoadUtil.uploadFile2Database(fis);
			String fileName = selectZwAnnex.getFileName();
			DownLoadUtil.downFile(fileName, fileString);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException e2) {
					e2.printStackTrace();
				}
			}
		}
	}
	/**
	 * <p>修订内容：psnInfoBase初始化</p>
	 * @MethodReviser qrr,2018年5月23日,codeEditAction
	 * */
	public void codeEditAction(){
		this.psnDialogType=1;
		TdZwPsninfoZzsb tdZwPsninfo=zwIntellReportServiceImpl.findTdZwPsnInfo(this.tdZwPsninfo.getRid());
//		String sql = "select PSN_TYPE from TD_ZW_PSN_TYPE t where t.MAIN_ID = "+this.tdZwPsninfo.getRid();
//		List<Map> list = commService.findDataBySql(sql,null);
//        Set list2 = new HashSet();
//		if(list != null && list.size()>0){
//            for(Map map : list){
//                BigDecimal a = (BigDecimal) map.get("PSN_TYPE");
//                TsSimpleCode tsSimpleCode = zwIntellReportServiceImpl.find(TsSimpleCode.class,a.intValue());
//                if(tsSimpleCode != null && tsSimpleCode.getExtendS1() != null && "1".equals(tsSimpleCode.getExtendS1())){
//                    psnInfoBase.setFlag1(1);
//                }
//                if(tsSimpleCode != null && tsSimpleCode.getExtendS2() != null && tsSimpleCode.getExtendS2().equals(1)){
//                    psnInfoBase.setFlag2(1);
//                }
//                list2.add(a.intValue()+"");
//            }
//        }
		psnInfoBase.setZwOrgType(this.zwOrgType);
		psnInfoBase.setPsnInfo(tdZwPsninfo);
		psnInfoBase.initPsnInfo();
		psnInfoBase.initPsnZzlb();
	}

	public void codeViewAction(){
		this.psnDialogType=2;
		viewAndEditInit();
		RequestContext.getCurrentInstance().execute("disabledInput()");
	}


	/**
	 * @Description: 人员修改
	 *
	 * @MethodAuthor pw,2022年02月17日
	 */
	public void psnInfoEditAction(){
		this.psnDialogType=1;
		this.removeZzlbList=new ArrayList<>();
		viewAndEditInit();
	}


	/**
	 * @Description: 查看或者修改初始化
	 *
	 * @MethodAuthor pw,2022年02月12日
	 */
	private void viewAndEditInit(){
		TdZwPsninfoZzsb tdZwPsninfo=this.zwIntellReportServiceImpl
				.findTdZwPsnInfo(this.selectZwOrgPsn.getTdZwPsninfo().getRid());
		String idcCard = tdZwPsninfo.getIdcCard();
		this.psnInfoBase.setZwOrgType(this.zwOrgType);
		this.psnInfoBase.setPsnInfo(tdZwPsninfo);
		this.psnInfoBase.initPsnInfo();
		this.psnInfoBase.initPsnZzlb();
		//初始化培训履历
		this.psnInfoBase.initTrainHis(idcCard,tdZwPsninfo.getZzlbList());
		this.psnInfoBase.setOrgPsnInfoRid(this.selectZwOrgPsn.getRid());
	}

	public void surTypeChange(){
		if(null == svrTypeId){
			tdZwInstinfo.setInstName(null);
			return;
		}
		if(!CollectionUtils.isEmpty(this.cacheInstSimpleCodeMap)){
			TsSimpleCode simpleCode = this.cacheInstSimpleCodeMap.get(this.svrTypeId);
			if(null != simpleCode){
				this.tdZwInstinfo.setInstName(simpleCode.getCodeName());
			}
		}
	}

	public void saveYlFile(){
		if(null== ylAnnex || StringUtils.isBlank(ylAnnex.getFilePath())){
			JsfUtil.addErrorMessage("医疗机构执业许可证不能为空！");
			return;
		}
		if(StringUtils.isBlank(searchZoneCode)){
			JsfUtil.addErrorMessage("注册地点不能为空！");
			return;
		}
		if(StringUtils.isBlank(unitName)){
			JsfUtil.addErrorMessage("发证单位不能为空！");
			return;
		}
		if(null == unitLevel){
			JsfUtil.addErrorMessage("发证单位级别不能为空！");
			return;
		}
		TsZone zone=this.zwIntellReportServiceImpl.findTsZoneByRid(searchZoneId);
		ylAnnex.setFkByRegZoneId(zone);
		ylAnnex.setRegZoneId(zone.getRid());
		ylAnnex.setUnitLevel(unitLevel);
		ylAnnex.setUnitName(unitName);
		if (selectZwAnnexType.equals(1)) {
			zwAnnexList1.add(ylAnnex);
		} else {
			zwAnnexList2.add(ylAnnex);
		}
		RequestContext.getCurrentInstance().execute("PF('PhotoDialog').hide();");
	}

	public void ylAddAction(){
		ylAnnex=null;
		searchZoneCode=null;
		searchZoneName=null;
		unitLevel=null;
		unitName=null;
	}
	public void tableChange(TabChangeEvent event) {
		tabTitle = event.getTab().getTitle();
	}

	/**
	 * @Description : 地区初始化
	 * @MethodAuthor: anjing
	 * @Date : 2020/2/26 20:28
	 **/
	public void initZwOrgOutRangeZoneList() {
		outZoneList = new ArrayList<>();
		zoneNames = "";
		zoneIds = "";
		if(!CollectionUtils.isEmpty(zwOrginfoEntity.getZwOrgOutRanges())) {
			StringBuilder nameSb = new StringBuilder();
			StringBuilder idSb = new StringBuilder();
			for(IZwOrgOutRangeZzsb orgOutRange : zwOrginfoEntity.getZwOrgOutRanges()) {
				nameSb.append("，").append(orgOutRange.getFkByZoneId().getZoneName());
				idSb.append(",").append(orgOutRange.getFkByZoneId().getRid().toString());
				outZoneList.add(orgOutRange.getFkByZoneId());
			}
			zoneNames = nameSb.toString().substring(1);
			zoneIds = idSb.toString().substring(1);
		}

		if(StringUtils.isNotBlank(zoneIds)) {
			List<TreeNode> children = this.zoneTree.getChildren();
			if(null != children && children.size()>0) {
				for(TreeNode node : children) {
					TsZone tsZone = (TsZone) node.getData();
					List<TreeNode> children2 = node.getChildren();
					if(zoneIds.contains(tsZone.getRid().toString())) {
						node.setSelected(true);
						if(null != children2 && children2.size()>0) {
							for(TreeNode node2 : children2) {
								node2.setSelected(true);
							}
						}
					} else {
						if(null != children2 && children2.size()>0) {
							for(TreeNode node2 : children2) {
								TsZone tsZone2 = (TsZone) node2.getData();
								if(zoneIds.contains(tsZone2.getRid().toString())) {
									node2.setSelected(true);
								}
							}
						}
					}


				}
			}
		}
	}

	/**
	 * 初始化质量管理体系制度集合
	 */
	public void initInstitutionList(){}
	/** 质量管理体系制度集合保存验证 */
	public boolean institutionSaveValidate(){
		return false;
	}
	/** 质量管理体系制度集合提交验证 */
	public boolean institutionSubmitValidate(){
		return false;
	}

	/**
	 * <p>方法描述：选择单位类型</p>
	 * @MethodAuthor qrr,2021年2月27日,selectUnitTypeAction
	 * */
	public void selectUnitTypeAction() {
		Map<String, Object> options = new HashMap<String, Object>();
		options.put("modal", true);
		options.put("draggable", true);
		options.put("resizable", false);
		options.put("width", 680);
		options.put("contentWidth", 625);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>();
		paramList.add("单位类型");
		paramMap.put("titleName", paramList);
		paramList = new ArrayList<String>();
		paramList.add("5509");
		paramMap.put("typeNo", paramList);
		paramList = new ArrayList<String>();
		paramList.add("false");
		paramMap.put("ifAllSelect", paramList);
		paramList = new ArrayList<String>();
		paramList.add("true");
		paramMap.put("ifShowFirstCode", paramList);
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/system/codeRadioSelectListNew2", options, paramMap);
	}
	/**
	 * <p>方法描述：</p>
	 * @MethodAuthor qrr,2021年2月27日,onUnitTypeSearch
	 * */
	public void onUnitTypeSearch(SelectEvent event){
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			TsSimpleCode simpleCode = (TsSimpleCode) selectedMap.get("selectPro");
			if (null!=simpleCode) {
				zwOrginfoEntity.setFkByUnitTypeId(simpleCode);
			}
		}
	}

	/********************************************************* 人员导入 ***********************************************************************/

	/**
	 * @Description : 人员模板下载
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/17 10:18
	 **/
	public StreamedContent getPsnfile() {
		InputStream stream = null;
		try {
			String moudleFilePath = "/resources/template/excel/资质填报人员导入模板.xls";
			stream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(moudleFilePath);
			this.psnfile = new DefaultStreamedContent(stream, "application/xls", URLEncoder.encode("资质填报人员导入模板.xls", "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("文件下载失败!");
		} finally{
			if(stream != null){
				try {
					stream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		}
		return psnfile;
	}

	/**
	 * @Description : Excel文件校验
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/17 11:54
	 **/
	private List<List<Object>> verfyFile(FileUploadEvent event, boolean isDevice) {
		List<List<Object>> readExcel = new ArrayList<>();
		String updateFormId = isDevice ? "mainForm:deviceFileUpload" : "mainForm:psnFileUpload";
		if (event == null || event.getFile() == null) {
			RequestContext.getCurrentInstance().update(updateFormId);
			JsfUtil.addErrorMessage("文件上传失败！");
			return null;
		}

		try {
			UploadedFile file = event.getFile();
			// 验证数据总数量是否满足
			Workbook wb = ImportExcelUtil.getWorkbook(file);
			int sheetNum = ImportExcelUtil.countExcel(wb);
			if (sheetNum <= 0) {
				RequestContext.getCurrentInstance().update(updateFormId);
				JsfUtil.addErrorMessage("Excel表格中无数据，请重新选择文件导入！");
				return null;
			}

			readExcel = ImportExcelUtil.readExcel(file);
			if (readExcel.size() <= 1) {
				RequestContext.getCurrentInstance().update(updateFormId);
				JsfUtil.addErrorMessage("Excel表格中无数据，请重新选择文件导入！");
				return null;
			}

			if(isDevice){
				this.deviceHeadRow = wb.getSheetAt(0).getRow(0);
			}else{
				this.psnHeadRow = wb.getSheetAt(0).getRow(0);
			}
		} catch (Exception e) {
			RequestContext.getCurrentInstance().update(updateFormId);
			JsfUtil.addErrorMessage("导入文件格式异常，请检查！");
			e.printStackTrace();
			return null;
		}
		return readExcel;
	}

	/**
	 * @Description : 码表Map
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/17 13:50
	 **/
	private Map<String, TsSimpleCode> getSimpleCode(String typeNo) {
		Map<String, TsSimpleCode> map = new HashMap<>();
		List<TsSimpleCode> list = this.commService.findallSimpleCodesByTypeId(typeNo);
		for(TsSimpleCode tsSimpleCode : list) {
			map.put(tsSimpleCode.getCodeName(), tsSimpleCode);
		}
		return map;
	}

	/**
	 * @Description : 人员-导入数据校验
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/17 13:56
	 **/
	private String checkRadhethPsnInfo(List<Object> list, Set<List<Object>> errorExcelSet, Set idcSet, Map<String, TsSimpleCode> educationMap, Map<String, TsSimpleCode> titleMap) throws Exception{
		StringBuffer sb = new StringBuffer();
		String idc = StringUtils.objectToString(list.get(1));

		if(StringUtils.isBlank(StringUtils.objectToString(list.get(0)))) {
			sb.append("姓名不能为空；");
			errorExcelSet.add(list);
		}
		if(StringUtils.isBlank(idc)) {
			sb.append("身份证号不能为空；");
			errorExcelSet.add(list);
		} else {
			// 身份证号重复
			if(idcSet.contains(StringUtils.upperCase(idc))) {
				sb.append("身份证号重复；");
				errorExcelSet.add(list);
				return sb.toString();
			} else {
				idcSet.add(StringUtils.upperCase(idc));

				// 验证身份证号格式
				String checkIDC = IdcUtils.checkIDC(idc);
				if(StringUtils.isNotBlank(checkIDC)) {
					sb.append("身份证号格式错误；");
					errorExcelSet.add(list);
				}
				// 验证身份证号是否重复
				List<Object> psnInfoList = psnPlanServiceImpl.findFieldValueIgnore("TD_ZW_PSNINFO", "IDC_CARD", idc, null);
				if(!CollectionUtils.isEmpty(psnInfoList)) {
					sb.append("身份证号重复；");
					errorExcelSet.add(list);
				}
			}
		}
		if(StringUtils.isBlank(StringUtils.objectToString(list.get(2)))) {
			sb.append("手机号码不能为空；");
			errorExcelSet.add(list);
		} else {
			if(!StringUtils.vertyMobilePhone(StringUtils.objectToString(list.get(2)))) {
				sb.append("手机号码格式不正确；");
				errorExcelSet.add(list);
			}
		}
		if(StringUtils.isBlank(StringUtils.objectToString(list.get(3)))) {
			sb.append("通讯地址不能为空；");
			errorExcelSet.add(list);
		}
		if(StringUtils.isBlank(StringUtils.objectToString(list.get(4)))) {
			sb.append("学历不能为空；");
			errorExcelSet.add(list);
		} else {
			if(null == educationMap.get(StringUtils.objectToString(list.get(4)))) {
				sb.append("学历选项不存在；");
				errorExcelSet.add(list);
			}
		}
		if(StringUtils.isBlank(StringUtils.objectToString(list.get(5)))) {
			sb.append("职称不能为空；");
			errorExcelSet.add(list);
		} else {
			if(null == titleMap.get(StringUtils.objectToString(list.get(5)))) {
				sb.append("职称选项不存在；");
				errorExcelSet.add(list);
			}
		}
		return sb.toString();
	}

	/**
	 * @Description : 人员信息-Excel导入
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/17 11:52
	 **/
	public void importPsnAction(FileUploadEvent event) {
		Set<List<Object>> errorExcelSet = new HashSet<>();
		List<List<Object>> readExcel = verfyFile(event, false);
		if(null == readExcel) {
			return;
		}

		// 学历
		Map<String, TsSimpleCode> educationMap = this.getSimpleCode("1001");
		// 职称
		Map<String, TsSimpleCode> titleMap = this.getSimpleCode("2003");
		Set idcSet = new HashSet();
		List<TdZwPsninfoZzsb> psninfoList = new ArrayList<>();
		try{
			for(int i = 0; i < readExcel.size(); i++) {
				if(i == 0) {
					continue;
				}
				List<Object> rowList = readExcel.get(i);
				String checkInfo = checkRadhethPsnInfo(rowList, errorExcelSet, idcSet, educationMap, titleMap);
				if("".equals(StringUtils.objectToString(checkInfo))) {
					TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
					psninfo.setTsUnit(this.sessionData.getUser().getTsUnit());
					psninfo.setAddSource(this.zwOrgType);
					psninfo.setCreateDate(new Date());
					psninfo.setCreateManid(this.sessionData.getUser().getRid());
					psninfo.setIdcCard(StringUtils.objectToString(rowList.get(1)));
					psninfo.setEmpName(StringUtils.objectToString(rowList.get(0)));
					// 性别
					if(Integer.valueOf(psninfo.getIdcCard().substring(
							psninfo.getIdcCard().length() - 2,
							psninfo.getIdcCard().length() - 1)) % 2 == 0) {
						psninfo.setSex("女");
					} else {
						psninfo.setSex("男");
					}
					// 出生日期
					Date calBirthday = IdcUtils.calBirthday(psninfo.getIdcCard());
					psninfo.setBirthday(DateUtils.formatDate(calBirthday,"yyyy-MM-dd"));
					psninfo.setMobileNo(StringUtils.objectToString(rowList.get(2)));
					psninfo.setLinkAddr(StringUtils.objectToString(rowList.get(3)));
					psninfo.setFkByEducationId(educationMap.get(StringUtils.objectToString(rowList.get(4))));
					psninfo.setFkByTitleId(titleMap.get(StringUtils.objectToString(rowList.get(5))));
					psninfoList.add(psninfo);
				} else {
					rowList.add(checkInfo);
				}
			}
		}catch (Exception e){
			JsfUtil.addErrorMessage("导入文件格式异常，请检查！");
			e.printStackTrace();
			return;
		}

		try{
			if(!CollectionUtils.isEmpty(psninfoList)){
				zwIntellReportServiceImpl.saveOrUpdateTdZwPsninfoList(psninfoList);
			}
		}catch(Exception e){
			JsfUtil.addErrorMessage("导入失败！");
			e.printStackTrace();
			return;
		}

		this.psnErrorList = new ArrayList<>(errorExcelSet);
		Integer total = readExcel.size() -1;
		if(!CollectionUtils.isEmpty(this.psnErrorList)) {
			this.psnIfDownload = 1;
			RequestContext.getCurrentInstance().execute("PF('PsnFileDialog').hide()");
			RequestContext.getCurrentInstance().update("mainForm:psnFileUpload");
			RequestContext.getCurrentInstance().update("mainForm:psnSelectButton");
			JsfUtil.addSuccessMessage("共导入" + total + "条数据。" + "成功" + (total-this.psnErrorList.size()) + "条数据，失败" + this.psnErrorList.size() + "条数据。请下载错误数据！");
			this.searchPsnDAction();
			return;
		} else {
			this.psnIfDownload = 0;
			RequestContext.getCurrentInstance().execute("PF('PsnFileDialog').hide()");
			RequestContext.getCurrentInstance().update("mainForm:psnFileUpload");
			RequestContext.getCurrentInstance().update("mainForm:psnSelectButton");
			JsfUtil.addSuccessMessage("共导入" + total + "条数据。" + "成功" + (total-this.psnErrorList.size()) + "条数据，失败" + this.psnErrorList.size() + "条数据。");
			this.searchPsnDAction();
			return;
		}
	}

	/**
	 * @Description : 人员信息-错误数据下载
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/17 14:34
	 **/
	public void downloadPsnErrorFile() {
		this.downloadExcel(this.psnErrorList, this.psnHeadRow, "资质填报人员错误数据");
	}


	/** 仪器信息 导入 */
	public void importDeviceAction(FileUploadEvent event) {
		Set<List<Object>> errorExcelSet = new HashSet<>();
		List<List<Object>> readExcel = verfyFile(event, true);
		if(null == readExcel) {
			return;
		}
		//检定要求Map acptReqMap 仪器状态Map instStateMap 仪器类型Map instTypeMap checkInstTypeMap
		if(null == acptReqMap){
			acptReqMap = this.getSimpleCode("5841");
		}
		if(null == instStateMap){
			instStateMap = this.getSimpleCode("5842");
		}
		if(null == instTypeMap){
			instTypeMap = this.getSimpleCode("5042");//放射与职卫的仪器类型一致
		}
		if(null == checkInstTypeMap){
			checkInstTypeMap = this.getSimpleCode("5024");//检查机构仪器类型
		}
		List<TsSimpleCode> checkInstFatherTypeList = new ArrayList<>();
		if(null != checkInstTypeMap){
			for(TsSimpleCode tmp : checkInstTypeMap.values()){
				if(StringUtils.isNotBlank(tmp.getCodeLevelNo()) && tmp.getCodeLevelNo().indexOf(".") == -1){
					checkInstFatherTypeList.add(tmp);
				}
			}
		}

		Set<String> custNoSet = new HashSet();
		List<TdZwInstinfoZzsb> instinfoList = new ArrayList<>();
		try{
			for(int i = 0; i < readExcel.size(); i++) {
				if(i == 0) {
					continue;
				}
				List<Object> rowList = readExcel.get(i);
				// 验证模板格式是否正确
				if(checkDeviceImportFile()){
					RequestContext.getCurrentInstance().update("mainForm:deviceFileUpload");
					JsfUtil.addErrorMessage("导入文件格式异常，请检查！");
					return;
				}
				String checkInfo = checkImportDeviceInfo(rowList, errorExcelSet, custNoSet);
				if(StringUtils.isBlank(checkInfo)) {
					TdZwInstinfoZzsb zwInstinfo = new TdZwInstinfoZzsb();
					TsSimpleCode instKindDetail = null;
					if(0 == this.zwOrgType || 2 == this.zwOrgType){
						instKindDetail = checkInstTypeMap.get(StringUtils.objectToString(rowList.get(0)));
					}else{
						instKindDetail = instTypeMap.get(StringUtils.objectToString(rowList.get(0)));
					}
					if(null != instKindDetail){
						zwInstinfo.setFkByInstKindDetalId(instKindDetail);
						if(1 == this.zwOrgType || 3 == this.zwOrgType){
							zwInstinfo.setFkByInstKindId(instKindDetail);
						}else{
							if(!CollectionUtils.isEmpty(checkInstFatherTypeList)){
								for(TsSimpleCode tmp : checkInstFatherTypeList){
									if(instKindDetail.getCodeLevelNo() != null
											&& instKindDetail.getCodeLevelNo().startsWith(tmp.getCodeNo()+".")){
										zwInstinfo.setFkByInstKindId(tmp);
										break;
									}
								}
							}
						}
					}

					zwInstinfo.setInstName(StringUtils.objectToString(rowList.get(1)));
					zwInstinfo.setInstModel(StringUtils.objectToString(rowList.get(2)));
					zwInstinfo.setInstCode(StringUtils.objectToString(rowList.get(3)));
					//生产厂家
					zwInstinfo.setInstFactory(StringUtils.objectToString(rowList.get(4)));
					//生产日期
					zwInstinfo.setInstProDate(null == rowList.get(5) ? null :
							DateUtils.parseDate(StringUtils.objectToString(rowList.get(5))));
					//购置日期
					zwInstinfo.setPurchaseDate(null == rowList.get(6) ? null :
							DateUtils.parseDate(StringUtils.objectToString(rowList.get(6))));
					//是否外包
					zwInstinfo.setOutter(StringUtils.objectToString(rowList.get(7)).equals("是") ? Short.parseShort("1") : 0);
					//序号
					zwInstinfo.setNum(null == rowList.get(8) ? null : Short.parseShort(rowList.get(8).toString()));
					//检定要求
					zwInstinfo.setAcptReqId(acptReqMap.get(StringUtils.objectToString(rowList.get(9))));
					Integer ifSuptOutChk = null;
					Integer ifNeedAcpt = null;
					Date lastAcptDate = null;
					Integer lastAcptCircle = null;
					TsSimpleCode instState = null;
					if(0 != zwOrgType){
						//是否需要校验
						ifNeedAcpt = StringUtils.objectToString(rowList.get(10)).equals("是") ? 1 : 0;
						//最近校验日期
						//校验周期（年）
						if(1 == ifNeedAcpt){
							lastAcptDate = DateUtils.parseDate(StringUtils.objectToString(rowList.get(11)));
							lastAcptCircle = Integer.parseInt(StringUtils.objectToString(rowList.get(12)));
						}
						//仪器状态
						instState = instStateMap.get(StringUtils.objectToString(rowList.get(13)));
					}else{
						//是否支持外检
						ifSuptOutChk = StringUtils.objectToString(rowList.get(10)).equals("是") ? 1 : 0;
						//是否需要校验
						ifNeedAcpt = StringUtils.objectToString(rowList.get(11)).equals("是") ? 1 : 0;
						//最近校验日期
						//校验周期（年）
						if(1 == ifNeedAcpt){
							lastAcptDate = DateUtils.parseDate(StringUtils.objectToString(rowList.get(12)));
							lastAcptCircle = Integer.parseInt(StringUtils.objectToString(rowList.get(13)));
						}
						//仪器状态
						instState = instStateMap.get(StringUtils.objectToString(rowList.get(14)));
					}
					zwInstinfo.setIfSuptOutChk(ifSuptOutChk);
					zwInstinfo.setIfNeedAcpt(ifNeedAcpt);
					zwInstinfo.setLastAcptDate(lastAcptDate);
					zwInstinfo.setLastAcptCircle(lastAcptCircle);
					zwInstinfo.setInstStateId(instState);

					zwInstinfo.setTsUnit(this.sessionData.getUser().getTsUnit());
					zwInstinfo.setCreateDate(new Date());
					zwInstinfo.setCreateManid(1);
					instinfoList.add(zwInstinfo);
				} else {
					rowList.add(checkInfo);
				}
			}
		}catch(Exception e){
			RequestContext.getCurrentInstance().update("mainForm:deviceFileUpload");
			JsfUtil.addErrorMessage("导入文件格式异常，请检查！");
			e.printStackTrace();
			return;
		}

		try{
			if(!CollectionUtils.isEmpty(instinfoList)){
				zwIntellReportServiceImpl.saveOrUpdateInstInfoList(instinfoList);
			}
		}catch(Exception e){
			RequestContext.getCurrentInstance().update("mainForm:deviceFileUpload");
			JsfUtil.addErrorMessage("文件导入失败！");
			e.printStackTrace();
			return;
		}

		this.deviceErrorList = new ArrayList<>(errorExcelSet);
		Integer total = readExcel.size() -1;
		if(!CollectionUtils.isEmpty(this.deviceErrorList)) {
			this.deviceIfDownLoad = 1;
			RequestContext.getCurrentInstance().execute("PF('DeviceFileDialog').hide()");
			RequestContext.getCurrentInstance().update("mainForm:deviceFileUpload");
			RequestContext.getCurrentInstance().update("mainForm:deviceSelectButton");//更新 显示错误数据下载
			JsfUtil.addSuccessMessage("共导入" + total + "条数据。" + "成功" + (total-this.deviceErrorList.size()) + "条数据，失败" + this.deviceErrorList.size() + "条数据。请下载错误数据！");
			this.addInstInitAction();
		} else {
			this.deviceIfDownLoad = 0;
			RequestContext.getCurrentInstance().execute("PF('DeviceFileDialog').hide()");
			RequestContext.getCurrentInstance().update("mainForm:deviceFileUpload");
			RequestContext.getCurrentInstance().update("mainForm:deviceSelectButton");
			JsfUtil.addSuccessMessage("共导入" + total + "条数据。" + "成功" + (total-this.deviceErrorList.size()) + "条数据，失败" + this.deviceErrorList.size() + "条数据。");
			this.addInstInitAction();
		}
	}
	/**
	 * @Description: 机构仪器导入 验证导入文件是否格式正常
	 * 异常返回true
	 *
	 * @MethodAuthor pw,2021年04月22日
	 */
	private boolean checkDeviceImportFile(){
		// 通过表头文件判断 具体哪个文件
		if(null == this.deviceHeadRow){
			return true;
		}else if(0 == zwOrgType && (this.deviceHeadRow.getLastCellNum() < 15 ||
				!this.deviceHeadRow.getCell(14).getStringCellValue().equals("仪器状态"))){
			return true;
		}else if(0 != zwOrgType && (this.deviceHeadRow.getLastCellNum() < 14 ||
				!this.deviceHeadRow.getCell(13).getStringCellValue().equals("仪器状态"))){
			return true;
		}
		return false;
	}

	private String checkImportDeviceInfo(List<Object> rowList, Set<List<Object>> errorExcelSet, Set<String> custNoSet) throws Exception{
		StringBuffer buffer = new StringBuffer();
		boolean ifErr = false;
		if(rowList.size() < 2){
			rowList.add("");
		}
		if(rowList.size() < 3){
			rowList.add("");
		}
		if(rowList.size() < 4){
			rowList.add("");
		}
		if(rowList.size() < 5){
			rowList.add("");
		}
		if(rowList.size() < 6){
			rowList.add("");
		}
		if(rowList.size() < 7){
			rowList.add("");
		}
		if(rowList.size() < 8){
			rowList.add("");
		}
		if(rowList.size() < 9){
			rowList.add("");
		}
		if(rowList.size() < 10){
			rowList.add("");
		}
		if(rowList.size() < 11){
			rowList.add("");
		}
		if(rowList.size() < 12){
			rowList.add("");
		}
		if(rowList.size() < 13){
			rowList.add("");
		}
		if(rowList.size() < 14){
			rowList.add("");
		}
		if(0 == zwOrgType && rowList.size() < 15){
			rowList.add("");
		}
		//检定要求Map acptReqMap 仪器状态Map instStateMap 仪器类型Map instTypeMap checkInstTypeMap
		//机构类型 0：检查机构 1：放射机构 2：诊断机构 3：职业卫生技术服务机构
		//仪器类型
		if((3 == zwOrgType || 1 == zwOrgType)){
			if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(0)))){
				buffer.append("仪器类型不能为空；");
				ifErr = true;
			}else if(null == instTypeMap.get(StringUtils.objectToString(rowList.get(0)))){
				buffer.append("仪器类型选项不存在；");
				ifErr = true;
			}
		}else if(StringUtils.isNotBlank(StringUtils.objectToString(rowList.get(0))) &&
				null == checkInstTypeMap.get(StringUtils.objectToString(rowList.get(0)))){
			buffer.append("仪器类型选项不存在；");
			ifErr = true;
		}

		// 仪器名称
		if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(1)))) {
			buffer.append("仪器名称不能为空；");
			ifErr = true;
		}else if(StringUtils.objectToString(rowList.get(1)).length() > 100){
			buffer.append("仪器名称过长；");
			ifErr = true;
		}
		// 仪器型号
		if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(2)))) {
			buffer.append("仪器型号不能为空；");
			ifErr = true;
		}else if(StringUtils.objectToString(rowList.get(2)).length() > 50){
			buffer.append("仪器型号过长；");
			ifErr = true;
		}
		// 仪器编号
		String custNo = StringUtils.objectToString(rowList.get(3));
		if(StringUtils.isBlank(custNo)) {
			buffer.append("仪器编号不能为空；");
			ifErr = true;
		}else if(custNo.length() > 50){
			buffer.append("仪器编号过长；");
			ifErr = true;
		}else{
			if(custNoSet.contains(custNo)){
				buffer.append("本单位下仪器编号重复；");
				ifErr = true;
			}else{
				Integer count = zwIntellReportServiceImpl.findExistsCustNo(custNo,
						null, this.sessionData.getUser().getTsUnit().getRid());
				if(count>0) {
					buffer.append("本单位下仪器编号重复；");
					ifErr = true;
				}
			}
			custNoSet.add(custNo);
		}
		//生产厂家 不需验证4
		//生产日期5
		Date productDate = null;
		if(null != rowList.get(5)){
			rowList.set(5, getDateByExcelDate(StringUtils.objectToString(rowList.get(5))));
			productDate = DateUtils.parseDate(StringUtils.objectToString(rowList.get(5)));
			if(null == productDate){
				buffer.append("生产日期格式错误；");
				ifErr = true;
			}
		}
		//购置日期6
		Date purchaseDate = null;
		if(null != rowList.get(6)){
			rowList.set(6,getDateByExcelDate(StringUtils.objectToString(rowList.get(6))));
			purchaseDate = DateUtils.parseDate(StringUtils.objectToString(rowList.get(6)));
			if(null == purchaseDate){
				buffer.append("购置日期格式错误；");
				ifErr = true;
			}
		}

		//最近校验日期
		Date acptDate = null;
		boolean showAcpt = false;

		// 是否外包
		if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(7)))) {
			buffer.append("是否外包不能为空；");
			ifErr = true;
		}
		//序号 不需验证8
		if(StringUtils.isNotBlank(StringUtils.objectToString(rowList.get(8))) &&
				!StringUtils.objectToString(rowList.get(8)).matches("^[1-9]\\d*|0$")){
			buffer.append("序号只能是非负整数；");
			ifErr = true;
		}
		// 检定要求
		if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(9)))) {
			buffer.append("检定要求不能为空；");
			errorExcelSet.add(rowList);
		}else if(null == acptReqMap.get(StringUtils.objectToString(rowList.get(9)))){
			buffer.append("检定要求选项不存在；");
			ifErr = true;
		}

		if(0 != zwOrgType){//只有职业健康检查机构有是否支持外检 其余没有
			// 是否需要校验
			if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(10)))) {
				buffer.append("是否需要校验不能为空；");
				ifErr = true;
			}
			if(StringUtils.isNotBlank(StringUtils.objectToString(rowList.get(10))) && StringUtils.objectToString(rowList.get(10)).equals("是")) {
				// 最近校验日期
				if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(11)))) {
					buffer.append("最近校验日期不能为空；");
					ifErr = true;
				}else{
					showAcpt = true;
				}
				// 校验周期（年）
				if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(12)))) {
					buffer.append("校验周期（年）不能为空；");
					ifErr = true;
				}else if(!StringUtils.objectToString(rowList.get(12)).matches("^[1-9]\\d*|0$")){
					buffer.append("校验周期（年）格式错误；");
					ifErr = true;
				}
			}
			// 仪器状态
			if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(13)))) {
				buffer.append("仪器状态不能为空；");
				ifErr = true;
			}else if(null == instStateMap.get(StringUtils.objectToString(rowList.get(13)))){
				buffer.append("仪器状态选项不存在；");
				ifErr = true;
			}
		}else{
			//是否支持外检
			if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(10)))){
				buffer.append("是否支持外检不能为空；");
				ifErr = true;
			}
			// 是否需要校验
			if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(11)))) {
				buffer.append("是否需要校验不能为空；");
				ifErr = true;
			}
			if(StringUtils.isNotBlank(StringUtils.objectToString(rowList.get(11))) && StringUtils.objectToString(rowList.get(11)).equals("是")) {
				// 最近校验日期
				if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(12)))) {
					buffer.append("最近校验日期不能为空；");
					ifErr = true;
				}else{
					showAcpt = true;
				}
				// 校验周期（年）
				if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(13)))) {
					buffer.append("校验周期（年）不能为空；");
					ifErr = true;
				}else if(!StringUtils.objectToString(rowList.get(13)).matches("^[1-9]\\d*|0$")){
					buffer.append("校验周期（年）格式错误；");
					ifErr = true;
				}
			}
			// 仪器状态
			if(StringUtils.isBlank(StringUtils.objectToString(rowList.get(14)))) {
				buffer.append("仪器状态不能为空；");
				ifErr = true;
			}else if(null == instStateMap.get(StringUtils.objectToString(rowList.get(14)))){
				buffer.append("仪器状态选项不存在；");
				ifErr = true;
			}
		}
		if(0 != zwOrgType){
			if(null != rowList.get(11)){
				rowList.set(11,getDateByExcelDate(StringUtils.objectToString(rowList.get(11))));
				acptDate = DateUtils.parseDate(StringUtils.objectToString(rowList.get(11)));
				if(null == acptDate && showAcpt){
					buffer.append("最近校验日期格式错误；");
					ifErr = true;
				}
			}
		}else{
			if(null != rowList.get(12)){
				rowList.set(12,getDateByExcelDate(StringUtils.objectToString(rowList.get(12))));
				acptDate = DateUtils.parseDate(StringUtils.objectToString(rowList.get(12)));
				if(null == acptDate && showAcpt){
					buffer.append("最近校验日期格式错误；");
					ifErr = true;
				}
			}
		}
		if(null != productDate && DateUtils.isDateAfter(productDate, new Date())){
			buffer.append("生产日期应小于等于当天；");
			ifErr = true;
		}
		if(null != purchaseDate && DateUtils.isDateAfter(purchaseDate, new Date())){
			buffer.append("购置日期应小于等于当天；");
			ifErr = true;
		}
		if(null != productDate && null != purchaseDate && DateUtils.isDateAfter(productDate,purchaseDate)){
			buffer.append("购置日期应大于等于生产日期；");
			ifErr = true;
		}
		if(null != acptDate && null != purchaseDate && DateUtils.isDateAfter(purchaseDate, acptDate)){
			buffer.append("最近校验日期应大于等于购置日期；");
			ifErr = true;
		}
		if(null == purchaseDate && null != productDate && null != acptDate && DateUtils.isDateAfter(productDate,acptDate)){
			buffer.append("最近校验日期应大于等于生产日期；");
			ifErr = true;
		}
		if(null != acptDate && DateUtils.isDateAfter(acptDate, new Date())){
			buffer.append("最近校验日期应小于等于当天；");
			ifErr = true;
		}
		if(ifErr){
			errorExcelSet.add(rowList);
		}
		return buffer.toString();
	}

	/**
	 * Excel日期 转java Date String
	 * Excel日期类型获取到的是天数数字
	 *  */
	private String getDateByExcelDate(String excelTime){
		if(StringUtils.isBlank(excelTime)){
			return excelTime;
		}
		Integer days = null;
		try{
			days = new BigDecimal(excelTime).intValue();
		}catch(Exception e){
			days = null;
		}
		if(null == days){
			return excelTime;
		}
		try{
			Calendar c = Calendar.getInstance();
			c.set(1900, 0, 1);
			c.add(Calendar.DATE, days - 2);
			return DateUtils.formatDate(c.getTime(), "yyyy-MM-dd");
		}catch(Exception e){
			return excelTime;
		}
	}

	/** 仪器错误数据下载 */
	public void downloadDeviceErrorFile() {
		String errFileName = "资质填报仪器错误数据";
		if(0 == zwOrgType){
			errFileName = "职业健康检查资质填报仪器错误数据";
		}else if(1 == zwOrgType){
			errFileName = "放射卫生服务资质填报仪器错误数据";
		}else if(2 == zwOrgType){
			errFileName = "职业病诊断资质填报仪器错误数据";
		}else if(3 == zwOrgType){
			errFileName = "职业卫生技术服务资质填报仪器错误数据";
		}
		this.downloadExcel(this.deviceErrorList, this.deviceHeadRow, errFileName);
	}

	public void downloadExcel(List<List<Object>> list, Row headRow, String fileNameStr) {
		HttpServletResponse response = JsfUtil.getResponse();
		List<String> headList = new ArrayList<>();
		if(null != headRow && !CollectionUtils.isEmpty(list)) {
			for (int i = 0; i < headRow.getLastCellNum(); i++) {
				headList.add(headRow.getCell(i).toString());
			}
			headList.add("数据错误原因");
			String[] title = headList.toArray(new String[headList.size()]);
			String fileName = fileNameStr + "_" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
			List<LinkedHashMap<String, Object>> content = new ArrayList<>();
			LinkedHashMap<String, Object> contentItem = null;
			for (int i = 0; i < list.size(); i++) {
				contentItem = new LinkedHashMap<>();
				for(int j=0; j<list.get(i).size(); j++) {
					contentItem.put(j + "", list.get(i).get(j));
				}
				content.add(contentItem);
			}
			ExportExcelUtil.downloadExcel(response, content, title, fileName);
		}
	}

	/**
	 * @Description : 人员信息初始化
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/17 14:55
	 **/
	private void initPsnInfoForImport() {
		this.psnInfoBase = new TdzwPsnInfoZzsbBase();
		TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
		psninfo.setTsUnit(this.sessionData.getUser().getTsUnit());
		/**设置资质人员来源*/
		psninfo.setAddSource(zwOrgType);
		psninfo.setFkByTitleId(new TsSimpleCode());
		psninfo.setFkByEducationId(new TsSimpleCode());
		psninfo.setFkByNation(new TsSimpleCode());
		psninfo.setFkByDegreeId(new TsSimpleCode());
		psninfo.setFkByTitleLevelId(new TsSimpleCode());
		psninfo.setFkByDoctLevelId(new TsSimpleCode());
		psninfo.setFkByDoctTypeId(new TsSimpleCode());
		psninfo.setFkByDoctSpecId(new TsSimpleCode());
		this.psnInfoBase.setPsnInfo(psninfo);
		this.psnInfoBase.setValidPsnInfoId(false);
		this.psnInfoBase.initPsnZzlb();
	}

	/**
	 * @Description : 仪器信息初始化
	 * @MethodAuthor: anjing
	 * @Date : 2021/3/19 9:28
	 **/
	private void initInstInfoForInport() {
		this.instinfo = new TdZwInstinfoZzsb();
		this.instinfo.setAcptReqId(new TsSimpleCode());
		this.instinfo.setInstStateId(new TsSimpleCode());
	}

	/**
	 * <p>方法描述： 放射卫生申报   服务项目   </p>
	 * @MethodAuthor  yzz，2021-04-21，fsServiceChangeActioin
	 **/
	public void fsServiceChangeActioin() {
		for(ZwItemPO zwItemPO : this.fsServiceObjList) {
			// 服务项目大类互斥项不能勾选
			if(zwItemPO.isIfSelected()) {
				// 相应服务项目大类下小类可勾选
				if(!CollectionUtils.isEmpty(zwItemPO.getZwItemSubPOList())) {
					for(ZwItemSubPO subPO : zwItemPO.getZwItemSubPOList()) {
						subPO.setDisabled(false);
					}
				}
				if("1".equals(zwItemPO.getExtendS1())) {
					for (ZwItemPO zwItemPO1 : this.fsServiceObjList) {
						if ("1".equals(zwItemPO1.getExtendS1()) && !zwItemPO.getCodeNo().equals(zwItemPO1.getCodeNo())) {
							zwItemPO1.setDisabled(true);
							// 相应服务项目大类下小类取消勾选
							zwItemPO1.setItemSubs(null);
							// 相应服务项目大类下小类不可勾选
							if (!CollectionUtils.isEmpty(zwItemPO1.getZwItemSubPOList())) {
								for (ZwItemSubPO subPO : zwItemPO1.getZwItemSubPOList()) {
									subPO.setDisabled(true);
								}
							}
						}
					}
				}
			} else {
				// 相应服务项目大类下小类取消勾选
				zwItemPO.setItemSubs(null);
				// 相应服务项目大类下小类不可勾选
				if(!CollectionUtils.isEmpty(zwItemPO.getZwItemSubPOList())) {
					for(ZwItemSubPO subPO : zwItemPO.getZwItemSubPOList()) {
						subPO.setDisabled(true);
					}
				}
				if("1".equals(zwItemPO.getExtendS1())) {
					for (ZwItemPO zwItemPO1 : this.fsServiceObjList) {
						if ("1".equals(zwItemPO1.getExtendS1()) && !zwItemPO.getCodeNo().equals(zwItemPO1.getCodeNo())) {
							zwItemPO1.setDisabled(false);
						}
					}
				}
			}
		}
	}


	/**
	 * <p>方法描述： 服务大类选择change事件 </p>
	 * @MethodAuthor  yzz，2021-06-30，serviceChange
	 **/
	public void serviceChange(){
		if(null!=serviceObj&&serviceObj.length>0){
			jkItemList= new ArrayList<>();
			for(String codeno:serviceObj){
				List<TsSimpleCode>  jkItems= commService.findallSimpleCodesByTypeIdAndExtends2("5527",jkMap.get(codeno).getCodeNo());
				ZwJkItemPO zwJkItemPO=new ZwJkItemPO();
				zwJkItemPO.setJcItem(jkMap.get(codeno));
				zwJkItemPO.setJcItemList(jkItems);
				jkItemList.add(zwJkItemPO);
			}
		}else{
			if(zwOrgType==3){
				JsfUtil.addErrorMessage("业务范围不能为空！");
			}else{
				JsfUtil.addErrorMessage("服务项目不能为空！");
			}
			jkItemList= new ArrayList<>();
		}
		initJcList(serviceObj);
	}


	/**
	 * <p>方法描述： 勾选大类时，实时更新小类中被勾选的值   </p>
	 * @MethodAuthor  yzz，2021-07-01，initJcList
	 **/
	public void initJcList(String[] serviceObj){
		if(zwOrginfoEntity.getZwSvritm() != null && zwOrginfoEntity.getZwSvritm().size() > 0){
			List<IZwSvritmZzsb> list = zwOrginfoEntity.getZwSvritm();
			Map<String,String[]> selectItems=new HashMap<String,String[]>();
			for(int i = 0 ; i < list.size();i++){
				IZwSvritmZzsb t = list.get(i);
				List<IZwSvritmSubZzsb> zwSvritmSubList = t.getZwSvritmSubList();
				if(!CollectionUtils.isEmpty(zwSvritmSubList)) {
					String[] itemSubs = new String[zwSvritmSubList.size()];
					for(int j = 0 ; j<zwSvritmSubList.size(); j++){
						IZwSvritmSubZzsb sub = zwSvritmSubList.get(j);

						if (null == sub.getFkBySubItemId()) {
							continue;
						}
						if(Arrays.asList(serviceObj).contains(String.valueOf(sub.getFkBySubItemId().getExtendS2()))){
							itemSubs[j] = sub.getFkBySubItemId().getRid().toString();
						}
					}
					selectItems.put(String.valueOf(t.getItemCode()),itemSubs);
				}
			}

			for(ZwJkItemPO itemPO:jkItemList){
				String key=itemPO.getJcItem().getCodeNo();
				if(null!=selectItems.get(key)&&selectItems.get(key).length>0){
					itemPO.setSelectedSerItmDetail(selectItems.get(key));
				}
			}
		}
		List<ZwJkItemRowPO> tempList =  convertZwJkItemPOToRowPO(jkItemList);
		List<ZwJkItemRowPO> resultList = new ArrayList<>(tempList.size());
		Iterator<ZwJkItemRowPO> iterator = tempList.iterator();
		while(iterator.hasNext()){
			ZwJkItemRowPO tmpRow = iterator.next();
			ZwJkItemRowPO realRow = null;
			for(ZwJkItemRowPO pageRow:this.jkItemRowList){
				if(tmpRow.equals(pageRow)){
					realRow = pageRow;
					break;
				}
			}
			if(realRow != null){
				resultList.add(realRow);
			}else{
				resultList.add(tmpRow);
			}
		}
		this.jkItemRowList = resultList;
		recalculateRowspan(jkItemRowList);
		changeSerItmDetail();
	}

	/**
	 * <p>描述 变更服务大类时同时改变下属小类勾选状态</p>
	 *
	 * @MethodAuthor gongzhe,2022/4/22 10:50,changeJcItem
	 * @return void
	 */
	public void changeJcItem(){
		String codeNo = null;
		String[] selectedJcItem = null;
		boolean out = zwOrginfoEntity!=null && zwOrginfoEntity.getOutWorkPower()!=null && zwOrginfoEntity.getOutWorkPower()==1;
		for(ZwJkItemRowPO itemPO:jkItemRowList){
			TsSimpleCode jcItem = itemPO.getJcItem();
			if(codeNo == null && jcItemRid.equals(jcItem.getRid().toString())){
				codeNo = jcItem.getCodeNo();
				selectedJcItem = itemPO.getSelectedJcItem();
			}
			if(codeNo!=null && codeNo.equals(jcItem.getCodeNo())){
				itemPO.setSelectedJcItem(selectedJcItem);
				if(selectedJcItem!=null && selectedJcItem.length>0){
					itemPO.setSelectedSerItmDetail(new String[]{itemPO.getSerItem().getRid().toString()});
					if(!out){
						//若不具备外检能力，则清空外检勾选情况
						itemPO.setSelectedIfExterInspect(new String[0]);
					}
				}else{
					itemPO.setSelectedSerItmDetail(new String[0]);
					itemPO.setSelectedIfExterInspect(new String[0]);
				}
			}
		}
	}
	/**
	 * <p>描述 变更服务小类，同时改变服务大类勾选清空，同时清空外检能力勾选</p>
	 *
	 * @MethodAuthor gongzhe,2022/4/22 10:54,changeSerItmDetail
	 * @return void
	 */
	public void changeSerItmDetail(){
		Map<String,List<ZwJkItemRowPO>> map = new HashMap<>();
		Map<String,List<ZwJkItemRowPO>> selectMap = new HashMap<>();
		boolean out = zwOrginfoEntity!=null && zwOrginfoEntity.getOutWorkPower()!=null && zwOrginfoEntity.getOutWorkPower()==1;
		for(ZwJkItemRowPO itemPO:jkItemRowList){
			TsSimpleCode jcItem = itemPO.getJcItem();
			List<ZwJkItemRowPO> list = map.get(jcItem.getCodeNo());
			if(list == null){
				list = new ArrayList<>();
			}
			list.add(itemPO);
			map.put(jcItem.getCodeNo(),list);
			if(itemPO.getDisabled()!=null && !itemPO.getDisabled()){
				List<ZwJkItemRowPO> selectList = selectMap.get(jcItem.getCodeNo());
				if(selectList == null){
					selectList = new ArrayList<>();
				}
				selectList.add(itemPO);
				selectMap.put(jcItem.getCodeNo(),selectList);
			}
		}
		for(ZwJkItemRowPO itemPO:jkItemRowList){
			String codeNo = itemPO.getJcItem().getCodeNo();
			if(selectMap.get(codeNo)!=null && selectMap.get(codeNo).size() == map.get(codeNo).size()){
				itemPO.setSelectedJcItem(new String[]{codeNo});
			}else{
				itemPO.setSelectedJcItem(new String[0]);
			}
			//若勾选框不可编辑或不具备外检能力，则清空外检勾选情况
			if(itemPO.getDisabled() || !out){
				//取消勾选
				itemPO.setSelectedIfExterInspect(new String[0]);
			}
		}
	}

	/**
	 * <p>描述 检定要求切换事件</p>
	 *
	 * @MethodAuthor gongzhe,2022/6/30 14:42,instAcptChangeListener
	 * @return void
	 */
	public void instAcptChangeListener(){
		showInstAcpt = false;
		for (TsSimpleCode simpleCode:acptReqList){
			if(simpleCode.getRid().equals(this.instinfo.getAcptReqId().getRid()) && "1".equals(simpleCode.getExtendS1())){
				//码表5841扩展字段1=1
				showInstAcpt = true;
				break;
			}
		}
	}

	/**
	 * @Description: 初始化外检车辆
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void initOutcar(){}

	/**
	 * @Description: 添加外检车辆
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void addOutcar(){}

	/**
	 * @Description: 修改外检车辆
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void modOutcar(){}

	/**
	 * @Description: 外检车辆提交
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void submitOutcar(){}

	/**
	 * @Description: 删除外检车辆
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void delOutcar(){}

	/**
	 * @Description: 外检车辆选择仪器 初始化仪器弹框
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void initOutCarInstAction(){}

	/**
	 * @Description: 外检车辆选择仪器 清空选择的仪器
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void clearOutCarInst(){}

	/**
	 * @Description: 外检车辆选择仪器 选择仪器弹出框确认
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void sureAddOutCarInst(){}

	/**
	 * @Description: 外检车辆选择仪器 选择仪器弹出框 过滤数据
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void searchOutCarInst(){}

	/**
	 * @Description: 外检车辆选择仪器 选择仪器弹出框 选择
	 *
	 * @MethodAuthor pw,2021年06月24日
	 */
	public void selectCarInstListAction(TdZwInstinfoZzsb instinfo){}
	/**
	 * 初始化科技攻关能力
	 */
	public void initSupportTrcInfo() {}
	/**
	 * 初始化科研课题信息
	 */
	public void initResearchInfo() {}

	/**
	 * 支撑机构-科研课题信息-人员选择弹出框-打开操作
	 */
	public void openSupportResearchPsnSelectAction() {}

	/**
	 * 支撑机构-科研课题信息-人员选择弹出框-确定操作
	 */
	public void saveSupportResearchPsnSelectAction() {}

	/**
	 * 支撑机构-科研课题信息-人员选择弹出框-查询操作
	 */
	public void searchSupportResearchPsnSelectAction() {}

	/**
	 * 支撑机构-科研课题信息-人员选择弹出框-选择操作
	 */
	public void rowSelectSupportResearchPsnSelectListener(SelectEvent event) {}

	/**
	 * 支撑机构-科研课题信息-人员选择弹出框-取消选择操作
	 */
	public void rowUnselectSupportResearchPsnSelectListener(UnselectEvent event) {}

	/**
	 * 支撑机构-科研课题信息-人员选择弹出框-切换选择操作
	 */
	public void toggleSelectSupportResearchPsnSelectListener(ToggleSelectEvent event) {}

	/**
	 * 支撑机构-科研课题信息-人员选择弹出框-清空操作
	 */
	public void emptySupportResearchPsnSelectAction() {}

	/**
	 * 支撑机构-科研课题信息-材料上传弹出框-打开弹出框
	 */
	public void openSupportResearchFileUploadAction() {}

	/**
	 * 支撑机构-科研课题信息-材料上传弹出框-上传
	 */
	public void uploadSupportResearchFileAction(FileUploadEvent event) {}

	/**
	 * 支撑机构-科研课题信息-材料上传弹出框-删除
	 */
	public void deleteSupportResearchFileAction() {}

	/**
	 * 支撑机构-科研课题信息-论文情况-添加操作
	 */
	public void addSupportThesisAction() {}

	/**
	 * 支撑机构-科研课题信息-论文情况-修改操作
	 */
	public void editSupportThesisAction(TdZwSupportThesis supportThesis) {}

	/**
	 * 支撑机构-科研课题信息-论文情况-保存操作
	 */
	public void saveSupportThesisAction() {}

	/**
	 * 支撑机构-科研课题信息-论文情况-删除操作
	 */
	public void delSupportThesisAction(Integer rid) {}

	/**
	 * 支撑机构-科研课题信息-专著情况-添加操作
	 */
	public void addSupportMonographAction() {}

	/**
	 * 支撑机构-科研课题信息-专著情况-修改操作
	 */
	public void editSupportMonographAction(TdZwSupportMomograph supportMomograph) {}

	/**
	 * 支撑机构-科研课题信息-专著情况-保存操作
	 */
	public void saveSupportMonographAction() {}

	/**
	 * 支撑机构-科研课题信息-专著情况-删除操作
	 */
	public void delSupportMonographAction(Integer rid) {}

	/**
	 * <p>方法描述： 支撑机构学术培训信息附件上传 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	public void handleSupportTrainFileUpload(FileUploadEvent event) {}

	/**
	 * 科研成果情况-专利
	 */
	public void addPatentInfoAction() {}

	/**
	 * 科研成果情况-标准
	 */
	public void addStandardInfoAction() {}

	/**
	 * 专利类型 change事件
	 */
	public void changePatentType(){}

	/**
	 * 专利信息-保存
	 */
	public void savePatentInfoAction(){}

	/**
	 * 标准信息-保存
	 */
	public void saveStandardInfoAction(){}


	/**
	 * 专利信息-删除
	 */
	public void delPatentInfoAction(Integer rid) {}

	/**
	 * 标准信息-删除
	 */
	public void delStandardInfoAction(Integer rid) {}

	/**
	 *专利信息-编辑
	 * @param rid
	 */
	public void editPatentInfoAction(Integer rid) {}
	/**
	 * 支撑机构-科研课题信息-课题情况添加-添加操作
	 */
	public void addSupportTopicAction(Integer type) {}

	/**
	 * 支撑机构-科研课题信息-课题情况-修改操作
	 */
	public void editSupportTopicAction(TdZwSupportTopic supportTopic) {}
	/**
	 * 支撑机构-科研课题信息-课题情况-删除操作
	 */
	public void delSupportTopicAction(Integer rid,Integer type) {}
	/**
	 * 课题信息-保存
	 */
	public void saveSupportTopicAction(){}
	public  void addSupportAdvanceAction(){};
	public  void delSupportAdvanceAction(Integer rid){};
	/**
	 * <p>方法描述：提交后需要初始化 扔这里 </p>
	 * pw 2023/10/26
	 **/
	public void afterSubmitInit() {}
	protected  boolean supportSubmitValidate(){return false;};

	public Integer getZwOrgType() {
		return zwOrgType;
	}

	public void setZwOrgType(Integer zwOrgType) {
		this.zwOrgType = zwOrgType;
	}

	public TdZwJsffjgCommAnnexZzsb getSelectZwAnnex() {
		return selectZwAnnex;
	}

	public void setSelectZwAnnex(TdZwJsffjgCommAnnexZzsb selectZwAnnex) {
		this.selectZwAnnex = selectZwAnnex;
	}

	public Integer getSelectZwAnnexType() {
		return selectZwAnnexType;
	}

	public void setSelectZwAnnexType(Integer selectZwAnnexType) {
		this.selectZwAnnexType = selectZwAnnexType;
	}

	public IZwInstZzsb getSelectOrgInst() {
		return selectOrgInst;
	}

	public void setSelectOrgInst(IZwInstZzsb selectOrgInst) {
		this.selectOrgInst = selectOrgInst;
	}

	public List<IZwInstZzsb> getZwInstInfoList() {
		return zwInstInfoList;
	}

	public void setZwInstInfoList(List<IZwInstZzsb> zwInstInfoList) {
		this.zwInstInfoList = zwInstInfoList;
	}

	public IZwOrgPsnsZzsb getSelectZwOrgPsn() {
		return selectZwOrgPsn;
	}

	public void setSelectZwOrgPsn(IZwOrgPsnsZzsb selectZwOrgPsn) {
		this.selectZwOrgPsn = selectZwOrgPsn;
	}

	public List<TsSimpleCode> getWorkOfficeList() {
		return workOfficeList;
	}

	public void setWorkOfficeList(List<TsSimpleCode> workOfficeList) {
		this.workOfficeList = workOfficeList;
	}

	public String[] getWorkOffices() {
		return workOffices;
	}

	public void setWorkOffices(String[] workOffices) {
		this.workOffices = workOffices;
	}

	public Map<TsSimpleCode, List<TsSimpleCode>> getDiagMap() {
		return diagMap;
	}

	public void setDiagMap(Map<TsSimpleCode, List<TsSimpleCode>> diagMap) {
		this.diagMap = diagMap;
	}

	public List<TsSimpleCode> getDiagList() {
		return diagList;
	}

	public void setDiagList(List<TsSimpleCode> diagList) {
		this.diagList = diagList;
	}

	public String getSelItemId() {
		return selItemId;
	}

	public void setSelItemId(String selItemId) {
		this.selItemId = selItemId;
	}

	public List<Object[]> getShowItemList() {
		return showItemList;
	}

	public void setShowItemList(List<Object[]> showItemList) {
		this.showItemList = showItemList;
	}

	public List<TdZwInstinfoZzsb> getDisplayList() {
		return displayList;
	}

	public void setDisplayList(List<TdZwInstinfoZzsb> displayList) {
		this.displayList = displayList;
	}

	public String getItmmodle() {
		return itmmodle;
	}

	public void setItmmodle(String itmmodle) {
		this.itmmodle = itmmodle;
	}

	public String getItmName() {
		return itmName;
	}

	public void setItmName(String itmName) {
		this.itmName = itmName;
	}

	public TdZwInstinfoZzsb getTdZwInstinfo() {
		return tdZwInstinfo;
	}

	public void setTdZwInstinfo(TdZwInstinfoZzsb tdZwInstinfo) {
		this.tdZwInstinfo = tdZwInstinfo;
	}

	public List<TdZwPsninfoZzsb> getDisplayDList() {
		return displayDList;
	}

	public void setDisplayDList(List<TdZwPsninfoZzsb> displayDList) {
		this.displayDList = displayDList;
	}

	public TdZwPsninfoZzsb getTdZwPsninfo() {
		return tdZwPsninfo;
	}

	public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
		this.tdZwPsninfo = tdZwPsninfo;
	}

//	public List<TsSimpleCode> getZcList() {
//		return zcList;
//	}
//
//	public void setZcList(List<TsSimpleCode> zcList) {
//		this.zcList = zcList;
//	}
//
//	public List<TsSimpleCode> getZwList() {
//		return zwList;
//	}
//
//	public void setZwList(List<TsSimpleCode> zwList) {
//		this.zwList = zwList;
//	}

	public String getItmDName() {
		return itmDName;
	}

	public void setItmDName(String itmDName) {
		this.itmDName = itmDName;
	}

	public String getTabTitle() {
		return tabTitle;
	}

	public void setTabTitle(String tabTitle) {
		this.tabTitle = tabTitle;
	}

	public List<TdZwInstinfoZzsb> getSrcList() {
		return srcList;
	}

	public void setSrcList(List<TdZwInstinfoZzsb> srcList) {
		this.srcList = srcList;
	}

	public List<SelectItemGroup> getSvrTypeList() {
		return svrTypeList;
	}

	public void setSvrTypeList(List<SelectItemGroup> svrTypeList) {
		this.svrTypeList = svrTypeList;
	}

	public String[] getSvrInstArr() {
		return svrInstArr;
	}

	public void setSvrInstArr(String[] svrInstArr) {
		this.svrInstArr = svrInstArr;
	}

	public Integer getSvrTypeId() {
		return svrTypeId;
	}

	public void setSvrTypeId(Integer svrTypeId) {
		this.svrTypeId = svrTypeId;
	}

	public Map<String, String> getCsProMap() {
		return csProMap;
	}

	public void setCsProMap(Map<String, String> csProMap) {
		this.csProMap = csProMap;
	}

	public String[] getSelectCsPro() {
		return selectCsPro;
	}

	public void setSelectCsPro(String[] selectCsPro) {
		this.selectCsPro = selectCsPro;
	}

	public List<TsSimpleCode> getAcptList() {
		return acptList;
	}

	public void setAcptList(List<TsSimpleCode> acptList) {
		this.acptList = acptList;
	}

	public List<TsSimpleCode> getSvrList() {
		return svrList;
	}

	public void setSvrList(List<TsSimpleCode> svrList) {
		this.svrList = svrList;
	}

	public Integer getPsnDialogType() {
		return psnDialogType;
	}

	public void setPsnDialogType(Integer psnDialogType) {
		this.psnDialogType = psnDialogType;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public Integer getUnitLevel() {
		return unitLevel;
	}

	public void setUnitLevel(Integer unitLevel) {
		this.unitLevel = unitLevel;
	}

	public TdZwJsffjgCommAnnexZzsb getYlAnnex() {
		return ylAnnex;
	}

	public void setYlAnnex(TdZwJsffjgCommAnnexZzsb ylAnnex) {
		this.ylAnnex = ylAnnex;
	}

	public Integer getSearchZoneId() {
		return searchZoneId;
	}

	public void setSearchZoneId(Integer searchZoneId) {
		this.searchZoneId = searchZoneId;
	}

	public void setZwAnnexList1(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList1) {
		this.zwAnnexList1 = zwAnnexList1;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList1() {
		return zwAnnexList1;
	}

	public void setZwAnnexList2(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList2) {
		this.zwAnnexList2 = zwAnnexList2;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList2() {
		return zwAnnexList2;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList3() {
		return zwAnnexList3;
	}

	public void setZwAnnexList3(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList3) {
		this.zwAnnexList3 = zwAnnexList3;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList4() {
		return zwAnnexList4;
	}

	public void setZwAnnexList4(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList4) {
		this.zwAnnexList4 = zwAnnexList4;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList5() {
		return zwAnnexList5;
	}

	public void setZwAnnexList5(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList5) {
		this.zwAnnexList5 = zwAnnexList5;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList6() {
		return zwAnnexList6;
	}

	public void setZwAnnexList6(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList6) {
		this.zwAnnexList6 = zwAnnexList6;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList7() {
		return zwAnnexList7;
	}

	public void setZwAnnexList7(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList7) {
		this.zwAnnexList7 = zwAnnexList7;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList8() {
		return zwAnnexList8;
	}

	public void setZwAnnexList8(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList8) {
		this.zwAnnexList8 = zwAnnexList8;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList9() {
		return zwAnnexList9;
	}

	public void setZwAnnexList9(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList9) {
		this.zwAnnexList9 = zwAnnexList9;
	}

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList20() {
		return zwAnnexList20;
	}

	public void setZwAnnexList20(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList20) {
		this.zwAnnexList20 = zwAnnexList20;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public List<TsSimpleCode> getFsList() {
		return fsList;
	}

	public void setFsList(List<TsSimpleCode> fsList) {
		this.fsList = fsList;
	}

	public String getWaringDate() {
		return waringDate;
	}

	public void setWaringDate(String waringDate) {
		this.waringDate = waringDate;
	}



	public List<TsSimpleCode> getZyList() {
		return zyList;
	}

	public List<SelectItem> getSvrTypeFsNewList() {
		return svrTypeFsNewList;
	}

	public void setSvrTypeFsNewList(List<SelectItem> svrTypeFsNewList) {
		this.svrTypeFsNewList = svrTypeFsNewList;
	}

	public void setZyList(List<TsSimpleCode> zyList) {
		this.zyList = zyList;

	}

	public List<SelectItem> getFsSvrTypeList() {
		return fsSvrTypeList;
	}

	public void setFsSvrTypeList(List<SelectItem> fsSvrTypeList) {
		this.fsSvrTypeList = fsSvrTypeList;
	}

	public TdzwPsnInfoZzsbBase getPsnInfoBase() {
		return psnInfoBase;
	}

	public void setPsnInfoBase(TdzwPsnInfoZzsbBase psnInfoBase) {
		this.psnInfoBase = psnInfoBase;
	}

	public String getItmDIdc() {
		return itmDIdc;
	}

	public void setItmDIdc(String itmDIdc) {
		this.itmDIdc = itmDIdc;
	}

	public String getItmDType() {
		return itmDType;
	}

	public void setItmDType(String itmDType) {
		this.itmDType = itmDType;
	}

	public String getDutySate() {
		return dutySate;
	}

	public void setDutySate(String dutySate) {
		this.dutySate = dutySate;
	}

	public List<TsSimpleCode> getJsfwList() {
		return jsfwList;
	}

	public void setJsfwList(List<TsSimpleCode> jsfwList) {
		this.jsfwList = jsfwList;
	}

	public boolean isIfHethFj() {
		return ifHethFj;
	}

	public void setIfHethFj(boolean ifHethFj) {
		this.ifHethFj = ifHethFj;
	}

	public TdZwInstinfoZzsb getInstinfo() {
		return instinfo;
	}

	public void setInstinfo(TdZwInstinfoZzsb instinfo) {
		this.instinfo = instinfo;
	}

	public String getIfPersonSummary() {
		return ifPersonSummary;
	}

	public void setIfPersonSummary(String ifPersonSummary) {
		this.ifPersonSummary = ifPersonSummary;
	}

	public String getFillDesc() {
		return fillDesc;
	}

	public void setFillDesc(String fillDesc) {
		this.fillDesc = fillDesc;
	}

	public List<List<String>> getPersonSummaryList() {
		return personSummaryList;
	}

	public void setPersonSummaryList(List<List<String>> personSummaryList) {
		this.personSummaryList = personSummaryList;
	}

	public List<TsSimpleCode> getTopOfInstitutionList() {
		return topOfInstitutionList;
	}

	public void setTopOfInstitutionList(List<TsSimpleCode> topOfInstitutionList) {
		this.topOfInstitutionList = topOfInstitutionList;
	}

	public Map<Integer, TsSimpleCode> getTopInstitutionMap() {
		return topInstitutionMap;
	}

	public void setTopInstitutionMap(Map<Integer, TsSimpleCode> topInstitutionMap) {
		this.topInstitutionMap = topInstitutionMap;
	}

	public Map<Integer, List<TsSimpleCode>> getInstitutionMap() {
		return institutionMap;
	}

	public void setInstitutionMap(Map<Integer, List<TsSimpleCode>> institutionMap) {
		this.institutionMap = institutionMap;
	}

	public Map<Integer, List<TdZwTjorgQualAnnexZzsb>> getQualAnnexMap() {
		return qualAnnexMap;
	}

	public void setQualAnnexMap(Map<Integer, List<TdZwTjorgQualAnnexZzsb>> qualAnnexMap) {
		this.qualAnnexMap = qualAnnexMap;
	}

	public Integer getCodeId() {
		return codeId;
	}

	public void setCodeId(Integer codeId) {
		this.codeId = codeId;
	}

	public TdZwTjorgQualAnnexZzsb getAnnexTmp() {
		return annexTmp;
	}

	public void setAnnexTmp(TdZwTjorgQualAnnexZzsb annexTmp) {
		this.annexTmp = annexTmp;
	}

	public String getTopInsPanelStr() {
		return topInsPanelStr;
	}

	public void setTopInsPanelStr(String topInsPanelStr) {
		this.topInsPanelStr = topInsPanelStr;
	}


	public void setPsnfile(StreamedContent psnfile) {
		this.psnfile = psnfile;
	}

	public Row getPsnHeadRow() {
		return psnHeadRow;
	}

	public void setPsnHeadRow(Row psnHeadRow) {
		this.psnHeadRow = psnHeadRow;
	}

	public List<List<Object>> getPsnErrorList() {
		return psnErrorList;
	}

	public void setPsnErrorList(List<List<Object>> psnErrorList) {
		this.psnErrorList = psnErrorList;
	}

	public Integer getPsnIfDownload() {
		return psnIfDownload;
	}

	public void setPsnIfDownload(Integer psnIfDownload) {
		this.psnIfDownload = psnIfDownload;
	}

	public List<TsSimpleCode> getAcptReqList() {
		return acptReqList;
	}

	public void setAcptReqList(List<TsSimpleCode> acptReqList) {
		this.acptReqList = acptReqList;
	}

	public List<TsSimpleCode> getInstStateList() {
		return instStateList;
	}

	public void setInstStateList(List<TsSimpleCode> instStateList) {
		this.instStateList = instStateList;
	}

	public StreamedContent getDeviceFile() {
		InputStream stream = null;
		try {
			String fileName = null;
			String downLoadFileName = null;
			//0：检查机构 1：放射机构 2：诊断机构 3：职业卫生技术服务机构
			if(0 == zwOrgType){
				downLoadFileName = "职业健康检查";
				fileName = "职业健康检查机构资质填报仪器导入模板.xls";
			}else if(1 == zwOrgType){
				//放射模板名称赋值
				fileName = "职业卫生技术服务机构资质填报仪器导入模板.xls";//放射与职业卫生技术服务机构的模板一致
				downLoadFileName = "放射卫生服务";
			}else if(2 == zwOrgType){
				//诊断模板名称赋值
				fileName = "职业病诊断机构资质填报仪器导入模板.xls";//诊断的模板 单独
				downLoadFileName = "职业病诊断";
			}else if(3 == zwOrgType){
				fileName = "职业卫生技术服务机构资质填报仪器导入模板.xls";
				downLoadFileName = "职业卫生技术服务";
			}
			if(null == fileName || null == downLoadFileName){
				JsfUtil.addErrorMessage("文件下载失败!");
			}else{
				stream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream("/resources/template/excel/"+fileName);
				this.deviceFile = new DefaultStreamedContent(stream, "application/xls", URLEncoder.encode(downLoadFileName + "机构资质填报仪器导入模板.xls", "UTF-8"));
			}
		} catch (Exception e) {
			e.printStackTrace();
			JsfUtil.addErrorMessage("文件下载失败!");
		} finally{
			if(stream != null){
				try {
					stream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		}
		return deviceFile;
	}

	public void onChangeOutWorkPower(){
		if(zwOrginfoEntity!=null && zwOrginfoEntity.getOutWorkPower()!=null && zwOrginfoEntity.getOutWorkPower()==1){
			RequestContext.getCurrentInstance().update("mainForm:tabView:serDetailDatatable");
		}else{
			//不具备
			for(ZwJkItemRowPO itemPO:jkItemRowList){
				itemPO.setSelectedIfExterInspect(new String[0]);
			}
		}
		this.initInstitutionList();
	}

	/**
	 * <p>描述 转换ZwJkItemPO-->ZwJkItemRowPO</p>
	 *
	 * @param ZwJkItemPOS
	 * @MethodAuthor gongzhe,2022/4/15 15:43,convertZwJkItemPOToRowPO
	 * @return java.util.List<com.chis.modules.heth.zzsb.logic.ZwJkItemRowPO>
	 */
	public List<ZwJkItemRowPO> convertZwJkItemPOToRowPO(List<ZwJkItemPO> ZwJkItemPOS){
		boolean out = zwOrginfoEntity!=null && zwOrginfoEntity.getOutWorkPower()!=null && zwOrginfoEntity.getOutWorkPower()==1;
		List<ZwJkItemRowPO> result = new ArrayList<>();
		int start = 0;
		Map<Integer,Integer> rowspanMap = new HashMap<>();
		if(this.zwOrginfoEntity.getState()==1){
			//查看时，需过滤掉停用的码表，计算需要合并的行
			for (int i = 0; i < ZwJkItemPOS.size(); i++) {
				ZwJkItemPO itemPO = ZwJkItemPOS.get(i);
				TsSimpleCode jcItem = itemPO.getJcItem();
				List<TsSimpleCode> jcItemList = itemPO.getJcItemList();
				//已勾选的服务小类
				List<String> selectedSerItmDetail = itemPO.getSelectedSerItmDetail()==null ? null: Arrays.asList(itemPO.getSelectedSerItmDetail());
				if(CollectionUtils.isEmpty(selectedSerItmDetail)){
					rowspanMap.put(jcItem.getRid(),0);
					continue;
				}
				//未停用码表数量
				int count = 0;
				for (int j = 0; j < jcItemList.size(); j++) {
					TsSimpleCode c = jcItemList.get(j);
					if(selectedSerItmDetail.contains(c.getRid().toString())){
						count++;
					}
				}
				rowspanMap.put(jcItem.getRid(),count);
			}
		}
		for (int i = 0; i < ZwJkItemPOS.size(); i++) {
			ZwJkItemPO itemPO = ZwJkItemPOS.get(i);
			TsSimpleCode jcItem = itemPO.getJcItem();
			List<TsSimpleCode> jcItemList = itemPO.getJcItemList();
			if(i>0){
				if(this.zwOrginfoEntity.getState()==1){
					start += rowspanMap.get(ZwJkItemPOS.get(i-1).getJcItem().getRid());
				}else{
					start += ZwJkItemPOS.get(i-1).getJcItemList().size();
				}
			}
			//已勾选的服务小类
			List<String> selectedSerItmDetail = itemPO.getSelectedSerItmDetail()==null ? null: Arrays.asList(itemPO.getSelectedSerItmDetail());
			List<String> selectedIfExterInspect = itemPO.getSelectedIfExterInspect()==null ? null: Arrays.asList(itemPO.getSelectedIfExterInspect());
			for (int j = 0; j < jcItemList.size(); j++) {
				TsSimpleCode c = jcItemList.get(j);
				if(this.zwOrginfoEntity.getState()==1 && (selectedSerItmDetail == null || !selectedSerItmDetail.contains(c.getRid().toString()))) {
					//如果是查看，且勾选的里面未包含该子项，则跳过
					continue;
				}
				ZwJkItemRowPO row = new ZwJkItemRowPO();
				row.setJcItem(jcItem);
				row.setSerItem(c);
				if(this.zwOrginfoEntity.getState()==1){
					row.setRowspan(rowspanMap.get(jcItem.getRid()));
				}else{
					row.setRowspan(jcItemList.size());
				}
				row.setRowspanStart(start);
				if(selectedSerItmDetail!=null && selectedSerItmDetail.contains(c.getRid().toString())){
					row.setSelectedSerItmDetail(new String[]{c.getRid().toString()});
				}
				if(out){
					row.setIfExterInspect("0");
				}
				if(selectedIfExterInspect!=null && selectedIfExterInspect.contains(c.getRid().toString())){
					row.setSelectedIfExterInspect(new String[]{c.getRid().toString()});
					row.setIfExterInspect("1");
				}
				result.add(row);
			}
		}
		return result;
	}

	/**
	 * <p>描述 重新计算合并行以及起始行</p>
	 *
	 * @MethodAuthor gongzhe,2022/4/25 10:12,recalculateRowspan
	 * @return void
	 */
	private void recalculateRowspan(List<ZwJkItemRowPO> list){
		Map<Integer,Integer> rowspanMap = new HashMap<>();
		for (int i = 0; i < list.size(); i++) {
			ZwJkItemRowPO itemPO = list.get(i);
			TsSimpleCode jcItem = itemPO.getJcItem();
			Integer count = rowspanMap.get(jcItem.getRid());
			count = count == null ? 1 : ++count;
			rowspanMap.put(jcItem.getRid(),count);
		}
		int start = 0;
		for (int i = 0; i < list.size(); i++) {
			ZwJkItemRowPO itemPO = list.get(i);
			TsSimpleCode jcItem = itemPO.getJcItem();
			if(i>0 && i == start+rowspanMap.get(list.get(i-1).getJcItem().getRid())){
				start += rowspanMap.get(list.get(i-1).getJcItem().getRid());
			}
			itemPO.setRowspanStart(start);
			itemPO.setRowspan(rowspanMap.get(jcItem.getRid()));
		}
	}
	/**
	 * <p>Description：具备资质能力 change 事件 </p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	public void changeQualTypeAction(TsSimpleCode t) {
		if (t.isIfSelected() && this.qualTypeMap.containsKey(t.getRid())) {
			if("1".equals(this.qualTypeMap.get(t.getRid()).getExtendS1())){
				clearQualType();
			}else{
				selectQualType();
			}
		}
	}

	/**
	 * <p>Description： </p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	public void clearQualType(){
		if("1".equals(this.unitType) && !CollectionUtils.isEmpty(this.qualTypeList1)){
			for (TsSimpleCode tsSimpleCode : qualTypeList1) {
				if(!"1".equals(tsSimpleCode.getExtendS1())){
					tsSimpleCode.setIfSelected(false);
				}
			}
		}else if("2".equals(this.unitType) && !CollectionUtils.isEmpty(this.qualTypeList2)){
			for (TsSimpleCode tsSimpleCode : qualTypeList2) {
				if(!"1".equals(tsSimpleCode.getExtendS1())){
					tsSimpleCode.setIfSelected(false);
				}
			}
		}
	}

	/**
	 * <p>Description： </p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	public void selectQualType(){
		if("1".equals(this.unitType) && !CollectionUtils.isEmpty(this.qualTypeList1)){
			for (TsSimpleCode tsSimpleCode : qualTypeList1) {
				if("1".equals(tsSimpleCode.getExtendS1())){
					tsSimpleCode.setIfSelected(false);
					break;
				}
			}
		}else if("2".equals(this.unitType) && !CollectionUtils.isEmpty(this.qualTypeList2)){
			for (TsSimpleCode tsSimpleCode : qualTypeList2) {
				if("1".equals(tsSimpleCode.getExtendS1())){
					tsSimpleCode.setIfSelected(false);
					break;
				}
			}
		}
	}

	/**
	 * <p>Description：职业病因素监测开展情况 添加 </p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	public void addSupportChk(){
		TdZwSupportChk supportChk=new TdZwSupportChk();
		supportChk.setFkByMainId((TdZwSupportOrgZzsb)zwOrginfoEntity);
		supportChk.setCreateDate(new Date());
		supportChk.setCreateManid(Global.getUser().getRid());
		this.zwOrginfoEntity.getSupportChkList().add(0,supportChk);
	}

	/**
	 * <p>Description：删除 职业病因素监测开展情况</p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	public void delSupportChk(TdZwSupportChk supportChk){
		this.zwOrginfoEntity.getSupportChkList().remove(supportChk);
		JsfUtil.addSuccessMessage("删除成功！");
	}


	/**
	 * <p>Description：添加  财政投入情况</p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	public void addSupportFund(){
		TdZwSupportFund supportFund=new TdZwSupportFund();
		supportFund.setFkByMainId((TdZwSupportOrgZzsb)zwOrginfoEntity);
		supportFund.setCreateDate(new Date());
		supportFund.setCreateManid(Global.getUser().getRid());
		this.zwOrginfoEntity.getSupportFundList().add(0,supportFund);
	}

	/**
	 * <p>Description：删除 财政投入情况 </p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	public void delSupportFund(TdZwSupportFund supportFund){
		this.zwOrginfoEntity.getSupportFundList().remove(supportFund);
		JsfUtil.addSuccessMessage("删除成功！");
	}


	public void setDeviceFile(StreamedContent deviceFile) {
		this.deviceFile = deviceFile;
	}

	public Row getDeviceHeadRow() {
		return deviceHeadRow;
	}

	public void setDeviceHeadRow(Row deviceHeadRow) {
		this.deviceHeadRow = deviceHeadRow;
	}

	public List<List<Object>> getDeviceErrorList() {
		return deviceErrorList;
	}

	public void setDeviceErrorList(List<List<Object>> deviceErrorList) {
		this.deviceErrorList = deviceErrorList;
	}

	public Integer getDeviceIfDownLoad() {
		return deviceIfDownLoad;
	}

	public void setDeviceIfDownLoad(Integer deviceIfDownLoad) {
		this.deviceIfDownLoad = deviceIfDownLoad;
	}

	public Map<String, TsSimpleCode> getAcptReqMap() {
		return acptReqMap;
	}

	public void setAcptReqMap(Map<String, TsSimpleCode> acptReqMap) {
		this.acptReqMap = acptReqMap;
	}

	public Map<String, TsSimpleCode> getInstStateMap() {
		return instStateMap;
	}

	public void setInstStateMap(Map<String, TsSimpleCode> instStateMap) {
		this.instStateMap = instStateMap;
	}

	public Map<String, TsSimpleCode> getInstTypeMap() {
		return instTypeMap;
	}

	public void setInstTypeMap(Map<String, TsSimpleCode> instTypeMap) {
		this.instTypeMap = instTypeMap;
	}

	public Map<String, TsSimpleCode> getCheckInstTypeMap() {
		return checkInstTypeMap;
	}

	public void setCheckInstTypeMap(Map<String, TsSimpleCode> checkInstTypeMap) {
		this.checkInstTypeMap = checkInstTypeMap;
	}

	public List<TdZwTjorgOutcarZzsb> getTjorgOutcarZzsbList() {
		return tjorgOutcarZzsbList;
	}

	public void setTjorgOutcarZzsbList(List<TdZwTjorgOutcarZzsb> tjorgOutcarZzsbList) {
		this.tjorgOutcarZzsbList = tjorgOutcarZzsbList;
	}

	public TdZwTjorgOutcarZzsb getOutcarZzsb() {
		return outcarZzsb;
	}

	public void setOutcarZzsb(TdZwTjorgOutcarZzsb outcarZzsb) {
		this.outcarZzsb = outcarZzsb;
	}

	public List<TdZwInstinfoZzsb> getOutCarInstList() {
		return outCarInstList;
	}

	public void setOutCarInstList(List<TdZwInstinfoZzsb> outCarInstList) {
		this.outCarInstList = outCarInstList;
	}

	public List<Integer> getOutCarInstSelectRidList() {
		return outCarInstSelectRidList;
	}

	public void setOutCarInstSelectRidList(List<Integer> outCarInstSelectRidList) {
		this.outCarInstSelectRidList = outCarInstSelectRidList;
	}

	public String getOutCarInstNames() {
		return outCarInstNames;
	}

	public void setOutCarInstNames(String outCarInstNames) {
		this.outCarInstNames = outCarInstNames;
	}

	public String getSearchOutCarInstName() {
		return searchOutCarInstName;
	}

	public void setSearchOutCarInstName(String searchOutCarInstName) {
		this.searchOutCarInstName = searchOutCarInstName;
	}

	public String getSearchOutCarModel() {
		return searchOutCarModel;
	}

	public void setSearchOutCarModel(String searchOutCarModel) {
		this.searchOutCarModel = searchOutCarModel;
	}

	public Map<String, TsSimpleCode> getJkMap() {
		return jkMap;
	}


	public void setJkMap(Map<String, TsSimpleCode> jkMap) {
		this.jkMap = jkMap;
	}


	public Map<String, TsSimpleCode> getJkItemSelectedMap() {
		return jkItemSelectedMap;
	}


	public void setJkItemSelectedMap(Map<String, TsSimpleCode> jkItemSelectedMap) {
		this.jkItemSelectedMap = jkItemSelectedMap;
	}


	public TdZwTjorgOutcarZzsb getModOutcarZzsb() {
		return modOutcarZzsb;
	}

	public void setModOutcarZzsb(TdZwTjorgOutcarZzsb modOutcarZzsb) {
		this.modOutcarZzsb = modOutcarZzsb;
	}
	public List<TdZwOcchethItemsShow> getItemsShows() {
		return itemsShows;
	}

	public void setItemsShows(List<TdZwOcchethItemsShow> itemsShows) {
		this.itemsShows = itemsShows;
	}

	public Boolean getIfZzsbSave() {
		return ifZzsbSave;
	}

	public void setIfZzsbSave(Boolean ifZzsbSave) {
		this.ifZzsbSave = ifZzsbSave;
	}

	public String getJcItemRid() {
		return jcItemRid;
	}

	public void setJcItemRid(String jcItemRid) {
		this.jcItemRid = jcItemRid;
	}

	public Boolean getIfShowRcdOrg() {
		return ifShowRcdOrg;
	}

	public void setIfShowRcdOrg(Boolean ifShowRcdOrg) {
		this.ifShowRcdOrg = ifShowRcdOrg;
	}

	public List<TsSimpleCode> getRcdOrgList() {
		return rcdOrgList;
	}

	public void setRcdOrgList(List<TsSimpleCode> rcdOrgList) {
		this.rcdOrgList = rcdOrgList;
	}

	public Map<Integer, String> getRcdOrgMap() {
		return rcdOrgMap;
	}

	public void setRcdOrgMap(Map<Integer, String> rcdOrgMap) {
		this.rcdOrgMap = rcdOrgMap;
	}

	public Boolean getIfZoneApprove() {
		return ifZoneApprove;
	}

	public void setIfZoneApprove(Boolean ifZoneApprove) {
		this.ifZoneApprove = ifZoneApprove;
	}

	public String getSelectZoneApproveName() {
		return selectZoneApproveName;
	}

	public void setSelectZoneApproveName(String selectZoneApproveName) {
		this.selectZoneApproveName = selectZoneApproveName;
	}

	public String getSelectZoneApproveRid() {
		return selectZoneApproveRid;
	}

	public void setSelectZoneApproveRid(String selectZoneApproveRid) {
		this.selectZoneApproveRid = selectZoneApproveRid;
	}

	public List<TsZone> getAllZoneApproveList() {
		return allZoneApproveList;
	}

	public void setAllZoneApproveList(List<TsZone> allZoneApproveList) {
		this.allZoneApproveList = allZoneApproveList;
	}

	public TreeNode getZoneApproveTree() {
		return zoneApproveTree;
	}

	public void setZoneApproveTree(TreeNode zoneApproveTree) {
		this.zoneApproveTree = zoneApproveTree;
	}

	public TreeNode[] getSelectZoneApproves() {
		return selectZoneApproves;
	}

	public void setSelectZoneApproves(TreeNode[] selectZoneApproves) {
		this.selectZoneApproves = selectZoneApproves;
	}

	public String getOutCarVersion() {
		return outCarVersion;
	}

	public void setOutCarVersion(String outCarVersion) {
		this.outCarVersion = outCarVersion;
	}

	public Integer getOutCarOpFile() {
		return outCarOpFile;
	}

	public void setOutCarOpFile(Integer outCarOpFile) {
		this.outCarOpFile = outCarOpFile;
	}

	public String getItmCode() {
		return itmCode;
	}

	public void setItmCode(String itmCode) {
		this.itmCode = itmCode;
	}

	public Boolean getShowInstAcpt() {
		return showInstAcpt;
	}

	public void setShowInstAcpt(Boolean showInstAcpt) {
		this.showInstAcpt = showInstAcpt;
	}

	public String getZkCheckRst() {
		return zkCheckRst;
	}

	public void setZkCheckRst(String zkCheckRst) {
		this.zkCheckRst = zkCheckRst;
	}

	public List<Object[]> getZwCheckRstList() {
		return zwCheckRstList;
	}

	public void setZwCheckRstList(List<Object[]> zwCheckRstList) {
		this.zwCheckRstList = zwCheckRstList;
	}

	public Map<Integer, TsSimpleCode> getCacheInstSimpleCodeMap() {
		return cacheInstSimpleCodeMap;
	}

	public void setCacheInstSimpleCodeMap(Map<Integer, TsSimpleCode> cacheInstSimpleCodeMap) {
		this.cacheInstSimpleCodeMap = cacheInstSimpleCodeMap;
	}

	public String getIfZoneRecord() {
		return ifZoneRecord;
	}

	public void setIfZoneRecord(String ifZoneRecord) {
		this.ifZoneRecord = ifZoneRecord;
	}

	public String getPsnInfoForm() {
		return psnInfoForm;
	}

	public void setPsnInfoForm(String psnInfoForm) {
		this.psnInfoForm = psnInfoForm;
	}

	public List<TdZwPsnZzlbZzsb> getRemoveZzlbList() {
		return removeZzlbList;
	}

	public void setRemoveZzlbList(List<TdZwPsnZzlbZzsb> removeZzlbList) {
		this.removeZzlbList = removeZzlbList;
	}

    public Boolean getHasResearchAndAcademicInfo() {
        return hasResearchAndAcademicInfo;
    }

    public void setHasResearchAndAcademicInfo(Boolean hasResearchAndAcademicInfo) {
        this.hasResearchAndAcademicInfo = hasResearchAndAcademicInfo;
    }

    public SupportResearchPsnSelVO getSupportResearchPsnSelVO() {
        return supportResearchPsnSelVO;
    }

    public void setSupportResearchPsnSelVO(SupportResearchPsnSelVO supportResearchPsnSelVO) {
        this.supportResearchPsnSelVO = supportResearchPsnSelVO;
    }
	/**
	 * <p>Description：初始化支撑能力 </p>
	 * <p>Author： yzz 2023-10-30 </p>
	 */
	public void initTechType(){
		//初始化支撑能力
		if(!CollectionUtils.isEmpty(zwOrginfoEntity.getSupportTechList())){
			//树状结构排序
			Collections.sort(zwOrginfoEntity.getSupportTechList(), new Comparator<TdZwSupportTech>() {
				@Override
				public int compare(TdZwSupportTech o1, TdZwSupportTech o2) {
					if(ObjectUtil.isNotNull(o1.getFkByTechTypeId().getCodeLevelNo()) && ObjectUtil.isNotNull(o2.getFkByTechTypeId().getCodeLevelNo())){
						int k = o1.getFkByTechTypeId().getCodeLevelNo().compareTo(o2.getFkByTechTypeId().getCodeLevelNo());
						if(k == 0){
							if (ObjectUtil.isNotNull(o1.getFkByTechTypeId().getNum()) && ObjectUtil.isNotNull(o2.getFkByTechTypeId().getNum())) {
								int i = o1.getFkByTechTypeId().getNum().compareTo(o2.getFkByTechTypeId().getNum());
								if(i == 0){
									return o1.getFkByTechTypeId().getCodeNo().compareTo(o2.getFkByTechTypeId().getCodeNo());
								}
								return i;
							} else if (ObjectUtil.isNotNull(o1.getFkByTechTypeId().getNum())) {
								return -1;
							} else if (ObjectUtil.isNotNull(o2.getFkByTechTypeId().getNum())) {
								return 1;
							} else {
								return o1.getFkByTechTypeId().getCodeNo().compareTo(o2.getFkByTechTypeId().getCodeNo());
							}
						}
						return k;
					}else if (ObjectUtil.isNotNull(o1.getFkByTechTypeId().getCodeLevelNo())) {
						return -1;
					} else if (ObjectUtil.isNotNull(o2.getFkByTechTypeId().getCodeLevelNo())) {
						return 1;
					} else {
						return o1.getFkByTechTypeId().getNum().compareTo(o2.getFkByTechTypeId().getNum());
					}

				}
			});

			for (TdZwSupportTech tdZwSupportTech : zwOrginfoEntity.getSupportTechList()) {
				for (TsSimpleCode tsSimpleCode : this.techTypeList) {
					if(tdZwSupportTech.getFkByTechTypeId().getRid().equals(tsSimpleCode.getRid())){
						tsSimpleCode.setIfSelected(true);
					}
				}
			}
			for (TechTypeVo techTypeVo : this.techTypesList) {
				techTypeVo.setSelNums(0);
				StringBuilder smallTypeNames=new StringBuilder();
				if(!CollectionUtils.isEmpty(techTypeVo.getTechTypes())){
					for (TsSimpleCode techType : techTypeVo.getTechTypes()) {
						if(techType.isIfSelected()){
							techTypeVo.setSelNums(techTypeVo.getSelNums()+1);
							smallTypeNames.append("，").append(techType.getCodeName());
						}
					}
				}
				if(StringUtils.isNotBlank(smallTypeNames)){
					techTypeVo.setSmallTypeNames(smallTypeNames.substring(1));
				}
			}
		}
	}
	/**
	 * <p>Description：提交后支撑机构初始化 </p>
	 * <p>Author： yzz 2023-10-25 </p>
	 */
	public void initOrgTypeAndTechType() {
		if(this.zwOrgType!=4 && zwOrginfoEntity.getState().intValue()==1){
			return;
		}
		if(zwOrginfoEntity.getFkByTypeId()==null && "2".equals(this.unitType)){
			zwOrginfoEntity.setFkByTypeId(new TsSimpleCode());
		}
		if(!CollectionUtils.isEmpty(zwOrginfoEntity.getSupportQualList())) {
			Collections.sort(zwOrginfoEntity.getSupportQualList(), new Comparator<TdZwSupportQual>() {
				@Override
				public int compare(TdZwSupportQual o1, TdZwSupportQual o2) {
					if (ObjectUtil.isNotNull(o1.getFkByTypeId().getNum()) && ObjectUtil.isNotNull(o2.getFkByTypeId().getNum())) {
						int i = o1.getFkByTypeId().getNum().compareTo(o2.getFkByTypeId().getNum());
						if(i == 0){
							return o1.getFkByTypeId().getCodeNo().compareTo(o2.getFkByTypeId().getCodeNo());
						}
						return i;
					} else if (ObjectUtil.isNotNull(o1.getFkByTypeId().getNum())) {
						return -1;
					} else if (ObjectUtil.isNotNull(o2.getFkByTypeId().getNum())) {
						return 1;
					} else {
						return o1.getFkByTypeId().getCodeNo().compareTo(o2.getFkByTypeId().getCodeNo());
					}
				}
			});
			StringBuilder str=new StringBuilder();
			for (TdZwSupportQual supportQual : zwOrginfoEntity.getSupportQualList()) {
				str.append("，").append(supportQual.getFkByTypeId().getCodeName());
			}
			zwOrginfoEntity.setQuaTypeName(str.substring(1));
		}
		//初始化场所检测
		/*if(zwOrginfoEntity.getFkByTechTypeId()==null){
			zwOrginfoEntity.setFkByTechTypeId(new TsSimpleCode());
		}*/
		//保存后 初始化场所监测
		initSupportPlace(zwOrginfoEntity);
	}
	/**
	 * <p>Description： </p>
	 * <p>Author： yzz 2023-10-25 </p>
	 */
	public void initSupportPlace(IZwOrginfoZzsb find) {
		TdZwSupportPlace supportPlace = this.zwIntellReportServiceImpl.findSupportPlace(find.getRid());
		if(supportPlace==null || supportPlace.getRid()==null){
			supportPlace=new TdZwSupportPlace();
			supportPlace.setFkByMainId(new TdZwSupportOrgZzsb(find.getRid()));
			supportPlace.setFkByPsnStdId(new TsSimpleCode());
			supportPlace.setFkByUnitStdId(new TsSimpleCode());
			supportPlace.setCreateDate(new Date());
			supportPlace.setCreateManid(Global.getUser().getRid());
			find.setSupportPlaces(supportPlace);
		}else{
			if(supportPlace.getFkByUnitStdId()==null){
				supportPlace.setFkByUnitStdId(new TsSimpleCode());
				supportPlace.setUnitStdId(null);
			}else{
				supportPlace.setFkByUnitStdId(this.stdMap.get(supportPlace.getFkByUnitStdId().getRid()));
				supportPlace.setUnitStdId(supportPlace.getFkByUnitStdId().getRid());
			}
			if(supportPlace.getFkByPsnStdId()==null){
				supportPlace.setFkByPsnStdId(new TsSimpleCode());
				supportPlace.setPsnStdId(null);
			}else{
				supportPlace.setFkByPsnStdId(this.stdMap1.get(supportPlace.getFkByPsnStdId().getRid()));
				supportPlace.setPsnStdId(supportPlace.getFkByPsnStdId().getRid());
			}
			find.setSupportPlaces(supportPlace);
			List<String> rids=new ArrayList<>();
			StringBuilder str=new StringBuilder();
			if(!CollectionUtils.isEmpty(supportPlace.getSupportOfficeList())){
				Collections.sort(supportPlace.getSupportOfficeList(), new Comparator<TdZwSupportOffice>() {
					@Override
					public int compare(TdZwSupportOffice o1, TdZwSupportOffice o2) {
						if (ObjectUtil.isNotNull(o1.getFkByOfficeId().getNum()) && ObjectUtil.isNotNull(o2.getFkByOfficeId().getNum())) {
							int i = o1.getFkByOfficeId().getNum().compareTo(o2.getFkByOfficeId().getNum());
							if(i == 0){
								return o1.getFkByOfficeId().getCodeNo().compareTo(o2.getFkByOfficeId().getCodeNo());
							}
							return i;
						} else if (ObjectUtil.isNotNull(o1.getFkByOfficeId().getNum())) {
							return -1;
						} else if (ObjectUtil.isNotNull(o2.getFkByOfficeId().getNum())) {
							return 1;
						} else {
							return o1.getFkByOfficeId().getCodeNo().compareTo(o2.getFkByOfficeId().getCodeNo());
						}
					}
				});
				for (TdZwSupportOffice supportOffice : supportPlace.getSupportOfficeList()) {
					rids.add(supportOffice.getFkByOfficeId().getRid().toString());
					str.append("，").append(supportOffice.getFkByOfficeId().getCodeName());
				}
				supportPlace.setSelOfficeName(str.substring(1));
				supportPlace.setSelOffices(rids);
			}
		}
	}


	public String getPsnInputId() {
		return psnInputId;
	}

	public void setPsnInputId(String psnInputId) {
		this.psnInputId = psnInputId;
	}

	public Integer getFileOptType() {
        return fileOptType;
    }

    public void setFileOptType(Integer fileOptType) {
        this.fileOptType = fileOptType;
    }

    public TdZwSupportThesis getSupportThesis() {
        return supportThesis;
    }

    public void setSupportThesis(TdZwSupportThesis supportThesis) {
        this.supportThesis = supportThesis;
    }

    public List<TdZwSupportThesis> getThesisList() {
        return thesisList;
    }

    public void setThesisList(List<TdZwSupportThesis> thesisList) {
        this.thesisList = thesisList;
    }

	public int getSupportOptIndex() {
		return supportOptIndex;
	}

	public void setSupportOptIndex(int supportOptIndex) {
		this.supportOptIndex = supportOptIndex;
	}

	public List<TsSimpleCode> getPublicationList() {
        return publicationList;
    }

    public void setPublicationList(List<TsSimpleCode> publicationList) {
        this.publicationList = publicationList;
    }

    public TdZwSupportMomograph getSupportMomograph() {
        return supportMomograph;
    }

    public void setSupportMomograph(TdZwSupportMomograph supportMomograph) {
        this.supportMomograph = supportMomograph;
    }

    public List<TdZwSupportMomograph> getMonographList() {
        return monographList;
    }

    public void setMonographList(List<TdZwSupportMomograph> monographList) {
        this.monographList = monographList;
    }

	public List<Object[]> getPatentList() {
		return patentList;
	}

	public void setPatentList(List<Object[]> patentList) {
		this.patentList = patentList;
	}

	public List<Object[]> getStandardList() {
		return standardList;
	}

	public void setStandardList(List<Object[]> standardList) {
		this.standardList = standardList;
	}

	public TdZwSupportPositive getSupportPositive() {
		return supportPositive;
	}

	public void setSupportPositive(TdZwSupportPositive supportPositive) {
		this.supportPositive = supportPositive;
	}

	public String getEmpPsns() {
		return empPsns;
	}

	public void setEmpPsns(String empPsns) {
		this.empPsns = empPsns;
	}

	public List<TsSimpleCode> getPatentTypelist() {
		return patentTypelist;
	}

	public void setPatentTypelist(List<TsSimpleCode> patentTypelist) {
		this.patentTypelist = patentTypelist;
	}

	public boolean getIsShowPatentType() {
		return isShowPatentType;
	}

	public void setIsShowPatentType(boolean showPatentType) {
		isShowPatentType = showPatentType;
	}

	public HashMap<Integer, TsSimpleCode> getPatentTypeMap() {
		return patentTypeMap;
	}

	public void setPatentTypeMap(HashMap<Integer, TsSimpleCode> patentTypeMap) {
		this.patentTypeMap = patentTypeMap;
	}


	public List<TsSimpleCode> getStandardTypelist() {
		return standardTypelist;
	}

	public void setStandardTypelist(List<TsSimpleCode> standardTypelist) {
		this.standardTypelist = standardTypelist;
	}

	public HashMap<Integer, TsSimpleCode> getStandardTypeMap() {
		return standardTypeMap;
	}

	public void setStandardTypeMap(HashMap<Integer, TsSimpleCode> standardTypeMap) {
		this.standardTypeMap = standardTypeMap;
	}

	public TdZwSupportTopic getSupportTopic() {
		return supportTopic;
	}

	public void setSupportTopic(TdZwSupportTopic supportTopic) {
		this.supportTopic = supportTopic;
	}

	public List<TdZwSupportTopic> getSupportTopics1() {
		return supportTopics1;
	}

	public void setSupportTopics1(List<TdZwSupportTopic> supportTopics1) {
		this.supportTopics1 = supportTopics1;
	}

	public List<TdZwSupportTopic> getSupportTopics2() {
		return supportTopics2;
	}

	public void setSupportTopics2(List<TdZwSupportTopic> supportTopics2) {
		this.supportTopics2 = supportTopics2;
	}

	public List<TdZwSupportTopic> getSupportTopics3() {
		return supportTopics3;
	}

	public void setSupportTopics3(List<TdZwSupportTopic> supportTopics3) {
		this.supportTopics3 = supportTopics3;
	}

	public List<TsSimpleCode> getPrizeLeveList() {
		return prizeLeveList;
	}

	public void setPrizeLeveList(List<TsSimpleCode> prizeLeveList) {
		this.prizeLeveList = prizeLeveList;
	}
	public List<TsSimpleCode> getTechTypeList() {
		return techTypeList;
	}

	public void setTechTypeList(List<TsSimpleCode> techTypeList) {
		techTypeList = techTypeList;
	}

	public List<TsSimpleCode> getUnitTypeList() {
		return unitTypeList;
	}

	public void setUnitTypeList(List<TsSimpleCode> unitTypeList) {
		this.unitTypeList = unitTypeList;
	}

	public List<TsSimpleCode> getQualTypeList1() {
		return qualTypeList1;
	}

	public void setQualTypeList1(List<TsSimpleCode> qualTypeList1) {
		this.qualTypeList1 = qualTypeList1;
	}

	public List<TsSimpleCode> getQualTypeList2() {
		return qualTypeList2;
	}

	public void setQualTypeList2(List<TsSimpleCode> qualTypeList2) {
		this.qualTypeList2 = qualTypeList2;
	}

	public Map<Integer,TsSimpleCode> getQualTypeMap() {
		return qualTypeMap;
	}

	public void setQualTypeMap(Map<Integer,TsSimpleCode> qualTypeMap) {
		this.qualTypeMap = qualTypeMap;
	}
	public List<TsSimpleCode> getOfficeList() {
		return OfficeList;
	}

	public void setOfficeList(List<TsSimpleCode> officeList) {
		OfficeList = officeList;
	}

	public List<TsSimpleCode> getStdList() {
		return stdList;
	}

	public void setStdList(List<TsSimpleCode> stdList) {
		this.stdList = stdList;
	}

	public TdZwSupportTrc getSupportTrc() {
		return supportTrc;
	}

	public void setSupportTrc(TdZwSupportTrc supportTrc) {
		this.supportTrc = supportTrc;
	}
	public List<Integer> getYearList() {
		return yearList;
	}

	public void setYearList(List<Integer> yearList) {
		this.yearList = yearList;
	}

	public List<TsSimpleCode> getStdList1() {
		return stdList1;
	}

	public void setStdList1(List<TsSimpleCode> stdList1) {
		this.stdList1 = stdList1;
	}

	public List<TechTypeVo> getTechTypesList() {
		return techTypesList;
	}

	public void setTechTypesList(List<TechTypeVo> techTypesList) {
		this.techTypesList = techTypesList;
	}
	public TdZwSupportAdvance getSupportAdvance() {
		return supportAdvance;
	}

	public void setSupportAdvance(TdZwSupportAdvance supportAdvance) {
		this.supportAdvance = supportAdvance;
	}

	public List<TdZwSupportAdvance> getAdvanceList() {
		return advanceList;
	}

	public void setAdvanceList(List<TdZwSupportAdvance> advanceList) {
		this.advanceList = advanceList;
	}

	/**
	 * 更新被删除人员的MASTER_DATA_TIME字段
	 * 当人员被删除时，需要更新其MASTER_DATA_TIME为当前日期
	 */
	private void updateMasterDataTimeForDeletedPersons() {
		if (!deletedPsnRidSet.isEmpty()) {
			try {
				// 批量更新被删除人员的MASTER_DATA_TIME字段
				Date currentDate = new Date();
				StringBuilder updateSql = new StringBuilder();
				updateSql.append("UPDATE TD_ZW_PSNINFO SET MASTER_DATA_TIME = :currentDate WHERE RID IN (");

				boolean first = true;
				for (Integer psnRid : deletedPsnRidSet) {
					if (!first) {
						updateSql.append(",");
					}
					updateSql.append(psnRid);
					first = false;
				}
				updateSql.append(")");

				Map<String, Object> paramMap = new HashMap<>();
				paramMap.put("currentDate", currentDate);

				// 执行更新SQL
				commService.executeSql(updateSql.toString(), paramMap);

				// 清空已处理的删除人员集合
				deletedPsnRidSet.clear();
			} catch (Exception e) {
				e.printStackTrace();
				// 记录日志但不影响主流程
			}
		}
	}

	/**
	 * 初始化人员状态跟踪
	 * 在页面初始化时记录所有人员的原始在职状态
	 */
	private void initPersonnelStateTracking() {
		originalDutyStateMap.clear();
		deletedPsnRidSet.clear();

		List<IZwOrgPsnsZzsb> zwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
		if (null != zwOrgPsns && zwOrgPsns.size() > 0) {
			for (IZwOrgPsnsZzsb psns : zwOrgPsns) {
				Integer psnRid = psns.getTdZwPsninfo().getRid();
				if (psnRid != null) {
					originalDutyStateMap.put(psnRid, psns.getOnDuty());
				}
			}
		}
	}


}
