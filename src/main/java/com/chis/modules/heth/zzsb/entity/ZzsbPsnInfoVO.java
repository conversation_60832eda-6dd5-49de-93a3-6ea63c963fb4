package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 资质申报人员信息
 *
 * <AUTHOR>
 * @date 2022/4/22
 */
public class ZzsbPsnInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 选择的机构人员
     */
    private TdZwTjorgpsnsApply selectOrgPsnApply;
    /**
     * 机构人员List
     */
    private List<TdZwTjorgpsnsApply> orgPsnApplyListView;
    /**
     * 人员弹出框类别 <p>0 新增;<p>1 修改;<p>2 详情;
     */
    private Integer psnDialogType;
    /**
     * 选择的人员信息
     */
    private TdZwPsninfoZzsb selectPsnInfo;
    /**
     * 从事科室弹出框 从事科室
     */
    private List<String> selectWorkOfficeRidList;
    /**
     * 从事科室弹出框 所有科室
     */
    private List<TsSimpleCode> workOfficeList;

    public ZzsbPsnInfoVO() {
    }

    public void init() {
        this.selectOrgPsnApply = new TdZwTjorgpsnsApply();
        this.selectOrgPsnApply.setFkByEmpId(new TdZwPsninfoZzsb());
        this.selectOrgPsnApply.setFkByOrgId(new TdZwTjorginfoApply());
        this.orgPsnApplyListView = new ArrayList<>();
        this.selectPsnInfo = new TdZwPsninfoZzsb();
        this.selectWorkOfficeRidList = new ArrayList<>();
        this.workOfficeList = new ArrayList<>();
    }

    public TdZwTjorgpsnsApply getSelectOrgPsnApply() {
        return selectOrgPsnApply;
    }

    public void setSelectOrgPsnApply(TdZwTjorgpsnsApply selectOrgPsnApply) {
        this.selectOrgPsnApply = selectOrgPsnApply;
    }

    public List<TdZwTjorgpsnsApply> getOrgPsnApplyListView() {
        return orgPsnApplyListView;
    }

    public void setOrgPsnApplyListView(List<TdZwTjorgpsnsApply> orgPsnApplyListView) {
        this.orgPsnApplyListView = orgPsnApplyListView;
    }

    public Integer getPsnDialogType() {
        return psnDialogType;
    }

    public void setPsnDialogType(Integer psnDialogType) {
        this.psnDialogType = psnDialogType;
    }

    public TdZwPsninfoZzsb getSelectPsnInfo() {
        return selectPsnInfo;
    }

    public void setSelectPsnInfo(TdZwPsninfoZzsb selectPsnInfo) {
        this.selectPsnInfo = selectPsnInfo;
    }

    public List<String> getSelectWorkOfficeRidList() {
        return selectWorkOfficeRidList;
    }

    public void setSelectWorkOfficeRidList(List<String> selectWorkOfficeRidList) {
        this.selectWorkOfficeRidList = selectWorkOfficeRidList;
    }

    public List<TsSimpleCode> getWorkOfficeList() {
        return workOfficeList;
    }

    public void setWorkOfficeList(List<TsSimpleCode> workOfficeList) {
        this.workOfficeList = workOfficeList;
    }

    @Override
    public String toString() {
        return "ZzsbPsnInfoVO{}";
    }
}
