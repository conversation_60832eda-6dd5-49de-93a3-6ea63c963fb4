package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;

/**
 * 
 * <AUTHOR>
 * @createTime 2016-5-12
 */
@Entity
@Table(name = "TD_ZW_PSNTJ_SUB")
@SequenceGenerator(name = "TdZwPsntjSub", sequenceName = "TD_ZW_PSNTJ_SUB_SEQ", allocationSize = 1)
public class TdZwPsntjSubZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwPsntjMainZzsb fkByMainId;
	private String descr;
	private String rst;
	private String rstUnit;
	
	public TdZwPsntjSubZzsb() {
	}

	public TdZwPsntjSubZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwPsntjSub")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwPsntjMainZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwPsntjMainZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "DESCR")	
	public String getDescr() {
		return descr;
	}

	public void setDescr(String descr) {
		this.descr = descr;
	}	
			
	@Column(name = "RST")	
	public String getRst() {
		return rst;
	}

	public void setRst(String rst) {
		this.rst = rst;
	}	
			
	@Column(name = "RST_UNIT")	
	public String getRstUnit() {
		return rstUnit;
	}

	public void setRstUnit(String rstUnit) {
		this.rstUnit = rstUnit;
	}	
			
}