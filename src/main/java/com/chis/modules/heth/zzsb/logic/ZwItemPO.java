package com.chis.modules.heth.zzsb.logic;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description :
 * @ClassAuthor : anjing
 * @Date : 2019/11/8 16:41
 **/
public class ZwItemPO {

    private String codeNo;
    private String codeName;
    private String extendS1;
    private boolean ifSelected;
    private boolean disabled;
    private List<ZwItemSubPO> zwItemSubPOList = new ArrayList<>();
    private String[] itemSubs;
    protected String[] itemStr;

    public ZwItemPO() {
    }

    public ZwItemPO(String codeNo, String codeName, String extendS1, boolean ifSelected, boolean disabled) {
        this.codeNo = codeNo;
        this.codeName = codeName;
        this.extendS1 = extendS1;
        this.ifSelected = ifSelected;
        this.disabled = disabled;
    }

    public String getCodeNo() {
        return codeNo;
    }

    public void setCodeNo(String codeNo) {
        this.codeNo = codeNo;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public String getExtendS1() {
        return extendS1;
    }

    public void setExtendS1(String extendS1) {
        this.extendS1 = extendS1;
    }

    public boolean isIfSelected() {
        return ifSelected;
    }

    public void setIfSelected(boolean ifSelected) {
        this.ifSelected = ifSelected;
    }

    public boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }

    public List<ZwItemSubPO> getZwItemSubPOList() {
        return zwItemSubPOList;
    }

    public void setZwItemSubPOList(List<ZwItemSubPO> zwItemSubPOList) {
        this.zwItemSubPOList = zwItemSubPOList;
    }

    public String[] getItemSubs() {
        return itemSubs;
    }

    public void setItemSubs(String[] itemSubs) {
        this.itemSubs = itemSubs;
    }

    public String[] getItemStr() {
        return itemStr;
    }

    public void setItemStr(String[] itemStr) {
        this.itemStr = itemStr;
    }
}
