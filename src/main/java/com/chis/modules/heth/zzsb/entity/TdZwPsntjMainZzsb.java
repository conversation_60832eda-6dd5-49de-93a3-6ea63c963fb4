package com.chis.modules.heth.zzsb.entity;

import com.google.common.collect.Lists;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2016-5-12
 */
@Entity
@Table(name = "TD_ZW_PSNTJ_MAIN")
@SequenceGenerator(name = "TdZwPsntjMain", sequenceName = "TD_ZW_PSNTJ_MAIN_SEQ", allocationSize = 1)
public class TdZwPsntjMainZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwPsninfoZzsb fkByEmpId;
	private Date tjDate;
	private String tjConcl;
	private Integer tjType;
	
	private List<TdZwPsntjSubZzsb> subList = Lists.newArrayList();
	
	public TdZwPsntjMainZzsb() {
	}

	public TdZwPsntjMainZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwPsntjMain")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "EMP_ID")			
	public TdZwPsninfoZzsb getFkByEmpId() {
		return fkByEmpId;
	}

	public void setFkByEmpId(TdZwPsninfoZzsb fkByEmpId) {
		this.fkByEmpId = fkByEmpId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "TJ_DATE")			
	public Date getTjDate() {
		return tjDate;
	}

	public void setTjDate(Date tjDate) {
		this.tjDate = tjDate;
	}	
			
	@Column(name = "TJ_CONCL")	
	public String getTjConcl() {
		return tjConcl;
	}

	public void setTjConcl(String tjConcl) {
		this.tjConcl = tjConcl;
	}	
			
	@Column(name = "TJ_TYPE")	
	public Integer getTjType() {
		return tjType;
	}

	public void setTjType(Integer tjType) {
		this.tjType = tjType;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
	public List<TdZwPsntjSubZzsb> getSubList() {
		return subList;
	}

	public void setSubList(List<TdZwPsntjSubZzsb> subList) {
		this.subList = subList;
	}	
	
	
			
}