package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-11-8
 */
@Entity
@Table(name = "TD_ZW_SRVORGITEMS_SUBS")
@SequenceGenerator(name = "TdZwSrvorgitemsSubsSeq", sequenceName = "TD_ZW_SRVORGITEMS_SUBS_SEQ", allocationSize = 1)
public class TdZwSrvorgitemsSubsZzsb implements java.io.Serializable, IZwSvritmSubZzsb {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSrvorgitemsZzsb tdZwSrvorgitems;
	private TsSimpleCode fkBySubItemId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwSrvorgitemsSubsZzsb() {
	}

	public TdZwSrvorgitemsSubsZzsb(Integer rid) {
		this.rid = rid;
	}

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSrvorgitemsSubsSeq")
	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
    @Override
    public IZwSvritmZzsb getZwSvritm() {
        return tdZwSrvorgitems;
    }

    @Override
    public void setZwSvritm(IZwSvritmZzsb zwSvritm) {
        tdZwSrvorgitems = (TdZwSrvorgitemsZzsb) zwSvritm;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TdZwSrvorgitemsZzsb getTdZwSrvorgitems() {
        return tdZwSrvorgitems;
    }

    public void setTdZwSrvorgitems(TdZwSrvorgitemsZzsb tdZwSrvorgitems) {
        this.tdZwSrvorgitems = tdZwSrvorgitems;
    }

    @ManyToOne
    @JoinColumn(name = "SUB_ITEM_ID")
	public TsSimpleCode getFkBySubItemId() {
		return fkBySubItemId;
	}

	public void setFkBySubItemId(TsSimpleCode fkBySubItemId) {
		this.fkBySubItemId = fkBySubItemId;
	}

    @Column(name = "CREATE_DATE" )
    @Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

    @Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@Transient
	@Override
	public Integer getIfExterInspect() {return null;}

	@Override
	public void setIfExterInspect(Integer ifExterInspect) {}

}