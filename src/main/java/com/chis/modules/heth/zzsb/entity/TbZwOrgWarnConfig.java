package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-10-24
 */
@Entity
@Table(name = "TB_ZW_ORG_WARN_CONFIG")
@SequenceGenerator(name = "TbZwOrgWarnConfig", sequenceName = "TB_ZW_ORG_WARN_CONFIG_SEQ", allocationSize = 1)
public class TbZwOrgWarnConfig implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private Integer busType;
	private Integer warnType;
	private Integer itemId;
	private String warnTypeDesc;
	private Integer xh;

	private TsSimpleCode fkByPostId;
	private TsSimpleCode fkByInstTypeId;
	private Integer minNums;
	private Integer trainYear;
	private TsSimpleCode fkByTitleId;

	private Integer titleRid;
	private Integer orgNums;
	private Integer ifTestInst;
	private Integer ifDel;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;

	/**人员岗位名称*/
	private String specialPost;
	/**人员岗位名称 rid*/
	private String specialPostRid;

	/**是否鉴定设备 是*/
	private boolean ifAppTrue;
	/**是否鉴定设备 否*/
	private boolean ifAppFalse;

	public TbZwOrgWarnConfig() {
	}

	public TbZwOrgWarnConfig(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwOrgWarnConfig")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@Column(name = "BUS_TYPE")	
	public Integer getBusType() {
		return busType;
	}

	public void setBusType(Integer busType) {
		this.busType = busType;
	}	
			
	@Column(name = "WARN_TYPE")	
	public Integer getWarnType() {
		return warnType;
	}

	public void setWarnType(Integer warnType) {
		this.warnType = warnType;
	}	
			
	@Column(name = "ITEM_ID")	
	public Integer getItemId() {
		return itemId;
	}

	public void setItemId(Integer itemId) {
		this.itemId = itemId;
	}	
			
	@Column(name = "WARN_TYPE_DESC")	
	public String getWarnTypeDesc() {
		return warnTypeDesc;
	}

	public void setWarnTypeDesc(String warnTypeDesc) {
		this.warnTypeDesc = warnTypeDesc;
	}	
			
	@Column(name = "XH")	
	public Integer getXh() {
		return xh;
	}

	public void setXh(Integer xh) {
		this.xh = xh;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@ManyToOne
	@JoinColumn(name = "POST_ID")
	public TsSimpleCode getFkByPostId() {
		return fkByPostId;
	}
	public void setFkByPostId(TsSimpleCode fkByPostId) {
		this.fkByPostId = fkByPostId;
	}
	@ManyToOne
	@JoinColumn(name = "INST_TYPE_ID")
	public TsSimpleCode getFkByInstTypeId() {
		return fkByInstTypeId;
	}
	public void setFkByInstTypeId(TsSimpleCode fkByInstTypeId) {
		this.fkByInstTypeId = fkByInstTypeId;
	}
	@Column(name = "MIN_NUMS")
	public Integer getMinNums() {
		return minNums;
	}
	public void setMinNums(Integer minNums) {
		this.minNums = minNums;
	}
	@Column(name = "TRAIN_YEAR")
	public Integer getTrainYear() {
		return trainYear;
	}
	public void setTrainYear(Integer trainYear) {
		this.trainYear = trainYear;
	}
	@ManyToOne
	@JoinColumn(name = "TITLE_ID")
	public TsSimpleCode getFkByTitleId() {
		return fkByTitleId;
	}
	public void setFkByTitleId(TsSimpleCode fkByTitleId) {
		this.fkByTitleId = fkByTitleId;
	}
	@Column(name = "ORG_NUMS")
	public Integer getOrgNums() {
		return orgNums;
	}
	public void setOrgNums(Integer orgNums) {
		this.orgNums = orgNums;
	}
	@Column(name = "IF_TEST_INST")
	public Integer getIfTestInst() {
		return ifTestInst;
	}
	public void setIfTestInst(Integer ifTestInst) {
		this.ifTestInst = ifTestInst;
	}
	@Column(name = "IF_DEL")
	public Integer getIfDel() {
		return ifDel;
	}
	public void setIfDel(Integer ifDel) {
		this.ifDel = ifDel;
	}

	@Transient
	public String getSpecialPost() {
		return specialPost;
	}
	public void setSpecialPost(String specialPost) {
		this.specialPost = specialPost;
	}
	@Transient
	public String getSpecialPostRid() {
		return specialPostRid;
	}
	public void setSpecialPostRid(String specialPostRid) {
		this.specialPostRid = specialPostRid;
	}
	@Transient
	public boolean getIfAppTrue() {
		return ifAppTrue;
	}
	public void setIfAppTrue(boolean ifAppTrue) {
		this.ifAppTrue = ifAppTrue;
	}
	@Transient
	public boolean getIfAppFalse() {
		return ifAppFalse;
	}
	public void setIfAppFalse(boolean ifAppFalse) {
		this.ifAppFalse = ifAppFalse;
	}

	@Transient
	public Integer getTitleRid() {
		return titleRid;
	}
	public void setTitleRid(Integer titleRid) {
		this.titleRid = titleRid;
	}
}