package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsZone;

import java.util.Date;

/**
 * 附件
 * Created by wlj on 2015-04-20.
 */
public interface IZwAnnexZzsb {
    public Integer getRid();
    public void setRid(Integer rid);

    public IZwOrginfoZzsb getZwOrginfo();
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo);

    public Short getAnnexType();
    public void setAnnexType(Short annexType);

    public String getFileName();
    public void setFileName(String fileName);

    public String getFilePath();
    public void setFilePath(String filePath);

    public Date getCreateDate();
    public void setCreateDate(Date createDate);

    public Integer getCreateManid();
    public void setCreateManid(Integer createManid);
    
    public TsZone getFkByRegZoneId();
    public void setFkByRegZoneId(TsZone fkByRegZoneId);
    
    public Integer getUnitLevel();
    public void setUnitLevel(Integer unitLevel);
    
    public String getUnitName();
    public void setUnitName(String unitName);
    
}
