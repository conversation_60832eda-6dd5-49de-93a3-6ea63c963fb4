package com.chis.modules.heth.zzsb.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.TbZwBusiMainItem;
import com.chis.modules.heth.zzsb.logic.BusiItemInfoDTO;
import com.chis.modules.heth.zzsb.logic.BusiMainItemPO;
import com.chis.modules.heth.zzsb.service.TbZwBusiMainItemService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.commons.collections.CollectionUtils;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.*;

/**
 * <p>类描述：业务范围与重点、一般检测项目关系维护</p>
 *
 * @ClassAuthor hsj 2024-07-18 9:03
 */
@ManagedBean(name = "tbZwBusiMainItemListBean")
@ViewScoped
public class TbZwBusiMainItemListBean extends FacesEditBean {
    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final TbZwBusiMainItemService busiMainItemService = SpringContextHolder.getBean(TbZwBusiMainItemService.class);
    /**
     * 码表map
     */
    private Map<Integer, TsSimpleCode> simpleCodeMap;
    /**
     * 查询页面列表对象
     */
    private List<BusiMainItemPO> datas;
    /**
     * 一级rid
     */
    private Integer bigId;
    /**
     * 一级名称
     */
    private String bigName;
    /**
     * 二级rid
     */
    private Integer simpleId;
    /**
     * 二级名称
     */
    private String simpleName;
    /**
     * 类型:1：重点，2：一般
     */

    private Integer type;

    /**
     * 重点表格
     */
    private List<TbZwBusiMainItem> emphasisBusiMainItem;
    /**
     * 一般表格
     */
    private List<TbZwBusiMainItem> ordinaryBusiMainItem;
    /**
     * 业务范围与重点检测项目关系Id
     */
    private Integer busiId;


    public TbZwBusiMainItemListBean() {
        this.ifSQL = true;
        this.initListSimple();
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        this.bigName = null;
        this.simpleName = null;
        if (ObjectUtil.isNull(this.bigId) || ObjectUtil.isNull(this.simpleId)) {
            return;
        }
        this.bigName = this.simpleCodeMap.get(this.bigId).getCodeName();
        this.simpleName = this.simpleCodeMap.get(this.simpleId).getCodeName();
        this.initTbZwBusiMainItem();
    }

    /**
     * <p>方法描述：业务范围与重点检测项目关系初始化</p>
     *
     * @MethodAuthor hsj 2024-07-18 13:45
     */
    private void initTbZwBusiMainItem() {
        this.emphasisBusiMainItem = new ArrayList<>();
        this.ordinaryBusiMainItem = new ArrayList<>();
        List<TbZwBusiMainItem> list = this.busiMainItemService.findTbZwBusiMainItemByBusId(this.simpleId, null);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<Integer, List<TbZwBusiMainItem>> map = new HashMap<>();
        GroupUtil.listGroup2Map(list, map, TbZwBusiMainItem.class, "getCheckType");
        if (map.containsKey(1)) {
            this.emphasisBusiMainItem = map.get(1);
            SortUtil.sortCodeByField(this.emphasisBusiMainItem, TbZwBusiMainItem.class, "getFkByItemId");
        }
        if (map.containsKey(2)) {
            this.ordinaryBusiMainItem = map.get(2);
            SortUtil.sortCodeByField(this.ordinaryBusiMainItem, TbZwBusiMainItem.class, "getFkByItemId");
        }
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:editForm:emphasisTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
        DataTable dataTable1 = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:editForm:ordinaryTable");
        dataTable1.setFirst(0);
        dataTable1.setRows(10);

    }

    /**
     * <p>方法描述：添加項目</p>
     *
     * @MethodAuthor hsj 2024-07-18 13:59
     */
    public void addItemAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 620, null, 460);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> tmpList = new ArrayList<>();
        tmpList.add(Convert.toStr(this.bigId));
        paramMap.put("firstId", tmpList);
        tmpList = new ArrayList<>();
        tmpList.add(Convert.toStr(this.simpleId));
        paramMap.put("secondId", tmpList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/zzsb/selectBusiMainItemList", options, paramMap);
    }

    /**
     * <p>方法描述：项目选择后</p>
     *
     * @MethodAuthor hsj 2024-07-18 14:13
     */
    public void onItemSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null == selectedMap || selectedMap.size() == 0) {
            return;
        }
        List<BusiItemInfoDTO> list = (List<BusiItemInfoDTO>) selectedMap.get("selected");
        if (org.springframework.util.CollectionUtils.isEmpty(list)) {
            return;
        }
        String str = "1".equals(Convert.toStr(this.type)) ? "重点检测项目" : "一般检测项目";
        try {
            this.busiMainItemService.saveTbZwBusiMainItemList(list, this.simpleId, this.type);
            JsfUtil.addSuccessMessage(str + "添加成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage(str + "添加失败！");
        }
        this.refreshTbZwBusiMainItem();
        RequestContext.getCurrentInstance().update("tabView:editForm");
    }

    /**
     * <p>方法描述：表格刷新</p>
     *
     * @MethodAuthor hsj 2024-07-18 15:23
     */
    private void refreshTbZwBusiMainItem() {
        if (ObjectUtil.isNull(this.type)) {
            return;
        }
        if (new Integer(1).equals(this.type)) {
            this.initEmphasisBusiMainItem();
        } else if (new Integer(2).equals(this.type)) {
            this.initOrdinaryBusiMainItem();
        }
    }

    /**
     * <p>方法描述：单刷新一般</p>
     *
     * @MethodAuthor hsj 2024-07-18 15:28
     */
    private void initOrdinaryBusiMainItem() {
        this.ordinaryBusiMainItem = this.busiMainItemService.findTbZwBusiMainItemByBusId(this.simpleId, 2);
        if (CollectionUtils.isNotEmpty(this.ordinaryBusiMainItem)) {
            SortUtil.sortCodeByField(this.ordinaryBusiMainItem, TbZwBusiMainItem.class, "getFkByItemId");
        }
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:editForm:ordinaryTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
    }

    /**
     * <p>方法描述：单刷新重点</p>
     *
     * @MethodAuthor hsj 2024-07-18 15:28
     */
    private void initEmphasisBusiMainItem() {
        this.emphasisBusiMainItem = this.busiMainItemService.findTbZwBusiMainItemByBusId(this.simpleId, 1);
        if (CollectionUtils.isNotEmpty(this.emphasisBusiMainItem)) {
            SortUtil.sortCodeByField(this.emphasisBusiMainItem, TbZwBusiMainItem.class, "getFkByItemId");
        }
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:editForm:emphasisTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
    }

    /**
     * <p>方法描述：删除</p>
     *
     * @MethodAuthor hsj 2024-07-18 14:34
     */
    public void delItemAction() {
        String str = "1".equals(Convert.toStr(this.type)) ? "重点检测项目" : "一般检测项目";
        try {
            this.busiMainItemService.delTbZwBusiMainItemByItemId(this.busiId);
            JsfUtil.addSuccessMessage(str + "删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage(str + "删除失败！");
        }
        this.refreshTbZwBusiMainItem();
        RequestContext.getCurrentInstance().update("tabView:editForm");
    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        return new String[0];
    }

    /**
     * <p>方法描述：列表码表处理</p>
     *
     * @MethodAuthor hsj 2024-07-18 9:40
     */
    private void initListSimple() {
        this.simpleCodeMap = new HashMap<>();
        this.datas = new ArrayList<>();
        List<TsSimpleCode> simpleCodes = this.commService.findLevelSimpleCodesByTypeId("5320");
        if (CollectionUtils.isEmpty(simpleCodes)) {
            return;
        }
        Map<String, TsSimpleCode> bigSimpleMap = new HashMap<>();
        Map<String, List<TsSimpleCode>> smallSimpleMap = new HashMap<>();
        for (TsSimpleCode simpleCode : simpleCodes) {
            this.simpleCodeMap.put(simpleCode.getRid(), simpleCode);
            String code = simpleCode.getCodeNo();
            String codeLevelNo = simpleCode.getCodeLevelNo();
            boolean flag = StringUtils.isBlank(codeLevelNo) || code.equals(codeLevelNo);
            if (flag) {
                bigSimpleMap.put(code, simpleCode);
                if (!smallSimpleMap.containsKey(code)) {
                    smallSimpleMap.put(code, new ArrayList<TsSimpleCode>());
                }
                continue;
            }
            String newCode = StringUtils.string2list(codeLevelNo, "\\.").get(0);
            if (smallSimpleMap.containsKey(newCode)) {
                smallSimpleMap.get(newCode).add(simpleCode);
                continue;
            }
            List<TsSimpleCode> list = new ArrayList<>();
            list.add(simpleCode);
            smallSimpleMap.put(newCode, list);
        }
        for (Map.Entry<String, List<TsSimpleCode>> map : smallSimpleMap.entrySet()) {
            List<TsSimpleCode> list = map.getValue();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            BusiMainItemPO po = new BusiMainItemPO();
            po.setSimpleCode(bigSimpleMap.get(map.getKey()));
            simpleSort(list);
            po.setSimpleCodeList(list);
            this.datas.add(po);
        }
        if (CollectionUtils.isEmpty(this.datas)) {
            return;
        }
        SortUtil.sortCodeByField(this.datas, BusiMainItemPO.class, "getSimpleCode");
    }

    public void simpleSort(List<TsSimpleCode> list) {
        Collections.sort(list, new Comparator<TsSimpleCode>() {
            @Override
            public int compare(TsSimpleCode o1, TsSimpleCode o2) {
                if (ObjectUtil.isNotNull(o1.getNum()) && ObjectUtil.isNotNull(o2.getNum())) {
                    int i = o1.getNum().compareTo(o2.getNum());
                    if (i == 0) {
                        return o1.getCodeNo().compareTo(o2.getCodeNo());
                    }
                    return i;
                } else if (ObjectUtil.isNotNull(o1.getNum())) {
                    return -1;
                } else if (ObjectUtil.isNotNull(o2.getNum())) {
                    return 1;
                } else {
                    return o1.getCodeNo().compareTo(o2.getCodeNo());
                }
            }
        });
    }

    public List<BusiMainItemPO> getDatas() {
        return datas;
    }

    public void setDatas(List<BusiMainItemPO> datas) {
        this.datas = datas;
    }

    public Integer getBigId() {
        return bigId;
    }

    public void setBigId(Integer bigId) {
        this.bigId = bigId;
    }

    public Integer getSimpleId() {
        return simpleId;
    }

    public void setSimpleId(Integer simpleId) {
        this.simpleId = simpleId;
    }

    public Map<Integer, TsSimpleCode> getSimpleCodeMap() {
        return simpleCodeMap;
    }

    public void setSimpleCodeMap(Map<Integer, TsSimpleCode> simpleCodeMap) {
        this.simpleCodeMap = simpleCodeMap;
    }

    public String getBigName() {
        return bigName;
    }

    public void setBigName(String bigName) {
        this.bigName = bigName;
    }

    public String getSimpleName() {
        return simpleName;
    }

    public void setSimpleName(String simpleName) {
        this.simpleName = simpleName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<TbZwBusiMainItem> getEmphasisBusiMainItem() {
        return emphasisBusiMainItem;
    }

    public void setEmphasisBusiMainItem(List<TbZwBusiMainItem> emphasisBusiMainItem) {
        this.emphasisBusiMainItem = emphasisBusiMainItem;
    }

    public List<TbZwBusiMainItem> getOrdinaryBusiMainItem() {
        return ordinaryBusiMainItem;
    }

    public void setOrdinaryBusiMainItem(List<TbZwBusiMainItem> ordinaryBusiMainItem) {
        this.ordinaryBusiMainItem = ordinaryBusiMainItem;
    }

    public Integer getBusiId() {
        return busiId;
    }

    public void setBusiId(Integer busiId) {
        this.busiId = busiId;
    }
}
