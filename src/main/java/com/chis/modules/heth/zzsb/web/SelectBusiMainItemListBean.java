package com.chis.modules.heth.zzsb.web;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.logic.BusiItemInfoDTO;
import com.chis.modules.system.interfaces.IProcessPackData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.PackLazyDataModel;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：业务范围与重点、一般检测项目关系维护项目选择</p>
 *
 * @ClassAuthor hsj 2024-07-18 14:02
 */
@ManagedBean(name = "selectBusiMainItemListBean")
@ViewScoped
public class SelectBusiMainItemListBean extends FacesSimpleBean implements IProcessPackData {

    private static final long serialVersionUID = -738445454536625063L;

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private PackLazyDataModel itemDataModel;
    /**
     * 查询条件-名称
     */
    private String searchName;
    /**
     * 抽取列表当前页已勾选
     */
    private List<BusiItemInfoDTO> selectedList;
    /**
     * 抽取列表全部已勾选的
     */
    private List<BusiItemInfoDTO> allSelectedList;
    private String TABLE_ID = "codeForm:dataTable";
    /**
     * 第一级
     */
    private Integer firstId;
    private Integer secondId;

    public SelectBusiMainItemListBean() {
        this.ifSQL = true;
        this.selectedList = new ArrayList<>();
        this.allSelectedList = new ArrayList<>();
        this.firstId = Convert.toInt(JsfUtil.getRequest().getParameter("firstId"));
        this.secondId = Convert.toInt(JsfUtil.getRequest().getParameter("secondId"));
        RequestContext.getCurrentInstance().update("codeForm");
        //置为第一页
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(TABLE_ID);
        dataTable.setFirst(0);
        this.searchAction();
    }

    @Override
    public void searchAction() {
        this.paramMap.clear();
        String[] hql = this.buildHqls();
        this.itemDataModel = new PackLazyDataModel<BusiItemInfoDTO>(hql[0], hql[1], this.paramMap, this, TABLE_ID, this.ifSQL);
        this.selectedList.clear();
        this.selectedList.addAll(allSelectedList);
    }

    /**
     * <p>描述 处理数据</p>
     *
     * @param list
     * @MethodAuthor gongzhe, 2022.03.10 11:10,processPackData
     */
    @Override
    public List<BusiItemInfoDTO> processPackData(List list) {
        List<Object[]> data = (List<Object[]>) list;
        List<BusiItemInfoDTO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }
        for (Object[] obj : data) {
            BusiItemInfoDTO dto = new BusiItemInfoDTO();
            dto.setRid(Convert.toInt(obj[0]));
            dto.setName((Convert.toStr(obj[1])));
            result.add(dto);
        }
        return result;
    }


    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" FROM  TB_ZW_BUSI_REL_ITEM T   ");
        sql.append(" LEFT JOIN  TS_SIMPLE_CODE T2 ON  T.ITEM_ID =T2.RID ");
        sql.append(" WHERE T.BUS_ID = ").append(this.firstId);
        sql.append(" AND NOT EXISTS (SELECT 1 FROM TB_ZW_BUSI_MAIN_ITEM T1   ");
        sql.append(" where  T.ITEM_ID = T1.ITEM_ID AND T1.BUS_ID =  ").append(this.secondId);
        sql.append(" )  ");
        if (StringUtils.isNotBlank(searchName)) {
            sql.append(" AND T2.CODE_NAME  LIKE :searchName escape '\\\'");
            this.paramMap.put("searchName", "%" + StringUtils.convertBFH(this.searchName.trim()) + "%");
        }
        String h2 = "SELECT COUNT(*) " + sql;
        String h1 = "SELECT T2.RID ,T2.CODE_NAME  " + sql + "  ORDER BY T2.NUM ,T2.CODE_NO";
        return new String[]{h1, h2};
    }

    /**
     * <p>方法描述：确定</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:13
     */
    public void submitAction() {
        if (null == this.allSelectedList || this.allSelectedList.size() == 0) {
            JsfUtil.addErrorMessage("请选择数据！");
            return;
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selected", this.allSelectedList);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * <p>方法描述：关闭</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:13
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    /**
     * <p>方法描述： 翻页监听</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:14
     */
    public void pageListener() {
        this.selectedList.clear();
        this.selectedList.addAll(this.allSelectedList);
        RequestContext.getCurrentInstance().update(this.TABLE_ID);
    }

    /**
     * <p>方法描述：行选中监听，添加元素</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:15
     */
    public void rowSelectListener(SelectEvent event) {
        BusiItemInfoDTO dto = (BusiItemInfoDTO) event.getObject();
        this.allSelectedList.add(dto);
    }

    /**
     * <p>方法描述：行取消，移除元素</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:15
     */
    public void rowUnselectListener(UnselectEvent event) {
        BusiItemInfoDTO dto = (BusiItemInfoDTO) event.getObject();
        this.allSelectedList.remove(dto);
    }

    /**
     * <p>方法描述：全选或取消全选</p>
     *
     * @MethodAuthor hsj 2024-07-08 16:15
     */
    public void toggleSelectListener(ToggleSelectEvent event) {
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(this.TABLE_ID);
        List<BusiItemInfoDTO> displayList = this.itemDataModel.getPackData();
        if (CollectionUtils.isEmpty(displayList)) {
            return;
        }
        if (event.isSelected()) {
            List<BusiItemInfoDTO> list = (List<BusiItemInfoDTO>) dataTable.getSelection();
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            for (BusiItemInfoDTO o : list) {
                if (!this.allSelectedList.contains(o)) {
                    this.allSelectedList.add(o);
                }
            }
        } else {//取消全选，需要移除当前页的所有元素
            this.allSelectedList.removeAll(displayList);
        }
    }

    public PackLazyDataModel getItemDataModel() {
        return itemDataModel;
    }

    public void setItemDataModel(PackLazyDataModel itemDataModel) {
        this.itemDataModel = itemDataModel;
    }

    public String getSearchName() {
        return searchName;
    }

    public void setSearchName(String searchName) {
        this.searchName = searchName;
    }

    public List<BusiItemInfoDTO> getSelectedList() {
        return selectedList;
    }

    public void setSelectedList(List<BusiItemInfoDTO> selectedList) {
        this.selectedList = selectedList;
    }

    public List<BusiItemInfoDTO> getAllSelectedList() {
        return allSelectedList;
    }

    public void setAllSelectedList(List<BusiItemInfoDTO> allSelectedList) {
        this.allSelectedList = allSelectedList;
    }

    public String getTABLE_ID() {
        return TABLE_ID;
    }

    public void setTABLE_ID(String TABLE_ID) {
        this.TABLE_ID = TABLE_ID;
    }

    public Integer getFirstId() {
        return firstId;
    }

    public void setFirstId(Integer firstId) {
        this.firstId = firstId;
    }

    public Integer getSecondId() {
        return secondId;
    }

    public void setSecondId(Integer secondId) {
        this.secondId = secondId;
    }
}
