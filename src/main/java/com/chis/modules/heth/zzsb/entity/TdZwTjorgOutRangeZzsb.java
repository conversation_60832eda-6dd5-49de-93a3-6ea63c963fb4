package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2020-2-26
 */
@Entity
@Table(name = "TD_ZW_TJORG_OUT_RANGE")
@SequenceGenerator(name = "TdZwTjorgOutRangeSeq", sequenceName = "TD_ZW_TJORG_OUT_RANGE_SEQ", allocationSize = 1)
public class TdZwTjorgOutRangeZzsb implements java.io.Serializable, IZwOrgOutRangeZzsb {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorginfoZzsb fkByOrgId;
	private TsZone fkByZoneId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwTjorgOutRangeZzsb() {
	}

	public TdZwTjorgOutRangeZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
    @Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgOutRangeSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    @Transient
    @Override
    public IZwOrginfoZzsb getZwOrginfo() {
        return fkByOrgId;
    }

    @Override
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
        this.fkByOrgId = (TdZwTjorginfoZzsb) zwOrginfo;
    }

    @ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwTjorginfoZzsb getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwTjorginfoZzsb fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZONE_ID")			
	public TsZone getFkByZoneId() {
		return fkByZoneId;
	}

	public void setFkByZoneId(TsZone fkByZoneId) {
		this.fkByZoneId = fkByZoneId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}