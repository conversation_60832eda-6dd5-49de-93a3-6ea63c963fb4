package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.faces.model.SelectItemGroup;
import java.io.Serializable;
import java.util.*;

/**
 * 资质申报仪器信息
 *
 * <AUTHOR>
 * @date 2022/4/22
 */
public class ZzsbInstInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构仪器关联List
     */
    private List<TdZwTjinstApply> orgInstApplyListView;
    /**
     * 所有仪器List
     */
    private List<TdZwInstinfoZzsb> allInstListView;
    /**
     * 机构关联仪器的仪器RID Set
     */
    private Set<Integer> orgInstApplyInstRidRidSet;
    /**
     * 选择的机构仪器关联
     */
    private TdZwTjinstApply selectOrgInstApply;
    /**
     * 选择的仪器
     */
    private TdZwInstinfoZzsb selectInstInfo;
    /**
     * 临时实体
     */
    private TdZwInstinfoZzsb tempInstInfo;
    /**
     * 查询条件
     * <p>0 仪器名称
     * <p>1 仪器型号
     */
    private String[] searchInstInfo;
    /**
     * 仪器类型小类别码表
     */
    private List<TsSimpleCode> instKindTypeList;
    /**
     * 信息点：仪器类型下拉列表
     */
    private List<SelectItemGroup> instTypeList;
    /**
     * 仪器类型小类别对应大类别RIDMap(小类别RID, 大类别RID)
     */
    private Map<Integer, Integer> instTypeMap;
    /**
     * 信息点：仪器检定要求下拉列表
     */
    private List<TsSimpleCode> instAcptReqList;
    /**
     * 信息点：仪器状态ManyCheckbox
     */
    private List<TsSimpleCode> instStateList;
    /**
     * 预警日期
     */
    private String waringDate;

    public ZzsbInstInfoVO() {
    }

    public void init() {
        this.orgInstApplyListView = new ArrayList<>();
        this.allInstListView = new ArrayList<>();
        this.orgInstApplyInstRidRidSet = new HashSet<>();
        this.selectOrgInstApply = new TdZwTjinstApply();
        this.selectOrgInstApply.setFkByOrgId(new TdZwTjorginfoApply());
        this.selectOrgInstApply.setFkByInstId(new TdZwInstinfoZzsb());
        this.selectInstInfo = new TdZwInstinfoZzsb();
        this.selectInstInfo.setFkByInstKindId(new TsSimpleCode());
        this.selectInstInfo.setFkByInstKindDetalId(new TsSimpleCode());
        this.selectInstInfo.setAcptReqId(new TsSimpleCode());
        this.selectInstInfo.setInstStateId(new TsSimpleCode());
        this.tempInstInfo = new TdZwInstinfoZzsb();
        this.searchInstInfo = new String[2];
        this.instKindTypeList = new ArrayList<>();
        this.instTypeList = new ArrayList<>();
        this.instTypeMap = new HashMap<>(16);
        this.instAcptReqList = new ArrayList<>();
        this.instStateList = new ArrayList<>();
    }

    public List<TdZwTjinstApply> getOrgInstApplyListView() {
        return orgInstApplyListView;
    }

    public void setOrgInstApplyListView(List<TdZwTjinstApply> orgInstApplyListView) {
        this.orgInstApplyListView = orgInstApplyListView;
    }

    public List<TdZwInstinfoZzsb> getAllInstListView() {
        return allInstListView;
    }

    public void setAllInstListView(List<TdZwInstinfoZzsb> allInstListView) {
        this.allInstListView = allInstListView;
    }

    public Set<Integer> getOrgInstApplyInstRidRidSet() {
        return orgInstApplyInstRidRidSet;
    }

    public void setOrgInstApplyInstRidRidSet(Set<Integer> orgInstApplyInstRidRidSet) {
        this.orgInstApplyInstRidRidSet = orgInstApplyInstRidRidSet;
    }

    public TdZwTjinstApply getSelectOrgInstApply() {
        return selectOrgInstApply;
    }

    public void setSelectOrgInstApply(TdZwTjinstApply selectOrgInstApply) {
        this.selectOrgInstApply = selectOrgInstApply;
    }

    public TdZwInstinfoZzsb getSelectInstInfo() {
        return selectInstInfo;
    }

    public void setSelectInstInfo(TdZwInstinfoZzsb selectInstInfo) {
        this.selectInstInfo = selectInstInfo;
    }

    public TdZwInstinfoZzsb getTempInstInfo() {
        return tempInstInfo;
    }

    public void setTempInstInfo(TdZwInstinfoZzsb tempInstInfo) {
        this.tempInstInfo = tempInstInfo;
    }

    public String[] getSearchInstInfo() {
        return searchInstInfo;
    }

    public void setSearchInstInfo(String[] searchInstInfo) {
        this.searchInstInfo = searchInstInfo;
    }

    public List<TsSimpleCode> getInstKindTypeList() {
        return instKindTypeList;
    }

    public void setInstKindTypeList(List<TsSimpleCode> instKindTypeList) {
        this.instKindTypeList = instKindTypeList;
    }

    public List<SelectItemGroup> getInstTypeList() {
        return instTypeList;
    }

    public void setInstTypeList(List<SelectItemGroup> instTypeList) {
        this.instTypeList = instTypeList;
    }

    public Map<Integer, Integer> getInstTypeMap() {
        return instTypeMap;
    }

    public void setInstTypeMap(Map<Integer, Integer> instTypeMap) {
        this.instTypeMap = instTypeMap;
    }

    public List<TsSimpleCode> getInstAcptReqList() {
        return instAcptReqList;
    }

    public void setInstAcptReqList(List<TsSimpleCode> instAcptReqList) {
        this.instAcptReqList = instAcptReqList;
    }

    public List<TsSimpleCode> getInstStateList() {
        return instStateList;
    }

    public void setInstStateList(List<TsSimpleCode> instStateList) {
        this.instStateList = instStateList;
    }

    public String getWaringDate() {
        return waringDate;
    }

    public void setWaringDate(String waringDate) {
        this.waringDate = waringDate;
    }

    @Override
    public String toString() {
        return "ZzsbInstInfoVO{}";
    }
}
