package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-14
 */
@Entity
@Table(name = "TD_ZW_TJORG_OUTCAR_APPLY")
@SequenceGenerator(name = "TdZwTjorgOutcarApply", sequenceName = "TD_ZW_TJORG_OUTCAR_APPLY_SEQ", allocationSize = 1)
public class TdZwTjorgOutcarApply implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorginfoApply fkByMainId;
	private String carNo;
	private String carBand;
	private String carStyle;
	private String carFrameNo;
	private Date productionDate;
	private Integer stateMark;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	private List<TdZwTjorgOutcInstApy> outcInstZzsbList = new ArrayList<>();
	/**
	 * +行驶证复印件20220623
	 */
	private String drivingAnnexPath;
	/**
	 * +车辆照片20220623
	 */
	private String carPhotoAnnexPath;
	/**
	 * +车载DR照片20220623
	 */
	private String carDrAnnexPath;
	
	public TdZwTjorgOutcarApply() {
	}

	public TdZwTjorgOutcarApply(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgOutcarApply")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwTjorginfoApply getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwTjorginfoApply fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "CAR_NO")	
	public String getCarNo() {
		return carNo;
	}

	public void setCarNo(String carNo) {
		this.carNo = carNo;
	}	
			
	@Column(name = "CAR_BAND")	
	public String getCarBand() {
		return carBand;
	}

	public void setCarBand(String carBand) {
		this.carBand = carBand;
	}	
			
	@Column(name = "CAR_STYLE")	
	public String getCarStyle() {
		return carStyle;
	}

	public void setCarStyle(String carStyle) {
		this.carStyle = carStyle;
	}	
			
	@Column(name = "CAR_FRAME_NO")	
	public String getCarFrameNo() {
		return carFrameNo;
	}

	public void setCarFrameNo(String carFrameNo) {
		this.carFrameNo = carFrameNo;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "PRODUCTION_DATE")			
	public Date getProductionDate() {
		return productionDate;
	}

	public void setProductionDate(Date productionDate) {
		this.productionDate = productionDate;
	}	
			
	@Column(name = "STATE_MARK")	
	public Integer getStateMark() {
		return stateMark;
	}

	public void setStateMark(Integer stateMark) {
		this.stateMark = stateMark;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByCarId", orphanRemoval = true)
	public List<TdZwTjorgOutcInstApy> getOutcInstZzsbList() {
		return outcInstZzsbList;
	}

	public void setOutcInstZzsbList(List<TdZwTjorgOutcInstApy> outcInstZzsbList) {
		this.outcInstZzsbList = outcInstZzsbList;
	}

	@Column(name = "DRIVING_ANNEX_PATH")
	public String getDrivingAnnexPath() {
		return drivingAnnexPath;
	}

	public void setDrivingAnnexPath(String drivingAnnexPath) {
		this.drivingAnnexPath = drivingAnnexPath;
	}

	@Column(name = "CAR_PHOTO_ANNEX_PATH")
	public String getCarPhotoAnnexPath() {
		return carPhotoAnnexPath;
	}

	public void setCarPhotoAnnexPath(String carPhotoAnnexPath) {
		this.carPhotoAnnexPath = carPhotoAnnexPath;
	}

	@Column(name = "CAR_DR_ANNEX_PATH")
	public String getCarDrAnnexPath() {
		return carDrAnnexPath;
	}

	public void setCarDrAnnexPath(String carDrAnnexPath) {
		this.carDrAnnexPath = carDrAnnexPath;
	}
}