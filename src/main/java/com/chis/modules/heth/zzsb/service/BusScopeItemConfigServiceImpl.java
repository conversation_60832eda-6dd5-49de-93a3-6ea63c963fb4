package com.chis.modules.heth.zzsb.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.TbZwBusiRelItem;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Transactional(readOnly = false)
public class BusScopeItemConfigServiceImpl extends AbstractTemplate {
    /**
     * <p>Description：查询 已选择的检测项目的rid </p>
     * <p>Author： yzz 2024-07-17 </p>
     */
    public String findRidsBySel() {
        String sql = "select listagg(ITEM_ID, ',') within group ( order by ITEM_ID) as rids " +
                "from TB_ZW_BUSI_REL_ITEM";
        List<Object> list = this.findDataBySqlNoPage(sql, null);
        return list.get(0) == null ? null : list.get(0).toString();
    }
    /**
     * <p>Description：通过检测项目rid删除检测项目 </p>
     * <p>Author： yzz 2024-07-17 </p>
     */
    public void delItemByRid(Integer rid) {
        String sql = "delete from TB_ZW_BUSI_REL_ITEM where RID=" + rid;
        this.executeSql(sql, null);
    }

    /**
     * <p>Description：查询业务范围大类ID、检测项目大类ID 下的所有检测项目小类 </p>
     * <p>Author： yzz 2024-07-17 </p>
     */
    public List<TbZwBusiRelItem> findItems(Object[] busiRelItem) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "select T from TbZwBusiRelItem T where T.fkByBusId.rid=:busId  ";
        if (busiRelItem[2] != null) {
            sql += " and T.fkByParentItemId.rid=:parentItemId ";
        }
        paramMap.put("busId", Integer.parseInt(busiRelItem[0].toString()));
        if (busiRelItem[2] != null) {
            paramMap.put("parentItemId", Integer.parseInt(busiRelItem[2].toString()));
        }
        sql+=" order by T.fkByItemId.num,T.fkByItemId.codeNo ";
        return this.findDataByHqlNoPage(sql, paramMap);
    }
    /**
     * <p>Description：批量保存 </p>
     * <p>Author： yzz 2024-07-17 </p>
     */
    public void saveItems(List<TbZwBusiRelItem> list) {
        this.saveBatchObjs(list);
    }
    
    /**
    * <p>Description：通过rid查询检测项目是否被关联 </p>
    * <p>Author： yzz 2024-07-18 </p>
    */
    public int findMainItemByRid(Integer rid) {
        String sql="select count(1)" +
                "from TB_ZW_BUSI_MAIN_ITEM where ITEM_ID="+rid;
        List list = this.findDataBySqlNoPage(sql, null);
        return list.get(0) == null ? 0 : Integer.parseInt(list.get(0).toString());
    }
}
