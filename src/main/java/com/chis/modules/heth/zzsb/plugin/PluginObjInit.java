package com.chis.modules.heth.zzsb.plugin;

import com.chis.modules.system.entity.*;
import com.chis.modules.system.enumn.SystemType;
import com.chis.modules.system.init.PluginInit;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Component(value = "PluginObjInit_Heth_141")
@Transactional(readOnly = false)
public class PluginObjInit extends PluginInit {

	@Override
	public void run() {
		//super.initDataStructureByXml();
		super.run();
		initTsMenuBtn();
		initReportTemplates();
	}
	/**
	 * 初始化菜单按钮
	 *
	 * <AUTHOR>
	 * @createDate 2020-04-26
	 */
	private void initTsMenuBtn() {
		Set<TsMenuBtn> set = HethMenuBtnPluginObj.menuSet;
		if (null != set && set.size() > 0) {
			for (TsMenuBtn t : set) {
				List<TsQuartz> list = em.createNamedQuery("TsMenuBtn.findByCode")
						.setParameter("buttoncode", t.getBtnCode()).getResultList();
				if (null == list || list.size() == 0) {
					TsMenu tsMenu = (TsMenu) em.createNamedQuery("TsMenu.findByMenuEn")
							.setParameter("menuEn", t.getMenucode()).getSingleResult();
					if (tsMenu != null) {
						t.setTsMenu(tsMenu);
						t.setLevelno("1");
						t.setIfReveal((short) 1);
						pluginUpdateService.save(t);
					}
				}
			}
		}
	}

	/**
	 * @Description: 初始化报告模板插件
	 *
	 * @MethodAuthor pw,2022年04月19日
	 */
	private void initReportTemplates() {
		Set<TsRpt> set = ReportTemplatePluginObj.dataSet;
		if (null != set && set.size() > 0) {
			for (TsRpt t : set) {
				List<TsRpt> list = em.createNamedQuery("TsRpt.findByCode").setParameter("rptCod", t.getRptCod())
						.getResultList();
				if (null == list || list.size() == 0) {
					t.setParamType(t.getSystemType().getTypeNo().intValue());
					pluginUpdateService.save(t);
				}
			}
		}
	}

	@Override
	public List<String> buildDataStructurePlugin() {
		return null;
	}

	@Override
	public Set<TsMenu> buildMenuPlugin() {
		return HethMenuPluginObj.menuSet;
	}

	@Override
	public Set<TsCodeRule> buildCodeRulePlugin() {
		return null;
	}

	@Override
	public Set<TsSystemParam> buildSystemParamPlugin() {
		return null;
	}

	@Override
	public Set<TsSimpleCode> buildCodePlugin() {
		return HethTsCodePluginObj.simpleCodeSet;
	}

	@Override
	public Set<TsCodeType> buildCodeTypePlugin() {
		return HethTsCodePluginObj.codeTypeSet;
	}

	@Override
	public SystemType buildSystemType() {
		return SystemType.HETH_ZZSB;
	}
}
