package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_ORGANNEX")
@SequenceGenerator(name = "TdZwOrgannexSeq", sequenceName = "TD_ZW_ORGANNEX_SEQ", allocationSize = 1)
public class TdZwOrgannexZzsb implements Serializable,IZwAnnexZzsb {
	private static final long serialVersionUID = 6314165072282772275L;

	private Integer rid;
	private TdZwSrvorginfoZzsb tdZwSrvorginfo;
	private Short annexType;
	private String fileName;
	private String filePath;
	private Date createDate;
	private Integer createManid;
	private TsZone fkByRegZoneId;
	private Integer unitLevel;
	private String unitName;

	public TdZwOrgannexZzsb() {
	}

	public TdZwOrgannexZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOrgannexSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwSrvorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwSrvorginfo = (TdZwSrvorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwSrvorginfoZzsb getTdZwSrvorginfo() {
		return this.tdZwSrvorginfo;
	}

	public void setTdZwSrvorginfo(TdZwSrvorginfoZzsb tdZwSrvorginfo) {
		this.tdZwSrvorginfo = tdZwSrvorginfo;
	}

	@Column(name = "ANNEX_TYPE" , precision = 2, scale = 0)
	public Short getAnnexType() {
		return this.annexType;
	}

	public void setAnnexType(Short annexType) {
		this.annexType = annexType;
	}

	@Column(name = "FILE_NAME" , length = 100)
	public String getFileName() {
		return this.fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	@Column(name = "FILE_PATH" , length = 200)
	public String getFilePath() {
		return this.filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	@ManyToOne
	@JoinColumn(name = "REG_ZONE_ID")			
	public TsZone getFkByRegZoneId() {
		return fkByRegZoneId;
	}

	public void setFkByRegZoneId(TsZone fkByRegZoneId) {
		this.fkByRegZoneId = fkByRegZoneId;
	}	
			
	@Column(name = "UNIT_LEVEL")	
	public Integer getUnitLevel() {
		return unitLevel;
	}

	public void setUnitLevel(Integer unitLevel) {
		this.unitLevel = unitLevel;
	}

	@Column(name = "UNIT_NAME")	
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}	
	
	
}