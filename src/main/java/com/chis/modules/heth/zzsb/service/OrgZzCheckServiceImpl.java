package com.chis.modules.heth.zzsb.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.IZwOrginfoZzsb;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资质机构审核Service
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(readOnly = false)
public class OrgZzCheckServiceImpl extends AbstractTemplate {

    /**
     * <p>方法描述：提交或者撤销资质机构审核 </p>
     * @MethodAuthor： pw 2022/10/31
     **/
    public void auditOrCancelOrgInfo(Integer orgType, IZwOrginfoZzsb zwOrginfoEntity){
        if(null == orgType || null == zwOrginfoEntity || null == zwOrginfoEntity.getRid()){
            return;
        }
        Integer editCheckRst = zwOrginfoEntity.getCheckRst();
        if(null == editCheckRst){
            return;
        }
        String editCheckAudit = zwOrginfoEntity.getAuditAdv();
        Integer chkPsnId = null == zwOrginfoEntity.getFkByChkPsnId() ? null : zwOrginfoEntity.getFkByChkPsnId().getRid();
        Short state = zwOrginfoEntity.getState();
        StringBuilder updateBuffer = new StringBuilder();
        if(0 == orgType){
            updateBuffer.append(" UPDATE TD_ZW_TJORGINFO ");
        }else if(1 == orgType){
            updateBuffer.append(" UPDATE TD_ZW_SRVORGINFO ");
        }else if(2 == orgType){
            updateBuffer.append(" UPDATE TD_ZW_DIAGORGINFO ");
        }else if(3 == orgType){
            updateBuffer.append(" UPDATE TD_ZW_OCCHETH_INFO ");
        }
        Map<String,Object> paramMap = new HashMap<>();
        updateBuffer.append(" SET ").append("STATE=:state ");
        updateBuffer.append(",CHECK_RST=");
        if(null != editCheckRst){
            updateBuffer.append(":checkRst ");
            paramMap.put("checkRst",editCheckRst);
        }else{
            updateBuffer.append("NULL ");
        }
        updateBuffer.append(",AUDIT_ADV= ");
        if(StringUtils.isNotBlank(editCheckAudit)){
            updateBuffer.append(":auditAdv ");
            paramMap.put("auditAdv",editCheckAudit);
        }else{
            updateBuffer.append("NULL ");
        }
        updateBuffer.append(",CHK_PSN_ID= ");
        if(null != chkPsnId){
            updateBuffer.append(":checkPsn ");
            paramMap.put("checkPsn",chkPsnId);
        }else{
            updateBuffer.append("NULL ");
        }
        updateBuffer.append(" WHERE RID=").append(zwOrginfoEntity.getRid());
        paramMap.put("state",state);
        this.executeSql(updateBuffer.toString(), paramMap);
    }

    /**
     * <p>方法描述： 通过资质类别以及资质机构rid 获取对应的预警项 </p>
     * @MethodAuthor： pw 2022/11/2
     **/
    @Transactional(readOnly = true)
    public Map<Integer,List<Object[]>> findWarnTypeIdByOrgTypeAndRidGroupByWarnType(Integer orgType, Integer rid){
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT DISTINCT t1.WARN_TYPE,t2.WARN_TYPE_ID,t3.CODE_NAME,t3.NUM,t3.CODE_LEVEL_NO ,t3.CODE_NO  FROM TD_ZW_ORG_WARN t  ")
                .append(" INNER JOIN TB_ZW_ORG_WARN_CONFIG t1 ON t.WARN_ID =t1.RID ")
                .append(" INNER JOIN TB_ZW_ORG_WARN_SUB t2 ON t2.MAIN_ID =t1.RID  ")
                .append(" INNER JOIN TS_SIMPLE_CODE t3 ON t2.WARN_TYPE_ID = t3.rid ");
        sqlBuffer.append(" WHERE t.BUS_TYPE =").append(orgType).append(" AND t.BUS_ID =").append(rid);
        sqlBuffer.append(" ORDER BY t3.NUM,t3.CODE_LEVEL_NO,t3.CODE_NO ");
        List<Object[]> queryResultList = this.findSqlResultList(sqlBuffer.toString());
        Map<Integer,List<Object[]>> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(queryResultList)){
            return resultMap;
        }
        for(Object[] objArr : queryResultList){
            if(objArr.length < 2 || null == objArr[0] || null == objArr[1]){
                continue;
            }
            Integer warnType = Integer.parseInt(objArr[0].toString());

            List<Object[]> tmpList = resultMap.get(warnType);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(objArr);
            resultMap.put(warnType, tmpList);
        }
        return resultMap;
    }

    /**
     * <p>方法描述： 对应类型预警配置子表条数 </p>
     * @MethodAuthor： pw 2022/11/2
     **/
    @Transactional(readOnly = true)
    public Map<Integer,Integer> findWarnConfigGroupByWarnType(){
        Map<Integer,Integer> resultMap = new HashMap<>();
        StringBuffer sqlBuffer = new StringBuffer();
        sqlBuffer.append(" SELECT T.WARN_TYPE ,COUNT(T1.RID) FROM TB_ZW_ORG_WARN_CONFIG T ")
                .append(" INNER JOIN TB_ZW_ORG_WARN_SUB T1 ON T.RID = T1.MAIN_ID ")
                .append(" GROUP BY T.WARN_TYPE  ");
        List<Object[]> queryResultList = this.findSqlResultList(sqlBuffer.toString());
        if(CollectionUtils.isEmpty(queryResultList)){
            return resultMap;
        }
        for(Object[] objArr : queryResultList){
            if(objArr.length < 2 || null == objArr[0] || null == objArr[1]){
                continue;
            }
            Integer warnType = Integer.parseInt(objArr[0].toString());
            Integer count = Integer.parseInt(objArr[1].toString());
            resultMap.put(warnType, count);
        }
        return resultMap;
    }

    /**
     * 批量审核
     *
     * @param orgCheckListMap 待审核数据
     * @param state           审核状态
     * @param checkRst        审核结果
     * @param checkAudit      审核原因
     * @param chkPsnId        审核人
     * @return 审核成功数量
     */
    public int batchOrgCheck(Map<String, List<Integer>> orgCheckListMap,
                             Short state, Integer checkRst, String checkAudit, Integer chkPsnId) {
        int count = 0;
        for (String s : orgCheckListMap.keySet()) {
            List<Integer> ridList = orgCheckListMap.get(s);
            batchUpdateOrgCheck(ridList, s, state, checkRst, checkAudit, chkPsnId);
            count += ridList.size();
        }
        return count;
    }

    /**
     * 批量更新机构数据审核信息
     *
     * @param ridList    待更新机构rid
     * @param tableName  机构资质表名称
     * @param state      状态
     * @param checkRst   审核结果
     * @param checkAudit 审核原因
     * @param chkPsnId   审核人
     */
    private void batchUpdateOrgCheck(List<Integer> ridList, String tableName,
                                     Short state, Integer checkRst, String checkAudit, Integer chkPsnId) {
        Map<String, Object> paramMap = new HashMap<>();
        StringBuilder baseSql = new StringBuilder();
        baseSql.append("UPDATE ").append(tableName).append(" T ");
        baseSql.append("SET STATE = :state, CHECK_RST = :checkRst, AUDIT_ADV = :checkAudit ");
        paramMap.put("state", state);
        paramMap.put("checkRst", checkRst);
        paramMap.put("checkAudit", checkAudit);
        baseSql.append(", CHK_PSN_ID = :checkPsn WHERE T.RID IN ( ");
        paramMap.put("checkPsn", chkPsnId);
        int length = ridList.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ridList.subList(i * 1000, endIndex);
            String sql = baseSql + StringUtils.list2string(subList, ",") + ")";
            this.executeSql(sql, paramMap);
        }
    }

}
