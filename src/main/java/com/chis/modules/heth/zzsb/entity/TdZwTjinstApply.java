package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-14
 */
@Entity
@Table(name = "TD_ZW_TJINST_APPLY")
@SequenceGenerator(name = "TdZwTjinstApply", sequenceName = "TD_ZW_TJINST_APPLY_SEQ", allocationSize = 1)
public class TdZwTjinstApply implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorginfoApply fkByOrgId;
	private TdZwInstinfoZzsb fkByInstId;
	private Date createDate;
	private Integer createManid;
	
	public TdZwTjinstApply() {
	}

	public TdZwTjinstApply(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjinstApply")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwTjorginfoApply getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwTjorginfoApply fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "INST_ID")			
	public TdZwInstinfoZzsb getFkByInstId() {
		return fkByInstId;
	}

	public void setFkByInstId(TdZwInstinfoZzsb fkByInstId) {
		this.fkByInstId = fkByInstId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}