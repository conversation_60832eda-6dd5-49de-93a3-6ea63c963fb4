package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;

import static com.chis.modules.heth.zzsb.entity.TdZwSrvorginfoZzsb.sortZwOrgPsnsZzsbs;

/**
 * 职业病诊断机构基本信息
 *
 * <AUTHOR>
 * @createTime 2015-04-20
 * <p>修订内容：添加属性清空资质人员的所属机构的人员Id</p>
 * @ClassReviser qrr,2018年6月25日,TdZwDiagorginfo
 */
@Entity
@Table(name = "TD_ZW_DIAGORGINFO")
@SequenceGenerator(name = "TdZwDiagorginfoSeq", sequenceName = "TD_ZW_DIAGORGINFO_SEQ", allocationSize = 1)
public class TdZwDiagorginfoZzsb implements Serializable,IZwOrginfoZzsb {
	private static final long serialVersionUID = -3945847738661967780L;

	private Integer rid;
	private TsUnit tsUnit;
	private String orgName;
	private String orgAddr;
	private String orgFz;
	private String orgFzzw;
	private String linkMan;
	private String linkMb;
	private String linkTel;
	private String fax;
	private String zipcode;
	private String email;
	private String certNo;
	private Date firstGetday;
	private Date createDate;
	private Integer createManid;
	private List<TdZwDiagitemsZzsb> tdZwDiagitemses = new LinkedList<TdZwDiagitemsZzsb>();
	private List<TdZwDiaginstZzsb> tdZwDiaginsts = new LinkedList<TdZwDiaginstZzsb>();
	private List<TdZwDiagpsnsZzsb> tdZwDiagpsnses = new LinkedList<TdZwDiagpsnsZzsb>();
	private List<TdZwDiagannexZzsb> tdZwDiagannexes = new LinkedList<TdZwDiagannexZzsb>();
    private Short state;

	private String jcItems;
	
	private Integer cancelState;
	private Date cancelDate;
	private Date validDate;
	private String delPsnIds;
	//社会信用代码
	private String creditCode;
    /**+外出开展职业健康检查工作能力20200221*/
    private Integer outWorkPower;

	/**
	 * 备案管理机构ID
	 */
	private Integer rcdOrgId;
	/**
	 * 备案单位名称20220822
	 * */
	private String rcdUnitName;

	/**审核信息-审核结果*/
	private Integer checkRst;
	/**审核信息-审核意见*/
	private String auditAdv;
	/**审核信息-审核人*/
	private TsUserInfo fkByChkPsnId;
	/**主数据时间戳*/
	private Date masterDataTime;
	private Date modifyDate;
	private Integer modifyManid;
	public TdZwDiagorginfoZzsb() {
	}

	public TdZwDiagorginfoZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwDiagorginfoSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TsUnit getTsUnit() {
		return this.tsUnit;
	}

	public void setTsUnit(TsUnit tsUnit) {
		this.tsUnit = tsUnit;
	}

	@Column(name = "ORG_NAME", length = 100)
	public String getOrgName() {
		return this.orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	@Column(name = "ORG_ADDR", length = 200)
	public String getOrgAddr() {
		return this.orgAddr;
	}

	public void setOrgAddr(String orgAddr) {
		this.orgAddr = orgAddr;
	}

	@Column(name = "ORG_FZ", length = 50)
	public String getOrgFz() {
		return this.orgFz;
	}

	public void setOrgFz(String orgFz) {
		this.orgFz = orgFz;
	}

	@Column(name = "ORG_FZZW", length = 50)
	public String getOrgFzzw() {
		return this.orgFzzw;
	}

	public void setOrgFzzw(String orgFzzw) {
		this.orgFzzw = orgFzzw;
	}

	@Column(name = "LINK_MAN", length = 50)
	public String getLinkMan() {
		return this.linkMan;
	}

	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}

	@Column(name = "LINK_MB", length = 50)
	public String getLinkMb() {
		return this.linkMb;
	}

	public void setLinkMb(String linkMb) {
		this.linkMb = linkMb;
	}

	@Column(name = "LINK_TEL", length = 50)
	public String getLinkTel() {
		return this.linkTel;
	}

	public void setLinkTel(String linkTel) {
		this.linkTel = linkTel;
	}

	@Column(name = "FAX", length = 50)
	public String getFax() {
		return this.fax;
	}

	public void setFax(String fax) {
		this.fax = fax;
	}

	@Column(name = "ZIPCODE", length = 10)
	public String getZipcode() {
		return this.zipcode;
	}

	public void setZipcode(String zipcode) {
		this.zipcode = zipcode;
	}

	@Column(name = "EMAIL", length = 50)
	public String getEmail() {
		return this.email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Column(name = "CERT_NO", length = 50)
	public String getCertNo() {
		return this.certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "FIRST_GETDAY", length = 7)
	public Date getFirstGetday() {
		return this.firstGetday;
	}

	public void setFirstGetday(Date firstGetday) {
		this.firstGetday = firstGetday;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" , precision = 22, scale = 0)
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	@Override
	public List<IZwAnnexZzsb> getZwAnnex() {
		if(null != this.tdZwDiagannexes && this.tdZwDiagannexes.size() > 0) {
			List<IZwAnnexZzsb> list = new ArrayList<IZwAnnexZzsb>();
			for(TdZwDiagannexZzsb t : this.tdZwDiagannexes) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwAnnex(List<IZwAnnexZzsb> zwAnnexList) {
		if(null != zwAnnexList && zwAnnexList.size() > 0) {
			this.tdZwDiagannexes = new ArrayList<TdZwDiagannexZzsb>();
			for(IZwAnnexZzsb t : zwAnnexList) {
				this.tdZwDiagannexes.add((TdZwDiagannexZzsb) t);
			}
		}else if(null == zwAnnexList) {
			this.tdZwDiagannexes = null;
		}else {
			this.tdZwDiagannexes = new ArrayList<TdZwDiagannexZzsb>(0);
		}
	}

	@Transient
	@Override
	public List<IZwInstZzsb> getZwInst() {
		if(null != this.tdZwDiaginsts && this.tdZwDiaginsts.size() > 0) {
			List<IZwInstZzsb> list = new ArrayList<IZwInstZzsb>();
			for(TdZwDiaginstZzsb t : this.tdZwDiaginsts) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwInst(List<IZwInstZzsb> zwInstList) {
		if(null != zwInstList && zwInstList.size() > 0) {
			this.tdZwDiaginsts = new ArrayList<TdZwDiaginstZzsb>();
			for(IZwInstZzsb t : zwInstList) {
				this.tdZwDiaginsts.add((TdZwDiaginstZzsb) t);
			}
		}else if(null == zwInstList) {
			this.tdZwDiaginsts = null;
		}else {
			this.tdZwDiaginsts = new ArrayList<TdZwDiaginstZzsb>(0);
		}
	}

	@Transient
	@Override
	public List<IZwItemsZzsb> getZwItems() {
		return null;
	}

	@Override
	public void setZwItems(List<IZwItemsZzsb> zwItemsList) {

	}

	@Transient
	@Override
	public List<IZwOrgPsnsZzsb> getZwOrgPsns() {
		if(null != this.tdZwDiagpsnses && this.tdZwDiagpsnses.size() > 0) {
			List<IZwOrgPsnsZzsb> list = new ArrayList<IZwOrgPsnsZzsb>(this.tdZwDiagpsnses);
			return sortZwOrgPsnsZzsbs(list);
		}else {
			return null;
		}
	}

	@Override
	public void setZwOrgPsns(List<IZwOrgPsnsZzsb> zwOrgPsnsList) {
		if(null != zwOrgPsnsList && zwOrgPsnsList.size() > 0) {
			this.tdZwDiagpsnses = new ArrayList<TdZwDiagpsnsZzsb>();
			for(IZwOrgPsnsZzsb t : zwOrgPsnsList) {
				this.tdZwDiagpsnses.add((TdZwDiagpsnsZzsb) t);
			}
		}else if(null == zwOrgPsnsList) {
			this.tdZwDiagpsnses = null;
		}else {
			this.tdZwDiagpsnses = new ArrayList<TdZwDiagpsnsZzsb>(0);
		}
	}

	@Transient
	@Override
	public List<IZwSvritmZzsb> getZwSvritm() {
		if(null != this.tdZwDiagitemses && this.tdZwDiagitemses.size() > 0) {
			List<IZwSvritmZzsb> list = new ArrayList<IZwSvritmZzsb>();
			for(TdZwDiagitemsZzsb t : this.tdZwDiagitemses) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwSvritm(List<IZwSvritmZzsb> zwSvritmList) {
		if(null != zwSvritmList && zwSvritmList.size() > 0) {
			this.tdZwDiagitemses = new ArrayList<TdZwDiagitemsZzsb>();
			for(IZwSvritmZzsb t : zwSvritmList) {
				this.tdZwDiagitemses.add((TdZwDiagitemsZzsb) t);
			}
		}else if(null == zwSvritmList) {
			this.tdZwDiagitemses = null;
		}else {
			this.tdZwDiagitemses = new ArrayList<TdZwDiagitemsZzsb>(0);
		}
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwDiagorginfo",orphanRemoval = true)
	public List<TdZwDiagitemsZzsb> getTdZwDiagitemses() {
		return tdZwDiagitemses;
	}

	public void setTdZwDiagitemses(List<TdZwDiagitemsZzsb> tdZwDiagitemses) {
		this.tdZwDiagitemses = tdZwDiagitemses;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwDiagorginfo",orphanRemoval = true)
	public List<TdZwDiaginstZzsb> getTdZwDiaginsts() {
		return tdZwDiaginsts;
	}

	public void setTdZwDiaginsts(List<TdZwDiaginstZzsb> tdZwDiaginsts) {
		this.tdZwDiaginsts = tdZwDiaginsts;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwDiagorginfo",orphanRemoval = true)
	public List<TdZwDiagpsnsZzsb> getTdZwDiagpsnses() {
		return tdZwDiagpsnses;
	}

	public void setTdZwDiagpsnses(List<TdZwDiagpsnsZzsb> tdZwDiagpsnses) {
		this.tdZwDiagpsnses = tdZwDiagpsnses;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwDiagorginfo",orphanRemoval = true)
	public List<TdZwDiagannexZzsb> getTdZwDiagannexes() {
		return tdZwDiagannexes;
	}

	public void setTdZwDiagannexes(List<TdZwDiagannexZzsb> tdZwDiagannexes) {
		this.tdZwDiagannexes = tdZwDiagannexes;
	}
	
	@Transient
	@Override
	public String getJcItems() {
		return this.jcItems;
	}

	@Override
	public void setJcItems(String jcItems) {
		this.jcItems = jcItems;
	}

    @Column(name = "STATE")
    public Short getState() {
        return state;
    }

    public void setState(Short state) {
        this.state = state;
    }
    
    @Column(name = "CANCEL_STATE")	
	public Integer getCancelState() {
		return cancelState;
	}

	public void setCancelState(Integer cancelState) {
		this.cancelState = cancelState;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "CANCEL_DATE")			
	public Date getCancelDate() {
		return cancelDate;
	}

	public void setCancelDate(Date cancelDate) {
		this.cancelDate = cancelDate;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "VALID_DATE")			
	public Date getValidDate() {
		return validDate;
	}

	public void setValidDate(Date validDate) {
		this.validDate = validDate;
	}

	@Override
	@Column(name = "RCD_UNIT_NAME")
	public String getRcdUnitName() {
		return rcdUnitName;
	}

	@Override
	public void setRcdUnitName(String rcdUnitName) {
		this.rcdUnitName = rcdUnitName;
	}

	@Transient
	@Override
	public String getDelPsnIds() {
		return delPsnIds;
	}

	@Override
	public void setDelPsnIds(String delPsnIds) {
		this.delPsnIds = delPsnIds;
	}
	@Transient
	@Override
	public String getCreditCode() {
		return creditCode;
	}

	@Override
	public void setCreditCode(String creditCode) {
		this.creditCode = creditCode;
	}

	@Transient
    @Override
    public TsSimpleCode getFkByLevelId() {
        return null;
    }

    @Override
    public void setFkByLevelId(TsSimpleCode fkByLevelId) {

    }

    @Transient
    @Override
    public Integer getOutWorkPower() {
        return outWorkPower;
    }

    @Transient
    @Override
    public void setOutWorkPower(Integer outWorkPower) {
        this.outWorkPower = outWorkPower;
    }

    @Transient
    @Override
    public List<IZwOrgOutRangeZzsb> getZwOrgOutRanges() {
        return null;
    }

    @Override
    public void setZwOrgOutRanges(List<IZwOrgOutRangeZzsb> zwOrgOutRangeList) {

    }
    @Transient
	@Override
	public String getLabAddr() {
		return null;
	}
	@Override
	public void setLabAddr(String labAddr) {
		
	}
	@Transient
	@Override
	public String getCommuniAddr() {
		return null;
	}
	@Override
	public void setCommuniAddr(String communiAddr) {
		
	}
	@Transient
	@Override
	public TsSimpleCode getFkByUnitTypeId() {
		return null;
	}
	@Override
	public void setFkByUnitTypeId(TsSimpleCode fkByUnitTypeId) {
		
	}

	@Transient
	@Override
	public Date getFilingDate() {return null;}
	@Override
	public void setFilingDate(Date filingDate) {}

	@Override
	@Column(name = "RCD_ORG_ID")
	public Integer getRcdOrgId() {
		return rcdOrgId;
	}

	@Override
	public void setRcdOrgId(Integer rcdOrgId) {
		this.rcdOrgId = rcdOrgId;
	}

	@Transient
	@Override
	public List<TdZwTjorgOutRangeZzsb> getTdZwTjorgOutRanges() {
		return null;
	}

	@Override
	public void setTdZwTjorgOutRanges(List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges) {
	}

	@Transient
	@Override
	public String getZoneApproves() {
		return null;
	}

	@Override
	public void setZoneApproves(String zoneApproves) {

	}
	@Column(name = "CHECK_RST")
	@Override
	public Integer getCheckRst() {
		return checkRst;
	}

	@Override
	public void setCheckRst(Integer checkRst) {
		this.checkRst = checkRst;
	}
	@Column(name = "AUDIT_ADV")
	@Override
	public String getAuditAdv() {
		return auditAdv;
	}

	@Override
	public void setAuditAdv(String auditAdv) {
		this.auditAdv = auditAdv;
	}

	@Override
	@ManyToOne
	@JoinColumn(name = "CHK_PSN_ID")
	public TsUserInfo getFkByChkPsnId() {
		return fkByChkPsnId;
	}

	@Override
	public void setFkByChkPsnId(TsUserInfo fkByChkPsnId) {
		this.fkByChkPsnId = fkByChkPsnId;
	}

	@Transient
	@Override
	public Date getLastSmtDate() {
		return null;
	}

	@Override
	public void setLastSmtDate(Date lastSmtDate) {
	}
	@Transient
	@Override
	public List<TdZwSupportQual> getSupportQualList() {
		return null;
	}

	@Override
	public void setSupportQualList(List<TdZwSupportQual> supportQualList) {

	}

	@Transient
	@Override
	public List<TdZwSupportChk> getSupportChkList() {
		return null;
	}

	@Override
	public void setSupportChkList(List<TdZwSupportChk> supportChkList) {

	}

	@Transient
	@Override
	public List<TdZwSupportFund> getSupportFundList() {
		return null;
	}

	@Override
	public void setSupportFundList(List<TdZwSupportFund> supportFundList) {

	}
	@Transient
	@Override
	public String getOrgTypeName() {
		return null;
	}

	@Override
	public void setOrgTypeName(String orgTypeName) {

	}
	@Transient
	@Override
	public String getQuaTypeName() {
		return null;
	}

	@Override
	public void setQuaTypeName(String quaTypeName) {

	}
	@Transient
	@Override
	public TdZwSupportPlace getSupportPlaces() {
		return null;
	}

	@Override
	public void setSupportPlaces(TdZwSupportPlace supportPlaces) {

	}

	@Transient
	@Override
	public TsSimpleCode getFkByTypeId() {
		return null;
	}

	@Override
	public void setFkByTypeId(TsSimpleCode fkByTypeId) {

	}
	@Transient
	@Override
	public List<TdZwSupportTech> getSupportTechList() {
		return null;
	}
	@Override
	public void setSupportTechList(List<TdZwSupportTech> supportTechList) {

	}
	@Column(name = "MASTER_DATA_TIME")
	@Override
	public Date getMasterDataTime() {
		return masterDataTime;
	}
	@Override
	public void setMasterDataTime(Date masterDataTime) {
		this.masterDataTime=masterDataTime;
	}
	@Column(name = "MODIFY_DATE")
	@Override
	public Date getModifyDate() {
		return modifyDate;
	}
	@Override
	public void setModifyDate(Date modifyDate) {
		this.modifyDate=modifyDate;
	}
	@Column(name = "MODIFY_MANID")
	@Override
	public Integer getModifyManid() {
		return modifyManid;
	}
	@Override
	public void setModifyManid(Integer modifyManid) {
		this.modifyManid=modifyManid;
	}
}