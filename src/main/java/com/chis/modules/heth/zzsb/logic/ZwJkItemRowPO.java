package com.chis.modules.heth.zzsb.logic;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * <p>类描述：    </p>
 *
 * @ClassAuthor yzz，年月日，ZwJkItemPO
 **/
public class ZwJkItemRowPO {
    private TsSimpleCode jcItem;
    private TsSimpleCode serItem;
    private String ifExterInspect;
    /** 合并行*/
    private Integer rowspan;
    /** 合并行起始行*/
    private Integer rowspanStart;

    private String[] selectedSerItmDetail;
    private String[] selectedJcItem;
    private String[] selectedIfExterInspect;

    private Boolean disabled;

    public TsSimpleCode getJcItem() {
        return jcItem;
    }

    public void setJcItem(TsSimpleCode jcItem) {
        this.jcItem = jcItem;
    }

    public TsSimpleCode getSerItem() {
        return serItem;
    }

    public void setSerItem(TsSimpleCode serItem) {
        this.serItem = serItem;
    }

    public String getIfExterInspect() {
        return ifExterInspect;
    }

    public void setIfExterInspect(String ifExterInspect) {
        this.ifExterInspect = ifExterInspect;
    }

    public String[] getSelectedSerItmDetail() {
        return selectedSerItmDetail;
    }

    public void setSelectedSerItmDetail(String[] selectedSerItmDetail) {
        this.selectedSerItmDetail = selectedSerItmDetail;
    }

    public String[] getSelectedJcItem() {
        return selectedJcItem;
    }

    public void setSelectedJcItem(String[] selectedJcItem) {
        this.selectedJcItem = selectedJcItem;
    }

    public String[] getSelectedIfExterInspect() {
        return selectedIfExterInspect;
    }

    public void setSelectedIfExterInspect(String[] selectedIfExterInspect) {
        this.selectedIfExterInspect = selectedIfExterInspect;
    }

    public Integer getRowspan() {
        return rowspan;
    }

    public void setRowspan(Integer rowspan) {
        this.rowspan = rowspan;
    }

    public Integer getRowspanStart() {
        return rowspanStart;
    }

    public void setRowspanStart(Integer rowspanStart) {
        this.rowspanStart = rowspanStart;
    }

    public Boolean getDisabled() {
        if(selectedSerItmDetail!=null && selectedSerItmDetail.length==1){
            disabled = false;
        }else{
            disabled = true;
        }
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    @Override
    public boolean equals(Object obj) {
        if(obj!=null && obj instanceof ZwJkItemRowPO){
            ZwJkItemRowPO row = (ZwJkItemRowPO)obj;
            if(jcItem.getRid().equals(row.getJcItem().getRid()) && serItem.getRid().equals(row.getSerItem().getRid())){
                //大类和小类都一样，认为是同一个对象
                return true;
            }
        }
        return super.equals(obj);
    }
}
