package com.chis.modules.heth.zzsb.logic;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: 机构备案 跳转 传递参数
 * 
 * @ClassAuthor pw,2022年04月18日,QualOrgApplyParamPO
 */
public class QualOrgApplyParamPO implements Serializable {
    private static final long serialVersionUID = 4044408512059770197L;

    /** 资质机构备案主表的rid */
    private Integer orgApplyRid;
    /** 是否显示外出开展职业健康检查工作能力地区(外检车辆) 配置参数 */
    private Boolean ifHethFj;
    /** 检查机构是否有质量管理体系 配置参数 */
    private Boolean ifQualAnnex;
    /** 是否有人员汇总及填报说明人员汇总 */
    private Boolean ifPersonSummary;
    /** 是否终审 */
    private Boolean ifFinalAudit;
    /** 资质备案有无有效期限 */
    private Boolean ifValidityPeriod;
    /** 职业健康检查机构是否有服务明细 */
    private Boolean ifServiceDetails;
    /** 是否详情 */
    private Boolean ifView;
    /** 缓存父页面传递过来的码表 key codeTypeName value对应码表集合 */
    private Map<String,List<TsSimpleCode>> allSimpleCodeMap;
    /** 行政区划所属地区集合 */
    private List<TsZone> orgZoneList;
    /** 所有地区集合 */
    private List<TsZone> allZoneList;

    public Integer getOrgApplyRid() {
        return orgApplyRid;
    }

    public void setOrgApplyRid(Integer orgApplyRid) {
        this.orgApplyRid = orgApplyRid;
    }

    public Boolean getIfHethFj() {
        return ifHethFj;
    }

    public void setIfHethFj(Boolean ifHethFj) {
        this.ifHethFj = ifHethFj;
    }

    public Boolean getIfQualAnnex() {
        return ifQualAnnex;
    }

    public void setIfQualAnnex(Boolean ifQualAnnex) {
        this.ifQualAnnex = ifQualAnnex;
    }

    public Boolean getIfPersonSummary() {
        return ifPersonSummary;
    }

    public void setIfPersonSummary(Boolean ifPersonSummary) {
        this.ifPersonSummary = ifPersonSummary;
    }

    public Boolean getIfFinalAudit() {
        return ifFinalAudit;
    }

    public void setIfFinalAudit(Boolean ifFinalAudit) {
        this.ifFinalAudit = ifFinalAudit;
    }

    public Boolean getIfValidityPeriod() {
        return ifValidityPeriod;
    }

    public void setIfValidityPeriod(Boolean ifValidityPeriod) {
        this.ifValidityPeriod = ifValidityPeriod;
    }

    public Boolean getIfServiceDetails() {
        return ifServiceDetails;
    }

    public void setIfServiceDetails(Boolean ifServiceDetails) {
        this.ifServiceDetails = ifServiceDetails;
    }

    public Boolean getIfView() {
        return ifView;
    }

    public void setIfView(Boolean ifView) {
        this.ifView = ifView;
    }

    public Map<String, List<TsSimpleCode>> getAllSimpleCodeMap() {
        return allSimpleCodeMap;
    }

    public void setAllSimpleCodeMap(Map<String, List<TsSimpleCode>> allSimpleCodeMap) {
        this.allSimpleCodeMap = allSimpleCodeMap;
    }

    public List<TsZone> getOrgZoneList() {
        return orgZoneList;
    }

    public void setOrgZoneList(List<TsZone> orgZoneList) {
        this.orgZoneList = orgZoneList;
    }

    public List<TsZone> getAllZoneList() {
        return allZoneList;
    }

    public void setAllZoneList(List<TsZone> allZoneList) {
        this.allZoneList = allZoneList;
    }
}
