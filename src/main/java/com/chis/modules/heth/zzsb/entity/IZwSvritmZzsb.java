package com.chis.modules.heth.zzsb.entity;

import java.util.Date;
import java.util.List;

/**
 * 服务
 * Created by wlj on 2015-04-20.
 */
public interface IZwSvritmZzsb {

    public Integer getRid();
    public void setRid(Integer rid);

    public IZwOrginfoZzsb getZwOrginfo();
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo);

    public Short getItemCode();
    public void setItemCode(Short itemCode);

    public Date getCreateDate();
    public void setCreateDate(Date createDate);

    public Integer getCreateManid();
    public void setCreateManid(Integer createManid);

    public List<IZwSvritmSubZzsb> getZwSvritmSubList() ;
    public void setZwSvritmSubList(List<IZwSvritmSubZzsb> zwSvritmSubList);
}
