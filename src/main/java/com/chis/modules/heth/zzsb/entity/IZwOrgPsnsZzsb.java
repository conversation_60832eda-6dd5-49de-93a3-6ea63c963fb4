package com.chis.modules.heth.zzsb.entity;

import java.util.List;

/**
 * 人员
 * Created by wlj on 2015-04-20.
 */
public interface IZwOrgPsnsZzsb {

    public Integer getRid();
    public void setRid(Integer rid);

    public IZwOrginfoZzsb getZwOrginfo();
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo);

    public TdZwPsninfoZzsb getTdZwPsninfo();
    public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo);

    public String getCertNo();
    public void setCertNo(String certNo);

    public List<TdZwWorkdeptZzsb> getTdZwWorkdepts();
    public void setTdZwWorkdepts(List<TdZwWorkdeptZzsb> tdZwWorkdepts);

    public List<TdZwPsnitemsZzsb> getTdZwPsnitemses();
    public void setTdZwPsnitemses(List<TdZwPsnitemsZzsb> tdZwPsnitemses);
    
    public String getWordOfficeName();
    public void setWordOfficeName(String wordOfficeName);

    public String getWordItems();
    public void setWordItems(String wordItems);
    
    public String getWordItemStr();
    public void setWordItemStr(String wordItemStr);
    
	public String getJlCode();
	public void setJlCode(String jlCode);
			
	public String getLjJl();
	public void setLjJl(String ljJl);
	
	public String getOnDuty();
	public void setOnDuty(String onDuty);
}
