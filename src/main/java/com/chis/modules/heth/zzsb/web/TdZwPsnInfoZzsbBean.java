package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.TdZwPsnZzlbZzsb;
import com.chis.modules.heth.zzsb.entity.TdZwPsninfoZzsb;
import com.chis.modules.heth.zzsb.service.TdZwPsnInfoZzsbServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.Constants;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * 专业技术人员档案检索
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "tdZwPsnInfoZzsbBean")
@ViewScoped
public class TdZwPsnInfoZzsbBean extends FacesEditBean implements IProcessData {

    private static final long serialVersionUID = 349876154367049580L;

    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private TdZwPsnInfoZzsbServiceImpl psnInfoService = SpringContextHolder.getBean(TdZwPsnInfoZzsbServiceImpl.class);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    private Integer rid;
    /**
     * 资质人员信息
     */
    private TdZwPsninfoZzsb entity;
    /**
     * 资质人员查询信息
     */
    private TdZwPsninfoZzsb searchEntity;
    /**
     * 资质人员信息列表
     */
    private List<Object[]> dataList;
    /**
     * 人员资质类别列表
     */
    private List<Object[]> zzlbDataList;
    /**
     * 工作关系
     */
    private List<Object[]> workRelationships;
    /**
     * 资质类型
     */
    private String searchZzlx;
    private List<SelectItem> zzlxList;
    private Map<String, String[]> zzlxTableMap;
    /**
     * 所有的资质类别
     */
    private List<TsSimpleCode> zzlbList;
    private Map<String, List<TsSimpleCode>> zzlbMap;
    /**
     * 选择的资质类别
     */
    private String searchZzlbNames;
    private String searchZzlbRids;
    /**
     * 地区树
     */
    private List<TsZone> zoneList;
    /**
     * 地区编码
     */
    private String searchZoneCode;
    /**
     * 地区名称
     */
    private String searchZoneName;
    private TdzwPsnInfoZzsbBase psnInfoBase;
    private TdZwPsninfoZzsb tdZwPsninfo = new TdZwPsninfoZzsb();

    public TdZwPsnInfoZzsbBean() {
    }

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        this.ifSQL = Boolean.TRUE;
        this.entity = new TdZwPsninfoZzsb();
        this.searchEntity = new TdZwPsninfoZzsb();

        initZz();
        initZoneTree();
        initPsnInfo();

        if (ObjectUtil.isEmpty(this.zzlxList)) {
            JsfUtil.addErrorMessage("无可查询资质类型！");
            return;
        }
        searchAction();
    }

    @Override
    public void searchAction() {
        if (ObjectUtil.isEmpty(this.zzlxList)) {
            JsfUtil.addErrorMessage("无可查询资质类型！");
            return;
        }
        super.searchAction();
    }

    /**
     * 资质类型/资质类别初始化
     */
    private void initZz() {
        this.zzlxList = new ArrayList<>();
        this.zzlxTableMap = new HashMap<>(16);
        this.zzlxTableMap.put("0", new String[]{"TD_ZW_TJORGPSNS", "TD_ZW_TJORGINFO"});
        this.zzlxTableMap.put("1", new String[]{"TD_ZW_SRVORGPSNS", "TD_ZW_SRVORGINFO"});
        this.zzlxTableMap.put("2", new String[]{"TD_ZW_DIAGPSNS", "TD_ZW_DIAGORGINFO"});
        this.zzlxTableMap.put("3", new String[]{"TD_ZW_OCCHETH_PSNS", "TD_ZW_OCCHETH_INFO"});
        this.zzlxTableMap.put("4", new String[]{"TD_ZW_SUPPORT_PSNS", "TD_ZW_SUPPORT_ORG"});
        Set<String> btnSet = Global.getBtnSet();
        if (btnSet.contains("heth_zyjsrydajs_zyjkjcjg")) {
            this.zzlxList.add(new SelectItem(0, "职业健康检查机构"));
        }
        if (btnSet.contains("heth_zyjsrydajs_fswsjsfwjg")) {
            this.zzlxList.add(new SelectItem(1, "放射卫生技术服务机构"));
        }
        if (btnSet.contains("heth_zyjsrydajs_zybzdjg")) {
            this.zzlxList.add(new SelectItem(2, "职业病诊断机构"));
        }
        if (btnSet.contains("heth_zyjsrydajs_zywsjsfwjg")) {
            this.zzlxList.add(new SelectItem(3, "职业卫生技术服务机构"));
        }
        if (btnSet.contains("heth_zyjsrydajs_zcjg")) {
            this.zzlxList.add(new SelectItem(4, "技术支撑机构"));
        }

        this.zzlbList = new ArrayList<>();
        this.zzlbMap = new HashMap<>(16);

        if (ObjectUtil.isEmpty(this.zzlxList)) {
            return;
        }
        this.searchZzlx = StringUtils.objectToString(this.zzlxList.get(0).getValue());

        for(int i=0;i<5;i++){
            this.zzlbMap.put(String.valueOf(i), new ArrayList<TsSimpleCode>());
        }
        List<TsSimpleCode> allZzlbList = this.commService.findNumSimpleCodesByTypeId("5025");
        List<TsSimpleCode> tmpZzlbList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(allZzlbList)) {
            for (TsSimpleCode simpleCode : allZzlbList) {
                if (ObjectUtil.isNotEmpty(simpleCode)
                        && ObjectUtil.isNotEmpty(simpleCode.getRid())
                        && ObjectUtil.isNotEmpty(simpleCode.getCodeName())) {
                    if(ObjectUtil.isNotEmpty(simpleCode.getCodeDesc())){
                        List<String> zzlxCodeStrList = StringUtils.string2list(simpleCode.getCodeDesc(), ",");
                        for (String s : zzlxCodeStrList) {
                            this.zzlbMap.get(s).add(simpleCode);
                        }
                    }else{
                        tmpZzlbList.add(simpleCode);
                    }
                }
            }
        }
        if(!CollectionUtils.isEmpty(tmpZzlbList)){
            for(int i=0;i<5;i++){
                this.zzlbMap.get(String.valueOf(i)).addAll(tmpZzlbList);
            }
        }
        if (this.zzlbMap.containsKey(this.searchZzlx)) {
            this.zzlbList = this.zzlbMap.get(this.searchZzlx);
        }
    }

    /**
     * 地区初始化
     */
    private void initZoneTree() {
        TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
        }
        this.searchZoneName = tsZone.getZoneName();
        this.searchZoneCode = tsZone.getZoneGb();
        if (ObjectUtil.isEmpty(this.zoneList)) {
            if (Constants.ADMIN.equals(this.sessionData.getUser().getUserNo())) {
                this.zoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "");
            } else {
                this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(this.searchZoneCode, true, "", "");
            }
        }
    }

    private void initPsnInfo() {
        this.psnInfoBase = new TdzwPsnInfoZzsbBase();
        this.psnInfoBase.setValidPsnInfoId(false);
        TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
        this.psnInfoBase.setPsnInfo(psninfo);
        this.psnInfoBase.setPsnZzlb(new TdZwPsnZzlbZzsb());
        this.psnInfoBase.initPsnZzlb();
    }

    @Override
    public String[] buildHqls() {
        String zzlxTableName1 = this.zzlxTableMap.get(this.searchZzlx)[0];
        String zzlxTableName2 = this.zzlxTableMap.get(this.searchZzlx)[1];
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT DISTINCT P.RID, ");
        sb.append("                Z.FULL_NAME, ");
        sb.append("                P.EMP_NAME, ");
        sb.append("                P.IDC_CARD, ");
        sb.append("                U.UNITNAME, ");
        sb.append("                LISTAGG(SC.CODE_NAME, '，') WITHIN GROUP (ORDER BY SC.NUM) OVER (PARTITION BY P.RID) ZZLB, ");
        sb.append("                Z.ZONE_GB ");
        sb.append("FROM TD_ZW_PSNINFO P ");
        sb.append("         LEFT JOIN TS_UNIT U ON P.ORG_ID = U.RID ");
        sb.append("         LEFT JOIN TD_ZW_PSN_ZZLB PZ ON P.RID = PZ.PSN_ID ");
        //资质类别
        if (ObjectUtil.isNotEmpty(this.zzlbList)) {
            List<String> searchZzlbRidList = new ArrayList<>();
            for (TsSimpleCode simpleCode : this.zzlbList) {
                searchZzlbRidList.add(StringUtils.objectToString(simpleCode.getRid()));
            }
            sb.append(" AND PZ.QUAL_ID IN (:zzlbRidList) ");
            this.paramMap.put("zzlbRidList", searchZzlbRidList);
        } else {
            sb.append(" AND 1 = 2 ");
        }
        sb.append("         LEFT JOIN TS_SIMPLE_CODE SC ON PZ.QUAL_ID = SC.RID ");
        sb.append("         LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID ");
        sb.append("         RIGHT JOIN ").append(zzlxTableName1).append(" T ON P.RID = T.EMP_ID AND T.ON_DUTY = 1 ");
        sb.append("         RIGHT JOIN ").append(zzlxTableName2).append(" TI ON T.ORG_ID = TI.RID AND TI.STATE = 1 ");
        sb.append(" WHERE TI.RID IS NOT NULL ");
        //地区
        if (StringUtils.isNotBlank(searchZoneCode)) {
            sb.append(" AND Z.ZONE_GB LIKE :zoneGb ");
            this.paramMap.put("zoneGb", ZoneUtil.zoneSelect(searchZoneCode) + "%");
        }
        //姓名
        if (StringUtils.isNotBlank(searchEntity.getEmpName())) {
            sb.append(" AND P.EMP_NAME LIKE :empName ESCAPE '\\\' ");
            this.paramMap.put("empName", "%" + StringUtils.convertBFH(searchEntity.getEmpName()) + "%");
        }
        //身份证号
        if (StringUtils.isNotBlank(searchEntity.getIdcCard())) {
            sb.append(" AND P.IDC_CARD LIKE :idcCard ESCAPE '\\\' ");
            this.paramMap.put("idcCard", "%" + StringUtils.convertBFH(searchEntity.getIdcCard()) + "%");
        }
        //资质类别
        if (ObjectUtil.isNotEmpty(this.searchZzlbRids)) {
            sb.append(" AND EXISTS(SELECT 1 FROM TD_ZW_PSNINFO P1 LEFT JOIN TD_ZW_PSN_ZZLB PZ1 ON P1.RID = PZ1.PSN_ID WHERE PZ1.QUAL_ID IN (:searchZzlbRidList) AND P.RID = P1.RID) ");
            this.paramMap.put("searchZzlbRidList", StringUtils.string2list(this.searchZzlbRids, ","));
        }

        sb.append(" GROUP BY P.RID, Z.FULL_NAME, P.EMP_NAME, P.IDC_CARD, U.UNITNAME, Z.ZONE_GB, SC.CODE_NAME, SC.NUM ");
        String dataSql = sb + " ORDER BY Z.ZONE_GB, U.UNITNAME, P.EMP_NAME";
        String countSql = "SELECT COUNT(*) FROM (" + dataSql + ")";
        return new String[]{dataSql, countSql};
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[1]);
            obj[1] = fullName.substring(fullName.indexOf("_") + 1);
            //身份证脱敏
            obj[3] = StringUtils.encryptIdc(StringUtils.objectToString(obj[3]));
        }
    }

    /**
     * 根据资质类型加载类别
     */
    public void changeZzlbList() {
        this.zzlbList = new ArrayList<>();
        if (this.zzlbMap.containsKey(StringUtils.objectToString(this.searchZzlx))) {
            this.zzlbList = this.zzlbMap.get(StringUtils.objectToString(this.searchZzlx));
        }
        this.searchZzlbNames = "";
        this.searchZzlbRids = "";
    }

    @Override
    public void addInit() {
    }

    public void codeViewAction() {
        this.workRelationships = new ArrayList<>();
        this.tdZwPsninfo = this.psnInfoService.findTdZwPsnInfoByRid(this.rid, true);
        String idcCard = this.tdZwPsninfo.getIdcCard();
        //身份证脱敏
        this.tdZwPsninfo.setIdcCard(StringUtils.encryptIdc(this.tdZwPsninfo.getIdcCard()));
        //手机号码脱敏
        this.tdZwPsninfo.setMobileNo(StringUtils.encryptPhone(this.tdZwPsninfo.getMobileNo()));
        //手机号码脱敏
        this.tdZwPsninfo.setTelPhone(StringUtils.encryptPhone(this.tdZwPsninfo.getTelPhone()));
        //收件人手机号码脱敏
        this.tdZwPsninfo.setReceiveMobileNo(StringUtils.encryptPhone(this.tdZwPsninfo.getReceiveMobileNo()));
        //出生年月脱敏
        this.tdZwPsninfo.setBirthday(StringUtils.isNotBlank(this.tdZwPsninfo.getBirthday()) ? "**********" : null);
        this.psnInfoBase.setPsnInfo(this.tdZwPsninfo);
        this.psnInfoBase.setZwOrgType(Integer.valueOf(this.searchZzlx));
        this.psnInfoBase.initPsnInfo();
        this.psnInfoBase.initPsnZzlb();
        //初始化培训履历
        this.psnInfoBase.initTrainHis(idcCard,tdZwPsninfo.getZzlbList());
        this.searchAction();
        RequestContext.getCurrentInstance().execute("disabledInput()");
    }

    public void zzlbEditAction(){
        this.psnInfoBase.zzlbEditAction();
        RequestContext.getCurrentInstance().execute("disabledZzlbInput()");
    }

    @Override
    public void viewInit() {
        this.entity = new TdZwPsninfoZzsb();
        this.entity = this.psnInfoService.findTdZwPsnInfoByRid(this.rid, false);
        if (this.entity.getRid() == null) {
            return;
        }
        //身份证号脱敏
        this.entity.setIdcCard(StringUtils.encryptIdc(this.entity.getIdcCard()));
        //手机号码脱敏
        this.entity.setMobileNo(StringUtils.encryptPhone(this.entity.getMobileNo()));
        //固定电话号码脱敏
        this.entity.setTelPhone(StringUtils.encryptPhone(this.entity.getTelPhone()));
        //人员资质
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", this.rid);
        this.zzlbDataList = this.commService.findDataBySqlNoPage(buildSql(), paramMap);
        //工作关系
        this.workRelationships = new ArrayList<>();
    }

    /**
     * 生成SQL
     *
     * @return SQL
     */
    private String buildSql() {
        return "  SELECT RID,CODE_NAME,CENT_NO,SEND_DATE,WM_CONCAT(CODE_NAME1) " +
                " FROM ( " +
                " SELECT T1.RID ,T5.CODE_NAME,T1.CENT_NO,T1.SEND_DATE,T4.CODE_NAME AS CODE_NAME1 FROM TD_ZW_PSN_ZZLB T1 " +
                " LEFT JOIN TD_ZW_PSNINFO T2 ON T2.RID = T1.PSN_ID " +
                " LEFT JOIN TD_ZW_ZZLB_RANGE T3 ON T1.RID = T3.MAIN_ID " +
                " LEFT JOIN TS_SIMPLE_CODE T4 ON T4.RID = T3.ZZ_RANGE_ID " +
                " LEFT JOIN TS_SIMPLE_CODE T5 ON T5.RID = T1.QUAL_ID " +
                " WHERE T2.RID =:rid) " +
                " GROUP BY RID,CODE_NAME,CENT_NO,SEND_DATE ";
    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZwPsninfoZzsb getEntity() {
        return entity;
    }

    public void setEntity(TdZwPsninfoZzsb entity) {
        this.entity = entity;
    }

    public TdZwPsninfoZzsb getSearchEntity() {
        return searchEntity;
    }

    public void setSearchEntity(TdZwPsninfoZzsb searchEntity) {
        this.searchEntity = searchEntity;
    }

    public List<Object[]> getDataList() {
        return dataList;
    }

    public void setDataList(List<Object[]> dataList) {
        this.dataList = dataList;
    }

    public List<Object[]> getZzlbDataList() {
        return zzlbDataList;
    }

    public void setZzlbDataList(List<Object[]> zzlbDataList) {
        this.zzlbDataList = zzlbDataList;
    }

    public List<Object[]> getWorkRelationships() {
        return workRelationships;
    }

    public void setWorkRelationships(List<Object[]> workRelationships) {
        this.workRelationships = workRelationships;
    }

    public String getSearchZzlx() {
        return searchZzlx;
    }

    public void setSearchZzlx(String searchZzlx) {
        this.searchZzlx = searchZzlx;
    }

    public List<SelectItem> getZzlxList() {
        return zzlxList;
    }

    public void setZzlxList(List<SelectItem> zzlxList) {
        this.zzlxList = zzlxList;
    }

    public Map<String, String[]> getZzlxTableMap() {
        return zzlxTableMap;
    }

    public void setZzlxTableMap(Map<String, String[]> zzlxTableMap) {
        this.zzlxTableMap = zzlxTableMap;
    }

    public List<TsSimpleCode> getZzlbList() {
        return zzlbList;
    }

    public void setZzlbList(List<TsSimpleCode> zzlbList) {
        this.zzlbList = zzlbList;
    }

    public Map<String, List<TsSimpleCode>> getZzlbMap() {
        return zzlbMap;
    }

    public void setZzlbMap(Map<String, List<TsSimpleCode>> zzlbMap) {
        this.zzlbMap = zzlbMap;
    }

    public String getSearchZzlbNames() {
        return searchZzlbNames;
    }

    public void setSearchZzlbNames(String searchZzlbNames) {
        this.searchZzlbNames = searchZzlbNames;
    }

    public String getSearchZzlbRids() {
        return searchZzlbRids;
    }

    public void setSearchZzlbRids(String searchZzlbRids) {
        this.searchZzlbRids = searchZzlbRids;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public TdzwPsnInfoZzsbBase getPsnInfoBase() {
        return psnInfoBase;
    }

    public void setPsnInfoBase(TdzwPsnInfoZzsbBase psnInfoBase) {
        this.psnInfoBase = psnInfoBase;
    }

    public TdZwPsninfoZzsb getTdZwPsninfo() {
        return tdZwPsninfo;
    }

    public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
        this.tdZwPsninfo = tdZwPsninfo;
    }
}
