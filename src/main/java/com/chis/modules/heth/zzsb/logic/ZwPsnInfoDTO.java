package com.chis.modules.heth.zzsb.logic;

import java.io.Serializable;
import java.util.Objects;

public class ZwPsnInfoDTO implements Serializable {

   private Integer rid;
   /** 姓名*/
   private String empName;
   /** 性别*/
   private String sex;
   /** 职称*/
   private String theTitle;
   /** 所属机构名*/
   private String orgName;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getEmpName() {
        return empName;
    }

    public void setEmpName(String empName) {
        this.empName = empName;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getTheTitle() {
        return theTitle;
    }

    public void setTheTitle(String theTitle) {
        this.theTitle = theTitle;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ZwPsnInfoDTO that = (ZwPsnInfoDTO) o;
        return Objects.equals(rid, that.rid);
    }
}
