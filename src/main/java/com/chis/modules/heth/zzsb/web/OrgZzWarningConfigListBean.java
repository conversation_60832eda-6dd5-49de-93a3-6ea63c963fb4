package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.MapUtils;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.TbZwOrgWarnConfig;
import com.chis.modules.heth.zzsb.service.OrgZzWarningConfigServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 资质机构预警配置
 */
@ManagedBean(name = "orgZzWarningConfigListBean")
@ViewScoped
public class OrgZzWarningConfigListBean extends FacesEditBean {

    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private OrgZzWarningConfigServiceImpl warningConfigService = SpringContextHolder.getBean(OrgZzWarningConfigServiceImpl.class);

    /**
     * 1：人员预警 2：设备预警
     */
    private Integer type;

    /**
     * 人员配置基本要求
     */
    private List<TbZwOrgWarnConfig> orgWarnConfigList;
    /**
     * 各类职业健康检查岗位人员配置要求
     */
    private List<TbZwOrgWarnConfig> specialWarnConfigList;
    /**
     * 标删的数据
     */
    private List<TbZwOrgWarnConfig> delWarnConfigList;
    /**
     * 必填标记
     */
    private String fontcolor = "<font color=\"red\">*</font>";

    /**
     * 职称级别最低要求  码表5062
     */
    private List<TsSimpleCode> titleList;
    /**
     * 备案类别
     */
    private List<TsSimpleCode> regisTypeList;
    /**
     * 岗位
     */
    private List<TsSimpleCode> postList;

    /**
     * 当前操作对象
     */
    private TbZwOrgWarnConfig warnConfig;
    /**
     * 码表
     */
    private String codeNo;


    public OrgZzWarningConfigListBean() {
        this.ifSQL = true;

        // 初始化码表
        initSimpleCodeList();

        this.searchAction();
    }

    /**
     * <p>Description：初始化码表 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void initSimpleCodeList() {
        // 职称级别最低要求 码表
        this.titleList = new ArrayList<>();
        this.titleList = commService.findLevelSimpleCodesByTypeId("5062");

        // 备案类别  码表
        regisTypeList = new ArrayList<>();
        regisTypeList = commService.findLevelSimpleCodesByTypeId("5018");
        // 岗位
        postList = new ArrayList<>();
        postList = commService.findLevelSimpleCodesByTypeId("5620");
    }

    @Override
    public String[] buildHqls() {
        StringBuilder sql = new StringBuilder();
        sql.append(" select '职业健康检查机构' as jg,'人员预警' as warning,1 as type ");
        sql.append(" from DUAL ");
        sql.append(" union all ");
        sql.append(" select null as jg,'仪器预警' as warning,2 as type ");
        sql.append(" from DUAL ");
        String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        return new String[]{sql.toString(), h2};
    }

    /**
     * <p>Description：添加人员配置基本条件 </p>
     * <p>Author： yzz 2024-07-06 </p>
     */
    public void addPsnAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList;
        paramList = new ArrayList<>(1);
        // 标题
        paramList.add(this.type == 1 ? "人员岗位" : "仪器");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>(1);
        // 码表类型
        paramList.add(this.codeNo);
        paramMap.put("typeNo", paramList);
        // 查询条件名称
        paramList = new ArrayList<>(1);
        paramList.add(this.type == 1 ? "岗位名称" : "仪器名称");
        paramMap.put("searchName", paramList);
        // 列名
        paramList = new ArrayList<>(1);
        paramList.add(this.type == 1 ? "岗位名称" : "仪器名称");
        paramMap.put("colName", paramList);
        // 已选数据不显示
        paramList = new ArrayList<>(1);
        paramList.add("1");
        paramMap.put("noContainsSelected", paramList);
        if (this.type == 1) {
            // code_desc  过滤
            paramList = new ArrayList<>(1);
            paramList.add("0");
            paramMap.put("codeDescContains", paramList);
        } else {
            // 只选最末级
            paramList = new ArrayList<>(1);
            paramList.add("1");
            paramMap.put("selLast", paramList);
        }

        // 已选数据
        StringBuilder selRids = new StringBuilder();
        if (!CollectionUtils.isEmpty(this.orgWarnConfigList)) {
            for (TbZwOrgWarnConfig config : orgWarnConfigList) {
                if (this.type == 1) {
                    if (config.getFkByPostId() == null || config.getFkByPostId().getRid() == null) {
                        continue;
                    }
                } else {
                    if (config.getFkByInstTypeId() == null || config.getFkByInstTypeId().getRid() == null) {
                        continue;
                    }
                }
                selRids.append(",").append(this.type == 1 ? config.getFkByPostId().getRid() : config.getFkByInstTypeId().getRid());
            }
        }
        if (StringUtils.isNotBlank(selRids.toString())) {
            paramList = new ArrayList<>(1);
            paramList.add(selRids.substring(1));
            paramMap.put("selectIds", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }

    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> postList = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if (CollectionUtils.isEmpty(postList)) {
                return;
            }
            for (TsSimpleCode tsSimpleCode : postList) {
                TbZwOrgWarnConfig warnConfig = new TbZwOrgWarnConfig();
                warnConfig.setBusType(0);
                warnConfig.setWarnType(this.type == 1 ? 1 : 3);
                if (this.type == 1) {
                    warnConfig.setFkByPostId(tsSimpleCode);
                } else {
                    warnConfig.setFkByInstTypeId(tsSimpleCode);
                }
                warnConfig.setIfDel(0);
                this.orgWarnConfigList.add(0,warnConfig);
            }
        }
    }


    /**
     * <p>Description： 添加 各类职业健康检查岗位人员配置要求  </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void addSpecialPostAction() {
        TbZwOrgWarnConfig warnConfig = new TbZwOrgWarnConfig();
        warnConfig.setBusType(0);
        warnConfig.setWarnType(this.type == 1 ? 2 : 4);
        warnConfig.setIfDel(0);
        this.specialWarnConfigList.add(0,warnConfig);
    }

    /**
     * <p>Description： 人员岗位 点击事件</p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void selectSpecialPostAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList;
        paramList = new ArrayList<>(1);
        // 标题
        // 标题
        paramList.add(this.type == 1 ? "人员岗位" : "仪器");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>(1);
        // 码表类型
        paramList.add(this.codeNo);
        paramMap.put("typeNo", paramList);
        // 查询条件名称
        paramList = new ArrayList<>(1);
        paramList.add(this.type == 1 ? "岗位名称" : "仪器名称");
        paramMap.put("searchName", paramList);
        // 列名
        paramList = new ArrayList<>(1);
        paramList.add(this.type == 1 ? "岗位名称" : "仪器名称");
        paramMap.put("colName", paramList);

        if (this.type == 1) {
            // code_desc  过滤
            paramList = new ArrayList<>(1);
            paramList.add("0");
            paramMap.put("codeDescContains", paramList);
        } else {
            // 只选最末级
            paramList = new ArrayList<>(1);
            paramList.add("1");
            paramMap.put("selLast", paramList);
        }
        // 已选数据
        if (StringUtils.isNotBlank(this.warnConfig.getSpecialPostRid())) {
            paramList = new ArrayList<>(1);
            paramList.add(this.warnConfig.getSpecialPostRid());
            paramMap.put("selectIds", paramList);
        }

        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }

    /**
     * <p>Description：人员岗位 选中事件 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void onSpecialPostSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> postList = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if (CollectionUtils.isEmpty(postList)) {
                return;
            }
            String postNames = "";
            String postNameRids = "";
            for (TsSimpleCode tsSimpleCode : postList) {
                postNames += "，" + tsSimpleCode.getCodeName();
                postNameRids += "," + tsSimpleCode.getRid();
            }
            this.warnConfig.setSpecialPost(StringUtils.isNotBlank(postNames) ? postNames.substring(1) : "");
            this.warnConfig.setSpecialPostRid(StringUtils.isNotBlank(postNameRids) ? postNameRids.substring(1) : "");
        }
    }


    /**
     * <p>Description：删除前 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void beforeDelWarnConfig() {
        RequestContext.getCurrentInstance().execute("PF('DeleteDialog').show();");
    }

    /**
     * <p>Description：删除人员配置基本要求中的预警信息 </p>
     * <p>Author： yzz 2024-07-06 </p>
     */
    public void delWarnConfig() {
        if (warnConfig.getRid() != null) {
            warnConfig.setIfDel(1);
            this.delWarnConfigList.add(warnConfig);
        }
        if (new Integer("1").equals(this.warnConfig.getWarnType()) || new Integer("3").equals(this.warnConfig.getWarnType())) {
            this.orgWarnConfigList.remove(this.warnConfig);
            RequestContext.getCurrentInstance().update("tabView:editForm:postTables");
        } else {

            this.specialWarnConfigList.remove(this.warnConfig);
            RequestContext.getCurrentInstance().update("tabView:editForm:specialPostTables");
        }
    }

    @Override
    public void addInit() {

    }
    @Override
    public void viewInit() {

    }
    @Override
    public void modInit() {
        initModData();
        afterSave();
    }

    /**
    * <p>Description：预警页面 初始化 </p>
    * <p>Author： yzz 2024-07-11 </p>
    */
    public void initModData(){
        this.orgWarnConfigList = new ArrayList<>();
        this.specialWarnConfigList = new ArrayList<>();
        this.delWarnConfigList = new ArrayList<>();
        this.orgWarnConfigList = warningConfigService.findByHql(" select T from TbZwOrgWarnConfig T where T.busType=0 and T.warnType=" + (this.type == 1 ? 1 : 3) + " and coalesce(T.ifDel,0) =0 order by T.xh,T.rid desc ", TbZwOrgWarnConfig.class);
        this.specialWarnConfigList = warningConfigService.findByHql(" select T from TbZwOrgWarnConfig T where T.busType=0 and T.warnType=" + (this.type == 1 ? 2 : 4) + " and coalesce(T.ifDel,0) =0 order by T.xh,T.rid desc ", TbZwOrgWarnConfig.class);
        List<Object[]> warnSublist = warningConfigService.findWarnSubByMainRid(this.specialWarnConfigList);
        for (TbZwOrgWarnConfig config : this.specialWarnConfigList) {
            for (Object[] obj : warnSublist) {
                if (!config.getRid().equals(Integer.parseInt(obj[0].toString()))) {
                    continue;
                }
                config.setSpecialPost(obj[1].toString());
                config.setSpecialPostRid(obj[2].toString());
            }
        }
    }


    @Override
    public void saveAction() {
        boolean flag = false;
        if (!CollectionUtils.isEmpty(orgWarnConfigList)) {
            // 校验 人员配置基本条件
            for (int i = 0; i < this.orgWarnConfigList.size(); i++) {
                TbZwOrgWarnConfig warnConfig = this.orgWarnConfigList.get(i);
                if (warnConfig.getXh() == null) {
                    JsfUtil.addErrorMessage((this.type == 1 ? "【人员配置基本要求】" : "【基本设备设施要求】") + "中第" + (i + 1) + "行的【序号】不能为空！");
                    flag = true;
                }
                if (this.type == 1 && warnConfig.getMinNums() == null) {
                    JsfUtil.addErrorMessage("【人员配置基本要求】中第" + (i + 1) + "行的【人数最低要求】不能为空！");
                    flag = true;
                }
                if (this.type == 2 && warnConfig.getMinNums() == null && !warnConfig.getIfAppFalse() && !warnConfig.getIfAppTrue()) {
                    JsfUtil.addErrorMessage("【基本设备设施要求】中第" + (i + 1) + "行的【数量最低要求】和【检定仪器】不能同时为空！");
                    flag = true;
                }
            }
        }

        if (!CollectionUtils.isEmpty(this.specialWarnConfigList)) {
            // 校验 各类职业健康检查岗位人员配置要求
            for (int i = 0; i < this.specialWarnConfigList.size(); i++) {
                TbZwOrgWarnConfig warnConfig = this.specialWarnConfigList.get(i);
                if (warnConfig.getXh() == null) {
                    JsfUtil.addErrorMessage((this.type == 1 ? "【各类职业健康检查岗位人员配置要求】" : "【特殊要求】") + "中第" + (i + 1) + "行的【序号】不能为空！");
                    flag = true;
                }
                if (warnConfig.getItemId() == null) {
                    JsfUtil.addErrorMessage("请选择" + (this.type == 1 ? "【各类职业健康检查岗位人员配置要求】" : "【特殊要求】") + "中第" + (i + 1) + "行的【备案类别】！");
                    flag = true;
                }
                if (StringUtils.isBlank(warnConfig.getSpecialPostRid())) {
                    JsfUtil.addErrorMessage("请选择" + (this.type == 1 ? "【各类职业健康检查岗位人员配置要求】" : "【特殊要求】") + "中第" + (i + 1) + "行的" + (this.type == 1 ? "【人员岗位】" : "【仪器类型】") + "！");
                    flag = true;
                }
                if (warnConfig.getMinNums() == null) {
                    JsfUtil.addErrorMessage((this.type == 1 ? "【各类职业健康检查岗位人员配置要求】" : "【特殊要求】") + "中第" + (i + 1) + "行" + (this.type == 1 ? "【人数最低要求】" : "【数量最低要求】") + "不能为空");
                    flag = true;
                }
            }
        }

        if (flag) {
            return;
        }
        try {
            beforeSave();
            warningConfigService.saveBatchWarningConfig(this.orgWarnConfigList, this.specialWarnConfigList, this.delWarnConfigList);
            initModData();
            afterSave();
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }


    /**
     * <p>Description：保存前数据处理 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void beforeSave() {
        if (!CollectionUtils.isEmpty(this.orgWarnConfigList)) {
            for (TbZwOrgWarnConfig config : this.orgWarnConfigList) {
                config.setFkByTitleId(config.getTitleRid() == null ? null : new TsSimpleCode(config.getTitleRid()));
                if (this.type == 2) {
                    config.setIfTestInst(config.getIfAppTrue() ? Integer.valueOf(1) : config.getIfAppFalse() ? 0 : null);
                }
            }
        }
        if (CollectionUtils.isEmpty(this.delWarnConfigList)) {
            return;
        }
        for (TbZwOrgWarnConfig config : this.delWarnConfigList) {
            config.setFkByTitleId(config.getTitleRid() == null ? null : new TsSimpleCode(config.getTitleRid()));
        }
    }

    /**
     * <p>Description：保存后数据处理 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void afterSave() {
        if (CollectionUtils.isEmpty(this.orgWarnConfigList)) {
            return;
        }
        for (TbZwOrgWarnConfig config : this.orgWarnConfigList) {
            if (config.getFkByTitleId() != null && config.getFkByTitleId().getRid() != null) {
                config.setTitleRid(config.getFkByTitleId().getRid());
            }
            if (config.getIfTestInst() != null && this.type == 2) {
                config.setIfAppTrue(config.getIfTestInst().equals(1));
                config.setIfAppFalse(config.getIfTestInst().equals(0));
            }
        }
    }

    /**
     * <p>Description：清空岗位 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void clearPost() {
        warnConfig.setSpecialPost(null);
        warnConfig.setSpecialPostRid(null);
    }

    /**
     * <p>Description： </p>
     * <p>Author： yzz 2024-07-10 </p>
     */
    public void changeTestInst(Integer type, TbZwOrgWarnConfig warnConfig) {
        if (new Integer("1").equals(type) && warnConfig.getIfAppTrue()) {
            warnConfig.setIfAppFalse(false);
        }
        if (new Integer("2").equals(type) && warnConfig.getIfAppFalse()) {
            warnConfig.setIfAppTrue(false);
        }
    }


    public Integer getType() {
        return type;
    }
    public void setType(Integer type) {
        this.type = type;
    }
    public List<TbZwOrgWarnConfig> getOrgWarnConfigList() {
        return orgWarnConfigList;
    }
    public void setOrgWarnConfigList(List<TbZwOrgWarnConfig> orgWarnConfigList) {
        this.orgWarnConfigList = orgWarnConfigList;
    }

    public String getFontcolor() {
        return fontcolor;
    }
    public void setFontcolor(String fontcolor) {
        this.fontcolor = fontcolor;
    }

    public List<TsSimpleCode> getTitleList() {
        return titleList;
    }
    public void setTitleList(List<TsSimpleCode> titleList) {
        this.titleList = titleList;
    }

    public List<TsSimpleCode> getRegisTypeList() {
        return regisTypeList;
    }
    public void setRegisTypeList(List<TsSimpleCode> regisTypeList) {
        this.regisTypeList = regisTypeList;
    }
    public List<TsSimpleCode> getPostList() {
        return postList;
    }
    public void setPostList(List<TsSimpleCode> postList) {
        this.postList = postList;
    }

    public List<TbZwOrgWarnConfig> getSpecialWarnConfigList() {
        return specialWarnConfigList;
    }
    public void setSpecialWarnConfigList(List<TbZwOrgWarnConfig> specialWarnConfigList) {
        this.specialWarnConfigList = specialWarnConfigList;
    }

    public TbZwOrgWarnConfig getWarnConfig() {
        return warnConfig;
    }
    public void setWarnConfig(TbZwOrgWarnConfig warnConfig) {
        this.warnConfig = warnConfig;
    }

    public String getCodeNo() {
        return codeNo;
    }
    public void setCodeNo(String codeNo) {
        this.codeNo = codeNo;
    }


}
