package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2024-7-17
 */
@Entity
@Table(name = "TB_ZW_BUSI_REL_ITEM")
@SequenceGenerator(name = "TbZwBusiRelItem", sequenceName = "TB_ZW_BUSI_REL_ITEM_SEQ", allocationSize = 1)
public class TbZwBusiRelItem implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsSimpleCode fkByBusId;
	private TsSimpleCode fkByParentItemId;
	private TsSimpleCode fkByItemId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TbZwBusiRelItem() {
	}

	public TbZwBusiRelItem(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbZwBusiRelItem")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "BUS_ID")			
	public TsSimpleCode getFkByBusId() {
		return fkByBusId;
	}

	public void setFkByBusId(TsSimpleCode fkByBusId) {
		this.fkByBusId = fkByBusId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "PARENT_ITEM_ID")			
	public TsSimpleCode getFkByParentItemId() {
		return fkByParentItemId;
	}

	public void setFkByParentItemId(TsSimpleCode fkByParentItemId) {
		this.fkByParentItemId = fkByParentItemId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ITEM_ID")			
	public TsSimpleCode getFkByItemId() {
		return fkByItemId;
	}

	public void setFkByItemId(TsSimpleCode fkByItemId) {
		this.fkByItemId = fkByItemId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}