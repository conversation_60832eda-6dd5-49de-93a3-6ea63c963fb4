package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

import java.util.Date;
import java.util.List;

/**
 * 机构
 * Created by wlj on 2015-04-20.
 */
public interface IZwOrginfoZzsb {

    public Integer getRid();
    public void setRid(Integer rid) ;

    public TsUnit getTsUnit();
    public void setTsUnit(TsUnit tsUnit);

    public String getOrgName() ;
    public void setOrgName(String orgName) ;

    public String getOrgAddr() ;
    public void setOrgAddr(String orgAddr) ;

    public String getOrgFz();
    public void setOrgFz(String orgFz) ;

    public String getOrgFzzw();
    public void setOrgFzzw(String orgFzzw) ;

    public String getLinkMan();
    public void setLinkMan(String linkMan) ;

    public String getLinkMb() ;
    public void setLinkMb(String linkMb);

    public String getLinkTel();
    public void setLinkTel(String linkTel);

    public String getFax() ;
    public void setFax(String fax) ;

    public String getZipcode() ;
    public void setZipcode(String zipcode);

    public String getEmail() ;
    public void setEmail(String email);

    public String getCertNo() ;
    public void setCertNo(String certNo) ;

    public Date getFirstGetday();
    public void setFirstGetday(Date firstGetday);

    public Date getFilingDate();
    public void setFilingDate(Date filingDate);


    public Date getCreateDate() ;
    public void setCreateDate(Date createDate) ;

    public Integer getCreateManid() ;
    public void setCreateManid(Integer createManid) ;

    public List<IZwAnnexZzsb> getZwAnnex() ;
    public void setZwAnnex(List<IZwAnnexZzsb> zwAnnexList);

    public List<IZwInstZzsb> getZwInst() ;
    public void setZwInst(List<IZwInstZzsb> zwInstList);

    public List<IZwItemsZzsb> getZwItems() ;
    public void setZwItems(List<IZwItemsZzsb> zwItemsList);

    public List<IZwOrgPsnsZzsb> getZwOrgPsns() ;
    public void setZwOrgPsns(List<IZwOrgPsnsZzsb> zwOrgPsnsList);

    public List<IZwSvritmZzsb> getZwSvritm() ;
    public void setZwSvritm(List<IZwSvritmZzsb> zwSvritmList);
    
    /** +检查项目Ids */
    public String getJcItems() ;
    public void setJcItems(String jcItems);

    public Short getState();
    public void setState(Short state);

    public Integer getCancelState();
    public void setCancelState(Integer cancelState);
    
    public Date getCancelDate();
    public void setCancelDate(Date cancelDate);
    
    public Date getValidDate();
    public void setValidDate(Date validDate);
    /**
 	 * <p>方法描述：删除资质人员的所属机构id</p>
 	 * @MethodAuthor qrr,2018年6月23日,getDelPsnIds
     * */
    public String getDelPsnIds();
	public void setDelPsnIds(String delPsnIds);
	/**
	 * <p>方法描述：社会信用代码</p>
 	 * @MethodAuthor rcj,2018年9月3日,getCreditCode
	 * @return
	 */
	public String getCreditCode() ;
	public void setCreditCode(String creditCode) ;
    

    public TsSimpleCode getFkByLevelId();

    public void setFkByLevelId(TsSimpleCode fkByLevelId);

    /**
    * @Description : +外出开展职业健康检查工作能力20200221
    * @MethodAuthor: anjing
    * @Date : 2020/2/26 15:52
    **/
    public Integer getOutWorkPower();
    public void setOutWorkPower(Integer outWorkPower);

    /**
    * @Description : +外出开展职业健康检查工作能力范围20200211
    * @MethodAuthor: anjing
    * @Date : 2020/2/26 15:52
    **/
    public List<IZwOrgOutRangeZzsb> getZwOrgOutRanges() ;
    public void setZwOrgOutRanges(List<IZwOrgOutRangeZzsb> zwOrgOutRangeList);
    
    public String getLabAddr();
	public void setLabAddr(String labAddr);
	
	public String getCommuniAddr();
	public void setCommuniAddr(String communiAddr);
	
	public TsSimpleCode getFkByUnitTypeId();
	public void setFkByUnitTypeId(TsSimpleCode fkByUnitTypeId);

    Integer getRcdOrgId();
    void setRcdOrgId(Integer rcdOrgId);

    List<TdZwTjorgOutRangeZzsb> getTdZwTjorgOutRanges();

    void setTdZwTjorgOutRanges(List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges);

    String getZoneApproves();

    void setZoneApproves(String zoneApproves);

    String getRcdUnitName();
    void setRcdUnitName(String rcdUnitName);

    Integer getCheckRst() ;
    void setCheckRst(Integer checkRst);

    String getAuditAdv() ;
    void setAuditAdv(String auditAdv);

    TsUserInfo getFkByChkPsnId();

    void setFkByChkPsnId(TsUserInfo fkByChkPsnId);

    Date getLastSmtDate();

    void setLastSmtDate(Date lastSmtDate);


    List<TdZwSupportQual> getSupportQualList();

    void setSupportQualList(List<TdZwSupportQual> supportQualList);

    List<TdZwSupportChk> getSupportChkList();

    void setSupportChkList(List<TdZwSupportChk> supportChkList);

    List<TdZwSupportFund> getSupportFundList();

    void setSupportFundList(List<TdZwSupportFund> supportFundList);
    String getOrgTypeName();

    void setOrgTypeName(String orgTypeName);

    String getQuaTypeName();

    void setQuaTypeName(String quaTypeName);

    TdZwSupportPlace getSupportPlaces();

    void setSupportPlaces(TdZwSupportPlace supportPlaces);

    TsSimpleCode getFkByTypeId();

    void setFkByTypeId(TsSimpleCode fkByTypeId);

    List<TdZwSupportTech> getSupportTechList();

    void setSupportTechList(List<TdZwSupportTech> supportTechList);

    Date getMasterDataTime();

    void setMasterDataTime(Date masterDataTime);

    public Date getModifyDate();

    public void setModifyDate(Date modifyDate);

    public Integer getModifyManid();

    public void setModifyManid(Integer modifyManid);

}
