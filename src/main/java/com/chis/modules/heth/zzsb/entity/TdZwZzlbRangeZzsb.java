package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2017-5-26
 */
@Entity
@Table(name = "TD_ZW_ZZLB_RANGE")
@SequenceGenerator(name = "TdZwZzlbRange", sequenceName = "TD_ZW_ZZLB_RANGE_SEQ", allocationSize = 1)
public class TdZwZzlbRangeZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwPsnZzlbZzsb fkByMainId;
	private TsSimpleCode fkByZzRangeId;
	private Date createDate;
	private Integer createManid;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwZzlbRangeZzsb() {
	}

	public TdZwZzlbRangeZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwZzlbRange")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwPsnZzlbZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwPsnZzlbZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "ZZ_RANGE_ID")			
	public TsSimpleCode getFkByZzRangeId() {
		return fkByZzRangeId;
	}

	public void setFkByZzRangeId(TsSimpleCode fkByZzRangeId) {
		this.fkByZzRangeId = fkByZzRangeId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}