package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_SRVORGITEMS")
@SequenceGenerator(name = "TdZwSrvorgitemsSeq", sequenceName = "TD_ZW_SRVORGITEMS_SEQ", allocationSize = 1)
public class TdZwSrvorgitemsZzsb implements Serializable,IZwSvritmZzsb {
	private static final long serialVersionUID = 8354154286240778775L;

	private Integer rid;
	private TdZwSrvorginfoZzsb tdZwSrvorginfo;
	private Short itemCode;
	private Date createDate;
	private Integer createManid;
	private List<TdZwSrvorgitemsSubsZzsb> srvorgitemsSubs = new LinkedList<TdZwSrvorgitemsSubsZzsb>();
	public TdZwSrvorgitemsZzsb() {
	}

	public TdZwSrvorgitemsZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true )
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSrvorgitemsSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwSrvorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwSrvorginfo = (TdZwSrvorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwSrvorginfoZzsb getTdZwSrvorginfo() {
		return this.tdZwSrvorginfo;
	}

	public void setTdZwSrvorginfo(TdZwSrvorginfoZzsb tdZwSrvorginfo) {
		this.tdZwSrvorginfo = tdZwSrvorginfo;
	}

	@Column(name = "ITEM_CODE" , precision = 1, scale = 0)
	public Short getItemCode() {
		return this.itemCode;
	}

	public void setItemCode(Short itemCode) {
		this.itemCode = itemCode;
	}

	@Column(name = "CREATE_DATE" )
	@Temporal(TemporalType.TIMESTAMP)
	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Column(name = "CREATE_MANID" )
	public Integer getCreateManid() {
		return this.createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@Transient
	@Override
	public List<IZwSvritmSubZzsb> getZwSvritmSubList() {
		if(null != this.srvorgitemsSubs && this.srvorgitemsSubs.size() > 0) {
			List<IZwSvritmSubZzsb> list = new ArrayList<IZwSvritmSubZzsb>();
			for(IZwSvritmSubZzsb t : this.srvorgitemsSubs) {
				list.add(t);
			}
			return list;
		}else {
			return null;
		}
	}

	@Override
	public void setZwSvritmSubList(List<IZwSvritmSubZzsb> zwSvritmSubList) {
		if(null != zwSvritmSubList && zwSvritmSubList.size() > 0) {
			this.srvorgitemsSubs = new ArrayList<TdZwSrvorgitemsSubsZzsb>();
			for(IZwSvritmSubZzsb t : zwSvritmSubList) {
				this.srvorgitemsSubs.add((TdZwSrvorgitemsSubsZzsb) t);
			}
		}else if(null == zwSvritmSubList) {
			this.srvorgitemsSubs = null;
		}else {
			this.srvorgitemsSubs = new ArrayList<TdZwSrvorgitemsSubsZzsb>(0);
		}
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwSrvorgitems",orphanRemoval = true)
	public List<TdZwSrvorgitemsSubsZzsb> getSrvorgitemsSubs() {
		return srvorgitemsSubs;
	}

	public void setSrvorgitemsSubs(List<TdZwSrvorgitemsSubsZzsb> srvorgitemsSubs) {
		this.srvorgitemsSubs = srvorgitemsSubs;
	}
}