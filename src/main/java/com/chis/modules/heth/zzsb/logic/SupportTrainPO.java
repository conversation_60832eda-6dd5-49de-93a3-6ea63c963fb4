package com.chis.modules.heth.zzsb.logic;

import com.chis.modules.heth.zzsb.entity.TdZwSupportTrain;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.List;
/**
 * <p>类描述： 支撑机构添加修改学术培训弹框使用 </p>
 * @ClassAuthor： pw 2023/3/30
 **/
public class SupportTrainPO {
    /** 学术培训类别大类 */
    private TsSimpleCode simpleCode;
    /** 大类合并行数 */
    private Integer rowSpan;
    /** 具体培训类别对应的数据 */
    private List<TdZwSupportTrain> trainList;

    public TsSimpleCode getSimpleCode() {
        return simpleCode;
    }

    public void setSimpleCode(TsSimpleCode simpleCode) {
        this.simpleCode = simpleCode;
    }

    public Integer getRowSpan() {
        return rowSpan;
    }

    public void setRowSpan(Integer rowSpan) {
        this.rowSpan = rowSpan;
    }

    public List<TdZwSupportTrain> getTrainList() {
        return trainList;
    }

    public void setTrainList(List<TdZwSupportTrain> trainList) {
        this.trainList = trainList;
    }
}
