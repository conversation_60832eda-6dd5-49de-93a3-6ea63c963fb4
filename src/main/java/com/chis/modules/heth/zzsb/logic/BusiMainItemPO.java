package com.chis.modules.heth.zzsb.logic;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.List;

/**
 * <p>类描述：业务范围与重点、一般检测项目关系维护</p>
 *
 * @ClassAuthor hsj 2024-07-18 9:38
 */
public class BusiMainItemPO {
    private TsSimpleCode simpleCode;
    private List<TsSimpleCode> simpleCodeList;

    public TsSimpleCode getSimpleCode() {
        return simpleCode;
    }

    public void setSimpleCode(TsSimpleCode simpleCode) {
        this.simpleCode = simpleCode;
    }

    public List<TsSimpleCode> getSimpleCodeList() {
        return simpleCodeList;
    }

    public void setSimpleCodeList(List<TsSimpleCode> simpleCodeList) {
        this.simpleCodeList = simpleCodeList;
    }
}
