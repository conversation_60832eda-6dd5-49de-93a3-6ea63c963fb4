package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-10-25
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_HEALTH")
@SequenceGenerator(name = "TdZwSupportHealth", sequenceName = "TD_ZW_SUPPORT_HEALTH_SEQ", allocationSize = 1)
public class TdZwSupportHealth implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSupportOrgZzsb fkByMainId;
	private Integer healthPublic;
	private Integer publicNum;
	private Integer trainNum;
	private Integer trainPsnNum;
	private Integer ifPublicRpt;
	private Integer publicRptNum;
	private Integer ifScience;
	private Integer videoNum;
	private Integer audioNum;
	private Integer books;
	private Integer brochure;
	private Integer otherNum;
	private Integer ifPromote;
	private Integer ifGuid;
	private Integer guidCrpts;
	private Integer totalNum;
	private Integer cureNum;
	private Integer betterNum;
	private Integer deathNum;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwSupportHealth() {
	}

	public TdZwSupportHealth(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportHealth")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSupportOrgZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "HEALTH_PUBLIC")	
	public Integer getHealthPublic() {
		return healthPublic;
	}

	public void setHealthPublic(Integer healthPublic) {
		this.healthPublic = healthPublic;
	}	
			
	@Column(name = "PUBLIC_NUM")	
	public Integer getPublicNum() {
		return publicNum;
	}

	public void setPublicNum(Integer publicNum) {
		this.publicNum = publicNum;
	}	
			
	@Column(name = "TRAIN_NUM")	
	public Integer getTrainNum() {
		return trainNum;
	}

	public void setTrainNum(Integer trainNum) {
		this.trainNum = trainNum;
	}	
			
	@Column(name = "TRAIN_PSN_NUM")	
	public Integer getTrainPsnNum() {
		return trainPsnNum;
	}

	public void setTrainPsnNum(Integer trainPsnNum) {
		this.trainPsnNum = trainPsnNum;
	}	
			
	@Column(name = "IF_PUBLIC_RPT")	
	public Integer getIfPublicRpt() {
		return ifPublicRpt;
	}

	public void setIfPublicRpt(Integer ifPublicRpt) {
		this.ifPublicRpt = ifPublicRpt;
	}	
			
	@Column(name = "PUBLIC_RPT_NUM")	
	public Integer getPublicRptNum() {
		return publicRptNum;
	}

	public void setPublicRptNum(Integer publicRptNum) {
		this.publicRptNum = publicRptNum;
	}	
			
	@Column(name = "IF_SCIENCE")	
	public Integer getIfScience() {
		return ifScience;
	}

	public void setIfScience(Integer ifScience) {
		this.ifScience = ifScience;
	}	
			
	@Column(name = "VIDEO_NUM")	
	public Integer getVideoNum() {
		return videoNum;
	}

	public void setVideoNum(Integer videoNum) {
		this.videoNum = videoNum;
	}	
			
	@Column(name = "AUDIO_NUM")	
	public Integer getAudioNum() {
		return audioNum;
	}

	public void setAudioNum(Integer audioNum) {
		this.audioNum = audioNum;
	}	
			
	@Column(name = "BOOKS")	
	public Integer getBooks() {
		return books;
	}

	public void setBooks(Integer books) {
		this.books = books;
	}	
			
	@Column(name = "BROCHURE")	
	public Integer getBrochure() {
		return brochure;
	}

	public void setBrochure(Integer brochure) {
		this.brochure = brochure;
	}	
			
	@Column(name = "OTHER_NUM")	
	public Integer getOtherNum() {
		return otherNum;
	}

	public void setOtherNum(Integer otherNum) {
		this.otherNum = otherNum;
	}	
			
	@Column(name = "IF_PROMOTE")	
	public Integer getIfPromote() {
		return ifPromote;
	}

	public void setIfPromote(Integer ifPromote) {
		this.ifPromote = ifPromote;
	}	
			
	@Column(name = "IF_GUID")	
	public Integer getIfGuid() {
		return ifGuid;
	}

	public void setIfGuid(Integer ifGuid) {
		this.ifGuid = ifGuid;
	}	
			
	@Column(name = "GUID_CRPTS")	
	public Integer getGuidCrpts() {
		return guidCrpts;
	}

	public void setGuidCrpts(Integer guidCrpts) {
		this.guidCrpts = guidCrpts;
	}	
			
	@Column(name = "TOTAL_NUM")	
	public Integer getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(Integer totalNum) {
		this.totalNum = totalNum;
	}	
			
	@Column(name = "CURE_NUM")	
	public Integer getCureNum() {
		return cureNum;
	}

	public void setCureNum(Integer cureNum) {
		this.cureNum = cureNum;
	}	
			
	@Column(name = "BETTER_NUM")	
	public Integer getBetterNum() {
		return betterNum;
	}

	public void setBetterNum(Integer betterNum) {
		this.betterNum = betterNum;
	}	
			
	@Column(name = "DEATH_NUM")	
	public Integer getDeathNum() {
		return deathNum;
	}

	public void setDeathNum(Integer deathNum) {
		this.deathNum = deathNum;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}