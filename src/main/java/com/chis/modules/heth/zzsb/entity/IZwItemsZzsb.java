package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * 开展项目
 * Created by wlj on 2015-04-20.
 */
public interface IZwItemsZzsb {

    public Integer getRid();
    public void setRid(Integer rid);

    public TsSimpleCode getTsSimpleCode();
    public void setTsSimpleCode(TsSimpleCode tsSimpleCode);

    public IZwOrginfoZzsb getZwOrginfo();
    public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo);

    public Date getCreateDate();
    public void setCreateDate(Date createDate);

    public Integer getCreateManid();
    public void setCreateManid(Integer createManid);
    
    public Integer getIsOutter();
    public void setIsOutter(Integer isOutter);
}
