package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_SRVORGPSNS")
@SequenceGenerator(name = "TdZwSrvorgpsnsSeq", sequenceName = "TD_ZW_SRVORGPSNS_SEQ", allocationSize = 1)
public class TdZwSrvorgpsnsZzsb implements Serializable,IZwOrgPsnsZzsb {
	private static final long serialVersionUID = -9125466907544007524L;

	private Integer rid;
	private TdZwSrvorginfoZzsb tdZwSrvorginfo;
	private TdZwPsninfoZzsb tdZwPsninfo;
	private String certNo;
	private String wordOfficeName;
	private String wordItems;
	
	private String jlCode;
	private String ljJl;
	private String onDuty;

	public TdZwSrvorgpsnsZzsb() {
	}

	public TdZwSrvorgpsnsZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSrvorgpsnsSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwSrvorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwSrvorginfo = (TdZwSrvorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwSrvorginfoZzsb getTdZwSrvorginfo() {
		return this.tdZwSrvorginfo;
	}

	public void setTdZwSrvorginfo(TdZwSrvorginfoZzsb tdZwSrvorginfo) {
		this.tdZwSrvorginfo = tdZwSrvorginfo;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ID" )
	public TdZwPsninfoZzsb getTdZwPsninfo() {
		return this.tdZwPsninfo;
	}

	public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
		this.tdZwPsninfo = tdZwPsninfo;
	}

	@Column(name = "CERT_NO", length = 50)
	public String getCertNo() {
		return this.certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	@Transient
	@Override
	public List<TdZwWorkdeptZzsb> getTdZwWorkdepts() {
		return null;
	}

	@Override
	public void setTdZwWorkdepts(List<TdZwWorkdeptZzsb> tdZwWorkdepts) {

	}

	@Transient
	@Override
	public List<TdZwPsnitemsZzsb> getTdZwPsnitemses() {
		return null;
	}

	@Override
	public void setTdZwPsnitemses(List<TdZwPsnitemsZzsb> tdZwPsnitemses) {

	}
	
	@Transient
	@Override
	public String getWordOfficeName() {
		return wordOfficeName;
	}

	@Override
	public void setWordOfficeName(String wordOfficeName) {
		this.wordOfficeName = wordOfficeName;
	}

	@Transient
	@Override
	public String getWordItems() {
		return wordItems;
	}

	@Override
	public void setWordItems(String wordItems) {
		this.wordItems = wordItems;
	}
	
	
	@Column(name = "JL_CODE")	
	public String getJlCode() {
		return jlCode;
	}

	public void setJlCode(String jlCode) {
		this.jlCode = jlCode;
	}	
			
	@Column(name = "LJ_JL")	
	public String getLjJl() {
		return ljJl;
	}

	public void setLjJl(String ljJl) {
		this.ljJl = ljJl;
	}

	@Transient
	@Override
	public String getWordItemStr() {
		return null;
	}

	@Override
	public void setWordItemStr(String wordItemStr) {
		
	}	
	@Column(name="ON_DUTY")
	public String getOnDuty() {
		return onDuty;
	}

	public void setOnDuty(String onDuty) {
		this.onDuty = onDuty;
	}
}