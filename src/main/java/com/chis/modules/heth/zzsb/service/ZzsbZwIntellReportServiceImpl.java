package com.chis.modules.heth.zzsb.service;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.logic.OrgWarnPO;
import com.chis.modules.heth.zzsb.logic.TdZwOcchethItemsShow;
import com.chis.modules.heth.zzsb.logic.TrainHisPO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 职业卫生资质申报会话Bean
 * 
 * <AUTHOR>
 * @createDate 2015年4月20日
 */
@Service
@Transactional(readOnly = false)
public class ZzsbZwIntellReportServiceImpl extends AbstractTemplate {

	/**
	 * 根据资质申报主表Id，获取资质申报主表数据
	 * 
	 * @return
	 */
	@Transactional(readOnly = true)
	public IZwOrginfoZzsb findTdZwTjorginfo(Integer orgInfoType, Integer unitId){
		IZwOrginfoZzsb find = findTdZwTjorginfoEntity(orgInfoType, unitId);
		return fillTdZwTjorgInfos(orgInfoType,find);
	}

    @Transactional(readOnly = true)
	public IZwOrginfoZzsb fillTdZwTjorgInfos(Integer orgInfoType,IZwOrginfoZzsb find){
		if (null != find && null != find.getRid()) {
			//避免懒加载异常 设置null
			find.setZwAnnex(null);
			find.setZwInst(null);
			find.setZwItems(null);
			find.setZwOrgPsns(null);
			find.setZwSvritm(null);
			find.setZwOrgOutRanges(null);
			if(find.getFkByTypeId()==null){
				find.setFkByTypeId(new TsSimpleCode());
			}
			Map<String, String> tempMap = Maps.newHashMap();
			tempMap.put("1", "接触粉尘作业劳动者健康检查");
			tempMap.put("2", "接触化学作业劳动者健康检查");
			tempMap.put("3", "放射性作业劳动者健康检查");
			tempMap.put("4", "接触物理因素作业劳动者健康检查");
			tempMap.put("5", "接触生物因素作业劳动者健康检查");
			tempMap.put("6", "从事电工、压力容器、高处作业以及机动车驾驶等劳动者健康检查");

			StringBuilder sb = new StringBuilder();
			//机构资质附件查询赋值
			if (orgInfoType == 0) {//TD_ZW_TJORGANNEX
				sb.append("SELECT T.RID,T.ANNEX_TYPE,T.FILE_NAME,T.FILE_PATH,T.CREATE_DATE,T.CREATE_MANID,T.REG_ZONE_ID,T.UNIT_LEVEL,T.UNIT_NAME FROM TD_ZW_TJORGANNEX T WHERE T.ORG_ID= ").append(find.getRid());
			} else if (orgInfoType == 1) {//TD_ZW_ORGANNEX
				sb.append("SELECT T.RID,T.ANNEX_TYPE,T.FILE_NAME,T.FILE_PATH,T.CREATE_DATE,T.CREATE_MANID,T.REG_ZONE_ID,T.UNIT_LEVEL,T.UNIT_NAME FROM TD_ZW_ORGANNEX T WHERE T.ORG_ID= ").append(find.getRid());
			} else if (orgInfoType == 2) {//TD_ZW_DIAGANNEX
				sb.append("SELECT T.RID,T.ANNEX_TYPE,T.FILE_NAME,T.FILE_PATH,T.CREATE_DATE,T.CREATE_MANID,T.REG_ZONE_ID,T.UNIT_LEVEL,T.UNIT_NAME FROM TD_ZW_DIAGANNEX T WHERE T.ORG_ID= ").append(find.getRid());
			}else if(orgInfoType == 3){//TD_ZW_OCCHETH_ANNEX
				sb.append("SELECT T.RID,T.ANNEX_TYPE,T.FILE_NAME,T.FILE_PATH,T.CREATE_DATE,T.CREATE_MANID,NULL AS REG_ZONE_ID,NULL AS UNIT_LEVEL,NULL AS UNIT_NAME FROM TD_ZW_OCCHETH_ANNEX T WHERE T.ORG_ID= ").append(find.getRid());
			}
			List<Object[]> list = sb.length() > 0 ? em.createNativeQuery(sb.toString()).getResultList() : null;
			if(!CollectionUtils.isEmpty(list)){
				List<IZwAnnexZzsb> zwAnnexList = new ArrayList<>();
				for(Object[] objArr : list){
					IZwAnnexZzsb zwAnnex = null;
					if (orgInfoType == 0) {//TD_ZW_TJORGANNEX
						zwAnnex = new TdZwTjorgannexZzsb();
					} else if (orgInfoType == 1) {//TD_ZW_ORGANNEX
						zwAnnex = new TdZwOrgannexZzsb();
					} else if (orgInfoType == 2) {//TD_ZW_DIAGANNEX
						zwAnnex = new TdZwDiagannexZzsb();
					}else {//TD_ZW_OCCHETH_ANNEX
						zwAnnex = new TdZwOcchethAnnexZzsb();
					}
					if(null != zwAnnex){
						String rid = objArr[0] == null ? null : objArr[0].toString();
						String type = objArr[1] == null ? null : objArr[1].toString();
						if(StringUtils.isBlank(rid)){
							continue;
						}
						zwAnnex.setRid(Integer.parseInt(rid));
						if(StringUtils.isNotBlank(type)){
							zwAnnex.setAnnexType(Short.valueOf(type));
						}
						zwAnnex.setFileName(objArr[2] == null ? null : objArr[2].toString());
						zwAnnex.setFilePath(objArr[3] == null ? null : objArr[3].toString());
						zwAnnex.setCreateDate(null == objArr[4] ? null : DateUtils.parseDate(objArr[4]));
						zwAnnex.setCreateManid(null == objArr[5] ? null : Integer.parseInt(objArr[5].toString()));
						zwAnnex.setFkByRegZoneId(null == objArr[6] ? null : new TsZone(Integer.parseInt(objArr[6].toString())));
						zwAnnex.setUnitLevel(null == objArr[7] ? null : Integer.parseInt(objArr[7].toString()));
						zwAnnex.setUnitName(null == objArr[8] ? null : objArr[8].toString());
						zwAnnex.setZwOrginfo(find);
						zwAnnexList.add(zwAnnex);
					}
				}
				find.setZwAnnex(zwAnnexList);
			}


			sb = new StringBuilder();
			//仪器信息查询赋值 注意 仪器信息需要全部查询出来 因为需要用来修改
			if (orgInfoType == 0) {//TD_ZW_TJINST   TD_ZW_INSTINFO
				sb.append("SELECT T.RID,T.INST_ID,T1.INST_NAME,T1.INST_MODEL,T1.CUST_NO,T1.PRDU_NO,T1.LAST_ACPT_DATE,T1.LAST_ACPT_CIRCLE,T1.OUTTER,T1.IF_SUPT_OUT_CHK,T1.CREATE_DATE,T1.CREATE_MANID,T1.NUM,T1.INST_TYPE, T1.INST_KIND_ID,T1.INST_KIND_DETAL_ID,T1.INST_FACTORY,T1.INST_PRO_DATE,T1.WHERE_STORE,T1.INST_CODE,T1.ACPT_DATE,T1.ACPT_CONCLU,T1.IF_NEED_ACPT,T.CREATE_DATE AS TCDATE,T.CREATE_MANID AS TMANID,T1.PURCHASE_DATE,T1.ACPT_REQ_ID,T1.INST_STATE_ID,T1.ORG_ID AS UNITID,T1.LAST_ACPT_CIRCLE_DOT,T1.CHECK_ANNEX_NAME,T2.CODE_NAME AS INSTKINDDETAL,T3.CODE_NAME AS INSTSTATE FROM TD_ZW_TJINST T INNER JOIN TD_ZW_INSTINFO T1 ON T.INST_ID = T1.RID LEFT JOIN TS_SIMPLE_CODE T2 ON T1.INST_KIND_DETAL_ID=T2.RID LEFT JOIN TS_SIMPLE_CODE T3 ON T1.INST_STATE_ID = T3.RID  WHERE T.ORG_ID= ").append(find.getRid());
			} else if (orgInfoType == 1) {//TD_ZW_ORGINST TD_ZW_INSTINFO,T1.LAST_ACPT_CIRCLE_DOT,T1.CHECK_ANNEX_NAME
				//PD里放射仪器情况表 TD_ZW_ORGINST 仪器ID和资质单位ID 外键关联反了 现程序里 将ORG_ID作为仪器ID,T1.LAST_ACPT_CIRCLE_DOT,T1.CHECK_ANNEX_NAME
				sb.append("SELECT T.RID,T.ORG_ID,T1.INST_NAME,T1.INST_MODEL,T1.CUST_NO,T1.PRDU_NO,T1.LAST_ACPT_DATE,T1.LAST_ACPT_CIRCLE,T1.OUTTER,T1.IF_SUPT_OUT_CHK,T1.CREATE_DATE,T1.CREATE_MANID,T1.NUM,T1.INST_TYPE, T1.INST_KIND_ID,T1.INST_KIND_DETAL_ID,T1.INST_FACTORY,T1.INST_PRO_DATE,T1.WHERE_STORE,T1.INST_CODE,T1.ACPT_DATE,T1.ACPT_CONCLU,T1.IF_NEED_ACPT,T.CREATE_DATE AS TCDATE,T.CREATE_MANID AS TMANID,T1.PURCHASE_DATE,T1.ACPT_REQ_ID,T1.INST_STATE_ID,T1.ORG_ID AS UNITID ,T1.LAST_ACPT_CIRCLE_DOT,T1.CHECK_ANNEX_NAME,T2.CODE_NAME AS INSTKINDDETAL,T3.CODE_NAME AS INSTSTATE FROM TD_ZW_ORGINST T INNER JOIN TD_ZW_INSTINFO T1 ON T.ORG_ID = T1.RID LEFT JOIN TS_SIMPLE_CODE T2 ON T1.INST_KIND_DETAL_ID=T2.RID LEFT JOIN TS_SIMPLE_CODE T3 ON T1.INST_STATE_ID = T3.RID  WHERE T.INST_ID= ").append(find.getRid());
			} else if (orgInfoType == 2) {//TD_ZW_DIAGINST TD_ZW_INSTINFO,T1.LAST_ACPT_CIRCLE_DOT,T1.CHECK_ANNEX_NAME
				sb.append("SELECT T.RID,T.INST_ID,T1.INST_NAME,T1.INST_MODEL,T1.CUST_NO,T1.PRDU_NO,T1.LAST_ACPT_DATE,T1.LAST_ACPT_CIRCLE,T1.OUTTER,T1.IF_SUPT_OUT_CHK,T1.CREATE_DATE,T1.CREATE_MANID,T1.NUM,T1.INST_TYPE, T1.INST_KIND_ID,T1.INST_KIND_DETAL_ID,T1.INST_FACTORY,T1.INST_PRO_DATE,T1.WHERE_STORE,T1.INST_CODE,T1.ACPT_DATE,T1.ACPT_CONCLU,T1.IF_NEED_ACPT,T.CREATE_DATE AS TCDATE,T.CREATE_MANID AS TMANID,T1.PURCHASE_DATE,T1.ACPT_REQ_ID,T1.INST_STATE_ID,T1.ORG_ID AS UNITID,T1.LAST_ACPT_CIRCLE_DOT,T1.CHECK_ANNEX_NAME,T2.CODE_NAME AS INSTKINDDETAL,T3.CODE_NAME AS INSTSTATE  FROM TD_ZW_DIAGINST T INNER JOIN TD_ZW_INSTINFO T1 ON T.INST_ID = T1.RID LEFT JOIN TS_SIMPLE_CODE T2 ON T1.INST_KIND_DETAL_ID=T2.RID LEFT JOIN TS_SIMPLE_CODE T3 ON T1.INST_STATE_ID = T3.RID  WHERE T.ORG_ID= ").append(find.getRid());
			}else if(orgInfoType == 3){//TD_ZW_OCCHETH_INST TD_ZW_INSTINFO,T1.LAST_ACPT_CIRCLE_DOT,T1.CHECK_ANNEX_NAME
				sb.append("SELECT T.RID,T.INST_ID,T1.INST_NAME,T1.INST_MODEL,T1.CUST_NO,T1.PRDU_NO,T1.LAST_ACPT_DATE,T1.LAST_ACPT_CIRCLE,T1.OUTTER,T1.IF_SUPT_OUT_CHK,T1.CREATE_DATE,T1.CREATE_MANID,T1.NUM,T1.INST_TYPE, T1.INST_KIND_ID,T1.INST_KIND_DETAL_ID,T1.INST_FACTORY,T1.INST_PRO_DATE,T1.WHERE_STORE,T1.INST_CODE,T1.ACPT_DATE,T1.ACPT_CONCLU,T1.IF_NEED_ACPT,T.CREATE_DATE AS TCDATE,T.CREATE_MANID AS TMANID,T1.PURCHASE_DATE,T1.ACPT_REQ_ID,T1.INST_STATE_ID,T1.ORG_ID AS UNITID,T1.LAST_ACPT_CIRCLE_DOT,T1.CHECK_ANNEX_NAME,T2.CODE_NAME AS INSTKINDDETAL,T3.CODE_NAME AS INSTSTATE FROM TD_ZW_OCCHETH_INST T INNER JOIN TD_ZW_INSTINFO T1 ON T.INST_ID = T1.RID LEFT JOIN TS_SIMPLE_CODE T2 ON T1.INST_KIND_DETAL_ID=T2.RID LEFT JOIN TS_SIMPLE_CODE T3 ON T1.INST_STATE_ID = T3.RID  WHERE T.ORG_ID= ").append(find.getRid());
			}
			List<Object[]> instQueryList = sb.length() > 0 ? em.createNativeQuery(sb.toString()).getResultList() : null;
			List<IZwInstZzsb> zwInstList = null;
			if(!CollectionUtils.isEmpty(instQueryList)){
				List<Integer> orgInstRidList = new ArrayList<>();
				for(Object[] objArr : instQueryList){
					Integer zwInstRid = objArr[0] == null ? null : Integer.parseInt(objArr[0].toString());
					Integer instId = objArr[1] == null ? null : Integer.parseInt(objArr[1].toString());
					if(null == zwInstRid || null == instId){
						continue;
					}
					String instName = objArr[2] == null ? null : objArr[2].toString();
					String instModel = objArr[3] == null ? null : objArr[3].toString();
					String instCustNo = objArr[4] == null ? null : objArr[4].toString();
					String instPrduNo = objArr[5] == null ? null : objArr[5].toString();
					Object instLastAcptDateObj = objArr[6];
					String instLastAcptCirle = objArr[7] == null ? null : objArr[7].toString();
					BigDecimal instLastAcptCirleDot = objArr[29] == null ? null : (BigDecimal)objArr[29];
					String instOutter = objArr[8] == null ? null : objArr[8].toString();
					String instIfSuptOutChk = objArr[9] == null ? null : objArr[9].toString();
					Object createDate = objArr[10];
					Integer createManid = objArr[11] == null ? null : Integer.parseInt(objArr[11].toString());
					Short num = objArr[12] == null ? null : Short.parseShort(objArr[12].toString());
					Integer instType = objArr[13] == null ? null : Integer.parseInt(objArr[13].toString());
					Integer instKindId = objArr[14] == null ? null : Integer.parseInt(objArr[14].toString());
					Integer instKindDetalId = objArr[15] == null ? null : Integer.parseInt(objArr[15].toString());
					String instFactory = objArr[16] == null ? null : objArr[16].toString();
					Object instProDate = objArr[17];
					String whereStore = objArr[18] == null ? null : objArr[18].toString();
					String instCode = objArr[19] == null ? null : objArr[19].toString();
					Object acptDate = objArr[20];
					String acptConclu = objArr[21] == null ? null : objArr[21].toString();
					Integer ifNeedAcpt = objArr[22] == null ? null : Integer.parseInt(objArr[22].toString());

					Object purchaseDate = objArr[25];
					/**+检定要求ID20210315*/
					Integer acptReqId = objArr[26] == null ? null : Integer.parseInt(objArr[26].toString());
					/**+仪器状态ID20210315*/
					Integer instStateId = objArr[27] == null ? null : Integer.parseInt(objArr[27].toString());
					Integer unitId = objArr[28] == null ? null : Integer.parseInt(objArr[28].toString());
					String checkAnnexName = objArr[30] == null ? null : objArr[30].toString();
					String instKindDetal = objArr[31] == null ? null : objArr[31].toString();
					//仪器状态codeName
					String instState = objArr[32] == null ? null : objArr[32].toString();

					TdZwInstinfoZzsb instinfo = new TdZwInstinfoZzsb();
					instinfo.setRid(instId);
					instinfo.setInstName(instName);
					instinfo.setInstModel(instModel);
					instinfo.setCustNo(instCustNo);
					instinfo.setPrduNo(instPrduNo);
					instinfo.setLastAcptDate(instLastAcptDateObj == null ? null : DateUtils.parseDate(instLastAcptDateObj));
					instinfo.setLastAcptCircle(instLastAcptCirle == null ? null : Integer.parseInt(instLastAcptCirle));
					instinfo.setLastAcptCircleDot(instLastAcptCirleDot == null ? null : instLastAcptCirleDot);
					instinfo.setOutter(instOutter == null ? null : Short.parseShort(instOutter));
					instinfo.setIfSuptOutChk(instIfSuptOutChk == null ? null : Integer.parseInt(instIfSuptOutChk));
					instinfo.setCreateDate(null == createDate ? null : DateUtils.parseDate(createDate));
					instinfo.setCreateManid(createManid);
					instinfo.setNum(num);
					instinfo.setInstType(instType);
					instinfo.setFkByInstKindId(null == instKindId ? null : new TsSimpleCode(instKindId));
					instinfo.setFkByInstKindDetalId(null == instKindDetalId ? null : new TsSimpleCode(instKindDetalId,instKindDetal, null));
					instinfo.setInstFactory(instFactory);
					instinfo.setInstProDate(null == instProDate ? null : DateUtils.parseDate(instProDate));
					instinfo.setWhereStore(whereStore);
					instinfo.setInstCode(instCode);
					instinfo.setAcptDate(null == acptDate ? null : DateUtils.parseDate(acptDate));
					instinfo.setAcptConclu(acptConclu);
					instinfo.setIfNeedAcpt(ifNeedAcpt);
					instinfo.setPurchaseDate(null == purchaseDate ? null : DateUtils.parseDate(purchaseDate));
					instinfo.setAcptReqId(null == acptReqId ? null : new TsSimpleCode(acptReqId));
					instinfo.setInstStateId(null == instStateId ? null : new TsSimpleCode(instStateId,instState, null));
					if(null != unitId){
						TsUnit tsUnit =find(TsUnit.class, unitId);
						instinfo.setTsUnit(tsUnit);
					}
					instinfo.setCheckAnnexName(checkAnnexName);
					IZwInstZzsb zwInst = null;
					if (orgInfoType == 0) {
						zwInst = new TdZwTjinstZzsb(zwInstRid);
					} else if (orgInfoType == 1) {
						zwInst = new TdZwOrginstZzsb(zwInstRid);
						orgInstRidList.add(zwInstRid);
					} else if (orgInfoType == 2) {
						zwInst = new TdZwDiaginstZzsb(zwInstRid);
					}else {
						zwInst = new TdZwOcchethInstZzsb(zwInstRid);
					}
					if(null != zwInst){
						Object tCreateDate = objArr[10];
						Integer tCreateManid = objArr[11] == null ? null : Integer.parseInt(objArr[11].toString());
						zwInst.setCreateDate(null == tCreateDate ? null : DateUtils.parseDate(tCreateDate));
						zwInst.setCreateManid(tCreateManid);
						zwInst.setTdZwInstinfo(instinfo);
						zwInst.setZwOrginfo(find);
						if(null == zwInstList){
							zwInstList = new ArrayList<>();
						}
						zwInstList.add(zwInst);
					}
				}
				//放射仪器 还需要放射仪器的场所监测情况与设备检测情况
				if (orgInfoType == 1 && !CollectionUtils.isEmpty(orgInstRidList)){
					List<List<Integer>> orgInstRids = StringUtils.splitListProxy(orgInstRidList, 1000);
					//放射仪器场所监测情况以及放射仪器场所监测情况明细
					List<Object[]> jcMainQuerylist = new ArrayList<>();
					for(List<Integer> instRids : orgInstRids){
						sb = new StringBuilder();
						sb.append(" SELECT T.RID,T.INST_ID,T.JC_DATE,T.JC_RST,T1.RID AS SUBRID,T1.DESCR,T1.RST,T1.RST_UNIT, ");
						sb.append(" T.CREATE_DATE AS MDATE,T.CREATE_MANID AS MMANID,T1.CREATE_DATE AS SUBCDATE,T1.CREATE_MANID AS SUBMANID ");
						sb.append(" FROM TD_ZW_INST_JCMAIN T  ");
						sb.append(" LEFT JOIN TD_ZW_INST_JCSUB T1 ON T.RID = T1.MAIN_ID WHERE T.INST_ID IN (:instRids)");
						Map<String, Object> paramMap = new HashMap<>();
						paramMap.put("instRids", instRids);
						List<Object[]> list1 = this.findDataBySqlNoPage(sb.toString(), paramMap);
						if(!CollectionUtils.isEmpty(list1)){
							jcMainQuerylist.addAll(list1);
						}
					}
					Map<Integer,List<Integer>> instRidWithJcMainRids = new HashMap<>();
					Map<Integer,TdZwInstJcmainZzsb> jcMainRidWithJcMain = new HashMap<>();
					Map<Integer,List<TdZwInstJcsubZzsb>> jcMainRidWithSubs = new HashMap<>();
					if(!CollectionUtils.isEmpty(jcMainQuerylist)){
						for(Object[] objArr : jcMainQuerylist){
							Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
							Integer instId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
							if(null == rid || null == instId){
								continue;
							}
							List<Integer> jcMainRids = instRidWithJcMainRids.get(instId.intValue());
							if(null == jcMainRids){
								jcMainRids = new ArrayList<>();
							}
							if(!jcMainRids.contains(rid.intValue())){
								jcMainRids.add(rid.intValue());
								instRidWithJcMainRids.put(instId.intValue(),jcMainRids);
							}

							TdZwInstJcmainZzsb instJcmain = jcMainRidWithJcMain.get(rid.intValue());
							if(null == instJcmain){
								instJcmain = new TdZwInstJcmainZzsb(rid.intValue());
								Object jcDate = objArr[2];
								String jcRst = null == objArr[3] ? null : objArr[3].toString();
								Object createDate = objArr[8];
								String createManId = null == objArr[9] ? null : objArr[9].toString();
								instJcmain.setJcDate(null == jcDate ? null : DateUtils.parseDate(jcDate));
								instJcmain.setJcRst(jcRst);
								instJcmain.setCreateDate(null == createDate ? null : DateUtils.parseDate(createDate));
								instJcmain.setCreateManid(null == createManId ? null : Integer.parseInt(createManId));
								jcMainRidWithJcMain.put(rid.intValue(),instJcmain);
							}

							Integer subRid = null == objArr[4] ? null : Integer.parseInt(objArr[4].toString());
							if(null == subRid){
								continue;
							}
							List<TdZwInstJcsubZzsb> jcsubList = jcMainRidWithSubs.get(rid.intValue());
							if(null == jcsubList){
								jcsubList = new ArrayList<>();
							}
							String descr = null == objArr[5] ? null : objArr[5].toString();
							String rst = null == objArr[6] ? null : objArr[6].toString();
							String rstUnit = null == objArr[7] ? null : objArr[7].toString();
							Object subCreateDate = objArr[10];
							String subManId = null == objArr[11] ? null : objArr[11].toString();
							TdZwInstJcsubZzsb jcsub = new TdZwInstJcsubZzsb(subRid);
							jcsub.setDescr(descr);
							jcsub.setRst(rst);
							jcsub.setRstUnit(rstUnit);
							jcsub.setCreateDate(null == subCreateDate ? null : DateUtils.parseDate(subCreateDate));
							jcsub.setCreateManid(null == subManId ? null : Integer.parseInt(subManId));
							jcsubList.add(jcsub);
							jcMainRidWithSubs.put(rid.intValue(), jcsubList);
						}
					}
					//放射仪器设备检测情况以及检测情况明细
					List<Object[]> sbjcMainQuerylist = new ArrayList<>();
					for(List<Integer> instRids : orgInstRids){
						sb = new StringBuilder();
						sb.append(" SELECT T.RID,T.INST_ID,T.JC_DATE,T.JC_RST,T1.RID AS SUBRID,T1.DESCR,T1.RST,T1.RST_UNIT,  ");
						sb.append(" T.CREATE_DATE AS MCREDATE,T.CREATE_MANID AS MMANID,T1.CREATE_DATE AS SUBCREDATE,T1.CREATE_MANID AS SUBMANID ");
						sb.append(" FROM TD_ZW_INST_SBJCMAIN T ");
						sb.append(" LEFT JOIN TD_ZW_INST_SBJCSUB T1 ON T.RID = T1.MAIN_ID WHERE T.INST_ID IN (:instRids)");
						Map<String, Object> paramMap = new HashMap<>();
						paramMap.put("instRids", instRids);
						List<Object[]> list1 = this.findDataBySqlNoPage(sb.toString(), paramMap);
						if(!CollectionUtils.isEmpty(list1)){
							sbjcMainQuerylist.addAll(list1);
						}
					}
					Map<Integer,List<Integer>> instRidWithsbJcMainRids = new HashMap<>();
					Map<Integer,TdZwInstSbjcmainZzsb> sbjcMainRidWithJcMain = new HashMap<>();
					Map<Integer,List<TdZwInstSbjcsubZzsb>> sbjcMainRidWithSubs = new HashMap<>();
					if(!CollectionUtils.isEmpty(sbjcMainQuerylist)){
						for(Object[] objArr : sbjcMainQuerylist){
							Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
							Integer instId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
							if(null == rid || null == instId){
								continue;
							}
							List<Integer> jcMainRids = instRidWithsbJcMainRids.get(instId.intValue());
							if(null == jcMainRids){
								jcMainRids = new ArrayList<>();
							}
							if(!jcMainRids.contains(rid.intValue())){
								jcMainRids.add(rid.intValue());
								instRidWithsbJcMainRids.put(instId.intValue(),jcMainRids);
							}

							TdZwInstSbjcmainZzsb instJcmain = sbjcMainRidWithJcMain.get(rid.intValue());
							if(null == instJcmain){
								instJcmain = new TdZwInstSbjcmainZzsb(rid.intValue());
								Object jcDate = objArr[2];
								String jcRst = null == objArr[3] ? null : objArr[3].toString();
								Object createDate = objArr[8];
								String createMainId = null == objArr[9] ? null : objArr[9].toString();
								instJcmain.setJcDate(null == jcDate ? null : DateUtils.parseDate(jcDate));
								instJcmain.setJcRst(jcRst);
								instJcmain.setCreateDate(null == createDate ? null : DateUtils.parseDate(createDate));
								instJcmain.setCreateManid(null == createMainId ? null : Integer.parseInt(createMainId));
								sbjcMainRidWithJcMain.put(rid.intValue(),instJcmain);
							}

							Integer subRid = null == objArr[4] ? null : Integer.parseInt(objArr[4].toString());
							if(null == subRid){
								continue;
							}
							List<TdZwInstSbjcsubZzsb> jcsubList = sbjcMainRidWithSubs.get(rid.intValue());
							if(null == jcsubList){
								jcsubList = new ArrayList<>();
							}
							String descr = null == objArr[5] ? null : objArr[5].toString();
							String rst = null == objArr[6] ? null : objArr[6].toString();
							String rstUnit = null == objArr[7] ? null : objArr[7].toString();
							Object subCreateDate = objArr[10];
							String subCreateMainId = null == objArr[11] ? null : objArr[11].toString();
							TdZwInstSbjcsubZzsb jcsub = new TdZwInstSbjcsubZzsb(subRid);
							jcsub.setDescr(descr);
							jcsub.setRst(rst);
							jcsub.setRstUnit(rstUnit);
							jcsub.setCreateDate(null == subCreateDate ? null : DateUtils.parseDate(subCreateDate));
							jcsub.setCreateManid(null == subCreateMainId ? null : Integer.parseInt(subCreateMainId));
							jcsubList.add(jcsub);
							sbjcMainRidWithSubs.put(rid.intValue(), jcsubList);
						}
					}
					if(null != zwInstList){
						if(!CollectionUtils.isEmpty(instRidWithJcMainRids) || !CollectionUtils.isEmpty(instRidWithsbJcMainRids)){
							for(IZwInstZzsb zwInst : zwInstList){
								if(null == zwInst.getTdZwInstinfo() || null == zwInst.getTdZwInstinfo().getRid()){
									continue;
								}
								if(!CollectionUtils.isEmpty(instRidWithJcMainRids)){
									List<Integer> jcMainRids = instRidWithJcMainRids.get(zwInst.getTdZwInstinfo().getRid());
									if(!CollectionUtils.isEmpty(jcMainRids)){
										List<TdZwInstJcmainZzsb> jcmainList = new ArrayList<>();
										for(Integer mainRid : jcMainRids){
											TdZwInstJcmainZzsb jcmain = jcMainRidWithJcMain.get(mainRid);
											if(null == jcmain){
												continue;
											}
											jcmain.setFkByInstId(zwInst.getTdZwInstinfo());
											List<TdZwInstJcsubZzsb> jcsubList = jcMainRidWithSubs.get(mainRid);
											if(!CollectionUtils.isEmpty(jcsubList)){
												for(TdZwInstJcsubZzsb jcsub : jcsubList){
													jcsub.setFkByMainId(jcmain);
												}
											}
											jcmain.setSubList(jcsubList);
											jcmainList.add(jcmain);
										}
										zwInst.getTdZwInstinfo().setInstJcMainList(jcmainList);
									}
								}
								if(!CollectionUtils.isEmpty(instRidWithsbJcMainRids)){
									List<Integer> sbjcMainRids = instRidWithsbJcMainRids.get(zwInst.getTdZwInstinfo().getRid());
									if(!CollectionUtils.isEmpty(sbjcMainRids)){
										List<TdZwInstSbjcmainZzsb> sbjcmainList = new ArrayList<>();
										for(Integer mainRid : sbjcMainRids){
											TdZwInstSbjcmainZzsb jcmain = sbjcMainRidWithJcMain.get(mainRid);
											if(null == jcmain){
												continue;
											}
											jcmain.setFkByInstId(zwInst.getTdZwInstinfo());
											List<TdZwInstSbjcsubZzsb> jcsubList = sbjcMainRidWithSubs.get(mainRid);
											if(!CollectionUtils.isEmpty(jcsubList)){
												for(TdZwInstSbjcsubZzsb jcsub : jcsubList){
													jcsub.setFkByMainId(jcmain);
												}
											}
											jcmain.setSubList(jcsubList);
											sbjcmainList.add(jcmain);
										}
										zwInst.getTdZwInstinfo().setInstSbjcmainList(sbjcmainList);
									}
								}
							}
						}
					}
				}
				if(null != zwInstList){
					find.setZwInst(zwInstList);
				}
			}
			sb = new StringBuilder();
			//人员信息查询赋值
			if (orgInfoType == 0) {
				sb.append("SELECT T1.EMP_NAME,T1.BIRTHDAY,T1.SEX,T1.TITLE_ID,T1.POSITION,T1.PSN_TYPE,T.RID,T.EMP_ID,T.CERT_NO,T.ON_DUTY,NULL AS JL_CODE,NULL AS LJ_JL,T1.IF_CHECK_EXPERT ")
						.append(" FROM TD_ZW_TJORGPSNS T  INNER JOIN TD_ZW_PSNINFO T1 ON T.EMP_ID = T1.RID ").append(" WHERE T.ORG_ID = ").append(find.getRid());
			} else if (orgInfoType == 1) {
				sb.append("SELECT T1.EMP_NAME,T1.BIRTHDAY,T1.SEX,T1.TITLE_ID,T1.POSITION,T1.PSN_TYPE,T.RID,T.EMP_ID,T.CERT_NO,T.ON_DUTY,T.JL_CODE,T.LJ_JL,T1.IF_CHECK_EXPERT ")
						.append(" FROM TD_ZW_SRVORGPSNS T INNER JOIN TD_ZW_PSNINFO T1 ON T.EMP_ID = T1.RID ").append(" WHERE T.ORG_ID = ").append(find.getRid());
			} else if (orgInfoType == 2) {
				sb.append("SELECT T1.EMP_NAME,T1.BIRTHDAY,T1.SEX,T1.TITLE_ID,T1.POSITION,T1.PSN_TYPE,T.RID,T.EMP_ID,T.CERT_NO,T.ON_DUTY,NULL AS JL_CODE,NULL AS LJ_JL,T1.IF_CHECK_EXPERT ")
						.append(" FROM TD_ZW_DIAGPSNS T INNER JOIN TD_ZW_PSNINFO T1 ON T.EMP_ID = T1.RID ").append(" WHERE T.ORG_ID = ").append(find.getRid());
			}else if (orgInfoType == 3){
				sb.append("SELECT T1.EMP_NAME,T1.BIRTHDAY,T1.SEX,T1.TITLE_ID,T1.POSITION,T1.PSN_TYPE,T.RID,T.EMP_ID,T.CERT_NO,T.ON_DUTY,NULL AS JL_CODE,NULL AS LJ_JL,T1.IF_CHECK_EXPERT ")
						.append(" FROM TD_ZW_OCCHETH_PSNS T INNER JOIN TD_ZW_PSNINFO T1 ON T.EMP_ID = T1.RID ").append(" WHERE T.ORG_ID = ").append(find.getRid());
			}else if (orgInfoType == 4){
				sb.append("SELECT T1.EMP_NAME,T1.BIRTHDAY,T1.SEX,T1.TITLE_ID,T1.POSITION,T1.PSN_TYPE,T.RID,T.EMP_ID,NULL as CERT_NO,T.ON_DUTY,NULL AS JL_CODE,NULL AS LJ_JL,T1.IF_CHECK_EXPERT ")
						.append(" FROM TD_ZW_SUPPORT_PSNS T INNER JOIN TD_ZW_PSNINFO T1 ON T.EMP_ID = T1.RID ").append(" WHERE T.ORG_ID = ").append(find.getRid());
			}
			List<Object[]> orgPsnObjs = em.createNativeQuery(sb.toString()).getResultList();
			if(!CollectionUtils.isEmpty(orgPsnObjs)){
				List<IZwOrgPsnsZzsb> zwOrgPsnsList = new ArrayList<>();
				// 职称 码表2003  避免每次都查询
				sb = new StringBuilder();
				sb.append(" SELECT T.RID,T.CODE_NAME FROM TS_SIMPLE_CODE T ")
						.append(" INNER JOIN TS_CODE_TYPE T1 ON T.CODE_TYPE_ID = T1.RID ")
						.append(" WHERE T1.CODE_TYPE_NAME='2003' ");
				List<Object[]> jobLevelList = em.createNativeQuery(sb.toString()).getResultList();
				Map<Integer,String> jobLevelMap = new HashMap<>();
				if(!CollectionUtils.isEmpty(jobLevelList)){
					for(Object[] objArr : jobLevelList){
						Integer levelRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
						if(null != levelRid){
							jobLevelMap.put(levelRid,(null == objArr[1] ? null : objArr[1].toString()));
						}
					}
				}

				List<Integer> orgPsnRids = new ArrayList<>();
				List<Integer> psnInfoRids = new ArrayList<>();
				for(Object[] objArr : orgPsnObjs){
					Integer rid = null == objArr[6] ? null : Integer.parseInt(objArr[6].toString());
					Integer empId = null == objArr[7] ? null : Integer.parseInt(objArr[7].toString());
					if(null == rid || null == empId){
						continue;
					}
					orgPsnRids.add(rid);
					psnInfoRids.add(empId);
				}

				Map<Integer,List<TdZwWorkdeptZzsb>> orgPsnRidsWithWorkDeptMap = new HashMap<>();//key TD_ZW_TJORGPSNS rid
				Map<Integer,List<TdZwPsnitemsZzsb>> orgPsnRidsWithPsnItemsMap = new HashMap<>();//key TD_ZW_TJORGPSNS rid
				if (orgInfoType == 0){
					if(!CollectionUtils.isEmpty(orgPsnRids)){
						List<List<Integer>> orgPsnRidList = StringUtils.splitListProxy(orgPsnRids, 1000);
						// 从事科室查询
						List<Object[]> deptObjArrList = new ArrayList<>();
						for(List<Integer> orgPsnRid : orgPsnRidList){
							sb = new StringBuilder();
							sb.append(" SELECT T.RID,T.EMP_ID,T.DEPT_ID,T2.CODE_NAME,T2.CODE_NO,  ")
									.append(" T.CREATE_DATE AS DEPTCREATEDATE,T.CREATE_MANID AS DEPTMANID ")
									.append(" FROM TD_ZW_WORKDEPT T ")
									.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.DEPT_ID = T2.RID ")
									.append(" WHERE T.EMP_ID IN (:orgPsnRid)")
									.append(" ORDER BY T2.CODE_NO ");
							Map<String, Object> paramMap = new HashMap<>();
							paramMap.put("orgPsnRid", orgPsnRid);
							List<Object[]> list1 = this.findDataBySqlNoPage(sb.toString(), paramMap);
							if(!CollectionUtils.isEmpty(list1)){
								deptObjArrList.addAll(list1);
							}
						}
						if(!CollectionUtils.isEmpty(deptObjArrList)){
							for(Object[] objArr : deptObjArrList){
								Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
								Integer empId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
								Integer deptId = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
								if(null == rid || null == empId || null == deptId){
									continue;
								}
								List<TdZwWorkdeptZzsb> deptList = orgPsnRidsWithWorkDeptMap.get(empId.intValue());
								if(null == deptList){
									deptList = new ArrayList<>();
								}
								String codeName = null == objArr[3] ? null : objArr[3].toString();
								String codeNo = null == objArr[4] ? null : objArr[4].toString();
								TsSimpleCode deptSimpleCode = new TsSimpleCode(deptId);
								deptSimpleCode.setCodeName(codeName);
								deptSimpleCode.setCodeNo(codeNo);
								TdZwWorkdeptZzsb workdept = new TdZwWorkdeptZzsb(rid);
								workdept.setTsSimpleCode(deptSimpleCode);

								Object createDate = objArr[5];
								String createMainId = null == objArr[6] ? null : objArr[6].toString();
								workdept.setCreateDate(null == createDate ? null : DateUtils.parseDate(createDate));
								workdept.setCreateManid(null == createMainId ? null : Integer.parseInt(createMainId));

								deptList.add(workdept);
								orgPsnRidsWithWorkDeptMap.put(empId.intValue(), deptList);
							}
						}

						// 从事项目查询
						List<Object[]> itemsObjArrList = new ArrayList<>();
						for(List<Integer> orgPsnRid : orgPsnRidList){
							sb = new StringBuilder();
							sb.append(" SELECT T.RID,T.EMP_ID,T.ITEM_CODE FROM TD_ZW_PSNITEMS T ")
									.append(" WHERE T.EMP_ID IN (:orgPsnRid)");
							Map<String, Object> paramMap = new HashMap<>();
							paramMap.put("orgPsnRid", orgPsnRid);
							List<Object[]> list1 = this.findDataBySqlNoPage(sb.toString(), paramMap);
							if(!CollectionUtils.isEmpty(list1)){
								itemsObjArrList.addAll(list1);
							}
						}
						if(!CollectionUtils.isEmpty(itemsObjArrList)){
							for(Object[] objArr : itemsObjArrList){
								Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
								Integer empId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
								if(null == rid || null == empId){
									continue;
								}
								Short itemCode = null == objArr[2] ? null : Short.parseShort(objArr[2].toString());
								TdZwPsnitemsZzsb psnitems = new TdZwPsnitemsZzsb(rid);
								psnitems.setItemCode(itemCode);
								List<TdZwPsnitemsZzsb> psnitemsList = orgPsnRidsWithPsnItemsMap.get(empId.intValue());
								if(null == psnitemsList){
									psnitemsList = new ArrayList<>();
								}
								psnitemsList.add(psnitems);
								orgPsnRidsWithPsnItemsMap.put(empId.intValue(), psnitemsList);
							}
						}
					}
				}


				Map<Integer,List<TdZwPsntjMainZzsb>> psnInfoRidWithTjMainMap = new HashMap<>();// key TdZwPsninfo rid
				Map<Integer, List<TdZwPsnZzlbZzsb>> psnInfoRidWithZzlbMap = new HashMap<>();//key TdZwPsninfo rid
				if(!CollectionUtils.isEmpty(psnInfoRids)){
					List<List<Integer>> psnInfoRidList = StringUtils.splitListProxy(psnInfoRids, 1000);
					if(orgInfoType == 1){// 这里排除其他机构 只保留放射技术服务机构
						// 放射技术服务机构人员体检信息查询以及放射技术服务机构人员体检明细
						List<Object[]> objArrList = new ArrayList<>();
						for(List<Integer> psnInfoRid : psnInfoRidList){
							sb = new StringBuilder();
							sb.append(" SELECT T.RID,T.EMP_ID,T.TJ_DATE,T.TJ_CONCL,T.TJ_TYPE, ")
									.append(" T1.RID AS SUBRID,T1.DESCR,T1.RST,T1.RST_UNIT FROM TD_ZW_PSNTJ_MAIN T ")
									.append(" LEFT JOIN TD_ZW_PSNTJ_SUB T1 ON T1.MAIN_ID = T.RID ")
									.append(" WHERE T.EMP_ID IN (:psnInfoRid)");
							Map<String, Object> paramMap = new HashMap<>();
							paramMap.put("psnInfoRid", psnInfoRid);
							List<Object[]> list1 = this.findDataBySqlNoPage(sb.toString(), paramMap);
							if(!CollectionUtils.isEmpty(list1)){
								objArrList.addAll(list1);
							}
						}
						Map<Integer,List<Integer>> psnInfoRidWithTjMainRidsMap = new HashMap<>(); // key TdZwPsninfo rid
						Map<Integer,TdZwPsntjMainZzsb> tjMainRidWithEntityMap = new HashMap<>(); // key TdZwPsntjMain rid
						for(Object[] objArr : objArrList){
							Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
							Integer empId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
							Integer subId = null == objArr[5] ? null : Integer.parseInt(objArr[5].toString());
							if(null == rid || null == empId){
								continue;
							}
							List<Integer> tjMainRids = psnInfoRidWithTjMainRidsMap.get(empId.intValue());
							if(null == tjMainRids){
								tjMainRids = new ArrayList<>();
							}
							if(!tjMainRids.contains(rid.intValue())){
								tjMainRids.add(rid.intValue());
								psnInfoRidWithTjMainRidsMap.put(empId.intValue(), tjMainRids);
							}

							TdZwPsntjMainZzsb psntjMain = tjMainRidWithEntityMap.get(rid.intValue());
							if(null == psntjMain){
								psntjMain = new TdZwPsntjMainZzsb(rid);
								Date tjDate = null == objArr[2] ? null : DateUtils.parseDate(objArr[2].toString());
								String tjConcl = null == objArr[3] ? null : objArr[3].toString();
								Integer tjType = null == objArr[4] ? null : Integer.parseInt(objArr[4].toString());
								psntjMain.setTjDate(tjDate);
								psntjMain.setTjConcl(tjConcl);
								psntjMain.setTjType(tjType);
							}
							if(null != subId){
								List<TdZwPsntjSubZzsb> psntjSubList = psntjMain.getSubList();
								if(null == psntjSubList){
									psntjSubList = new ArrayList<>();
								}
								TdZwPsntjSubZzsb psntjSub = new TdZwPsntjSubZzsb(subId);
								String desc = null == objArr[6] ? null : objArr[6].toString();
								String rst = null == objArr[7] ? null : objArr[7].toString();
								String rstUnit = null == objArr[8] ? null : objArr[8].toString();
								psntjSub.setDescr(desc);
								psntjSub.setRst(rst);
								psntjSub.setRstUnit(rstUnit);
								psntjSubList.add(psntjSub);
							}
							tjMainRidWithEntityMap.put(rid.intValue(), psntjMain);
						}
						if(!CollectionUtils.isEmpty(psnInfoRidWithTjMainRidsMap)){
							for(Map.Entry<Integer,List<Integer>> mapEntity : psnInfoRidWithTjMainRidsMap.entrySet()){
								List<Integer> tjMainRids = mapEntity.getValue();
								if(!CollectionUtils.isEmpty(tjMainRids)){
									List<TdZwPsntjMainZzsb> tmpPsntjMainList = new ArrayList<>();
									for(Integer tjMainRid : tjMainRids){
										if(null == tjMainRid){
											continue;
										}
										TdZwPsntjMainZzsb tmpTjMain = tjMainRidWithEntityMap.get(tjMainRid);
										if(null != tmpTjMain){
											tmpPsntjMainList.add(tmpTjMain);
										}
									}
									if(!CollectionUtils.isEmpty(tmpPsntjMainList)){
										psnInfoRidWithTjMainMap.put(mapEntity.getKey(), tmpPsntjMainList);
									}
								}
							}
						}
					}
					//  人员资质类别查询
					List<Object[]> zzlbObjArrList = new ArrayList<>();
					for(List<Integer> psnInfoRid : psnInfoRidList){
						sb = new StringBuilder();
						sb.append(" SELECT T.RID,T.PSN_ID,T.QUAL_ID,T1.CODE_NAME,T.CENT_NO,T.SEND_DATE,T.VALID_DATE, ")
								.append(" T.AGEIN_SEND_DATE,T.AGEIN_VALID_DATE,T.ANNEX_PATH,T.ANNEX_NAME,T.ZZ_FETCH_WAY,T.CREATE_DATE AS CREDATE,T.CREATE_MANID AS CREMANID,T1.CODE_DESC  ")
								.append(" FROM TD_ZW_PSN_ZZLB T ").append(" INNER JOIN TS_SIMPLE_CODE T1 ON T.QUAL_ID=T1.RID ")
								.append(" WHERE T.PSN_ID IN (:psnInfoRid)").append(" ORDER BY T.PSN_ID, T.SEND_DATE DESC ");;
						Map<String, Object> paramMap = new HashMap<>();
						paramMap.put("psnInfoRid", psnInfoRid);
						List<Object[]> list1 = this.findDataBySqlNoPage(sb.toString(), paramMap);
						if(!CollectionUtils.isEmpty(list1)){
							zzlbObjArrList.addAll(list1);
						}
					}
					if(!CollectionUtils.isEmpty(zzlbObjArrList)){
						for(Object[] objArr : zzlbObjArrList){
							Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
							Integer empId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
							Integer qualId = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
							if(null == rid || null == empId || null == qualId){
								continue;
							}
							TsSimpleCode qual = new TsSimpleCode(qualId);
							String qualName = null == objArr[3] ? null : objArr[3].toString();
							String codeDesc = null == objArr[14] ? null : objArr[14].toString();
							qual.setCodeName(qualName);
                            qual.setCodeDesc(codeDesc);
							List<TdZwPsnZzlbZzsb> zzlbList = psnInfoRidWithZzlbMap.get(empId.intValue());
							if(null == zzlbList){
								zzlbList = new ArrayList<>();
							}
							String centNo = null == objArr[4] ? null : objArr[4].toString();
							Date sendDate = null == objArr[5] ? null : DateUtils.parseDate(objArr[5]);
							Date validDate = null == objArr[6] ? null : DateUtils.parseDate(objArr[6]);
							Date ageinSendDate = null == objArr[7] ? null : DateUtils.parseDate(objArr[7]);
							Date ageinValidDate = null == objArr[8] ? null : DateUtils.parseDate(objArr[8]);
							String annexPath = null == objArr[9] ? null : objArr[9].toString();
							String annexName = null == objArr[10] ? null : objArr[10].toString();
							Integer zzFetchWay = null == objArr[11] ? null : Integer.parseInt(objArr[11].toString());
							Date creDate = null == objArr[12] ? null : DateUtils.parseDate(objArr[12]);
							Integer creManId = null == objArr[13] ? null : Integer.parseInt(objArr[13].toString());
							TdZwPsnZzlbZzsb zzlb = new TdZwPsnZzlbZzsb(rid);
							zzlb.setFkByQualId(qual);
							zzlb.setCentNo(centNo);
							zzlb.setSendDate(sendDate);
							zzlb.setValidDate(validDate);
							zzlb.setAgeinSendDate(ageinSendDate);
							zzlb.setAgeinValidDate(ageinValidDate);
							zzlb.setAnnexPath(annexPath);
							zzlb.setAnnexName(annexName);
							zzlb.setZzFetchWay(zzFetchWay);
							zzlb.setCreateDate(creDate);
							zzlb.setCreateManid(creManId);
							zzlbList.add(zzlb);
							psnInfoRidWithZzlbMap.put(empId.intValue(), zzlbList);
						}
					}
				}

				for(Object[] objArr : orgPsnObjs){
					Integer rid = null == objArr[6] ? null : Integer.parseInt(objArr[6].toString());
					Integer empId = null == objArr[7] ? null : Integer.parseInt(objArr[7].toString());
					if(null == rid || null == empId){
						continue;
					}
					String empName = null == objArr[0] ? null : objArr[0].toString();
					String birthDay = null == objArr[1] ? null : objArr[1].toString();
					String sex = null == objArr[2] ? null : objArr[2].toString();
					Integer titleId = null == objArr[3] ? null : Integer.parseInt(objArr[3].toString());
					TsSimpleCode titleSimple = null;
					if(null != titleId){
						String titleName = jobLevelMap.get(titleId);
						if(null != titleName){
							titleSimple = new TsSimpleCode(titleId);
							titleSimple.setCodeName(titleName);
						}
					}
					String position = null == objArr[4] ? null : objArr[4].toString();
					Integer psnType = null == objArr[5] ? null : Integer.parseInt(objArr[5].toString());
					TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb(empId);
					psninfo.setEmpName(empName);
					psninfo.setBirthday(birthDay);
					psninfo.setSex(sex);
					psninfo.setFkByTitleId(titleSimple);
					psninfo.setPosition(position);
					psninfo.setPsnType(psnType);
					psninfo.setIfCheckExpert(StringUtils.objectToString(objArr[12]));
					if(orgInfoType == 1){
						//体检信息赋值
						List<TdZwPsntjMainZzsb> tjMainList = psnInfoRidWithTjMainMap.get(psninfo.getRid().intValue());
						if(!CollectionUtils.isEmpty(tjMainList)){
							for(TdZwPsntjMainZzsb psntjMain : tjMainList){
								psntjMain.setFkByEmpId(psninfo);
							}
						}
						psninfo.setTjMainList(tjMainList);
					}
					//人员资质类别赋值
					List<TdZwPsnZzlbZzsb> zzlbList = psnInfoRidWithZzlbMap.get(psninfo.getRid().intValue());
					if(!CollectionUtils.isEmpty(zzlbList)){
						for(TdZwPsnZzlbZzsb psnZzlb : zzlbList){
							psnZzlb.setFkByPsnId(psninfo);
						}
					}
					psninfo.setZzlbList(zzlbList);

					IZwOrgPsnsZzsb zwOrgPsn = null;
					if (orgInfoType == 0) {
						zwOrgPsn = new TdZwTjorgpsnsZzsb(rid);
					} else if (orgInfoType == 1) {
						zwOrgPsn = new TdZwSrvorgpsnsZzsb(rid);
					} else if (orgInfoType == 2) {
						zwOrgPsn = new TdZwDiagpsnsZzsb(rid);
					}else if(orgInfoType == 3){
						zwOrgPsn = new TdZwOcchethPsnsZzsb(rid);
					}else if(orgInfoType == 4){
						zwOrgPsn = new TdZwSupportPsnsZzsb(rid);
					}
					if(null != zwOrgPsn){
						String certNo = null == objArr[8] ? null : objArr[8].toString();
						String onDuty = null == objArr[9] ? null : objArr[9].toString();
						String jlCode = null == objArr[10] ? null : objArr[10].toString();
						String ljjl = null == objArr[11] ? null : objArr[11].toString();
						zwOrgPsn.setZwOrginfo(find);
						zwOrgPsn.setTdZwPsninfo(psninfo);
						zwOrgPsn.setCertNo(certNo);
						zwOrgPsn.setOnDuty(onDuty);
						zwOrgPsn.setJlCode(jlCode);
						zwOrgPsn.setLjJl(ljjl);
						if (orgInfoType == 0){
							//从事科室赋值
							List<TdZwWorkdeptZzsb> tdZwWorkdepts = orgPsnRidsWithWorkDeptMap.get(zwOrgPsn.getRid().intValue());
							if(!CollectionUtils.isEmpty(tdZwWorkdepts)){
								StringBuilder name = new StringBuilder();
								for (TdZwWorkdeptZzsb dept : tdZwWorkdepts) {
									name.append("，").append(dept.getTsSimpleCode().getCodeName());
									dept.setTdZwTjorgpsns((TdZwTjorgpsnsZzsb)zwOrgPsn);
								}
								zwOrgPsn.setTdZwWorkdepts(tdZwWorkdepts);
								//WordOfficeName 赋值
								zwOrgPsn.setWordOfficeName(name.substring(1));
							}

							//从事项目赋值
							List<TdZwPsnitemsZzsb> tdZwPsnitemses = orgPsnRidsWithPsnItemsMap.get(zwOrgPsn.getRid().intValue());
							if(!CollectionUtils.isEmpty(tdZwPsnitemses)) {
								StringBuilder name = new StringBuilder();
								StringBuilder nameStr = new StringBuilder();
								for(TdZwPsnitemsZzsb items : tdZwPsnitemses)	{
									name.append(",").append(items.getItemCode().toString());
									nameStr.append(",").append(tempMap.get(items.getItemCode().toString()));
									items.setTdZwTjorgpsns((TdZwTjorgpsnsZzsb)zwOrgPsn);
								}
								zwOrgPsn.setTdZwPsnitemses(tdZwPsnitemses);
								//WordItems 以及 WordItemStr赋值
								zwOrgPsn.setWordItems(name.substring(1));
								zwOrgPsn.setWordItemStr(nameStr.substring(1));
							}
						}
						zwOrgPsnsList.add(zwOrgPsn);
					}
				}

				find.setZwOrgPsns(zwOrgPsnsList);
			}


			sb = new StringBuilder();

			if(orgInfoType == 1||orgInfoType == 0){
				if(orgInfoType == 1){
					sb.append("select T from TdZwSrvorgitemsZzsb T where T.tdZwSrvorginfo.rid=").append(find.getRid().intValue());
				}else{
					sb.append("select T from TdZwTjorggitemsZzsb T where T.tdZwTjorginfo.rid=").append(find.getRid().intValue());
				}
				List<IZwSvritmZzsb> zwSvrItmObjList = em.createQuery(sb.toString()).getResultList();
				if(!CollectionUtils.isEmpty(zwSvrItmObjList) ) {
					for (IZwSvritmZzsb svritm : zwSvrItmObjList) {
						List<IZwSvritmSubZzsb> svritmSubs = svritm.getZwSvritmSubList();
						if (null != svritmSubs) {
							svritm.getZwSvritmSubList().size();
						}
					}
					find.setZwSvritm(zwSvrItmObjList);
				}
			}else{
				if (orgInfoType == 2) {
					sb.append(" SELECT T.RID,T.ITEM_CODE,T.CREATE_DATE,T.CREATE_MANID FROM TD_ZW_DIAGITEMS T WHERE T.ORG_ID= ").append(find.getRid().intValue());
				} else if(orgInfoType == 3) {
					sb.append(" SELECT T.RID,T.ITEM_CODE,T.CREATE_DATE,T.CREATE_MANID FROM TD_ZW_OCCHETH_ITEMS T WHERE T.ORG_ID= ").append(find.getRid().intValue());
				}
				//服务项目查询赋值
				List<Object[]> zwSvrItmObjList = sb.length() > 0 ? em.createNativeQuery(sb.toString()).getResultList() : null;
				if(!CollectionUtils.isEmpty(zwSvrItmObjList)){
					List<IZwSvritmZzsb> zwSvrItmList = new ArrayList<>();
					for(Object[] objArr : zwSvrItmObjList){
						Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
						if(null == rid){
							continue;
						}
						Short itemCode = null == objArr[1] ? null : Short.parseShort(objArr[1].toString());
						IZwSvritmZzsb zwSvritm = null;
						if (orgInfoType == 0) {
							zwSvritm = new TdZwTjorggitemsZzsb(rid);
						} else if (orgInfoType == 1) {
							zwSvritm = new TdZwSrvorgitemsZzsb(rid);
						} else if (orgInfoType == 2) {
							zwSvritm = new TdZwDiagitemsZzsb(rid);
						}else {
							zwSvritm = new TdZwOcchethItemsZzsb(rid);
						}
						if(null != zwSvritm){
							Date createDate = null == objArr[2] ? null : DateUtils.parseDate(objArr[2]);
							Integer createManId = null == objArr[3] ? null : Integer.parseInt(objArr[3].toString());
							zwSvritm.setCreateDate(createDate);
							zwSvritm.setCreateManid(createManId);
							zwSvritm.setItemCode(itemCode);
							zwSvritm.setZwOrginfo(find);
							zwSvrItmList.add(zwSvritm);

//						if (null != find.getZwSvritm()) {
//							List<IZwSvritm> svritms = find.getZwSvritm();
//							if (null!=svritms && svritms.size()>0) {
//								for (IZwSvritm svritm : svritms) {
//									List<IZwSvritmSub> svritmSubs = svritm.getZwSvritmSubList();
//									if (null!=svritmSubs) {
//										svritm.getZwSvritmSubList().size();
//									}
//								}
//							}
//						}
						}
					}
					find.setZwSvritm(zwSvrItmList);
				}
			}




			if (orgInfoType == 0){
				sb = new StringBuilder();
				sb.append(" SELECT T.RID,T.ITEM_ID,T.IS_OUTTER,T1.CODE_NAME,T.CREATE_DATE,T.CREATE_MANID FROM TD_ZW_TJITEMS T ")
						.append(" INNER JOIN TS_SIMPLE_CODE T1 ON T.ITEM_ID = T1.RID ")
						.append(" WHERE T.ORG_ID = ").append(find.getRid().intValue());
				// 特殊处理健康检查机构的检验项目查询赋值
				List<Object[]> zwItmObjList = em.createNativeQuery(sb.toString()).getResultList();
				if(!CollectionUtils.isEmpty(zwItmObjList)){
					List<IZwItemsZzsb> zwItemList = new ArrayList<>();
					StringBuilder itemIds = new StringBuilder("");
					for(Object[] objArr : zwItmObjList){
						Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
						Integer itemId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
						if(null == rid || null == itemId){
							continue;
						}
						Integer outter = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
						String codeName = null == objArr[3] ? null : objArr[3].toString();
						TsSimpleCode itemSimple = new TsSimpleCode(itemId);
						itemSimple.setCodeName(codeName);

						Date createDate = null == objArr[4] ? null : DateUtils.parseDate(objArr[4]);
						Integer createManId = null == objArr[5] ? null : Integer.parseInt(objArr[5].toString());
						TdZwTjitemsZzsb tjitem = new TdZwTjitemsZzsb(rid);
						tjitem.setCreateDate(createDate);
						tjitem.setCreateManid(createManId);
						tjitem.setIsOutter(outter);
						tjitem.setTsSimpleCode(itemSimple);
						tjitem.setTdZwTjorginfo((TdZwTjorginfoZzsb)find);
						zwItemList.add(tjitem);
						itemIds.append(",").append(itemId);
					}
					String itemIdsStr = itemIds.toString();
					find.setJcItems(StringUtils.isNotBlank(itemIdsStr) ? itemIdsStr.substring(1) : null);
					find.setZwItems(zwItemList);
				}

				sb = new StringBuilder();
				sb.append(" SELECT T.RID,T.ZONE_ID,T1.ZONE_NAME,T1.ZONE_GB,T1.ZONE_TYPE,T1.ZONE_CODE,T.CREATE_DATE,T.CREATE_MANID ")
						.append(" FROM TD_ZW_TJORG_OUT_RANGE T ")
						.append(" INNER JOIN TS_ZONE T1 ON T.ZONE_ID = T1.RID WHERE T.ORG_ID= ")
						.append(find.getRid().intValue());
				//外出开展职业健康检查工作能力范围查询赋值
				List<Object[]> orgOutRangesObjList = em.createNativeQuery(sb.toString()).getResultList();
				if(!CollectionUtils.isEmpty(orgOutRangesObjList)){
					List<IZwOrgOutRangeZzsb> zwOrgOutRangeList = new ArrayList<>();
					for(Object[] objArr : orgOutRangesObjList){
						Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
						Integer zoneId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
						if(null == rid || null == zoneId){
							continue;
						}
						String zoneName = null == objArr[2] ? null : objArr[2].toString();
						String zoneGb = null == objArr[3] ? null : objArr[3].toString();
						Short zoneType = null == objArr[4] ? null : Short.parseShort(objArr[4].toString());
						String zoneCode = null == objArr[5] ? null : objArr[5].toString();
						TsZone tsZone = new TsZone(zoneId);
						tsZone.setZoneName(zoneName);
						tsZone.setZoneGb(zoneGb);
						tsZone.setZoneType(zoneType);
						tsZone.setZoneCode(zoneCode);
						IZwOrgOutRangeZzsb outRange = new TdZwTjorgOutRangeZzsb(rid);
						Date createDate = null == objArr[6] ? null : DateUtils.parseDate(objArr[6]);
						Integer createManId = null == objArr[7] ? null : Integer.parseInt(objArr[7].toString());
						outRange.setCreateDate(createDate);
						outRange.setCreateManid(createManId);
						outRange.setZwOrginfo(find);
						outRange.setFkByZoneId(tsZone);
						zwOrgOutRangeList.add(outRange);
					}
					if(!CollectionUtils.isEmpty(zwOrgOutRangeList)){
						find.setZwOrgOutRanges(zwOrgOutRangeList);
					}
				}
			}

		}
		return find;
	}


	@Transactional(readOnly = true)
	public IZwOrginfoZzsb findTdZwTjorginfoEntity(Integer orgInfoType, Integer unitId) {
		if (orgInfoType != null && null != unitId) {
			StringBuilder sb = new StringBuilder();
			if (orgInfoType == 0) {
				sb.append("select t from TdZwTjorginfoZzsb t where t.tsUnit.rid = ").append(unitId);
			} else if (orgInfoType == 1) {
				sb.append("select t from TdZwSrvorginfoZzsb t where t.tsUnit.rid = ").append(unitId);
			} else if (orgInfoType == 2) {
				sb.append("select t from TdZwDiagorginfoZzsb t where t.tsUnit.rid = ").append(unitId);
			}else if (orgInfoType == 4) {
				sb.append("select t from TdZwSupportOrgZzsb t where t.tsUnit.rid = ").append(unitId);
			}else {
				sb.append("select t from TdZwOcchethInfoZzsb t where t.tsUnit.rid = ").append(unitId);
			}

			List<IZwOrginfoZzsb> list = em.createQuery(sb.toString()).getResultList();
			if (list != null && list.size() > 0) {
				IZwOrginfoZzsb find = list.get(0);
				// 立即加载其对应的懒加载集合 停止懒加载
				if(orgInfoType == 4){
					find.getSupportTechList().size();
					find.getSupportQualList().size();
					find.getSupportChkList().size();
					find.getSupportFundList().size();
				}
				return find;
			} else {
				return null;
			}
		}
		return null;
	}

	/**
	 * 根据实体对象进行立即加载对应的子集合
	 */
	@Transactional(readOnly = true)
	private void findZwOrSubList(IZwOrginfoZzsb find) {
		if (null != find) {
			if (null != find.getZwAnnex()) {
				find.getZwAnnex().size();
			}
			if (null != find.getZwInst()) {
				List<IZwInstZzsb> instList = find.getZwInst();
				for(IZwInstZzsb inst : instList) {
					List<TdZwInstJcmainZzsb> mainList = inst.getTdZwInstinfo().getInstJcMainList();
					if(null != mainList && mainList.size() > 0) {
						for(TdZwInstJcmainZzsb main : mainList) {
							main.getSubList().size();
						}
					}
					
					List<TdZwInstSbjcmainZzsb> sbMainList = inst.getTdZwInstinfo().getInstSbjcmainList();
					if(null != sbMainList && sbMainList.size() > 0) {
						for(TdZwInstSbjcmainZzsb main : sbMainList) {
							main.getSubList().size();
						}
					}
				}
			}
			if (null != find.getZwOrgPsns()) {
				List<IZwOrgPsnsZzsb> zwOrgPsns = find.getZwOrgPsns();
				if (null != zwOrgPsns && zwOrgPsns.size() > 0) {
					Map<String, String> tempMap = Maps.newHashMap();
					tempMap.put("1", "接触粉尘作业劳动者健康检查");
					tempMap.put("2", "接触化学作业劳动者健康检查");
					tempMap.put("3", "放射性作业劳动者健康检查");
					tempMap.put("4", "接触物理因素作业劳动者健康检查");
					tempMap.put("5", "接触生物因素作业劳动者健康检查");
					tempMap.put("6", "从事电工、压力容器、高处作业以及机动车驾驶等劳动者健康检查");
					
					// 将从事科室与从事项目组织到具体字段中展示
					for (IZwOrgPsnsZzsb zwOrgPsn : zwOrgPsns) {
						List<TdZwWorkdeptZzsb> tdZwWorkdepts = zwOrgPsn.getTdZwWorkdepts();
						if (null != tdZwWorkdepts && tdZwWorkdepts.size() > 0) {
							Collections.sort(tdZwWorkdepts, new Comparator<TdZwWorkdeptZzsb>() {

								public int compare(TdZwWorkdeptZzsb o1, TdZwWorkdeptZzsb o2) {
									return String.valueOf(o1.getTsSimpleCode().getCodeNo()).compareTo(
											String.valueOf(o2.getTsSimpleCode().getCodeNo()));
								}
							});
							StringBuilder name = new StringBuilder();
							for (TdZwWorkdeptZzsb dept : tdZwWorkdepts) {
								name.append(",").append(dept.getTsSimpleCode().getCodeName());
							}
							zwOrgPsn.setWordOfficeName(name.substring(1));
						}
						List<TdZwPsnitemsZzsb> tdZwPsnitemses = zwOrgPsn.getTdZwPsnitemses();
						if(null != tdZwPsnitemses && tdZwPsnitemses.size() > 0) {
							StringBuilder name = new StringBuilder();
							StringBuilder nameStr = new StringBuilder();
							for(TdZwPsnitemsZzsb items : tdZwPsnitemses)	{
								name.append(",").append(items.getItemCode().toString());
								nameStr.append(",").append(tempMap.get(items.getItemCode().toString()));
							}
							zwOrgPsn.setWordItems(name.substring(1));
							zwOrgPsn.setWordItemStr(nameStr.substring(1));
						}
						
						//体检信息
						TdZwPsninfoZzsb tdZwPsninfo = zwOrgPsn.getTdZwPsninfo();
						List<TdZwPsntjMainZzsb> tjMainList = tdZwPsninfo.getTjMainList();
						if(null != tjMainList && tjMainList.size() > 0) {
							for(TdZwPsntjMainZzsb main : tjMainList) {
								main.getSubList().size();
							}
						}
						tdZwPsninfo.getZzlbList().size();
						zwOrgPsn.getTdZwPsninfo().setTjMainList(tjMainList);
					}
				}
			}
			if (null != find.getZwSvritm()) {
				find.getZwSvritm().size();
			}
			// 特殊处理健康检查机构的检验项目
			if (null != find.getZwItems() && find.getZwItems().size() > 0) {
				StringBuilder itemIds = new StringBuilder();
				for (IZwItemsZzsb item : find.getZwItems()) {
					itemIds.append(",").append(item.getTsSimpleCode().getRid());
				}
				find.setJcItems(itemIds.substring(1));
			}

			if(null != find.getZwOrgOutRanges()) {
                find.getZwOrgOutRanges().size();
            }
		}
	}

	public TdZwInstinfoZzsb saveOrUpdateInstInfo(TdZwInstinfoZzsb tdZwInstinfo) {
		if (tdZwInstinfo.getRid() == null) {
			return (TdZwInstinfoZzsb) super.saveObj(tdZwInstinfo);
		} else {
			return (TdZwInstinfoZzsb) super.updateObj(tdZwInstinfo);
		}
	}

	@Transactional(readOnly = false)
	public void saveOrUpdateInstInfoList(List<TdZwInstinfoZzsb> instinfoList){
		List<TdZwInstinfoZzsb> saveObjList = new ArrayList<>();
		List<TdZwInstinfoZzsb> updateObjList = new ArrayList<>();
		if(!CollectionUtils.isEmpty(instinfoList)){
			for(TdZwInstinfoZzsb instinfo : instinfoList){
				if(null == instinfo.getRid()){
					saveObjList.add(instinfo);
				}else{
					updateObjList.add(instinfo);
				}
			}
		}
		if(!CollectionUtils.isEmpty(saveObjList)){
			this.saveBatchObjs(saveObjList);
		}
		if(!CollectionUtils.isEmpty(updateObjList)){
			this.updateBatchObjs(updateObjList);
		}
	}

	@Transactional(readOnly = false)
	public void saveOrUpdateTdZwPsninfoList(List<TdZwPsninfoZzsb> psninfoList){
		List<TdZwPsninfoZzsb> saveObjList = new ArrayList<>();
		List<TdZwPsninfoZzsb> updateObjList = new ArrayList<>();
		if(!CollectionUtils.isEmpty(psninfoList)){
			for(TdZwPsninfoZzsb psninfo : psninfoList){
				if(null == psninfo.getRid()){
					saveObjList.add(psninfo);
				}else{
					updateObjList.add(psninfo);
				}
			}
		}
		if(!CollectionUtils.isEmpty(saveObjList)){
			this.saveBatchObjs(saveObjList);
		}
		if(!CollectionUtils.isEmpty(updateObjList)){
			this.updateBatchObjs(updateObjList);
		}
	}
	
    public TdZwPsnTypeZzsb saveOrUpdatePsnType(TdZwPsnTypeZzsb tdZwPsnType) {
        if (tdZwPsnType.getRid() == null) {
            return (TdZwPsnTypeZzsb) super.saveObj(tdZwPsnType);
        } else {
            return (TdZwPsnTypeZzsb) super.updateObj(tdZwPsnType);
        }
    }

    public void deletePsnType(Integer psnId, Integer zzjgType) {
        StringBuilder sb = new StringBuilder();
        sb.append("delete TD_ZW_PSN_TYPE where MAIN_ID = ").append(psnId);
        sb.append(" and  ZZJG_TYPE = ").append(zzjgType);
        try {
            em.createNativeQuery(sb.toString()).executeUpdate();
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }
	/**
	 * 保存支撑机构
	 *
	 * @param supportOrg 支撑机构
	 * @param annexList  附件
	 * @return 支撑机构
	 */
	public IZwOrginfoZzsb saveOrUpdateSupportEntity(TdZwSupportOrgZzsb supportOrg,
													List<TdZwJsffjgCommAnnexZzsb> annexList) {
		Boolean isFlag = ObjectUtil.isNotNull(supportOrg.getRid()) ;
		IZwOrginfoZzsb iZwOrginfoZzsb = saveOrUpdateZwOrginfoEntity(supportOrg, annexList);
		Integer rid = iZwOrginfoZzsb.getRid();
		updateSupportInst(rid, supportOrg.getSupportInstSimpleCodeRidList());
		if(isFlag){
			this.upsertEntity(supportOrg.getSupportTrc());
			this.updateSupportAdvance(rid,supportOrg.getAdvanceList());
		}
		this.saveAbilityAndSupportHealth(supportOrg.getAbilityList(), rid, supportOrg.getSupportHealth());
		return iZwOrginfoZzsb;
	}

	/**
	 *  <p>方法描述：科技攻关能力保存</p>
	 * @MethodAuthor hsj 2023-10-26 15:36
	 */
	private void updateSupportAdvance(Integer rid,List<TdZwSupportAdvance> advanceList) {

		Map<String, Object> paramMap = new HashMap<>();
		String sql = "DELETE FROM TD_ZW_SUPPORT_ADVANCE WHERE MAIN_ID = :rid";
		paramMap.put("rid", rid);
		this.executeSql(sql, paramMap);
		if(CollectionUtils.isEmpty(advanceList)){
			return;
		}
		this.saveBatchObjs(advanceList);
	}

	/**
	 * 更新支撑机构仪器设备配置信息
	 *
	 * @param rid                   主表rid
	 * @param instSimpleCodeRidList 仪器设备配置信息对应码表rid列表
	 */
	private void updateSupportInst(Integer rid, List<Integer> instSimpleCodeRidList) {
		Map<String, Object> paramMap = new HashMap<>();
		//删除仪器设备配置信息
		String sql = "DELETE FROM TD_ZW_SUPPORT_INST WHERE MAIN_ID = :rid";
		paramMap.put("rid", rid);
		this.executeSql(sql, paramMap);
		//封装仪器设备配置信息
		if (ObjectUtil.isEmpty(instSimpleCodeRidList)) {
			return;
		}
		List<TdZwSupportInst> supportInstList = new ArrayList<>();
		for (Integer instSimpleCodeRid : instSimpleCodeRidList) {
			if (instSimpleCodeRid == null) {
				return;
			}
			TdZwSupportInst supportInst = new TdZwSupportInst();
			supportInst.setFkByMainId(new TdZwSupportOrgZzsb(rid));
			supportInst.setFkByInstId(new TsSimpleCode(instSimpleCodeRid));
			this.preEntity(supportInst);
			supportInstList.add(supportInst);
		}
		this.updateBatchObjs(supportInstList);
	}
	/**
 	 * <p>修订内容：人员信息保存在添加或修改人员信息中已保存，保存时更新所属机构</p>
 	 * @MethodReviser qrr,2018年6月22日,saveOrUpdateZwOrginfoEntity
	 * */
	public IZwOrginfoZzsb saveOrUpdateZwOrginfoEntity(IZwOrginfoZzsb zwOrginfoEntity, List<TdZwJsffjgCommAnnexZzsb> annexList) {
		// 处理被删除人员的MASTER_DATA_TIME更新
		if (StringUtils.isNotBlank(zwOrginfoEntity.getDelPsnIds())) {
			updateMasterDataTimeForDeletedPersons(zwOrginfoEntity.getDelPsnIds());
			updatePsnInfo(zwOrginfoEntity,zwOrginfoEntity.getTsUnit().getRid());
		}

		// 获取原始人员状态用于比较
		Map<Integer, String> originalDutyStateMap = getOriginalPersonnelDutyStates(zwOrginfoEntity);

		List<Integer> needClearOrgIdPsnInfoList = new ArrayList<>();
		if (null!=zwOrginfoEntity.getTsUnit() && null!=zwOrginfoEntity.getTsUnit().getRid()) {
			List<IZwOrgPsnsZzsb> psnList = zwOrginfoEntity.getZwOrgPsns();
			if(null != psnList && psnList.size() > 0) {
				for(IZwOrgPsnsZzsb p : psnList) {
					Integer psnInfoRid = null == p.getTdZwPsninfo() ? null : p.getTdZwPsninfo().getRid();
					if(null == psnInfoRid){
						continue;
					}

					// 检查在职状态是否发生变化，如果发生变化则更新MASTER_DATA_TIME
					checkAndUpdateMasterDataTimeForDutyChange(psnInfoRid, p.getOnDuty(), originalDutyStateMap);

					if ("2".equals(p.getOnDuty())) {
						needClearOrgIdPsnInfoList.add(psnInfoRid);
						continue;
					}
					//在职的时候 将资质人员所属机构 设置成当前单位
					String sql ="update TD_ZW_PSNINFO set ORG_ID ='"+zwOrginfoEntity.getTsUnit().getRid()+"' where rid ="+psnInfoRid;
					super.executeSql(sql);
				}
			}
		}
		if(!CollectionUtils.isEmpty(needClearOrgIdPsnInfoList)){
			clearPsnInfoOrgId(needClearOrgIdPsnInfoList, zwOrginfoEntity,zwOrginfoEntity.getTsUnit().getRid());
		}
		List<IZwInstZzsb> instList = zwOrginfoEntity.getZwInst();
		if(null != instList && instList.size() > 0) {
			for(IZwInstZzsb p : instList) {
				super.update(p.getTdZwInstinfo());
			}
		}
		if(null == zwOrginfoEntity.getZwAnnex()){
			zwOrginfoEntity.setZwAnnex(new ArrayList<IZwAnnexZzsb>());
		}
		if(null == zwOrginfoEntity.getZwInst()){
			zwOrginfoEntity.setZwInst(new ArrayList<IZwInstZzsb>());
		}
		if(null == zwOrginfoEntity.getZwItems()){
			zwOrginfoEntity.setZwItems(new ArrayList<IZwItemsZzsb>());
		}
		if(null == zwOrginfoEntity.getZwOrgPsns()){
			zwOrginfoEntity.setZwOrgPsns(new ArrayList<IZwOrgPsnsZzsb>());
		}
		if(null == zwOrginfoEntity.getZwSvritm()){
			zwOrginfoEntity.setZwSvritm(new ArrayList<IZwSvritmZzsb>());
		}
		if(null == zwOrginfoEntity.getZwOrgOutRanges()){
			zwOrginfoEntity.setZwOrgOutRanges(new ArrayList<IZwOrgOutRangeZzsb>());
		}
		if(zwOrginfoEntity.getRid()==null && zwOrginfoEntity instanceof TdZwSupportOrgZzsb){
			zwOrginfoEntity.setSupportPlaces(null);
		}
		if (zwOrginfoEntity.getRid() == null) {
			super.save(zwOrginfoEntity);
		} else {
			super.update(zwOrginfoEntity);
		}
		//保存场所检测
		if(zwOrginfoEntity.getSupportPlaces()!=null){
			super.upsertEntity(zwOrginfoEntity.getSupportPlaces());
		}
		Integer type = 0;
		if (zwOrginfoEntity instanceof TdZwTjorginfoZzsb) {
			type = 0;
		} else if (zwOrginfoEntity instanceof TdZwSrvorginfoZzsb) {
			type = 1;
		} else if (zwOrginfoEntity instanceof TdZwDiagorginfoZzsb){
			type = 2;
		}else if(zwOrginfoEntity instanceof TdZwSupportOrgZzsb){
			type = 4;
		} else {
		    type = 3;
        }
		IZwOrginfoZzsb find = this.findZwOrgInfoById(type, zwOrginfoEntity.getRid());
        if(4 != type){
			doSaveJsffjgCommAnnexAction(zwOrginfoEntity, annexList);
		}
		doSaveZoneApproves(zwOrginfoEntity);
		return find;
	}

	public IZwOrginfoZzsb saveOrUpdateZwOrginfoEntity(IZwOrginfoZzsb zwOrginfoEntity) {
		if (StringUtils.isNotBlank(zwOrginfoEntity.getDelPsnIds())) {
			updatePsnInfo(zwOrginfoEntity,zwOrginfoEntity.getTsUnit().getRid());
		}
		if (null!=zwOrginfoEntity.getTsUnit() && null!=zwOrginfoEntity.getTsUnit().getRid()) {
			List<IZwOrgPsnsZzsb> psnList = zwOrginfoEntity.getZwOrgPsns();
			if(null != psnList && psnList.size() > 0) {
				for(IZwOrgPsnsZzsb p : psnList) {
					if ("2".equals(p.getOnDuty())) {
						continue;
					}
					String sql ="update TD_ZW_PSNINFO set ORG_ID ='"+zwOrginfoEntity.getTsUnit().getRid()+"' where rid ="+p.getTdZwPsninfo().getRid();
					super.executeSql(sql);
				}
			}
		}
		List<IZwInstZzsb> instList = zwOrginfoEntity.getZwInst();
		if(null != instList && instList.size() > 0) {
			for(IZwInstZzsb p : instList) {
				super.update(p.getTdZwInstinfo());
			}
		}
//		if(null != zwOrginfoEntity.getTsUnit() && null != zwOrginfoEntity.getTsUnit().getRid()){
//			super.update(zwOrginfoEntity.getTsUnit());
//		}
		if(null == zwOrginfoEntity.getZwAnnex()){
			zwOrginfoEntity.setZwAnnex(new ArrayList<IZwAnnexZzsb>());
		}
		if(null == zwOrginfoEntity.getZwInst()){
			zwOrginfoEntity.setZwInst(new ArrayList<IZwInstZzsb>());
		}
		if(null == zwOrginfoEntity.getZwItems()){
			zwOrginfoEntity.setZwItems(new ArrayList<IZwItemsZzsb>());
		}
		if(null == zwOrginfoEntity.getZwOrgPsns()){
			zwOrginfoEntity.setZwOrgPsns(new ArrayList<IZwOrgPsnsZzsb>());
		}
		if(null == zwOrginfoEntity.getZwSvritm()){
			zwOrginfoEntity.setZwSvritm(new ArrayList<IZwSvritmZzsb>());
		}
		if(null == zwOrginfoEntity.getZwOrgOutRanges()){
			zwOrginfoEntity.setZwOrgOutRanges(new ArrayList<IZwOrgOutRangeZzsb>());
		}
		if (zwOrginfoEntity.getRid() == null) {
			super.save(zwOrginfoEntity);
		} else {
			super.update(zwOrginfoEntity);
		}
		Integer type = 0;
		if (zwOrginfoEntity instanceof TdZwTjorginfoZzsb) {
			type = 0;
		} else if (zwOrginfoEntity instanceof TdZwSrvorginfoZzsb) {
			type = 1;
		} else if (zwOrginfoEntity instanceof TdZwDiagorginfoZzsb){
			type = 2;
		} else {
			type = 3;
		}
		IZwOrginfoZzsb find = this.findZwOrgInfoById(type, zwOrginfoEntity.getRid());
		return find;
	}

	/** 生成新的对象 避免懒加载的属性 出现 A collection with cascade="all-delete-orphan" was no longer referenced by the owning entity instance */
	private IZwOrginfoZzsb resetZwOrgInfo(IZwOrginfoZzsb zwOrginfo){
		IZwOrginfoZzsb orginfo = null;
		if (zwOrginfo instanceof TdZwTjorginfoZzsb) {
			orginfo = new TdZwTjorginfoZzsb();
		} else if (zwOrginfo instanceof TdZwSrvorginfoZzsb) {
			orginfo = new TdZwSrvorginfoZzsb();
		} else if (zwOrginfo instanceof TdZwDiagorginfoZzsb){
			orginfo = new TdZwDiagorginfoZzsb();
		}else if(zwOrginfo instanceof TdZwSupportOrgZzsb){
			orginfo = new TdZwSupportOrgZzsb();
		} else {
			orginfo = new TdZwOcchethInfoZzsb();
		}
		orginfo.setRid(zwOrginfo.getRid());
		orginfo.setTsUnit(zwOrginfo.getTsUnit());
		orginfo.setOrgName(zwOrginfo.getOrgName());
		orginfo.setOrgAddr(zwOrginfo.getOrgAddr());
		orginfo.setOrgFz(zwOrginfo.getOrgFz());
		orginfo.setOrgFzzw(zwOrginfo.getOrgFzzw());
		orginfo.setLinkMan(zwOrginfo.getLinkMan());
		orginfo.setLinkMb(zwOrginfo.getLinkMb());
		orginfo.setLinkTel(zwOrginfo.getLinkTel());
		orginfo.setFax(zwOrginfo.getFax());
		orginfo.setZipcode(zwOrginfo.getZipcode());
		orginfo.setEmail(zwOrginfo.getEmail());
		orginfo.setCertNo(zwOrginfo.getCertNo());
		orginfo.setFirstGetday(zwOrginfo.getFirstGetday());
		orginfo.setFilingDate(zwOrginfo.getFilingDate());
		orginfo.setCreateDate(zwOrginfo.getCreateDate());
		orginfo.setCreateManid(zwOrginfo.getCreateManid());
		orginfo.setJcItems(zwOrginfo.getJcItems());
		orginfo.setState(zwOrginfo.getState());
		orginfo.setCancelState(zwOrginfo.getCancelState());
		orginfo.setCancelDate(zwOrginfo.getCancelDate());
		orginfo.setValidDate(zwOrginfo.getValidDate());
		orginfo.setDelPsnIds(zwOrginfo.getDelPsnIds());
		orginfo.setCreditCode(zwOrginfo.getCreditCode());
		orginfo.setFkByLevelId(zwOrginfo.getFkByLevelId());
		orginfo.setOutWorkPower(zwOrginfo.getOutWorkPower());
		orginfo.setFkByUnitTypeId(zwOrginfo.getFkByUnitTypeId());
		orginfo.setLabAddr(zwOrginfo.getLabAddr());
		orginfo.setCommuniAddr(zwOrginfo.getCommuniAddr());
		orginfo.setRcdOrgId(zwOrginfo.getRcdOrgId());
		orginfo.setRcdUnitName(zwOrginfo.getRcdUnitName());
		orginfo.setCheckRst(zwOrginfo.getCheckRst());
		orginfo.setAuditAdv(zwOrginfo.getAuditAdv());
		orginfo.setFkByChkPsnId(zwOrginfo.getFkByChkPsnId());
		orginfo.setLastSmtDate(zwOrginfo.getLastSmtDate());
		orginfo.setSupportTechList(zwOrginfo.getSupportTechList());
		orginfo.setSupportQualList(zwOrginfo.getSupportQualList());
		orginfo.setSupportChkList(zwOrginfo.getSupportChkList());
		orginfo.setSupportFundList(zwOrginfo.getSupportFundList());
		orginfo.setSupportPlaces(zwOrginfo.getSupportPlaces());
		orginfo.setFkByTypeId(zwOrginfo.getFkByTypeId());
		return orginfo;
	}

	/** sql 执行状态修改 */
	public void updateToCancelState(IZwOrginfoZzsb zwOrginfoEntity){
		if(null == zwOrginfoEntity || null == zwOrginfoEntity.getRid()){
			return;
		}
		StringBuilder builder = new StringBuilder();
		if (zwOrginfoEntity instanceof TdZwTjorginfoZzsb) {
			builder.append(" UPDATE TD_ZW_TJORGINFO SET STATE = 0 ");
		} else if (zwOrginfoEntity instanceof TdZwSrvorginfoZzsb) {
			builder.append(" UPDATE TD_ZW_SRVORGINFO SET STATE = 0 ");
		} else if (zwOrginfoEntity instanceof TdZwDiagorginfoZzsb){
			builder.append(" UPDATE TD_ZW_DIAGORGINFO SET STATE = 0 ");
		}else if(zwOrginfoEntity instanceof TdZwSupportOrgZzsb){
			builder.append(" UPDATE TD_ZW_SUPPORT_ORG SET STATE = 0 ");
		} else {
			builder.append(" UPDATE TD_ZW_OCCHETH_INFO SET STATE = 0 ");
			if(null != zwOrginfoEntity.getFkByLevelId() && null != zwOrginfoEntity.getFkByLevelId().getRid()){
				builder.append(" ,LEVEL_ID = ").append(zwOrginfoEntity.getFkByLevelId().getRid().intValue());
			}else{
				builder.append(" ,LEVEL_ID = NULL ");
			}

		}
		builder.append(" WHERE RID= ").append(zwOrginfoEntity.getRid().intValue());
		super.executeSql(builder.toString(),null);
	}
	@Transactional(readOnly = true)
	public List<TdZwInstinfoZzsb> findTdZwInsfInfos(String ids, Integer unitId) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TdZwInstinfoZzsb t where t.tsUnit.rid = ").append(unitId);
//		if (StringUtils.isNotBlank(ids)) {
//			sb.append(" and t.rid not in (").append(ids).append(")");
//		}
		sb.append(" order by t.num");
		List<TdZwInstinfoZzsb> instList = em.createQuery(sb.toString()).getResultList();
		
		if(null != instList && instList.size() > 0) {
			for(TdZwInstinfoZzsb inst : instList) {
				List<TdZwInstJcmainZzsb> mainList = inst.getInstJcMainList();
				if(null != mainList) {
					for(TdZwInstJcmainZzsb main : mainList) {
						main.getSubList().size();
					}
				}
				
				List<TdZwInstSbjcmainZzsb> sbMainList = inst.getInstSbjcmainList();
				if(null != sbMainList && sbMainList.size() > 0) {
					for(TdZwInstSbjcmainZzsb main : sbMainList) {
						main.getSubList().size();
					}
				}
			}
		}
		return instList;
	}

	public String deleteZwInstInfo(Integer instId) {
		StringBuilder sb = new StringBuilder();
		sb.append("delete TD_ZW_INSTINFO where rid = ").append(instId);
		String msg = null;
		try {
			em.createNativeQuery(sb.toString()).executeUpdate();
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			msg = "该仪器已被引用，无法删除！";
		}
		return msg;

	}

	@Transactional(readOnly = true)
	public List<TdZwPsninfoZzsb> findTdZwPsnInfos(String ids, Integer unitId) {
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TdZwPsninfoZzsb t where t.tsUnit.rid = ").append(unitId);
//		if (StringUtils.isNotBlank(ids)) {
//			sb.append(" and t.rid not in (").append(ids).append(")");
//		}
		sb.append(" order by t.empName");
		List<TdZwPsninfoZzsb> list = em.createQuery(sb.toString()).getResultList();
		if(null != list && list.size() > 0) {
			for(TdZwPsninfoZzsb t : list) {
				t.getTjMainList().size();
				t.getZzlbList().size();
				if(null !=t.getZzlbList() && t.getZzlbList().size()>0){
					for(TdZwPsnZzlbZzsb zzlb:t.getZzlbList()){
						zzlb.getRangeList().size();
					}
				}
			}
		}
		return list;
	}
	
	@Transactional(readOnly = true)
	public TdZwPsninfoZzsb findTdZwPsnInfo(Integer psnId) {
		TdZwPsninfoZzsb psn=new TdZwPsninfoZzsb();
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TdZwPsninfoZzsb t where t.rid = ").append(psnId);
		List<TdZwPsninfoZzsb> list = em.createQuery(sb.toString()).getResultList();
		if(null != list && list.size() > 0) {
			psn=list.get(0);
			psn.getTjMainList().size();
			psn.getZzlbList().size();
			if(null !=psn.getZzlbList() && psn.getZzlbList().size()>0){
				for(TdZwPsnZzlbZzsb zzlb:psn.getZzlbList()){
					zzlb.getRangeList().size();
					if(null != zzlb.getRangeList() && zzlb.getRangeList().size()>0){
						String[] str=new String[zzlb.getRangeList().size()];
						for(int i=0;i<zzlb.getRangeList().size();i++){
							TdZwZzlbRangeZzsb range=zzlb.getRangeList().get(i);
							str[i]=range.getFkByZzRangeId().getRid().toString();
						}
						zzlb.setSelectRanges(str);
					}
				}
			}
		}
		return psn;
	}


	@Transactional(readOnly = true)
	public IZwOrginfoZzsb findZwOrgInfoByEntityRid(IZwOrginfoZzsb zwOrginfoEntity){
		if(null == zwOrginfoEntity || null == zwOrginfoEntity.getRid()){
			return null;
		}
		Integer type = 0;
		if (zwOrginfoEntity instanceof TdZwTjorginfoZzsb) {
			type = 0;
		} else if (zwOrginfoEntity instanceof TdZwSrvorginfoZzsb) {
			type = 1;
		} else if (zwOrginfoEntity instanceof TdZwDiagorginfoZzsb){
			type = 2;
		} else if(zwOrginfoEntity instanceof TdZwSupportOrgZzsb){
			type = 4;
		} else {
			type = 3;
		}
		return fillTdZwTjorgInfos(type,zwOrginfoEntity);
	}
	@Transactional(readOnly = true)
	public IZwOrginfoZzsb findZwOrgInfoById(Integer type, Integer rid) {
		IZwOrginfoZzsb zwOrginfo;
		if (type.equals(0)) {
			zwOrginfo = (IZwOrginfoZzsb) super.find(TdZwTjorginfoZzsb.class, rid);
		} else if (type.equals(1)) {
			zwOrginfo = (IZwOrginfoZzsb) super.find(TdZwSrvorginfoZzsb.class, rid);
		} else if (type.equals(2)) {
			zwOrginfo = (IZwOrginfoZzsb) super.find(TdZwDiagorginfoZzsb.class, rid);
		}else if(type.equals(4)){
			zwOrginfo = (IZwOrginfoZzsb) super.find(TdZwSupportOrgZzsb.class, rid);
		}else {
			zwOrginfo = (IZwOrginfoZzsb) super.find(TdZwOcchethInfoZzsb.class, rid);
		}
		// 在这里 如果不用懒加载 会有报错
		// A collection with cascade="all-delete-orphan" was no longer referenced by the owning entity instance
		// 处理方式是 生成一个新的对象 不包含懒加载属性
		zwOrginfo = resetZwOrgInfo(zwOrginfo);
		return fillTdZwTjorgInfos(type,zwOrginfo);
	}

	public String deleteZwPsnInfo(Integer psnId) {
		StringBuilder sb = new StringBuilder();
		sb.append("DELETE TD_ZW_PSNINFO WHERE RID = ").append(psnId);
		String msg = null;
		try {
			this.executeSql("DELETE FROM TD_ZW_PSN_TYPE WHERE MAIN_ID ="+psnId);
			this.executeSql("DELETE FROM TD_ZW_ZZLB_RANGE WHERE MAIN_ID in (select rid from TD_ZW_PSN_ZZLB where PSN_ID="+psnId+")");
			this.executeSql("DELETE FROM TD_ZW_PSN_ZZLB WHERE PSN_ID ="+psnId);
			em.createNativeQuery(sb.toString()).executeUpdate();
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			msg = "该人员已被引用，无法删除！";
		}
		return msg;
	}

	public void saveOrUpdatePsnInfo(TdZwPsninfoZzsb tdZwPsninfo) {
		if (tdZwPsninfo.getRid() == null) {
			super.save(tdZwPsninfo);
		} else {
			super.update(tdZwPsninfo);
		}
	}
	
	public List<TsSimpleCode> findUpCodeList(String typeNo){
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") ");
			sb.append(" and t.codeNo=t.codeLevelNo");
			sb.append(" order by t.codeNo");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	@Transactional(readOnly = true)
	public List<TsSimpleCode> findSimpleCodesByTypeNo(String typeNo) {
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select new TsSimpleCode(t.rid, t.codeName, t.tsCodeType.codeTypeName, t.codeLevelNo) from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo)
					.append(") order by t.codeLevelNo,t.codeNo ");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

//	public String getDesc() {
//		return "职业卫生资质申报服务";
//	}
//
//	public String getVersion() {
//		return "1.0.0";
//	}
	
	public List<TsSimpleCode> findUpListOrderNum(String typeNo){
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo)
					.append(")  ");
			sb.append(" and t.codeNo=t.codeLevelNo");
			sb.append(" order by t.num");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	public List<TsSimpleCode> findParentCodeList(String typeNo,String codeNo){
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
					.append(typeNo)
					.append(")  ");
			sb.append(" and t.codeLevelNo like '").append(codeNo).append(".%'");
			sb.append(" order by t.num");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	
	public TsZone findTsZoneByRid(Integer rid){
		TsZone zone=new TsZone();
		zone = (TsZone) super.find(TsZone.class, rid);
		return zone;
	}
	/**
 	 * <p>方法描述：查询资质人员信息</p>
 	 * @MethodAuthor qrr,2018年6月20日,findTdZwPsnInfos
	 * */
	@Transactional(readOnly = true)
	public List<TdZwPsninfoZzsb> findTdZwPsnInfosByParam(String name,String idc,String type, Integer unitId, Integer zwOrgType) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		StringBuilder sb = new StringBuilder();
		sb.append("select t from TdZwPsninfoZzsb t where 1=1");
		if (StringUtils.isNotBlank(name)) {
			sb.append(" and t.empName like :empName ");
			paramMap.put("empName", new StringBuilder("%").append(StringUtils.convertBFH(name.trim())).append("%").toString());
		}
		if (StringUtils.isNotBlank(idc)) {
			sb.append(" and t.idcCard = :idcCard ");
			paramMap.put("idcCard", StringUtils.convertBFH(idc.trim()));
		}
		if ("1".equals(type)) {
			sb.append(" and t.tsUnit.rid =").append(unitId);
		}else if ("2".equals(type)) {
			sb.append(" and (t.tsUnit.rid is null");
			//检查机构 
			sb.append(" or (not exists(select t1 from TdZwTjorgpsnsZzsb t1  where t1.tdZwPsninfo.tsUnit.rid is not null and t1.onDuty='1' and t1.tdZwPsninfo.rid = t.rid)");
			//放射机构 
			sb.append(" and not exists(select t1 from TdZwSrvorgpsnsZzsb t1  where t1.tdZwPsninfo.tsUnit.rid is not null and t1.onDuty='1' and t1.tdZwPsninfo.rid = t.rid)");
			//诊断机构
			sb.append(" and not exists(select t1 from TdZwDiagpsnsZzsb t1  where t1.tdZwPsninfo.tsUnit.rid is not null and t1.onDuty='1' and t1.tdZwPsninfo.rid = t.rid)))");
		}
		sb.append(" order by t.empName");
		List<TdZwPsninfoZzsb> list =findDataByHqlNoPage(sb.toString(),paramMap);
		if(null != list && list.size() > 0) {
			for(TdZwPsninfoZzsb t : list) {
				t.getTjMainList().size();
				t.getZzlbList().size();
				if(null !=t.getZzlbList() && t.getZzlbList().size()>0) {
                    // 证书编号赋值
				    StringBuffer certNoStr = new StringBuffer();
					for(TdZwPsnZzlbZzsb zzlb : t.getZzlbList()) {
						zzlb.getRangeList().size();
                        String codeDesc = zzlb.getFkByQualId().getCodeDesc();
                        boolean match = StringUtils.isNotBlank(codeDesc) && null != zwOrgType && codeDesc.contains(zwOrgType.toString());
                        if(StringUtils.isBlank(codeDesc) || match) {
                            certNoStr.append(",").append(zzlb.getCentNo());
                        }
					}
					if(StringUtils.isNotBlank(certNoStr)) {
                        t.setCertNoStr(certNoStr.substring(1));
                    }
				}

                // 资质范围查询
                sb = new StringBuilder();
                sb.append(" SELECT DISTINCT T.ZZ_RANGE_ID, T2.RID AS EMP_ID, T4.CODE_NO, T4.CODE_NAME ");
                sb.append(" FROM TD_ZW_ZZLB_RANGE T ");
                sb.append(" INNER JOIN TD_ZW_PSN_ZZLB T1 ON T.MAIN_ID = T1.RID ");
                sb.append(" INNER JOIN TD_ZW_PSNINFO T2 ON T1.PSN_ID = T2.RID ");
                sb.append(" INNER JOIN TS_SIMPLE_CODE T4 ON T.ZZ_RANGE_ID = T4.RID ");
                sb.append(" INNER JOIN TS_SIMPLE_CODE T5 ON T1.QUAL_ID = T5.RID ");
                sb.append(" WHERE T2.RID =").append(t.getRid());
                sb.append(" AND T5.CODE_DESC = '").append(zwOrgType).append("' ");
                sb.append(" GROUP BY T.ZZ_RANGE_ID, T2.RID, T4.CODE_NO, T4.CODE_NAME ");
                sb.append(" ORDER BY T2.RID, T4.CODE_NO ");
                List<Object[]> rangeObjArrList = em.createNativeQuery(sb.toString()).getResultList();
                if(!CollectionUtils.isEmpty(rangeObjArrList)) {
                    StringBuilder zzlbRangeStr = new StringBuilder();
                    for(Object[] objArr : rangeObjArrList) {
                        String codeName = null == objArr[3] ? null : objArr[3].toString();
                        zzlbRangeStr.append("，").append(codeName);
                    }
                    t.setZzlbRange(zzlbRangeStr.deleteCharAt(0).toString());
                }
			}
		}
		return list;
	}
	/**
 	 * <p>方法描述：清空资质人员的所属机构为空，若在其他单位中存在在职的情况，不清空</p>
 	 * @MethodAuthor qrr,2018年6月23日,updatePsnInfo
	 * */
	public void updatePsnInfo(IZwOrginfoZzsb zwOrginfoEntity,Integer orgId) {
		boolean flag = (null!=zwOrginfoEntity.getDelPsnIds() &&
				StringUtils.isBlank(zwOrginfoEntity.getDelPsnIds().substring(1)))||null==orgId;
		if (flag) {
			return;
		}
		List<Integer> ridList = new ArrayList<>();
		for(String ridStr : zwOrginfoEntity.getDelPsnIds().substring(1).split(",")){
			if(StringUtils.isNotBlank(ridStr)){
				ridList.add(Integer.parseInt(ridStr));
			}
		}
		clearPsnInfoOrgId(ridList, zwOrginfoEntity, orgId);
	}

	/**
	 * @Description: 删除和离职时 判断清空资质人员的所属单位
	 *
	 * @MethodAuthor pw,2022年02月28日
	 */
	@Transactional(readOnly = false)
	public void clearPsnInfoOrgId(List<Integer> psnInfoRidList, IZwOrginfoZzsb zwOrginfoEntity, Integer orgId){
		if(CollectionUtils.isEmpty(psnInfoRidList) || null == orgId){
			return;
		}
		List<List<Integer>> groupList = StringUtils.splitListProxy(psnInfoRidList, 1000);
		StringBuffer sb = new StringBuffer();
		sb.append("update TD_ZW_PSNINFO t set t.ORG_ID = null where t.rid in (:ridList)");
		if (!(zwOrginfoEntity instanceof TdZwTjorginfoZzsb)) {
			sb.append(" and not exists(select 1 from TD_ZW_TJORGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid ");
			sb.append(" and t.org_id = ").append(orgId).append(")");
		}
		if (!(zwOrginfoEntity instanceof TdZwSrvorginfoZzsb)) {
			sb.append(" and not exists(select 1 from TD_ZW_SRVORGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid");
			sb.append(" and t.org_id = ").append(orgId).append(")");
		}
		if (!(zwOrginfoEntity instanceof TdZwDiagorginfoZzsb)) {
			sb.append(" and not exists(select 1 from TD_ZW_DIAGPSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid");
			sb.append(" and t.org_id = ").append(orgId).append(")");
		}
		if(!(zwOrginfoEntity instanceof TdZwOcchethInfoZzsb)){
			sb.append(" and not exists(select 1 from TD_ZW_OCCHETH_PSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid");
			sb.append(" and t.org_id = ").append(orgId).append(")");
		}
		if(!(zwOrginfoEntity instanceof TdZwSupportOrgZzsb)){
			sb.append(" and not exists(select 1 from TD_ZW_SUPPORT_PSNS t1 where t1.ON_DUTY='1' and t1.EMP_ID = t.rid");
			sb.append(" and t.org_id = ").append(orgId).append(")");
		}
		for(List<Integer> ridList : groupList){
			Map<String,Object> paramsMap = new HashMap<>();
			paramsMap.put("ridList", ridList);
			super.executeSql(sb.toString(), paramsMap);
		}
	}
	/**
 	 * <p>方法描述：仅查询启用的码表数据</p>
 	 * @MethodAuthor qrr,2019年10月11日,findEnableUpCodeList
	 * */
	public List<TsSimpleCode> findEnableUpCodeList(String typeNo){
		if (StringUtils.isNotBlank(typeNo)) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TsSimpleCode t ");
			sb.append(" where t.tsCodeType.codeTypeName in (")
					.append(typeNo).append(") ");
			sb.append(" and (t.codeNo=t.codeLevelNo or t.codeLevelNo is null)");
			sb.append(" and t.ifReveal =1 ");
			sb.append(" order by t.num,t.codeLevelNo,t.codeNo");
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}
	/**
 	 * <p>方法描述：根据机构/单位Id、状态查询职业卫生技术服务机构基本信息</p>
 	 * @MethodAuthor qrr,2020年2月14日,selectTdZwOcchethInfoByOrgIdAndState
	 * */
    public List<TdZwOcchethInfoZzsb> selectTdZwOcchethInfoByOrgIdAndState(Integer orgId, Integer state) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwOcchethInfoZzsb t ");
            sb.append(" where t.tsUnit.rid = ").append(orgId);
            sb.append(" and t.state = ").append(state);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }
    /**
 	 * <p>方法描述：根据地区编码查询职业卫生技术服务机构基本信息</p>
 	 * @MethodAuthor qrr,2020年2月14日,selectTdZwOcchethInfoByOrgIdAndState
	 * */
    public List<TdZwOcchethInfoZzsb> selectTdZwOcchethInfoByZoneCode(String zoneCode) {
    	StringBuilder sb = new StringBuilder();
    	sb.append(" select t from TdZwOcchethInfoZzsb t left join t.tsUnit t1");
    	sb.append(" where t.state = 1");
    	if (StringUtils.isNotBlank(zoneCode)) {
    		sb.append(" and t1.tsZone.zoneGb like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%'");
		}
    	sb.append(" order by t1.tsZone.zoneGb,t.orgName");
    	return em.createQuery(sb.toString()).getResultList();
    }

    /**
    * @Description : 根据机构/单位Id、状态查询放射卫生技术服务机构资质信息
    * @MethodAuthor: anjing
    * @Date : 2020/2/17 15:36
    **/
    public List<TdZwSrvorginfoZzsb> selectTdZwSrvorginfoByOrgIdAndState(Integer orgId, Integer state) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwSrvorginfoZzsb t ");
            sb.append(" where t.tsUnit.rid = ").append(orgId);
            sb.append(" and t.state = ").append(state);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

	/**
	 * 根据机构/单位Id、状态查询职业病诊断机构资质信息
	 * @param orgId
	 * @param state
	 * @return
	 */
    public List<TdZwDiagorginfoZzsb> selectTdZwDiagorginfoByOrgIdAndState(Integer orgId, Integer state) {
		if (null != orgId) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TdZwDiagorginfoZzsb t ");
			sb.append(" where t.tsUnit.rid = ").append(orgId);
			sb.append(" and t.state = ").append(state);
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

	/**
	 * 根据机构/单位Id、状态查询职业健康检查机构资质信息
	 * @param orgId
	 * @param state
	 * @return
	 */
	public List<TdZwTjorginfoZzsb> selectTdZwTjorginfoByOrgIdAndState(Integer orgId, Integer state) {
		if (null != orgId) {
			StringBuilder sb = new StringBuilder();
			sb.append(" select t from TdZwTjorginfoZzsb t ");
			sb.append(" where t.tsUnit.rid = ").append(orgId);
			sb.append(" and t.state = ").append(state);
			return em.createQuery(sb.toString()).getResultList();
		}
		return null;
	}

    /**
    * @Description : 根据单位编码、状态查询放射卫生技术服务机构集合
    * @MethodAuthor: anjing
    * @Date : 2020/2/19 15:03
    **/
    public List<TdZwSrvorginfoZzsb> selectTdZwSrvorginfoByZoneCode(String zoneCode) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TdZwSrvorginfoZzsb t left join t.tsUnit t1");
        sb.append(" where t.state = 1");
        if (StringUtils.isNotBlank(zoneCode)) {
            sb.append(" and t1.tsZone.zoneGb like '").append(ZoneUtil.zoneSelect(zoneCode)).append("%'");
        }
        sb.append(" order by t1.tsZone.zoneGb,t.orgName");
        return em.createQuery(sb.toString()).getResultList();
    }

    /**
    * @Description : 根据CodelebelNo获取码表大类
    * @MethodAuthor: anjing
    * @Date : 2020/2/21 15:04
    **/
    public TsSimpleCode findBigTsSimpleCodeByCodeLevelNo(TsSimpleCode code) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TsSimpleCode t where t.tsCodeType.codeTypeName ='5327' ");
        if(code.getCodeLevelNo().indexOf(".") != -1) {
            sb.append(" and t.codeNo = '").append(code.getCodeLevelNo().substring(0, code.getCodeLevelNo().indexOf("."))).append("'");
         }else {
            sb.append(" and t.codeNo = '").append(code.getCodeNo()).append("'");
        }
        List<TsSimpleCode> list = em.createQuery(sb.toString()).getResultList();
        if(CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }

    /**
    * @Description : 根据单位管理编号、所属机构ID、RID获取仪器个数
    * @MethodAuthor: anjing
    * @Date : 2020/3/13 10:51
    **/
    public Integer findExistsCustNo(String custNo,Integer rid,Integer unitId){
		Map<String, Object> paramMap = new HashMap<String, Object>();
        if (StringUtils.isBlank(custNo)) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT * from TD_ZW_INSTINFO WHERE INST_CODE =:INST_CODE");
		paramMap.put("INST_CODE",custNo.trim());
        if(null != unitId) {
            sb.append(" AND ORG_ID = '").append(unitId).append("'");
        }
        if(null != rid) {
            sb.append(" AND RID != ").append(rid);
        }
		List list = findDataBySqlNoPage(sb.toString(), paramMap);
        if(CollectionUtils.isEmpty(list)){
			return 0;
		}else{
			return list.size();
		}
    }

    /**
    * @Description : 根据出厂编号、所属机构ID、RID获取仪器个数
    * @MethodAuthor: anjing
    * @Date : 2020/3/13 10:53
    **/
    public Integer findExistsPrduNo(String prduNo,Integer rid,Integer unitId){
		Map<String, Object> paramMap = new HashMap<String, Object>();
        if (StringUtils.isBlank(prduNo)) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        sb.append(" SELECT * from TD_ZW_INSTINFO WHERE PRDU_NO =:PRDU_NO");
		paramMap.put("PRDU_NO",prduNo.trim());
        if(null != unitId) {
            sb.append(" AND ORG_ID = '").append(unitId).append("'");
        }
        if(null != rid) {
            sb.append(" AND RID != ").append(rid);
        }
		List list = findDataBySqlNoPage(sb.toString(), paramMap);
		if(CollectionUtils.isEmpty(list)){
			return 0;
		}else{
			return list.size();
		}
    }

    @Transactional(readOnly = true)
    public TdZwInstinfoZzsb findTdZwInstInfo(Integer instId) {
        TdZwInstinfoZzsb instinfo = new TdZwInstinfoZzsb();
        StringBuilder sb = new StringBuilder();
        sb.append("select t from TdZwInstinfoZzsb t where t.rid = ").append(instId);
        List<TdZwInstinfoZzsb> list = em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            instinfo = list.get(0);
            instinfo.getInstSbjcmainList().size();
            instinfo.getInstJcMainList().size();
            if(null != instinfo.getInstSbjcmainList() && instinfo.getInstSbjcmainList().size() > 0){
                for(TdZwInstSbjcmainZzsb instSbjcmain : instinfo.getInstSbjcmainList()){
                    instSbjcmain.getSubList().size();
                }
            }
            if(null != instinfo.getInstJcMainList() && instinfo.getInstJcMainList().size() > 0) {
                for(TdZwInstJcmainZzsb instJcmain : instinfo.getInstJcMainList()) {
                    instJcmain.getSubList().size();
                }
            }
        }
        return instinfo;
    }
    /**
 	 * <p>方法描述：职业健康健康机构按地区备案保存机构信息</p>
 	 * @MethodAuthor qrr,2020年10月15日,saveTjOrgInfo
     * */
    public IZwOrginfoZzsb saveTjOrgInfo(IZwOrginfoZzsb zwOrginfoEntity,List<TdZwTjorgRecordZzsb> tjorgRecords, List<TdZwJsffjgCommAnnexZzsb> annexList) {
    	IZwOrginfoZzsb org = this.saveOrUpdateZwOrginfoEntity(zwOrginfoEntity, null);
    	//删除历史数据
    	this.executeSql("delete from TD_ZW_TJORG_RCD_ITEM where MAIN_ID in (select rid from TD_ZW_TJORG_RECORD where MAIN_ID="+org.getRid()+")");
    	this.executeSql("delete from TD_ZW_TJORG_RECORD where MAIN_ID="+org.getRid());
    	if (!CollectionUtils.isEmpty(tjorgRecords)) {
			for (TdZwTjorgRecordZzsb t : tjorgRecords) {
				t.setFkByMainId(new TdZwTjorginfoZzsb(org.getRid()));
				t.setFkByZoneId(new TsZone(t.getZoneId()));
				List<String> obj = t.getServiceObj();
				t.getRcdItems().clear();
				for (String val : obj) {
					TdZwTjorgRcdItemZzsb item = new TdZwTjorgRcdItemZzsb();
					item.setFkByMainId(t);
					item.setFkByItemId(new TsSimpleCode(new Integer(val)));
					preInsert(item);
					t.getRcdItems().add(item);
				}
				preInsert(t);
				this.upsertEntity(t);
			}
		}
        doSaveJsffjgCommAnnexAction(zwOrginfoEntity, annexList);
		doSaveZoneApproves(zwOrginfoEntity);
    	return org;
    }
    /**
 	 * <p>方法描述：查询职业健康检查机构的备案信息</p>
 	 * @MethodAuthor qrr,2020年10月15日,findTjorgRecords
     * */
    public List<TdZwTjorgRecordZzsb> findTjorgRecords(Integer mainId) {
    	if (null==mainId) {
			return null;
		}
    	StringBuffer sb = new StringBuffer();
        sb.append(" SELECT T FROM TdZwTjorgRecordZzsb T WHERE T.fkByMainId.rid = '").append(mainId).append("'");
        sb.append(" order by T.stateMark,T.fkByZoneId.zoneGb,T.certDate DESC");
        List<TdZwTjorgRecordZzsb> list = this.findByHql(sb.toString(), TdZwTjorgRecordZzsb.class);
    	if (!CollectionUtils.isEmpty(list)) {
			for (TdZwTjorgRecordZzsb t : list) {
				t.getRcdItems().size();
			}
		}
    	return list;
	}
    /**
 	 * <p>方法描述：人员汇总 </p>
	 * 0：健康检查资质；1：放射卫生技术服务；2：诊断资质；3：职业卫生技术服务
 	 * @MethodAuthor qrr,2020年10月16日,findPsnTypeSummary
     * */
    public List<Object[]> findPsnTypeSummary(Integer type,Integer orgId) {
		return this.findPsnTypeSummaryOrderByCountNum(type, orgId, false);
	}

	/**
	 * <p>方法描述： 人员汇总 - 增加按数量倒序 </p>
	 * @param type 0：健康检查资质；1：放射卫生技术服务；2：诊断资质；3：职业卫生技术服务
	 * @param orgId 资质机构rid
	 * @param ifCountDesc 是否优先按数量倒序排序 true是
	 * @MethodAuthor： pw 2022/11/2
	 **/
	public List<Object[]> findPsnTypeSummaryOrderByCountNum(Integer type,Integer orgId, boolean ifCountDesc){
		if (null == type || null == orgId) {
			return null;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("SELECT T.RID,T.CODE_NAME,COUNT(CASE WHEN T4.RID IS NOT NULL THEN T3.rid ELSE NULL END) AS COUNTNUM ");
		sql.append(" FROM TS_SIMPLE_CODE T");
		sql.append(" LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID");
		sql.append(" LEFT JOIN TD_ZW_PSN_TYPE T2 ON T2.PSN_TYPE = T.rid and T2.ZZJG_TYPE=").append(type).append(" ");
		sql.append(" LEFT JOIN TD_ZW_PSNINFO T3 ON T3.RID= T2.MAIN_ID");
		if (0==type) {
			sql.append(" LEFT JOIN TD_ZW_TJORGPSNS T4 ON T4.EMP_ID= T3.RID AND T4.ON_DUTY = 1");
			sql.append(" AND T4.ORG_ID =").append(orgId);
		}else if(1 == type){
			sql.append(" LEFT JOIN TD_ZW_SRVORGPSNS T4 ON T4.EMP_ID= T3.RID AND T4.ON_DUTY = 1");
			sql.append(" AND T4.ORG_ID =").append(orgId);
		}else if(2 == type){
			sql.append(" LEFT JOIN TD_ZW_DIAGPSNS T4 ON T4.EMP_ID= T3.RID AND T4.ON_DUTY = 1");
			sql.append(" AND T4.ORG_ID =").append(orgId);
		}else if(3 == type){
			sql.append(" LEFT JOIN TD_ZW_OCCHETH_PSNS T4 ON T4.EMP_ID= T3.RID AND T4.ON_DUTY = 1");
			sql.append(" AND T4.ORG_ID =").append(orgId);
		}
		sql.append(" WHERE T1.code_type_name = '5308' and t.IF_REVEAL =1");
		//CODE_DESC为空的时候 是全部资质都满足 oracle中字符串位置从1开始
		sql.append(" and instr(nvl(T.CODE_DESC,'").append(type).append("') , '").append(type).append("', 1) > 0 ");
		sql.append(" GROUP BY t.rid,t.CODE_NAME,t.num,t.CODE_NO ");
		if(ifCountDesc){
			sql.append(" ORDER BY COUNTNUM DESC,t.num,t.CODE_NO ");
		}else{
			sql.append(" ORDER BY t.num,t.CODE_NO ");
		}
		return this.findSqlResultList(sql.toString());
	}

	public void institutionSaveOrUpdate(List<TdZwTjorgQualAnnexZzsb> saveList ,List<TdZwTjorgQualAnnexZzsb> updateList,
										List<TdZwTjorgQualAnnexZzsb> deleteList){
    	if(null != deleteList && deleteList.size() > 0){
    		for(TdZwTjorgQualAnnexZzsb tmp : deleteList){
    			this.delete(TdZwTjorgQualAnnexZzsb.class,tmp.getRid());
			}
		}
    	if(null != saveList && saveList.size() > 0){
    		this.saveBatchObjs(saveList);
		}
    	if(null != updateList && updateList.size() > 0){
    		this.updateBatchObjs(updateList);
		}
	}



	@Transactional(readOnly = true)
    public List<Object[]> findExportOccHethList(String searchZoneCode,String orgName,Date rptDateSearchStart,Date rptDateSearchEnd
											,String searchUnitId,String jsfwTreeCode,String manageNo){
		Map<String, Object> paramMap = new HashMap<String, Object>();
		StringBuffer sql = new StringBuffer();
		sql.append("select  t1.MANAGE_NO, t1 .CRPT_NAME, CASE WHEN T2.ZONE_TYPE > 2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME, '_') + 1)ELSE T2.FULL_NAME END ZONE_NAME, ");
		sql.append(" t3.INSTITUTION_CODE, t3.ADDRESS, t5.code_name as hyname, t6.code_name as jjname,  t3.safety_principal,t3.safephone, t.org_name, t4.code_name, t1.RPT_DATE,t1.RPT_NO ");
		sql.append(" from TD_ZW_OCCHETH_INFO t ");
		sql.append(" left join TD_ZW_OCCHETH_RPT t1 on t.rid = t1.ORG_ID");
		sql.append(" left join ts_zone t2 on t1.zone_id = t2.rid");
		sql.append(" left join TB_TJ_CRPT t3 on t1.CRPT_ID = t3.rid");
		sql.append(" left join ts_simple_code t4 on t1.SORT_ID = t4.rid ");
		sql.append(" left join ts_simple_code t5 on t3.INDUS_TYPE_ID = t5.rid ");
		sql.append(" left join ts_simple_code t6 on t3.ECONOMY_ID = t6.rid ");
		sql.append(" where 1= 1 and t1.state =1 and t2.zone_gb like '").append(ZoneUtil.zoneSelect(searchZoneCode)).append("%'");

		if(StringUtils.isNotBlank(orgName)){
			sql.append(" and t3.CRPT_NAME like :orgName");
			paramMap.put("orgName","%"+orgName+"%");
		}

		if(null != rptDateSearchStart){
			sql.append(" and T1.RPT_DATE >= to_date(:rptDateSearchStart,'YYYY-MM-DD HH24:MI:SS') ");
			paramMap.put("rptDateSearchStart", DateUtils.formatDate(rptDateSearchStart)+" 00:00:00");
		}
		if(null != rptDateSearchEnd){
			sql.append(" and  T1.RPT_DATE <= to_date(:rptDateSearchEnd,'YYYY-MM-DD HH24:MI:SS') ");
			paramMap.put("rptDateSearchEnd", DateUtils.formatDate(rptDateSearchEnd)+" 23:59:59");
		}

		if(StringUtils.isNotBlank(searchUnitId)){
			sql.append(" and t.rid in (").append(searchUnitId).append(")");
		}

		if(StringUtils.isNotEmpty(jsfwTreeCode)){
			sql.append("and T4.rid in (").append(jsfwTreeCode).append(") ");
		}

		if(StringUtils.isNotBlank(manageNo)){
			sql.append(" and t1.MANAGE_NO like :manageNo");
			paramMap.put("manageNo","%"+manageNo+"%");
		}
		sql.append(" order by t1.MANAGE_NO desc");

		List<Object[]> list = findDataBySqlNoPage(sql.toString(), paramMap);
		return  list;
	}

	/**
	 * @MethodName: updateOrgInfoLastDate
	 * @Description:
	 * @Param: [rid]
	 * @Return: void
	 * @Author: maox
	 * @Date: 2020-11-17
	**/
	public void updateOrgInfoLastDate(Integer rid){
		String sql ="update TD_ZW_TJORGINFO set LAST_SMT_DATE =to_date('"+DateUtils.formatDate(new Date())+"','yyyy-mm-dd') where rid ="+rid;
		super.executeSql(sql);
	}
	/**
 	 * <p>方法描述：职业卫生技术服务机构保存机构信息</p>
 	 * @MethodAuthor qrr,2021年3月1日,saveOccHethOrgInfo
	 * */
    public IZwOrginfoZzsb saveOccHethOrgInfo(IZwOrginfoZzsb zwOrginfoEntity, List<TdZwOcchethItemsShow> itemsShows, List<TdZwJsffjgCommAnnexZzsb> annexList) {
    	IZwOrginfoZzsb org = this.saveOrUpdateZwOrginfoEntity(zwOrginfoEntity, null);
    	//删除历史数据
    	this.executeSql("delete from TD_ZW_OCCHETH_ITEMS where ORG_ID="+org.getRid());
    	if (!CollectionUtils.isEmpty(itemsShows)) {
			for (TdZwOcchethItemsShow t : itemsShows) {
				if (!CollectionUtils.isEmpty(t.getSelectItems())) {
					for (String s : t.getSelectItems()) {
						TdZwOcchethItemsZzsb occhethItems = new TdZwOcchethItemsZzsb();
						occhethItems.setFkByOrgId(new TdZwOcchethInfoZzsb(org.getRid()));
						occhethItems.setItemCode(Short.valueOf(s));
						preInsert(occhethItems);
						this.upsertEntity(occhethItems);
					}
				}
			}
    	}
        doSaveJsffjgCommAnnexAction(zwOrginfoEntity, annexList);
    	return org;
    }

    public List<TdZwJsffjgCommAnnexZzsb> findTdZwJsffjgCommAnnexListByOrgId(String orgId) {
        if(StringUtils.isBlank(orgId)) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        sb.append(" select t from TdZwJsffjgCommAnnexZzsb t where t.fkByOrgId.rid = '").append(orgId).append("'");
        return this.findByHql(sb.toString(), TdZwJsffjgCommAnnexZzsb.class);
    }

    /**
    * @Description : 执行保存技术服务机构公用资质附件
    * @MethodAuthor: anjing
    * @Date : 2021/3/12 9:28
    **/
    @Transactional(readOnly = false)
    private void doSaveJsffjgCommAnnexAction(IZwOrginfoZzsb zwOrginfoEntity, List<TdZwJsffjgCommAnnexZzsb> annexList) {
        StringBuilder sb = new StringBuilder();
        sb.append("DELETE FROM TD_ZW_JSFFJG_COMM_ANNEX T WHERE T.ORG_ID =").append(zwOrginfoEntity.getTsUnit().getRid());
        super.executeSql(sb.toString());
        if(!CollectionUtils.isEmpty(annexList)) {
            for(TdZwJsffjgCommAnnexZzsb annex : annexList) {
                annex.setRid(null);
            }
            super.saveBatchObjs(annexList);
        }
    }

	/**
	 * 更新批准的执业区域
	 *
	 * @param zwOrginfoEntity 主表
	 */
	public void doSaveZoneApproves(IZwOrginfoZzsb zwOrginfoEntity) {
		String ifZoneApproveString = "";
		try {
			ifZoneApproveString = PropertyUtils.getValueWithoutException("zzsb.ifZoneApprove");
		} catch (Exception e) {
			e.printStackTrace();
		}
		if (!"1".equals(ifZoneApproveString) || !(zwOrginfoEntity instanceof TdZwTjorginfoZzsb)) {
			return;
		}
		if (zwOrginfoEntity.getRid() == null) {
			return;
		}
		super.executeSql("DELETE FROM TD_ZW_TJORG_OUT_RANGE T WHERE T.ORG_ID = " + zwOrginfoEntity.getRid());
		if (StringUtils.isBlank(zwOrginfoEntity.getZoneApproves())) {
			return;
		}
		String zoneApproves = StringUtils.objectToString(zwOrginfoEntity.getZoneApproves());
		List<String> zoneApproveStrList = StringUtils.string2list(zoneApproves, ",");
		if (CollectionUtils.isEmpty(zoneApproveStrList)) {
			return;
		}
		List<TdZwTjorgOutRangeZzsb> zoneApproveList = new ArrayList<>();
		for (String s : zoneApproveStrList) {
			try {
				TdZwTjorgOutRangeZzsb zoneApprove = new TdZwTjorgOutRangeZzsb();
				zoneApprove.setFkByOrgId(new TdZwTjorginfoZzsb(zwOrginfoEntity.getRid()));
				zoneApprove.setFkByZoneId(new TsZone(Integer.parseInt(s)));
				zoneApprove.setCreateDate(new Date());
				zoneApprove.setCreateManid(Global.getUser().getRid());
				zoneApproveList.add(zoneApprove);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		if (!CollectionUtils.isEmpty(zoneApproveList)) {
			for (TdZwTjorgOutRangeZzsb zoneApprove : zoneApproveList) {
				zoneApprove.setRid(null);
			}
			super.saveBatchObjs(zoneApproveList);
		}
	}

    
    /**
     * @Description: 通过检查机构rid 获取对应的外检车辆信息
     *
     * @MethodAuthor pw,2021年06月25日
     */
	@Transactional(readOnly = true)
    public List<TdZwTjorgOutcarZzsb> findTjorgOutcarZzsbListByMainRid(Integer rid){
    	if(null == rid){
    		return null;
		}
		List<TdZwTjorgOutcarZzsb> resultList = null;
    	String hql = " from TdZwTjorgOutcarZzsb T where T.fkByMainId.rid="+rid;
		resultList = em.createQuery(hql).getResultList();
		if(!CollectionUtils.isEmpty(resultList)){
			for(TdZwTjorgOutcarZzsb outcarZzsb : resultList){
				if(null != outcarZzsb.getOutcInstZzsbList()){
					outcarZzsb.getOutcInstZzsbList().size();
				}
			}
		}
    	return resultList;
	}
	
	
	/**
	 * <p>方法描述：查询是否有在备案中的流程</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-04-22
	 **/
	public List<Object[]> findOrgInfoApply(){
		StringBuilder sql=new StringBuilder();
		sql.append(" select T.rid,T.STATE ");
		sql.append(" from TD_ZW_TJORGINFO_APPLY T ");
		sql.append(" left join TS_SIMPLE_CODE T1 on T.QUAL_TYPE_ID=T1.RID ");
		sql.append(" left join TS_SIMPLE_CODE T2 on T.APPLY_TYPE_ID=T2.RID ");
		sql.append(" where T1.EXTENDS1='1' and T2.EXTENDS1='2' and ORG_ID=").append(Global.getUser().getTsUnit().getRid());
		sql.append(" order by T.CREATE_DATE desc ");
		return em.createNativeQuery(sql.toString()).getResultList();
	}

	public String findUnitByOrgId(String orgId) {
		Map<String, Object> paramMap = new HashMap<>(16);
		String sql = "SELECT ORG_ID FROM TD_ZW_TJORGINFO WHERE RID = :orgId";
		paramMap.put("orgId", orgId);
		List<Object> sqlResultList = CollectionUtil.castList(Object.class, findDataBySqlNoPage(sql, paramMap));
		if (ObjectUtil.isEmpty(sqlResultList)) {
			return "";
		}
		return StringUtils.objectToString(sqlResultList.get(0));
	}

	public List<Object[]> findServiceOverview(String unitRid) {
		Map<String, Object> paramMap = new HashMap<>(16);
		String sql = " WITH BHK_CRPT AS (SELECT B.CRPT_ID, B.RID BHK_RID, SC.RID CODE_RID " +
				"                  FROM TD_TJ_BHK B " +
				"                           INNER JOIN TB_TJ_SRVORG O ON B.BHKORG_ID = O.RID AND O.REG_ORGID = :unitRid " +
				"                           INNER JOIN TB_TJ_CRPT C ON B.CRPT_ID = C.RID AND C.INTER_PRC_TAG = '1' " +
				"                           INNER JOIN TD_TJ_MHKRST M ON M.BHK_ID = B.RID " +
				"                           INNER JOIN TS_SIMPLE_CODE SC ON M.BHKRST_ID = SC.RID " +
				"                  WHERE B.BHK_TYPE IN ('3', '4') " +
				"                    AND B.BHK_DATE >= TO_DATE('" + DateUtils.getYear() + "-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')), " +
				"     CRPT AS (SELECT 1 TYPE, '' NAME, COUNT(1) SUM, 0 NUM FROM (SELECT CRPT_ID FROM BHK_CRPT GROUP BY CRPT_ID)), " +
				"     BHK AS (SELECT 2 TYPE, '' NAME, COUNT(1) SUM, 0 NUM FROM BHK_CRPT), " +
				"     SBFS AS (SELECT 3 TYPE, '' NAME, COUNT(1) SUM, 0 NUM FROM TB_TJ_SRVORG O WHERE O.STOP_TAG = '1' AND O.REG_CODE IS NOT NULL AND O.REG_ORGID = :unitRid), " +
				"     TJJL AS ( " +
				"     SELECT 4 TYPE,CODE_NAME, SUM(NUM) SUM, RN FROM (SELECT SC.CODE_NAME, DECODE(BC.BHK_RID, NULL, 0, 1) NUM, SC.NUM RN " +
				"                                                      FROM TS_SIMPLE_CODE SC " +
				"                                                               LEFT JOIN TS_CODE_TYPE CT ON SC.CODE_TYPE_ID = CT.RID " +
				"                                                               LEFT JOIN BHK_CRPT BC ON SC.RID = BC.CODE_RID " +
				"                                                      WHERE SC.IF_REVEAL = 1 AND CT.CODE_TYPE_NAME = '5005' " +
				"                                                      GROUP BY SC.CODE_NAME, SC.NUM, BC.BHK_RID) " +
				"                                                GROUP BY CODE_NAME, RN " +
				"              ) " +
				"SELECT * FROM (SELECT * FROM CRPT UNION SELECT * FROM BHK UNION SELECT * FROM SBFS UNION SELECT * FROM TJJL) ORDER BY TYPE, NUM ";
		paramMap.put("unitRid", unitRid);
		return CollectionUtil.castList(Object[].class, findDataBySqlNoPage(sql, paramMap));
	}
	/**
	 *  <p>方法描述：清空该资质类型的预警信息
	 *  type:资质类型
	 *  busId:业务表rid</p>
	 * @MethodAuthor hsj 2022-10-24 13:56
	 */
	public void deleteTdZwOrgWarnByBusId(Integer type, Integer busId) {
		if(null == type || null == busId){
			return;
		}
		String str = "DELETE FROM  TD_ZW_ORG_WARN WHERE BUS_TYPE = "+type+" AND BUS_ID = "+busId;
		super.executeSql(str,null);
	}
	/**
	 *  <p>方法描述：获取预警配置信息:查询当前资质类型 服务项目为空+当前资质所选的服务项目
	 *  busType：资质类型
	 *  warnType：预警类型
	 *  itemIds：服务项目 </p>
	 * @MethodAuthor hsj 2022-10-24 14:18
	 */
	public List<Object[]> getOrgWarn(Integer busType, Integer warnType, List<String> itemIds) {
		if(null == busType){
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T.RID AS subId,T.WARN_TYPE_ID,T1.RID AS mainId,T1.BUS_TYPE ,T1.WARN_TYPE,T1.ITEM_ID ");
		sql.append(" FROM  TB_ZW_ORG_WARN_SUB  T ");
		sql.append(" LEFT JOIN  TB_ZW_ORG_WARN_CONFIG T1 ON T.MAIN_ID = T1.RID ");
		sql.append(" WHERE  T1.BUS_TYPE =").append(busType);
		if(null != warnType){
			sql.append(" AND T1.WARN_TYPE = ").append(warnType);
		}
		if(!CollectionUtils.isEmpty(itemIds)){
			sql.append(" AND (  T1.ITEM_ID  is null OR T1.ITEM_ID in ( ").append(StringUtils.list2string(itemIds,",")).append(" ) )");
		}
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/**
	 *  <p>方法描述：获取人员属性
	 *  busType：资质类型
	 *  busId:业务表rid
	 *  人员属性：码表5038
	 *  </p>
	 * @MethodAuthor hsj 2022-10-24 15:09
	 */
	public List<BigDecimal> getPsnType(Integer busType, Integer busId) {
		if(null == busType || null == busId){
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT DISTINCT(T.PSN_TYPE) FROM TD_ZW_PSN_TYPE T  ");
		if(busType == 0){
			//职业健康检查机构资质
			sql.append(" LEFT JOIN  TD_ZW_TJORGPSNS T1 ON T1.EMP_ID = T.MAIN_ID ");
		}
		sql.append("WHERE T1.ON_DUTY = 1  AND T.ZZJG_TYPE = ").append(busType);
		sql.append(" AND T1.ORG_ID =").append(busId);
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/**
	 *  <p>方法描述：获取仪器类型 -仪器类型非必填需排除为空的
	 * 	 *  busType：资质类型
	 * 	 *  busId:业务表rid
	 * 	 *  仪器类型：码表5024/5042</p>
	 * @MethodAuthor hsj 2022-10-24 16:53
	 */
	public List<BigDecimal> getInstinfoType(Integer busType, Integer busId) {
		if(null == busType || null == busId){
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT DISTINCT(T.INST_KIND_DETAL_ID) FROM TD_ZW_INSTINFO T  ");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T.INST_STATE_ID = T1.RID  ");
		if(busType == 0){
			//职业健康检查机构资质
			sql.append(" LEFT JOIN TD_ZW_TJINST T2 ON T2.INST_ID = T.RID ");
		}
		sql.append("WHERE T1.EXTENDS1 != 2 AND T.INST_KIND_DETAL_ID IS NOT NULL ");
		sql.append(" AND T2.ORG_ID =").append(busId);
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/**
	 *  <p>方法描述：获取当前资质服务项目-
	 *  busType：资质类型
	 *   busId:业务表rid
	 *   服务项目码表：
	 *   职业健康检查机构资质：5018
	 *   放射：5019
	 *   诊断：5020
	 *   职业卫生技术服务：5320
	 *   </p>
	 * @MethodAuthor hsj 2022-10-25 9:35
	 */
	private List<String> getItems(Integer busType, Integer busId) {
		if(null == busType || null == busId){
			return null;
		}
		StringBuffer sql = new StringBuffer();
		if(busType == 0){
			//职业健康检查机构资质 --备案服务项目
			sql.append(" SELECT DISTINCT(T.ITEM_ID)  FROM TD_ZW_TJORG_RCD_ITEM T ");
			sql.append(" LEFT JOIN TD_ZW_TJORG_RECORD T1 ON T.MAIN_ID = T1.RID ");
			sql.append(" WHERE T1.MAIN_ID =").append(busId);
		}
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/**
	 *  <p>方法描述：获取当前资质检查项目--职业健康检查机构资质
	 *  检查项目码表：5015</p>
	 * @MethodAuthor hsj 2022-10-25 9:45
	 */
	private List<BigDecimal> getItemType(Integer busId) {
		if(null == busId){
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T.ITEM_ID FROM TD_ZW_TJITEMS T ");
		sql.append(" WHERE T.ORG_ID =").append(busId);
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/**
	 *  <p>方法描述：获取质量管理体系-有--职业健康检查机构资质
	 *  制度码表：5507</p>
	 * @MethodAuthor hsj 2022-10-25 9:54
	 */
	private List<BigDecimal> getQualAnnexType(Integer busId) {
		if(null == busId){
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T.RULE_ID FROM TD_ZW_TJORG_QUAL_ANNEX T ");
		sql.append(" WHERE T.IF_HAS = 1 AND T.MAIN_ID =").append(busId);
		return em.createNativeQuery(sql.toString()).getResultList();
	}
	/**
	 *  <p>方法描述：zzsb.ifCheckOrg=1 时 存在审核功能
	 *  zwOrgType：资质类型
	 *  zwOrgId :资质</p>
	 * @MethodAuthor hsj 2022-10-24 17:37
	 */
	public void toSaveAndAudit(Integer zwOrgType, Integer zwOrgId) {
		//清空预警表信息--四类资质
		this.deleteTdZwOrgWarnByBusId(zwOrgType,zwOrgId);
		//当前资质服务项目
		List<String> itemIds = this.getItems(zwOrgType,zwOrgId);
		//查询配置(所有的)
		List<Object[]> list = this.getOrgWarn(zwOrgType,null,itemIds);
		if(CollectionUtils.isEmpty(list)){
			//无需预警
			return;
		}
		//预警表封装
		List<OrgWarnPO> orgWarns = this.getOrgWarnPO(list);
		//key:预警类型 ，预警信息
		Map<Integer,List<OrgWarnPO>> orgWarnMap = new HashMap<>();
		GroupUtil.listGroup2Map(orgWarns,orgWarnMap,OrgWarnPO.class,"getWarnType");
		//人员处理--四类资质
		this.dealOrgWarns(zwOrgType,zwOrgId,orgWarnMap,1);
		//仪器处理-四类资质
		this.dealOrgWarns(zwOrgType,zwOrgId,orgWarnMap,2);
		//检查项目--职业健康检查机构资质
		if(zwOrgType == 0){
			this.dealOrgWarns(0,zwOrgId,orgWarnMap,3);
		}
		//质量管理体系--参数配置--职业健康检查机构资质
		String ifQualAnnex = PropertyUtils.getValueWithoutException("zzsb.ifQualAnnex");
		if(zwOrgType == 0 && "1".equals(ifQualAnnex) ){
			this.dealOrgWarns(0,zwOrgId,orgWarnMap,4);
		}
	}
	/**
	 *  <p>方法描述：各类型预警信息</p>
	 * @MethodAuthor hsj 2022-10-25 14:55
	 */
	private void dealOrgWarns(Integer zwOrgType, Integer zwOrgId, Map<Integer, List<OrgWarnPO>> orgWarnMap, Integer type) {
		if(null == type){
			return;
		}
		List<OrgWarnPO> orgWarns = orgWarnMap.get(type);
		if(CollectionUtils.isEmpty(orgWarns)){
			return;
		}
		List<BigDecimal> list  = new ArrayList<>();
		if(1 == type){
			//人员信息
			list = this.getPsnType(zwOrgType,zwOrgId);
		}else if(2 == type){
			//仪器信息
			list = this.getInstinfoType(zwOrgType,zwOrgId);
		}else if(3 == type){
			//检查项目
			list = this.getItemType(zwOrgId);
		}else if(4 == type){
			//质量管理体系-有
			list = this.getQualAnnexType(zwOrgId);
		}
		this.dealOrgWarn(zwOrgType,zwOrgId,list,orgWarns);
	}

	/**
	 *  <p>方法描述：预警数据封装及保存</p>
	 * @MethodAuthor hsj 2022-10-24 15:24
	 */
	private void dealOrgWarn(Integer zwOrgType, Integer zwOrgId,List<BigDecimal> busList, List<OrgWarnPO> orgWarns) {
		//业务表封装(去重)
		Set<Integer> orgInfos = new HashSet<>();
		if(!CollectionUtils.isEmpty(busList)){
			for(BigDecimal str : busList){
				orgInfos.add(Integer.valueOf(str.toString()));
			}
		}
		//根据预警主表分组
		Map<Integer,List<OrgWarnPO>> OrgWarnMap = new HashMap<>();
		GroupUtil.listGroup2Map(orgWarns,OrgWarnMap,OrgWarnPO.class,"getWarnRid");
		List<TdZwOrgWarn> tdZwOrgWarns = new ArrayList<>();
		for (Map.Entry<Integer, List<OrgWarnPO>> entry : OrgWarnMap.entrySet()) {
			//遍历预警配置表
			Integer warnId = entry.getKey();
			List<OrgWarnPO> orgWarnPOS =  entry.getValue();
			Set<Integer> orgWarn = new HashSet<>();
			for(OrgWarnPO orgWarnPO:orgWarnPOS ){
				orgWarn.add(orgWarnPO.getSimpleCodeId());
			}
			//当 Collection 对象中不存在公共元素时返回true，否则返回false
			if(Collections.disjoint(orgInfos,orgWarn)){
				//需要预警
				TdZwOrgWarn tdZwOrgWarn = new TdZwOrgWarn();
				tdZwOrgWarn.setBusType(zwOrgType);
				tdZwOrgWarn.setBusId(zwOrgId);
				tdZwOrgWarn.setFkByWarnId(new TbZwOrgWarnConfig(warnId));
				preInsert(tdZwOrgWarn);
				tdZwOrgWarns.add(tdZwOrgWarn);
			}
		}
		if(!CollectionUtils.isEmpty(tdZwOrgWarns)){
			//直接保存
			this.saveBatchObjs(tdZwOrgWarns);
		}
	}

	/**
	 *  <p>方法描述：预警信息查询之封装</p>
	 * @MethodAuthor hsj 2022-10-24 15:26
	 */
	private List<OrgWarnPO> getOrgWarnPO(List<Object[]> list) {
		List<OrgWarnPO> orgWarns = new ArrayList<>();
		for(Object[] o:list){
			OrgWarnPO orgWarn = new OrgWarnPO();
			//预警子表rid
			orgWarn.setWarnSubRid(o[0] == null ? null : Integer.valueOf( o[0].toString()));
			//预警子表-预警值
			orgWarn.setSimpleCodeId(o[1] == null ? null : Integer.valueOf( o[1].toString()));
			//预警表rid
			orgWarn.setWarnRid(o[2] == null ? null : Integer.valueOf( o[2].toString()));
			//资质类型
			orgWarn.setBusType(o[3] == null ? null : Integer.valueOf( o[3].toString()));
			//预警类型
			orgWarn.setWarnType(o[4] == null ? null : Integer.valueOf( o[4].toString()));
			//服务项目rid
			orgWarn.setItemId(o[5] == null ? null : Integer.valueOf( o[5].toString()));
			orgWarns.add(orgWarn);
		}
		return orgWarns;
	}

	/**
	 * <p>方法描述： </p>
	 *  0：健康检查资质；1：放射卫生技术服务；2：诊断资质；3：职业卫生技术服务
	 * @MethodAuthor： pw 2022/11/2
	 **/
	public List<Object[]> findInstTypeSummaryOrderByCountNum(Integer type,Integer orgId, boolean ifCountDesc){
		if (null == type || null == orgId) {
			return null;
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT t3.RID ,t3.CODE_NAME ,COUNT(t.RID) AS COUNTNUM ");
		if (0==type) {
			sql.append(" FROM TD_ZW_TJINST t INNER JOIN TD_ZW_INSTINFO t1 ON t.INST_ID = t1.RID ");
			sql.append(" AND t.ORG_ID =").append(orgId);
		}else if(1 == type){
			//放射的仪器字段取ORG_ID
			sql.append(" FROM TD_ZW_ORGINST t INNER JOIN TD_ZW_INSTINFO t1 ON t.ORG_ID = t1.RID ");
			sql.append(" AND t.INST_ID =").append(orgId);
		}else if(2 == type){
			sql.append(" FROM TD_ZW_DIAGINST t INNER JOIN TD_ZW_INSTINFO t1 ON t.INST_ID = t1.RID ");
			sql.append(" AND t.ORG_ID =").append(orgId);
		}else if(3 == type){
			sql.append(" FROM TD_ZW_OCCHETH_INST t INNER JOIN TD_ZW_INSTINFO t1 ON t.INST_ID = t1.RID ");
			sql.append(" AND t.ORG_ID =").append(orgId);
		}
		sql.append(" INNER JOIN TS_SIMPLE_CODE t2 ON t1.INST_STATE_ID = t2.RID  ");
		sql.append(" INNER JOIN TS_SIMPLE_CODE t3 ON t1.INST_KIND_DETAL_ID = t3.RID  ");
		sql.append(" WHERE t2.EXTENDS1 IN ('1','3') ");
		sql.append(" GROUP BY t3.RID ,t3.NUM ,t3.CODE_LEVEL_NO ,t3.CODE_NAME ");
		if(ifCountDesc){
			sql.append(" ORDER BY COUNTNUM DESC ,t3.NUM ,t3.CODE_LEVEL_NO ");
		}else{
			sql.append(" ORDER BY t3.NUM ,t3.CODE_LEVEL_NO ");
		}
		return this.findSqlResultList(sql.toString());
	}


	/**
	 * <p>方法描述：根据身份证号查询培训履历</p>
	 * @MethodAuthor： yzz
	 * @Date：2022-11-18
	 **/
	public Map<String, List<TrainHisPO>> findTrainHisByIdc(String idc) {
		//此处是开辟了先河，前无古人，后应该有来者，通过判断是否有表结构来判断是否有在线考试的功能，绝绝子
		Map<String, List<TrainHisPO>> trainHisMap = new HashMap<>();
		StringBuilder sb = new StringBuilder();
		sb.append("select count(*) from user_tables t where table_name= upper('TD_ZW_EXAM_PSN')");
		int examCount = this.findCountBySql(sb.toString());
		if (examCount>0) {
			sb = new StringBuilder();
			sb.append(" select T4.IDC_CARD,T1.QUAL_TYPE_ID,T3.EXTENDS5,T.CERT_NO,listagg(T2.CODE_NAME,'，') within group ( order by T2.NUM) as names,to_char(T.CERT_START_DATE,'yyyy-mm-dd')||' ~ '||to_char(T.CERT_END_DATE,'yyyy-mm-dd') ");
			sb.append(" from TD_ZW_EXAM_PSN T ");
			sb.append(" left join TD_ZW_EXAM_SUBJECT T1 on T1.MAIN_ID=T.RID ");
			sb.append(" left join TD_ZW_PSNINFO T4 on T.PRO_PSN_ID=T4.RID ");
			sb.append(" left join TS_SIMPLE_CODE T2 on T1.RANGE_TYPE_ID=T2.RID ");
			sb.append(" left join TS_SIMPLE_CODE T3 on T1.QUAL_TYPE_ID=T3.RID ");
			sb.append(" where T.STATE=4 and T.CERT_NO is not null and T1.IF_HG=1 ");
			sb.append(" and T4.IDC_CARD in('").append(idc).append("')");
			sb.append(" group by T4.IDC_CARD,T1.QUAL_TYPE_ID,T3.EXTENDS5,T.CERT_NO,T.CERT_START_DATE,T.CERT_END_DATE ");
			sb.append(" order by T.CERT_NO desc ");
			List<Object[]> examPsnList = em.createNativeQuery(sb.toString()).getResultList();
			if (!CollectionUtils.isEmpty(examPsnList)) {
				for (Object[] objects : examPsnList) {
					TrainHisPO trainHisPO = new TrainHisPO();
					trainHisPO.setCertNo(objects[3] != null ? objects[3].toString() : null);
					trainHisPO.setCertDate(objects[5] != null ? objects[5].toString() : null);
					if (objects[2] == null) {
						trainHisPO.setRangeTypes(null);
					} else {
						trainHisPO.setRangeTypes(objects[4] != null ? objects[4].toString() : null);
					}
					if (objects[0] != null && objects[1] != null && trainHisMap.containsKey(objects[0].toString() + "&" + objects[1].toString())) {
						trainHisMap.get(objects[0].toString() + "&" + objects[1].toString()).add(trainHisPO);
					} else {
						List<TrainHisPO> newTrainHisPOList = new ArrayList<>();
						newTrainHisPOList.add(trainHisPO);
						trainHisMap.put(objects[0].toString() + "&" + objects[1].toString(), newTrainHisPOList);
					}
				}
			}
		}
		return trainHisMap;
	}

	/**
	 * 根据支撑机构RID获取论文情况
	 *
	 * @param mainRid 支撑机构RID
	 * @return 论文情况
	 */
	public List<TdZwSupportThesis> findAllSupportThesis(Integer mainRid) {
		String hql = "select t from TdZwSupportThesis t where t.fkByMainId.rid = " + mainRid + " order by t.year desc, t.rid desc ";
		List<TdZwSupportThesis> thesisList = super.findByHql(hql, TdZwSupportThesis.class);
		if (CollectionUtils.isEmpty(thesisList)) {
			thesisList = new ArrayList<>();
		}
		for (TdZwSupportThesis thesis : thesisList) {
			thesis.getThesisPsnList().size();
		}
		return thesisList;
	}

	/**
	 * 根据支撑机构RID获取专著情况
	 *
	 * @param mainRid 支撑机构RID
	 * @return 专著情况
	 */
	public List<TdZwSupportMomograph> findAllSupportMomograph(Integer mainRid) {
		String hql = "select t from TdZwSupportMomograph t where t.fkByMainId.rid = " + mainRid + " order by t.year desc, t.rid desc ";
		List<TdZwSupportMomograph> momographList = super.findByHql(hql, TdZwSupportMomograph.class);
		if (CollectionUtils.isEmpty(momographList)) {
			momographList = new ArrayList<>();
		}
		for (TdZwSupportMomograph momograph : momographList) {
			momograph.getMomographPsnList().size();
		}
		return momographList;
	}

	/**
	 * 获取当前支撑机构在职人员
	 *
	 * @return 在职人员
	 */
    public List<Object[]> findAllIncumbencyPsnList(Integer mainRid, String psnName) {
		Map<String, Object> paramMap = new HashMap<>();
		String sql = "SELECT P.RID, P.EMP_NAME, P.SEX, P.BIRTHDAY, SC.CODE_NAME AS THETITLE, T.RID AS O " +
				"FROM TD_ZW_SUPPORT_PSNS T INNER JOIN TD_ZW_PSNINFO P ON T.EMP_ID = P.RID " +
				"LEFT JOIN TS_SIMPLE_CODE SC ON P.TITLE_ID = SC.RID " +
				"WHERE T.ON_DUTY = 1 AND T.ORG_ID = :mainRid ";
		paramMap.put("mainRid", mainRid);
		if (StringUtils.isNotBlank(psnName)) {
			sql += " AND P.EMP_NAME like :psnName escape '\\\' ";
			paramMap.put("psnName", "%" + StringUtils.convertBFH(psnName) + "%");
		}
		sql += " ORDER BY T.RID ";
		return CollectionUtil.castList(Object[].class, findDataBySqlNoPage(sql, paramMap));
	}

	/**
	 * 保存论文情况
	 *
	 * @param supportThesis 论文情况
	 */
	public void saveSupportThesis(TdZwSupportThesis supportThesis) {
		if (supportThesis.getYear() == null) {
			supportThesis.setYear(DateUtils.getYearInt());
		}
		supportThesis.setThesisPsnList(new ArrayList<TdZwSupportThesisPsns>());
		for (Integer psnRid : supportThesis.getPsnSelVO().getPsnRidList()) {
			if (psnRid == null) {
				continue;
			}
			TdZwSupportThesisPsns thesisPsns = new TdZwSupportThesisPsns();
			thesisPsns.setFkByMainId(supportThesis);
			thesisPsns.setFkByEmpId(new TdZwPsninfoZzsb(psnRid));
			thesisPsns.setCreateDate(new Date());
			thesisPsns.setCreateManid(Global.getUser().getRid());
			supportThesis.getThesisPsnList().add(thesisPsns);
		}
		upsertEntity(supportThesis);
	}

	/**
	 * 删除论文情况
	 *
	 * @param rid 论文情况rid
	 */
	public void delSupportThesis(Integer rid) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("rid", rid);
		String sql = "DELETE TD_ZW_SUPPORT_THESIS_PSNS WHERE MAIN_ID = :rid";
		executeSql(sql, paramMap);
		sql = "DELETE TD_ZW_SUPPORT_THESIS WHERE RID = :rid";
		executeSql(sql, paramMap);
	}

	/**
	 * 保存专著情况
	 *
	 * @param supportMomograph 专著情况
	 */
    public void saveSupportMomograph(TdZwSupportMomograph supportMomograph) {
        if (supportMomograph.getYear() == null) {
            supportMomograph.setYear(DateUtils.getYearInt());
        }
        supportMomograph.setMomographPsnList(new ArrayList<TdZwSupportMomographPsns>());
        dealMomographPsns(supportMomograph, supportMomograph.getPsnSelVO1().getPsnRidList(), 1);
        dealMomographPsns(supportMomograph, supportMomograph.getPsnSelVO2().getPsnRidList(), 2);
        dealMomographPsns(supportMomograph, supportMomograph.getPsnSelVO3().getPsnRidList(), 3);
        upsertEntity(supportMomograph);
    }

    private void dealMomographPsns(TdZwSupportMomograph supportMomograph, List<Integer> psnRidList, int type) {
        for (Integer psnRid : psnRidList) {
            if (psnRid == null) {
                continue;
            }
            TdZwSupportMomographPsns momographPsns = new TdZwSupportMomographPsns();
            momographPsns.setFkByMainId(supportMomograph);
            momographPsns.setEmpType(type);
            momographPsns.setFkByEmpId(new TdZwPsninfoZzsb(psnRid));
            momographPsns.setCreateDate(new Date());
            momographPsns.setCreateManid(Global.getUser().getRid());
            supportMomograph.getMomographPsnList().add(momographPsns);
        }
    }

	/**
	 * 删除专著情况
	 *
	 * @param rid 专著情况rid
	 */
	public void delSupportMomograph(Integer rid) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("rid", rid);
		String sql = "DELETE TD_ZW_SUPPORT_MOMOGRAPH_PSNS WHERE MAIN_ID = :rid";
		executeSql(sql, paramMap);
		sql = "DELETE TD_ZW_SUPPORT_MOMOGRAPH WHERE RID = :rid";
		executeSql(sql, paramMap);
	}
	/**
	 * <p>方法描述： 通过支撑机构主表rid获取支撑机构学术培训信息 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	@Transactional(readOnly = true)
	public List<Object[]> findTdZwSupportTrain(Integer mainId){
		if(null == mainId){
			return Collections.EMPTY_LIST;
		}
		StringBuffer sqlBuffer = new StringBuffer();
		sqlBuffer.append(" SELECT t.\"YEAR\", t.TRAIN_ID, t.PARTAKE_NUMBER, t.EVIDENCE_FILE_PATH, ''  ")
				.append(" FROM  TD_ZW_SUPPORT_TRAIN t  ")
				.append(" INNER JOIN TS_SIMPLE_CODE t1 ON t.TRAIN_ID = t1.RID ")
				.append(" WHERE t.MAIN_ID =").append(mainId)
				.append(" ORDER BY t.\"YEAR\" DESC ");
		return this.findDataBySqlNoPage(sqlBuffer.toString(), null);
	}

	/**
	 * <p>方法描述： 保存支撑机构学术培训信息 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	public void saveTdZwSupportTrain(List<TdZwSupportTrain> trainList){
		if(CollectionUtils.isEmpty(trainList)){
			return;
		}
		Integer year = trainList.get(0).getYear();
		Integer mainRid = trainList.get(0).getFkByMainId().getRid();
		for(TdZwSupportTrain train : trainList){
			preInsert(train);
		}
		//先删除
		StringBuffer delBuffer = new StringBuffer();
		delBuffer.append(" DELETE FROM TD_ZW_SUPPORT_TRAIN t WHERE t.\"YEAR\" = ")
				.append(year)
				.append(" AND t.MAIN_ID =").append(mainRid);
		this.executeSql(delBuffer.toString(), null);
		//保存
		this.saveBatchObjs(trainList);
	}

	/**
	 * <p>方法描述： 删除支撑机构学术培训信息 </p>
	 * @MethodAuthor： pw 2023/3/30
	 **/
	public void delTdZwSupportTrain(Integer year, Integer mainRid){
		if(null == year || null == mainRid){
			return;
		}
		StringBuffer delBuffer = new StringBuffer();
		delBuffer.append(" DELETE FROM TD_ZW_SUPPORT_TRAIN t WHERE t.\"YEAR\" = ")
				.append(year)
				.append(" AND t.MAIN_ID =").append(mainRid);
		this.executeSql(delBuffer.toString(), null);
	}


	/**
	 *专利列表
	 * @param mainRid
	 * @return
	 */
	public List<Object[]> findpPatentListByMainId(Integer mainRid,Integer positiveType) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select T.RID,T.YEAR,listagg(T2.EMP_NAME,'、') within group ( order by T2.EMP_NAME) as EMP_NAME,T.PATENT_NAME, ");
		sql.append(" T3.CODE_NAME as PATENT_TYPE_NAME,T3.EXTENDS1,T.PATENT_NO,T4.CODE_NAME as STANDARD_NAME,T.SUPPORT_FILE_PATH,T.OTHER_PATENT_TYPE ");
		sql.append(" from TD_ZW_SUPPORT_POSITIVE T ");
		sql.append(" left join TD_ZW_SUPPORT_POSITIVE_PSNS T1 on T1.MAIN_ID=T.RID ");
		sql.append(" left join TD_ZW_PSNINFO T2 on T1.EMP_ID=T2.RID ");
		sql.append(" left join TS_SIMPLE_CODE T3 on T3.RID=T.PATENT_ID ");
		sql.append(" left join TS_SIMPLE_CODE T4 on T4.RID=T.STANDARD_ID ");
		sql.append(" where T.MAIN_ID= ").append(mainRid);
		sql.append(" and POSITIVE_TYPE=").append(positiveType);
		sql.append(" group by T.RID,T.YEAR,T.PATENT_NAME,T3.CODE_NAME,T3.EXTENDS1,T.PATENT_NO,T4.CODE_NAME,T.SUPPORT_FILE_PATH,T.OTHER_PATENT_TYPE ");
		sql.append(" order by T.YEAR desc,T.RID desc ");
		return em.createNativeQuery(sql.toString()).getResultList();
	}

	/**
	 * 保存科研成果情况 、科研成果人员
	 * @param supportPositive
	 */
	public void saveSupportPositive(TdZwSupportPositive supportPositive) {
		if (supportPositive == null) {
			return;
		}
		// 保存
		if (supportPositive.getRid() == null) {
			supportPositive.setCreateDate(new Date());
			supportPositive.setCreateManid(Global.getUser().getRid());
			this.saveObj(supportPositive);
		} else { //更新
			supportPositive.setModifyDate(new Date());
			supportPositive.setModifyManid(Global.getUser().getRid());
			this.updateObj(supportPositive);
			//删除子表
			StringBuilder sql = new StringBuilder();
			sql.append("DELETE TD_ZW_SUPPORT_POSITIVE_PSNS where MAIN_ID = ").append(supportPositive.getRid());
			em.createNativeQuery(sql.toString()).executeUpdate();
		}
		//保存子表
		if (supportPositive.getPsnSelVO() != null && !CollectionUtils.isEmpty(supportPositive.getPsnSelVO().getPsnRidList())) {
			for (Integer rid : supportPositive.getPsnSelVO().getPsnRidList()) {
				TdZwSupportPositivePsns positivePsns = new TdZwSupportPositivePsns();
				positivePsns.setFkByMainId(supportPositive);
				positivePsns.setFkByEmpId(new TdZwPsninfoZzsb(rid));
				positivePsns.setCreateDate(new Date());
				positivePsns.setCreateManid(Global.getUser().getRid());
				this.saveObj(positivePsns);
			}
		}
	}

	/**
	 * 删除附件
	 * @param rid
	 */
	public void delPatentInfoById(Integer rid) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("rid", rid);
		String sql = "DELETE TD_ZW_SUPPORT_POSITIVE_PSNS WHERE MAIN_ID = :rid";
		executeSql(sql, paramMap);
		sql = "DELETE TD_ZW_SUPPORT_POSITIVE WHERE RID = :rid";
		executeSql(sql, paramMap);
	}

	/**
	 * 通过rid 查询
	 * @param rid
	 */
	public TdZwSupportPositive findAllPatentInfoByRid(Integer rid) {
		TdZwSupportPositive positive = this.find(TdZwSupportPositive.class, rid);
		if (null != positive.getPositivePsnList()) {
			positive.getPositivePsnList().size();
		}
		return positive;
	}

	/**
	 *  <p>方法描述：获取课题情况</p>
	 * @MethodAuthor hsj 2023-04-01 15:55
	 */
	@Transactional(readOnly = true)
	public List<TdZwSupportTopic> findAllSupportTopics(Integer mainRid) {
			String hql = "select t from TdZwSupportTopic t where t.fkByMainId.rid = " + mainRid + " order by t.year desc, t.rid desc ";
			List<TdZwSupportTopic> supportTopics = super.findByHql(hql, TdZwSupportTopic.class);
			if (CollectionUtils.isEmpty(supportTopics)) {
				supportTopics = new ArrayList<>();
			}
			for (TdZwSupportTopic thesis : supportTopics) {
				thesis.getSupportTopicPsns().size();
			}
			return supportTopics;
		}
	/**
	 * 删除课题情况
	 *
	 * @param rid 课题情况rid
	 */
	@Transactional(readOnly = false)
	public void delSupportTopic(Integer rid) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("rid", rid);
		String sql = "DELETE TD_ZW_SUPPORT_TOPIC_PSNS WHERE MAIN_ID = :rid";
		executeSql(sql, paramMap);
		sql = "DELETE TD_ZW_SUPPORT_TOPIC WHERE RID = :rid";
		executeSql(sql, paramMap);
	}
	/**
	 *  <p>方法描述：保存课题情况</p>
	 * @MethodAuthor hsj 2023-04-01 16:25
	 */
	@Transactional(readOnly = false)
	public void saveSupportTopic(TdZwSupportTopic supportTopic) {
		if (supportTopic.getYear() == null) {
			supportTopic.setYear(DateUtils.getYearInt());
		}
		supportTopic.setSupportTopicPsns(new ArrayList<TdZwSupportTopicPsns>());
		for (Integer psnRid : supportTopic.getPsnSelVO().getPsnRidList()) {
			if (psnRid == null) {
				continue;
			}
			TdZwSupportTopicPsns supportTopicPsns = new TdZwSupportTopicPsns();
			supportTopicPsns.setFkByMainId(supportTopic);
			supportTopicPsns.setFkByEmpId(new TdZwPsninfoZzsb(psnRid));
			supportTopicPsns.setCreateDate(new Date());
			supportTopicPsns.setCreateManid(Global.getUser().getRid());
			supportTopic.getSupportTopicPsns().add(supportTopicPsns);
		}
		upsertEntity(supportTopic);
	}
	/**
	 *  <p>方法描述：先进适宜技术筛选推广应用- 删除</p>
	 * @MethodAuthor hsj 2023-10-25 10:54
	 */
	@Transactional(readOnly = false)
	public void delSupportAdvance(Integer rid) {
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("rid", rid);
		String sql = "DELETE TD_ZW_SUPPORT_ADVANCE WHERE RID = :rid";
		executeSql(sql, paramMap);
	}

	/**
	 * 查询支撑机构仪器对应码表RID
	 *
	 * @param orgId 支撑机构id
	 * @return 仪器对应码表RID
	 */
	public Set<Integer> findZwSupportInstIdList(Integer orgId) {
		if (orgId == null) {
			return Collections.emptySet();
		}
		String sql = "SELECT SC.RID FROM TD_ZW_SUPPORT_INST I LEFT JOIN TS_SIMPLE_CODE SC ON I.INST_ID = SC.RID WHERE I.MAIN_ID = :orgId ";
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("orgId", orgId);
		List<BigDecimal> zwSupportInstIdSrcList = CollectionUtil.castList(BigDecimal.class, findDataBySqlNoPage(sql, paramMap));
		Set<Integer> zwSupportInstIdList = new HashSet<>();
		for (BigDecimal zwSupportInstId : zwSupportInstIdSrcList) {
			Integer id = ObjectUtil.convert(Integer.class, zwSupportInstId, null);
			if (id == null) {
				continue;
			}
			zwSupportInstIdList.add(id);
		}
		return zwSupportInstIdList;
	}



	/**
	 * <p>方法描述： 存储能力建设信息与职业健康教育和健康促进 </p>
	 * pw 2023/10/25
	 **/
	public void saveAbilityAndSupportHealth (List<Integer> abilityList, Integer mainId, TdZwSupportHealth supportHealth) {
		if (null != supportHealth) {
			this.upsertEntity(supportHealth);
		}
		String delSql = " DELETE FROM TD_ZW_SUPPORT_ABILITY WHERE MAIN_ID="+mainId;
		this.executeSql(delSql, null);
		if (CollectionUtils.isEmpty(abilityList)) {
			return;
		}
		List<TdZwSupportAbility> saveList = new ArrayList<>();
		for (Integer ability : abilityList) {
			TdZwSupportAbility supportAbility = new TdZwSupportAbility();
			supportAbility.setFkByMainId(new TdZwSupportOrgZzsb(mainId));
			supportAbility.setFkByAbilityId(new TsSimpleCode(ability));
			preInsert(supportAbility);
			saveList.add(supportAbility);
		}
		this.saveBatchObjs(saveList);
	}

	/**
	 * <p>Description： 获取支撑机构—具备资质能力</p>
	 * <p>Author： yzz 2023-10-27 </p>
	 */
	public List<TdZwSupportQual> findSupportQualByMainId(Integer rid) {
		StringBuilder hql=new StringBuilder();
		hql.append(" select T from TdZwSupportQual T where T.fkByMainId.rid= ").append(rid);
		hql.append(" order by T.fkByTypeId.num,T.fkByTypeId.codeNo ");
		return  this.findByHql(hql.toString(), TdZwSupportQual.class);
	}

	/**
	 * <p>Description： 获取支撑机构—职业病因素监测开展情况</p>
	 * <p>Author： yzz 2023-10-27 </p>
	 */
	public List<TdZwSupportChk> findSupportChkByMainId(Integer rid) {
		StringBuilder hql=new StringBuilder();
		hql.append(" select T from TdZwSupportChk T where T.fkByMainId.rid= ").append(rid);
		hql.append(" order by T.rid ");
		return  this.findByHql(hql.toString(), TdZwSupportChk.class);
	}

	/**
	 * <p>Description： 获取支撑机构—财政投入情况</p>
	 * <p>Author： yzz 2023-10-27 </p>
	 */
	public List<TdZwSupportFund> findSupportFundByMainId(Integer rid) {
		StringBuilder hql=new StringBuilder();
		hql.append(" select T from TdZwSupportFund T where T.fkByMainId.rid= ").append(rid);
		hql.append(" order by T.rid ");
		return  this.findByHql(hql.toString(), TdZwSupportFund.class);
	}

	/**
	 * <p>Description： </p>
	 * <p>Author： yzz 2023-10-30 </p>
	 */
	public List<TdZwSupportTech> findSupportTechByMainId(Integer rid) {
		StringBuilder hql=new StringBuilder();
		hql.append(" select T from TdZwSupportTech T where T.fkByMainId.rid= ").append(rid);
		hql.append(" order by T.rid ");
		return  this.findByHql(hql.toString(), TdZwSupportTech.class);
	}
	/**
	 * <p>Description：通过mainid获取 TdZwSupportPlace</p>
	 * <p>Author： yzz 2023-10-25 </p>
	 */
	public TdZwSupportPlace findSupportPlace(Integer rid) {
		StringBuilder hql=new StringBuilder();
		hql.append(" select T from TdZwSupportPlace T where T.fkByMainId.rid=").append(rid);
		TdZwSupportPlace supportPlace = this.findOneByHql(hql.toString(), TdZwSupportPlace.class);
		if(supportPlace!=null){
			supportPlace.getSupportOfficeList().size();
		}
		return supportPlace;
	}

	/**
	 *  <p>方法描述：科技攻关能力 -查询</p>
	 * @MethodAuthor hsj 2023-10-25 18:34
	 */
	@Transactional(readOnly = true)
	public TdZwSupportTrc findAllSupportTrc(Integer mainRid) {
		String hql = "select t from TdZwSupportTrc t where t.fkByMainId.rid = " + mainRid ;
		List<TdZwSupportTrc> list = super.findByHql(hql, TdZwSupportTrc.class);
		if(CollectionUtils.isEmpty(list)){
			return new TdZwSupportTrc();
		}
		return list.get(0);
	}

	/**
	 *  <p>方法描述：先进适宜技术筛选推广应用 - 查询</p>
	 * @MethodAuthor hsj 2023-10-25 11:17
	 */
	@Transactional(readOnly = true)
	public List<TdZwSupportAdvance> findAllSupportAdvance(Integer mainRid) {
		String hql = "select t from TdZwSupportAdvance t where t.fkByMainId.rid = " + mainRid + " order by t.rid ";
		return   super.findByHql(hql, TdZwSupportAdvance.class);
	}

	/**
	 * <p>方法描述：通过主表rid 获取支撑机构—职业健康教育和健康促进 </p>
	 * pw 2023/10/25
	 **/
	@Transactional(readOnly = true)
	public TdZwSupportHealth findSupportHealthByMainId (Integer mainId) {
		if (null == mainId) {
			return null;
		}
		List<TdZwSupportHealth> resultList = this.findEntityListByMainId(TdZwSupportHealth.class, mainId);
		return CollectionUtils.isEmpty(resultList) ? null : resultList.get(0);
	}


	/**
	 * <p>方法描述：通过主表rid 获取支撑机构能力建设信息集合 </p>
	 * pw 2023/10/25
	 **/
	@Transactional(readOnly = true)
	public List<Integer> findAbilitysByMainId (Integer mainId) {
		if (null == mainId) {
			return Collections.emptyList();
		}
		String sql = " SELECT T.ABILITY_ID FROM TD_ZW_SUPPORT_ABILITY T WHERE T.MAIN_ID="+mainId;
		List<BigDecimal> queryResultList =  this.findSqlResultList(sql);
		if (CollectionUtils.isEmpty(queryResultList)) {
			return Collections.emptyList();
		}
		List<Integer> resultList = new ArrayList<>();
		for (BigDecimal decimal : queryResultList) {
			resultList.add(decimal.intValue());
		}
		return resultList;
	}

	/**
	 *  <p>方法描述：检测能力的查询</p>
	 * @MethodAuthor hsj 2025-04-02 14:20
	 */
	public List<Object[]> findChkAbility(Integer orgId, String searchProjectName, String searchJcWay, List<String> searchStates) {
		List<Object[]> resultList = new ArrayList<>();
		if (ObjectUtil.isNull(orgId)) {
			return resultList;
		}
		Map<String, Object> paramMap = new HashMap<>();
		StringBuffer buffer = new StringBuffer();
		buffer.append(" SELECT T.RID,T3.CODE_NAME,T2.LAW_CODE,T.JC_WAY,T.LAB_APPROVAL_TAG, ");
		buffer.append(" T.QUAL_APPROVAL_TAG,T.OUT_TAG,T.STATE,T.LIMIT_DESC,listagg(T5.CODE_NAME,'、') within group ( order by T5.NUM,T5.CODE_NO) as METHOD_NAME  ");
		buffer.append(" FROM TB_YSJC_CHK_ABILITY_REL T ");
		buffer.append(" INNER JOIN TB_YSJC_CHK_ABILITY T1 ON  T.MAIN_ID = T1.RID ");
		buffer.append(" LEFT JOIN TB_TJ_LAW T2 ON T.JC_BASE_ID = T2.RID ");
		buffer.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.ITEM_ID = T3.RID ");
		buffer.append(" LEFT JOIN TB_YSJC_CHK_METHOD T4 on T.RID = T4.MAIN_ID ");
		buffer.append(" left join TS_SIMPLE_CODE T5 on T4.METHOD_ID = T5.RID ");
		buffer.append(" WHERE T1.ORG_ID = ").append(orgId).append(" ");
		if (StringUtils.isNotBlank(searchProjectName)) {
			buffer.append(" AND T3.CODE_NAME LIKE :searchProjectName ");
			paramMap.put("searchProjectName", "%" + searchProjectName.trim() + "%");
		}
		if(StringUtils.isNotBlank(searchJcWay)){
			buffer.append(" AND T.JC_WAY LIKE :searchJcWay ");
			paramMap.put("searchJcWay", "%" + searchJcWay.trim() + "%");
		}
		//如果启用 停用两个状态都选择了 查询就不需要状态条件
		if(!CollectionUtils.isEmpty(searchStates) && searchStates.size() == 1){
			buffer.append(" AND T.STATE = :searchState ");
			paramMap.put("searchState", Integer.parseInt(searchStates.get(0)));
		}
		buffer.append("  group by T.RID, T3.CODE_NAME, T2.LAW_CODE, T.JC_WAY, T.LAB_APPROVAL_TAG, T.QUAL_APPROVAL_TAG, T.OUT_TAG, T.STATE, T.LIMIT_DESC,T3.NUM ");
		buffer.append("  ORDER BY T3.NUM,T3.CODE_NAME,T.JC_WAY ");
		return this.findDataBySqlNoPage(buffer.toString(), paramMap);
	}

	/**
	 *  <p>方法描述：检测能力-状态更新</p>
	 * @MethodAuthor hsj 2025-04-02 14:31 
	 */
	@Transactional(readOnly = false)
	public void updateCheckAbilityState(Integer rid, Integer state) {
		if(rid==null || state == null){
			return;
		}
		StringBuffer buffer = new StringBuffer();
		buffer.append("UPDATE TB_YSJC_CHK_ABILITY_REL SET STATE = ").append(state).append("WHERE RID = ").append(rid);
		this.em.createNativeQuery(buffer.toString()).executeUpdate();
	}

	/**
	 *  <p>方法描述：检测能力删除</p>
	 * @MethodAuthor hsj 2025-04-02 14:35
	 */
	@Transactional(readOnly = false)
	public void deleteTdYsjcChkManage(Integer rid) {
		if (null==rid) {
			return;
		}
		StringBuffer sb = new StringBuffer();
		sb.append("SELECT COUNT(RID) NUM from TB_YSJC_CHK_ABILITY_REL  WHERE MAIN_ID IN (SELECT RID FROM TB_YSJC_CHK_ABILITY WHERE 1=1 AND ORG_ID = ").append(Global.getUser().getTsUnit().getRid()).append(")");
		List resultList = this.em.createNativeQuery(sb.toString()).getResultList();
		if(resultList!=null){
			BigDecimal o = (BigDecimal) resultList.get(0);
			if(o.intValue()==1){
				//同时删除主子表
				this.em.createNativeQuery("DELETE FROM TB_YSJC_CHK_METHOD WHERE MAIN_ID="+rid).executeUpdate();
				this.em.createNativeQuery("delete from TB_YSJC_CHK_ABILITY_REL where RID ="+rid).executeUpdate();
				this.em.createNativeQuery("DELETE FROM TB_YSJC_CHK_ABILITY WHERE ORG_ID ="+Global.getUser().getTsUnit().getRid()).executeUpdate();

			}else {
				//删除
				this.em.createNativeQuery("DELETE FROM TB_YSJC_CHK_METHOD WHERE MAIN_ID="+rid).executeUpdate();
				//不删除主表，值删除子表数据
				this.em.createNativeQuery("delete from TB_YSJC_CHK_ABILITY_REL where RID ="+rid).executeUpdate();
			}
		}


	}

	/**
	 *  <p>方法描述：法律法规的查询</p>
	 * @MethodAuthor hsj 2025-04-02 15:19
	 */
	@Transactional(readOnly = true)
	public Integer selectLowTypeId() {

		StringBuffer buffer = new StringBuffer();
		buffer.append("SELECT RID from TS_SIMPLE_CODE where code_type_id = " +
				"(SELECT RID from TS_CODE_TYPE where CODE_TYPE_NAME = '5016') AND CODE_NO = '1002'");

		List resultList = em.createNativeQuery(buffer.toString()).getResultList();
		if(CollectionUtils.isEmpty(resultList)){
			return null;
		}
		Object obj = resultList.get(0);

		return ((BigDecimal)obj).intValue();

	}

	/**
	 *  <p>方法描述：检测能力-更新</p>
	 * @MethodAuthor hsj 2025-04-02 15:34
	 */
	@Transactional(readOnly = false)
	public void addOrUpdateTdYsjcChkManage(TbYsjcChkAbilityRelZzsb tbYsjcChkAbilityRel, TsUnit tsUnit, List<String> chkMethodRids) {
		try{
			//根据unitId查询主表信息
			StringBuffer buffer = new StringBuffer();
			buffer.append("SELECT T FROM TbYsjcChkAbilityZzsb T WHERE T.fkByOrgId = ").append(tsUnit.getRid());
			TbYsjcChkAbilityZzsb tbYsjcChkAbility = this.findOneByHql(buffer.toString(),TbYsjcChkAbilityZzsb.class);
			if(tbYsjcChkAbility==null||null == tbYsjcChkAbility.getRid()){//第一次保存
				if(null== tbYsjcChkAbility){
					tbYsjcChkAbility = new TbYsjcChkAbilityZzsb();
				}
				tbYsjcChkAbility.setFkByOrgId(tsUnit);
				tbYsjcChkAbility.setCreateDate(new Date());
				tbYsjcChkAbility.setCreateManid(Global.getUser().getRid());
				//tbYsjcChkAbility.setModifyDate(new Date());
				//保存主表
				this.save(tbYsjcChkAbility);
				//保存子表
				tbYsjcChkAbilityRel.setCreateDate(new Date());
				tbYsjcChkAbilityRel.setFkByMainId(tbYsjcChkAbility);
				tbYsjcChkAbilityRel.setCreateManid(Global.getUser().getRid());
				//tbYsjcChkAbilityRel.setModifyDate(new Date());
				this.saveObj(tbYsjcChkAbilityRel);
			}else {
				//主表已有数据
				//判定子表数据添加、修改
				if(null == tbYsjcChkAbilityRel.getRid()){
					//新增
					tbYsjcChkAbilityRel.setCreateDate(new Date());
					tbYsjcChkAbilityRel.setFkByMainId(tbYsjcChkAbility);
					tbYsjcChkAbilityRel.setCreateManid(Global.getUser().getRid());
					this.saveObj(tbYsjcChkAbilityRel);
				}else {
					//修改
					tbYsjcChkAbilityRel.setModifyDate(new Date());
					this.update(tbYsjcChkAbilityRel);
				}
			}
			//保存方法验证、确认或论证表 子表
			StringBuffer sql=new StringBuffer();
			sql.append(" DELETE FROM TB_YSJC_CHK_METHOD WHERE MAIN_ID=").append(tbYsjcChkAbilityRel.getRid());
			this.em.createNativeQuery(sql.toString()).executeUpdate();
			if(!CollectionUtils.isEmpty(chkMethodRids)){
				for (String chkMethodRid : chkMethodRids) {
					TbYsjcChkMethodZzsb chkMethod=new TbYsjcChkMethodZzsb();
					chkMethod.setFkByMainId(tbYsjcChkAbilityRel);
					chkMethod.setFkByMethodId(new TsSimpleCode(Integer.parseInt(chkMethodRid)));
					chkMethod.setCreateDate(new Date());
					chkMethod.setCreateManid(Global.getUser().getRid());
					this.save(chkMethod);
				}
			}
		}catch (Exception e){
			e.printStackTrace();
		}


	}

	public List<Object[]> findLowBadRsnList(String typeNo) {
		List<Object[]> sqlResultList = null;

		StringBuffer buffer = new StringBuffer();
		if(StringUtils.isEmpty(typeNo)){
			return sqlResultList;
		}
		if(StringUtils.isNotBlank(typeNo)){
			buffer.append("SELECT T.RID,T.LAW_CODE,T.LAW_NAME,T.DECREE_DATE  FROM TB_TJ_LAW T WHERE 1=1 AND STATE_MARK = 1");
			buffer.append("AND  T.LAW_TYPE_ID =").append(typeNo).append(" ORDER BY T.LAW_CODE ASC");
			sqlResultList = super.findSqlResultList(buffer
					.toString());
		}

		return sqlResultList;

	}
	/**
	 *  <p>方法描述：检测能力状态查询</p>
	 * @MethodAuthor hsj 2025-04-07 9:39
	 */
	public Integer findCheckAbilityStateByMainId(Integer modAbilityRelRid) {
		if (ObjectUtil.isNull(modAbilityRelRid)) {
			return null;
		}
		String sql = " SELECT S.RID,S.STATE FROM TB_YSJC_CHK_ABILITY_REL S WHERE S.RID = '" + modAbilityRelRid + "'";
		List<Object[]> list = this.findDataBySqlNoPage(sql, null);
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		return Convert.toInt(list.get(0)[1]);
	}
	/**
	 *  <p>方法描述：检测能力-业务数据是都存在</p>
	 * @MethodAuthor hsj 2025-04-07 9:48
	 */
	public Boolean findCheckAbilityByMainId(Integer modAbilityRelRid) {
		if (ObjectUtil.isNull(modAbilityRelRid)) {
			return Boolean.FALSE;
		}
		String sql = " SELECT count(1) FROM TB_YSJC_CHK_ABILITY_REL S WHERE S.RID = '" + modAbilityRelRid + "'";
		return this.findCountBySql(sql) > 0;
	}

	/**
	 *  <p>方法描述：查询该机构下是否存在检测能力</p>
	 * @MethodAuthor hsj 2025-04-07 9:58
	 */
	public boolean findChkAbilityCount(Integer orgId) {
		if (ObjectUtil.isNull(orgId)) {
			return Boolean.FALSE;
		}
		StringBuffer buffer = new StringBuffer();
		buffer.append(" SELECT count(1) ");
		buffer.append(" FROM TB_YSJC_CHK_ABILITY_REL T ");
		buffer.append(" INNER JOIN TB_YSJC_CHK_ABILITY T1 ON  T.MAIN_ID = T1.RID ");
		buffer.append(" WHERE T1.ORG_ID = ").append(orgId);
		return this.findCountBySql(buffer.toString()) > 0;
	}

	/**
	 * 更新被删除人员的MASTER_DATA_TIME字段
	 * 当人员被删除时，需要更新其MASTER_DATA_TIME为当前日期
	 *
	 * @param delPsnIds 被删除的人员ID字符串，格式如",123,456,789"
	 */
	private void updateMasterDataTimeForDeletedPersons(String delPsnIds) {
		if (StringUtils.isBlank(delPsnIds)) {
			return;
		}

		try {
			// 解析被删除的人员ID
			List<Integer> deletedPsnRidList = new ArrayList<>();
			String[] ridArray = delPsnIds.substring(1).split(","); // 去掉开头的逗号
			for (String ridStr : ridArray) {
				if (StringUtils.isNotBlank(ridStr)) {
					deletedPsnRidList.add(Integer.parseInt(ridStr.trim()));
				}
			}

			if (!deletedPsnRidList.isEmpty()) {
				// 批量更新被删除人员的MASTER_DATA_TIME字段
				Date currentDate = new Date();
				StringBuilder updateSql = new StringBuilder();
				updateSql.append("UPDATE TD_ZW_PSNINFO SET MASTER_DATA_TIME = :currentDate WHERE RID IN (");

				for (int i = 0; i < deletedPsnRidList.size(); i++) {
					if (i > 0) {
						updateSql.append(",");
					}
					updateSql.append(deletedPsnRidList.get(i));
				}
				updateSql.append(")");

				Map<String, Object> paramMap = new HashMap<>();
				paramMap.put("currentDate", currentDate);

				// 执行更新SQL
				super.executeSql(updateSql.toString(), paramMap);
			}
		} catch (Exception e) {
			e.printStackTrace();
			// 记录日志但不影响主流程
		}
	}

	/**
	 * 获取原始人员在职状态
	 * 用于比较状态是否发生变化
	 *
	 * @param zwOrginfoEntity 机构信息实体
	 * @return 人员RID与原始在职状态的映射
	 */
	private Map<Integer, String> getOriginalPersonnelDutyStates(IZwOrginfoZzsb zwOrginfoEntity) {
		Map<Integer, String> originalDutyStateMap = new HashMap<>();

		if (zwOrginfoEntity.getRid() == null) {
			// 新增机构，没有原始状态
			return originalDutyStateMap;
		}

		try {
			// 查询数据库中当前机构的人员在职状态
			StringBuilder sql = new StringBuilder();
			sql.append("SELECT EMP_ID, ON_DUTY FROM ");

			// 根据机构类型选择对应的人员关系表
			if (zwOrginfoEntity instanceof TdZwTjorginfoZzsb) {
				sql.append("TD_ZW_TJORGPSNS");
			} else if (zwOrginfoEntity instanceof TdZwSrvorginfoZzsb) {
				sql.append("TD_ZW_SRVORGPSNS");
			} else if (zwOrginfoEntity instanceof TdZwDiagorginfoZzsb) {
				sql.append("TD_ZW_DIAGPSNS");
			} else if (zwOrginfoEntity instanceof TdZwSupportOrgZzsb) {
				sql.append("TD_ZW_SUPPORT_PSNS");
			} else {
				sql.append("TD_ZW_OCCHETH_PSNS");
			}

			sql.append(" WHERE ORG_ID = ").append(zwOrginfoEntity.getRid());

			List<Object[]> results = super.findDataBySqlNoPage(sql.toString(),null);
			if (results != null) {
				for (Object[] row : results) {
					Integer empId = (Integer) row[0];
					String onDuty = (String) row[1];
					originalDutyStateMap.put(empId, onDuty);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			// 出现异常时返回空Map，不影响主流程
		}

		return originalDutyStateMap;
	}

	/**
	 * 检查并更新人员在职状态变化时的MASTER_DATA_TIME
	 *
	 * @param psnInfoRid 人员信息RID
	 * @param currentDutyState 当前在职状态
	 * @param originalDutyStateMap 原始在职状态映射
	 */
	private void checkAndUpdateMasterDataTimeForDutyChange(Integer psnInfoRid, String currentDutyState,
														   Map<Integer, String> originalDutyStateMap) {
		if (psnInfoRid == null || originalDutyStateMap == null) {
			return;
		}

		try {
			String originalDutyState = originalDutyStateMap.get(psnInfoRid);

			// 检查在职状态是否发生变化（离职变在职或在职变离职）
			if (originalDutyState != null && !StringUtils.equals(originalDutyState, currentDutyState)) {
				// 状态发生变化，更新MASTER_DATA_TIME为当前日期
				Date currentDate = new Date();
				String updateSql = "UPDATE TD_ZW_PSNINFO SET MASTER_DATA_TIME = :currentDate WHERE RID = " + psnInfoRid;

				Map<String, Object> paramMap = new HashMap<>();
				paramMap.put("currentDate", currentDate);

				super.executeSql(updateSql, paramMap);
			}
		} catch (Exception e) {
			e.printStackTrace();
			// 记录日志但不影响主流程
		}
	}
}
