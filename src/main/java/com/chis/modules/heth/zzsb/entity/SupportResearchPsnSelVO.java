package com.chis.modules.heth.zzsb.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支撑机构信息管理-支撑机构信息管理-人员选择弹出框用
 */
public class SupportResearchPsnSelVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private String searchPsnName;
    private String selPsnName;
    private List<Object[]> allIncumbencyPsnList;
    private List<Integer> psnRidList;
    private List<Integer> selectedRowRid;
    private List<Object[]> selectedPsnList;
    private Map<Integer, Object[]> allPsnMap;

    public SupportResearchPsnSelVO() {
        init();
    }

    public void init() {
        this.searchPsnName = "";
        this.selPsnName = "";
        this.psnRidList = new ArrayList<>();
        this.selectedRowRid = new ArrayList<>();
        this.selectedPsnList = new ArrayList<>();
        this.allPsnMap = new HashMap<>();
    }

    public String getSearchPsnName() {
        return searchPsnName;
    }

    public void setSearchPsnName(String searchPsnName) {
        this.searchPsnName = searchPsnName;
    }

    public String getSelPsnName() {
        return selPsnName;
    }

    public void setSelPsnName(String selPsnName) {
        this.selPsnName = selPsnName;
    }

    public List<Object[]> getAllIncumbencyPsnList() {
        return allIncumbencyPsnList;
    }

    public void setAllIncumbencyPsnList(List<Object[]> allIncumbencyPsnList) {
        this.allIncumbencyPsnList = allIncumbencyPsnList;
    }

    public List<Integer> getPsnRidList() {
        return psnRidList;
    }

    public void setPsnRidList(List<Integer> psnRidList) {
        this.psnRidList = psnRidList;
    }

    public List<Integer> getSelectedRowRid() {
        return selectedRowRid;
    }

    public void setSelectedRowRid(List<Integer> selectedRowRid) {
        this.selectedRowRid = selectedRowRid;
    }

    public List<Object[]> getSelectedPsnList() {
        return selectedPsnList;
    }

    public void setSelectedPsnList(List<Object[]> selectedPsnList) {
        this.selectedPsnList = selectedPsnList;
    }

    public Map<Integer, Object[]> getAllPsnMap() {
        return allPsnMap;
    }

    public void setAllPsnMap(Map<Integer, Object[]> allPsnMap) {
        this.allPsnMap = allPsnMap;
    }
}
