package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2015-04-20
 */
@Entity
@Table(name = "TD_ZW_TJORGPSNS")
@SequenceGenerator(name = "TdZwTjorgpsnsSeq", sequenceName = "TD_ZW_TJORGPSNS_SEQ", allocationSize = 1)
public class TdZwTjorgpsnsZzsb implements Serializable,IZwOrgPsnsZzsb {
	private static final long serialVersionUID = -5998381496362816724L;

	private Integer rid;
	private TdZwTjorginfoZzsb tdZwTjorginfo;
	private TdZwPsninfoZzsb tdZwPsninfo;
	private String certNo;
	private List<TdZwWorkdeptZzsb> tdZwWorkdepts = new LinkedList<TdZwWorkdeptZzsb>();
	private List<TdZwPsnitemsZzsb> tdZwPsnitemses = new LinkedList<TdZwPsnitemsZzsb>();
	private String wordOfficeName;
	private String wordItems;
	
	private String wordItemStr;
	
	private String onDuty;

	public TdZwTjorgpsnsZzsb() {
	}

	public TdZwTjorgpsnsZzsb(Integer rid) {
		this.rid = rid;
	}

	@Id
	@Column(name = "RID", unique = true , precision = 22, scale = 0)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwTjorgpsnsSeq")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return tdZwTjorginfo;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		tdZwTjorginfo = (TdZwTjorginfoZzsb) zwOrginfo;
	}

	@ManyToOne
	@JoinColumn(name = "ORG_ID" )
	public TdZwTjorginfoZzsb getTdZwTjorginfo() {
		return this.tdZwTjorginfo;
	}

	public void setTdZwTjorginfo(TdZwTjorginfoZzsb tdZwTjorginfo) {
		this.tdZwTjorginfo = tdZwTjorginfo;
	}

	@ManyToOne
	@JoinColumn(name = "EMP_ID" )
	public TdZwPsninfoZzsb getTdZwPsninfo() {
		return this.tdZwPsninfo;
	}

	public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
		this.tdZwPsninfo = tdZwPsninfo;
	}

	@Column(name = "CERT_NO", length = 50)
	public String getCertNo() {
		return this.certNo;
	}

	public void setCertNo(String certNo) {
		this.certNo = certNo;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwTjorgpsns",orphanRemoval = true)
	public List<TdZwWorkdeptZzsb> getTdZwWorkdepts() {
		return tdZwWorkdepts;
	}

	public void setTdZwWorkdepts(List<TdZwWorkdeptZzsb> tdZwWorkdepts) {
		this.tdZwWorkdepts = tdZwWorkdepts;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwTjorgpsns",orphanRemoval = true)
	public List<TdZwPsnitemsZzsb> getTdZwPsnitemses() {
		return tdZwPsnitemses;
	}

	public void setTdZwPsnitemses(List<TdZwPsnitemsZzsb> tdZwPsnitemses) {
		this.tdZwPsnitemses = tdZwPsnitemses;
	}
	
	@Transient
	@Override
	public String getWordOfficeName() {
		return wordOfficeName;
	}

	@Override
	public void setWordOfficeName(String wordOfficeName) {
		this.wordOfficeName = wordOfficeName;
	}

	@Transient
	@Override
	public String getWordItems() {
		return wordItems;
	}

	@Override
	public void setWordItems(String wordItems) {
		this.wordItems = wordItems;
	}
	
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		TdZwTjorgpsnsZzsb other = (TdZwTjorgpsnsZzsb) obj;
		if (rid == null) {
			return false;
		} else if (!rid.equals(other.rid))
			return false;
		return true;
	}
	

	@Transient
	@Override
	public String getJlCode() {
		return null;
	}

	@Override
	public void setJlCode(String jlCode) {
	}

	@Transient
	@Override
	public String getLjJl() {
		return null;
	}

	@Override
	public void setLjJl(String ljJl) {
	}

	@Transient
	@Override
	public String getWordItemStr() {
		return this.wordItemStr;
	}

	@Override
	public void setWordItemStr(String wordItemStr) {
		this.wordItemStr = wordItemStr;
	}
	@Column(name="ON_DUTY")
	public String getOnDuty() {
		return onDuty;
	}

	public void setOnDuty(String onDuty) {
		this.onDuty = onDuty;
	}
}