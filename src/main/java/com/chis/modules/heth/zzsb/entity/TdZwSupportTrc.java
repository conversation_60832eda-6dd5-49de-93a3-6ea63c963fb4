package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-10-25
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_TRC")
@SequenceGenerator(name = "TdZwSupportTrc", sequenceName = "TD_ZW_SUPPORT_TRC_SEQ", allocationSize = 1)
public class TdZwSupportTrc implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSupportOrgZzsb fkByMainId;
	private Integer ifTrc;
	private Integer ifTjTrain;
	private Integer tjTrainNum;
	private Integer ifTtb;
	private Integer tranNum;
	private Integer ifExpose;
	private Integer ifDiagTrain;
	private Integer diagNum;
	private Integer ifDiagTrc;
	private Integer ifScienGuid;
	private Integer guidNum;
	private Integer scienTrainNum;
	private Integer ifUniversity;
	private Integer universityNum;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwSupportTrc() {
	}

	public TdZwSupportTrc(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportTrc")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSupportOrgZzsb getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSupportOrgZzsb fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@Column(name = "IF_TRC")	
	public Integer getIfTrc() {
		return ifTrc;
	}

	public void setIfTrc(Integer ifTrc) {
		this.ifTrc = ifTrc;
	}	
			
	@Column(name = "IF_TJ_TRAIN")	
	public Integer getIfTjTrain() {
		return ifTjTrain;
	}

	public void setIfTjTrain(Integer ifTjTrain) {
		this.ifTjTrain = ifTjTrain;
	}	
			
	@Column(name = "TJ_TRAIN_NUM")	
	public Integer getTjTrainNum() {
		return tjTrainNum;
	}

	public void setTjTrainNum(Integer tjTrainNum) {
		this.tjTrainNum = tjTrainNum;
	}	
			
	@Column(name = "IF_TTB")	
	public Integer getIfTtb() {
		return ifTtb;
	}

	public void setIfTtb(Integer ifTtb) {
		this.ifTtb = ifTtb;
	}	
			
	@Column(name = "TRAN_NUM")	
	public Integer getTranNum() {
		return tranNum;
	}

	public void setTranNum(Integer tranNum) {
		this.tranNum = tranNum;
	}	
			
	@Column(name = "IF_EXPOSE")	
	public Integer getIfExpose() {
		return ifExpose;
	}

	public void setIfExpose(Integer ifExpose) {
		this.ifExpose = ifExpose;
	}	
			
	@Column(name = "IF_DIAG_TRAIN")	
	public Integer getIfDiagTrain() {
		return ifDiagTrain;
	}

	public void setIfDiagTrain(Integer ifDiagTrain) {
		this.ifDiagTrain = ifDiagTrain;
	}	
			
	@Column(name = "DIAG_NUM")	
	public Integer getDiagNum() {
		return diagNum;
	}

	public void setDiagNum(Integer diagNum) {
		this.diagNum = diagNum;
	}	
			
	@Column(name = "IF_DIAG_TRC")	
	public Integer getIfDiagTrc() {
		return ifDiagTrc;
	}

	public void setIfDiagTrc(Integer ifDiagTrc) {
		this.ifDiagTrc = ifDiagTrc;
	}	
			
	@Column(name = "IF_SCIEN_GUID")	
	public Integer getIfScienGuid() {
		return ifScienGuid;
	}

	public void setIfScienGuid(Integer ifScienGuid) {
		this.ifScienGuid = ifScienGuid;
	}	
			
	@Column(name = "GUID_NUM")	
	public Integer getGuidNum() {
		return guidNum;
	}

	public void setGuidNum(Integer guidNum) {
		this.guidNum = guidNum;
	}	
			
	@Column(name = "SCIEN_TRAIN_NUM")	
	public Integer getScienTrainNum() {
		return scienTrainNum;
	}

	public void setScienTrainNum(Integer scienTrainNum) {
		this.scienTrainNum = scienTrainNum;
	}	
			
	@Column(name = "IF_UNIVERSITY")	
	public Integer getIfUniversity() {
		return ifUniversity;
	}

	public void setIfUniversity(Integer ifUniversity) {
		this.ifUniversity = ifUniversity;
	}	
			
	@Column(name = "UNIVERSITY_NUM")	
	public Integer getUniversityNum() {
		return universityNum;
	}

	public void setUniversityNum(Integer universityNum) {
		this.universityNum = universityNum;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}