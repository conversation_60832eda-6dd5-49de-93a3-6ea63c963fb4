package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2023-3-29
 */
@Entity
@Table(name = "TD_ZW_SUPPORT_POSITIVE_PSNS")
@SequenceGenerator(name = "TdZwSupportPositivePsns", sequenceName = "TD_ZW_S_POSITIVE_PSNS_SEQ", allocationSize = 1)
public class TdZwSupportPositivePsns implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwSupportPositive fkByMainId;
	private TdZwPsninfoZzsb fkByEmpId;
	private Integer createManid;
	private Date createDate;
	private Date modifyDate;
	private Integer modifyManid;
	
	public TdZwSupportPositivePsns() {
	}

	public TdZwSupportPositivePsns(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportPositivePsns")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "MAIN_ID")			
	public TdZwSupportPositive getFkByMainId() {
		return fkByMainId;
	}

	public void setFkByMainId(TdZwSupportPositive fkByMainId) {
		this.fkByMainId = fkByMainId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "EMP_ID")			
	public TdZwPsninfoZzsb getFkByEmpId() {
		return fkByEmpId;
	}

	public void setFkByEmpId(TdZwPsninfoZzsb fkByEmpId) {
		this.fkByEmpId = fkByEmpId;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "MODIFY_DATE")			
	public Date getModifyDate() {
		return modifyDate;
	}

	public void setModifyDate(Date modifyDate) {
		this.modifyDate = modifyDate;
	}	
			
	@Column(name = "MODIFY_MANID")	
	public Integer getModifyManid() {
		return modifyManid;
	}

	public void setModifyManid(Integer modifyManid) {
		this.modifyManid = modifyManid;
	}	
			
}