package com.chis.modules.heth.zzsb.plugin;

import com.chis.modules.system.entity.TsCodeType;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.enumn.SystemType;

import java.util.HashSet;
import java.util.Set;

/**
 * 码表的插件 码表编码是写死的
 * 
 * <AUTHOR>
 * @createDate 2014年8月29日 上午11:07:02
 * @LastModify LuXuekun
 * @ModifyDate 2014年8月29日 上午11:07:02
 */
public class HethTsCodePluginObj {

	public static Set<TsCodeType> codeTypeSet;
	public static Set<TsSimpleCode> simpleCodeSet;

	static {
		codeTypeSet = new HashSet<TsCodeType>();
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5019", "放射机构服务项目", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5021", "医师资格级别", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5023", "医师资格类别", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5025", "职业卫生专业技术人员资质类别", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5042", "放射技术机构：放射仪器类别", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5062", "职称级别", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5130", "支撑机构论文刊物类别", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5131", "支撑机构获奖课题等级", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5132", "支撑机构科研成果专利类型", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5133", "支持机构科研成果标准级别", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5134", "支撑机构学术培训类别", (short)1, (short)0));

		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5308", "资质人员属性", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5323", "放射卫生服务项目小类", (short)0, (short)0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5507", "质量管理体系", (short) 1, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5509", "单位类型", (short) 1, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5510", "职业病诊断医师（职业健康检查）的资质范围", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5527", "健康检查资质服务小类", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5554", "考核类型（质量控制考核）", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5562", "机构资质", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5563", "资质申请类别", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5565", "备案管理机构", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5568", "质控结论", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5569", "整改结果", (short) 0, (short) 0));

		//检定要求
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5841", "检定要求", (short) 0, (short) 0));
		//仪器状态
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5842", "仪器状态", (short) 0, (short) 0));
		//支撑机构-职防院-机构类型
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5604", "职防院机构类型", (short) 0, (short) 0));
		//支撑机构-技术支撑能力
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5605", "技术支撑能力", (short) 1, (short) 0));
		//支撑机构-具备职业健康技术服务相关资质或能力的情况
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5606", "具备资质能力", (short) 0, (short) 0));
		//支撑机构-能力建设
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5608", "能力建设", (short) 1, (short) 0));
		//支撑机构-仪器设备
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5607", "仪器设备", (short) 1, (short) 0));
		//支撑机构-科室
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5609", "科室（支撑机构）", (short) 0, (short) 0));
		//支撑机构-建设标准/人员配置标准
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5610", "建设标准/人员配置标准", (short) 0, (short) 0));
		//支撑机构-人员-从事专业
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5840", "从事专业", (short) 1, (short) 0));
		//业务范围与检测项目关系配置 --检测项目大类
		codeTypeSet.add(new TsCodeType(SystemType.HETH_ZZSB, "5625", "检测项目大类", (short) 0, (short) 0));
		codeTypeSet.add(new TsCodeType(SystemType.BADRSN_CHECK, "5624", "方法验证、确认或论证", (short) 0, (short) 0));
	}
}
