package com.chis.modules.heth.zzsb.plugin;

import com.chis.modules.system.entity.TsMenu;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 模块说明：菜单插件
 *
 * <AUTHOR>
 * @createDate 2020年2月27日
 *
 * <p>修订内容：
 *  增加5：江西、6：重庆
 * </p>
 *
 * @MethodReviser pw,2020年10月13日,
 */
public class HethMenuPluginObj {

    public static Set<TsMenu> menuSet;

	static {
		menuSet = new HashSet<TsMenu>();
		/*
		//资质人员多点执业版本
		menuSet.add(new TsMenu("034.010","职业健康检查机构资质申报","heth_jcjg_zzsb","职业健康检查机构资质申报", Short.valueOf("1"),"/webapp/heth/zzsb/zwTjOrgInfoEdit.faces","process-manage.png","recruit-manage.png",new Date(),1,1));
		menuSet.add(new TsMenu("034.002","放射卫生服务机构资质申报","heth_fsjg_zzsb","放射卫生服务机构资质申报", Short.valueOf("1"),"/webapp/heth/zzsb/zwSrvOrgInfoEdit.faces","sort.png","my-performance.png",new Date(),1,2));
		menuSet.add(new TsMenu("034.003","职业病诊断机构资质申报","heth_zdjg_zzsb","职业病诊断机构资质申报", Short.valueOf("1"),"/webapp/heth/zzsb/zwDiagOrgInfoEdit.faces","sequence-settings.png","portal2.png",new Date(),1,3));
		menuSet.add(new TsMenu("034.004","职业卫生技术服务机构资质申报","heth_zywsjsfwjg_zzsb","职业卫生技术服务机构资质申报", Short.valueOf("1"),"/webapp/heth/zzsb/zwOcchethInfoEdit.faces","sort.png","my-performance.png",new Date(),1,4));
		menuSet.add(new TsMenu("034.034","支撑机构信息管理","heth_zzsb_zcjg","支撑机构信息管理", Short.valueOf("1"),"/webapp/heth/zzsb/zwSupportOrgEdit.faces","process-manage.png","process-manage.png",new Date(),1,34));
		menuSet.add(new TsMenu("034.015","职业卫生服务机构查询","heth_zzjgcx_zzsb","职业卫生服务机构查询", Short.valueOf("1"),"/webapp/heth/zzsb/zwOrgInfoSearch.faces","search.png","organization-manage.png",new Date(),1,4));
		menuSet.add(new TsMenu("043.008","专业技术人员档案检索","heth_cxtj_zyjsrydajs","专业技术人员档案检索", Short.valueOf("1"),"/webapp/heth/zzsb/tdZwPsInfoList.faces","default.png","MYGH_B_183.png",new Date(),1,8));
		//陕西体检机构备案
		menuSet.add(new TsMenu("034.010","资质机构备案申请","heth_zzjgbasq_zzsb","资质机构备案申请zzsb", Short.valueOf("1"),"/webapp/heth/zzsb/zwQualAgencyFilingAppList.faces","sort.png","my-performance.png",new Date(),1,4));
		menuSet.add(new TsMenu("034.011","资质机构备案审核-初审","heth_zzjgbashcs_zzsb","资质机构备案审核-初审zzsb", Short.valueOf("1"),"/webapp/heth/zzsb/zwTjorginfoApplyList.faces?tag=0","sort.png","my-performance.png",new Date(),1,6));
		menuSet.add(new TsMenu("034.012","资质机构备案审核-终审","heth_zzjgbashzs_zzsb","资质机构备案审核-终审zzsb", Short.valueOf("1"),"/webapp/heth/zzsb/zwTjorginfoApplyList.faces?tag=1","sort.png","my-performance.png",new Date(),1,7));

		//吉林体检机构资质审核
		menuSet.add(new TsMenu("034.025","资质机构审核","heth_zzjgsh_zzsb","资质机构审核", Short.valueOf("1"),"/webapp/heth/zzsb/orgZzCheckList.faces","sort.png","my-performance.png",new Date(),1,25));

		menuSet.add(new TsMenu("001.015","质量控制结果录入","heth_zzsb_zlkzjglr","质量控制结果录入", Short.valueOf("1"),"/webapp/heth/zzsb/qualityControlResultList.faces","sort.png","my-performance.png",new Date(),1,15));
		//山东职卫 资质机构预警配置
		menuSet.add(new TsMenu("034.011","资质机构预警配置","heth_zzsb_zzjgyjpz","资质机构预警配置", Short.valueOf("1"),"/webapp/heth/zzsb/orgZzWarningConfigList.faces","eye.png","pie_chart_yellow.png",new Date(),1,11));
		//重庆职卫 职业技术服务机构检测能力
		menuSet.add(new TsMenu("207","职业技术服务机构检测能力","zyjsfwjgjcnl","职业技术服务机构检测能力", Short.valueOf("0"),"#","slow_S_jdqk_10.png","myghnew_jzxx.png",new Date(),1,89));
		menuSet.add(new TsMenu("207.001","业务范围与检测项目关系配置","heth_zzsb_ywfwyjcxmgxpz","业务范围与检测项目关系配置", Short.valueOf("1"),"/webapp/heth/zzsb/busScopeItemConfig.faces","eye.png","pie_chart_yellow.png",new Date(),1,1));
		menuSet.add(new TsMenu("207.002","业务范围与重点、一般检测项目关系维护","heth_zzsb_busiMainItem","业务范围与重点、一般检测项目关系维护", Short.valueOf("1"),"/webapp/heth/zzsb/tbZwBusiMainItemList.faces","eye.png","pie_chart_yellow.png",new Date(),1,2));
		menuSet.add(new TsMenu("207.003","检测能力汇总","heth_zzsb_jcnlhz","检测能力汇总", Short.valueOf("1"),"/webapp/heth/zzsb/jcChkAbilityAnalysis.faces","default.png","MYGH-B-045.png",new Date(),1,3));
		*/
	}

}
