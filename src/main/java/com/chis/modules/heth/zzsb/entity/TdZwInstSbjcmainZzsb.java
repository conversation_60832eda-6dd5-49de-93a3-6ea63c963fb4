package com.chis.modules.heth.zzsb.entity;

import com.google.common.collect.Lists;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 
 * <AUTHOR>
 * @createTime 2016-5-12
 */
@Entity
@Table(name = "TD_ZW_INST_SBJCMAIN")
@SequenceGenerator(name = "TdZwInstSbjcmain", sequenceName = "TD_ZW_INST_SBJCMAIN_SEQ", allocationSize = 1)
public class TdZwInstSbjcmainZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwInstinfoZzsb fkByInstId;
	private Date jcDate;
	private String jcRst;
	private Date createDate = new Date();
	private Integer createManid;
	
	private List<TdZwInstSbjcsubZzsb> subList = Lists.newArrayList();
	
	public TdZwInstSbjcmainZzsb() {
	}

	public TdZwInstSbjcmainZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwInstSbjcmain")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "INST_ID")			
	public TdZwInstinfoZzsb getFkByInstId() {
		return fkByInstId;
	}

	public void setFkByInstId(TdZwInstinfoZzsb fkByInstId) {
		this.fkByInstId = fkByInstId;
	}	
			
	@Temporal(TemporalType.DATE)		
	@Column(name = "JC_DATE")			
	public Date getJcDate() {
		return jcDate;
	}

	public void setJcDate(Date jcDate) {
		this.jcDate = jcDate;
	}	
			
	@Column(name = "JC_RST")	
	public String getJcRst() {
		return jcRst;
	}

	public void setJcRst(String jcRst) {
		this.jcRst = jcRst;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}

	@OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
	public List<TdZwInstSbjcsubZzsb> getSubList() {
		return subList;
	}

	public void setSubList(List<TdZwInstSbjcsubZzsb> subList) {
		this.subList = subList;
	}	
	
	
			
}