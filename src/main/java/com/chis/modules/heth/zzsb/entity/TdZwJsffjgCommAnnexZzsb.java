package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2021-3-11
 */
@Entity
@Table(name = "TD_ZW_JSFFJG_COMM_ANNEX")
@SequenceGenerator(name = "TdZwJsffjgCommAnnex", sequenceName = "TD_ZW_JSFFJG_COMM_ANNEX_SEQ", allocationSize = 1)
public class TdZwJsffjgCommAnnexZzsb implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TsUnit fkByOrgId;
	private Integer annexType;
	private String fileName;
	private String filePath;
	private Integer regZoneId;
	private Integer unitLevel;
	private String unitName;
	private Integer createManid;
	private Date createDate;

	private TsZone fkByRegZoneId;
	private Integer addSource;
	
	public TdZwJsffjgCommAnnexZzsb() {
	}

	public TdZwJsffjgCommAnnexZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwJsffjgCommAnnex")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TsUnit getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TsUnit fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Column(name = "ANNEX_TYPE")	
	public Integer getAnnexType() {
		return annexType;
	}

	public void setAnnexType(Integer annexType) {
		this.annexType = annexType;
	}	
			
	@Column(name = "FILE_NAME")	
	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Column(name = "REG_ZONE_ID")	
	public Integer getRegZoneId() {
		return regZoneId;
	}

	public void setRegZoneId(Integer regZoneId) {
		this.regZoneId = regZoneId;
	}	
			
	@Column(name = "UNIT_LEVEL")	
	public Integer getUnitLevel() {
		return unitLevel;
	}

	public void setUnitLevel(Integer unitLevel) {
		this.unitLevel = unitLevel;
	}	
			
	@Column(name = "UNIT_NAME")	
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	@Transient
    public TsZone getFkByRegZoneId() {
        return fkByRegZoneId;
    }

    public void setFkByRegZoneId(TsZone fkByRegZoneId) {
        this.fkByRegZoneId = fkByRegZoneId;
    }

	@Column(name = "ADD_SOURCE")
	public Integer getAddSource() {
		return addSource;
	}

	public void setAddSource(Integer addSource) {
		this.addSource = addSource;
	}
}