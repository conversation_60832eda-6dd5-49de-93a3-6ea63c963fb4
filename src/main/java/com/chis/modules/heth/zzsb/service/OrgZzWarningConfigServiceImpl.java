package com.chis.modules.heth.zzsb.service;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.TbZwOrgWarnConfig;
import com.chis.modules.heth.zzsb.entity.TbZwOrgWarnSub;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @description:
 */
@Service
@Transactional(readOnly = false)
public class OrgZzWarningConfigServiceImpl extends AbstractTemplate {


    /**
     * <p>Description：保存 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public void saveBatchWarningConfig(List<TbZwOrgWarnConfig> orgWarnConfigList, List<TbZwOrgWarnConfig> specialWarnConfigList, List<TbZwOrgWarnConfig> delWarnConfigList) {
        if(!CollectionUtils.isEmpty(orgWarnConfigList)){
            for (TbZwOrgWarnConfig warnConfig : orgWarnConfigList) {
                this.upsertEntity(warnConfig);
            }
        }
        if(!CollectionUtils.isEmpty(specialWarnConfigList)){
            for (TbZwOrgWarnConfig warnConfig : specialWarnConfigList) {
                this.upsertEntity(warnConfig);
                // 删除子表
                StringBuilder sql = new StringBuilder();
                sql.append(" delete from TB_ZW_ORG_WARN_SUB where MAIN_ID=").append(warnConfig.getRid());
                this.executeSql(sql.toString(), null);
                // 新增子表
                if (StringUtils.isBlank(warnConfig.getSpecialPostRid())) {
                    continue;
                }
                List<String> list = StringUtils.string2list(warnConfig.getSpecialPostRid(), ",");
                for (String rid : list) {
                    TbZwOrgWarnSub orgWarnSub = new TbZwOrgWarnSub();
                    orgWarnSub.setFkByWarnTypeId(new TsSimpleCode(Integer.parseInt(rid)));
                    orgWarnSub.setFkByMainId(warnConfig);
                    this.upsertEntity(orgWarnSub);
                }
            }
        }
        if (!CollectionUtils.isEmpty(delWarnConfigList)) {
            for (TbZwOrgWarnConfig warnConfig : delWarnConfigList) {
                this.upsertEntity(warnConfig);
            }
        }
    }
    /**
     * <p>Description：通过主表rid 获取子表记录 </p>
     * <p>Author： yzz 2024-07-08 </p>
     */
    public List<Object[]> findWarnSubByMainRid(List<TbZwOrgWarnConfig> specialWarnConfigList) {
        if(CollectionUtils.isEmpty(specialWarnConfigList)){
            return new ArrayList<>();
        }
        List<Integer> rids = new ArrayList<>();
        for (TbZwOrgWarnConfig warnConfig : specialWarnConfigList) {
            rids.add(warnConfig.getRid());
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("list", rids);

        StringBuilder sql = new StringBuilder();
        sql.append(" select T.MAIN_ID,listagg(T1.CODE_NAME,'，') within group (order by T1.num,T1.code_no) as warn_name,listagg(T1.rid,',') within group (order by T1.num,T1.code_no) as warn_rid ");
        sql.append(" from TB_ZW_ORG_WARN_SUB T ");
        sql.append(" inner join TS_SIMPLE_CODE T1 on T.WARN_TYPE_ID = T1.RID ");
        sql.append(" where T.MAIN_ID in(:list) ");
        sql.append(" group by T.MAIN_ID ");
        return this.findDataBySqlNoPage(sql.toString(), paramMap);
    }
}
