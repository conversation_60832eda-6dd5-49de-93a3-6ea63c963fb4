package com.chis.modules.heth.zzsb.utils;

import com.chis.common.utils.JsfUtil;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;

import javax.faces.context.FacesContext;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

/**
 * @Description : 导出excel工具类
 * @ClassAuthor : anjing
 * @Date : 2019/8/13 16:29
 **/
public class ExportExcelUtil {

    /**
    * @Description : 下载excel
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 16:29
    **/
    public static void downloadExcel(HttpServletResponse response,
                                     List<LinkedHashMap<String, Object>> content, String[] title,
                                     String fileName) {
        Workbook wb = new HSSFWorkbook();
        try {
            if (content.size() > 0) {
                wb = createWorkBook(content, title);
            }
            exportExcel(response, null, fileName, wb);
        } catch (final Exception e) {
            e.printStackTrace();
        }
    }

    /**
    * @Description : 创建excel工作区
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 16:30
    **/
    public static Workbook createWorkBook(List<LinkedHashMap<String, Object>> content, String title[]) {
        //获取key值
        Object[] objs = content.get(0).keySet().toArray();
        String[] keys = Arrays.asList(objs).toArray(new String[0]);

        //创建excel工作簿
        Workbook workbook = new HSSFWorkbook();

        //创建第一个sheet（页），并命名
        Sheet sheet = workbook.createSheet("Sheet1");
        //定义样式并设置
        CellStyle cs = workbook.createCellStyle();
        CellStyle cs2 = workbook.createCellStyle();
        setStyle(keys, workbook, sheet, cs, cs2);
        //生成表格
        generateTable(content, title, keys, workbook, sheet, cs, cs2, 0);
        //setAutoWith(keys, sheet);
        return workbook;
    }

    /**
    * @Description : 导出excel
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 16:30
    **/
    private static void exportExcel(HttpServletResponse response, ByteArrayOutputStream os, String fileName, Workbook wb) {
        OutputStream output=null;
        try {
            //设置response参数，可以打开下载页面
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    + new String((fileName + ".xls").getBytes("UTF-8"), "iso-8859-1"));

            output = response.getOutputStream();
            wb.write(output);
            output.flush();
            output.close();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("下载失败！");
        } finally {
            if (output != null) {
                try {
                    output.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        FacesContext.getCurrentInstance().responseComplete();
    }

    /**
    * @Description : 设置单元格字体大小和样式
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 16:30
    **/
    private static void setStyle(String[] keys, Workbook wb, Sheet sheet, CellStyle cs, CellStyle cs2) {
        //创建两种字体
        Font f = wb.createFont();
        Font f2 = wb.createFont();
        //创建第一种字体样式（用于列名）
        f.setFontName("宋体");
        f.setFontHeightInPoints((short) 10);
        f.setColor(IndexedColors.BLACK.getIndex());
        f.setBoldweight(Font.BOLDWEIGHT_BOLD);
        //创建第二种字体样式（用于值）
        f2.setFontName("宋体");
        f2.setFontHeightInPoints((short) 10); // 设置行高
        //设置第一种单元格的样式（用于列名）
        cs.setFont(f);
        cs.setBorderLeft(CellStyle.BORDER_THIN); // 左边框
        cs.setBorderRight(CellStyle.BORDER_THIN); // 右边框
        cs.setBorderTop(CellStyle.BORDER_THIN); // 上边框
        cs.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        cs.setAlignment(CellStyle.ALIGN_CENTER); // 居中
        //设置第二种单元格的样式（用于值）
        cs2.setFont(f2);
        cs2.setBorderLeft(CellStyle.BORDER_THIN); // 左边框
        cs2.setBorderRight(CellStyle.BORDER_THIN); // 右边框
        cs2.setBorderTop(CellStyle.BORDER_THIN); // 上边框
        cs2.setBorderBottom(CellStyle.BORDER_THIN); // 下边框
        //cs2.setAlignment(XSSFCellStyle.ALIGN_LEFT);// 左右居中
        //cs2.setVerticalAlignment(XSSFCellStyle.VERTICAL_CENTER);// 上下居中
        cs2.setAlignment(CellStyle.ALIGN_CENTER); // 居中
        cs2.setWrapText(true);  // 设置自动换行
    }

    /**
    * @Description : 生成表格
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 16:30
    **/
    private static void generateTable(List<LinkedHashMap<String, Object>> content,
                                      String[] title, String[] keys, Workbook wb, Sheet sheet, CellStyle cs, CellStyle cs2, int start) {
        //创建第一行
        Row row = sheet.createRow(start);
        Font f = wb.createFont();
        CellStyle style = wb.createCellStyle();
        style.setBorderLeft(CellStyle.BORDER_THIN); // 左边框
        style.setBorderRight(CellStyle.BORDER_THIN); // 右边框
        style.setBorderTop(CellStyle.BORDER_THIN); // 上边框
        style.setBorderBottom(CellStyle.BORDER_THIN); // 下边框

        //存储最大列宽
        Map<Integer,Integer> maxWidth = new HashMap<Integer,Integer>();

        //设置列名
        for (int i = 0; i < title.length; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(cs);
            maxWidth.put(i, cell.getStringCellValue().getBytes().length  * 256 + 1200);
        }
        CellStyle cellStyle = wb.createCellStyle();
        DataFormat dataFormat =wb.createDataFormat();
        cellStyle.setDataFormat(dataFormat.getFormat("@"));

        //设置每行每列的值
        for (short offset = 0; offset < content.size(); offset++) {
            //创建一行，在页sheet上
            Row row1 = sheet.createRow(start + offset + 1);
            // 在row行上创建一个方格
            for (short j = 0; j < keys.length; j++) {
                Cell cell = row1.createCell(j);
                cell.setCellStyle(cellStyle);
                cell.setCellValue(content.get(offset).get(keys[j]) == null ? " " : content
                        .get(offset).get(keys[j]).toString());
                cell.setCellStyle(cs2);

                if(j == (keys.length-1)) {
                    f.setColor(IndexedColors.RED.getIndex());
                    style.setFont(f);
                    cell.setCellStyle(style);
                    cell.setCellType(HSSFCellStyle.ALIGN_LEFT);
                }

                int length = cell.getStringCellValue().getBytes().length  * 256 + 1200;
                //这里把宽度最大限制到15000
                if (length>15000){
                    length = 15000;
                }
                maxWidth.put((int) j, Math.max(length, maxWidth.get((int) j)));
            }
        }

        // 列宽自适应
        for (int i = 0; i < title.length; i++) {
            sheet.setColumnWidth(i, maxWidth.get(i));
        }
    }

    /**
    * @Description : 设置单元格宽度
    * @MethodAuthor: anjing
    * @Date : 2019/8/13 16:31
    **/
    private static void setAutoWith(String[] keys, Sheet sheet) {
        for (int i = 0; i < keys.length; i++) {
            //自适应宽度
            sheet.autoSizeColumn(i);
        }
    }
}
