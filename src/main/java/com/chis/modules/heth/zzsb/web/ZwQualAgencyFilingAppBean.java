package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.logic.OutCarInstPO;
import com.chis.modules.heth.zzsb.logic.QualOrgApplyParamPO;
import com.chis.modules.heth.zzsb.service.ZwQualAgencyFilingAppService;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * <p>类描述：资质机构备案申请列表页 </p>
 * @ClassAuthor: yzz
 * @date： 2022年04月14日
 **/
@ManagedBean(name = "zwQualAgencyFilingAppBean")
@ViewScoped
public class ZwQualAgencyFilingAppBean extends FacesEditBean implements IProcessData {

    public SystemModuleServiceImpl moduleService = (SystemModuleServiceImpl)SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    protected CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    protected ZwQualAgencyFilingAppService qualAgencyFilingAppService = (ZwQualAgencyFilingAppService) SpringContextHolder.getBean(ZwQualAgencyFilingAppService.class);

    /**查询-机构资质*/
    private List<TsSimpleCode> qualTypeList=new ArrayList<>();
    /**查询-选中的机构资质*/
    private Integer selectQualType;
    /**查询-申请类型*/
    private List<TsSimpleCode> applyTypeList;
    /**查询-选中的申请类型rid*/
    private String selectApplyType;
    /**查询-选中的申请类型名称*/
    private String applyTypeName;
    /**查询-审核状态*/
    private List<SelectItem> stateList;
    /**查询-选中的审核状态*/
    private String[] states;
    /**rid*/
    private Integer rid;
    /**弹框-选中的机构资质*/
    private Integer dialogSelectQualType;
    /**弹框-选中的申请类型rid*/
    private Integer dialogSelectApplyType;
    /**机构资质 key 码表rid  value extendS1*/
    private Map<Integer,String> qualTypeMap;
    /**申请类型 key 码表rid  value extendS1*/
    private Map<Integer,String> applyTypeMap;
    /**保存后获取的对象*/
    private TdZwTjorginfoApply tdZwQualOrgApply;
    /** 是否详情 1详情 */
    private Integer ifView;
    /** 保存或者修改 编辑页的Bean */
    private TdZwQualOrgApplyInfoBaseBean applyInfoBaseBean;
    /** 提供给子页面的码表 key codeTypeName value对应码表集合 */
    private Map<String,List<TsSimpleCode>> allSimpleCodeMap;
    /** 是否显示外出开展职业健康检查工作能力地区(外检车辆) 无配置参数返回null */
    private Boolean ifHethFj;
    /** 职业健康检查机构是否有人员汇总及填报说明人员汇总 */
    private Boolean ifPersonSummary;
    /** 检查机构是否有质量管理体系 */
    private Boolean ifQualAnnex;
    /** 资质备案有无有效期限 */
    private Boolean ifValidityPeriod;
    /** 行政区划所属地区集合 */
    private List<TsZone> orgZoneList;
    /** 所有地区集合 */
    private List<TsZone> allZoneList;



    public ZwQualAgencyFilingAppBean(){
        this.ifSQL=true;
        this.applyInfoBaseBean = new TdZwQualOrgApplyInfoBaseBean();
        this.allSimpleCodeMap = new HashMap<>();
        this.qualTypeMap=new HashMap<>();
        this.applyTypeMap=new HashMap<>();

        //初始化配置文件参数
        this.initPropertiesParam();
        //初始化码表参数
        this.initAllSimpleCodeMap();

        //按钮权限
        List<String> list = new ArrayList<>();
        for(String btn:Global.getBtnSet()){
            if ("zzsb_zzjgbasq_zyjkjcjg".equals(btn)) {
                list.add("1");
            }else if("zzsb_zzjgbasq_zybzdjg".equals(btn)){
                list.add("2");
            }
        }
        //机构资质 码表初始化
        List<TsSimpleCode> qualTypes=this.allSimpleCodeMap.get("5562");
        if(!CollectionUtils.isEmpty(qualTypes)&&!CollectionUtils.isEmpty(list)){
            for(TsSimpleCode qualType:qualTypes){
                if(StringUtils.isNotBlank(qualType.getExtendS1())&&list.contains(qualType.getExtendS1())){
                    qualTypeList.add(qualType);
                    qualTypeMap.put(qualType.getRid(),qualType.getExtendS1());
                }
            }
        }
        //申请类型码表初始化
        applyTypeList=this.allSimpleCodeMap.get("5563");
        if(!CollectionUtils.isEmpty(applyTypeList)){
            for(TsSimpleCode tsc:applyTypeList){
                applyTypeMap.put(tsc.getRid(),tsc.getExtendS1());
            }
        }
        //初始化审核状态
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem("0", "待提交"));
        this.stateList.add(new SelectItem("1", "待初审"));
        this.stateList.add(new SelectItem("2", "初审驳回"));
        this.stateList.add(new SelectItem("3", "初审通过"));
        this.stateList.add(new SelectItem("4", "终审驳回"));
        this.stateList.add(new SelectItem("5", "终审通过"));
        String zoneGb = Global.getUser().getTsUnit().getTsZone().getZoneGb().substring(0,2);
        this.orgZoneList = this.moduleService.findZoneListWithAllZoneByFlag(false, zoneGb);
        this.allZoneList = new ArrayList<>();

        this.searchAction();
    }

    /**
     * <p>方法描述：资质备案添加确定</p>
     * @MethodAuthor： yzz
     * @Date：2022-04-15
     **/
    public void QualOrgSaveAction(){
        boolean bool=false;
        if(dialogSelectQualType==null){
            JsfUtil.addErrorMessage("请选择机构资质！");
            bool=true;
        }
        if(dialogSelectApplyType==null){
            JsfUtil.addErrorMessage("请选择申请类型！");
            bool=true;
        }
        if(bool){
            return;
        }

        //判断机构资质
        if(!qualTypeMap.isEmpty()&&StringUtils.isNotBlank(qualTypeMap.get(dialogSelectQualType))){
            //职业健康检查机构、职业病诊断机构  查询资质信息表中的状态
            String flag= qualTypeMap.get(dialogSelectQualType);
            List<Object[]> orgInfoArr = qualAgencyFilingAppService.findOrgInfoStateByOrgId(flag,Global.getUser().getTsUnit().getRid());
            //查询 是否有备案中的数据
            int existCount = this.qualAgencyFilingAppService.findCountInApplying(Global.getUser().getTsUnit().getRid(), flag);
            if(!applyTypeMap.isEmpty()&&StringUtils.isNotBlank(applyTypeMap.get(dialogSelectApplyType))&&"1".equals(applyTypeMap.get(dialogSelectApplyType))){
                if(CollectionUtils.isEmpty(orgInfoArr)){
                    //初次备案
                    if(existCount > 0){
                        JsfUtil.addErrorMessage(("1".equals(flag)?"检查机构":"诊断机构")+"初次备案正在审核中，请勿重复申请！");
                    }else{
                        //保存
                        tdZwQualOrgApply=qualAgencyFilingAppService.saveQualOrgInfo(dialogSelectQualType,dialogSelectApplyType);
                        RequestContext.getCurrentInstance().execute("PF('AddDialog').hide()");
                        this.afterSaveAction();
                    }
                }else{
                    JsfUtil.addErrorMessage("已保存过"+("1".equals(flag)?"检查机构":"诊断机构")+"资质，不可申请初次备案！");
                }
            }else{
                if(CollectionUtils.isEmpty(orgInfoArr)||(!CollectionUtils.isEmpty(orgInfoArr)&&!"1".equals(orgInfoArr.get(0)[1].toString()))) {
                    //变更申请  查询资质是否提交
                    JsfUtil.addErrorMessage("请先提交"+("1".equals(flag)?"检查机构":"诊断机构")+"资质！");
                }else{
                    if(existCount > 0){
                        JsfUtil.addErrorMessage(("1".equals(flag)?"检查机构":"诊断机构")+"申请变更正在审核中，请勿重复申请！");
                    }else {
                        //保存
                        tdZwQualOrgApply=qualAgencyFilingAppService.saveQualOrgChange(dialogSelectQualType,dialogSelectApplyType,flag);
                        RequestContext.getCurrentInstance().execute("PF('AddDialog').hide()");
                        this.afterSaveAction();
                    }
                }
            }
        }
    }


    @Override
    public void addInit() {
        dialogSelectQualType=null;
        dialogSelectApplyType=null;
        RequestContext.getCurrentInstance().execute("PF('AddDialog').show()");
    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }

    @Override
    public String[] buildHqls() {
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        StringBuilder sb = new StringBuilder();
        sb.append(" select T.rid,T1.CODE_NAME as qualType,T2.CODE_NAME as applyType, ");
        sb.append(" CASE WHEN T3.ZONE_TYPE >2 THEN SUBSTR(T3.FULL_NAME, INSTR(T3.FULL_NAME,'_')+1) ELSE T3.FULL_NAME END ZONE_NAME, ");
        sb.append(" T.APPLY_DATE,T.STATE,T.FST_CHK_DATE,T4.UNITNAME as FSTUNITNAME,T.FINAL_CHK_DATE,T5.UNITNAME as FINALUNITNAME,T.FST_BACK_RSN,T.FINAL_BACK_RSN,T.QUAL_TYPE_ID,T.APPLY_TYPE_ID,T1.NUM as QUAL_TYPE_NUM,T2.NUM as APPLY_TYPE_NUM ");
        sb.append(" from TD_ZW_TJORGINFO_APPLY T ");
        sb.append(" left join TS_SIMPLE_CODE T1 on T.QUAL_TYPE_ID=T1.RID ");
        sb.append(" left join TS_SIMPLE_CODE T2 on T.APPLY_TYPE_ID=T2.RID ");
        sb.append(" left join TS_ZONE T3 on T3.RID=T.ZONE_ID ");
        sb.append(" left join TS_UNIT T4 on T4.RID=T.FST_CHK_ORG_ID ");
        sb.append(" left join TS_UNIT T5 on T5.RID=T.FINAL_CHK_ORG_ID ");
        sb.append(" WHERE T.ORG_ID = ").append(Global.getUser().getTsUnit().getRid());
        //审核状态
        if(this.states!=null&&this.states.length==1) {
            sb.append(" AND T.STATE=").append(states[0]);
        }else if(this.states!=null&&this.states.length>1){
            sb.append(" AND T.STATE in (").append(StringUtils.list2string(Arrays.asList(states),",")).append(")");
        }
        // 申请类型
        if(StringUtils.isNotBlank(this.selectApplyType)) {
            sb.append(" AND T.APPLY_TYPE_ID in(").append(selectApplyType).append(")");
        }
        // 机构资质
        if(this.selectQualType!=null) {
            sb.append(" AND T.QUAL_TYPE_ID =").append(selectQualType);
        }

        String h2 = "SELECT COUNT(*) FROM ("+sb+")";
        String h1 = "SELECT * FROM ("+sb+")"+" ORDER BY QUAL_TYPE_NUM,APPLY_TYPE_NUM desc,APPLY_DATE desc";
        return new String[] { h1, h2 };
    }

    @Override
    public void processData(List<?> list) {
    }

    /**
     * @Description: 添加后跳转到具体添加信息页
     *
     * @MethodAuthor pw,2022年04月16日
     */
    public void afterSaveAction(){
        this.ifView = 0;
        this.rid = null == this.tdZwQualOrgApply ? null : this.tdZwQualOrgApply.getRid();
        this.preModAction();
        this.searchAction();
    }

    /**
     * @Description: 添加或者修改前验证
     * 验证后跳转
     * @MethodAuthor pw,2022年04月16日
     */
    public void preModAction(){
        if(null == this.ifHethFj || null == this.ifPersonSummary || null == this.ifQualAnnex){
            JsfUtil.addErrorMessage("缺少关键参数！");
            return;
        }
        if(null == this.rid){
            return;
        }
        QualOrgApplyParamPO paramPO = new QualOrgApplyParamPO();
        paramPO.setOrgApplyRid(this.rid);
        paramPO.setIfHethFj(this.ifHethFj);
        paramPO.setIfQualAnnex(this.ifQualAnnex);
        paramPO.setIfPersonSummary(this.ifPersonSummary);
        paramPO.setIfFinalAudit(false);
        paramPO.setIfValidityPeriod(this.ifValidityPeriod);
        paramPO.setIfView(null == this.ifView || 1 == this.ifView);
        paramPO.setAllSimpleCodeMap(this.allSimpleCodeMap);
        paramPO.setOrgZoneList(this.orgZoneList);
        paramPO.setAllZoneList(this.allZoneList);
        this.applyInfoBaseBean = new TdZwQualOrgApplyInfoBaseBean(paramPO);
        if(null == this.applyInfoBaseBean.getTjorginfoApply()){
            JsfUtil.addErrorMessage("操作失败！");
            return;
        }
        this.modInitAction();
        RequestContext.getCurrentInstance().update("tabView");
    }

    /**
     * @Description: 子保存
     *
     * @MethodAuthor pw,2022年04月18日
     */
    public void applyInfoSave(){
        if(null == this.applyInfoBaseBean || null == this.applyInfoBaseBean.getTjorginfoApply()){
            return;
        }
        this.applyInfoBaseBean.getTjorginfoApply().setState(0);
        this.applyInfoBaseBean.saveAction();
        this.searchAction();
    }

    /**
     * @Description: 子提交
     *
     * @MethodAuthor pw,2022年04月18日
     */
    public void applyInfoSubmit(){
        if(null == this.applyInfoBaseBean || null == this.applyInfoBaseBean.getTjorginfoApply()){
            return;
        }
        this.applyInfoBaseBean.getTjorginfoApply().setState(1);
        this.applyInfoBaseBean.getTjorginfoApply().setApplyDate(new Date());
        this.applyInfoBaseBean.submitAction();
        this.searchAction();
    }

    /**
     * @Description: 子撤销
     *
     * @MethodAuthor pw,2022年04月18日
     */
    public void applyInfoCancel(){
        if(null == this.applyInfoBaseBean || null == this.applyInfoBaseBean.getTjorginfoApply()){
            return;
        }
        this.applyInfoBaseBean.getTjorginfoApply().setState(0);
        this.applyInfoBaseBean.cancelAction();
        this.searchAction();
    }

    /**
     * @Description: 初始化配置文件参数
     *
     * @MethodAuthor pw,2022年04月16日
     */
    private void initPropertiesParam(){
        //若不需要控制一定有这个参数 可默认false
        this.ifHethFj = true;
        this.ifPersonSummary = false;
        this.ifQualAnnex = true;
        this.ifValidityPeriod = true;
    }


    /**
     * @Description: 初始化码表参数
     *
     * @MethodAuthor pw,2022年04月22日
     */
    private void initAllSimpleCodeMap(){
        //机构资质（资质备案流程）
        this.initAllSimpleCodeMapHelper("5562", false);
        //申请类型码表初始化
        this.initAllSimpleCodeMapHelper("5563", false);
        //法人职务
        this.initAllSimpleCodeMapHelper("2002", false);
        //职业健康检查机构服务项目
        this.initAllSimpleCodeMapHelper("5018", false);
        //诊断机构服务项目
        this.initAllSimpleCodeMapHelper("5020", false);
        //质量管理体系
        this.initAllSimpleCodeMapHelper("5507", false);
        //健康检查资质服务小类
        this.initAllSimpleCodeMapHelper("5527", false);
    }

    /**
     * @Description: 辅助初始化码表
     *
     * @MethodAuthor pw,2022年04月22日
     */
    private void initAllSimpleCodeMapHelper(String typeNo, boolean ifGetAll){
        List<TsSimpleCode> list = this.allSimpleCodeMap.get(typeNo);
        if(CollectionUtils.isEmpty(list)){
            list = ifGetAll ? this.commService.findallSimpleCodesByTypeIdOrderByNum(typeNo) :
                    this.commService.findLevelSimpleCodesByTypeId(typeNo);
            this.allSimpleCodeMap.put(typeNo, list);
        }
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<TsSimpleCode> getApplyTypeList() {
        return applyTypeList;
    }

    public void setApplyTypeList(List<TsSimpleCode> applyTypeList) {
        this.applyTypeList = applyTypeList;
    }

    public String getSelectApplyType() {
        return selectApplyType;
    }

    public void setSelectApplyType(String selectApplyType) {
        this.selectApplyType = selectApplyType;
    }

    public String getApplyTypeName() {
        return applyTypeName;
    }

    public void setApplyTypeName(String applyTypeName) {
        this.applyTypeName = applyTypeName;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public List<TsSimpleCode> getQualTypeList() {
        return qualTypeList;
    }

    public void setQualTypeList(List<TsSimpleCode> qualTypeList) {
        this.qualTypeList = qualTypeList;
    }

    public Integer getSelectQualType() {
        return selectQualType;
    }

    public void setSelectQualType(Integer selectQualType) {
        this.selectQualType = selectQualType;
    }

    public Integer getDialogSelectQualType() {
        return dialogSelectQualType;
    }

    public void setDialogSelectQualType(Integer dialogSelectQualType) {
        this.dialogSelectQualType = dialogSelectQualType;
    }

    public Integer getDialogSelectApplyType() {
        return dialogSelectApplyType;
    }

    public void setDialogSelectApplyType(Integer dialogSelectApplyType) {
        this.dialogSelectApplyType = dialogSelectApplyType;
    }

    public Integer getIfView() {
        return ifView;
    }

    public void setIfView(Integer ifView) {
        this.ifView = ifView;
    }

    public TdZwQualOrgApplyInfoBaseBean getApplyInfoBaseBean() {
        return applyInfoBaseBean;
    }

    public void setApplyInfoBaseBean(TdZwQualOrgApplyInfoBaseBean applyInfoBaseBean) {
        this.applyInfoBaseBean = applyInfoBaseBean;
    }

    public Map<String, List<TsSimpleCode>> getAllSimpleCodeMap() {
        return allSimpleCodeMap;
    }

    public void setAllSimpleCodeMap(Map<String, List<TsSimpleCode>> allSimpleCodeMap) {
        this.allSimpleCodeMap = allSimpleCodeMap;
    }

    public Boolean getIfHethFj() {
        return ifHethFj;
    }

    public void setIfHethFj(Boolean ifHethFj) {
        this.ifHethFj = ifHethFj;
    }

    public Boolean getIfPersonSummary() {
        return ifPersonSummary;
    }

    public void setIfPersonSummary(Boolean ifPersonSummary) {
        this.ifPersonSummary = ifPersonSummary;
    }

    public Boolean getIfQualAnnex() {
        return ifQualAnnex;
    }

    public void setIfQualAnnex(Boolean ifQualAnnex) {
        this.ifQualAnnex = ifQualAnnex;
    }

    public Boolean getIfValidityPeriod() {
        return ifValidityPeriod;
    }

    public void setIfValidityPeriod(Boolean ifValidityPeriod) {
        this.ifValidityPeriod = ifValidityPeriod;
    }
    
    public TdZwTjorginfoApply getTdZwQualOrgApply() {
        return tdZwQualOrgApply;
    }

    public void setTdZwQualOrgApply(TdZwTjorginfoApply tdZwQualOrgApply) {
        this.tdZwQualOrgApply = tdZwQualOrgApply;
    }

    public List<TsZone> getOrgZoneList() {
        return orgZoneList;
    }

    public void setOrgZoneList(List<TsZone> orgZoneList) {
        this.orgZoneList = orgZoneList;
    }

    public List<TsZone> getAllZoneList() {
        return allZoneList;
    }

    public void setAllZoneList(List<TsZone> allZoneList) {
        this.allZoneList = allZoneList;
    }
}
