package com.chis.modules.heth.zzsb.logic;

import com.chis.common.utils.StringUtils;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Arrays;
import java.util.List;

/**
 * <p>类描述：    </p>
 *
 * @ClassAuthor yzz，年月日，ZwJkItemPO
 **/
public class ZwJkItemPO {
    private TsSimpleCode jcItem;
    private List<TsSimpleCode> jcItemList;
    private String[] selectedSerItmDetail;
    private String selectedItemDetail;
    private String[] selectedJcItem;
    private String[] selectedIfExterInspect;
    private String selectedSerItmDetailStr;


    public TsSimpleCode getJcItem() {
        return jcItem;
    }

    public void setJcItem(TsSimpleCode jcItem) {
        this.jcItem = jcItem;
    }

    public List<TsSimpleCode> getJcItemList() {
        return jcItemList;
    }

    public void setJcItemList(List<TsSimpleCode> jcItemList) {
        this.jcItemList = jcItemList;
    }

    public String[] getSelectedSerItmDetail() {
        return selectedSerItmDetail;
    }

    public void setSelectedSerItmDetail(String[] selectedSerItmDetail) {
        this.selectedSerItmDetail = selectedSerItmDetail;
    }

    public String getSelectedItemDetail() {
        return selectedItemDetail;
    }

    public void setSelectedItemDetail(String selectedItemDetail) {
        this.selectedItemDetail = selectedItemDetail;
    }

    public String[] getSelectedJcItem() {
        return selectedJcItem;
    }

    public void setSelectedJcItem(String[] selectedJcItem) {
        this.selectedJcItem = selectedJcItem;
    }

    public String[] getSelectedIfExterInspect() {
        return selectedIfExterInspect;
    }

    public void setSelectedIfExterInspect(String[] selectedIfExterInspect) {
        this.selectedIfExterInspect = selectedIfExterInspect;
    }

    public String getSelectedSerItmDetailStr() {
        selectedSerItmDetailStr = "";
        if(selectedSerItmDetail!=null && selectedSerItmDetail.length>0){
            String s = StringUtils.list2string(Arrays.asList(selectedSerItmDetail),",");
            selectedSerItmDetailStr = ","+s+",";
        }
        return selectedSerItmDetailStr;
    }

    public void setSelectedSerItmDetailStr(String selectedSerItmDetailStr) {
        this.selectedSerItmDetailStr = selectedSerItmDetailStr;
    }
}
