package com.chis.modules.heth.zzsb.entity;

import javax.persistence.*;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2022-4-14
 */
@Entity
@Table(name = "TD_ZW_WORKDEPT_APPLY")
@SequenceGenerator(name = "TdZwWorkdeptApply", sequenceName = "TD_ZW_WORKDEPT_APPLY_SEQ", allocationSize = 1)
public class TdZwWorkdeptApply implements java.io.Serializable {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwTjorgpsnsApply fkByEmpId;
	private TsSimpleCode fkByDeptId;
	private Date createDate;
	private Integer createManid;
	
	public TdZwWorkdeptApply() {
	}

	public TdZwWorkdeptApply(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwWorkdeptApply")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "EMP_ID")			
	public TdZwTjorgpsnsApply getFkByEmpId() {
		return fkByEmpId;
	}

	public void setFkByEmpId(TdZwTjorgpsnsApply fkByEmpId) {
		this.fkByEmpId = fkByEmpId;
	}	
			
	@ManyToOne
	@JoinColumn(name = "DEPT_ID")			
	public TsSimpleCode getFkByDeptId() {
		return fkByDeptId;
	}

	public void setFkByDeptId(TsSimpleCode fkByDeptId) {
		this.fkByDeptId = fkByDeptId;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}	
			
}