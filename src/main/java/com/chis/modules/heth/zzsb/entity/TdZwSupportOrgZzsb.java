package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

@Entity
@Table(name = "TD_ZW_SUPPORT_ORG")
@SequenceGenerator(name = "TdZwSupportOrgZzsbSeq", sequenceName = "TD_ZW_SUPPORT_ORG_SEQ", allocationSize = 1)
public class TdZwSupportOrgZzsb implements Serializable,IZwOrginfoZzsb{
    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TsUnit tsUnit;
    private String orgName;
    private String orgAddr;
    private String orgFz;
    private String orgFzzw;
    private String linkMan;
    private String linkMb;
    private String linkTel;
    private String fax;
    private String zipcode;
    private String email;
    private Short state;
    private Integer cancelState;
    private Date cancelDate;
    private Date createDate;
    private Integer createManid;

    private String jcItems;
    private Date validDate;
    private String delPsnIds;
    private String creditCode;
    private Integer outWorkPower;
    private String certNo;
    private Date firstGetday;
    private List<TdZwSupportPsnsZzsb> tdZwSupportPsns = new LinkedList<TdZwSupportPsnsZzsb>();

    private List<TdZwSupportTech> supportTechList = new LinkedList<TdZwSupportTech>();

    private List<TdZwSupportQual> supportQualList = new LinkedList<TdZwSupportQual>();

    private List<TdZwSupportChk> supportChkList = new LinkedList<TdZwSupportChk>();

    private List<TdZwSupportFund> supportFundList = new LinkedList<TdZwSupportFund>();
    private TdZwSupportTrc supportTrc = new TdZwSupportTrc();
    private List<TdZwSupportAdvance> advanceList  = new LinkedList<>();

    private TdZwSupportPlace supportPlaces;

    private String orgTypeName;
    private String quaTypeName;
    /**
     * 仪器设备配置信息对应码表rid列表
     */
    private List<Integer> supportInstSimpleCodeRidList;
    /**
     * 支撑机构—职业健康教育和健康促进存储传输对象
     * */
    private TdZwSupportHealth supportHealth;
    /** 能力建设ID集合 */
    private List<Integer> abilityList;

    /*机构类型*/
    private TsSimpleCode fkByTypeId;

    /**主数据时间戳*/
    private Date masterDataTime;
    private Date modifyDate;
    private Integer modifyManid;
    public TdZwSupportOrgZzsb(){}
    public TdZwSupportOrgZzsb(Integer rid){
        this.rid = rid;
    }

    @Override
    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwSupportOrgZzsbSeq")
    public Integer getRid() {
        return this.rid;
    }

    @Override
    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Override
    @ManyToOne
    @JoinColumn(name = "ORG_ID" )
    public TsUnit getTsUnit() {
        return this.tsUnit;
    }

    @Override
    public void setTsUnit(TsUnit tsUnit) {
        this.tsUnit = tsUnit;
    }

    @Override
    @Column(name = "ORG_NAME", length = 100)
    public String getOrgName() {
        return this.orgName;
    }

    @Override
    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Override
    @Column(name = "ORG_ADDR", length = 200)
    public String getOrgAddr() {
        return this.orgAddr;
    }

    @Override
    public void setOrgAddr(String orgAddr) {
        this.orgAddr = orgAddr;
    }

    @Override
    @Column(name = "ORG_FZ", length = 50)
    public String getOrgFz() {
        return this.orgFz;
    }

    @Override
    public void setOrgFz(String orgFz) {
        this.orgFz = orgFz;
    }

    @Override
    @Column(name = "ORG_FZZW", length = 50)
    public String getOrgFzzw() {
        return this.orgFzzw;
    }

    @Override
    public void setOrgFzzw(String orgFzzw) {
        this.orgFzzw = orgFzzw;
    }

    @Override
    @Column(name = "LINK_MAN", length = 50)
    public String getLinkMan() {
        return this.linkMan;
    }

    @Override
    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    @Override
    @Column(name = "LINK_MB", length = 50)
    public String getLinkMb() {
        return this.linkMb;
    }

    @Override
    public void setLinkMb(String linkMb) {
        this.linkMb = linkMb;
    }

    @Override
    @Column(name = "LINK_TEL", length = 50)
    public String getLinkTel() {
        return this.linkTel;
    }

    @Override
    public void setLinkTel(String linkTel) {
        this.linkTel = linkTel;
    }

    @Override
    @Column(name = "FAX", length = 50)
    public String getFax() {
        return this.fax;
    }

    @Override
    public void setFax(String fax) {
        this.fax = fax;
    }

    @Override
    @Column(name = "ZIPCODE", length = 10)
    public String getZipcode() {
        return this.zipcode;
    }

    @Override
    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    @Override
    @Column(name = "EMAIL", length = 50)
    public String getEmail() {
        return this.email;
    }

    @Override
    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    @Transient
    public String getCertNo() {
        return this.certNo;
    }

    @Override
    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    @Override
    @Transient
    public Date getFirstGetday() {
        return this.firstGetday;
    }

    @Override
    public void setFirstGetday(Date firstGetday) {
        this.firstGetday = firstGetday;
    }

    @Transient
    @Override
    public Date getFilingDate() {
        return null;
    }

    @Override
    public void setFilingDate(Date filingDate) {

    }

    @Override
    @Column(name = "CREATE_DATE" )
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCreateDate() {
        return this.createDate;
    }

    @Override
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Override
    @Column(name = "CREATE_MANID" , precision = 22, scale = 0)
    public Integer getCreateManid() {
        return this.createManid;
    }

    @Override
    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Transient
    @Override
    public List<IZwAnnexZzsb> getZwAnnex() {
        return null;
    }

    @Override
    public void setZwAnnex(List<IZwAnnexZzsb> zwAnnexList) {
    }

    @Transient
    @Override
    public List<IZwInstZzsb> getZwInst() {
        return null;
    }

    @Override
    public void setZwInst(List<IZwInstZzsb> zwInstList) {
    }

    @Transient
    @Override
    public List<IZwItemsZzsb> getZwItems() {
        return null;
    }

    @Override
    public void setZwItems(List<IZwItemsZzsb> zwItemsList) {

    }

    @Transient
    @Override
    public List<IZwOrgPsnsZzsb> getZwOrgPsns() {
        if(null != this.tdZwSupportPsns && this.tdZwSupportPsns.size() > 0) {
            List<IZwOrgPsnsZzsb> list = new ArrayList<IZwOrgPsnsZzsb>();
            for(TdZwSupportPsnsZzsb t : this.tdZwSupportPsns) {
                list.add(t);
            }
            return list;
        }else {
            return null;
        }
    }

    @Override
    public void setZwOrgPsns(List<IZwOrgPsnsZzsb> zwOrgPsnsList) {
        if(null != zwOrgPsnsList && zwOrgPsnsList.size() > 0) {
            this.tdZwSupportPsns = new ArrayList<TdZwSupportPsnsZzsb>();
            for(IZwOrgPsnsZzsb t : zwOrgPsnsList) {
                this.tdZwSupportPsns.add((TdZwSupportPsnsZzsb) t);
            }
        }else if(null == zwOrgPsnsList) {
            this.tdZwSupportPsns = null;
        }else {
            this.tdZwSupportPsns = new ArrayList<TdZwSupportPsnsZzsb>(0);
        }
    }

    @Transient
    @Override
    public List<IZwSvritmZzsb> getZwSvritm() {
        return null;
    }

    @Override
    public void setZwSvritm(List<IZwSvritmZzsb> zwSvritmList) {
    }

    @Transient
    @Override
    public String getJcItems() {
        return this.jcItems;
    }

    @Override
    public void setJcItems(String jcItems) {
        this.jcItems = jcItems;
    }

    @Override
    @Column(name = "STATE")
    public Short getState() {
        return state;
    }

    @Override
    public void setState(Short state) {
        this.state = state;
    }

    @Override
    @Column(name = "CANCEL_STATE")
    public Integer getCancelState() {
        return cancelState;
    }

    @Override
    public void setCancelState(Integer cancelState) {
        this.cancelState = cancelState;
    }

    @Override
    @Temporal(TemporalType.DATE)
    @Column(name = "CANCEL_DATE")
    public Date getCancelDate() {
        return cancelDate;
    }

    @Override
    public void setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
    }

    @Override
    @Transient
    public Date getValidDate() {
        return validDate;
    }

    @Override
    public void setValidDate(Date validDate) {
        this.validDate = validDate;
    }
    @Transient
    @Override
    public String getDelPsnIds() {
        return delPsnIds;
    }

    @Override
    public void setDelPsnIds(String delPsnIds) {
        this.delPsnIds = delPsnIds;
    }
    @Transient
    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Transient
    @Override
    public TsSimpleCode getFkByLevelId() {
        return null;
    }

    @Override
    public void setFkByLevelId(TsSimpleCode fkByLevelId) {

    }

    @Transient
    @Override
    public Integer getOutWorkPower() {
        return outWorkPower;
    }

    @Transient
    @Override
    public void setOutWorkPower(Integer outWorkPower) {
        this.outWorkPower = outWorkPower;
    }

    @Transient
    @Override
    public List<IZwOrgOutRangeZzsb> getZwOrgOutRanges() {
        return null;
    }

    @Override
    public void setZwOrgOutRanges(List<IZwOrgOutRangeZzsb> zwOrgOutRangeList) {

    }

    @Transient
    @Override
    public String getLabAddr() {
        return null;
    }

    @Override
    public void setLabAddr(String labAddr) {

    }

    @Transient
    @Override
    public String getCommuniAddr() {
        return null;
    }

    @Override
    public void setCommuniAddr(String communiAddr) {

    }

    @Transient
    @Override
    public TsSimpleCode getFkByUnitTypeId() {
        return null;
    }

    @Override
    public void setFkByUnitTypeId(TsSimpleCode fkByUnitTypeId) {

    }

    @Transient
    @Override
    public Integer getRcdOrgId() {
        return null;
    }

    @Override
    public void setRcdOrgId(Integer rcdOrgId) {

    }

    @Transient
    @Override
    public List<TdZwTjorgOutRangeZzsb> getTdZwTjorgOutRanges() {
        return null;
    }

    @Override
    public void setTdZwTjorgOutRanges(List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges) {

    }

    @Transient
    @Override
    public String getZoneApproves() {
        return null;
    }

    @Override
    public void setZoneApproves(String zoneApproves) {

    }

    @Transient
    @Override
    public String getRcdUnitName() {
        return null;
    }

    @Override
    public void setRcdUnitName(String rcdUnitName) {

    }

    @Transient
    @Override
    public Integer getCheckRst() {
        return null;
    }

    @Override
    public void setCheckRst(Integer checkRst) {

    }

    @Transient
    @Override
    public String getAuditAdv() {
        return null;
    }

    @Override
    public void setAuditAdv(String auditAdv) {

    }

    @Transient
    @Override
    public TsUserInfo getFkByChkPsnId() {
        return null;
    }

    @Override
    public void setFkByChkPsnId(TsUserInfo fkByChkPsnId) {

    }

    @Transient
    @Override
    public Date getLastSmtDate() {
        return null;
    }

    @Override
    public void setLastSmtDate(Date lastSmtDate) {

    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdZwSupportOrg",orphanRemoval = true)
    public List<TdZwSupportPsnsZzsb> getTdZwSupportPsns() {
        return tdZwSupportPsns;
    }

    public void setTdZwSupportPsns(List<TdZwSupportPsnsZzsb> tdZwSupportPsns) {
        this.tdZwSupportPsns = tdZwSupportPsns;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId",orphanRemoval = true)
    public List<TdZwSupportQual> getSupportQualList() {
        return supportQualList;
    }

    public void setSupportQualList(List<TdZwSupportQual> supportQualList) {
        this.supportQualList = supportQualList;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId",orphanRemoval = true)
    public List<TdZwSupportChk> getSupportChkList() {
        return supportChkList;
    }

    public void setSupportChkList(List<TdZwSupportChk> supportChkList) {
        this.supportChkList = supportChkList;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId",orphanRemoval = true)
    public List<TdZwSupportFund> getSupportFundList() {
        return supportFundList;
    }

    public void setSupportFundList(List<TdZwSupportFund> supportFundList) {
        this.supportFundList = supportFundList;
    }

    @Transient
    public List<Integer> getSupportInstSimpleCodeRidList() {
        return supportInstSimpleCodeRidList;
    }

    public void setSupportInstSimpleCodeRidList(List<Integer> supportInstSimpleCodeRidList) {
        this.supportInstSimpleCodeRidList = supportInstSimpleCodeRidList;
    }

    @Transient
    public TdZwSupportTrc getSupportTrc() {
        return supportTrc;
    }

    public void setSupportTrc(TdZwSupportTrc supportTrc) {
        this.supportTrc = supportTrc;
    }

    @Transient
    public List<TdZwSupportAdvance> getAdvanceList() {
        return advanceList;
    }

    public void setAdvanceList(List<TdZwSupportAdvance> advanceList) {
        this.advanceList = advanceList;
    }
    @Transient
    public String getOrgTypeName() {
        return orgTypeName;
    }

    public void setOrgTypeName(String orgTypeName) {
        this.orgTypeName = orgTypeName;
    }

    @Transient
    public String getQuaTypeName() {
        return quaTypeName;
    }

    public void setQuaTypeName(String quaTypeName) {
        this.quaTypeName = quaTypeName;
    }

    @Transient
    @Override
    public TdZwSupportPlace getSupportPlaces() {
        return supportPlaces;
    }

    public void setSupportPlaces(TdZwSupportPlace supportPlaces) {
        this.supportPlaces = supportPlaces;
    }

    @Override
    @ManyToOne
    @JoinColumn(name = "TYPE_ID")
    public TsSimpleCode getFkByTypeId() {
        return fkByTypeId;
    }

    @Override
    public void setFkByTypeId(TsSimpleCode fkByTypeId) {
        this.fkByTypeId=fkByTypeId;
    }

    @Transient
    public TdZwSupportHealth getSupportHealth() {
        return supportHealth;
    }

    public void setSupportHealth(TdZwSupportHealth supportHealth) {
        this.supportHealth = supportHealth;
    }

    @Transient
    public List<Integer> getAbilityList() {
        return abilityList;
    }

    public void setAbilityList(List<Integer> abilityList) {
        this.abilityList = abilityList;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId",orphanRemoval = true)
    public List<TdZwSupportTech> getSupportTechList() {
        return supportTechList;
    }

    public void setSupportTechList(List<TdZwSupportTech> supportTechList) {
        this.supportTechList = supportTechList;
    }
    @Column(name = "MASTER_DATA_TIME")
    @Override
    public Date getMasterDataTime() {
        return masterDataTime;
    }
    @Override
    public void setMasterDataTime(Date masterDataTime) {
        this.masterDataTime=masterDataTime;
    }
    @Column(name = "MODIFY_DATE")
    @Override
    public Date getModifyDate() {
        return modifyDate;
    }
    @Override
    public void setModifyDate(Date modifyDate) {
        this.modifyDate=modifyDate;
    }
    @Column(name = "MODIFY_MANID")
    @Override
    public Integer getModifyManid() {
        return modifyManid;
    }
    @Override
    public void setModifyManid(Integer modifyManid) {
        this.modifyManid=modifyManid;
    }


}
