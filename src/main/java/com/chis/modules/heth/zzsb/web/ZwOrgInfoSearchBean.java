package com.chis.modules.heth.zzsb.web;

import com.chis.common.entity.ExcelExportObject;
import com.chis.common.pojo.PieChartParamPojo;
import com.chis.common.pojo.PieDataPojo;
import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.modules.heth.zzsb.entity.vo.SupportInstAbilityVO;
import com.chis.modules.heth.zzsb.entity.vo.TechTypeVo;
import com.chis.modules.heth.zzsb.logic.ZwJkItemPO;
import com.chis.modules.heth.zzsb.logic.ZwJkItemRowPO;
import com.chis.modules.heth.zzsb.service.ZzsbZwIntellReportServiceImpl;
import com.chis.modules.heth.zzsb.service.ZzsbZwPsnPlanServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.DownLoadUtil;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import com.github.abel533.echarts.series.Pie;
import com.google.common.collect.Lists;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.*;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;

/**
 * 资质查询 Created by wlj on 2015-04-22.
 */
@ManagedBean(name = "zwOrgInfoSearchZzsbBean")
@ViewScoped
public class ZwOrgInfoSearchBean extends FacesEditBean {
	private static final long serialVersionUID = 8439387263034336056L;
	/** session对象 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/** EJB 会话Bean */
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private ZzsbZwIntellReportServiceImpl zwIntellReportServiceImpl = SpringContextHolder
			.getBean(ZzsbZwIntellReportServiceImpl.class);
    protected ZzsbZwPsnPlanServiceImpl psnPlanServiceImpl = SpringContextHolder.getBean(ZzsbZwPsnPlanServiceImpl.class);
	/* 地区集合地区集合 */
	private List<TsZone> zoneList;
	/* 查询条件：地区名称 */
	private String searchZoneName;
	/* 查询条件：地区编码 */
	private String searchZoneCode;
	/* 查询条件：地区级别 */
	private String searchZoneType;
	/* 资质类型 */
	private Integer searchZwOrhType;
	/* 单位名称 */
	private String searchUnitName;
	/* 选择项目 */
	private Object[] selectObj;

	private Integer zwOrgType;
	/* 资质信息 */
	private IZwOrginfoZzsb zwOrginfoEntity;
	/* 服务项目 */
	private List<String> serviceList;
	/* 附件 */
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList1 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList2 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList3 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList4 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList5 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList6 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList7 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList8 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList9 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected List<TdZwJsffjgCommAnnexZzsb> zwAnnexList20 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
	protected TdZwJsffjgCommAnnexZzsb selectZwAnnex;
	/** 页面展示项目集合 */
	private List<Object[]> showItemList;
	/** 页面展示码表Map */
	Map<TsSimpleCode, List<TsSimpleCode>> diagMap = null;
	private String[] searchState = new String[] { "1" };
	// 导出文件名称
	private String exportFileName;
	
	private TdZwPsninfoZzsb tdZwPsninfo = new TdZwPsninfoZzsb();


    private TdZwPsntjMainZzsb firstTjMain = new TdZwPsntjMainZzsb();
    private List<TdZwPsntjSubZzsb> firstTjSubList = Lists.newArrayList();
    private TdZwPsntjMainZzsb lastTjMain = new TdZwPsntjMainZzsb();
    private List<TdZwPsntjSubZzsb> lastTjSubList = Lists.newArrayList();
    private TdZwPsntjSubZzsb tjSub;

    private List<TdZwInstJcmainZzsb> jcMainList  = Lists.newArrayList();
    private TdZwInstJcmainZzsb jcMain = new TdZwInstJcmainZzsb();
    private List<TdZwInstJcsubZzsb> jcSubList = Lists.newArrayList();
    private TdZwInstJcsubZzsb jcSub = new TdZwInstJcsubZzsb();

    private List<TdZwInstSbjcmainZzsb> sbJcMainList  = Lists.newArrayList();
    private TdZwInstSbjcmainZzsb sbJcMain = new TdZwInstSbjcmainZzsb();
    private List<TdZwInstSbjcsubZzsb> sbJcSubList = Lists.newArrayList();
    private TdZwInstSbjcsubZzsb sbJcSub = new TdZwInstSbjcsubZzsb();

    /** 人员实体 */
    protected IZwOrgPsnsZzsb selectZwOrgPsn;
    /*仪器信息*/
    protected IZwInstZzsb selectOrgInst;
    /**职业健康项目*/
    private List<TsSimpleCode> jkList=new ArrayList<TsSimpleCode>();
    /**放射项目*/
    private List<TsSimpleCode> fsList=new ArrayList<TsSimpleCode>();
    /**职业病*/
    private List<TsSimpleCode> zyList=new ArrayList<TsSimpleCode>();

	/**提前预警天数**/
	private Integer earlyDays;
	/**提前预警输入框默认禁用**/
	private Boolean earlyDaysDisabled = Boolean.TRUE;
	/**提前输入复选框默认不勾选*/
	private Boolean earlyDaysChecked = Boolean.FALSE;
	
	private TdzwPsnInfoZzsbBase psnInfoBase;
	
	/**职业病*/
    private List<TsSimpleCode> zwjsList=new ArrayList<TsSimpleCode>();
    /**是否福建职卫*/
    private boolean ifHethFj;
    private String zoneNames;
	
    /**是否按地区备案，0否1是*/
    private String ifZoneRecord;
    private List<TdZwTjorgRecordZzsb> tjorgRecords;
    /**是否有人员汇总及填报说明人员汇总0否1是*/
    private String ifPersonSummary;
    /**人员汇总信息*/
    private List<List<String>> personSummaryList;
    /**填报说明*/
    private String fillDesc;

	/** 是否有质量管理体系 0否 1是 */
	private String ifQualAnnex;
	/** 质量管理体系制度集合  码表5507 num排序 需初始化*/
	private List<TsSimpleCode> institutionList;
	/** 质量管理体系制度 顶级制度集合 */
	private List<TsSimpleCode> topOfInstitutionList;
	/** 质量管理体系制度 顶级制度对应Map */
	private Map<Integer, TsSimpleCode> topInstitutionMap;
	/** 质量管理体系制度 顶级制度下的分制度 */
	private Map<Integer, List<TsSimpleCode>> institutionMap;
	/** 质量管理体系制度 各制度对应的List */
	private Map<Integer,List<TdZwTjorgQualAnnexZzsb>> qualAnnexMap;
	/**资质类型集合 */
	private List<SelectItem> searchZwOrhTypeList;

	/**资质类型集合*/
	private List<Integer> typeList;
	/*服务项目小类*/
	protected List<ZwJkItemPO> jkItemList= new ArrayList<>();
	protected List<ZwJkItemRowPO> jkItemRowList= new ArrayList<>();
	/** 外检车辆列表 */
	private List<TdZwTjorgOutcarZzsb> tjorgOutcarZzsbList;

	/** 检查机构资质备案有无有效期限 空或0：无 1：有 默认空或0*/
	protected String ifValidityPeriod;
	/** 有无资质机构备案审核流程 空或0：无 1：有 默认空或0*/
	protected String ifReviewProcess;
	/** 是否有服务明细 空或0：无 1：有 默认空或0*/
	protected String ifServiceDetails;

	/**
	 * 是否显示备案管理机构
	 */
	private Boolean ifShowRcdOrg = Boolean.FALSE;
	/**
	 * 备案管理机构
	 */
	private List<TsSimpleCode> rcdOrgList;
	private Map<Integer, String> rcdOrgMap;
	/**
	 * 检查机构资质有无批准的执业区域
	 */
	private Boolean ifZoneApprove = Boolean.FALSE;
	private String zoneApproveName = "";

	/**
	 * 外检车辆版本，1：陕西
	 */
	private String outCarVersion;
	/**是否显示【质量控制结果】0：不显示；1：显示*/
	private String zkCheckRst;
	List<Object[]> zwCheckRstList ;

	private String ifShowCheckOrgAnaly;

	private String serviceOverviewStr1;
	private String serviceOverviewStr2;
	private String serviceOverviewStr3;
	private String pieJson;
	/** 职业病诊断机构是否显示备案单位名称、放射卫生技术服务机构与职业卫生技术服务机构是否显示资质批准单位 0否1是 */
	private String ifShowApproveUnit;
	/** 四类资质的资质附件是否显示房屋所有权证或租赁合同，0否1是，默认为0 */
	private String ifHaveHouseAnnex;

	private TdZwPsnZzlbZzsb psnZzlbNew;
	/** 支撑机构有无科研、学术信息 默认无 */
	private Boolean hasResearchAndAcademicInfo = Boolean.FALSE;

	/** 学术培训列表数据 */
	private List<List<TdZwSupportTrain>> showSupportTrainList = new ArrayList<>();
	/** 标题用学术培训类别 */
	private List<List<TsSimpleCode>> showAcademicList = new ArrayList<>();
	/** 用于支撑机构学术培训列表遍历动态数据 */
	private Integer academicDataSize = 0;

	/** 论文情况 */
	private List<TdZwSupportThesis> thesisList;
	/** 专著情况 */
	private List<TdZwSupportMomograph> monographList;
	/** 新立项课题 */
	private List<TdZwSupportTopic> supportTopics1;
	/** 在研课题 */
	private List<TdZwSupportTopic> supportTopics2;
	/** 获奖课题 */
	private List<TdZwSupportTopic> supportTopics3;
	/** 专利 */
	private List<Object[]> patentList;
	/** 标准 */
	private List<Object[]> standardList;
	/**
	 * 支撑机构-科技攻关能力
	 */
	protected TdZwSupportTrc supportTrc;
	protected List<TdZwSupportAdvance> advanceList;
	/**
	 * 支撑机构-仪器设备配置信息-仪器设备码表
	 */
	protected List<TsSimpleCode> supportInstSimpleCodeList;
	/**
	 * 支撑机构-仪器设备配置信息-VO
	 */
	protected List<SupportInstAbilityVO> supportInstVOList;

	/**支撑机构-单位类型  1:疾控  2：职业防治院*/
	protected String unitType;

	/** 支撑机构-能力建设码表 */
	private List<TsSimpleCode> abilitySimpleCodeList;
	/** 支撑机构-能力建设信息 */
	private List<SupportInstAbilityVO> abilityShowList;
	/** 支撑机构-职业健康教育和健康促进 */
	private TdZwSupportHealth supportHealth;
	/**支撑机构-职业防治院-技术支撑能力*/
	protected List<TsSimpleCode> techTypeList;
	protected List<TechTypeVo> techTypesList;
	/****************检测能力*********************/

	/**检测能力*/
	private List<Object[]> chkAbilityList;
	/** 查询条件 检测项目名称*/
	private String searchProjectName;
	/** 查询条件 检测方法*/
	private String searchJcWay;
	/** 查询条件 状态*/
	private List<String> searchStates;
	/****************检测能力*********************/

	public ZwOrgInfoSearchBean() {
		// 本单位
		TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
		}
		// 地区信息初始化
		searchZoneCode = tsZone.getZoneGb();
		searchZoneName = tsZone.getZoneName();
		searchZoneType = tsZone.getZoneType().toString();
		// 地区初始化
		if (ObjectUtil.isEmpty(this.zoneList)) {
			//省级可查全国
			String zoneGb = new Short("2").equals(tsZone.getRealZoneType()) ? "" : tsZone.getZoneGb();
			this.zoneList = this.commService.findZoneListByGbAndType(zoneGb, true, "", "");
		}
		//资质类型初始化
		initTypeList();
		//支撑机构 初始化支撑能力码表
		initTechTypeTree();
		ifSQL = true;
		searchAction();
		
		psnInfoBase = new TdzwPsnInfoZzsbBase();
    	psnInfoBase.setValidPsnInfoId(false);
    	TdZwPsninfoZzsb psninfo = new TdZwPsninfoZzsb();
    	psnInfoBase.setPsnInfo(psninfo);
    	psnInfoBase.setPsnZzlb(new TdZwPsnZzlbZzsb());
    	psnInfoBase.setPsnZzlbNew(new TdZwPsnZzlbZzsb());
        String sIfHethFj = PropertyUtils.getValue("zzsb.ifHethFj");
		this.ifHethFj = "true".equals(sIfHethFj);
        this.ifZoneRecord = PropertyUtils.getValue("zzsb.ifZoneRecord");
        this.ifPersonSummary = PropertyUtils.getValue("zzsb.ifPersonSummary");

		ifValidityPeriod = PropertyUtils.getValueWithoutException("zzsb.ifValidityPeriod");
		ifReviewProcess = PropertyUtils.getValueWithoutException("zzsb.ifReviewProcess");
		ifServiceDetails = PropertyUtils.getValueWithoutException("zzsb.ifServiceDetails");
		this.zkCheckRst = PropertyUtils.getValueWithoutException("zzsb.zkCheckRst");
		//备案管理机构
		String ifShowRcdOrgString = PropertyUtils.getValueWithoutException("zzsb.ifShowRcdOrg");
		this.ifShowRcdOrg = "1".equals(ifShowRcdOrgString);
		this.rcdOrgList = new ArrayList<>();
		this.rcdOrgList = this.commService.findLevelSimpleCodesByTypeId("5565");
		this.rcdOrgMap = new HashMap<>();
		for (TsSimpleCode simpleCode : this.rcdOrgList) {
			this.rcdOrgMap.put(simpleCode.getRid(), simpleCode.getCodeName());
		}
		String ifZoneApproveString = PropertyUtils.getValueWithoutException("zzsb.ifZoneApprove");
		this.ifZoneApprove = "1".equals(ifZoneApproveString);

		this.outCarVersion = StringUtils.objectToString(PropertyUtils.getValueWithoutException("zzsb.outCarVersion"));
		this.ifShowCheckOrgAnaly = PropertyUtils.getValueWithoutException("zzsb.ifShowCheckOrgAnaly");
		this.ifShowApproveUnit = PropertyUtils.getValueWithoutException("zzsb.ifShowApproveUnit");
		this.ifHaveHouseAnnex = PropertyUtils.getValueWithoutException("zzsb.ifHaveHouseAnnex");
		this.hasResearchAndAcademicInfo = "1".equals(PropertyUtils.getValueWithoutException("zzsb.hasResearchAndAcademiaInfo"));
	}

	/**
	 * <p>Description：初始化技术支撑能力 下拉单选 带层级 </p>
	 * <p>Author： yzz 2023-10-24 </p>
	 */
	private void initTechTypeTree() {
		//技术支撑能力 树形
		this.techTypeList=new ArrayList<>();
		this.techTypesList=new ArrayList<>();

		// 初始化业务分类
		this.techTypeList=this.commService.findSimpleCodeListOrderByLevelNumNo("5605");
		if (!CollectionUtils.isEmpty(techTypeList)) {
			// 只有第一层
			Set<String> firstLevelNoSet = new LinkedHashSet<String>();
			// 所有类别
			Map<String, TsSimpleCode> menuMap = new HashMap<String, TsSimpleCode>();
			for (TsSimpleCode t : techTypeList) {
				menuMap.put(t.getCodeLevelNo(), t);
				if (StringUtils.isNotBlank(t.getCodeLevelNo())) {
					if (StringUtils.containsNone(t.getCodeLevelNo(), ".")) {
						firstLevelNoSet.add(t.getCodeLevelNo());
					}
				}
			}
			// 由第一层开始遍历
			for (String ln : firstLevelNoSet) {
				TechTypeVo techTypeVo=new TechTypeVo();
				techTypeVo.setCodeName(menuMap.get(ln).getCodeName());
				techTypeVo.setSelNums(0);
				List<TsSimpleCode> techType=new ArrayList<>();
				for (TsSimpleCode tsSimpleCode : techTypeList) {
					if(tsSimpleCode.getCodeLevelNo().contains(ln+".")){
						techType.add(tsSimpleCode);
					}
				}
				techTypeVo.setTechTypes(techType);
				techTypesList.add(techTypeVo);
			}
		}
	}
	/**
	 *  <p>方法描述：资质类型初始化</p>
	 * @MethodAuthor hsj 2022/3/1 18:16
	 */
	private void initTypeList() {
		searchZwOrhTypeList=new ArrayList<>();
		typeList=new ArrayList<>();
		Set<String> btnSet= Global.getBtnSet();
		if (!CollectionUtils.isEmpty(btnSet)) {
			for (String s : btnSet) {
				if ("zzsb_zyjkjcjg".equals(s)) {
					this.searchZwOrhTypeList.add(new SelectItem(0,"职业健康检查机构"));
					typeList.add(0);
				}else if ("zzsb_fswsjsfwjg".equals(s)) {
					this.searchZwOrhTypeList.add(new SelectItem(1,"放射卫生技术服务机构"));
					typeList.add(1);
				}else if ("zzsb_zybzdjg".equals(s)) {
					this.searchZwOrhTypeList.add(new SelectItem(2,"职业病诊断机构"));
					typeList.add(2);
				}else if ("zzsb_zywsjsfwjg".equals(s)) {
					this.searchZwOrhTypeList.add(new SelectItem(3,"职业卫生技术服务机构"));
					typeList.add(3);
				}else if ("zzsb_zcjg".equals(s)) {
					this.searchZwOrhTypeList.add(new SelectItem(4,"技术支撑机构"));
					typeList.add(4);
				}
			}
		}
		if(CollectionUtils.isEmpty(typeList)){
			typeList.add(0);
			typeList.add(1);
			typeList.add(2);
			typeList.add(3);
			typeList.add(4);
			this.searchZwOrhTypeList.add(new SelectItem(0,"职业健康检查机构"));
			this.searchZwOrhTypeList.add(new SelectItem(1,"放射卫生技术服务机构"));
			this.searchZwOrhTypeList.add(new SelectItem(2,"职业病诊断机构"));
			this.searchZwOrhTypeList.add(new SelectItem(3,"职业卫生技术服务机构"));
			this.searchZwOrhTypeList.add(new SelectItem(4,"技术支撑机构"));
		}
		Collections.sort(searchZwOrhTypeList,new Comparator (){

			@Override
			public int compare(Object o1, Object o2) {
				SelectItem item1=(SelectItem)o1;
				SelectItem item2=(SelectItem)o2;
				return item1.getValue().toString().compareTo(item2.getValue().toString());
			}
		});
	}

	/**
	 * 提前天数输入框禁用启用
	 */
	public void earlyDaysAction(){
		if(earlyDaysChecked){
			earlyDaysDisabled = Boolean.FALSE;
			earlyDays = 60;
		}else{
			earlyDaysDisabled = Boolean.TRUE;
			earlyDays = null;
		}
	}
	@Override
	public void addInit() {

	}

	@Override
	public void viewInit() {
		psnZzlbNew=new TdZwPsnZzlbZzsb();
		zwOrginfoEntity = zwIntellReportServiceImpl.findZwOrgInfoById(Integer.valueOf(selectObj[1].toString()),
				Integer.valueOf(selectObj[8].toString()));
		//初始化机构类型
		TsUnit tsUnit = commService.findTsUnitByRid(zwOrginfoEntity.getTsUnit().getRid());
		if(null != tsUnit && tsUnit.getRid() !=null && !CollectionUtils.isEmpty(tsUnit.getTsUnitAttrs())) {
			for(TsUnitAttr attr: tsUnit.getTsUnitAttrs()) {
				if("2001".equals(attr.getTsBsSort().getSortCode())){
					this.unitType="1";
					break;
				}else if("2020".equals(attr.getTsBsSort().getSortCode())){
					this.unitType="2";
					break;
				}
			}
		}
		//基本信息-支撑机构 初始化
		initSupportBaseInfo();
		List<IZwOrgPsnsZzsb> a = zwOrginfoEntity.getZwOrgPsns();
		// 手机号码加密
		String telMbNo = String.valueOf(null == zwOrginfoEntity.getLinkMb() ? ""
				: zwOrginfoEntity.getLinkMb());
		if (StringUtils.isNotBlank(telMbNo)) {
			String num = "***************".substring(0, telMbNo.length() - 4);
			zwOrginfoEntity
					.setLinkMb(num.concat(telMbNo.substring(telMbNo.length() - 4)));
		}

		// 电话号码加密
		String telNo = String.valueOf(null == zwOrginfoEntity.getLinkTel() ? ""
				: zwOrginfoEntity.getLinkTel());
		if (StringUtils.isNotBlank(telNo)) {
			String num = "***************".substring(0, telNo.length() - 4);
			zwOrginfoEntity
					.setLinkTel(num.concat(telNo.substring(telNo.length() - 4)));
		}

		//批准的执业区域
		this.zoneApproveName = "";
				List<TdZwTjorgOutRangeZzsb> tdZwTjorgOutRanges = zwOrginfoEntity.getTdZwTjorgOutRanges();
		List<String> zoneApproveNameList = new ArrayList<>();
		if (!CollectionUtils.isEmpty(tdZwTjorgOutRanges)) {
			for (TdZwTjorgOutRangeZzsb tdZwTjorgOutRange : tdZwTjorgOutRanges) {
				TsZone fkByZoneId = tdZwTjorgOutRange.getFkByZoneId();
				zoneApproveNameList.add(fkByZoneId.getZoneName());
			}
			if (!CollectionUtils.isEmpty(zoneApproveNameList)) {
				this.zoneApproveName = StringUtils.list2string(zoneApproveNameList, "，");
			}
		}
		
		// 服务项目
		initServiceItem();
		// 附件
		initAnnex();
		// 检查项目
		if (selectObj[1].toString().equals("0")) {
			initZwJcItems();
			//服务明细
			initSerItem(this.jkItemList);
			this.jkItemRowList = convertZwJkItemPOToRowPO(jkItemList);
			//外检车辆
			initOutcar();
		}
		//获取人员证书编号
		if(null != zwOrginfoEntity.getZwOrgPsns() && zwOrginfoEntity.getZwOrgPsns().size()>0){
			for(IZwOrgPsnsZzsb t:zwOrginfoEntity.getZwOrgPsns()){
				if(null != t.getTdZwPsninfo().getZzlbList() && t.getTdZwPsninfo().getZzlbList().size()>0){
					StringBuilder sb=new StringBuilder();
					for(TdZwPsnZzlbZzsb zz:t.getTdZwPsninfo().getZzlbList()){
						sb.append(",").append(zz.getCentNo());
					}
					t.getTdZwPsninfo().setCertNoStr(sb.deleteCharAt(0).toString());
				}
			}
		}

        zoneNames = "";
        if(!CollectionUtils.isEmpty(zwOrginfoEntity.getZwOrgOutRanges())) {
            StringBuilder nameSb = new StringBuilder();
            for(IZwOrgOutRangeZzsb orgOutRange : zwOrginfoEntity.getZwOrgOutRanges()) {
                nameSb.append("，").append(orgOutRange.getFkByZoneId().getZoneName());
            }
            zoneNames = nameSb.toString().substring(1);
        }
        if ("1".equals(ifZoneRecord)) {//按地区备案
        	if ("0".equals(selectObj[1].toString())) {
        		initTjorgRecords();
        	}
		}
        if ("1".equals(ifPersonSummary)) {
        	this.fillDesc = commService.findParamValue("PSNINFO_FILL_DESC");
		}
        if ("1".equals(ifPersonSummary)) {
        	initPersonSummary();
		}
        initPsnType();
		initInstitutionList();
		initCheckRst();
		RequestContext.getCurrentInstance().execute("PF('OrgTableView').select(0)");
		this.initSupportTrain();
		this.initSupportTrc();
		this.initZwResearchTopicsInfo();

		initSupportInst();
		this.initAbility();
		this.initSupportPlace();
		//查询条件初始化
		this.searchProjectName = null;
		this.searchJcWay=null;
		this.searchStates = new ArrayList<>();
		this.searchStates.add("1");//默认启用
		//表格初始化
		DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tabView:viewForm:tabView:chkAbilityTable");
		if (dataTable != null) {
			dataTable.setFirst(0);
			dataTable.setRows(20);
		}
		//检测能力初始化
		this.initCheckCapability();
	}
	/**
	 *  <p>方法描述：检测能力初始化</p>
	 * @MethodAuthor hsj 2025-04-02 14:17
	 */
	public void initCheckCapability() {
		this.chkAbilityList = this.zwIntellReportServiceImpl.findChkAbility(zwOrginfoEntity.getTsUnit().getRid(),searchProjectName,searchJcWay,searchStates);
	}

	/**
	 * <p>Description：支撑机构基本信息详情初始化 </p>
	 * <p>Author： yzz 2023-10-27 </p>
	 */
	public void initSupportBaseInfo(){
		//具备资质能力
		List<TdZwSupportQual> supportQualList = zwIntellReportServiceImpl.findSupportQualByMainId(zwOrginfoEntity.getRid());
		if(!CollectionUtils.isEmpty(supportQualList)) {
			StringBuilder str=new StringBuilder();
			Collections.sort(supportQualList, new Comparator<TdZwSupportQual>() {
				@Override
				public int compare(TdZwSupportQual o1, TdZwSupportQual o2) {
					if (ObjectUtil.isNotNull(o1.getFkByTypeId().getNum()) && ObjectUtil.isNotNull(o2.getFkByTypeId().getNum())) {
						int i = o1.getFkByTypeId().getNum().compareTo(o2.getFkByTypeId().getNum());
						if(i == 0){
							return o1.getFkByTypeId().getCodeNo().compareTo(o2.getFkByTypeId().getCodeNo());
						}
						return i;
					} else if (ObjectUtil.isNotNull(o1.getFkByTypeId().getNum())) {
						return -1;
					} else if (ObjectUtil.isNotNull(o2.getFkByTypeId().getNum())) {
						return 1;
					} else {
						return o1.getFkByTypeId().getCodeNo().compareTo(o2.getFkByTypeId().getCodeNo());
					}
				}
			});
			for (TdZwSupportQual supportQual : supportQualList) {
				str.append("，").append(supportQual.getFkByTypeId().getCodeName());
			}
			zwOrginfoEntity.setQuaTypeName(str.substring(1));
		}
		//支撑机构—职业病因素监测开展情况
		List<TdZwSupportChk> supportChkList = zwIntellReportServiceImpl.findSupportChkByMainId(zwOrginfoEntity.getRid());
		if(CollectionUtils.isEmpty(supportChkList)){
			zwOrginfoEntity.setSupportChkList(new ArrayList<TdZwSupportChk>());
		}else{
			zwOrginfoEntity.setSupportChkList(supportChkList);
		}
		//支撑机构—财政投入情况
		List<TdZwSupportFund> supportFundList = zwIntellReportServiceImpl.findSupportFundByMainId(zwOrginfoEntity.getRid());
		if(CollectionUtils.isEmpty(supportFundList)){
			zwOrginfoEntity.setSupportFundList(new ArrayList<TdZwSupportFund>());
		}else{
			zwOrginfoEntity.setSupportFundList(supportFundList);
		}
		//支撑机构—技术支撑能力
		List<TdZwSupportTech> supportTechList = zwIntellReportServiceImpl.findSupportTechByMainId(zwOrginfoEntity.getRid());
		if(CollectionUtils.isEmpty(supportTechList)){
			zwOrginfoEntity.setSupportTechList(new ArrayList<TdZwSupportTech>());
		}else{
			zwOrginfoEntity.setSupportTechList(supportTechList);
		}
		//初始化支撑能力
		this.initTechType();
	}


	/**
	 * <p>Description：初始化支撑能力 </p>
	 * <p>Author： yzz 2023-10-30 </p>
	 */
	public void initTechType(){
		//初始化
		if(!CollectionUtils.isEmpty(this.techTypesList)){
			for (TechTypeVo techTypeVo : techTypesList) {
				techTypeVo.setSelNums(0);
				techTypeVo.setSmallTypeNames(null);
				if(!CollectionUtils.isEmpty(techTypeVo.getTechTypes())){
					for (TsSimpleCode techType : techTypeVo.getTechTypes()) {
						techType.setIfSelected(false);
					}
				}
			}
		}
		//初始化支撑能力
		if(!CollectionUtils.isEmpty(zwOrginfoEntity.getSupportTechList())){
			//树状结构排序
			Collections.sort(zwOrginfoEntity.getSupportTechList(), new Comparator<TdZwSupportTech>() {
				@Override
				public int compare(TdZwSupportTech o1, TdZwSupportTech o2) {
					if(ObjectUtil.isNotNull(o1.getFkByTechTypeId().getCodeLevelNo()) && ObjectUtil.isNotNull(o2.getFkByTechTypeId().getCodeLevelNo())){
						int k = o1.getFkByTechTypeId().getCodeLevelNo().compareTo(o2.getFkByTechTypeId().getCodeLevelNo());
						if(k == 0){
							if (ObjectUtil.isNotNull(o1.getFkByTechTypeId().getNum()) && ObjectUtil.isNotNull(o2.getFkByTechTypeId().getNum())) {
								int i = o1.getFkByTechTypeId().getNum().compareTo(o2.getFkByTechTypeId().getNum());
								if(i == 0){
									return o1.getFkByTechTypeId().getCodeNo().compareTo(o2.getFkByTechTypeId().getCodeNo());
								}
								return i;
							} else if (ObjectUtil.isNotNull(o1.getFkByTechTypeId().getNum())) {
								return -1;
							} else if (ObjectUtil.isNotNull(o2.getFkByTechTypeId().getNum())) {
								return 1;
							} else {
								return o1.getFkByTechTypeId().getCodeNo().compareTo(o2.getFkByTechTypeId().getCodeNo());
							}
						}
						return k;
					}else if (ObjectUtil.isNotNull(o1.getFkByTechTypeId().getCodeLevelNo())) {
						return -1;
					} else if (ObjectUtil.isNotNull(o2.getFkByTechTypeId().getCodeLevelNo())) {
						return 1;
					} else {
						return o1.getFkByTechTypeId().getNum().compareTo(o2.getFkByTechTypeId().getNum());
					}

				}
			});

			for (TdZwSupportTech tdZwSupportTech : zwOrginfoEntity.getSupportTechList()) {
				for (TsSimpleCode tsSimpleCode : this.techTypeList) {
					if(tdZwSupportTech.getFkByTechTypeId().getRid().equals(tsSimpleCode.getRid())){
						tsSimpleCode.setIfSelected(true);
					}
				}
			}
			for (TechTypeVo techTypeVo : this.techTypesList) {
				techTypeVo.setSelNums(0);
				techTypeVo.setSmallTypeNames(null);
				StringBuilder smallTypeNames=new StringBuilder();
				if(!CollectionUtils.isEmpty(techTypeVo.getTechTypes())){
					for (TsSimpleCode techType : techTypeVo.getTechTypes()) {
						if(techType.isIfSelected()){
							techTypeVo.setSelNums(techTypeVo.getSelNums()+1);
							smallTypeNames.append("，").append(techType.getCodeName());
						}
					}
				}
				if(StringUtils.isNotBlank(smallTypeNames)){
					techTypeVo.setSmallTypeNames(smallTypeNames.substring(1));
				}
			}
		}
	}

	/**
	 * <p>Description：场所建设初始化 </p>
	 * <p>Author： yzz 2023-10-27 </p>
	 */
	private void initSupportPlace(){
		TdZwSupportPlace supportPlace = this.zwIntellReportServiceImpl.findSupportPlace(zwOrginfoEntity.getRid());
		if(supportPlace!=null && supportPlace.getRid()!=null){
			zwOrginfoEntity.setSupportPlaces(supportPlace);
			StringBuilder str=new StringBuilder();
			if(!CollectionUtils.isEmpty(supportPlace.getSupportOfficeList())){
				//按照码表num code 排序;num可能为空的情况
				Collections.sort(supportPlace.getSupportOfficeList(), new Comparator<TdZwSupportOffice>() {
					@Override
					public int compare(TdZwSupportOffice o1, TdZwSupportOffice o2) {
						if (ObjectUtil.isNotNull(o1.getFkByOfficeId().getNum()) && ObjectUtil.isNotNull(o2.getFkByOfficeId().getNum())) {
							int i = o1.getFkByOfficeId().getNum().compareTo(o2.getFkByOfficeId().getNum());
							if(i == 0){
								return o1.getFkByOfficeId().getCodeNo().compareTo(o2.getFkByOfficeId().getCodeNo());
							}
							return i;
						} else if (ObjectUtil.isNotNull(o1.getFkByOfficeId().getNum())) {
							return -1;
						} else if (ObjectUtil.isNotNull(o2.getFkByOfficeId().getNum())) {
							return 1;
						} else {
							return o1.getFkByOfficeId().getCodeNo().compareTo(o2.getFkByOfficeId().getCodeNo());
						}
					}
				});
				for (TdZwSupportOffice supportOffice : supportPlace.getSupportOfficeList()) {
					str.append("，").append(supportOffice.getFkByOfficeId().getCodeName());
				}
				supportPlace.setSelOfficeName(str.substring(1));
			}
		}
	}

	/**
	 *  <p>方法描述：科技攻关能力初始化</p>
	 * @MethodAuthor hsj 2023-10-26 13:56
	 */
	private void initSupportTrc() {
		this.supportTrc = this.zwIntellReportServiceImpl.findAllSupportTrc(this.zwOrginfoEntity.getRid());
	}

	/**
	 * <p>方法描述： 学术培训信息初始化 </p>
	 * @MethodAuthor： pw 2023/4/4
	 **/
	private void initSupportTrain(){
		this.academicDataSize = 0;
		this.showAcademicList.clear();
		this.showSupportTrainList.clear();
		//初始化支撑机构学术培训类别
		List<TsSimpleCode> academicTrainTypeList = this.commService.findNumSimpleCodesByTypeId("5134");
		if(CollectionUtils.isEmpty(academicTrainTypeList)){
			return;
		}
		//组合列表数据用 学术培训类别
		List<TsSimpleCode> logicAcademicList = new ArrayList<>();
		Map<String, List<TsSimpleCode>> tmpMap = new LinkedHashMap<>();
		for(TsSimpleCode simpleCode : academicTrainTypeList){
			String fatherNo  = simpleCode.getCodeLevelNo().split("\\.")[0];
			List<TsSimpleCode> tmpList = tmpMap.get(fatherNo);
			if(null == tmpList){
				tmpList = new ArrayList<>();
			}
			tmpList.add(simpleCode);
			tmpMap.put(fatherNo, tmpList);
		}
		for(Map.Entry<String, List<TsSimpleCode>> mapEntity : tmpMap.entrySet()){
			List<TsSimpleCode> tmpList = new ArrayList<>();
			tmpList.addAll(mapEntity.getValue());
			//排除无父级的
			if(tmpList.get(0).getCodeLevelNo().contains(".")){
				continue;
			}
			if(tmpList.size() > 1){
				//拥有父级子级的 去除父级
				tmpList.remove(0);
			}
			this.showAcademicList.add(mapEntity.getValue());
			logicAcademicList.addAll(tmpList);
		}
		//初始查询 一次查询
		List<Object[]> supportTrainList = this.zwIntellReportServiceImpl.findTdZwSupportTrain(this.zwOrginfoEntity.getRid());
		//学术类别按rid 不按codeNo匹配
		//key 年份 subKey TdZwSupportTrain.fkByTrainId.rid
		Map<Integer,Map<Integer, Object[]>> yearMap = new LinkedHashMap<>();
		if(!CollectionUtils.isEmpty(supportTrainList)){
			for(Object[] trainArr : supportTrainList){
				Integer year = null == trainArr[0] ? null : Integer.parseInt(trainArr[0].toString());
				if(null == year){
					continue;
				}
				Map<Integer, Object[]> typeMap = yearMap.get(year);
				if(null == typeMap){
					typeMap = new HashMap<>();
				}
				Integer type = null == trainArr[1] ? null : Integer.parseInt(trainArr[1].toString());
				if(null == type){
					continue;
				}
				typeMap.put(type, trainArr);
				yearMap.put(year, typeMap);
			}
		}
		if(!CollectionUtils.isEmpty(yearMap)){
			for(Integer year : yearMap.keySet()){
				Map<Integer, Object[]> trainMap = yearMap.get(year);
				List<TdZwSupportTrain> trainList = new ArrayList<>();
				for(TsSimpleCode simpleCode : logicAcademicList){
					Object[] trainArr = trainMap.get(simpleCode.getRid());
					TdZwSupportTrain train = new TdZwSupportTrain();
					train.setFkByTrainId(simpleCode);
					train.setYear(year);
					if(null != trainArr){
						train.setPartakeNumber(null == trainArr[2] ? null : Integer.parseInt(trainArr[2].toString()));
						train.setEvidenceFilePath(null == trainArr[3] ? null : trainArr[3].toString());
					}
					trainList.add(train);
				}
				this.showSupportTrainList.add(trainList);
			}
		}
		this.academicDataSize = logicAcademicList.size();
	}

	/**
	 * <p>方法描述： 科研课题信息初始化 </p>
	 * @MethodAuthor： pw 2023/4/4
	 **/
	private void initZwResearchTopicsInfo(){
		this.thesisList = new ArrayList<>();
		this.monographList = new ArrayList<>();
		this.supportTopics1 = new ArrayList<>();
		this.supportTopics2 = new ArrayList<>();
		this.supportTopics3 = new ArrayList<>();
		this.patentList = new ArrayList<>();
		this.standardList = new ArrayList<>();
		this.advanceList = new ArrayList<>();
		Integer mainRid = this.zwOrginfoEntity.getRid();
		this.thesisList = this.zwIntellReportServiceImpl.findAllSupportThesis(mainRid);
		for (TdZwSupportThesis thesis : this.thesisList) {
			if (!CollectionUtils.isEmpty(thesis.getThesisPsnList())) {
				List<String> authorList = new ArrayList<>();
				for (TdZwSupportThesisPsns psn : thesis.getThesisPsnList()) {
					authorList.add(psn.getFkByEmpId().getEmpName());
				}
				thesis.setAuthor(StringUtils.list2string(authorList, "、"));
			}
		}
		this.monographList = this.zwIntellReportServiceImpl.findAllSupportMomograph(mainRid);
		for (TdZwSupportMomograph momograph : this.monographList) {
			if (!CollectionUtils.isEmpty(momograph.getMomographPsnList())) {
				List<String> psnNameList1 = new ArrayList<>();
				List<String> psnNameList2 = new ArrayList<>();
				List<String> psnNameList3 = new ArrayList<>();
				for (TdZwSupportMomographPsns psn : momograph.getMomographPsnList()) {
					switch (psn.getEmpType()) {
						case 1:
							psnNameList1.add(psn.getFkByEmpId().getEmpName());
							break;
						case 2:
							psnNameList2.add(psn.getFkByEmpId().getEmpName());
							break;
						case 3:
							psnNameList3.add(psn.getFkByEmpId().getEmpName());
							break;
						default:
							break;
					}
				}
				momograph.setPsnName1(StringUtils.list2string(psnNameList1, "、"));
				momograph.setPsnName2(StringUtils.list2string(psnNameList2, "、"));
				momograph.setPsnName3(StringUtils.list2string(psnNameList3, "、"));
			}
		}
		List<TdZwSupportTopic> supportTopics =this.zwIntellReportServiceImpl.findAllSupportTopics(mainRid);
		if(!CollectionUtils.isEmpty(supportTopics)){
			//责任人
			for (TdZwSupportTopic supportTopic : supportTopics) {
				if (!CollectionUtils.isEmpty(supportTopic.getSupportTopicPsns())) {
					List<String> responsibleList = new ArrayList<>();
					for (TdZwSupportTopicPsns psn : supportTopic.getSupportTopicPsns()) {
						responsibleList.add(psn.getFkByEmpId().getEmpName());
					}
					supportTopic.setResponsible(StringUtils.list2string(responsibleList, "、"));
				}
			}
			//根据topicType分组
			Map<Integer,List<TdZwSupportTopic>> supportTopicMap = new LinkedHashMap<>();
			GroupUtil.listGroup2Map(supportTopics,supportTopicMap, TdZwSupportTopic.class,"getTopicType");
			this.supportTopics1 = supportTopicMap.containsKey(1)  ? supportTopicMap.get(1) : new ArrayList<TdZwSupportTopic>();
			this.supportTopics2 = supportTopicMap.containsKey(2) ? supportTopicMap.get(2) : new ArrayList<TdZwSupportTopic>();
			this.supportTopics3 = supportTopicMap.containsKey(3) ? supportTopicMap.get(3) : new ArrayList<TdZwSupportTopic>();
		}
		//专利列表
		this.patentList = this.zwIntellReportServiceImpl.findpPatentListByMainId(mainRid,1);
		//标准列表
		this.standardList = this.zwIntellReportServiceImpl.findpPatentListByMainId(mainRid,2);
		this.advanceList = this.zwIntellReportServiceImpl.findAllSupportAdvance(mainRid);
	}

	/**
	 *  <p>方法描述：质量控制结果初始化</p>
	 * @MethodAuthor hsj 2022-07-12 10:20
	 */
	private void initCheckRst() {
		zwCheckRstList = new ArrayList<>();
		//职业健康检查资质申报/职业病诊断资质申报
		if("1".equals(zkCheckRst) && ( zwOrgType == 0 || zwOrgType == 2 )){
			StringBuilder excuteSqlStr = new StringBuilder();
			excuteSqlStr.append(" SELECT T.EXPERT_LEADERS,T.EXPERT_MEMBERS,T.PROBLEMS,tsc.CODE_NAME AS CHECK_RST_NAME , T1.CODE_NAME AS ZG_RST_NAME ")
					.append(" ,T.CHECK_DATE,T2.UNITNAME ,T.NOTICE_FILE_PATH ,T.ZG_FILE_PATH FROM  TD_ZW_CHECK_RST T ")
					.append(" LEFT JOIN TS_SIMPLE_CODE tsc  ON T.CHECK_RST_ID = tsc.RID  ")
					.append(" LEFT JOIN TS_SIMPLE_CODE T1  ON T.ZG_RST_ID = T1.RID ")
					.append(" LEFT JOIN TS_UNIT T2 ON T.CHECK_UNIT_ID = T2.RID ")
					.append(" LEFT JOIN TS_SIMPLE_CODE T3 ON T.CHECK_TYPE_ID = T3.RID ")
					.append(" WHERE T.STATE =1 AND T.DEL_MARK = 0 AND T.UNIT_ID = ").append(zwOrginfoEntity.getTsUnit().getRid());
			if(zwOrgType == 0){
				excuteSqlStr.append("AND  T3.EXTENDS1 = 1") ;
			}else {
				excuteSqlStr.append("AND  T3.EXTENDS1 = 2") ;
			}
			excuteSqlStr.append(" ORDER BY T.CHECK_DATE DESC,T.RID DESC");
			this.zwCheckRstList = commService.getSqlList(excuteSqlStr.toString());
		}
	}
	/**
	 *  <p>方法描述：服务明细</p>
	 * @MethodAuthor hsj 2022/3/2 15:01
	 */
	public void initSerItem(List<ZwJkItemPO> zwJkItemPOS){
		Map<String,String[]> selectItems=new HashMap<String,String[]>();
		Map<String,String> selectItemName=new HashMap<String,String>();
		Map<String,String[]> selectItemsExter=new HashMap<String,String[]>();
		List<String> zwSvritmNoArray=new ArrayList<>();
		if(zwOrginfoEntity.getZwSvritm() != null && zwOrginfoEntity.getZwSvritm().size() > 0){
			List<IZwSvritmZzsb> list = zwOrginfoEntity.getZwSvritm();

			for(int i = 0 ; i < list.size();i++){
				IZwSvritmZzsb t = list.get(i);
				zwSvritmNoArray.add(t.getItemCode().toString());
				List<IZwSvritmSubZzsb> zwSvritmSubList = t.getZwSvritmSubList();
				if(!CollectionUtils.isEmpty(zwSvritmSubList)) {
					String[] itemSubs = new String[zwSvritmSubList.size()];
					List<String> itemSubsExter = new ArrayList<>();
					StringBuffer itemNames=new StringBuffer();
					for(int j = 0 ; j<zwSvritmSubList.size(); j++){
						IZwSvritmSubZzsb sub = zwSvritmSubList.get(j);
						if (null == sub.getFkBySubItemId()) {
							continue;
						}
						itemSubs[j] = sub.getFkBySubItemId().getRid().toString();
						itemNames.append(sub.getFkBySubItemId().getCodeName()).append("，");
						if(sub.getIfExterInspect()!=null && sub.getIfExterInspect()==1){
							itemSubsExter.add(itemSubs[j]);
						}
					}
					selectItems.put(String.valueOf(t.getItemCode()),itemSubs);
					selectItemName.put(String.valueOf(t.getItemCode()),itemNames.toString().substring(0,itemNames.toString().length()-1));
					selectItemsExter.put(String.valueOf(t.getItemCode()),itemSubsExter.toArray(new String[0]));
				}
			}

		}
		List<ZwJkItemPO> newZwJkItemPO=new ArrayList<>();
		for(ZwJkItemPO itemPO:zwJkItemPOS){
			String key=itemPO.getJcItem().getCodeNo();
			if(null!=selectItems&&null!=selectItems.get(key)&&selectItems.get(key).length>0){
				itemPO.setSelectedSerItmDetail(selectItems.get(key));
				itemPO.setSelectedItemDetail(selectItemName.get(key));
			}
			itemPO.setSelectedIfExterInspect(selectItemsExter.get(key));
			if(zwSvritmNoArray.contains(itemPO.getJcItem().getCodeNo())){
				newZwJkItemPO.add(itemPO);
			}
		}
		for(ZwJkItemPO itemPO: newZwJkItemPO){
			if(itemPO.getSelectedSerItmDetail()!=null&&itemPO.getSelectedSerItmDetail().length==0){
				itemPO.setSelectedItemDetail(null);
			}
		}
		zwJkItemPOS.clear();
		zwJkItemPOS.addAll(newZwJkItemPO);
	}


	/**
	 *  <p>方法描述：外检车辆</p>
	 * @MethodAuthor hsj 2022/3/2 14:52
	 */
	private void initOutcar() {
		tjorgOutcarZzsbList = new ArrayList<>();
		tjorgOutcarZzsbList = zwIntellReportServiceImpl.findTjorgOutcarZzsbListByMainRid(this.zwOrginfoEntity.getRid());
		sortTjorgOutcarZzsbList();
	}
	/**
	 * @Description: 按车牌号排序
	 *
	 * @MethodAuthor pw,2021年06月29日
	 */
	private void sortTjorgOutcarZzsbList(){
		if(!CollectionUtils.isEmpty(tjorgOutcarZzsbList)){
			Collections.sort(tjorgOutcarZzsbList, new Comparator<TdZwTjorgOutcarZzsb>() {
				@Override
				public int compare(TdZwTjorgOutcarZzsb o1, TdZwTjorgOutcarZzsb o2) {
					String carNo1 = o1.getCarNo();
					String carNo2 = o2.getCarNo();
					if(null == carNo1 && null == carNo2){
						return 0;
					}else if(null == carNo1){
						return -1;
					}else if(null == carNo2){
						return 1;
					}
					return carNo1.compareTo(carNo2);
				}
			});
		}
	}


	/**
 	 * <p>方法描述：初始化人员属性</p>
 	 * @MethodAuthor qrr,2020年10月16日,initPsnType
	 * */
    private void initPsnType() {
        List<IZwOrgPsnsZzsb> zwOrgPsns = zwOrginfoEntity.getZwOrgPsns();
        if (!CollectionUtils.isEmpty(zwOrgPsns)) {
			List<Integer> psnIds = new ArrayList<>();
            for (IZwOrgPsnsZzsb t : zwOrgPsns){
                TdZwPsninfoZzsb psnInfo = t.getTdZwPsninfo();
                if (null!=psnInfo && null!=psnInfo.getRid()){
					psnIds.add(psnInfo.getRid().intValue());
                }
            }

            Map<Integer,List<TsSimpleCode>> simpleCodeMap = new HashMap<>();// key TdZwPsninfo rid
            Map<Integer,List<TdZwZzlbRangeZzsb>> orgPsnRidsWithZzlbRangeMap = new HashMap<>();//key TD_ZW_TJORGPSNS rid
            Map<Integer,List<TdZwPsnZzlbZzsb>> orgPsnRidsWithCentNoMap = new HashMap<>();//key TD_ZW_TJORGPSNS rid
			if(!CollectionUtils.isEmpty(psnIds)){
				List<Object[]> resultList = new ArrayList<>();
				List<List<Integer>> psnIdList = StringUtils.splitListProxy(psnIds, 1000);
				StringBuilder excuteSqlStr = new StringBuilder();
				for(List<Integer> psnId : psnIdList){
					excuteSqlStr = new StringBuilder();
					excuteSqlStr.append(" SELECT T2.EXTENDS2,T2.EXTENDS3,T1.RID, T2.CODE_NAME,T2.RID AS CODERID,T.OTHER_PSN_TYPE FROM TD_ZW_PSN_TYPE T ")
							.append(" INNER JOIN TD_ZW_PSNINFO T1 ON T.MAIN_ID = T1.RID ")
							.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.PSN_TYPE ")
							.append(" WHERE T1.RID IN (:psnId)")
							.append(" AND T.ZZJG_TYPE = '").append(this.selectObj[1].toString()).append("' ");
					Map<String, Object> paramMap = new HashMap<>();
					paramMap.put("psnId", psnId);
					List<Object[]> results = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
					if(CollectionUtils.isEmpty(results)){
						continue;
					}
					resultList.addAll(results);
				}
                if(!CollectionUtils.isEmpty(resultList)){
                    for(Object[] objArr : resultList){
                        Integer rid = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
                        String otherPsnType = null == objArr[5] ? null : objArr[5].toString();
                        Integer codeRid = null == objArr[4] ? null : Integer.parseInt(objArr[4].toString());
                        if(null == rid || null == codeRid){
                            continue;
                        }
                        Integer extend2 = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                        String extend3 = null == objArr[1] ? null : objArr[1].toString();
                        String codeName = null == objArr[3] ? null : objArr[3].toString();
                        List<TsSimpleCode> simpleList = simpleCodeMap.get(rid.intValue());
                        if(null == simpleList){
                            simpleList = new ArrayList<>();
                        }
                        TsSimpleCode tsSimpleCode = new TsSimpleCode(codeRid);
                        tsSimpleCode.setCodeName(codeName);
                        tsSimpleCode.setExtendS2(extend2);
                        tsSimpleCode.setExtendS3(extend3);
                        tsSimpleCode.setExtendS4(otherPsnType);//将OtherPsnType 放到extends4字段中 由后边使用
                        simpleList.add(tsSimpleCode);
                        simpleCodeMap.put(rid.intValue(), simpleList);
                    }
                }

                // 从事项目
				List<Object[]> rangeObjArrList = new ArrayList<>();
				for(List<Integer> psnId : psnIdList){
					excuteSqlStr = new StringBuilder();
					excuteSqlStr.append(" SELECT DISTINCT T.ZZ_RANGE_ID, T2.RID AS EMP_ID, T4.CODE_NO, T4.CODE_NAME ");
					excuteSqlStr.append(" FROM TD_ZW_ZZLB_RANGE T ");
					excuteSqlStr.append(" INNER JOIN TD_ZW_PSN_ZZLB T1 ON T.MAIN_ID = T1.RID ");
					excuteSqlStr.append(" INNER JOIN TD_ZW_PSNINFO T2 ON T1.PSN_ID = T2.RID ");
					excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T4 ON T.ZZ_RANGE_ID = T4.RID ");
					excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T5 ON T1.QUAL_ID = T5.RID ");
					excuteSqlStr.append(" WHERE T2.RID IN (:psnId)");
					excuteSqlStr.append(" AND (T5.CODE_DESC IS NULL OR T5.CODE_DESC LIKE '%").append(this.selectObj[1].toString()).append("%') ");
					excuteSqlStr.append(" GROUP BY T.ZZ_RANGE_ID, T2.RID, T4.CODE_NO, T4.CODE_NAME ");
					excuteSqlStr.append(" ORDER BY T2.RID, T4.CODE_NO ");
					Map<String, Object> paramMap = new HashMap<>();
					paramMap.put("psnId", psnId);
					List<Object[]> rangeObjs = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
					if(CollectionUtils.isEmpty(rangeObjs)){
						continue;
					}
					rangeObjArrList.addAll(rangeObjs);
				}
                if(!CollectionUtils.isEmpty(rangeObjArrList)) {
                    for(Object[] objArr : rangeObjArrList) {
                        Integer rangeId = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                        Integer empId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                        String codeNo = null == objArr[2] ? null : objArr[2].toString();
                        String codeName = null == objArr[3] ? null : objArr[3].toString();
                        TdZwZzlbRangeZzsb zzlbRange = new TdZwZzlbRangeZzsb();
                        TsSimpleCode rangeSimpleCode = new TsSimpleCode(rangeId);
                        rangeSimpleCode.setCodeName(codeName);
                        rangeSimpleCode.setCodeNo(codeNo);
                        zzlbRange.setFkByZzRangeId(rangeSimpleCode);
                        List<TdZwZzlbRangeZzsb> zwZzlbRangeList = orgPsnRidsWithZzlbRangeMap.get(empId.intValue());
                        if(null == zwZzlbRangeList) {
                            zwZzlbRangeList = new ArrayList<>();
                        }
                        zwZzlbRangeList.add(zzlbRange);
                        orgPsnRidsWithZzlbRangeMap.put(empId.intValue(), zwZzlbRangeList);
                    }
                }

                // 证书编号
				List<Object[]> psnZzlbObjArrList = new ArrayList<>();
				for(List<Integer> psnId : psnIdList){
					excuteSqlStr = new StringBuilder();
					excuteSqlStr.append(" SELECT T.RID, T.PSN_ID, T.QUAL_ID, T.CENT_NO, T.SEND_DATE, T.VALID_DATE, T.AGEIN_SEND_DATE, T.AGEIN_VALID_DATE ");
					excuteSqlStr.append(" FROM TD_ZW_PSN_ZZLB T ");
					excuteSqlStr.append(" INNER JOIN TD_ZW_PSNINFO T1 ON T.PSN_ID = T1.RID ");
					excuteSqlStr.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.QUAL_ID = T2.RID ");
					excuteSqlStr.append(" WHERE T1.RID IN (:psnId)");
					excuteSqlStr.append(" AND (T2.CODE_DESC IS NULL OR T2.CODE_DESC LIKE '%").append(this.selectObj[1].toString()).append("%') ");
					excuteSqlStr.append(" ORDER BY T.SEND_DATE DESC ");
					Map<String, Object> paramMap = new HashMap<>();
					paramMap.put("psnId", psnId);
					List<Object[]> psnZzlbObjs = commService.findDataBySqlNoPage(excuteSqlStr.toString(),paramMap);
					if(CollectionUtils.isEmpty(psnZzlbObjs)){
						continue;
					}
					psnZzlbObjArrList.addAll(psnZzlbObjs);
				}
                if(!CollectionUtils.isEmpty(psnZzlbObjArrList)) {
                    for(Object[] objArr : psnZzlbObjArrList) {
                        Integer rid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
                        Integer psnId = null == objArr[1] ? null : Integer.parseInt(objArr[1].toString());
                        Integer qualId = null == objArr[2] ? null : Integer.parseInt(objArr[2].toString());
                        String codeNo = null == objArr[3] ? null : objArr[3].toString();
                        TdZwPsnZzlbZzsb tdZwPsnZzlb = new TdZwPsnZzlbZzsb(rid);
                        tdZwPsnZzlb.setFkByPsnId(new TdZwPsninfoZzsb(psnId));
                        TsSimpleCode tsSimpleCode = new TsSimpleCode(qualId);
                        tsSimpleCode.setCodeDesc(this.selectObj[1].toString());
                        tdZwPsnZzlb.setFkByQualId(tsSimpleCode);
                        tdZwPsnZzlb.setCentNo(codeNo);
                        List<TdZwPsnZzlbZzsb> zwPsnZzlbList = orgPsnRidsWithCentNoMap.get(psnId.intValue());
                        if(null == zwPsnZzlbList) {
                            zwPsnZzlbList = new ArrayList<>();
                        }
                        zwPsnZzlbList.add(tdZwPsnZzlb);
                        orgPsnRidsWithCentNoMap.put(psnId.intValue(), zwPsnZzlbList);
                    }
                }
            }
            for (IZwOrgPsnsZzsb t : zwOrgPsns) {
                TdZwPsninfoZzsb psnInfo = t.getTdZwPsninfo();
                //给人员属性赋值
                if (null!=psnInfo && null!=psnInfo.getRid()) {
                    List<TsSimpleCode> simpleList = simpleCodeMap.get(psnInfo.getRid().intValue());
                    if(!CollectionUtils.isEmpty(simpleList)){
                        StringBuilder sb = new StringBuilder();
                        for(TsSimpleCode psnType : simpleList){
                            sb.append("，").append(psnType.getCodeName());
                            if (null!=psnType.getExtendS2() && 1==psnType.getExtendS2()) {
                                if ("1".equals(psnInfo.getIfCheckExpert())) {
                                    sb.append("（鉴定专家）");
                                }
                            }
                            if ("4".equals(psnType.getExtendS3())) {
                                //注意 查询的时候将OtherPsnType 放到extends4字段中 避免实例化多个类型的对象
                                if (StringUtils.isNotBlank(psnType.getExtendS4())) {
                                    sb.append("（").append(psnType.getExtendS4()).append("）");
                                }
                            }
                        }
                        psnInfo.setPsnTypes(sb.substring(1));
                    }

                    // 从事项目
                    List<TdZwZzlbRangeZzsb> zwZzlbRanges = orgPsnRidsWithZzlbRangeMap.get(psnInfo.getRid().intValue());
                    if(!CollectionUtils.isEmpty(zwZzlbRanges)) {
                        StringBuilder name = new StringBuilder();
                        for(TdZwZzlbRangeZzsb items : zwZzlbRanges) {
                            name.append("，").append(items.getFkByZzRangeId().getCodeName());
                        }
                        psnInfo.setZzlbRange(name.deleteCharAt(0).toString());
                    }

                    // 证书编号
                    List<TdZwPsnZzlbZzsb> zwPsnZzlbList = orgPsnRidsWithCentNoMap.get(psnInfo.getRid().intValue());
                    if(!CollectionUtils.isEmpty(zwPsnZzlbList)) {
                        StringBuilder name = new StringBuilder();
                        for(TdZwPsnZzlbZzsb items : zwPsnZzlbList) {
                            name.append(",").append(items.getCentNo());
                        }
                        psnInfo.setCertNoStr(name.deleteCharAt(0).toString());
                    }else{
						psnInfo.setCertNoStr(null);
					}
                }
            }
        }
    }

	private void initPersonSummary() {
		this.personSummaryList = new ArrayList<>();
		if (null!=zwOrginfoEntity.getRid() && "0".equals(selectObj[1].toString())) {
			List<Object[]> list = zwIntellReportServiceImpl.findPsnTypeSummary(new Integer(selectObj[1].toString()), zwOrginfoEntity.getRid());
			if (!CollectionUtils.isEmpty(list)) {
				List<String> row = new ArrayList<>();
				int i = 0;
				for (Object[] obj : list) {
					if (i%7==0) {
						row = new ArrayList<>();
						this.personSummaryList.add(row);
					}
					row.add(obj[1]+"："+obj[2]+"人");
					i++;
				}
			}
		}
	}
	/**
 	 * <p>方法描述：初始化备案记录</p>
 	 * @MethodAuthor qrr,2020年10月15日,initTjorgRecords
     * */
    private void initTjorgRecords() {
    	this.tjorgRecords = zwIntellReportServiceImpl.findTjorgRecords(zwOrginfoEntity.getRid());
    	if (!CollectionUtils.isEmpty(this.tjorgRecords)) {
			for (TdZwTjorgRecordZzsb t : this.tjorgRecords) {
				List<TdZwTjorgRcdItemZzsb> rcdItems = t.getRcdItems();
				if (!CollectionUtils.isEmpty(rcdItems)) {
					List<String> serviceObj = new ArrayList<>();
					StringBuffer sb = new StringBuffer();
					for (TdZwTjorgRcdItemZzsb itm : rcdItems) {
						sb.append("，").append(itm.getFkByItemId().getCodeName());
						serviceObj.add(itm.getFkByItemId().getRid().toString());
					}
					t.setServiceItems(sb.substring(1));
					t.setServiceObj(serviceObj);
				}
			}
		}
    }
	
	public void codeViewAction(){
		tdZwPsninfo=zwIntellReportServiceImpl.findTdZwPsnInfo(tdZwPsninfo.getRid());
		// 隐藏身份证
		String idcCard = tdZwPsninfo.getIdcCard();
		String encryptIdc = StringUtils.encryptIdc(idcCard);
		tdZwPsninfo.setIdcCard(encryptIdc);
		// 加密手机号码
		String mobileNo = tdZwPsninfo.getMobileNo();
		if (StringUtils.isNotBlank(mobileNo)) {
			if(mobileNo.length() > 4){
				String num = "***************".substring(0, mobileNo.length() - 4);
				tdZwPsninfo.setMobileNo(num.concat(mobileNo.substring(mobileNo
						.length() - 4)));
			}
		}

		// 加密手机号码
		String telPhone = tdZwPsninfo.getTelPhone();
		if (StringUtils.isNotBlank(telPhone)) {
			if(telPhone.length() > 4){
				String num = "***************".substring(0, telPhone.length() - 4);
				tdZwPsninfo.setTelPhone(num.concat(telPhone.substring(telPhone
						.length() - 4)));
			}
		}
		
		// 加密收件人手机号码
		String receiveMobileNo = tdZwPsninfo.getReceiveMobileNo();
		if (StringUtils.isNotBlank(receiveMobileNo)) {
			if(receiveMobileNo.length() > 4){
				String num = "***************".substring(0, receiveMobileNo.length() - 4);
				tdZwPsninfo.setReceiveMobileNo(num.concat(receiveMobileNo.substring(receiveMobileNo
						.length() - 4)));
			}
		}
		/**出生年月**/
		if (StringUtils.isNotBlank(tdZwPsninfo.getBirthday())) {
			tdZwPsninfo.setBirthday("**********");
		}
        psnInfoBase.setZwOrgType(Integer.valueOf(this.selectObj[1].toString()));
		psnInfoBase.setPsnInfo(tdZwPsninfo);
		psnInfoBase.initPsnInfo();
		psnInfoBase.initPsnZzlb();
		//初始化培训履历
		this.psnInfoBase.initTrainHis(idcCard,tdZwPsninfo.getZzlbList());
		RequestContext.getCurrentInstance().execute("disabledInput()");
	}

	private void initZwJcItems() {
		showItemList = new ArrayList<Object[]>();
		String jcItems = zwOrginfoEntity.getJcItems();
		if (StringUtils.isNotBlank(jcItems)) {
			// 初始化选择检查项目
			this.initJcItemList();
			// 封装页面展示集合
			Iterator<TsSimpleCode> iterator = diagMap.keySet().iterator();
			// 序号
			int xh = 1;
			while (iterator.hasNext()) {
				// 存放子集数据
				List<String[]> subList = new ArrayList<String[]>();
				TsSimpleCode next = iterator.next();
				List<TsSimpleCode> list = diagMap.get(next);
				if (null != list && list.size() > 0) {
					for (TsSimpleCode tscode : list) {
						String rid = tscode.getRid().toString();
						if (StringUtils.contains("," + jcItems + ",", "," + rid + ",")) {// 如当前项目被选中，则展示集合中增加该元素
							String[] str=new String[2];
							str[0]=tscode.getCodeName();
							str[1]=rid;
							subList.add(str);
						}
					}
					if (subList.size() > 0) {
						for (int i = 0; i < subList.size(); i++) {
							String[] str = subList.get(i);
							if(null != zwOrginfoEntity.getZwItems() && zwOrginfoEntity.getZwItems().size()>0){
								for(IZwItemsZzsb item:zwOrginfoEntity.getZwItems()){
									if(null != str[1] && str[1].toString().equals(item.getTsSimpleCode().getRid().toString())){
										if(null != item.getIsOutter() && item.getIsOutter().intValue()==1){
											showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),true,str[1].toString() });
											continue;
										}else{
											showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),false,str[1].toString() });
											continue;
										}
									}
								}
							}else{
								showItemList.add(new Object[] { i == 0, subList.size(), xh, next.getCodeName(), str[0].toString(),false,str[1].toString() });
							}
							xh++;
						}
						subList.clear();
					}
				}
			}
		}
	}

	private void initJcItemList() {
		if (null == diagMap) {// 5015
			List<TsSimpleCode> list = this.commService.findLevelSimpleCodesByTypeId("5015");
			// 选中的检查项目ids
			String jcItems = zwOrginfoEntity.getJcItems();
			if (null != list && list.size() > 0) {
				// 页面展示码表Map
				diagMap = new LinkedHashMap<TsSimpleCode, List<TsSimpleCode>>();
				for (TsSimpleCode code : list) {
					if (!StringUtils.contains(code.getCodeLevelNo(), ".")) {
						diagMap.put(code, new ArrayList<TsSimpleCode>());
					}
				}
				Iterator<TsSimpleCode> it = diagMap.keySet().iterator();
				while (it.hasNext()) {
					// 父级编码
					TsSimpleCode parentCode = it.next();
					String parentC = parentCode.getCodeNo();
					// 清空选择和子集合
					parentCode.setIfSelected(false);
					parentCode.setSelCodeRids(null);
					// 选中的Id
					List<Integer> selList = new ArrayList<Integer>();
					for (TsSimpleCode code : list) {
						if (code.getCodeLevelNo().startsWith(parentC + ".")) {
							diagMap.get(parentCode).add(code);
							if (StringUtils.isNotBlank(jcItems)
									&& StringUtils.contains("," + jcItems + ",", "," + code.getRid() + ",")) {
								selList.add(code.getRid());
							}
						}
					}

					if (selList.size() > 0) {
						String[] rids = new String[selList.size()];
						for (int i = 0; i < selList.size(); i++) {
							rids[i] = String.valueOf(selList.get(i));
						}
						parentCode.setSelCodeRids(rids);
						if (rids.length == diagMap.get(parentCode).size()) {
							parentCode.setIfSelected(true);
						}
					}
				}
			}
		}
	}

	private void initAnnex() {
		zwAnnexList1 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList2 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList3 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList4 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList5 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList6 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList7 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList8 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList9 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		zwAnnexList20 = new ArrayList<TdZwJsffjgCommAnnexZzsb>();
		//List<IZwAnnex> list = zwOrginfoEntity.getZwAnnex();
		if(this.zwOrginfoEntity.getTsUnit()==null){
			return;
		}
        List<TdZwJsffjgCommAnnexZzsb> list = this.zwIntellReportServiceImpl.findTdZwJsffjgCommAnnexListByOrgId(this.zwOrginfoEntity.getTsUnit().getRid().toString());
        if (list != null && list.size() > 0) {
            for(TdZwJsffjgCommAnnexZzsb t : list) {
                if(null != t.getRegZoneId()) {
                    TsZone tsZone = this.zwIntellReportServiceImpl.find(TsZone.class, t.getRegZoneId());
                    if(null != tsZone) {
                        t.setFkByRegZoneId(tsZone);
                    }
                }
				if (t.getAnnexType().equals(1)) {
					zwAnnexList1.add(t);
				} else if(t.getAnnexType().equals(2)){
					zwAnnexList2.add(t);
				}else if(t.getAnnexType().equals(3)){
					zwAnnexList3.add(t);
				}else if(t.getAnnexType().equals(4)){
					zwAnnexList4.add(t);
				}else if(t.getAnnexType().equals(5)){
					zwAnnexList5.add(t);
				}else if(t.getAnnexType().equals(6)){
					zwAnnexList6.add(t);
				}else if(t.getAnnexType().equals(7)){
					zwAnnexList7.add(t);
				}else if(t.getAnnexType().equals(8)){
					zwAnnexList8.add(t);
				}else if(t.getAnnexType().equals(11)){
                    zwAnnexList9.add(t);
                }else if(t.getAnnexType().equals(20)){
					zwAnnexList20.add(t);
				}
			}
		}
	}

	private void initServiceItem() {
		//职业健康服务项目
    	jkList=commService.findNumSimpleCodesByTypeId("5018");
    	//放射机构服务项目
    	fsList=commService.findallSimpleCodesByTypeId("5019");
    	//诊断服务项目
    	zyList=commService.findallSimpleCodesByTypeId("5020");
    	//职卫技术服务机构业务范围
    	zwjsList=commService.findallSimpleCodesByTypeIdOrderByNum("5320");
        serviceList = new ArrayList<String>();
        List<IZwSvritmZzsb> list = zwOrginfoEntity.getZwSvritm();
        Integer zwOrgType=Integer.valueOf(selectObj[1].toString());
        String itemStr=null;
        if(list != null && list.size() > 0){
        	if(zwOrgType.equals(3)){
        		Map<String, String> itemCodeMap = new HashMap<String, String>();
        		for (IZwSvritmZzsb t : list) {
        			String itemCode = t.getItemCode().toString();
            		itemCodeMap.put(itemCode, itemCode);
				}
        		Map<String, TsSimpleCode> simpleMap = new HashMap<String,TsSimpleCode>();
        		Map<String, List<String>> childMap = new LinkedHashMap<String, List<String>>();
        		for(TsSimpleCode t : zwjsList){
            		simpleMap.put(t.getCodeNo(), t);
        			if (null!=itemCodeMap.get(t.getCodeNo())) {
						String codeLevelNo = t.getCodeLevelNo();
						String[] val = codeLevelNo.split("\\.");
						if (null==childMap.get(val[0])) {
	            			List<String> childList = new ArrayList<>();
	            			childList.add(t.getCodeNo());
	            			childMap.put(val[0], childList);
						}else {
							List<String> childList = childMap.get(val[0]);
							childList.add(t.getCodeNo());
						}
					}
        		}
            	for (Entry<String, List<String>> entry : childMap.entrySet()) {
            		String key = entry.getKey();
            		TsSimpleCode first = simpleMap.get(key);
            		StringBuffer sb = new StringBuffer();
            		sb.append(first.getCodeName());
            		sb.append("（");
            		List<String> childList = entry.getValue();
            		StringBuffer childSb = new StringBuffer();
            		for (String s : childList) {
            			TsSimpleCode child = simpleMap.get(s);
            			childSb.append("，").append(child.getCodeName());
					}
            		sb.append(childSb.substring(1)).append("）");
            		serviceList.add(sb.toString());
				}
            	
        	}else if(zwOrgType.equals(1)){
				for(int i = 0 ; i < list.size();i++){
					IZwSvritmZzsb t = list.get(i);
					for (TsSimpleCode ts : fsList) {
						if (ts.getCodeNo().equals(t.getItemCode().toString())) {
							itemStr = ts.getCodeName();
							List<IZwSvritmSubZzsb> zwSvritmSubList = t.getZwSvritmSubList();
							if (!CollectionUtils.isEmpty(zwSvritmSubList)) {
								StringBuffer subItemNames = new StringBuffer();
								for (int j = 0; j < zwSvritmSubList.size(); j++) {
									IZwSvritmSubZzsb sub = zwSvritmSubList.get(j);
									if (null == sub.getFkBySubItemId()) {
										continue;
									}
									subItemNames.append("，").append(sub.getFkBySubItemId().getCodeName());
								}
								if (subItemNames.length() > 0) {
									itemStr = itemStr + "（" + subItemNames.substring(1) + "）";
								}
							}
							serviceList.add(itemStr);
						}
					}
				}
			}else {
        		for(int i = 0 ; i < list.size();i++){
            		IZwSvritmZzsb t = list.get(i);
            		if(zwOrgType.equals(0)){
                    	for(TsSimpleCode ts:jkList){
                    		if(ts.getCodeNo().equals(t.getItemCode().toString())){
                    			itemStr=ts.getCodeName();
                    		}
                    	}
                    }
                    if(zwOrgType.equals(2)){
                    	for(TsSimpleCode ts:zyList){
                    		if(ts.getCodeNo().equals(t.getItemCode().toString())){
                    			itemStr=ts.getCodeName();
                    		}
                    	}
                    }
                    serviceList.add(itemStr);
            	}
			}
        }
		jkItemList = new ArrayList<>();
		if(selectObj[1].toString().equals("0") && !CollectionUtils.isEmpty(jkList)){
			for(TsSimpleCode simpleCode:jkList){
				List<TsSimpleCode>  jkItems= commService.findallSimpleCodesByTypeIdAndExtends2("5527",simpleCode.getCodeNo());
				ZwJkItemPO zwJkItemPO=new ZwJkItemPO();
				zwJkItemPO.setJcItem(simpleCode);
				zwJkItemPO.setJcItemList(jkItems);
				jkItemList.add(zwJkItemPO);
			}
		}
	}


	public void downLoadDiskFile() {
		String path = selectZwAnnex.getFilePath();
		path = JsfUtil.getAbsolutePath() + path.substring(1);
		FileInputStream fis = null;
		try {
			File file = new File(path);
			fis = new FileInputStream(file);
			String fileString = DownLoadUtil.uploadFile2Database(fis);
			String fileName = selectZwAnnex.getFileName();
			DownLoadUtil.downFile(fileName, fileString);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException e2) {
					e2.printStackTrace();
				}
			}
		}
	}

	@Override
	public void modInit() {

	}

	@Override
	public void saveAction() {

	}
	
	   
    /**********放射人员体检开始************/
    public void addInitTjRst() {
    	List<TdZwPsntjMainZzsb> mainList = selectZwOrgPsn.getTdZwPsninfo().getTjMainList();
    	if(null != mainList && mainList.size() > 0) {
    		for(TdZwPsntjMainZzsb main : mainList) {
    			if(main.getTjType().intValue() == 1) {
    				//入职前
    				this.firstTjMain = main;
    				this.firstTjSubList = main.getSubList();
    			}else {
    				//末次
    				this.lastTjMain = main;
    				this.lastTjSubList = main.getSubList();
    			}
    		}
    	}else {
    	    this.firstTjMain = new TdZwPsntjMainZzsb();
    	    this.firstTjMain.setFkByEmpId(selectZwOrgPsn.getTdZwPsninfo());
    	    this.firstTjMain.setTjType(1);
    	    this.firstTjSubList = Lists.newArrayList();
    	    this.lastTjMain = new TdZwPsntjMainZzsb();
    	    this.lastTjMain.setFkByEmpId(selectZwOrgPsn.getTdZwPsninfo());
    	    this.lastTjMain.setTjType(2);
    	    this.lastTjSubList = Lists.newArrayList();
    	}
    }
    
    /**********场所监测开始************/
    public void addInitInstJc() {
    	this.jcMainList = this.selectOrgInst.getTdZwInstinfo().getInstJcMainList();
    }
    
    public void addInitInstJcSub() {
    	this.jcSubList = jcMain.getSubList();
    }
    
    /**********设备检测开始************/
    public void addInitInstSbJc() {
    	this.sbJcMainList = this.selectOrgInst.getTdZwInstinfo().getInstSbjcmainList();
    }
    
    public void addInitInstSbJcSub() {
    	this.sbJcSubList = sbJcMain.getSubList();
    }

	@Override
	public String[] buildHqls() {
		StringBuilder sb = new StringBuilder();
		if(typeList.contains(0)){
			if(sb.length()>0){
				sb.append(" UNION ALL ");
			}
			sb.append("SELECT T1.ORG_ID ORGID,0 ORGTYPE,T1.ORG_NAME ORGNAME,T1.ORG_FZ FZ,T1.ORG_FZZW FZZW,T1.CERT_NO CERTNO,T1.FIRST_GETDAY FIRDAY,T1.CREATE_DATE MODDAY,T1.RID MAINID,T1.STATE STATE,T1.VALID_DATE FROM TD_ZW_TJORGINFO T1");
		}
		if(typeList.contains(1)){
			if(sb.length()>0){
				sb.append(" UNION ALL ");
			}
			sb.append("SELECT T2.ORG_ID ORGID,1 ORGTYPE,T2.ORG_NAME ORGNAME,T2.ORG_FZ FZ,T2.ORG_FZZW FZZW,T2.CERT_NO CERTNO,T2.FIRST_GETDAY FIRDAY,T2.CREATE_DATE MODDAY,T2.RID MAINID,T2.STATE STATE,T2.VALID_DATE  FROM TD_ZW_SRVORGINFO T2");
		}
		if(typeList.contains(2)){
			if(sb.length()>0){
				sb.append(" UNION ALL ");
			}
			sb.append("SELECT T3.ORG_ID ORGID,2 ORGTYPE,T3.ORG_NAME ORGNAME,T3.ORG_FZ FZ,T3.ORG_FZZW FZZW,T3.CERT_NO CERTNO,T3.FIRST_GETDAY FIRDAY,T3.CREATE_DATE MODDAY,T3.RID MAINID,T3.STATE STATE,T3.VALID_DATE  FROM TD_ZW_DIAGORGINFO T3");
		}
		if(typeList.contains(3)){
			if(sb.length()>0){
				sb.append(" UNION ALL ");
			}
			sb.append("SELECT T4.ORG_ID ORGID,3 ORGTYPE,T4.ORG_NAME ORGNAME,T4.ORG_FZ FZ,T4.ORG_FZZW FZZW,T4.CERT_NO CERTNO,T4.FIRST_GETDAY FIRDAY,T4.CREATE_DATE MODDAY,T4.RID MAINID,T4.STATE STATE,T4.VALID_DATE  FROM TD_ZW_OCCHETH_INFO T4");
		}
		if(typeList.contains(4)){
			if(sb.length()>0){
				sb.append(" UNION ALL ");
			}
			sb.append("SELECT T5.ORG_ID ORGID,4 ORGTYPE,T5.ORG_NAME ORGNAME,T5.ORG_FZ FZ,T5.ORG_FZZW FZZW,'' CERTNO,to_date(null,'yyyy-mm-dd') FIRDAY,T5.CREATE_DATE MODDAY,T5.RID MAINID,T5.STATE STATE,to_date(null,'yyyy-mm-dd') VALID_DATE  FROM TD_ZW_SUPPORT_ORG T5");
		}
		sb.append(" ) TA ");
		sb.append(" INNER JOIN TS_UNIT TB ON TA.ORGID = TB.RID ");
		sb.append(" INNER JOIN TS_ZONE TC ON TB.ZONE_ID = TC.RID ");
		sb.append(" LEFT JOIN TS_ZONE managedZone ON TB.MANAGE_ZONE_ID = managedZone.RID ");
		sb.append(" WHERE 1=1 ");
		if(StringUtils.isNotBlank(searchZoneCode)){
			sb.append(" AND TC.ZONE_GB LIKE :searchZoneCode ");
			this.paramMap.put("searchZoneCode", ZoneUtil.zoneSelect(searchZoneCode)+"%");
		}
		if (searchZwOrhType != null) {
			sb.append(" AND TA.ORGTYPE =:searchZwOrhType ");
			this.paramMap.put("searchZwOrhType", searchZwOrhType);
		}
		if (StringUtils.isNotBlank(searchUnitName)) {
			sb.append(" AND TA.ORGNAME LIKE :searchUnitName");
			this.paramMap.put("searchUnitName", "%"+StringUtils.convertBFH(searchUnitName)+"%");
		}
		if (searchState != null && searchState.length == 1) {
			sb.append(" and ta.state = ").append(searchState[0]);
		}
		if(earlyDays!=null){
			Date date = DateUtils.addDays(new Date(), earlyDays);
			String daysAfterDate = DateUtils.formatDate(date, "yyyy-MM-dd");
			sb.append(" and TA.VALID_DATE < TO_DATE('"+daysAfterDate+"','YYYY-MM-DD')");
		}
		sb.append(" ORDER BY TC.ZONE_GB,managedZone.ZONE_GB,TB.RID,ta.orgtype ");
		StringBuilder h1 = new StringBuilder();
		StringBuilder h2 = new StringBuilder();
		//原来查询的是TD_ZW_TJORGINFO中的单位信息
		h1.append("SELECT TC.ZONE_NAME,TA.ORGTYPE,TA.ORGNAME,TA.FZ,TA.FZZW,TA.CERTNO,TA.FIRDAY,TA.MODDAY,TA.MAINID,TA.STATE,TA.VALID_DATE,TC.ZONE_TYPE,TC.FULL_NAME FROM (");
		//现在查询的是TS_UNIT中的单位信息
		//h1.append("SELECT TC.ZONE_NAME,TA.ORGTYPE,TB.UNITNAME,TB.ORG_FZ,TB.ORG_FZZW,TA.CERTNO,TA.FIRDAY,TA.MODDAY,TA.MAINID,TA.STATE,TA.VALID_DATE FROM (");
		h1.append(sb.toString());
		h2.append("select count(*) from (");
		h2.append(sb.toString());
		return new String[] { h1.toString(), h2.toString() };
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public String getSearchZoneType() {
		return searchZoneType;
	}

	public void setSearchZoneType(String searchZoneType) {
		this.searchZoneType = searchZoneType;
	}

	public Integer getSearchZwOrhType() {
		return searchZwOrhType;
	}

	public void setSearchZwOrhType(Integer searchZwOrhType) {
		this.searchZwOrhType = searchZwOrhType;
	}

	public String getSearchUnitName() {
		return searchUnitName;
	}

	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}

	public Object[] getSelectObj() {
		return selectObj;
	}

	public void setSelectObj(Object[] selectObj) {
		this.selectObj = selectObj;
	}

	public IZwOrginfoZzsb getZwOrginfoEntity() {
		return zwOrginfoEntity;
	}

	public void setZwOrginfoEntity(IZwOrginfoZzsb zwOrginfoEntity) {
		this.zwOrginfoEntity = zwOrginfoEntity;
	}

	public List<String> getServiceList() {
		return serviceList;
	}

	public void setServiceList(List<String> serviceList) {
		this.serviceList = serviceList;
	}

    public TdZwJsffjgCommAnnexZzsb getSelectZwAnnex() {
        return selectZwAnnex;
    }

    public void setSelectZwAnnex(TdZwJsffjgCommAnnexZzsb selectZwAnnex) {
        this.selectZwAnnex = selectZwAnnex;
    }

    public List<Object[]> getShowItemList() {
		return showItemList;
	}

	public void setShowItemList(List<Object[]> showItemList) {
		this.showItemList = showItemList;
	}

	public String[] getSearchState() {
		return searchState;
	}

	public void setSearchState(String[] searchState) {
		this.searchState = searchState;
	}

	/** 初始化质量管理体系 */
	public void initInstitutionList() {
		this.ifQualAnnex = PropertyUtils.getValue("zzsb.ifQualAnnex");
		if(!"1".equals(ifQualAnnex)){
			return;
		}
        topOfInstitutionList = new ArrayList<>();
        topInstitutionMap = new HashMap<>();
        institutionMap = new HashMap<>();
        qualAnnexMap = new HashMap<>();

		if(null != zwOrginfoEntity && zwOrginfoEntity instanceof TdZwTjorginfoZzsb){
            if(null == institutionList || institutionList.size() < 1){
                institutionList= this.commService.findLevelSimpleCodesByTypeId("5507");
            }
            initInstitutionListAndMap(null == zwOrginfoEntity || null == zwOrginfoEntity.getRid() ? null : zwOrginfoEntity.getRid());
        }
	}

	/** 查询数据 组合数据 */
	private void initInstitutionListAndMap(Integer mainId){
		if(null == institutionList || institutionList.size() == 0){
			return;// 码表无值 停止
		}
		topOfInstitutionList = new ArrayList<>();
		topInstitutionMap = new HashMap<>();
		institutionMap = new HashMap<>();
		qualAnnexMap = new HashMap<>();
		//初始化顶级
		for(TsSimpleCode tsCode : institutionList){
			if(null != tsCode.getCodeLevelNo() && tsCode.getCodeLevelNo().indexOf(".") < 0){
				//码表扩展字段2无值 或者不是1 或者具备外出开展职业健康检查工作能力
				boolean flag = null == tsCode.getExtendS2() || 1 != tsCode.getExtendS2() ||
						(null != this.zwOrginfoEntity.getOutWorkPower() && 1 == this.zwOrginfoEntity.getOutWorkPower());
				if(flag){
					topOfInstitutionList.add(tsCode);
					topInstitutionMap.put(tsCode.getRid(),tsCode);
				}
			}
		}
		for(TsSimpleCode tsChCode : institutionList){
			for(TsSimpleCode top : topOfInstitutionList){
				if(null != tsChCode.getCodeLevelNo() && tsChCode.getCodeLevelNo().indexOf(".") > -1 &&
						tsChCode.getCodeLevelNo().startsWith(top.getCodeLevelNo())){
					List<TsSimpleCode> tsList = institutionMap.get(top.getRid());
					if(null == tsList){
						tsList = new ArrayList<>();
					}
					tsList.add(tsChCode);
					institutionMap.put(top.getRid(),tsList);
				}
			}
		}
		if(null != institutionMap && !institutionMap.isEmpty()){
			for(Entry<Integer, List<TsSimpleCode>> mapTmp : institutionMap.entrySet()){
				Integer mapKey = mapTmp.getKey();
				List<TsSimpleCode> tsTmpList = mapTmp.getValue();
				List<TdZwTjorgQualAnnexZzsb> annexList = qualAnnexMap.get(mapKey);
				if(null == annexList){
					annexList = new ArrayList<>();
				}
				if(null != tsTmpList && tsTmpList.size() > 0){
					for(TsSimpleCode tsCode : tsTmpList){
						annexList.add(new TdZwTjorgQualAnnexZzsb(tsCode));
					}
				}
				qualAnnexMap.put(mapKey,annexList);
			}
		}
		if(null != mainId){
			String hql = " from TdZwTjorgQualAnnexZzsb t where t.fkByMainId.rid = " + mainId.intValue();
			List<TdZwTjorgQualAnnexZzsb> annexList = commService.findData(hql, null);
			if(annexList != null && annexList.size() > 0){
				//只要保存了就会存在基础数据 不需要判断 基础数据不存在的问题
				for(TdZwTjorgQualAnnexZzsb annex : annexList){
					if(null != topInstitutionMap && !topInstitutionMap.isEmpty() &&
							null != topInstitutionMap.get(annex.getFkByRuleId().getRid())){
						List<TdZwTjorgQualAnnexZzsb> tmpAnnexList = qualAnnexMap.get(annex.getFkByRuleId().getRid());
						if(null == tmpAnnexList){
							tmpAnnexList = new ArrayList<>();
						}
						tmpAnnexList.add(annex);
						qualAnnexMap.put(annex.getFkByRuleId().getRid(), tmpAnnexList);
						continue;
					}

					for(Entry<Integer,List<TdZwTjorgQualAnnexZzsb>> tmpMap : qualAnnexMap.entrySet()){
						List<TdZwTjorgQualAnnexZzsb> tmpList = tmpMap.getValue();
						boolean flag = false;
						if(null != tmpList && tmpList.size() > 0){
							for(TdZwTjorgQualAnnexZzsb qualTmp : tmpList){
								if(qualTmp.getFkByRuleId().getRid() ==
										annex.getFkByRuleId().getRid().intValue()){
									fillTjorgQualAnnexData(qualTmp, annex);
									flag = true;
									break;
								}
							}
							if(flag){
								break;
							}
						}
						if(flag){
							break;
						}
					}
				}
			}else{
				qualAnnexMap = new HashMap<>();
			}
		}else{
			qualAnnexMap = new HashMap<>();
		}
	}

	/** 给对象赋值 */
	public void fillTjorgQualAnnexData(TdZwTjorgQualAnnexZzsb tmp, TdZwTjorgQualAnnexZzsb qualAnnex){
		tmp.setRid(qualAnnex.getRid());
		tmp.setFkByMainId(qualAnnex.getFkByMainId());
		tmp.setFkByRuleId(qualAnnex.getFkByRuleId());
		tmp.setOtherRuleName(qualAnnex.getOtherRuleName());
		tmp.setIfHas(qualAnnex.getIfHas());
		tmp.setAnnexPath(qualAnnex.getAnnexPath());
		tmp.setCreateDate(qualAnnex.getCreateDate());
		tmp.setCreateManid(qualAnnex.getCreateManid());
		tmp.setModifyManid(qualAnnex.getModifyManid());
		tmp.setModifyDate(qualAnnex.getModifyDate());
	}

	/**
	 * <p>描述 转换ZwJkItemPO-->ZwJkItemRowPO</p>
	 *
	 * @param ZwJkItemPOS
	 * @MethodAuthor gongzhe,2022/4/15 15:43,convertZwJkItemPOToRowPO
	 * @return java.util.List<com.chis.modules.heth.zzsb.logic.ZwJkItemRowPO>
	 */
	public List<ZwJkItemRowPO> convertZwJkItemPOToRowPO(List<ZwJkItemPO> ZwJkItemPOS){
		boolean out = zwOrginfoEntity!=null && zwOrginfoEntity.getOutWorkPower()!=null && zwOrginfoEntity.getOutWorkPower()==1;
		List<ZwJkItemRowPO> result = new ArrayList<>();
		int start = 0;
		Map<Integer,Integer> rowspanMap = new HashMap<>();
		if(this.zwOrginfoEntity.getState()==1){
			//查看时，需过滤掉停用的码表，计算需要合并的行
			for (int i = 0; i < ZwJkItemPOS.size(); i++) {
				ZwJkItemPO itemPO = ZwJkItemPOS.get(i);
				TsSimpleCode jcItem = itemPO.getJcItem();
				List<TsSimpleCode> jcItemList = itemPO.getJcItemList();
				//已勾选的服务小类
				List<String> selectedSerItmDetail = itemPO.getSelectedSerItmDetail()==null ? null: Arrays.asList(itemPO.getSelectedSerItmDetail());
				if(CollectionUtils.isEmpty(selectedSerItmDetail)){
					rowspanMap.put(jcItem.getRid(),0);
					continue;
				}
				//未停用码表数量
				int count = 0;
				for (int j = 0; j < jcItemList.size(); j++) {
					TsSimpleCode c = jcItemList.get(j);
					if(selectedSerItmDetail.contains(c.getRid().toString())){
						count++;
					}
				}
				rowspanMap.put(jcItem.getRid(),count);
			}
		}
		for (int i = 0; i < ZwJkItemPOS.size(); i++) {
			ZwJkItemPO itemPO = ZwJkItemPOS.get(i);
			TsSimpleCode jcItem = itemPO.getJcItem();
			List<TsSimpleCode> jcItemList = itemPO.getJcItemList();
			if(i>0){
				if(this.zwOrginfoEntity.getState()==1){
					start += rowspanMap.get(ZwJkItemPOS.get(i-1).getJcItem().getRid());
				}else{
					start += ZwJkItemPOS.get(i-1).getJcItemList().size();
				}
			}
			//已勾选的服务小类
			List<String> selectedSerItmDetail = itemPO.getSelectedSerItmDetail()==null ? null: Arrays.asList(itemPO.getSelectedSerItmDetail());
			List<String> selectedIfExterInspect = itemPO.getSelectedIfExterInspect()==null ? null: Arrays.asList(itemPO.getSelectedIfExterInspect());
			for (int j = 0; j < jcItemList.size(); j++) {
				TsSimpleCode c = jcItemList.get(j);
				if(this.zwOrginfoEntity.getState()==1 && (selectedSerItmDetail == null || !selectedSerItmDetail.contains(c.getRid().toString()))) {
					//如果是查看，且勾选的里面未包含该子项，则跳过
					continue;
				}
				ZwJkItemRowPO row = new ZwJkItemRowPO();
				row.setJcItem(jcItem);
				row.setSerItem(c);
				if(this.zwOrginfoEntity.getState()==1){
					row.setRowspan(rowspanMap.get(jcItem.getRid()));
				}else{
					row.setRowspan(jcItemList.size());
				}
				row.setRowspanStart(start);
				if(selectedSerItmDetail!=null && selectedSerItmDetail.contains(c.getRid().toString())){
					row.setSelectedSerItmDetail(new String[]{c.getRid().toString()});
				}
				if(out){
					row.setIfExterInspect("0");
				}
				if(selectedIfExterInspect!=null && selectedIfExterInspect.contains(c.getRid().toString())){
					row.setSelectedIfExterInspect(new String[]{c.getRid().toString()});
					row.setIfExterInspect("1");
				}
				result.add(row);
			}
		}
		return result;
	}

	/**
	 * 去重string(保持原分割顺序)
	 *
	 * @param str  原始按str1分割的string
	 * @param str1 原始string分割符
	 * @param str2 去重后的string分割符
	 * @return 去重后的List(保持原List顺序)
	 */
	public static String removeDupByContains(String str, String str1, String str2) {
		List<String> list = StringUtils.string2list(str, str1);
		List<String> newList = new ArrayList<>();
		for (String s : list) {
			boolean isContains = newList.contains(s);
			if (!isContains) {
				newList.add(s);
			}
		}
		list.clear();
		list.addAll(newList);
		return StringUtils.list2string(list, str2);
	}

	/**
	 * 导出
	 */
	public DefaultStreamedContent export() {
		RequestContext context = RequestContext.getCurrentInstance();
		context.execute("showStatus();");
		String excelTitle;
		ExcelExportUtil excelExportUtil;
		excelTitle = "资质机构汇总表";
		List<String> excelHeadersList = new ArrayList<>();
		excelHeadersList.add("地区");
		excelHeadersList.add("单位名称");
		excelHeadersList.add("单位地址");
		excelHeadersList.add("资质类型");
		excelHeadersList.add("法定代表人");
		excelHeadersList.add("法定代表人职务");
		excelHeadersList.add("备案（证书）编号");
		excelHeadersList.add("备案（发证）日期");
		excelHeadersList.add("失效日期");
		excelHeadersList.add("服务项目");
		if (this.ifZoneApprove) {
			excelHeadersList.add("批准的执业区域");
		}
		if (this.ifHethFj) {
			excelHeadersList.add("是否具备外出开展职业健康检查能力");
		}
		if ("1".equals(this.zkCheckRst)) {
			excelHeadersList.add("质控日期");
			excelHeadersList.add("质控结论");
		}
		excelHeadersList.add("最后申报日期");
		String[] excelHeaders = excelHeadersList.toArray(new String[0]);
		excelExportUtil = new ExcelExportUtil(excelTitle, excelHeaders, pakExcelExportDataList(executeExportSql(), excelHeadersList.size()));
		excelExportUtil.setFrozenPaneRowsNum(2);
		Workbook wb = excelExportUtil.exportExcel();
		if (wb != null) {
			try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
				String fileName = excelTitle + ".xlsx";
				fileName = URLEncoder.encode(fileName, "UTF-8");
				wb.write(baos);
				baos.flush();
				byte[] aa = baos.toByteArray();
				context.execute("hideStatus();");
				return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
			} catch (Exception e) {
				JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
			}
		}
		context.execute("hideStatus();");
		return null;
	}

	private List<Object[]> executeExportSql() {
		Map<String, Object> paramMap = new HashMap<>();
		StringBuilder baseSql = new StringBuilder();
		if (typeList.contains(0)) {
			if (baseSql.length() > 0) {
				baseSql.append(" UNION ALL ");
			}
			baseSql.append("SELECT DISTINCT ");
			baseSql.append("	T1.ORG_ID			ORGID, ");
			baseSql.append("	0					ORGTYPE, ");
			baseSql.append("	T1.ORG_NAME			ORGNAME, ");
			baseSql.append("	T1.ORG_ADDR			ORGADDR, ");
			baseSql.append("	T1.ORG_FZ			FZ, ");
			baseSql.append("	T1.ORG_FZZW			FZZW, ");
			baseSql.append("	T1.CERT_NO			CERTNO, ");
			baseSql.append("	T1.FIRST_GETDAY		FIRDAY, ");
			baseSql.append("	T1.VALID_DATE		VALID_DATE, ");
			baseSql.append("	T1.CREATE_DATE		MODDAY, ");
			baseSql.append("	T1.RID				MAINID, ");
			baseSql.append("	T1.STATE			STATE, ");
			baseSql.append("	LISTAGG(SC1.CODE_NAME, '#@，@#') WITHIN GROUP (ORDER BY TI.RID) OVER (PARTITION BY T1.RID) SERVICE_ITEMS, ");
			baseSql.append("	ZA.ZONE_APPROVES	ZONE_APPROVES, ");
			baseSql.append("	DECODE(T1.OUT_WORK_POWER, 1, '具备', 0, '不具备', '')                                 OUT_WORK_POWER, ");
			baseSql.append("	CR1.CHECK_DATE		CHECK_DATE, ");
			baseSql.append("	SC.CODE_NAME		CODE_NAME, ");
			baseSql.append("	ROW_NUMBER() OVER (PARTITION BY T1.RID ORDER BY CR1.CHECK_DATE DESC, CR1.RID DESC) RN ");
			baseSql.append("FROM TD_ZW_TJORGINFO T1 ");
			baseSql.append("	LEFT JOIN TD_ZW_TJORGGITEMS TI ON T1.RID = TI.ORG_ID ");
			baseSql.append("	LEFT JOIN TS_SIMPLE_CODE SC1 ON to_char(TI.ITEM_CODE) = SC1.CODE_NO ");
			baseSql.append("	LEFT JOIN TS_CODE_TYPE CT ON SC1.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5018' ");
			baseSql.append("	LEFT JOIN ZONE_APPROVES_TABLE ZA ON T1.RID = ZA.RID ");
			baseSql.append("	LEFT JOIN (SELECT CR.* ");
			baseSql.append("		FROM TD_ZW_CHECK_RST CR ");
			baseSql.append("			INNER JOIN TS_SIMPLE_CODE SC2 ON CR.CHECK_TYPE_ID = SC2.RID AND SC2.EXTENDS1 = 1) CR1 ");
			baseSql.append("			ON T1.ORG_ID = CR1.UNIT_ID AND NVL(CR1.DEL_MARK, 0) = 0 AND CR1.STATE = 1 ");
			baseSql.append("	LEFT JOIN TS_SIMPLE_CODE SC ON CR1.CHECK_RST_ID = SC.RID ");
			baseSql.append("WHERE ((NVL(SC1.RID, 0) <> 0 AND NVL(CT.RID, 0) <> 0) OR (NVL(SC1.RID, 0) = 0)) ");
		}
		if (typeList.contains(1)) {
			if (baseSql.length() > 0) {
				baseSql.append(" UNION ALL ");
			}
			baseSql.append("SELECT DISTINCT ");
			baseSql.append("	T2.ORG_ID			ORGID, ");
			baseSql.append("	1                   ORGTYPE, ");
			baseSql.append("	T2.ORG_NAME			ORGNAME, ");
			baseSql.append("	T2.ORG_ADDR			ORGADDR, ");
			baseSql.append("	T2.ORG_FZ			FZ, ");
			baseSql.append("	T2.ORG_FZZW			FZZW, ");
			baseSql.append("	T2.CERT_NO			CERTNO, ");
			baseSql.append("	T2.FIRST_GETDAY		FIRDAY, ");
			baseSql.append("	T2.VALID_DATE		VALID_DATE, ");
			baseSql.append("	T2.CREATE_DATE		MODDAY, ");
			baseSql.append("	T2.RID				MAINID, ");
			baseSql.append("	T2.STATE			STATE, ");
			baseSql.append("	LISTAGG(SC1.CODE_NAME, '#@，@#') WITHIN GROUP (ORDER BY TI.RID) OVER (PARTITION BY T2.RID) SERVICE_ITEMS, ");
			baseSql.append("	''                  ZONE_APPROVES, ");
			baseSql.append("	''                  OUT_WORK_POWER, ");
			baseSql.append("	NULL                CHECK_DATE, ");
			baseSql.append("	''                  CODE_NAME, ");
			baseSql.append("	1                   RN ");
			baseSql.append("FROM TD_ZW_SRVORGINFO T2 ");
			baseSql.append("	LEFT JOIN TD_ZW_SRVORGITEMS TI ON T2.RID = TI.ORG_ID ");
			baseSql.append("	LEFT JOIN TS_SIMPLE_CODE SC1 ON to_char(TI.ITEM_CODE) = SC1.CODE_NO ");
			baseSql.append("	LEFT JOIN TS_CODE_TYPE CT ON SC1.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5019' ");
			baseSql.append("WHERE (NVL(SC1.RID, 0) <> 0 AND NVL(CT.RID, 0) <> 0) OR (NVL(SC1.RID, 0) = 0) ");
		}
		if (typeList.contains(2)) {
			if (baseSql.length() > 0) {
				baseSql.append(" UNION ALL ");
			}
			baseSql.append("SELECT DISTINCT ");
			baseSql.append("	T3.ORG_ID           ORGID, ");
			baseSql.append("	2                   ORGTYPE, ");
			baseSql.append("	T3.ORG_NAME         ORGNAME, ");
			baseSql.append("	T3.ORG_ADDR         ORGADDR, ");
			baseSql.append("	T3.ORG_FZ           FZ, ");
			baseSql.append("	T3.ORG_FZZW         FZZW, ");
			baseSql.append("	T3.CERT_NO          CERTNO, ");
			baseSql.append("	T3.FIRST_GETDAY     FIRDAY, ");
			baseSql.append("	T3.VALID_DATE       VALID_DATE, ");
			baseSql.append("	T3.CREATE_DATE      MODDAY, ");
			baseSql.append("	T3.RID              MAINID, ");
			baseSql.append("	T3.STATE            STATE, ");
			baseSql.append("	LISTAGG(SC1.CODE_NAME, '#@，@#') WITHIN GROUP (ORDER BY TI.RID) OVER (PARTITION BY T3.RID) SERVICE_ITEMS, ");
			baseSql.append("	''                  ZONE_APPROVES, ");
			baseSql.append("	''                  OUT_WORK_POWER, ");
			baseSql.append("	CR1.CHECK_DATE       CHECK_DATE, ");
			baseSql.append("	SC.CODE_NAME        CODE_NAME, ");
			baseSql.append("	ROW_NUMBER() OVER (PARTITION BY T3.RID ORDER BY CR1.CHECK_DATE DESC, CR1.RID DESC) RN ");
			baseSql.append("FROM TD_ZW_DIAGORGINFO T3 ");
			baseSql.append("	LEFT JOIN TD_ZW_DIAGITEMS TI ON T3.RID = TI.ORG_ID ");
			baseSql.append("	LEFT JOIN TS_SIMPLE_CODE SC1 ON to_char(TI.ITEM_CODE) = SC1.CODE_NO ");
			baseSql.append("	LEFT JOIN TS_CODE_TYPE CT ON SC1.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5020' ");
			baseSql.append("	LEFT JOIN (SELECT CR.* ");
			baseSql.append("		FROM TD_ZW_CHECK_RST CR ");
			baseSql.append("			INNER JOIN TS_SIMPLE_CODE SC2 ON CR.CHECK_TYPE_ID = SC2.RID AND SC2.EXTENDS1 = 2) CR1 ");
			baseSql.append("			ON T3.ORG_ID = CR1.UNIT_ID AND NVL(CR1.DEL_MARK, 0) = 0 AND CR1.STATE = 1 ");
			baseSql.append("	LEFT JOIN TS_SIMPLE_CODE SC ON CR1.CHECK_RST_ID = SC.RID ");
			baseSql.append("WHERE ((NVL(SC1.RID, 0) <> 0 AND NVL(CT.RID, 0) <> 0) OR (NVL(SC1.RID, 0) = 0)) ");
		}
		if (typeList.contains(3)) {
			if (baseSql.length() > 0) {
				baseSql.append(" UNION ALL ");
			}
			baseSql.append("SELECT DISTINCT ");
			baseSql.append("	T4.ORG_ID          ORGID, ");
			baseSql.append("	3                  ORGTYPE, ");
			baseSql.append("	T4.ORG_NAME        ORGNAME, ");
			baseSql.append("	T4.ORG_ADDR        ORGADDR, ");
			baseSql.append("	T4.ORG_FZ          FZ, ");
			baseSql.append("	T4.ORG_FZZW        FZZW, ");
			baseSql.append("	T4.CERT_NO         CERTNO, ");
			baseSql.append("	T4.FIRST_GETDAY    FIRDAY, ");
			baseSql.append("	T4.VALID_DATE      VALID_DATE, ");
			baseSql.append("	T4.CREATE_DATE     MODDAY, ");
			baseSql.append("	T4.RID             MAINID, ");
			baseSql.append("	T4.STATE           STATE, ");
			baseSql.append("	LISTAGG(SC2.CODE_NAME, '#@，@#') WITHIN GROUP (ORDER BY TI.RID) OVER (PARTITION BY T4.RID) SERVICE_ITEMS, ");
			baseSql.append("	''                 ZONE_APPROVES, ");
			baseSql.append("	''                 OUT_WORK_POWER, ");
			baseSql.append("	NULL               CHECK_DATE, ");
			baseSql.append("	''                 CODE_NAME, ");
			baseSql.append("	1                  RN ");
			baseSql.append("FROM TD_ZW_OCCHETH_INFO T4 ");
			baseSql.append("	LEFT JOIN TD_ZW_OCCHETH_ITEMS TI ON T4.RID = TI.ORG_ID ");
			baseSql.append("	LEFT JOIN TS_SIMPLE_CODE SC1 ON to_char(TI.ITEM_CODE) = SC1.CODE_NO ");
			baseSql.append("	LEFT JOIN TS_SIMPLE_CODE SC2 ON SC1.CODE_LEVEL_NO = (SC2.CODE_LEVEL_NO || '.' || SC1.CODE_NO) AND SC1.CODE_TYPE_ID = SC2.CODE_TYPE_ID ");
			baseSql.append("	LEFT JOIN TS_CODE_TYPE CT ON SC1.CODE_TYPE_ID = CT.RID AND CT.CODE_TYPE_NAME = '5320' AND SC2.CODE_TYPE_ID = CT.RID ");
			baseSql.append("WHERE (NVL(SC1.RID, 0) <> 0 AND NVL(CT.RID, 0) <> 0) OR (NVL(SC1.RID, 0) = 0) ");
		}
		if (typeList.contains(4)) {
			if (baseSql.length() > 0) {
				baseSql.append(" UNION ALL ");
			}
			baseSql.append("SELECT DISTINCT ");
			baseSql.append("	T4.ORG_ID          ORGID, ");
			baseSql.append("	4                  ORGTYPE, ");
			baseSql.append("	T4.ORG_NAME        ORGNAME, ");
			baseSql.append("	T4.ORG_ADDR        ORGADDR, ");
			baseSql.append("	T4.ORG_FZ          FZ, ");
			baseSql.append("	T4.ORG_FZZW        FZZW, ");
			baseSql.append("	null	           CERTNO, ");
			baseSql.append("	null     		   FIRDAY, ");
			baseSql.append("	null               VALID_DATE, ");
			baseSql.append("	T4.CREATE_DATE     MODDAY, ");
			baseSql.append("	T4.RID             MAINID, ");
			baseSql.append("	T4.STATE           STATE, ");
			baseSql.append("	'' 				   SERVICE_ITEMS, ");
			baseSql.append("	''                 ZONE_APPROVES, ");
			baseSql.append("	''                 OUT_WORK_POWER, ");
			baseSql.append("	NULL               CHECK_DATE, ");
			baseSql.append("	''                 CODE_NAME, ");
			baseSql.append("	1                  RN ");
			baseSql.append("    FROM TD_ZW_SUPPORT_ORG T4 ");

		}
		StringBuilder mainSql = new StringBuilder();
		if (typeList.contains(0)) {
			mainSql.append("WITH ZONE_APPROVES_TABLE AS ( ");
			mainSql.append("SELECT DISTINCT T.RID, LISTAGG(Z.ZONE_NAME, '#@，@#') WITHIN GROUP (ORDER BY TOR.RID) OVER (PARTITION BY T.RID) ZONE_APPROVES ");
			mainSql.append("FROM TD_ZW_TJORGINFO T ");
			mainSql.append("LEFT JOIN TD_ZW_TJORG_OUT_RANGE TOR ON T.RID = TOR.ORG_ID ");
			mainSql.append("LEFT JOIN TS_ZONE Z ON TOR.ZONE_ID = Z.RID ");
			mainSql.append(")");
		}
		mainSql.append("SELECT ");
		mainSql.append("	TC.FULL_NAME      P1, ");
		mainSql.append("	TA.ORGNAME        P2, ");
		mainSql.append("	TA.ORGADDR        P3, ");
		mainSql.append("	TA.ORGTYPE        P4, ");
		mainSql.append("	TA.FZ             P5, ");
		mainSql.append("	TA.FZZW           P6, ");
		mainSql.append("	TA.CERTNO         P7, ");
		mainSql.append("	TA.FIRDAY         P8, ");
		mainSql.append("	TA.VALID_DATE     P9, ");
		mainSql.append("	TA.SERVICE_ITEMS  P10, ");
		mainSql.append("	TA.ZONE_APPROVES  P11, ");
		mainSql.append("	TA.OUT_WORK_POWER P12, ");
		mainSql.append("	TA.CHECK_DATE     P13, ");
		mainSql.append("	TA.CODE_NAME      P14, ");
		mainSql.append("	TA.MODDAY         P15, ");
		mainSql.append("	TA.MAINID, ");
		mainSql.append("	TA.STATE ");
		mainSql.append("FROM (").append(baseSql).append(") TA ");
		mainSql.append("	INNER JOIN TS_UNIT TB ON TA.ORGID = TB.RID ");
		mainSql.append("	INNER JOIN TS_ZONE TC ON TB.ZONE_ID = TC.RID ");
		mainSql.append("	LEFT JOIN TS_ZONE MZ ON TB.MANAGE_ZONE_ID = MZ.RID ");
		mainSql.append("WHERE TC.ZONE_GB LIKE :searchZoneGb escape '\\\' ");
		mainSql.append("	AND TA.RN = 1 ");
		paramMap.put("searchZoneGb", StringUtils.convertBFH(ZoneUtil.zoneSelect(StringUtils.objectToString(this.searchZoneCode)).trim()) + "%");
		if (ObjectUtil.isNotEmpty(searchZwOrhType)) {
			mainSql.append("	AND TA.ORGTYPE = :searchZwOrhType");
			paramMap.put("searchZwOrhType", searchZwOrhType);
		}
		if (ObjectUtil.isNotEmpty(searchUnitName)) {
			mainSql.append("	AND TA.ORGNAME LIKE :searchUnitName");
			paramMap.put("searchUnitName", "%" + StringUtils.convertBFH(searchUnitName) + "%");
		}
		if (searchState != null && searchState.length == 1) {
			mainSql.append("	AND TA.STATE = ").append(searchState[0]);
		}
		if (earlyDays != null) {
			Date date = DateUtils.addDays(new Date(), earlyDays);
			String daysAfterDate = DateUtils.formatDate(date, "yyyy-MM-dd");
			mainSql.append("	AND TA.VALID_DATE < TO_DATE('").append(daysAfterDate).append("','YYYY-MM-DD')");
		}
		mainSql.append(" ORDER BY TC.ZONE_GB,MZ.ZONE_GB,TB.RID,TA.ORGTYPE ");
		return CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(mainSql.toString(), paramMap));
	}

	private List<ExcelExportObject[]> pakExcelExportDataList(List<Object[]> dataList, int headerSize) {
		if (ObjectUtil.isEmpty(dataList) || headerSize <= 0) {
			return new ArrayList<>();
		}
		List<ExcelExportObject[]> excelExportObjectList = new ArrayList<>();
		for (Object[] data : dataList) {
			ExcelExportObject[] objects = new ExcelExportObject[headerSize];

			//地区去除省份
			String fullName = StringUtils.objectToString(data[0]);
			data[0] = fullName.substring(fullName.indexOf("_") + 1);
			String orgType = StringUtils.objectToString(data[3]);
			if ("0".equals(orgType)) {
				data[3] = "职业健康检查机构";
			} else if ("1".equals(orgType)) {
				data[3] = "放射卫生技术服务机构";
			} else if ("2".equals(orgType)) {
				data[3] = "职业病诊断机构";
			} else if ("3".equals(orgType)) {
				data[3] = "职业卫生技术服务机构";
			}else if ("4".equals(orgType)) {
				data[3] = "技术支撑机构";
			}
			int dataIndex = 0;
			int objectIndex = 0;
			objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex++]));
			objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex++]));
			objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex++]));
			objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex++]));
			objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex++]), XSSFCellStyle.ALIGN_CENTER);
			objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex++]), XSSFCellStyle.ALIGN_CENTER);
			objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex++]), XSSFCellStyle.ALIGN_CENTER);
			if (ObjectUtil.isNotEmpty(data[dataIndex])) {
				objects[objectIndex++] = new ExcelExportObject(DateUtils.formatDate((Timestamp) data[dataIndex], "yyyy/MM/dd"), XSSFCellStyle.ALIGN_CENTER);
			} else {
				objects[objectIndex++] = new ExcelExportObject("", XSSFCellStyle.ALIGN_CENTER);
			}
			dataIndex++;
			if (ObjectUtil.isNotEmpty(data[dataIndex])) {
				objects[objectIndex++] = new ExcelExportObject(DateUtils.formatDate((Timestamp) data[dataIndex], "yyyy/MM/dd"), XSSFCellStyle.ALIGN_CENTER);
			} else {
				objects[objectIndex++] = new ExcelExportObject("", XSSFCellStyle.ALIGN_CENTER);
			}
			dataIndex++;
			objects[objectIndex++] = new ExcelExportObject(removeDupByContains(StringUtils.objectToString(data[dataIndex++]), "#@，@#", "，"));
			if (this.ifZoneApprove) {
				objects[objectIndex++] = new ExcelExportObject(removeDupByContains(StringUtils.objectToString(data[dataIndex]), "#@，@#", "，"));
			}
			dataIndex++;
			if (this.ifHethFj) {
				objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex]), XSSFCellStyle.ALIGN_CENTER);
			}
			dataIndex++;
			if ("1".equals(this.zkCheckRst)) {
				if (ObjectUtil.isNotEmpty(data[dataIndex])) {
					objects[objectIndex++] = new ExcelExportObject(DateUtils.formatDate((Timestamp) data[dataIndex], "yyyy/MM/dd"), XSSFCellStyle.ALIGN_CENTER);
				} else {
					objects[objectIndex++] = new ExcelExportObject("", XSSFCellStyle.ALIGN_CENTER);
				}
				objects[objectIndex++] = new ExcelExportObject(StringUtils.objectToString(data[dataIndex + 1]));
			}
			dataIndex++;
			dataIndex++;
			if (ObjectUtil.isNotEmpty(data[dataIndex])) {
				objects[objectIndex] = new ExcelExportObject(DateUtils.formatDate((Timestamp) data[dataIndex], "yyyy/MM/dd"), XSSFCellStyle.ALIGN_CENTER);
			} else {
				objects[objectIndex] = new ExcelExportObject("", XSSFCellStyle.ALIGN_CENTER);
			}
			excelExportObjectList.add(objects);
		}
		return excelExportObjectList;
	}

	/**
	 * 职业健康检查机构-服务概况
	 */
	public void showServiceOverview() {
		String unitId = this.zwIntellReportServiceImpl.findUnitByOrgId(selectObj[8].toString());
		System.out.println(unitId);
		this.pieJson = buildPieJson(this.zwIntellReportServiceImpl.findServiceOverview(unitId));
		RequestContext.getCurrentInstance().execute("buildPieChart();");
		RequestContext.getCurrentInstance().execute("PF('ServiceOverviewDialog').show();");
	}

	private String buildPieJson(List<Object[]> serviceOverviewList) {
		this.serviceOverviewStr1 = "0";
		this.serviceOverviewStr2 = "0";
		this.serviceOverviewStr3 = "-";
		List<String[]> pieDataList = new ArrayList<>();
		for (Object[] objects : serviceOverviewList) {
			String s0 = StringUtils.objectToString(objects[0]);
			String s1 = StringUtils.objectToString(objects[1]);
			String s2 = StringUtils.objectToString(objects[2]);
			if ("1".equals(s0)) {
				s2 = ObjectUtil.isEmpty(s2) ? "0" : s2;
				this.serviceOverviewStr1 = s2;
			}
			if ("2".equals(s0)) {
				s2 = ObjectUtil.isEmpty(s2) ? "0" : s2;
				this.serviceOverviewStr2 = s2;
			}
			if ("3".equals(s0)) {
				s2 = ObjectUtil.isEmpty(s2) ? "在线直报" : ("0".equals(s2) ? "在线直报" : "系统对接");
				this.serviceOverviewStr3 = s2;
			}
			if ("4".equals(s0)) {
				s2 = ObjectUtil.isEmpty(s2) ? "0" : s2;
				pieDataList.add(new String[]{s1, s2});
			}
		}
		List<PieDataPojo> pies = Lists.newArrayList();
		String[] pieLegends = new String[pieDataList.size()];
		for (int i = 0, pieDataListSize = pieDataList.size(); i < pieDataListSize; i++) {
			String[] strings = pieDataList.get(i);
			pieLegends[i] = strings[0];
			pies.add(EchartsUtil.initPieData(strings[0], strings[1], ""));
		}
		String pieTitle = StringUtils.objectToString(selectObj[2]) + "（体检结论分布图）";
		return initPieJson(pies, pieTitle, pieLegends);
	}

	private String initPieJson(List<PieDataPojo> pies, String pieTitle, String[] pieLegends) {
		Pie pie = new Pie();
		pie.setData(pies);

		PieChartParamPojo paramPojo = new PieChartParamPojo();
		paramPojo.setData(pie);
		paramPojo.setTitle(pieTitle);
		paramPojo.setTooltip("{b}<br/>人次数：{c} <br/>占比：{d}%");
		paramPojo.setLegend(pieLegends);
		return EchartsUtil.initPieXml1(paramPojo);
	}

	/**
	 * 初始化-支撑机构-仪器设备配置信息
	 */
	public void initSupportInst() {
		this.supportInstVOList = new LinkedList<>();
		this.supportInstSimpleCodeList = new ArrayList<>();
		if (!new Integer(4).equals(this.zwOrgType)) {
			return;
		}
		if (ObjectUtil.isEmpty(this.supportInstSimpleCodeList)) {
			this.supportInstSimpleCodeList = this.commService.findSimpleCodeListOrderByNumLevelNo("5607");
		}
		Set<Integer> zwSupportInstIdList = this.zwIntellReportServiceImpl.findZwSupportInstIdList(this.zwOrginfoEntity.getRid());
		initSupportInstAbilityVOList(this.supportInstVOList, this.supportInstSimpleCodeList, zwSupportInstIdList, false);
	}

	/**
	 * 封装多级码表页面VO
	 *
	 * @param supportInstAbilityVOList    层级记录码表VO
	 * @param simpleCodeList              码表数据
	 * @param zwSupportInstIdList         机构已有的码表rid
	 * @param ifAbility                   是否能力建设
	 */
	private void initSupportInstAbilityVOList(List<SupportInstAbilityVO> supportInstAbilityVOList,
											  List<TsSimpleCode> simpleCodeList,
											  Set<Integer> zwSupportInstIdList, boolean ifAbility) {
		if (ObjectUtil.isEmpty(simpleCodeList)) {
			return;
		}
		Map<String, SupportInstAbilityVO> supportInstAbilityVOMap = new HashMap<>();
		for (TsSimpleCode simpleCode : simpleCodeList) {
			Integer rid = simpleCode.getRid();
			String codeLevelNo = simpleCode.getCodeLevelNo();
			if (ObjectUtil.isEmpty(rid) || ObjectUtil.isEmpty(codeLevelNo)) {
				continue;
			}
			String extendS3 = StringUtils.objectToString(simpleCode.getExtendS3());
			if (StringUtils.isNotBlank(extendS3) && !extendS3.equals(this.unitType)) {
				continue;
			}
			SupportInstAbilityVO supportInstAbilityVO = new SupportInstAbilityVO(simpleCode);
			supportInstAbilityVO.setSel(zwSupportInstIdList.contains(rid));
			boolean firstNode = !codeLevelNo.contains(".");
			String key;
			//第一级节点直接添加
			if (firstNode) {
				if (supportInstAbilityVOMap.containsKey(codeLevelNo)) {
					continue;
				}
				supportInstAbilityVOMap.put(codeLevelNo, supportInstAbilityVO);
				supportInstAbilityVOList.add(supportInstAbilityVO);
				continue;
			}
			//非第一级节点需要找到父级
			key = codeLevelNo.substring(0, codeLevelNo.lastIndexOf("."));
			if (supportInstAbilityVOMap.containsKey(codeLevelNo) || !supportInstAbilityVOMap.containsKey(key)) {
				continue;
			}
			supportInstAbilityVOMap.put(codeLevelNo, supportInstAbilityVO);
			SupportInstAbilityVO parentVO = supportInstAbilityVOMap.get(key);
			parentVO.addChildList(supportInstAbilityVO);
		}
		//详情页需要移除未选择的
		removeUnSelSupportInstAbilityVOList(supportInstAbilityVOList, ifAbility);
		//处理序号
		for (SupportInstAbilityVO supportInstAbilityVO1 : supportInstAbilityVOList) {
			initSupportInstAbilityVOListNum(supportInstAbilityVO1);
		}
	}

	/**
	 * 移除未选择的
	 *
	 * @param supportInstAbilityVOList 页面VO
	 */
	private void removeUnSelSupportInstAbilityVOList(List<SupportInstAbilityVO> supportInstAbilityVOList, boolean ifAbility) {
		Iterator<SupportInstAbilityVO> iterator = supportInstAbilityVOList.iterator();
		while (iterator.hasNext()) {
			SupportInstAbilityVO i = iterator.next();
			if (ObjectUtil.isNotEmpty(i.getChildList())) {
				removeUnSelSupportInstAbilityVOList(i.getChildList(), ifAbility);
			}
			if (ifAbility) {
				String ext1 = i.getSimpleCode().getExtendS1();
				if ("1".equals(ext1) || "2".equals(ext1)) {
					continue;
				}
			}
			if (i.getSel() || ObjectUtil.isNotEmpty(i.getChildList())) {
				continue;
			}
			iterator.remove();
		}
	}

	/**
	 * 处理序号
	 *
	 * @param supportInstAbilityVO1 一级结构
	 */
	private void initSupportInstAbilityVOListNum(SupportInstAbilityVO supportInstAbilityVO1) {
		List<SupportInstAbilityVO> childList = supportInstAbilityVO1.getChildList();
		//二级结构要么都可选要么都不可选
		boolean onlyLevel2 = ObjectUtil.isNotEmpty(supportInstAbilityVO1.getChildList()) &&
				ObjectUtil.isEmpty(supportInstAbilityVO1.getChildList().get(0).getChildList());
		supportInstAbilityVO1.setOnlyLevel2(onlyLevel2);
		int num = 0;
		for (int i = 0, childListSize = childList.size(); i < childListSize; i++) {
			SupportInstAbilityVO supportInstAbilityVO2 = childList.get(i);
			//最大只有二级结构时二级结构需要序号
			if (onlyLevel2) {
				supportInstAbilityVO2.setNum(StringUtils.objectToString(i + 1));
				continue;
			}
			for (int ii = 0; ii < supportInstAbilityVO2.getChildList().size(); ii++) {
				num++;
				SupportInstAbilityVO supportInstAbilityVO3 = supportInstAbilityVO2.getChildList().get(ii);
				supportInstAbilityVO3.setNum(StringUtils.objectToString(num));
				for (int i1 = 0; i1 < supportInstAbilityVO3.getChildList().size(); i1++) {
					SupportInstAbilityVO supportInstAbilityVO4 = supportInstAbilityVO3.getChildList().get(i1);
					String num1 = num + "." + StringUtils.objectToString(i1 + 1);
					supportInstAbilityVO4.setNum(num1);
				}
			}
		}
	}

	/**
	 * <p>方法描述：能力建设信息初始化 </p>
	 * pw 2023/10/27
	 **/
	private void initAbility () {
		this.abilitySimpleCodeList = new ArrayList<>();
		this.abilityShowList = new ArrayList<>();
		this.supportHealth = new TdZwSupportHealth();
		if (!new Integer(4).equals(this.zwOrgType)) {
			return;
		}
		this.abilitySimpleCodeList = this.commService.findSimpleCodeListOrderByNumLevelNo("5608", true);
		this.supportHealth = this.zwIntellReportServiceImpl.findSupportHealthByMainId(this.zwOrginfoEntity.getRid());
		if (null == this.supportHealth) {
			this.supportHealth = new TdZwSupportHealth();
			this.supportHealth.setFkByMainId((TdZwSupportOrgZzsb) this.zwOrginfoEntity);
		}
		List<Integer> abilityList = this.zwIntellReportServiceImpl.findAbilitysByMainId(this.zwOrginfoEntity.getRid());
		Set<Integer> abilitySet = new HashSet<>();
		if (!CollectionUtils.isEmpty(abilityList)) {
			for (Integer ability : abilityList) {
				abilitySet.add(ability);
			}
		}
		initSupportInstAbilityVOList(this.abilityShowList, this.abilitySimpleCodeList, abilitySet, true);
		this.fillChildSpec();
	}

	/**
	 * <p>方法描述： 职业病治疗与康复上一级设置特殊标记 </p>
	 * pw 2023/10/27
	 **/
	private void fillChildSpec () {
		//处理职业病治疗与康复
		for (SupportInstAbilityVO abilityVO : this.abilityShowList) {
			this.fillChildSpec(abilityVO);
		}
	}

	/**
	 * <p>方法描述： 详情页 职业病治疗与康复上一级设置特殊标记 </p>
	 * pw 2023/10/27
	 **/
	private void fillChildSpec (SupportInstAbilityVO abilityVO) {
		List<SupportInstAbilityVO> childList = abilityVO.getChildList();
		if (CollectionUtils.isEmpty(childList)) {
			return;
		}
		for (SupportInstAbilityVO ability : childList) {
			String ext1 = null == ability.getSimpleCode() ? null : ability.getSimpleCode().getExtendS1();
			if ("2".equals(ext1)) {
				abilityVO.setIfChildSpec(true);
				break;
			}
		}
	}

	public TdZwPsntjMainZzsb getFirstTjMain() {
		return firstTjMain;
	}

	public void setFirstTjMain(TdZwPsntjMainZzsb firstTjMain) {
		this.firstTjMain = firstTjMain;
	}

	public List<TdZwPsntjSubZzsb> getFirstTjSubList() {
		return firstTjSubList;
	}

	public void setFirstTjSubList(List<TdZwPsntjSubZzsb> firstTjSubList) {
		this.firstTjSubList = firstTjSubList;
	}

	public TdZwPsntjMainZzsb getLastTjMain() {
		return lastTjMain;
	}

	public void setLastTjMain(TdZwPsntjMainZzsb lastTjMain) {
		this.lastTjMain = lastTjMain;
	}

	public List<TdZwPsntjSubZzsb> getLastTjSubList() {
		return lastTjSubList;
	}

	public void setLastTjSubList(List<TdZwPsntjSubZzsb> lastTjSubList) {
		this.lastTjSubList = lastTjSubList;
	}

	public TdZwPsntjSubZzsb getTjSub() {
		return tjSub;
	}

	public void setTjSub(TdZwPsntjSubZzsb tjSub) {
		this.tjSub = tjSub;
	}

	public List<TdZwInstJcmainZzsb> getJcMainList() {
		return jcMainList;
	}

	public void setJcMainList(List<TdZwInstJcmainZzsb> jcMainList) {
		this.jcMainList = jcMainList;
	}

	public TdZwInstJcmainZzsb getJcMain() {
		return jcMain;
	}

	public void setJcMain(TdZwInstJcmainZzsb jcMain) {
		this.jcMain = jcMain;
	}

	public List<TdZwInstJcsubZzsb> getJcSubList() {
		return jcSubList;
	}

	public void setJcSubList(List<TdZwInstJcsubZzsb> jcSubList) {
		this.jcSubList = jcSubList;
	}

	public TdZwInstJcsubZzsb getJcSub() {
		return jcSub;
	}

	public void setJcSub(TdZwInstJcsubZzsb jcSub) {
		this.jcSub = jcSub;
	}

	public List<TdZwInstSbjcmainZzsb> getSbJcMainList() {
		return sbJcMainList;
	}

	public void setSbJcMainList(List<TdZwInstSbjcmainZzsb> sbJcMainList) {
		this.sbJcMainList = sbJcMainList;
	}

	public TdZwInstSbjcmainZzsb getSbJcMain() {
		return sbJcMain;
	}

	public void setSbJcMain(TdZwInstSbjcmainZzsb sbJcMain) {
		this.sbJcMain = sbJcMain;
	}

	public List<TdZwInstSbjcsubZzsb> getSbJcSubList() {
		return sbJcSubList;
	}

	public void setSbJcSubList(List<TdZwInstSbjcsubZzsb> sbJcSubList) {
		this.sbJcSubList = sbJcSubList;
	}

	public TdZwInstSbjcsubZzsb getSbJcSub() {
		return sbJcSub;
	}

	public void setSbJcSub(TdZwInstSbjcsubZzsb sbJcSub) {
		this.sbJcSub = sbJcSub;
	}

	public IZwOrgPsnsZzsb getSelectZwOrgPsn() {
		return selectZwOrgPsn;
	}

	public void setSelectZwOrgPsn(IZwOrgPsnsZzsb selectZwOrgPsn) {
		this.selectZwOrgPsn = selectZwOrgPsn;
	}

	public IZwInstZzsb getSelectOrgInst() {
		return selectOrgInst;
	}

	public void setSelectOrgInst(IZwInstZzsb selectOrgInst) {
		this.selectOrgInst = selectOrgInst;
	}

	public Integer getEarlyDays() {
		return earlyDays;
	}

	public void setEarlyDays(Integer earlyDays) {
		this.earlyDays = earlyDays;
	}

	public Boolean getEarlyDaysDisabled() {
		return earlyDaysDisabled;
	}

	public void setEarlyDaysDisabled(Boolean earlyDaysDisabled) {
		this.earlyDaysDisabled = earlyDaysDisabled;
	}

	public Boolean getEarlyDaysChecked() {
		return earlyDaysChecked;
	}

	public void setEarlyDaysChecked(Boolean earlyDaysChecked) {
		this.earlyDaysChecked = earlyDaysChecked;
	}

	public TdZwPsninfoZzsb getTdZwPsninfo() {
		return tdZwPsninfo;
	}

	public void setTdZwPsninfo(TdZwPsninfoZzsb tdZwPsninfo) {
		this.tdZwPsninfo = tdZwPsninfo;
	}

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList1() {
        return zwAnnexList1;
    }

    public void setZwAnnexList1(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList1) {
        this.zwAnnexList1 = zwAnnexList1;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList2() {
        return zwAnnexList2;
    }

    public void setZwAnnexList2(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList2) {
        this.zwAnnexList2 = zwAnnexList2;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList3() {
        return zwAnnexList3;
    }

    public void setZwAnnexList3(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList3) {
        this.zwAnnexList3 = zwAnnexList3;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList4() {
        return zwAnnexList4;
    }

    public void setZwAnnexList4(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList4) {
        this.zwAnnexList4 = zwAnnexList4;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList5() {
        return zwAnnexList5;
    }

    public void setZwAnnexList5(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList5) {
        this.zwAnnexList5 = zwAnnexList5;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList6() {
        return zwAnnexList6;
    }

    public void setZwAnnexList6(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList6) {
        this.zwAnnexList6 = zwAnnexList6;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList7() {
        return zwAnnexList7;
    }

    public void setZwAnnexList7(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList7) {
        this.zwAnnexList7 = zwAnnexList7;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList8() {
        return zwAnnexList8;
    }

    public void setZwAnnexList8(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList8) {
        this.zwAnnexList8 = zwAnnexList8;
    }

    public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList9() {
        return zwAnnexList9;
    }

    public void setZwAnnexList9(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList9) {
        this.zwAnnexList9 = zwAnnexList9;
    }

	public List<TdZwJsffjgCommAnnexZzsb> getZwAnnexList20() {
		return zwAnnexList20;
	}

	public void setZwAnnexList20(List<TdZwJsffjgCommAnnexZzsb> zwAnnexList20) {
		this.zwAnnexList20 = zwAnnexList20;
	}

	public TdzwPsnInfoZzsbBase getPsnInfoBase() {
		return psnInfoBase;
	}

	public void setPsnInfoBase(TdzwPsnInfoZzsbBase psnInfoBase) {
		this.psnInfoBase = psnInfoBase;
	}

	public boolean isIfHethFj() {
        return ifHethFj;
    }

    public void setIfHethFj(boolean ifHethFj) {
        this.ifHethFj = ifHethFj;
    }

    public String getZoneNames() {
        return zoneNames;
    }

    public void setZoneNames(String zoneNames) {
        this.zoneNames = zoneNames;
    }

	public String getIfZoneRecord() {
		return ifZoneRecord;
	}

	public void setIfZoneRecord(String ifZoneRecord) {
		this.ifZoneRecord = ifZoneRecord;
	}

	public List<TdZwTjorgRecordZzsb> getTjorgRecords() {
		return tjorgRecords;
	}

	public void setTjorgRecords(List<TdZwTjorgRecordZzsb> tjorgRecords) {
		this.tjorgRecords = tjorgRecords;
	}

	public String getIfPersonSummary() {
		return ifPersonSummary;
	}

	public void setIfPersonSummary(String ifPersonSummary) {
		this.ifPersonSummary = ifPersonSummary;
	}

	public List<List<String>> getPersonSummaryList() {
		return personSummaryList;
	}

	public void setPersonSummaryList(List<List<String>> personSummaryList) {
		this.personSummaryList = personSummaryList;
	}

	public String getFillDesc() {
		return fillDesc;
	}

	public void setFillDesc(String fillDesc) {
		this.fillDesc = fillDesc;
	}

    public String getIfQualAnnex() {
        return ifQualAnnex;
    }

    public void setIfQualAnnex(String ifQualAnnex) {
        this.ifQualAnnex = ifQualAnnex;
    }

    public List<TsSimpleCode> getInstitutionList() {
        return institutionList;
    }

    public void setInstitutionList(List<TsSimpleCode> institutionList) {
        this.institutionList = institutionList;
    }

    public List<TsSimpleCode> getTopOfInstitutionList() {
        return topOfInstitutionList;
    }

    public void setTopOfInstitutionList(List<TsSimpleCode> topOfInstitutionList) {
        this.topOfInstitutionList = topOfInstitutionList;
    }

    public Map<Integer, TsSimpleCode> getTopInstitutionMap() {
        return topInstitutionMap;
    }

    public void setTopInstitutionMap(Map<Integer, TsSimpleCode> topInstitutionMap) {
        this.topInstitutionMap = topInstitutionMap;
    }

    public Map<Integer, List<TsSimpleCode>> getInstitutionMap() {
        return institutionMap;
    }

    public void setInstitutionMap(Map<Integer, List<TsSimpleCode>> institutionMap) {
        this.institutionMap = institutionMap;
    }

    public Map<Integer, List<TdZwTjorgQualAnnexZzsb>> getQualAnnexMap() {
        return qualAnnexMap;
    }

    public void setQualAnnexMap(Map<Integer, List<TdZwTjorgQualAnnexZzsb>> qualAnnexMap) {
        this.qualAnnexMap = qualAnnexMap;
    }

	public Integer getZwOrgType() {
		return zwOrgType;
	}

	public void setZwOrgType(Integer zwOrgType) {
		this.zwOrgType = zwOrgType;
	}

	public List<Integer> getTypeList() {
		return typeList;
	}

	public void setTypeList(List<Integer> typeList) {
		this.typeList = typeList;
	}

	public List<SelectItem> getSearchZwOrhTypeList() {
		return searchZwOrhTypeList;
	}

	public void setSearchZwOrhTypeList(List<SelectItem> searchZwOrhTypeList) {
		this.searchZwOrhTypeList = searchZwOrhTypeList;
	}

	public List<ZwJkItemPO> getJkItemList() {
		return jkItemList;
	}

	public void setJkItemList(List<ZwJkItemPO> jkItemList) {
		this.jkItemList = jkItemList;
	}

	public List<TdZwTjorgOutcarZzsb> getTjorgOutcarZzsbList() {
		return tjorgOutcarZzsbList;
	}

	public void setTjorgOutcarZzsbList(List<TdZwTjorgOutcarZzsb> tjorgOutcarZzsbList) {
		this.tjorgOutcarZzsbList = tjorgOutcarZzsbList;
	}

	public String getIfValidityPeriod() {
		return ifValidityPeriod;
	}

	public void setIfValidityPeriod(String ifValidityPeriod) {
		this.ifValidityPeriod = ifValidityPeriod;
	}

	public String getIfReviewProcess() {
		return ifReviewProcess;
	}

	public void setIfReviewProcess(String ifReviewProcess) {
		this.ifReviewProcess = ifReviewProcess;
	}

	public String getIfServiceDetails() {
		return ifServiceDetails;
	}

	public void setIfServiceDetails(String ifServiceDetails) {
		this.ifServiceDetails = ifServiceDetails;
	}

	public List<ZwJkItemRowPO> getJkItemRowList() {
		return jkItemRowList;
	}

	public void setJkItemRowList(List<ZwJkItemRowPO> jkItemRowList) {
		this.jkItemRowList = jkItemRowList;
	}

	public Boolean getIfShowRcdOrg() {
		return ifShowRcdOrg;
	}

	public void setIfShowRcdOrg(Boolean ifShowRcdOrg) {
		this.ifShowRcdOrg = ifShowRcdOrg;
	}

	public List<TsSimpleCode> getRcdOrgList() {
		return rcdOrgList;
	}

	public void setRcdOrgList(List<TsSimpleCode> rcdOrgList) {
		this.rcdOrgList = rcdOrgList;
	}

	public Map<Integer, String> getRcdOrgMap() {
		return rcdOrgMap;
	}

	public void setRcdOrgMap(Map<Integer, String> rcdOrgMap) {
		this.rcdOrgMap = rcdOrgMap;
	}

	public Boolean getIfZoneApprove() {
		return ifZoneApprove;
	}

	public void setIfZoneApprove(Boolean ifZoneApprove) {
		this.ifZoneApprove = ifZoneApprove;
	}

	public String getZoneApproveName() {
		return zoneApproveName;
	}

	public void setZoneApproveName(String zoneApproveName) {
		this.zoneApproveName = zoneApproveName;
	}

	public String getOutCarVersion() {
		return outCarVersion;
	}

	public void setOutCarVersion(String outCarVersion) {
		this.outCarVersion = outCarVersion;
	}

	public String getZkCheckRst() {
		return zkCheckRst;
	}

	public void setZkCheckRst(String zkCheckRst) {
		this.zkCheckRst = zkCheckRst;
	}

	public List<Object[]> getZwCheckRstList() {
		return zwCheckRstList;
	}

	public void setZwCheckRstList(List<Object[]> zwCheckRstList) {
		this.zwCheckRstList = zwCheckRstList;
	}

	public String getIfShowCheckOrgAnaly() {
		return ifShowCheckOrgAnaly;
	}

	public void setIfShowCheckOrgAnaly(String ifShowCheckOrgAnaly) {
		this.ifShowCheckOrgAnaly = ifShowCheckOrgAnaly;
	}

	public String getServiceOverviewStr1() {
		return serviceOverviewStr1;
	}

	public void setServiceOverviewStr1(String serviceOverviewStr1) {
		this.serviceOverviewStr1 = serviceOverviewStr1;
	}

	public String getServiceOverviewStr2() {
		return serviceOverviewStr2;
	}

	public void setServiceOverviewStr2(String serviceOverviewStr2) {
		this.serviceOverviewStr2 = serviceOverviewStr2;
	}

	public String getServiceOverviewStr3() {
		return serviceOverviewStr3;
	}

	public void setServiceOverviewStr3(String serviceOverviewStr3) {
		this.serviceOverviewStr3 = serviceOverviewStr3;
	}

	public String getPieJson() {
		return pieJson;
	}

	public void setPieJson(String pieJson) {
		this.pieJson = pieJson;
	}

	public String getIfShowApproveUnit() {
		return ifShowApproveUnit;
	}

	public void setIfShowApproveUnit(String ifShowApproveUnit) {
		this.ifShowApproveUnit = ifShowApproveUnit;
	}

	public String getIfHaveHouseAnnex() {
		return ifHaveHouseAnnex;
	}

	public void setIfHaveHouseAnnex(String ifHaveHouseAnnex) {
		this.ifHaveHouseAnnex = ifHaveHouseAnnex;
	}

	public TdZwPsnZzlbZzsb getPsnZzlbNew() {
		return psnZzlbNew;
	}

	public void setPsnZzlbNew(TdZwPsnZzlbZzsb psnZzlbNew) {
		this.psnZzlbNew = psnZzlbNew;
	}

	public Boolean getHasResearchAndAcademicInfo() {
		return hasResearchAndAcademicInfo;
	}

	public void setHasResearchAndAcademicInfo(Boolean hasResearchAndAcademicInfo) {
		this.hasResearchAndAcademicInfo = hasResearchAndAcademicInfo;
	}

	public List<List<TdZwSupportTrain>> getShowSupportTrainList() {
		return showSupportTrainList;
	}

	public void setShowSupportTrainList(List<List<TdZwSupportTrain>> showSupportTrainList) {
		this.showSupportTrainList = showSupportTrainList;
	}

	public List<List<TsSimpleCode>> getShowAcademicList() {
		return showAcademicList;
	}

	public void setShowAcademicList(List<List<TsSimpleCode>> showAcademicList) {
		this.showAcademicList = showAcademicList;
	}

	public Integer getAcademicDataSize() {
		return academicDataSize;
	}

	public void setAcademicDataSize(Integer academicDataSize) {
		this.academicDataSize = academicDataSize;
	}

	public List<TdZwSupportThesis> getThesisList() {
		return thesisList;
	}

	public void setThesisList(List<TdZwSupportThesis> thesisList) {
		this.thesisList = thesisList;
	}

	public List<TdZwSupportMomograph> getMonographList() {
		return monographList;
	}

	public void setMonographList(List<TdZwSupportMomograph> monographList) {
		this.monographList = monographList;
	}

	public List<TdZwSupportTopic> getSupportTopics1() {
		return supportTopics1;
	}

	public void setSupportTopics1(List<TdZwSupportTopic> supportTopics1) {
		this.supportTopics1 = supportTopics1;
	}

	public List<TdZwSupportTopic> getSupportTopics2() {
		return supportTopics2;
	}

	public void setSupportTopics2(List<TdZwSupportTopic> supportTopics2) {
		this.supportTopics2 = supportTopics2;
	}

	public List<TdZwSupportTopic> getSupportTopics3() {
		return supportTopics3;
	}

	public void setSupportTopics3(List<TdZwSupportTopic> supportTopics3) {
		this.supportTopics3 = supportTopics3;
	}

	public List<Object[]> getPatentList() {
		return patentList;
	}

	public void setPatentList(List<Object[]> patentList) {
		this.patentList = patentList;
	}

	public List<Object[]> getStandardList() {
		return standardList;
	}

	public void setStandardList(List<Object[]> standardList) {
		this.standardList = standardList;
	}

	public TdZwSupportTrc getSupportTrc() {
		return supportTrc;
	}

	public void setSupportTrc(TdZwSupportTrc supportTrc) {
		this.supportTrc = supportTrc;
	}

	public List<TdZwSupportAdvance> getAdvanceList() {
		return advanceList;
	}

	public void setAdvanceList(List<TdZwSupportAdvance> advanceList) {
		this.advanceList = advanceList;
	}

	public List<TsSimpleCode> getSupportInstSimpleCodeList() {
		return supportInstSimpleCodeList;
	}

	public void setSupportInstSimpleCodeList(List<TsSimpleCode> supportInstSimpleCodeList) {
		this.supportInstSimpleCodeList = supportInstSimpleCodeList;
	}

	public List<SupportInstAbilityVO> getSupportInstVOList() {
		return supportInstVOList;
	}

	public void setSupportInstVOList(List<SupportInstAbilityVO> supportInstVOList) {
		this.supportInstVOList = supportInstVOList;
	}

	public String getUnitType() {
		return unitType;
	}

	public void setUnitType(String unitType) {
		this.unitType = unitType;
	}

	public List<TsSimpleCode> getAbilitySimpleCodeList() {
		return abilitySimpleCodeList;
	}

	public void setAbilitySimpleCodeList(List<TsSimpleCode> abilitySimpleCodeList) {
		this.abilitySimpleCodeList = abilitySimpleCodeList;
	}

	public List<SupportInstAbilityVO> getAbilityShowList() {
		return abilityShowList;
	}

	public void setAbilityShowList(List<SupportInstAbilityVO> abilityShowList) {
		this.abilityShowList = abilityShowList;
	}

	public TdZwSupportHealth getSupportHealth() {
		return supportHealth;
	}

	public void setSupportHealth(TdZwSupportHealth supportHealth) {
		this.supportHealth = supportHealth;
	}

	public List<TsSimpleCode> getTechTypeList() {
		return techTypeList;
	}

	public void setTechTypeList(List<TsSimpleCode> techTypeList) {
		this.techTypeList = techTypeList;
	}

	public List<TechTypeVo> getTechTypesList() {
		return techTypesList;
	}

	public void setTechTypesList(List<TechTypeVo> techTypesList) {
		this.techTypesList = techTypesList;
	}

	public List<Object[]> getChkAbilityList() {
		return chkAbilityList;
	}

	public void setChkAbilityList(List<Object[]> chkAbilityList) {
		this.chkAbilityList = chkAbilityList;
	}

	public String getSearchProjectName() {
		return searchProjectName;
	}

	public void setSearchProjectName(String searchProjectName) {
		this.searchProjectName = searchProjectName;
	}

	public String getSearchJcWay() {
		return searchJcWay;
	}

	public void setSearchJcWay(String searchJcWay) {
		this.searchJcWay = searchJcWay;
	}

	public List<String> getSearchStates() {
		return searchStates;
	}

	public void setSearchStates(List<String> searchStates) {
		this.searchStates = searchStates;
	}
}
