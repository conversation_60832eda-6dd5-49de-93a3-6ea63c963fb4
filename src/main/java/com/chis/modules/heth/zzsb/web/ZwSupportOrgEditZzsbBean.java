package com.chis.modules.heth.zzsb.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.zzsb.entity.*;
import com.chis.common.utils.DateUtils;
import com.chis.common.utils.FileUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.zzsb.entity.vo.SupportInstAbilityVO;
import com.chis.modules.heth.zzsb.logic.SupportTrainPO;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnitAttr;
import com.chis.modules.system.utils.Global;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.fieldset.Fieldset;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;
import org.primefaces.event.UnselectEvent;
import org.primefaces.model.UploadedFile;
import org.springframework.util.CollectionUtils;

import javax.faces.application.FacesMessage;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * <p>类描述： 支撑机构信息管理 </p>
 *
 * @ClassAuthor： pw 2023/2/23
 **/
@ManagedBean(name = "zwSupportOrgEditZzsbBean")
@ViewScoped
public class ZwSupportOrgEditZzsbBean extends ZwOrgInfoBaseZzsbBean {

    /**
     * 支撑机构论文刊物类别
     */
    private Map<Integer, TsSimpleCode> publicationMap = new HashMap<>();

    /** 提交时是否校验职业病治疗与康复相关信息点 */
    private boolean ifAbilityValidateKf;
    /** 记录提交前能力建设中 关闭的fieldset 提交后保持关闭 其余的保持打开状态 */
    private List<Integer> abilityCloseFieldList;
    /** 记录提交前仪器设备配置信息中 关闭的fieldset 提交后保持关闭 其余的保持打开状态 */
    private List<Integer> supportCloseFieldList;

    public ZwSupportOrgEditZzsbBean() {
        super.init();
        this.initBaseInfo();
        initSupportInst();
        this.initAbility();
        this.initSupportTrainInfo();
    }

    /**
     * <p>Description：基本信息初始化 </p>
     * <p>Author： yzz 2023-10-25 </p>
     */
    public void initBaseInfo(){
        //判断当前用户是疾控还是职业防治院
        Set<TsUnitAttr> attrSet = Global.getUser().getTsUnit().getTsUnitAttrs();
        if(null != attrSet && attrSet.size() > 0) {
            for(TsUnitAttr attr: attrSet) {
                if("2001".equals(attr.getTsBsSort().getSortCode())){
                    this.unitType="1";
                    break;
                }else if("2020".equals(attr.getTsBsSort().getSortCode())){
                    this.unitType="2";
                    break;
                }
            }
        }
        //初始化支撑能力
        this.initTechType();

        //初始化具备职业健康技术服务相关资质或能力的情况
        if("1".equals(this.unitType) && !CollectionUtils.isEmpty(zwOrginfoEntity.getSupportQualList())){
            initQuaType(this.qualTypeList1);
        }else if("2".equals(this.unitType) && !CollectionUtils.isEmpty(zwOrginfoEntity.getSupportQualList())){
            initQuaType(this.qualTypeList2);
        }

        //初始化年份列表
        Integer year=DateUtils.getYearInt();
        yearList = new ArrayList<>();
        for(int k = 0; k < 10; k++){
            yearList.add(year-k);
        }
        super.initOrgTypeAndTechType();
    }

    @Override
    public Integer getOrgInfoType() {
        return 4;
    }

    @Override
    public Class getZwIntellReportClass() {
        return TdZwSupportOrgZzsb.class;
    }
    /**
     *  <p>方法描述：科技攻关能力</p>
     * @MethodAuthor hsj 2023-10-26 16:23
     */

    @Override
    public void initSupportTrcInfo() {
        super.supportTrc = new TdZwSupportTrc();
        //6、科技攻关能力
        super.supportTrc = super.zwIntellReportServiceImpl.findAllSupportTrc(super.zwOrginfoEntity.getRid());
    }
    @Override
    public void initResearchInfo() {
        this.supportPositive=new TdZwSupportPositive();
        this.supportPositive.setFkByPatentId(new TsSimpleCode());
        this.supportPositive.setFkByStandardId(new TsSimpleCode());
        this.supportPositive.setPsnSelVO(new SupportResearchPsnSelVO());

        super.supportResearchPsnSelVO = new SupportResearchPsnSelVO();
        super.supportThesis = new TdZwSupportThesis();
        super.supportThesis.setPsnSelVO(new SupportResearchPsnSelVO());
        super.supportThesis.setFkByPublicationId(new TsSimpleCode());
        //课题信息初始化
        super.supportTopic = new TdZwSupportTopic();
        super.supportTopic.setPsnSelVO(new SupportResearchPsnSelVO());
        super.supportTopic.setFkByPrizeLevelId(new TsSimpleCode());
        super.supportTopics1 = new LinkedList<>();
        super.supportTopics2 = new LinkedList<>();
        super.supportTopics3 = new LinkedList<>();
        super.supportAdvance = new TdZwSupportAdvance();
        super.advanceList = new ArrayList<>();
        //课题情况
        if (CollectionUtils.isEmpty(super.prizeLeveList)) {
            super.prizeLeveList = commService.findNumSimpleCodesByTypeId("5131");
            if(!CollectionUtils.isEmpty(prizeLeveList)){
                for (TsSimpleCode simpleCode : super.prizeLeveList) {
                    this.publicationMap.put(simpleCode.getRid(), simpleCode);
                }
            }
        }
        if (CollectionUtils.isEmpty(super.publicationList)) {
            super.publicationList = commService.findNumSimpleCodesByTypeId("5130");
        }
        for (TsSimpleCode simpleCode : super.publicationList) {
            this.publicationMap.put(simpleCode.getRid(), simpleCode);
        }
        super.thesisList = new ArrayList<>();
        super.supportMomograph = new TdZwSupportMomograph();
        super.supportMomograph.setPsnSelVO1(new SupportResearchPsnSelVO());
        super.supportMomograph.setPsnSelVO2(new SupportResearchPsnSelVO());
        super.supportMomograph.setPsnSelVO3(new SupportResearchPsnSelVO());
        super.monographList = new ArrayList<>();

        this.patentTypeMap=new HashMap<>();
        //专利类型 码表初始化
        if (CollectionUtils.isEmpty(this.patentTypelist)) {
            this.patentTypelist =commService.findNumSimpleCodesByTypeId("5132");
            if(!CollectionUtils.isEmpty(patentTypelist)){
                for (TsSimpleCode tsSimpleCode : patentTypelist) {
                    patentTypeMap.put(tsSimpleCode.getRid(),tsSimpleCode);
                }
            }
        }
        this.standardTypeMap=new HashMap<>();
        //标准等级
        if (CollectionUtils.isEmpty(this.standardTypelist)) {
            this.standardTypelist =commService.findNumSimpleCodesByTypeId("5133");
            if(!CollectionUtils.isEmpty(standardTypelist)){
                for (TsSimpleCode tsSimpleCode : standardTypelist) {
                    standardTypeMap.put(tsSimpleCode.getRid(),tsSimpleCode);
                }
            }
        }
        if (super.zwOrginfoEntity == null || super.zwOrginfoEntity.getRid() == null) {
            return;
        }
        Integer mainRid = super.zwOrginfoEntity.getRid();
        super.thesisList = super.zwIntellReportServiceImpl.findAllSupportThesis(mainRid);
        for (TdZwSupportThesis thesis : super.thesisList) {
            if (!CollectionUtils.isEmpty(thesis.getThesisPsnList())) {
                List<String> authorList = new ArrayList<>();
                for (TdZwSupportThesisPsns psn : thesis.getThesisPsnList()) {
                    authorList.add(psn.getFkByEmpId().getEmpName());
                }
                thesis.setAuthor(StringUtils.list2string(authorList, "、"));
            }
        }
        super.monographList = super.zwIntellReportServiceImpl.findAllSupportMomograph(mainRid);
        for (TdZwSupportMomograph momograph : super.monographList) {
            if (!CollectionUtils.isEmpty(momograph.getMomographPsnList())) {
                List<String> psnNameList1 = new ArrayList<>();
                List<String> psnNameList2 = new ArrayList<>();
                List<String> psnNameList3 = new ArrayList<>();
                for (TdZwSupportMomographPsns psn : momograph.getMomographPsnList()) {
                    switch (psn.getEmpType()) {
                        case 1:
                            psnNameList1.add(psn.getFkByEmpId().getEmpName());
                            break;
                        case 2:
                            psnNameList2.add(psn.getFkByEmpId().getEmpName());
                            break;
                        case 3:
                            psnNameList3.add(psn.getFkByEmpId().getEmpName());
                            break;
                        default:
                            break;
                    }
                }
                momograph.setPsnName1(StringUtils.list2string(psnNameList1, "、"));
                momograph.setPsnName2(StringUtils.list2string(psnNameList2, "、"));
                momograph.setPsnName3(StringUtils.list2string(psnNameList3, "、"));
            }
        }
        List<TdZwSupportTopic> supportTopics =super.zwIntellReportServiceImpl.findAllSupportTopics(mainRid);
        if(!CollectionUtils.isEmpty(supportTopics)){
            //责任人
            for (TdZwSupportTopic supportTopic : supportTopics) {
                if (!CollectionUtils.isEmpty(supportTopic.getSupportTopicPsns())) {
                    List<String> responsibleList = new ArrayList<>();
                    for (TdZwSupportTopicPsns psn : supportTopic.getSupportTopicPsns()) {
                        responsibleList.add(psn.getFkByEmpId().getEmpName());
                    }
                    supportTopic.setResponsible(StringUtils.list2string(responsibleList, "、"));
                }
                //等级
                if(supportTopic.getFkByPrizeLevelId() == null ){
                    supportTopic.setFkByPrizeLevelId(new TsSimpleCode());
                }
            }
            //根据topicType分组
            Map<Integer,List<TdZwSupportTopic>> supportTopicMap = new LinkedHashMap<>();
            GroupUtil.listGroup2Map(supportTopics,supportTopicMap, TdZwSupportTopic.class,"getTopicType");
            this.supportTopics1 = supportTopicMap.containsKey(1)  ? supportTopicMap.get(1) : new ArrayList<TdZwSupportTopic>();
            this.supportTopics2 = supportTopicMap.containsKey(2) ? supportTopicMap.get(2) : new ArrayList<TdZwSupportTopic>();
            this.supportTopics3 = supportTopicMap.containsKey(3) ? supportTopicMap.get(3) : new ArrayList<TdZwSupportTopic>();
        }
        //专利列表
        this.patentList=findPatentList(mainRid,1);
        //标准列表
        this.standardList=findPatentList(mainRid,2);
        //先进适宜技术筛选推广应用
        super.advanceList = super.zwIntellReportServiceImpl.findAllSupportAdvance(mainRid);
    }

    /**
     * 查询专利列表
     * @param mainRid
     */
    private List<Object[]> findPatentList(Integer mainRid,Integer flag) {
        return this.zwIntellReportServiceImpl.findpPatentListByMainId(mainRid,flag);
    }

    /**
     * 支撑机构-科研课题信息-人员选择弹出框-打开操作
     */
    @Override
    public void openSupportResearchPsnSelectAction() {
        initAllIncumbencyPsnList();
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("mainForm:tabView:psnDataTable");
        dataTable.setRows(10);
        dataTable.setFirst(calFirstPageDataTable(10));
        RequestContext.getCurrentInstance().update("mainForm:tabView:searchPsnName");
        RequestContext.getCurrentInstance().update("mainForm:tabView:psnDataTable");
        RequestContext.getCurrentInstance().execute("PF('SupportResearchPsnSelDialog').show()");
    }

    public void initAllIncumbencyPsnList() {
        super.supportResearchPsnSelVO.setSearchPsnName("");
        findAllIncumbencyPsnList();
        super.supportResearchPsnSelVO.setSelectedPsnList(new ArrayList<Object[]>());
        super.supportResearchPsnSelVO.setSelectedRowRid(new ArrayList<Integer>());
        if (!CollectionUtils.isEmpty(super.supportResearchPsnSelVO.getPsnRidList())) {
            for (Integer rid : super.supportResearchPsnSelVO.getPsnRidList()) {
                Object[] objects = super.supportResearchPsnSelVO.getAllPsnMap().get(rid);
                if (objects == null) {
                    continue;
                }
                super.supportResearchPsnSelVO.getSelectedPsnList().add(objects);
                super.supportResearchPsnSelVO.getSelectedRowRid().add(rid);
            }
        }
    }

    private void findAllIncumbencyPsnList() {
        super.supportResearchPsnSelVO.setAllIncumbencyPsnList(
                super.zwIntellReportServiceImpl.findAllIncumbencyPsnList(
                        super.zwOrginfoEntity.getRid(), super.supportResearchPsnSelVO.getSearchPsnName()
                )
        );
        if (!CollectionUtils.isEmpty(super.supportResearchPsnSelVO.getAllIncumbencyPsnList())) {
            for (Object[] objects : super.supportResearchPsnSelVO.getAllIncumbencyPsnList()) {
                Integer recoveryInstRid = ObjectUtil.convert(Integer.class, objects[0]);
                if (recoveryInstRid == null) {
                    continue;
                }
                super.supportResearchPsnSelVO.getAllPsnMap().put(recoveryInstRid, objects);
            }
        }
    }

    /**
     * 计算人员弹出框打开时的页码
     *
     * @return 页码
     */
    private int calFirstPageDataTable(int rows) {
        if (CollectionUtils.isEmpty(super.supportResearchPsnSelVO.getSelectedRowRid())
                || CollectionUtils.isEmpty(super.supportResearchPsnSelVO.getAllIncumbencyPsnList())) {
            return 0;
        }
        Integer firstPsnRid = super.supportResearchPsnSelVO.getSelectedRowRid().get(0);
        if (firstPsnRid == null) {
            return 0;
        }
        List<Object[]> allIncumbencyPsnList = super.supportResearchPsnSelVO.getAllIncumbencyPsnList();
        for (int i = 0, allIncumbencyPsnListSize = allIncumbencyPsnList.size(); i < allIncumbencyPsnListSize; i++) {
            Object[] o = allIncumbencyPsnList.get(i);
            if (firstPsnRid.equals(ObjectUtil.convert(Integer.class, o[0]))) {
                return i / rows * rows;
            }
        }
        return 0;
    }

    /**
     * 支撑机构-科研课题信息-人员选择弹出框-确定操作
     */
    @Override
    public void saveSupportResearchPsnSelectAction() {
        if (CollectionUtils.isEmpty(super.supportResearchPsnSelVO.getSelectedRowRid())) {
            JsfUtil.addErrorMessage("请选择人员！");
            return;
        } else {
            List<Object[]> psnObjectList = new ArrayList<>();
            for (Integer psnRid : super.supportResearchPsnSelVO.getSelectedRowRid()) {
                psnObjectList.add(super.supportResearchPsnSelVO.getAllPsnMap().get(psnRid));
            }
            Collections.sort(psnObjectList, new Comparator<Object[]>() {
                @Override
                public int compare(Object[] o1, Object[] o2) {
                    if (o1 == null || o1[5] == null) {
                        return -1;
                    }
                    Integer order1 = ObjectUtil.convert(Integer.class, o1[5]);
                    if (order1 == null) {
                        return -1;
                    }
                    if (o2 == null || o2[5] == null) {
                        return 1;
                    }
                    Integer order2 = ObjectUtil.convert(Integer.class, o2[5]);
                    if (order2 == null) {
                        return 1;
                    }
                    return order1.compareTo(order2);
                }
            });
            List<String> psnNameList = new ArrayList<>();
            super.supportResearchPsnSelVO.setPsnRidList(new ArrayList<Integer>());
            for (Object[] psn : psnObjectList) {
                psnNameList.add(StringUtils.objectToString(psn[1]));
                super.supportResearchPsnSelVO.getPsnRidList().add(ObjectUtil.convert(Integer.class, psn[0]));
            }
            super.supportResearchPsnSelVO.setSelPsnName(StringUtils.list2string(psnNameList, "、"));
        }

        RequestContext.getCurrentInstance().update(super.psnInputId);
        RequestContext.getCurrentInstance().execute("PF('SupportResearchPsnSelDialog').hide()");
    }

    /**
     * 支撑机构-科研课题信息-人员选择弹出框-查询操作
     */
    @Override
    public void searchSupportResearchPsnSelectAction() {
        findAllIncumbencyPsnList();
        super.supportResearchPsnSelVO.setSelectedPsnList(new ArrayList<Object[]>());
        if (!CollectionUtils.isEmpty(super.supportResearchPsnSelVO.getSelectedRowRid())) {
            for (Integer rid : super.supportResearchPsnSelVO.getSelectedRowRid()) {
                Object[] objects = super.supportResearchPsnSelVO.getAllPsnMap().get(rid);
                if (objects == null) {
                    continue;
                }
                super.supportResearchPsnSelVO.getSelectedPsnList().add(objects);
            }
        }
    }

    /**
     * 支撑机构-科研课题信息-人员选择弹出框-选择操作
     */
    @Override
    public void rowSelectSupportResearchPsnSelectListener(SelectEvent event) {
        Object[] objArr = (Object[]) event.getObject();
        dealSelectRowRid(objArr);
    }

    /**
     * 支撑机构-科研课题信息-人员选择弹出框-取消选择操作
     */
    @Override
    public void rowUnselectSupportResearchPsnSelectListener(UnselectEvent event) {
        Object[] objArr = (Object[]) event.getObject();
        if (null != objArr && null != objArr[0]) {
            Integer curSelectRowRid = Integer.parseInt(objArr[0].toString());
            super.supportResearchPsnSelVO.getSelectedRowRid().remove(curSelectRowRid);
        }
    }

    /**
     * 支撑机构-科研课题信息-人员选择弹出框-切换选择操作
     */
    @Override
    public void toggleSelectSupportResearchPsnSelectListener(ToggleSelectEvent event) {
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("mainForm:tabView:psnDataTable");
        if (CollectionUtils.isEmpty(super.supportResearchPsnSelVO.getAllIncumbencyPsnList())) {
            return;
        }
        if (event.isSelected()) {
            //当前datatable选中的项
            List<Object[]> list = CollectionUtil.castList(Object[].class, dataTable.getSelection());
            for (Object[] objArr : list) {
                dealSelectRowRid(objArr);
            }
        } else {
            //获取当前页码
            int current = dataTable.getPage();
            //每页条数
            int rows = dataTable.getRows();
            //当前页第一个元素下标
            int curFirst = current * rows;
            //当前页最后一个元素的下标（取相对小的）
            int curLast = Math.min(curFirst + rows - 1, super.supportResearchPsnSelVO.getAllIncumbencyPsnList().size() - 1);
            //遍历当前表格数据，将当前页的数据移除
            for (int i = curFirst; i <= curLast; i++) {
                Object[] objArr = super.supportResearchPsnSelVO.getAllIncumbencyPsnList().get(i);
                if (null != objArr && null != objArr[0]) {
                    Integer curSelectUnitRid = Integer.parseInt(objArr[0].toString());
                    super.supportResearchPsnSelVO.getSelectedRowRid().remove(curSelectUnitRid);
                }
            }
        }
    }

    /**
     * 记录当前选择行RID
     *
     * @param objArr 当前选择行
     */
    private void dealSelectRowRid(Object[] objArr) {
        Integer instRid = null != objArr && null != objArr[0] ? Integer.parseInt(objArr[0].toString()) : null;
        if (null != instRid && !super.supportResearchPsnSelVO.getSelectedRowRid().contains(instRid)) {
            super.supportResearchPsnSelVO.getSelectedRowRid().add(Integer.parseInt(objArr[0].toString()));
        }
    }

    /**
     * 支撑机构-科研课题信息-人员选择弹出框-清空操作
     */
    @Override
    public void emptySupportResearchPsnSelectAction() {
        super.supportResearchPsnSelVO.init();
    }

    /**
     * 支撑机构-科研课题信息-材料上传弹出框-打开弹出框
     */
    @Override
    public void openSupportResearchFileUploadAction() {
        RequestContext.getCurrentInstance().execute("PF('FileDialog').show()");
    }

    /**
     * 支撑机构-科研课题信息-材料上传弹出框-上传
     */
    @Override
    public void uploadSupportResearchFileAction(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            try {
                String fileName = file.getFileName();
                String contentType = file.getContentType();
                String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String uuid = java.util.UUID.randomUUID().toString().replaceAll("-", "");
                String path = JsfUtil.getAbsolutePath();
                String relativePath = "heth/zzsb/supportOrg/" + uuid + fileName.substring(fileName.lastIndexOf("."));
                // 文件路径
                String filePath = path + relativePath;
                FileUtils.copyFile(filePath, file.getInputstream());
                switch (super.fileOptType) {
                    case 1:
                        super.supportThesis.setSupportFilePath(relativePath);
                        break;
                    case 2:
                        super.supportMomograph.setSupportFilePath(relativePath);
                        break;
                    case 3:
                    case 4:
                        this.supportPositive.setSupportFilePath(relativePath);
                        break;
                    case 5:
                        //课题情况
                        this.supportTopic.setSupportFilePath(relativePath);
                        break;
                    default:
                        break;
                }
                RequestContext.getCurrentInstance().update("mainForm:tabView:researchFilePanel" + super.fileOptType);
                RequestContext.getCurrentInstance().execute("PF('FileDialog').hide();");
                JsfUtil.addSuccessMessage("上传成功！");
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("上传失败！");
            }
        }
    }

    /**
     * 支撑机构-科研课题信息-材料上传弹出框-删除
     */
    @Override
    public void deleteSupportResearchFileAction() {
        try {
            switch (super.fileOptType) {
                case 1:
                    super.supportThesis.setSupportFilePath("");
                    break;
                case 2:
                    super.supportMomograph.setSupportFilePath("");
                    break;
                case 3:
                case 4:
                    super.supportPositive.setSupportFilePath(null);
                    break;
                case 5:
                    super.supportTopic.setSupportFilePath(null);
                    break;
                default:
                    break;
            }
            RequestContext.getCurrentInstance().update("mainForm:tabView:researchFilePanel" + super.fileOptType);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * 支撑机构-科研课题信息-论文情况-添加操作
     */
    @Override
    public void addSupportThesisAction() {
        super.supportThesis = new TdZwSupportThesis();
        super.supportThesis.setFkByMainId(new TdZwSupportOrgZzsb(super.zwOrginfoEntity.getRid()));
        super.supportThesis.setPsnSelVO(new SupportResearchPsnSelVO());
        super.supportThesis.setFkByPublicationId(new TsSimpleCode());
        RequestContext.getCurrentInstance().update("mainForm:tabView:supportResearchThesisDialogPanel");
        RequestContext.getCurrentInstance().execute("PF('SupportResearchThesisDialog').show()");
    }

    /**
     * 支撑机构-科研课题信息-论文情况-修改操作
     */
    @Override
    public void editSupportThesisAction(TdZwSupportThesis supportThesis) {
        List<TdZwSupportThesis> tempList = new ArrayList<>();
        tempList.add(supportThesis);
        super.supportThesis = ObjectCopyUtil.deepCopy(tempList).get(0);
        super.supportThesis.setPsnSelVO(new SupportResearchPsnSelVO());
        super.supportThesis.getPsnSelVO().setSelPsnName(super.supportThesis.getAuthor());
        if (super.supportThesis.getFkByPublicationId() == null
                || super.supportThesis.getFkByPublicationId().getRid() == null) {
            super.supportThesis.setFkByPublicationId(new TsSimpleCode());
        }
        if (!CollectionUtils.isEmpty(super.supportThesis.getThesisPsnList())) {
            for (TdZwSupportThesisPsns psn : super.supportThesis.getThesisPsnList()) {
                if (psn.getFkByEmpId() == null) {
                    continue;
                }
                super.supportThesis.getPsnSelVO().getPsnRidList().add(psn.getFkByEmpId().getRid());
                super.supportThesis.getPsnSelVO().getSelectedRowRid().add(psn.getFkByEmpId().getRid());
            }
        }
        RequestContext.getCurrentInstance().update("mainForm:tabView:supportResearchThesisDialogPanel");
        RequestContext.getCurrentInstance().execute("PF('SupportResearchThesisDialog').show()");
    }

    /**
     * 支撑机构-科研课题信息-论文情况-保存操作
     */
    @Override
    public void saveSupportThesisAction() {
        boolean verifyFail = false;
        if (CollectionUtils.isEmpty(super.supportThesis.getPsnSelVO().getPsnRidList())) {
            verifyFail = true;
            JsfUtil.addErrorMessage("请选择作者！");
        }
        if (super.supportThesis.getFkByPublicationId() == null
                || super.supportThesis.getFkByPublicationId().getRid() == null) {
            verifyFail = true;
            JsfUtil.addErrorMessage("请选择刊物类别！");
        }
        if (StringUtils.isBlank(super.supportThesis.getThesisName())) {
            verifyFail = true;
            JsfUtil.addErrorMessage("论文名称不能为空！");
        } else if (super.supportThesis.getThesisName().length() > 200) {
            verifyFail = true;
            JsfUtil.addErrorMessage("论文名称长度不能超过200！");
        }
        if (StringUtils.isBlank(super.supportThesis.getPublicationName())) {
            verifyFail = true;
            JsfUtil.addErrorMessage("刊物名称不能为空！");
        } else if (super.supportThesis.getPublicationName().length() > 200) {
            verifyFail = true;
            JsfUtil.addErrorMessage("刊物名称长度不能超过200！");
        }
        if (StringUtils.isBlank(super.supportThesis.getSupportFilePath())) {
            verifyFail = true;
            JsfUtil.addErrorMessage("请上传支撑材料！");
        }
        if (verifyFail) {
            return;
        }
        try {
            boolean isAdd = super.supportThesis.getRid() == null;
            super.zwIntellReportServiceImpl.saveSupportThesis(super.supportThesis);
            super.supportThesis.setAuthor(super.supportThesis.getPsnSelVO().getSelPsnName());
            if (super.supportThesis.getFkByPublicationId() != null
                    && super.supportThesis.getFkByPublicationId().getRid() != null) {
                super.supportThesis.setFkByPublicationId(this.publicationMap.get(super.supportThesis.getFkByPublicationId().getRid()));
            }
            if (isAdd) {
                super.thesisList.add(0, super.supportThesis);
            } else {
                super.thesisList.set(super.supportOptIndex, super.supportThesis);
            }
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().update("mainForm:tabView:thesisListTable");
            RequestContext.getCurrentInstance().execute("PF('SupportResearchThesisDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * 支撑机构-科研课题信息-论文情况-删除操作
     */
    @Override
    public void delSupportThesisAction(Integer rid) {
        if (rid == null) {
            return;
        }
        try {
            super.zwIntellReportServiceImpl.delSupportThesis(rid);
            super.thesisList.remove(super.supportOptIndex);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * 支撑机构-科研课题信息-专著情况-添加操作
     */
    @Override
    public void addSupportMonographAction() {
        super.supportMomograph = new TdZwSupportMomograph();
        super.supportMomograph.setFkByMainId(new TdZwSupportOrgZzsb(super.zwOrginfoEntity.getRid()));
        super.supportMomograph.setPsnSelVO1(new SupportResearchPsnSelVO());
        super.supportMomograph.setPsnSelVO2(new SupportResearchPsnSelVO());
        super.supportMomograph.setPsnSelVO3(new SupportResearchPsnSelVO());
        RequestContext.getCurrentInstance().update("mainForm:tabView:supportResearchMonographDialogPanel");
        RequestContext.getCurrentInstance().execute("PF('SupportResearchMonographDialog').show()");
    }

    /**
     * 支撑机构-科研课题信息-专著情况-修改操作
     */
    @Override
    public void editSupportMonographAction(TdZwSupportMomograph supportMomograph) {
        List<TdZwSupportMomograph> tempList = new ArrayList<>();
        tempList.add(supportMomograph);
        super.supportMomograph = ObjectCopyUtil.deepCopy(tempList).get(0);
        super.supportMomograph.setPsnSelVO1(new SupportResearchPsnSelVO());
        super.supportMomograph.getPsnSelVO1().setSelPsnName(super.supportMomograph.getPsnName1());
        super.supportMomograph.setPsnSelVO2(new SupportResearchPsnSelVO());
        super.supportMomograph.getPsnSelVO2().setSelPsnName(super.supportMomograph.getPsnName2());
        super.supportMomograph.setPsnSelVO3(new SupportResearchPsnSelVO());
        super.supportMomograph.getPsnSelVO3().setSelPsnName(super.supportMomograph.getPsnName3());
        if (!CollectionUtils.isEmpty(super.supportMomograph.getMomographPsnList())) {
            for (TdZwSupportMomographPsns psn : super.supportMomograph.getMomographPsnList()) {
                if (psn.getFkByEmpId() == null) {
                    continue;
                }
                switch (psn.getEmpType()) {
                    case 1:
                        super.supportMomograph.getPsnSelVO1().getPsnRidList().add(psn.getFkByEmpId().getRid());
                        super.supportMomograph.getPsnSelVO1().getSelectedRowRid().add(psn.getFkByEmpId().getRid());
                        break;
                    case 2:
                        super.supportMomograph.getPsnSelVO2().getPsnRidList().add(psn.getFkByEmpId().getRid());
                        super.supportMomograph.getPsnSelVO2().getSelectedRowRid().add(psn.getFkByEmpId().getRid());
                        break;
                    case 3:
                        super.supportMomograph.getPsnSelVO3().getPsnRidList().add(psn.getFkByEmpId().getRid());
                        super.supportMomograph.getPsnSelVO3().getSelectedRowRid().add(psn.getFkByEmpId().getRid());
                        break;
                    default:
                        break;
                }
            }
        }
        RequestContext.getCurrentInstance().update("mainForm:tabView:supportResearchMonographDialogPanel");
        RequestContext.getCurrentInstance().execute("PF('SupportResearchMonographDialog').show()");
    }

    /**
     * 支撑机构-科研课题信息-专著情况-保存操作
     */
    @Override
    public void saveSupportMonographAction() {
        boolean verifyFail = false;
        if (StringUtils.isBlank(super.supportMomograph.getMomographName())) {
            verifyFail = true;
            JsfUtil.addErrorMessage("论著名称不能为空！");
        } else if (super.supportMomograph.getMomographName().length() > 200) {
            verifyFail = true;
            JsfUtil.addErrorMessage("论著名称长度不能超过200！");
        }
        if (StringUtils.isBlank(super.supportMomograph.getPressName())) {
            verifyFail = true;
            JsfUtil.addErrorMessage("出版社名称不能为空！");
        } else if (super.supportMomograph.getPressName().length() > 200) {
            verifyFail = true;
            JsfUtil.addErrorMessage("出版社名称长度不能超过200！");
        }
        Set<Integer> psnRidSet = new HashSet<>();
        boolean repeatPsn = false;
        boolean emptyPsn = true;
        if (!CollectionUtils.isEmpty(super.supportMomograph.getPsnSelVO1().getPsnRidList())) {
            emptyPsn = false;
            for (Integer psnRid : super.supportMomograph.getPsnSelVO1().getPsnRidList()) {
                if (psnRid == null) {
                    continue;
                }
                if (psnRidSet.contains(psnRid)) {
                    repeatPsn = true;
                    break;
                }
                psnRidSet.add(psnRid);
            }
        }
        if (!CollectionUtils.isEmpty(super.supportMomograph.getPsnSelVO2().getPsnRidList())) {
            emptyPsn = false;
            for (Integer psnRid : super.supportMomograph.getPsnSelVO2().getPsnRidList()) {
                if (psnRid == null) {
                    continue;
                }
                if (psnRidSet.contains(psnRid)) {
                    repeatPsn = true;
                    break;
                }
                psnRidSet.add(psnRid);
            }
        }
        if (!CollectionUtils.isEmpty(super.supportMomograph.getPsnSelVO3().getPsnRidList())) {
            emptyPsn = false;
            for (Integer psnRid : super.supportMomograph.getPsnSelVO3().getPsnRidList()) {
                if (psnRid == null) {
                    continue;
                }
                if (psnRidSet.contains(psnRid)) {
                    repeatPsn = true;
                    break;
                }
                psnRidSet.add(psnRid);
            }
        }
        if (emptyPsn) {
            verifyFail = true;
            JsfUtil.addErrorMessage("主编、副主编、参编不能同时为空！");
        }
        if (repeatPsn) {
            verifyFail = true;
            JsfUtil.addErrorMessage("同一人员不能同时为主编、副主编、参编！");
        }
        if (StringUtils.isBlank(super.supportMomograph.getSupportFilePath())) {
            verifyFail = true;
            JsfUtil.addErrorMessage("请上传支撑材料！");
        }
        if (verifyFail) {
            return;
        }
        try {
            boolean isAdd = super.supportMomograph.getRid() == null;
            super.zwIntellReportServiceImpl.saveSupportMomograph(super.supportMomograph);
            super.supportMomograph.setPsnName1(super.supportMomograph.getPsnSelVO1().getSelPsnName());
            super.supportMomograph.setPsnName2(super.supportMomograph.getPsnSelVO2().getSelPsnName());
            super.supportMomograph.setPsnName3(super.supportMomograph.getPsnSelVO3().getSelPsnName());
            if (isAdd) {
                super.monographList.add(0, super.supportMomograph);
            } else {
                super.monographList.set(super.supportOptIndex, super.supportMomograph);
            }
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().update("mainForm:tabView:monographListTable");
            RequestContext.getCurrentInstance().execute("PF('SupportResearchMonographDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * 支撑机构-科研课题信息-专著情况-删除操作
     */
    @Override
    public void delSupportMonographAction(Integer rid) {
        if (rid == null) {
            return;
        }
        try {
            super.zwIntellReportServiceImpl.delSupportMomograph(rid);
            super.monographList.remove(super.supportOptIndex);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述： 学术培训类别相关初始化 </p>
     * @MethodAuthor： pw 2023/3/30
     **/
    private void initSupportTrainInfo(){
        //真删 真存储 不需要主表级联
        //初始化支撑机构学术培训类别
        List<TsSimpleCode> academicTrainTypeList = this.commService.findNumSimpleCodesByTypeId("5134");
        if(CollectionUtils.isEmpty(academicTrainTypeList)){
            return;
        }
        this.showAcademicList.clear();
        //组合列表数据用 学术培训类别
        List<TsSimpleCode> logicAcademicList = new ArrayList<>();
        Map<String, List<TsSimpleCode>> tmpMap = new LinkedHashMap<>();
        for(TsSimpleCode simpleCode : academicTrainTypeList){
            String fatherNo  = simpleCode.getCodeLevelNo().split("\\.")[0];
            List<TsSimpleCode> tmpList = tmpMap.get(fatherNo);
            if(null == tmpList){
                tmpList = new ArrayList<>();
            }
            tmpList.add(simpleCode);
            tmpMap.put(fatherNo, tmpList);
        }
        for(Map.Entry<String, List<TsSimpleCode>> mapEntity : tmpMap.entrySet()){
            List<TsSimpleCode> tmpList = new ArrayList<>();
            tmpList.addAll(mapEntity.getValue());
            //排除无父级的
            if(tmpList.get(0).getCodeLevelNo().contains(".")){
                continue;
            }
            if(tmpList.size() > 1){
                //拥有父级子级的 去除父级
                tmpList.remove(0);
            }
            this.showAcademicList.add(mapEntity.getValue());
            logicAcademicList.addAll(tmpList);
        }
        //初始查询 一次查询
        List<Object[]> supportTrainList = this.zwIntellReportServiceImpl.findTdZwSupportTrain(this.zwOrginfoEntity.getRid());
        this.showSupportTrainList.clear();
        //学术类别按rid 不按codeNo匹配
        //key 年份 subKey TdZwSupportTrain.fkByTrainId.rid
        Map<Integer,Map<Integer, Object[]>> yearMap = new LinkedHashMap<>();
        if(!CollectionUtils.isEmpty(supportTrainList)){
            for(Object[] trainArr : supportTrainList){
                Integer year = null == trainArr[0] ? null : Integer.parseInt(trainArr[0].toString());
                if(null == year){
                    continue;
                }
                Map<Integer, Object[]> typeMap = yearMap.get(year);
                if(null == typeMap){
                    typeMap = new HashMap<>();
                }
                Integer type = null == trainArr[1] ? null : Integer.parseInt(trainArr[1].toString());
                if(null == type){
                    continue;
                }
                typeMap.put(type, trainArr);
                yearMap.put(year, typeMap);
            }
        }
        if(!CollectionUtils.isEmpty(yearMap)){
            for(Integer year : yearMap.keySet()){
                Map<Integer, Object[]> trainMap = yearMap.get(year);
                List<TdZwSupportTrain> trainList = new ArrayList<>();
                for(TsSimpleCode simpleCode : logicAcademicList){
                    Object[] trainArr = trainMap.get(simpleCode.getRid());
                    TdZwSupportTrain train = new TdZwSupportTrain();
                    train.setFkByTrainId(simpleCode);
                    train.setYear(year);
                    if(null != trainArr){
                        train.setPartakeNumber(null == trainArr[2] ? null : Integer.parseInt(trainArr[2].toString()));
                        train.setEvidenceFilePath(null == trainArr[3] ? null : trainArr[3].toString());
                    }
                    trainList.add(train);
                }
                this.showSupportTrainList.add(trainList);
            }
        }
        this.academicDataSize = logicAcademicList.size();

    }

    private void initQuaType(List<TsSimpleCode> qualTypeList) {
        for (TsSimpleCode tsSimpleCode : qualTypeList) {
            for (TdZwSupportQual supportQual : zwOrginfoEntity.getSupportQualList()) {
                if(supportQual.getFkByTypeId().getRid().equals(tsSimpleCode.getRid())){
                    tsSimpleCode.setIfSelected(true);
                }
            }
        }
    }

    @Override
    public void addSupportTrain() {
        this.trainYear = DateUtils.getYear(new Date());
        if(!CollectionUtils.isEmpty(this.showSupportTrainList)){
            Integer nearYear = this.showSupportTrainList.get(0).get(0).getYear();
            if(nearYear.compareTo(this.trainYear) == 0){
                JsfUtil.addErrorMessage("当前年份记录已存在，不允许重复添加！");
                return;
            }
        }
        if(CollectionUtils.isEmpty(this.showAcademicList)){
            return;
        }
        this.addOrModSupportTrainInit(null);
    }

    @Override
    public void modSupportTrain() {
        if(null == this.trainYear){
            return;
        }
        if(CollectionUtils.isEmpty(this.showAcademicList)){
            return;
        }
        Map<Integer, TdZwSupportTrain> trainMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(this.showSupportTrainList)){
            for(List<TdZwSupportTrain> trainList : this.showSupportTrainList){
                Integer year = trainList.get(0).getYear();
                if(year.compareTo(this.trainYear) == 0){
                    for(TdZwSupportTrain train : trainList){
                        if(null == train.getPartakeNumber()){
                            continue;
                        }
                        trainMap.put(train.getFkByTrainId().getRid(), train);
                    }
                    break;
                }
            }
        }
        this.addOrModSupportTrainInit(trainMap);
    }

    @Override
    public void saveOrUpdateSupportTrain() {
        if(CollectionUtils.isEmpty(this.supportTrainDiagDataList)){
            return;
        }
        boolean flag = false;
        List<TdZwSupportTrain> saveList = new ArrayList<>();
        StringBuffer msgBuffer = new StringBuffer();
        for(SupportTrainPO trainPO : this.supportTrainDiagDataList){
            TsSimpleCode simpleCode = trainPO.getSimpleCode();
            for(TdZwSupportTrain train : trainPO.getTrainList()){
                msgBuffer.setLength(0);
                msgBuffer.append(simpleCode.getCodeName());
                if(simpleCode.getRid().compareTo(train.getFkByTrainId().getRid()) != 0){
                    msgBuffer.append(train.getFkByTrainId().getCodeName());
                }
                Integer partakeNumber = train.getPartakeNumber();
                String evidenceFilePath = train.getEvidenceFilePath();
                if(null == partakeNumber){
                    JsfUtil.addErrorMessage(msgBuffer.toString()+"参与人数不能为空！");
                    flag = true;
                }
                if(null != partakeNumber && partakeNumber > 0 && StringUtils.isBlank(evidenceFilePath)){
                    JsfUtil.addErrorMessage(msgBuffer.toString()+"培训证明材料不能为空！");
                    flag = true;
                }
                if(!flag){
                    saveList.add(train);
                }
            }
        }
        if(flag){
            return;
        }
        try{
            this.zwIntellReportServiceImpl.saveTdZwSupportTrain(saveList);
            //更新
            this.updateShowSupportTrain(saveList);
            JsfUtil.addSuccessMessage("保存成功！");
            //更新页面
            RequestContext.getCurrentInstance().update("mainForm:tabView:supportTrainDataId");
            //关闭弹框
            RequestContext.getCurrentInstance().execute("PF('SupportTrainDialog').hide();");
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }

    }

    @Override
    public void delSupportTrain() {
        if(null == this.trainYear){
            return;
        }
        try{
            this.zwIntellReportServiceImpl.delTdZwSupportTrain(this.trainYear, this.zwOrginfoEntity.getRid());
            List<TdZwSupportTrain> removeList = null;
            if(!CollectionUtils.isEmpty(this.showSupportTrainList)){
                for(List<TdZwSupportTrain> trainList : this.showSupportTrainList){
                    Integer year = trainList.get(0).getYear();
                    if(year.compareTo(this.trainYear) == 0){
                        removeList = trainList;
                        break;
                    }
                }
            }
            if(null != removeList){
                this.showSupportTrainList.remove(removeList);
            }
            JsfUtil.addSuccessMessage("删除成功！");
        }catch(Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    @Override
    public void removeSupportTrainFilePath() {
        if(null == this.editSupportTrain){
            return;
        }
        this.editSupportTrain.setEvidenceFilePath(null);
    }

    @Override
    public void handleSupportTrainFileUpload(FileUploadEvent event) {
        if (null != event) {
            UploadedFile file = event.getFile();
            String fileName = file.getFileName();
            String contentType = file.getContentType().toLowerCase();
            try {
                String errorMsg = FileUtils.veryFile(file.getInputstream(), contentType, fileName, "3");
                if (StringUtils.isNotBlank(errorMsg)) {
                    JsfUtil.addErrorMessage(errorMsg);
                    return;
                }
                String uuid = UUID.randomUUID().toString().replaceAll("-", "");
                String hz = fileName.substring(fileName.lastIndexOf(".") + 1);
                String filePath = this.xnPath + "heth/zworgannex/" + uuid + "." + hz;
                String showDir = "/heth/zworgannex/" + uuid + "." + hz;
                this.editSupportTrain.setEvidenceFilePath(showDir);
                FileUtils.copyFile(filePath, file.getInputstream());
                JsfUtil.addSuccessMessage("上传成功！");
                RequestContext.getCurrentInstance().execute("SupportTrainFileDiag.hide();");
            } catch (IOException e) {
                FacesMessage msg = new FacesMessage("上传失败", file.getFileName() + "上传失败！");
                FacesContext.getCurrentInstance().addMessage("上传失败", msg);
                e.printStackTrace();
            }
        }
    }

    /**
     * <p>方法描述：更新学术培训信息显示列表 </p>
     * @MethodAuthor： pw 2023/3/30
     **/
    private void updateShowSupportTrain(List<TdZwSupportTrain> saveList){
        if(CollectionUtils.isEmpty(this.showSupportTrainList)){
            this.showSupportTrainList.add(saveList);
            return;
        }
        int curYear = DateUtils.getYear(new Date());
        Integer saveYear = saveList.get(0).getYear();
        Integer showYear = this.showSupportTrainList.get(0).get(0).getYear();
        //新增的当年数据 直接加入集合
        if(curYear == saveYear && saveYear.compareTo(showYear) != 0){
            this.showSupportTrainList.add(0, saveList);
            return;
        }
        //修改的数据 找到对应集合替换元素
        if(!CollectionUtils.isEmpty(this.showSupportTrainList)){
            for(List<TdZwSupportTrain> trainList : this.showSupportTrainList){
                Integer year = trainList.get(0).getYear();
                if(year.compareTo(saveYear) == 0){
                    trainList.clear();
                    trainList.addAll(saveList);
                    break;
                }
            }
        }
    }

    /**
     * <p>方法描述：学术培训添加、修改初始化 </p>
     * @MethodAuthor： pw 2023/3/30
     **/
    private void addOrModSupportTrainInit(Map<Integer, TdZwSupportTrain> trainMap){
        this.supportTrainDiagDataList.clear();
        TdZwSupportOrgZzsb orgZzsb = new TdZwSupportOrgZzsb(this.zwOrginfoEntity.getRid());
        for(List<TsSimpleCode> simpleCodeList : this.showAcademicList){
            TsSimpleCode fatherSimpleCode = simpleCodeList.get(0);
            int size = simpleCodeList.size();
            SupportTrainPO trainPO = new SupportTrainPO();
            //只有大类 没有子类
            trainPO.setRowSpan(1 == size ? 1 : size - 1);
            trainPO.setSimpleCode(fatherSimpleCode);
            trainPO.setTrainList(new ArrayList<TdZwSupportTrain>());
            for(TsSimpleCode simpleCode : simpleCodeList){
                if(1 == size || simpleCode.getRid().compareTo(fatherSimpleCode.getRid()) != 0){
                    TdZwSupportTrain supportTrain = CollectionUtils.isEmpty(trainMap) ? null :
                            trainMap.get(simpleCode.getRid());
                    Integer partakeNumber = null;
                    String evidenceFilePath = null;
                    if(null != supportTrain){
                        partakeNumber = supportTrain.getPartakeNumber();
                        evidenceFilePath = supportTrain.getEvidenceFilePath();
                    }
                    supportTrain = new TdZwSupportTrain();
                    supportTrain.setFkByTrainId(simpleCode);
                    supportTrain.setYear(this.trainYear);
                    supportTrain.setFkByMainId(orgZzsb);
                    supportTrain.setPartakeNumber(partakeNumber);
                    supportTrain.setEvidenceFilePath(evidenceFilePath);
                    trainPO.getTrainList().add(supportTrain);
                }
            }
            this.supportTrainDiagDataList.add(trainPO);
        }
        //更新弹框信息 弹出框
        RequestContext.getCurrentInstance().execute("SupportTrainDialog.show();");
        RequestContext.getCurrentInstance().update("mainForm:supportTrainDialog");
    }

    /**
     * 科研成果情况-专利
     */
    @Override
    public void addPatentInfoAction() {
        supportPositive=new TdZwSupportPositive();
        supportPositive.setFkByPatentId(new TsSimpleCode());
        supportPositive.setFkByStandardId(new TsSimpleCode());
        supportPositive.setPsnSelVO(new SupportResearchPsnSelVO());
        this.isShowPatentType=false;
        RequestContext.getCurrentInstance().execute("PF('AddPatentInfoDialog').show()");
    }

    /**
     * 专利change事件
     */
    @Override
    public void changePatentType() {
        if(supportPositive==null || supportPositive.getFkByPatentId()==null || supportPositive.getFkByPatentId().getRid()==null){
            return;
        }
        if(patentTypeMap.containsKey(supportPositive.getFkByPatentId().getRid())
                && "1".equals(patentTypeMap.get(supportPositive.getFkByPatentId().getRid()).getExtendS1())){
            isShowPatentType=true;
        }else{
            isShowPatentType=false;
            supportPositive.setOtherPatentType(null);
        }
    }


    /**
     * 专利信息-保存
     */
    @Override
    public void savePatentInfoAction(){
        if(this.supportPositive==null){
            return;
        }
        boolean bool=false;
        //完成人
        if(supportPositive.getPsnSelVO()==null || StringUtils.isBlank(supportPositive.getPsnSelVO().getSelPsnName())){
            JsfUtil.addErrorMessage("请选择完成人！");
            bool=true;
        }
        //专利名称
        if(StringUtils.isBlank(supportPositive.getPatentName())){
            JsfUtil.addErrorMessage("专利名称不能为空！");
            bool=true;
        }
        if(StringUtils.isNotBlank(supportPositive.getPatentName()) && supportPositive.getPatentName().length()>200){
            JsfUtil.addErrorMessage("专利名称长度不能超过200！");
            bool=true;
        }
        //专利类型
        if(supportPositive.getFkByPatentId()==null || supportPositive.getFkByPatentId().getRid()==null){
            JsfUtil.addErrorMessage("请选择专利类型！");
            bool=true;
        }
        if(supportPositive.getFkByPatentId()!=null && supportPositive.getFkByPatentId().getRid()!=null
                && patentTypeMap.containsKey(supportPositive.getFkByPatentId().getRid())
                && "1".equals(patentTypeMap.get(supportPositive.getFkByPatentId().getRid()).getExtendS1()) ){
            if(StringUtils.isBlank(supportPositive.getOtherPatentType())){
                JsfUtil.addErrorMessage("其他专利类型不能为空！");
                bool=true;
            }
            if(StringUtils.isNotBlank(supportPositive.getOtherPatentType()) && supportPositive.getOtherPatentType().length()>50){
                JsfUtil.addErrorMessage("其他专利类型长度不能超过50！");
                bool=true;
            }
        }
        //专利号
        if(StringUtils.isBlank(supportPositive.getPatentNo())){
            JsfUtil.addErrorMessage("专利号不能为空！");
            bool=true;
        }
        if(StringUtils.isNotBlank(supportPositive.getPatentNo()) && supportPositive.getPatentNo().length()>100){
            JsfUtil.addErrorMessage("专利号长度不能超过100！");
            bool=true;
        }
        //支撑附件
        if(StringUtils.isBlank(supportPositive.getSupportFilePath())){
            JsfUtil.addErrorMessage("请上传支撑材料！");
            bool=true;
        }
        if(bool){
            return;
        }
        try{
            supportPositive.setFkByMainId(new TdZwSupportOrgZzsb(this.zwOrginfoEntity.getRid()));
            supportPositive.setPositiveType(1);
            supportPositive.setFkByStandardId(null);
            supportPositive.setYear(Integer.parseInt(DateUtils.getYear()));
            zwIntellReportServiceImpl.saveSupportPositive(supportPositive);
            if(supportPositive.getFkByPatentId()==null){
                supportPositive.setFkByPatentId(new TsSimpleCode());
            }
            if(supportPositive.getFkByStandardId()==null){
                supportPositive.setFkByStandardId(new TsSimpleCode());
            }
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().execute("PF('AddPatentInfoDialog').hide()");
            //重查列表 并刷新
            this.patentList=findPatentList(zwOrginfoEntity.getRid(),1);
            RequestContext.getCurrentInstance().update("mainForm:tabView:patentListTable");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    /**
     * 标准信息-保存
     */
    @Override
    public void saveStandardInfoAction() {
        if(this.supportPositive==null){
            return;
        }
        boolean bool=false;
        //标准主要起草人
        if(supportPositive.getPsnSelVO()==null || StringUtils.isBlank(supportPositive.getPsnSelVO().getSelPsnName())){
            JsfUtil.addErrorMessage("请选择标准主要起草人！");
            bool=true;
        }

        //标准名称
        if(StringUtils.isBlank(supportPositive.getPatentName())){
            JsfUtil.addErrorMessage("标准名称不能为空！");
            bool=true;
        }
        if(StringUtils.isNotBlank(supportPositive.getPatentName()) && supportPositive.getPatentName().length()>200){
            JsfUtil.addErrorMessage("标准名称长度不能超过200！");
            bool=true;
        }
        //标准编号
        if(StringUtils.isBlank(supportPositive.getPatentNo())){
            JsfUtil.addErrorMessage("标准编号不能为空！");
            bool=true;
        }
        if(StringUtils.isNotBlank(supportPositive.getPatentNo()) && supportPositive.getPatentNo().length()>100){
            JsfUtil.addErrorMessage("标准编号长度不能超过100！");
            bool=true;
        }
        //标准级别
        if(supportPositive.getFkByStandardId()==null || supportPositive.getFkByStandardId().getRid()==null){
            JsfUtil.addErrorMessage("请选择标准级别！");
            bool=true;
        }
        //支撑附件
        if(StringUtils.isBlank(supportPositive.getSupportFilePath())){
            JsfUtil.addErrorMessage("请上传支撑材料！");
            bool=true;
        }
        if(bool){
            return;
        }
        try{
            supportPositive.setFkByMainId(new TdZwSupportOrgZzsb(this.zwOrginfoEntity.getRid()));
            supportPositive.setPositiveType(2);
            supportPositive.setFkByPatentId(null);
            supportPositive.setYear(Integer.parseInt(DateUtils.getYear()));
            zwIntellReportServiceImpl.saveSupportPositive(supportPositive);
            if(supportPositive.getFkByPatentId()==null){
                supportPositive.setFkByPatentId(new TsSimpleCode());
            }
            if(supportPositive.getFkByStandardId()==null){
                supportPositive.setFkByStandardId(new TsSimpleCode());
            }
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().execute("PF('AddStandardInfoDialog').hide()");
            //重查列表 并刷新
            this.standardList=findPatentList(zwOrginfoEntity.getRid(),2);
            RequestContext.getCurrentInstance().update("mainForm:tabView:standardListTable");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }

    }


    /**
     * 专利信息-删除
     */
    @Override
    public void delPatentInfoAction(Integer rid) {
        if (rid == null) {
            return;
        }
        try {
            this.zwIntellReportServiceImpl.delPatentInfoById(rid);
            this.patentList=findPatentList(zwOrginfoEntity.getRid(),1);
            RequestContext.getCurrentInstance().update("mainForm:tabView:patentListTable");
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }
    /**
     * 标准信息-删除
     */
    @Override
    public void delStandardInfoAction(Integer rid) {
        if (rid == null) {
            return;
        }
        try {
            this.zwIntellReportServiceImpl.delPatentInfoById(rid);
            this.standardList=findPatentList(zwOrginfoEntity.getRid(),2);
            RequestContext.getCurrentInstance().update("mainForm:tabView:standardListTable");
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }


    /**
     *专利信息-编辑
     * @param rid
     */
    @Override
    public void editPatentInfoAction(Integer rid) {
        this.supportPositive=zwIntellReportServiceImpl.findAllPatentInfoByRid(rid);
        if(supportPositive.getFkByPatentId()==null){
            supportPositive.setFkByPatentId(new TsSimpleCode());
        }else {
            if(supportPositive.getFkByPatentId().getRid()!=null && "1".equals(supportPositive.getFkByPatentId().getExtendS1())){
                this.isShowPatentType=true;
            }else{
                this.isShowPatentType=false;
            }
        }
        if(supportPositive.getFkByStandardId()==null){
            supportPositive.setFkByStandardId(new TsSimpleCode());
        }
        SupportResearchPsnSelVO psnSelVO=new SupportResearchPsnSelVO();
        String psnName="";
        List<Integer> rids=new ArrayList<>();
        if(supportPositive!=null && !CollectionUtils.isEmpty(supportPositive.getPositivePsnList())){
            for (TdZwSupportPositivePsns positivePsns : supportPositive.getPositivePsnList()) {
                psnName+="、"+positivePsns.getFkByEmpId().getEmpName();
                rids.add(positivePsns.getFkByEmpId().getRid());
            }
            psnSelVO.setSelPsnName(psnName.substring(1));
            psnSelVO.setPsnRidList(rids);
        }
        supportPositive.setPsnSelVO(psnSelVO);

        if(supportPositive.getPositiveType()==1){
            RequestContext.getCurrentInstance().execute("PF('AddPatentInfoDialog').show()");
            RequestContext.getCurrentInstance().update("mainForm:tabView:addPatentInfoDialog");
        }else{
            RequestContext.getCurrentInstance().execute("PF('AddStandardInfoDialog').show()");
            RequestContext.getCurrentInstance().update("mainForm:tabView:addStandardInfoDialog");
        }

    }

    @Override
    public void addStandardInfoAction() {
        supportPositive=new TdZwSupportPositive();
        supportPositive.setFkByPatentId(new TsSimpleCode());
        supportPositive.setFkByStandardId(new TsSimpleCode());
        supportPositive.setPsnSelVO(new SupportResearchPsnSelVO());
        RequestContext.getCurrentInstance().execute("PF('AddStandardInfoDialog').show()");
    }

    /**
     *  <p>方法描述：课题情况添加</p>
     * @MethodAuthor hsj 2023-04-01 15:30
     */
    @Override
    public void addSupportTopicAction(Integer type) {
        super.supportTopic = new TdZwSupportTopic();
        super.supportTopic.setFkByMainId(new TdZwSupportOrgZzsb(super.zwOrginfoEntity.getRid()));
        super.supportTopic.setTopicType(type);
        super.supportTopic.setPsnSelVO(new SupportResearchPsnSelVO());
        super.supportTopic.setFkByPrizeLevelId(new TsSimpleCode());
        RequestContext.getCurrentInstance().update("mainForm:tabView:supportTopicDialog");
        RequestContext.getCurrentInstance().execute("PF('SupportTopicDialog').show()");
    }
    /**
     * 支撑机构-科研课题信息-课题情况-修改操作
     */
    @Override
    public void editSupportTopicAction(TdZwSupportTopic supportTopic) {
        List<TdZwSupportTopic> tempList = new ArrayList<>();
        tempList.add(supportTopic);
        super.supportTopic = ObjectCopyUtil.deepCopy(tempList).get(0);
        super.supportTopic.setPsnSelVO(new SupportResearchPsnSelVO());
        super.supportTopic.getPsnSelVO().setSelPsnName(super.supportTopic.getResponsible());
        if (!CollectionUtils.isEmpty(super.supportTopic.getSupportTopicPsns())) {
            for (TdZwSupportTopicPsns psn : super.supportTopic.getSupportTopicPsns()) {
                if (psn.getFkByEmpId() == null) {
                    continue;
                }
                super.supportTopic.getPsnSelVO().getPsnRidList().add(psn.getFkByEmpId().getRid());
                super.supportTopic.getPsnSelVO().getSelectedRowRid().add(psn.getFkByEmpId().getRid());
            }
        }
        RequestContext.getCurrentInstance().update("mainForm:tabView:supportTopicDialog");
        RequestContext.getCurrentInstance().execute("PF('SupportTopicDialog').show()");
    }
    /**
     * 支撑机构-科研课题信息-课题情况-删除操作
     */
    @Override
    public void delSupportTopicAction(Integer rid,Integer type) {
        if (ObjectUtil.isNull(rid)|| ObjectUtil.isNull(type)) {
            return;
        }
        try {
            super.zwIntellReportServiceImpl.delSupportTopic(rid);
            if(type == 1){
                super.supportTopics1.remove(super.supportOptIndex);
            }else if(type == 2){
                super.supportTopics2.remove(super.supportOptIndex);
            }else {
                super.supportTopics3.remove(super.supportOptIndex);
            }

            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }
    /**
     * 课题信息-保存
     */
    @Override
    public void saveSupportTopicAction(){
        if(this.supportTopic==null ){
            return;
        }
        boolean verifyFail=false;
        //责任人
        if(supportTopic.getPsnSelVO() == null || CollectionUtils.isEmpty(supportTopic.getPsnSelVO().getPsnRidList())){
            JsfUtil.addErrorMessage("请选择责任人！");
            verifyFail=true;
        }
        //课题名称
        if(StringUtils.isBlank(supportTopic.getTopicName())){
            JsfUtil.addErrorMessage(supportTopic.getTopicType() == 3 ? "项目名称不能为空！" :"课题名称不能为空！");
            verifyFail=true;
        }
        if(StringUtils.isNotBlank(supportTopic.getTopicName()) && supportTopic.getTopicName().length()>200){
            JsfUtil.addErrorMessage("课题名称长度不能超过200！");
            verifyFail=true;
        }
        if(supportTopic.getTopicType() == 3){
            if(StringUtils.isBlank(supportTopic.getPrizeName())){
                JsfUtil.addErrorMessage("获奖名称不能为空！");
                verifyFail=true;
            }
            if(StringUtils.isNotBlank(supportTopic.getPrizeName()) && supportTopic.getPrizeName().length()>100){
                JsfUtil.addErrorMessage("获奖名称长度不能超过100！");
                verifyFail=true;
            }
        }
        //立项批准部门
        if(StringUtils.isBlank(supportTopic.getAwardDepartment())){
            JsfUtil.addErrorMessage(supportTopic.getTopicType() == 3 ? "授奖部门不能为空！" :"立项批准部门不能为空！");
            verifyFail=true;
        }
        if(StringUtils.isNotBlank(supportTopic.getAwardDepartment()) && supportTopic.getAwardDepartment().length()>100){
            JsfUtil.addErrorMessage(supportTopic.getTopicType() == 3 ? "授奖部门不能为空！" :"立项批准部门"+"长度不能超过100！");
            verifyFail=true;
        }
        if(ObjectUtil.isNull(supportTopic.getFkByPrizeLevelId().getRid())){
            JsfUtil.addErrorMessage("请选择等级！");
            verifyFail=true;
        }
        //支撑附件
        if(StringUtils.isBlank(supportTopic.getSupportFilePath())){
            JsfUtil.addErrorMessage("请上传支撑材料！");
            verifyFail=true;
        }
        if (verifyFail) {
            return;
        }
        try {
            boolean isAdd = super.supportTopic.getRid() == null;
            supportTopic.setFkByPrizeLevelId(publicationMap.get(supportTopic.getFkByPrizeLevelId().getRid()));
            super.zwIntellReportServiceImpl.saveSupportTopic(super.supportTopic);
            super.supportTopic.setResponsible(super.supportTopic.getPsnSelVO().getSelPsnName());
            if (isAdd) {
                if(supportTopic.getTopicType() == 1){
                    super.supportTopics1.add(0, super.supportTopic);
                }else if(supportTopic.getTopicType() == 2){
                    super.supportTopics2.add(0, super.supportTopic);
                }else {
                    super.supportTopics3.add(0, super.supportTopic);
                }

            } else {
                if(supportTopic.getTopicType() == 1){
                    super.supportTopics1.set(super.supportOptIndex, super.supportTopic);
                }else if(supportTopic.getTopicType() == 2){
                    super.supportTopics2.set(super.supportOptIndex, super.supportTopic);
                }else {
                    super.supportTopics3.set(super.supportOptIndex, super.supportTopic);
                }
            }
            JsfUtil.addSuccessMessage("保存成功！");
            RequestContext.getCurrentInstance().update("mainForm:tabView:supportTopic"+supportTopic.getTopicType());
            RequestContext.getCurrentInstance().execute("PF('SupportTopicDialog').hide()");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }
    /**
     * 支撑机构-先进适宜技术筛选推广应用-添加操作
     */
    @Override
    public void addSupportAdvanceAction() {
        super.supportAdvance = new TdZwSupportAdvance();
        super.supportAdvance.setFkByMainId(new TdZwSupportOrgZzsb(super.zwOrginfoEntity.getRid()));
        super.advanceList.add(0, super.supportAdvance);
        RequestContext.getCurrentInstance().update("mainForm:tabView:advanceListTable");
    }



    /**
     * 支撑机构-先进适宜技术筛选推广应用-删除操作
     */
    @Override
    public void delSupportAdvanceAction(Integer rid) {
        try {
            super.advanceList.remove(super.supportOptIndex);
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * <p>方法描述：保存与提交逻辑校验 </p>
     * pw 2023/10/26
     **/
    @Override
    protected boolean veryOtherBeforeSave() {
        return false;
    }

    /**
     *  <p>方法描述：支撑机构必填验证</p>
     * @MethodAuthor hsj 2023-10-25 17:04
     */
    @Override
    protected boolean supportSubmitValidate() {
        Boolean isFlag = Boolean.FALSE;
        //能力建设信息校验
        if (this.validateAbility()) {
            isFlag =  Boolean.TRUE;
        }
        //科技攻关能力
        if(verifySupportTrc()){
            isFlag =  Boolean.TRUE;
        }
        //先进适宜技术筛选推广应用
        if(verifySupportAdvance()){
            isFlag =  Boolean.TRUE;
        }
        return isFlag;
    }

    /**
     *  <p>方法描述：科技攻关能力验证</p>
     * @MethodAuthor hsj 2023-10-26 10:40
     */
    private boolean verifySupportTrc() {
        Boolean isFlag = Boolean.FALSE;
        //省部级技术研究中心
        if(ObjectUtil.isNull(super.supportTrc.getIfTrc())){
            isFlag = Boolean.TRUE;
            JsfUtil.addErrorMessage("科技攻关能力-请选择省部级技术研究中心！");
        }
        //职业健康人才培训基地
        if(ObjectUtil.isNull(super.supportTrc.getIfTjTrain())){
            isFlag = Boolean.TRUE;
            JsfUtil.addErrorMessage("科技攻关能力-请选择职业健康人才培训基地！");
        }else {
            if(super.supportTrc.getIfTjTrain().compareTo(1) == 0 && ObjectUtil.isNull(super.supportTrc.getTjTrainNum())){
                isFlag = Boolean.TRUE;
                JsfUtil.addErrorMessage("科技攻关能力-培训人员数量（人）不能为空！");
            }
        }
        if(ObjectUtil.isNull(super.supportTrc.getIfTtb())){
            isFlag = Boolean.TRUE;
            JsfUtil.addErrorMessage("科技攻关能力-请选择职业健康技术转化基地！");
        }else {
            if(super.supportTrc.getIfTtb().compareTo(1) == 0 && ObjectUtil.isNull(super.supportTrc.getTranNum())){
                isFlag = Boolean.TRUE;
                JsfUtil.addErrorMessage("科技攻关能力-转化成果（项）不能为空！");
            }
        }
        if(ObjectUtil.isNull(super.supportTrc.getIfExpose())){
            isFlag = Boolean.TRUE;
            JsfUtil.addErrorMessage("科技攻关能力-请选择职业暴露人群生物标本库平台！");
        }
        if(ObjectUtil.isNull(super.supportTrc.getIfDiagTrain())){
            isFlag = Boolean.TRUE;
            JsfUtil.addErrorMessage("科技攻关能力-请选择职业病诊断救治人才培训基地！");
        }else {
            if(super.supportTrc.getIfDiagTrain().compareTo(1) == 0 && ObjectUtil.isNull(super.supportTrc.getDiagNum())){
                isFlag = Boolean.TRUE;
                JsfUtil.addErrorMessage("科技攻关能力-培训人员数量（人）不能为空！");
            }
        }
        if(ObjectUtil.isNull(super.supportTrc.getIfDiagTrc())){
            isFlag = Boolean.TRUE;
            JsfUtil.addErrorMessage("科技攻关能力-请选择职业病诊断救治技术研究中心！");
        }
        if(ObjectUtil.isNull(super.supportTrc.getIfScienGuid())){
            isFlag = Boolean.TRUE;
            JsfUtil.addErrorMessage("科技攻关能力-请选择基层科研技术指导！");
        }else {
            if(super.supportTrc.getIfScienGuid().compareTo(1) == 0 && ObjectUtil.isNull(super.supportTrc.getGuidNum())){
                isFlag = Boolean.TRUE;
                JsfUtil.addErrorMessage("科技攻关能力-开展基层技术指导（次）不能为空！");
            }
            if(super.supportTrc.getIfScienGuid().compareTo(1) == 0 && ObjectUtil.isNull(super.supportTrc.getScienTrainNum())){
                isFlag = Boolean.TRUE;
                JsfUtil.addErrorMessage("科技攻关能力-培训（人）不能为空！");
            }
        }
        if(ObjectUtil.isNull(super.supportTrc.getIfUniversity())){
            isFlag = Boolean.TRUE;
            JsfUtil.addErrorMessage("科技攻关能力-请选择高校科研实践基地！");
        }else {
            if(super.supportTrc.getIfUniversity().compareTo(1) == 0 && ObjectUtil.isNull(super.supportTrc.getUniversityNum())){
                isFlag = Boolean.TRUE;
                JsfUtil.addErrorMessage("科技攻关能力-高校实习（人）不能为空！");
            }
        }
        return isFlag;
    }

    /**
     * <p>方法描述：能力建设信息初始化 </p>
     * pw 2023/10/25
     **/
    public void initAbility () {
        this.abilitySaveList = new ArrayList<>();
        this.abilityShowList = new LinkedList<>();
        this.abilitySimpleCodeList = this.commService.findSimpleCodeListOrderByNumLevelNo("5608", (null != this.zwOrginfoEntity.getState() && 1 == this.zwOrginfoEntity.getState()));
        this.supportHealth = this.zwIntellReportServiceImpl.findSupportHealthByMainId(this.zwOrginfoEntity.getRid());
        if (null == this.supportHealth) {
            this.supportHealth = new TdZwSupportHealth();
            this.supportHealth.setFkByMainId((TdZwSupportOrgZzsb) this.zwOrginfoEntity);
        }
        List<Integer> abilityList = this.zwIntellReportServiceImpl.findAbilitysByMainId(this.zwOrginfoEntity.getRid());
        Set<Integer> abilitySet = new HashSet<>();
        if (!CollectionUtils.isEmpty(abilityList)) {
            for (Integer ability : abilityList) {
                abilitySet.add(ability);
            }
        }
        this.ifAbilityValidateKf = false;
        this.initSupportInstAbilityVOList(this.abilitySaveList, this.abilityShowList, this.abilitySimpleCodeList, abilitySet, true);
        this.fillChildSpec();
    }

    /**
     *  <p>方法描述：先进适宜技术筛选推广应用验证</p>
     * @MethodAuthor hsj 2023-10-25 17:06
     */
    private boolean verifySupportAdvance() {
        Boolean isFlag = Boolean.FALSE;
        if(CollectionUtils.isEmpty(super.advanceList)){
            return isFlag;
        }
        int i = 1;
        for(TdZwSupportAdvance advance : super.advanceList){
            if (StringUtils.isBlank(advance.getAdvanceName())) {
                isFlag = Boolean.TRUE;
                JsfUtil.addErrorMessage("科研课题-先进适宜技术筛选推广应用第"+i+"行名称不能为空！");
            } else if (advance.getAdvanceName().length() > 100) {
                isFlag = Boolean.TRUE;
                JsfUtil.addErrorMessage("科研课题-先进适宜技术筛选推广应用第"+i+"行名称长度不能超过100！");
            }
            i++;
        }
        return isFlag;
    }


    /**
     * 初始化-支撑机构-仪器设备配置信息
     */
    public void initSupportInst() {
        super.allSupportInstVOList = new ArrayList<>();
        super.supportInstVOList = new LinkedList<>();
        super.supportInstSimpleCodeList = this.commService.findSimpleCodeListOrderByNumLevelNo("5607");
        Set<Integer> zwSupportInstIdList = super.zwIntellReportServiceImpl.findZwSupportInstIdList(super.zwOrginfoEntity.getRid());
        initSupportInstAbilityVOList(super.allSupportInstVOList, super.supportInstVOList, super.supportInstSimpleCodeList, zwSupportInstIdList, false);
    }

    @Override
    public void afterSubmitInit() {
        this.initSupportInst();
        this.initAbility();
        this.afterSubmitFieldset();
    }

    @Override
    public void preExecuteCancel() {
        this.preSubmitFieldset();
    }

    /**
     * <p>方法描述：提交后 Fieldset展开与闭合的处理 </p>
     * pw 2023/10/27
     **/
    private void afterSubmitFieldset () {
        if (!CollectionUtils.isEmpty(this.abilityShowList) && null != this.abilityCloseFieldList) {
            int size = this.abilityShowList.size();
            for (int i=0;i<size;i++) {
                SupportInstAbilityVO abilityVO = this.abilityShowList.get(i);
                Integer simpleRid = null == abilityVO.getSimpleCode() ? null : abilityVO.getSimpleCode().getRid();
                if (null == simpleRid) {
                    continue;
                }
                Fieldset fieldset = (Fieldset)FacesContext.getCurrentInstance().getViewRoot().findComponent("mainForm:tabView:fieldCapacity"+i);
                if (null != fieldset) {
                    fieldset.setCollapsed(this.abilityCloseFieldList.contains(simpleRid));
                }
            }
        }
        if (!CollectionUtils.isEmpty(this.supportInstVOList) && null != this.supportCloseFieldList) {
            int size = this.supportInstVOList.size();
            for (int i=0; i<size; i++) {
                SupportInstAbilityVO abilityVO = this.supportInstVOList.get(i);
                Integer simpleRid = null == abilityVO.getSimpleCode() ? null : abilityVO.getSimpleCode().getRid();
                if (null == simpleRid) {
                    continue;
                }
                Fieldset fieldset = (Fieldset)FacesContext.getCurrentInstance().getViewRoot().findComponent("mainForm:tabView:fieldSupport"+i);
                if (null != fieldset) {
                    if (fieldset.isCollapsed()) {
                        fieldset.setCollapsed(this.supportCloseFieldList.contains(simpleRid));
                    }
                }
            }
        }
    }

    /**
     * <p>方法描述：提交前 记录闭合的Fieldset相关信息 </p>
     * pw 2023/10/27
     **/
    private void preSubmitFieldset () {
        this.abilityCloseFieldList = new ArrayList<>();
        this.supportCloseFieldList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(this.abilityShowList)) {
            int size = this.abilityShowList.size();
            for (int i=0; i<size; i++) {
                SupportInstAbilityVO abilityVO = this.abilityShowList.get(i);
                Integer simpleRid = null == abilityVO.getSimpleCode() ? null : abilityVO.getSimpleCode().getRid();
                if (null == simpleRid) {
                    continue;
                }
                Fieldset fieldset = (Fieldset)FacesContext.getCurrentInstance().getViewRoot().findComponent("mainForm:tabView:fieldCapacity"+i);
                if (null != fieldset) {
                    if (fieldset.isCollapsed()) {
                        this.abilityCloseFieldList.add(simpleRid);
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(this.supportInstVOList)) {
            int size = this.supportInstVOList.size();
            for (int i=0; i<size; i++) {
                SupportInstAbilityVO abilityVO = this.supportInstVOList.get(i);
                Integer simpleRid = null == abilityVO.getSimpleCode() ? null : abilityVO.getSimpleCode().getRid();
                if (null == simpleRid) {
                    continue;
                }
                Fieldset fieldset = (Fieldset)FacesContext.getCurrentInstance().getViewRoot().findComponent("mainForm:tabView:fieldSupport"+i);
                if (null != fieldset) {
                    if (fieldset.isCollapsed()) {
                        this.supportCloseFieldList.add(simpleRid);
                    }
                }
            }
        }
    }

    /**
     * 封装多级码表页面VO
     *
     * @param allSupportInstAbilityVOList 用于直接记录所有码表VO（便于保存）
     * @param supportInstAbilityVOList    层级记录码表VO
     * @param simpleCodeList              码表数据
     * @param zwSupportInstIdList         机构已有的码表rid
     * @param ifAbility                   是否能力建设
     */
    private void initSupportInstAbilityVOList(List<SupportInstAbilityVO> allSupportInstAbilityVOList,
                                              List<SupportInstAbilityVO> supportInstAbilityVOList,
                                              List<TsSimpleCode> simpleCodeList,
                                              Set<Integer> zwSupportInstIdList, boolean ifAbility) {
        if (ObjectUtil.isEmpty(simpleCodeList)) {
            return;
        }
        Map<String, SupportInstAbilityVO> supportInstAbilityVOMap = new HashMap<>();
        for (TsSimpleCode simpleCode : simpleCodeList) {
            Integer rid = simpleCode.getRid();
            String codeLevelNo = simpleCode.getCodeLevelNo();
            if (ObjectUtil.isEmpty(rid) || ObjectUtil.isEmpty(codeLevelNo)) {
                continue;
            }
            String extendS3 = StringUtils.objectToString(simpleCode.getExtendS3());
            if (StringUtils.isNotBlank(extendS3) && !extendS3.equals(this.unitType)) {
                continue;
            }
            SupportInstAbilityVO supportInstAbilityVO = new SupportInstAbilityVO(simpleCode);
            supportInstAbilityVO.setSel(zwSupportInstIdList.contains(rid));
            allSupportInstAbilityVOList.add(supportInstAbilityVO);
            boolean firstNode = !codeLevelNo.contains(".");
            String key;
            //第一级节点直接添加
            if (firstNode) {
                if (supportInstAbilityVOMap.containsKey(codeLevelNo)) {
                    continue;
                }
                supportInstAbilityVOMap.put(codeLevelNo, supportInstAbilityVO);
                supportInstAbilityVOList.add(supportInstAbilityVO);
                continue;
            }
            //非第一级节点需要找到父级
            key = codeLevelNo.substring(0, codeLevelNo.lastIndexOf("."));
            if (supportInstAbilityVOMap.containsKey(codeLevelNo) || !supportInstAbilityVOMap.containsKey(key)) {
                continue;
            }
            supportInstAbilityVOMap.put(codeLevelNo, supportInstAbilityVO);
            SupportInstAbilityVO parentVO = supportInstAbilityVOMap.get(key);
            parentVO.addChildList(supportInstAbilityVO);
        }
        //详情页需要移除未选择的
        if ((new Short("1")).equals(super.zwOrginfoEntity.getState())) {
            removeUnSelSupportInstAbilityVOList(supportInstAbilityVOList, ifAbility);
        }
        //处理序号
        for (SupportInstAbilityVO supportInstAbilityVO1 : supportInstAbilityVOList) {
            initSupportInstAbilityVOListNum(supportInstAbilityVO1);
        }
    }

    /**
     * 移除未选择的
     *
     * @param supportInstAbilityVOList 页面VO
     */
    private void removeUnSelSupportInstAbilityVOList(List<SupportInstAbilityVO> supportInstAbilityVOList, boolean ifAbility) {
        Iterator<SupportInstAbilityVO> iterator = supportInstAbilityVOList.iterator();
        while (iterator.hasNext()) {
            SupportInstAbilityVO i = iterator.next();
            if (ObjectUtil.isNotEmpty(i.getChildList())) {
                removeUnSelSupportInstAbilityVOList(i.getChildList(), ifAbility);
            }
            if (ifAbility) {
                String ext1 = i.getSimpleCode().getExtendS1();
                if ("1".equals(ext1) || "2".equals(ext1)) {
                    continue;
                }
            }
            if (i.getSel() || ObjectUtil.isNotEmpty(i.getChildList())) {
                continue;
            }
            iterator.remove();
        }
    }

    /**
     * 处理序号
     *
     * @param supportInstAbilityVO1 一级结构
     */
    private void initSupportInstAbilityVOListNum(SupportInstAbilityVO supportInstAbilityVO1) {
        List<SupportInstAbilityVO> childList = supportInstAbilityVO1.getChildList();
        //二级结构要么都可选要么都不可选
        boolean onlyLevel2 = ObjectUtil.isNotEmpty(supportInstAbilityVO1.getChildList()) &&
                ObjectUtil.isEmpty(supportInstAbilityVO1.getChildList().get(0).getChildList());
        supportInstAbilityVO1.setOnlyLevel2(onlyLevel2);
        int num = 0;
        for (int i = 0, childListSize = childList.size(); i < childListSize; i++) {
            SupportInstAbilityVO supportInstAbilityVO2 = childList.get(i);
            //最大只有二级结构时二级结构需要序号
            if (onlyLevel2) {
                supportInstAbilityVO2.setNum(StringUtils.objectToString(i + 1));
                continue;
            }
            for (int ii = 0; ii < supportInstAbilityVO2.getChildList().size(); ii++) {
                num++;
                SupportInstAbilityVO supportInstAbilityVO3 = supportInstAbilityVO2.getChildList().get(ii);
                supportInstAbilityVO3.setNum(StringUtils.objectToString(num));
                for (int i1 = 0; i1 < supportInstAbilityVO3.getChildList().size(); i1++) {
                    SupportInstAbilityVO supportInstAbilityVO4 = supportInstAbilityVO3.getChildList().get(i1);
                    String num1 = num + "." + StringUtils.objectToString(i1 + 1);
                    supportInstAbilityVO4.setNum(num1);
                }
            }
        }
    }

    /**
     * 保存支撑机构前操作
     */
    public void saveSupportBeforeAction(TdZwSupportOrgZzsb supportOrg) {
        this.preSubmitFieldset();
        //能力建设信息
        this.fillAbilityBeforeSave(supportOrg);
        //仪器设备配置信息
        supportOrg.setSupportInstSimpleCodeRidList(new ArrayList<Integer>());
        if (!ObjectUtil.isEmpty(this.allSupportInstVOList)) {
            for (SupportInstAbilityVO supportInstAbilityVO : this.allSupportInstVOList) {
                if (!supportInstAbilityVO.getSel() || ObjectUtil.isEmpty(supportInstAbilityVO.getSimpleCode())) {
                    continue;
                }
                //存在子结构时无需存储
                if (ObjectUtil.isNotEmpty(supportInstAbilityVO.getChildList())) {
                    continue;
                }
                supportOrg.getSupportInstSimpleCodeRidList().add(supportInstAbilityVO.getSimpleCode().getRid());
            }
        }
        //科技攻关能力赋值
        this.dealSupportTrc();
        supportOrg.setSupportTrc(super.supportTrc);
        //先进适宜技术筛选推广应用赋值
        this.dealAdvanceList();
        supportOrg.setAdvanceList(super.advanceList);
    }

    /**
     *  <p>方法描述：先进适宜技术筛选推广应用清空rid</p>
     * @MethodAuthor hsj 2023-10-26 15:41
     */
    private void dealAdvanceList() {
        if(CollectionUtils.isEmpty(super.advanceList)){
            return;
        }
        for(TdZwSupportAdvance advance:super.advanceList){
            advance.setRid(null);
            advance.setCreateDate(new Date());
            advance.setCreateManid(Global.getUser().getRid());
        }
    }
    /**
     * <p>方法描述： 能力建设信息保存前处理 </p>
     * pw 2023/10/26
     **/
    public void fillAbilityBeforeSave (TdZwSupportOrgZzsb supportOrg) {
        if (null == this.supportHealth.getHealthPublic() || 1 != this.supportHealth.getHealthPublic()) {
            this.supportHealth.setPublicNum(null);
            this.supportHealth.setTrainNum(null);
            this.supportHealth.setTrainPsnNum(null);
        }
        if (null == this.supportHealth.getIfPublicRpt() || 1 != this.supportHealth.getIfPublicRpt()) {
            this.supportHealth.setPublicRptNum(null);
        }
        if (null == this.supportHealth.getIfScience() || 1 != this.supportHealth.getIfScience()) {
            this.supportHealth.setVideoNum(null);
            this.supportHealth.setAudioNum(null);
            this.supportHealth.setBooks(null);
            this.supportHealth.setBrochure(null);
            this.supportHealth.setOtherNum(null);
        }
        if (null == this.supportHealth.getIfGuid() || 1 != this.supportHealth.getIfGuid()) {
            this.supportHealth.setGuidCrpts(null);
        }
        if (!this.ifAbilityValidateKf) {
            this.supportHealth.setTotalNum(null);
            this.supportHealth.setCureNum(null);
            this.supportHealth.setBetterNum(null);
            this.supportHealth.setDeathNum(null);
        }
        supportOrg.setSupportHealth(this.supportHealth);
        if (CollectionUtils.isEmpty(this.abilitySaveList)) {
            return;
        }
        List<Integer> curList = new ArrayList<>();
        for (SupportInstAbilityVO abilityVO : this.abilitySaveList) {
            if (!abilityVO.getSel() || ObjectUtil.isEmpty(abilityVO.getSimpleCode())) {
                continue;
            }
            //存在子结构时无需存储
            if (ObjectUtil.isNotEmpty(abilityVO.getChildList())) {
                continue;
            }
            curList.add(abilityVO.getSimpleCode().getRid());
        }
        supportOrg.setAbilityList(curList);
    }

    /**
     *  <p>方法描述：科技攻关能力赋值</p>
     * @MethodAuthor hsj 2023-10-26 15:21
     */
    private void dealSupportTrc() {
        super.supportTrc.setFkByMainId(new TdZwSupportOrgZzsb(super.zwOrginfoEntity.getRid()));
        if(null != super.supportTrc.getIfTjTrain() && super.supportTrc.getIfTjTrain().compareTo(0) == 0){
            super.supportTrc.setTjTrainNum(null);
        }
        if(null != super.supportTrc.getIfTtb() && super.supportTrc.getIfTtb().compareTo(0) == 0){
            super.supportTrc.setTranNum(null);
        }
        if(null != super.supportTrc.getIfDiagTrain() &&  super.supportTrc.getIfDiagTrain().compareTo(0) == 0 ){
            super.supportTrc.setDiagNum(null);
        }
        if(null != super.supportTrc.getIfScienGuid() &&  super.supportTrc.getIfScienGuid().compareTo(0) == 0 ){
            super.supportTrc.setGuidNum(null);
        }
        if(null != super.supportTrc.getIfScienGuid() &&  super.supportTrc.getIfScienGuid().compareTo(0) == 0){
            super.supportTrc.setScienTrainNum(null);
        }
        if(null != super.supportTrc.getIfUniversity() &&  super.supportTrc.getIfUniversity().compareTo(0) == 0 ){
            super.supportTrc.setUniversityNum(null);
        }
    }

    public void openInfoDialog(){
        RequestContext.getCurrentInstance().execute("PF('ConfirmSupportDialog').show()");
    }

    /**
     * <p>方法描述：能力建设信息提交校验 </p>
     * pw 2023/10/25
     **/
    private boolean validateAbility () {
        boolean ifUnPassValidate = false;
        if (null == this.supportHealth.getHealthPublic()) {
            JsfUtil.addErrorMessage("能力建设信息-请选择是否职业健康知识宣传！");
            ifUnPassValidate = true;
        }
        if (null != this.supportHealth.getHealthPublic() && 1 == this.supportHealth.getHealthPublic()) {
            if (null == this.supportHealth.getPublicNum()) {
                JsfUtil.addErrorMessage("能力建设信息-宣传活动（场）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getTrainNum()) {
                JsfUtil.addErrorMessage("能力建设信息-培训活动（场）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getTrainPsnNum()) {
                JsfUtil.addErrorMessage("能力建设信息-培训学员（人）不允许为空！");
                ifUnPassValidate = true;
            }
        }
        if (null == this.supportHealth.getIfPublicRpt()) {
            JsfUtil.addErrorMessage("能力建设信息-请选择是否职业健康宣传信息报送！");
            ifUnPassValidate = true;
        }
        boolean valueFlag = null != this.supportHealth.getIfPublicRpt() && 1 == this.supportHealth.getIfPublicRpt()
                && null == this.supportHealth.getPublicRptNum();
        if (valueFlag) {
            JsfUtil.addErrorMessage("能力建设信息-上报宣传信息（条）不允许为空！");
            ifUnPassValidate = true;
        }
        if (null == this.supportHealth.getIfScience()) {
            JsfUtil.addErrorMessage("能力建设信息-请选择是否职业健康科普作品！");
            ifUnPassValidate = true;
        }
        if (null != this.supportHealth.getIfScience() && 1 == this.supportHealth.getIfScience()) {
            if (null == this.supportHealth.getVideoNum()) {
                JsfUtil.addErrorMessage("能力建设信息-视频（件）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getAudioNum()) {
                JsfUtil.addErrorMessage("能力建设信息-音频（件）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getBooks()) {
                JsfUtil.addErrorMessage("能力建设信息-书刊（件）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getBrochure()) {
                JsfUtil.addErrorMessage("能力建设信息-宣传册（件）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getOtherNum()) {
                JsfUtil.addErrorMessage("能力建设信息-其它（件）不允许为空！");
                ifUnPassValidate = true;
            }
        }
        if (null == this.supportHealth.getIfPromote()) {
            JsfUtil.addErrorMessage("能力建设信息-请选择是否职业健康促进！");
            ifUnPassValidate = true;
        }
        if (null == this.supportHealth.getIfGuid()) {
            JsfUtil.addErrorMessage("能力建设信息-请选择是否健康企业建设技术指导与支持！");
            ifUnPassValidate = true;
        }
        valueFlag = null != this.supportHealth.getIfGuid() && 1 == this.supportHealth.getIfGuid()
                && null == this.supportHealth.getGuidCrpts();
        if (valueFlag) {
            JsfUtil.addErrorMessage("能力建设信息-指导企业（家）不允许为空！");
            ifUnPassValidate = true;
        }
        if (this.ifAbilityValidateKf) {
            if (null == this.supportHealth.getTotalNum()) {
                JsfUtil.addErrorMessage("能力建设信息-收治病例（人）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getCureNum()) {
                JsfUtil.addErrorMessage("能力建设信息-治愈（例）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getBetterNum()) {
                JsfUtil.addErrorMessage("能力建设信息-好转（例）不允许为空！");
                ifUnPassValidate = true;
            }
            if (null == this.supportHealth.getDeathNum()) {
                JsfUtil.addErrorMessage("能力建设信息-死亡（例）不允许为空！");
                ifUnPassValidate = true;
            }
        }
        return ifUnPassValidate;
    }

    /**
     * <p>方法描述： 职业病治疗与康复上一级设置特殊标记 </p>
     * pw 2023/10/27
     **/
    private void fillChildSpec () {
        //处理职业病治疗与康复
        for (SupportInstAbilityVO abilityVO : this.abilityShowList) {
            this.fillChildSpec(abilityVO);
        }
    }

    /**
     * <p>方法描述： 详情页 职业病治疗与康复上一级设置特殊标记 </p>
     * pw 2023/10/27
     **/
    private void fillChildSpec (SupportInstAbilityVO abilityVO) {
        List<SupportInstAbilityVO> childList = abilityVO.getChildList();
        if (CollectionUtils.isEmpty(childList)) {
            return;
        }
        for (SupportInstAbilityVO ability : childList) {
            String ext1 = null == ability.getSimpleCode() ? null : ability.getSimpleCode().getExtendS1();
            if ("2".equals(ext1)) {
                this.ifAbilityValidateKf = true;
                abilityVO.setIfChildSpec(true);
                break;
            }
        }
    }
}
