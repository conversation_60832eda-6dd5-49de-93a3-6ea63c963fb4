package com.chis.modules.heth.zzsb.logic;

import java.util.List;

/**
 * <p>类描述： 资质机构备案-资质附件</p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年04月19日
 **/
public class ZwAnnexsPO {

    /**附件名称*/
    private String annexName;
    /**附件类型*/
    private Integer annexType;
    /**表头列名称*/
    private List<String> lineName;
    /**附件值*/
    private List<Object[]> values;

    public String getAnnexName() {
        return annexName;
    }

    public void setAnnexName(String annexName) {
        this.annexName = annexName;
    }

    public List<String> getLineName() {
        return lineName;
    }

    public void setLineName(List<String> lineName) {
        this.lineName = lineName;
    }

    public List<Object[]> getValues() {
        return values;
    }

    public void setValues(List<Object[]> values) {
        this.values = values;
    }

    public Integer getAnnexType() {
        return annexType;
    }

    public void setAnnexType(Integer annexType) {
        this.annexType = annexType;
    }
}
