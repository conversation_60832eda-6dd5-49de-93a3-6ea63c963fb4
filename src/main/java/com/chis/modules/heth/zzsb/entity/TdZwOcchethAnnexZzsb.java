package com.chis.modules.heth.zzsb.entity;

import com.chis.modules.system.entity.TsZone;

import javax.persistence.*;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @createTime 2019-9-24
 */
@Entity
@Table(name = "TD_ZW_OCCHETH_ANNEX")
@SequenceGenerator(name = "TdZwOcchethAnnex", sequenceName = "TD_ZW_OCCHETH_ANNEX_SEQ", allocationSize = 1)
public class TdZwOcchethAnnexZzsb implements java.io.Serializable,IZwAnnexZzsb {
	
	private static final long serialVersionUID = 1L;
	private Integer rid;
	private TdZwOcchethInfoZzsb fkByOrgId;
	private Short annexType;
	private String fileName;
	private String filePath;
	private Date createDate;
	private Integer createManid;
	
	public TdZwOcchethAnnexZzsb() {
	}

	public TdZwOcchethAnnexZzsb(Integer rid) {
		this.rid = rid;
	}
	
	@Id
	@Column(name = "RID")
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdZwOcchethAnnex")
	public Integer getRid() {
		return this.rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}	
	
	@ManyToOne
	@JoinColumn(name = "ORG_ID")			
	public TdZwOcchethInfoZzsb getFkByOrgId() {
		return fkByOrgId;
	}

	public void setFkByOrgId(TdZwOcchethInfoZzsb fkByOrgId) {
		this.fkByOrgId = fkByOrgId;
	}	
			
	@Column(name = "ANNEX_TYPE")	
	public Short getAnnexType() {
		return annexType;
	}

	public void setAnnexType(Short annexType) {
		this.annexType = annexType;
	}	
			
	@Column(name = "FILE_NAME")	
	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}	
			
	@Column(name = "FILE_PATH")	
	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}	
			
	@Temporal(TemporalType.TIMESTAMP)		
	@Column(name = "CREATE_DATE")			
	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}	
			
	@Column(name = "CREATE_MANID")	
	public Integer getCreateManid() {
		return createManid;
	}

	public void setCreateManid(Integer createManid) {
		this.createManid = createManid;
	}
	@Transient
	@Override
	public IZwOrginfoZzsb getZwOrginfo() {
		return fkByOrgId;
	}

	@Override
	public void setZwOrginfo(IZwOrginfoZzsb zwOrginfo) {
		this.fkByOrgId = (TdZwOcchethInfoZzsb) zwOrginfo;
	}
	@Transient
	@Override
	public TsZone getFkByRegZoneId() {
		return null;
	}

	@Override
	public void setFkByRegZoneId(TsZone fkByRegZoneId) {
		
	}
	@Transient
	@Override
	public Integer getUnitLevel() {
		return null;
	}

	@Override
	public void setUnitLevel(Integer unitLevel) {
		
	}
	@Transient
	@Override
	public String getUnitName() {
		return null;
	}

	@Override
	public void setUnitName(String unitName) {
		
	}	
			
}