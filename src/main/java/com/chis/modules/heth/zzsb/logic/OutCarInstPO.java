package com.chis.modules.heth.zzsb.logic;

import java.io.Serializable;

/**
 * <p>类描述：外检车辆-仪器PO </p>
 *
 * @ClassAuthor: yzz
 * @date： 2022年04月19日
 **/
public class OutCarInstPO implements Serializable {

    /**职业健康检查仪器rid*/
    private Integer rid;
    /**仪器名称*/
    private String instName;
    /**仪器型号*/
    private String instModel;
    /**仪器编号*/
    private String instCode;
    /**复选框选中*/
    private Boolean selected;
    /**仪器rid*/
    private Integer instRid;

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public String getInstName() {
        return instName;
    }

    public void setInstName(String instName) {
        this.instName = instName;
    }

    public String getInstCode() {
        return instCode;
    }

    public void setInstCode(String instCode) {
        this.instCode = instCode;
    }

    public String getInstModel() {
        return instModel;
    }

    public void setInstModel(String instModel) {
        this.instModel = instModel;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public Integer getInstRid() {
        return instRid;
    }

    public void setInstRid(Integer instRid) {
        this.instRid = instRid;
    }
}
