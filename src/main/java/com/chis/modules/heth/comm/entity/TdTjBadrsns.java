package com.chis.modules.heth.comm.entity;

import java.math.BigInteger;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;

/**
 * 接触危害因素
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 */
@Entity
@Table(name = "TD_TJ_BADRSNS")
@SequenceGenerator(name = "TdTjBadrsns_Seq", sequenceName = "TD_TJ_BADRSNS_SEQ", allocationSize = 1)
public class TdTjBadrsns implements java.io.Serializable {

    private static final long serialVersionUID = 6274406447007243145L;
    private BigInteger rid;
    private TdTjBhk tdTjBhk;
    private TsSimpleCode tsSimpleCode;
    private Date createDate;
    private Integer createManid;
    private TsSimpleCode fkByExamConclusionId;
    private String qtjbName;

    /** default constructor */
    public TdTjBadrsns() {
    }

    /** full constructor */
    public TdTjBadrsns(BigInteger rid, TdTjBhk tdTjBhk, TsSimpleCode tsSimpleCode,
                       Date createDate, Integer createManid) {
        this.rid = rid;
        this.tdTjBhk = tdTjBhk;
        this.tsSimpleCode = tsSimpleCode;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjBadrsns_Seq")
    public BigInteger getRid() {
        return this.rid;
    }

    public void setRid(BigInteger rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhk getTdTjBhk() {
        return this.tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID" )
    public TsSimpleCode getTsSimpleCode() {
        return this.tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @ManyToOne
    @JoinColumn(name = "EXAM_CONCLUSION_ID")
    public TsSimpleCode getFkByExamConclusionId() {
        return fkByExamConclusionId;
    }

    public void setFkByExamConclusionId(TsSimpleCode fkByExamConclusionId) {
        this.fkByExamConclusionId = fkByExamConclusionId;
    }

    @Column(name = "QTJB_NAME")
    public String getQtjbName() {
        return qtjbName;
    }

    public void setQtjbName(String qtjbName) {
        this.qtjbName = qtjbName;
    }

}
