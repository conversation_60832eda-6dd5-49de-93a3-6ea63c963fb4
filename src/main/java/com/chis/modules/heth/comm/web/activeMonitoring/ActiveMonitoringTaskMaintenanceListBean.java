package com.chis.modules.heth.comm.web.activeMonitoring;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.json.CrptDialogParamJson;
import com.chis.modules.heth.comm.service.ActiveMonitoringTaskServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.util.*;

/**
 * 主动监测任务维护Bean
 *
 * <AUTHOR>
 * @version 1.0
 */
@ManagedBean(name = "activeMonitoringTaskMaintenanceListBean")
@ViewScoped
public class ActiveMonitoringTaskMaintenanceListBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;

    private final CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private final ActiveMonitoringTaskServiceImpl activeMonitoringTaskService =
            SpringContextHolder.getBean(ActiveMonitoringTaskServiceImpl.class);
    /**
     * 当前登录人单位RID
     */
    private Integer unitRid;
    /**
     * 主动监测任务RID
     */
    private Integer rid;
    /**
     * 主动监测任务
     */
    private TbTjJcTask jcTask;
    /**
     * 查询条件: 年份
     */
    private Integer searchYear;
    private List<Integer> searchYearList;
    /**
     * 查询条件: 单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件: 社会信用代码
     */
    private String searchInstitutionCode;
    /**
     * 查询条件: 行业类别
     */
    private String selectIndusTypeIds;
    private String selectIndusTypeNames;
    /**
     * 查询条件: 经济类型
     */
    private String selectEconomyIds;
    private String selectEconomyNames;
    /**
     * 查询条件: 企业规模
     */
    private String selectCrptSizeIds;
    private List<TsSimpleCode> crptSizeList;
    /**
     * 查询条件: 姓名
     */
    private String searchPsnName;
    /**
     * 查询条件: 身份证号
     */
    private String searchIdc;
    /**
     * 查询条件: 状态
     */
    private Integer[] searchState;
    private List<SelectItem> stateList;
    /**
     * 当前操作码表类型
     */
    private String simpleCodeOpType;
    /**
     * 当前操作码表特殊标记
     */
    private Integer simpleCodeOpTag;
    /**
     * 在岗状态页面选择
     */
    private Map<String, Integer> editOnguadrStateMap;
    /**
     * 在岗状态-在岗期间-RID
     */
    private Integer whileOnDutyRid;
    /**
     * 证件号码-身份证件号类型-RID
     */
    private Integer idcTypeRid;
    /**
     * 主动监测因素是否多个
     */
    private Boolean ifMultipleFactors;
    /**
     * 当前行业类别主动监测岗位
     */
    private Set<String> postSet;
    /**
     * 当前行业类别主动监测因素
     */
    private Set<String> rsnSet;

    public ActiveMonitoringTaskMaintenanceListBean() {
        initSimpleCode();
        otherInit();
        super.searchAction();
    }

    /**
     * 初始化码表相关
     */
    private void initSimpleCode() {
        //企业规模
        if (CollectionUtils.isEmpty(this.crptSizeList)) {
            this.crptSizeList = this.commService.findLevelSimpleCodesByTypeId("5004");
        }
        //在岗状态
        if (ObjectUtil.isEmpty(this.editOnguadrStateMap)) {
            this.editOnguadrStateMap = new LinkedHashMap<>();
            List<TsSimpleCode> simpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5009");
            for (TsSimpleCode simpleCode : simpleCodeList) {
                Integer simpleCodeRid = simpleCode.getRid();
                this.editOnguadrStateMap.put(simpleCode.getCodeName(), simpleCodeRid);
                //在岗期间
                if ("2".equals(simpleCode.getExtendS1())) {
                    this.whileOnDutyRid = simpleCodeRid;
                }
            }
        }
        if (this.idcTypeRid == null) {
            List<TsSimpleCode> simpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5503");
            for (TsSimpleCode simpleCode : simpleCodeList) {
                //身份证件号
                if ("01".equals(simpleCode.getCodeNo())) {
                    this.idcTypeRid = simpleCode.getRid();
                    break;
                }
            }
        }
    }

    /**
     * 加载当前行业类别主动监测岗位和主动监测因素
     */
    private void initPostAndRsnSet() {
        this.postSet = new HashSet<>();
        String sql = genSql(2, "");
        pakPostOrRsnSet(this.postSet, sql);
        this.rsnSet = new HashSet<>();
        sql = genSql(3, "");
        pakPostOrRsnSet(this.rsnSet, sql);
    }

    private void pakPostOrRsnSet(Set<String> set, String sql) {
        List<Object[]> list = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, null));
        for (Object[] objects : list) {
            Integer rid = ObjectUtil.convert(Integer.class, objects[0]);
            if (rid == null) {
                continue;
            }
            set.add(StringUtils.objectToString(rid));
        }
    }

    /**
     * 其它初始化
     */
    private void otherInit() {
        this.ifSQL = true;
        //操作人所在单位
        if (Global.getUser().getTsUnit() == null || Global.getUser().getTsUnit().getRid() == null) {
            this.unitRid = -1;
        } else {
            this.unitRid = Global.getUser().getTsUnit().getRid();
        }
        //年份, 最近10年
        this.searchYearList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            this.searchYearList.add(DateUtils.getYearInt() - i);
        }
        //默认当年
        this.searchYear = this.searchYearList.get(0);
        //状态, 默认待提交、已退回
        this.searchState = new Integer[]{0, 3};
        this.stateList = new ArrayList<>();
        this.stateList.add(new SelectItem(0, "待提交"));
        this.stateList.add(new SelectItem(1, "待审核"));
        this.stateList.add(new SelectItem(3, "已退回"));
        this.stateList.add(new SelectItem(2, "审核通过"));
        this.ifMultipleFactors = "1".equals(this.commService.findParamValue("IF_MULTIPLE_FACTORS"));
    }

    @Override
    public String[] buildHqls() {
        String dataSql = "SELECT T.RID, " +
                "       T.YEAR, " +
                "       Z.FULL_NAME   AS ZONE_NAME, " +
                "       T.CRPT_NAME, " +
                "       T.CREDIT_CODE, " +
                "       SC1.CODE_NAME AS INDUS_TYPE, " +
                "       SC2.CODE_NAME AS ECONOMY, " +
                "       SC3.CODE_NAME AS CRPT_SIZE, " +
                "       '', " +
                "       T.BACK_RSN, " +
                "       T.STATE ";
        String sql = "FROM TB_TJ_JC_TASK T " +
                "         LEFT JOIN TS_ZONE Z ON T.ZONE_ID = Z.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC1 ON T.INDUS_TYPE_ID = SC1.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC2 ON T.ECONOMY_ID = SC2.RID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC3 ON T.CRPT_SIZE_ID = SC3.RID " +
                "WHERE NVL(T.DEL_MARK, 0) = 0 AND T.ORG_ID = :unitRid ";
        //仅查询本单位
        this.paramMap.put("unitRid", this.unitRid);
        //年份
        if (ObjectUtil.isNotEmpty(this.searchYear)) {
            this.paramMap.put("searchYear", this.searchYear);
            sql += "AND T.YEAR = :searchYear ";
        }
        //单位名称
        if (ObjectUtil.isNotEmpty(this.searchCrptName)) {
            this.paramMap.put("searchCrptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
            sql += "AND T.CRPT_NAME LIKE :searchCrptName escape '\\' ";
        }
        //社会信用代码
        if (ObjectUtil.isNotEmpty(this.searchInstitutionCode)) {
            this.paramMap.put("searchInstitutionCode", "%" + StringUtils.convertBFH(this.searchInstitutionCode.trim()) + "%");
            sql += "AND T.CREDIT_CODE LIKE :searchInstitutionCode escape '\\' ";
        }
        //行业类别
        if (StringUtils.isNotBlank(this.selectIndusTypeIds)) {
            List<String> indusTypeIdList = StringUtils.string2list(this.selectIndusTypeIds, ",");
            this.paramMap.put("indusTypeIdList", indusTypeIdList);
            sql += "AND T.INDUS_TYPE_ID IN (:indusTypeIdList) ";
        }
        //经济类型
        if (StringUtils.isNotBlank(this.selectEconomyIds)) {
            List<String> economyIdList = StringUtils.string2list(this.selectEconomyIds, ",");
            this.paramMap.put("economyIdList", economyIdList);
            sql += "AND T.ECONOMY_ID IN (:economyIdList) ";
        }
        //企业规模
        if (StringUtils.isNotBlank(this.selectCrptSizeIds)) {
            List<String> crptSizeIdList = StringUtils.string2list(this.selectCrptSizeIds, ",");
            this.paramMap.put("crptSizeIdList", crptSizeIdList);
            sql += "AND T.CRPT_SIZE_ID IN (:crptSizeIdList) ";
        }
        //姓名&身份证号
        if (ObjectUtil.isNotEmpty(this.searchPsnName) && ObjectUtil.isNotEmpty(this.searchIdc)) {
            this.paramMap.put("searchPsnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
            this.paramMap.put("searchIdc", this.searchIdc);
            sql += "AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.PSN_NAME LIKE :searchPsnName escape '\\' AND P.IDC = :searchIdc) ";
        } else if (ObjectUtil.isNotEmpty(this.searchPsnName)) {
            this.paramMap.put("searchPsnName", "%" + StringUtils.convertBFH(this.searchPsnName.trim()) + "%");
            sql += "AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.PSN_NAME LIKE :searchPsnName escape '\\') ";
        } else if (ObjectUtil.isNotEmpty(this.searchIdc)) {
            this.paramMap.put("searchIdc", this.searchIdc);
            sql += "AND EXISTS(SELECT 1 FROM TB_TJ_JC_TASK_PSN P WHERE T.RID = P.MAIN_ID AND P.IDC = :searchIdc) ";
        }
        //状态
        if (ObjectUtil.isNotEmpty(this.searchState)) {
            List<Integer> searchStateList = new ArrayList<>(Arrays.asList(searchState));
            this.paramMap.put("searchStateList", searchStateList);
            sql += "AND T.STATE IN (:searchStateList) ";
        }
        String countSql = "SELECT COUNT(1) " + sql;
        dataSql += sql + "ORDER BY T.YEAR DESC, Z.ZONE_GB, T.CRPT_NAME, T.CREDIT_CODE";
        return new String[]{dataSql, countSql};
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> result = CollectionUtil.castList(Object[].class, list);
        for (Object[] obj : result) {
            //地区去除省份
            String fullName = StringUtils.objectToString(obj[2]);
            obj[2] = fullName.substring(fullName.indexOf("_") + 1);
            //处理页面显示状态
            obj[8] = "";
            String state = StringUtils.objectToString(obj[10]);
            switch (state) {
                case "0":
                    obj[8] = "待提交";
                    break;
                case "1":
                    obj[8] = "待审核";
                    break;
                case "2":
                    obj[8] = "审核通过";
                    break;
                case "3":
                    obj[8] = "已退回";
                    break;
            }
            //仅已退回显示退回原因
            if (!"3".equals(state)) {
                obj[9] = "";
            }
        }
    }

    /**
     * 选择码表弹框页面
     */
    public void selSimpleCodeAction() {
        String titleName = "";
        String selectIds = "";
        String dialogUrl = "";
        String ifAllSelect = "";
        String customizationSql = "";
        Integer width = null;
        Integer contentWidth = null;
        Integer height = null;
        Integer contentHeight = null;
        boolean ifShowFirstCode = false;
        switch (this.simpleCodeOpType) {
            case "5002":
                titleName = "行业类别";
                selectIds = this.selectIndusTypeIds;
                contentWidth = 650;
                contentHeight = 500;
                customizationSql = genSql(1, "");
                dialogUrl = "/webapp/system/codeMulitySelectList";
                break;
            case "5003":
                titleName = "经济类型";
                selectIds = this.selectEconomyIds;
                contentWidth = 650;
                contentHeight = 500;
                ifShowFirstCode = true;
                dialogUrl = "/webapp/system/codeMulitySelectList";
                break;
            case "5595":
                TbTjJcTaskPsn jcTaskPsn1 = findJcTaskPsnBySimpleCodeOpTag();
                titleName = "岗位";
                selectIds = StringUtils.objectToString(jcTaskPsn1.getPostId());
                contentWidth = 550;
                contentHeight = 430;
                ifAllSelect = "true";
                dialogUrl = "/webapp/system/codeRadioSelectListNew";
                customizationSql = genSql(2, selectIds);
                break;
            case "5007":
                TbTjJcTaskPsn jcTaskPsn2 = findJcTaskPsnBySimpleCodeOpTag();
                titleName = "职业病主动监测因素";
                selectIds = jcTaskPsn2.getRsnId();
                if (this.ifMultipleFactors) {
                    contentWidth = 650;
                    contentHeight = 500;
                    dialogUrl = "/webapp/system/codeMulitySelectList";
                    customizationSql = genSql(4, "");
                } else {
                    contentWidth = 550;
                    contentHeight = 430;
                    ifAllSelect = "true";
                    dialogUrl = "/webapp/system/codeRadioSelectListNew";
                    customizationSql = genSql(3, selectIds);
                }
                break;
            default:
                break;
        }
        Map<String, Object> options = MapUtils.produceDialogMap(width, contentWidth, height, contentHeight);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add(titleName);
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add(this.simpleCodeOpType);
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<>();
        paramList.add(selectIds);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<>();
        paramList.add(StringUtils.objectToString(ifShowFirstCode));
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<>();
        paramList.add(ifAllSelect);
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<>();
        paramList.add(StringUtils.objectToString(customizationSql));
        paramMap.put("customizationSql", paramList);

        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog(dialogUrl, options, paramMap);
    }

    /**
     * 生成SQL
     *
     * @param type      <pre>1: 查询重点行业的行业类别</pre><pre>2: 查询行业类别下监测岗位</pre>
     *                  <pre>3: 查询行业类别下职业病主动监测因素(单选)</pre><pre>4: 查询行业类别下职业病主动监测因素(多选)</pre>
     * @param excludeId 排除的id
     * @return SQL
     */
    private String genSql(int type, String excludeId) {
        if (type == 1) {
            return "SELECT SC.RID,SC.CODE_NAME,SC.CODE_NO,'' AS CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2, " +
                    "       SC.EXTENDS3,SC.EXTENDS4,SC.EXTENDS5,SC.CODE_PATH " +
                    "FROM TD_ZDZYB_ANALY_DETAIL_SUB DS " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC ON DS.ANALY_ITEM_ID = SC.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID " +
                    "WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 " +
                    "GROUP BY SC.RID,SC.CODE_NAME,SC.CODE_NO,SC.CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2, " +
                    "   SC.EXTENDS3,SC.EXTENDS4,SC.EXTENDS5,SC.CODE_PATH,SC.NUM " +
                    "ORDER BY SC.NUM,SC.CODE_LEVEL_NO,SC.CODE_NO";
        } else if (type == 2) {
            return "SELECT SC.RID,SC.CODE_NAME,SC.CODE_NO,'' AS CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2, " +
                    "       SC.EXTENDS3,SC.EXTENDS4,SC.CODE_DESC " +
                    "FROM TD_ZDZYB_ANALY_DETAIL_SUB DS " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC1 ON DS.ANALY_ITEM_ID = SC1.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID " +
                    "         LEFT JOIN TD_ZDZYB_DETAIL_SUB_REL DSR ON DS.RID = DSR.MAIN_ID " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC ON DSR.ANALY_ITEM_ID = SC.RID " +
                    "WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 AND DSR.TYPE = 1 AND SC1.RID = " + this.jcTask.getFkByIndusTypeId().getRid() + " " +
                    (StringUtils.isNotBlank(excludeId) ? ("AND SC.RID NOT IN (" + excludeId + ")") : "") + " " +
                    "GROUP BY SC.RID,SC.CODE_NAME,SC.CODE_NO,SC.CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2," +
                    "   SC.EXTENDS3,SC.EXTENDS4,SC.CODE_DESC,SC.NUM " +
                    "ORDER BY SC.NUM,SC.CODE_LEVEL_NO,SC.CODE_NO";
        } else if (type == 3) {
            return "SELECT SC.RID,SC.CODE_NAME,SC.CODE_NO,'' AS CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2, " +
                    "       SC.EXTENDS3,SC.EXTENDS4,SC.CODE_DESC " +
                    "FROM TD_ZDZYB_ANALY_DETAIL_SUB DS " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC1 ON DS.ANALY_ITEM_ID = SC1.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID " +
                    "         LEFT JOIN TD_ZDZYB_DETAIL_SUB_REL DSR ON DS.RID = DSR.MAIN_ID " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC ON DSR.ANALY_ITEM_ID = SC.RID " +
                    "WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 AND DSR.TYPE = 2 AND SC1.RID = " + this.jcTask.getFkByIndusTypeId().getRid() + " " +
                    (StringUtils.isNotBlank(excludeId) ? ("AND SC.RID NOT IN (" + excludeId + ")") : "") + " " +
                    "GROUP BY SC.RID,SC.CODE_NAME,SC.CODE_NO,SC.CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2, " +
                    "   SC.EXTENDS3,SC.EXTENDS4,SC.CODE_DESC,SC.NUM " +
                    "ORDER BY SC.NUM,SC.CODE_LEVEL_NO,SC.CODE_NO";
        } else if (type == 4) {
            return "SELECT SC.RID,SC.CODE_NAME,SC.CODE_NO,'' AS CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2, " +
                    "       SC.EXTENDS3,SC.EXTENDS4,SC.EXTENDS5,SC.CODE_PATH " +
                    "FROM TD_ZDZYB_ANALY_DETAIL_SUB DS " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC1 ON DS.ANALY_ITEM_ID = SC1.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_DETAIL D ON DS.MAIN_ID = D.RID " +
                    "         LEFT JOIN TD_ZDZYB_ANALY_TYPE T ON D.MAIN_ID = T.RID " +
                    "         LEFT JOIN TD_ZDZYB_DETAIL_SUB_REL DSR ON DS.RID = DSR.MAIN_ID " +
                    "         LEFT JOIN TS_SIMPLE_CODE SC ON DSR.ANALY_ITEM_ID = SC.RID " +
                    "WHERE T.BUS_TYPE = 4 AND T.ANALY_TYPE = 3 AND DSR.TYPE = 2 AND SC1.RID = " + this.jcTask.getFkByIndusTypeId().getRid() + " " +
                    "GROUP BY SC.RID,SC.CODE_NAME,SC.CODE_NO,SC.CODE_LEVEL_NO,SC.SPLSHT,SC.EXTENDS1,SC.EXTENDS2, " +
                    "   SC.EXTENDS3,SC.EXTENDS4,SC.EXTENDS5,SC.CODE_PATH,SC.NUM " +
                    "ORDER BY SC.NUM,SC.CODE_LEVEL_NO,SC.CODE_NO";
        }
        return "";
    }

    /**
     * 选择码表后操作
     *
     * @param event 选择项
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (ObjectUtil.isEmpty(selectedMap)) {
            return;
        }
        List<String> idAndNameList;
        if (selectedMap.get("selectPro") instanceof TsSimpleCode) {
            idAndNameList = pakIdAndNameForRadio((TsSimpleCode) selectedMap.get("selectPro"));
        } else {
            idAndNameList = pakIdAndNameForMulity(CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros")));
        }
        switch (this.simpleCodeOpType) {
            case "5002":
                this.selectIndusTypeIds = idAndNameList.get(0);
                this.selectIndusTypeNames = idAndNameList.get(1);
                break;
            case "5003":
                this.selectEconomyIds = idAndNameList.get(0);
                this.selectEconomyNames = idAndNameList.get(1);
                break;
            case "5595":
                TbTjJcTaskPsn jcTaskPsn1 = findJcTaskPsnBySimpleCodeOpTag();
                jcTaskPsn1.setPostId(ObjectUtil.convert(Integer.class, idAndNameList.get(0)));
                jcTaskPsn1.setPostName(idAndNameList.get(1));
                break;
            case "5007":
                TbTjJcTaskPsn jcTaskPsn2 = findJcTaskPsnBySimpleCodeOpTag();
                jcTaskPsn2.setRsnId(idAndNameList.get(0));
                jcTaskPsn2.setRsnName(idAndNameList.get(1));
                break;
            default:
                break;
        }
    }

    /**
     * 单选弹出框返回数据处理
     *
     * @param simpleCode 码表
     * @return 处理后数据
     */
    private List<String> pakIdAndNameForRadio(TsSimpleCode simpleCode) {
        List<String> idAndNameList = new ArrayList<>();
        if (simpleCode == null || simpleCode.getRid() == null) {
            idAndNameList.add("");
            idAndNameList.add("");
            return idAndNameList;
        }
        idAndNameList.add(StringUtils.objectToString(simpleCode.getRid()));
        idAndNameList.add(StringUtils.objectToString(simpleCode.getCodeName()));
        return idAndNameList;
    }

    /**
     * 多选弹出框返回数据处理
     *
     * @param simpleCodeList 码表
     * @return 处理后数据
     */
    private List<String> pakIdAndNameForMulity(List<TsSimpleCode> simpleCodeList) {
        List<String> idAndNameList = new ArrayList<>();
        if (CollectionUtils.isEmpty(simpleCodeList)) {
            idAndNameList.add("");
            idAndNameList.add("");
            return idAndNameList;
        }
        List<String> idList = new ArrayList<>();
        List<String> nameList = new ArrayList<>();
        for (TsSimpleCode simpleCode : simpleCodeList) {
            if (simpleCode == null || simpleCode.getRid() == null) {
                continue;
            }
            idList.add(StringUtils.objectToString(simpleCode.getRid()));
            nameList.add(StringUtils.objectToString(simpleCode.getCodeName()));
        }
        idAndNameList.add(StringUtils.list2string(idList, ","));
        idAndNameList.add(StringUtils.list2string(nameList, "，"));
        return idAndNameList;
    }

    /**
     * 清除页面选择码表
     */
    public void clearSimpleCode() {
        switch (this.simpleCodeOpType) {
            case "5002":
                this.selectIndusTypeNames = null;
                this.selectIndusTypeIds = null;
                break;
            case "5003":
                this.selectEconomyNames = null;
                this.selectEconomyIds = null;
                break;
            default:
                break;
        }
    }

    public TbTjJcTaskPsn findJcTaskPsnBySimpleCodeOpTag() {
        if (this.simpleCodeOpTag == null || CollectionUtils.isEmpty(this.jcTask.getJcTaskPsnList())
                || this.jcTask.getJcTaskPsnList().size() <= this.simpleCodeOpTag) {
            return null;
        }
        return this.jcTask.getJcTaskPsnList().get(this.simpleCodeOpTag);
    }

    @Override
    public void addInit() {
        this.jcTask = new TbTjJcTask();
        this.commService.preEntity(this.jcTask);
        //当前年
        this.jcTask.setYear(DateUtils.getYearInt());
        //操作人系统单位
        this.jcTask.setFkByOrgId(new TsUnit(this.unitRid));
        this.jcTask.setDelMark(0);
        if (CollectionUtils.isEmpty(this.jcTask.getJcTaskPsnList())) {
            this.jcTask.setJcTaskPsnList(new ArrayList<TbTjJcTaskPsn>());
        }
    }

    @Override
    public void viewInit() {
        this.jcTask = this.activeMonitoringTaskService.findTbTjJcTaskByRid(this.rid);
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:viewForm:jcTaskPsnTable");
        if (dataTable == null) {
            return;
        }
        dataTable.setFirst(0);
        dataTable.setRows(20);
    }

    @Override
    public void modInit() {
        this.jcTask = this.activeMonitoringTaskService.findTbTjJcTaskByRid(this.rid);
        DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:jcTaskPsnTable");
        if (dataTable == null) {
            return;
        }
        dataTable.setFirst(0);
        dataTable.setRows(20);
    }

    @Override
    public void saveAction() {
        try {
            if (verifyCrptInfoFailed()) {
                return;
            }
            if (verifyLogic()) {
                return;
            }
            this.jcTask.setState(0);
            this.jcTask.setBackRsn("");
            this.activeMonitoringTaskService.upsertTbTjJcTask(this.jcTask);
            this.rid = this.jcTask.getRid();
            JsfUtil.addSuccessMessage("保存成功！");
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }

    public void submitAction() {
        try {
            this.jcTask.setState(1);
            this.jcTask.setBackRsn("");
            this.activeMonitoringTaskService.upsertTbTjJcTask(this.jcTask);
            this.rid = this.jcTask.getRid();
            JsfUtil.addSuccessMessage("提交成功！");
            viewInitAction();
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败！");
        }
    }

    public void beforeSubmitAction() {
        if (verifySubmitFailed()) {
            return;
        }
        RequestContext.getCurrentInstance().execute("PF('ConfirmDialog').show()");
    }

    public void deleteAction() {
        try {
            if (this.rid == null) {
                return;
            }
            this.activeMonitoringTaskService.updateTbTjJcTaskParamByRid(this.rid, "DEL_MARK", 1);
            JsfUtil.addSuccessMessage("删除成功！");
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    public void quashAction() {
        try {
            if (this.rid == null) {
                return;
            }
            this.activeMonitoringTaskService.updateTbTjJcTaskParamByRid(this.rid, "STATE", 0);
            JsfUtil.addSuccessMessage("撤销成功！");
            modInitAction();
            searchAction();
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
        }
    }

    /**
     * 验证单位信息(暂存)
     *
     * @return 成功返回false, 失败返回true
     */
    public boolean verifyCrptInfoFailed() {
        if (this.jcTask.getFkByCrptId() == null || this.jcTask.getFkByCrptId().getRid() == null) {
            JsfUtil.addErrorMessage("请先选择单位名称！");
            return true;
        }
        return false;
    }

    /**
     * 验证身份证件号唯一和格式是否正确
     *
     * @return 成功返回false, 失败返回true
     */
    public boolean verifyLogic() {
        boolean flag = false;
        List<TbTjJcTaskPsn> jcTaskPsnList = this.jcTask.getJcTaskPsnList();
        if (CollectionUtils.isEmpty(jcTaskPsnList)) {
            return false;
        }
        initPostAndRsnSet();
        Map<String, List<String>> idcMap = new HashMap<>();
        for (int i = 0, jcTaskPsnListSize = jcTaskPsnList.size(); i < jcTaskPsnListSize; i++) {
            TbTjJcTaskPsn jcTaskPsn = jcTaskPsnList.get(i);
            if (StringUtils.isNotBlank(jcTaskPsn.getIdc())) {
                if (IdcUtils.checkIDC(jcTaskPsn.getIdc()) != null) {
                    JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息身份证号输入错误！");
                    flag = true;
                }
                if (!idcMap.containsKey(jcTaskPsn.getIdc())) {
                    idcMap.put(jcTaskPsn.getIdc(), new ArrayList<String>());
                }
                idcMap.get(jcTaskPsn.getIdc()).add(StringUtils.objectToString(i + 1));
            }
            if (jcTaskPsn.getPostId() != null && !this.postSet.contains(StringUtils.objectToString(jcTaskPsn.getPostId()))) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息监测岗位名称必须在主动监测重点行业范围内！");
                flag = true;
            }
            if (StringUtils.isNotBlank(jcTaskPsn.getRsnId())) {
                if (verifyRsnFailed(jcTaskPsn.getRsnId())) {
                    JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息职业病主动监测因素必须在主动监测重点行业范围内！");
                    flag = true;
                }
            }
        }
        if (verifySole(idcMap)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 提交验证
     *
     * @return 成功返回false, 失败返回true
     */
    public boolean verifySubmitFailed() {
        boolean flag = verifyCrptInfoFailed();
        List<TbTjJcTaskPsn> jcTaskPsnList = this.jcTask.getJcTaskPsnList();
        if (CollectionUtils.isEmpty(jcTaskPsnList)) {
            JsfUtil.addErrorMessage("请添加劳动者信息！");
            return true;
        }
        initPostAndRsnSet();
        Map<String, List<String>> idcMap = new HashMap<>();
        for (int i = 0, jcTaskPsnListSize = jcTaskPsnList.size(); i < jcTaskPsnListSize; i++) {
            TbTjJcTaskPsn jcTaskPsn = jcTaskPsnList.get(i);
            if (StringUtils.isBlank(jcTaskPsn.getPsnName())) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息姓名不能为空！");
                flag = true;
            }
            if (StringUtils.isBlank(jcTaskPsn.getIdc())) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息身份证号不能为空！");
                flag = true;
            } else {
                if (IdcUtils.checkIDC(jcTaskPsn.getIdc()) != null) {
                    JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息身份证号输入错误！");
                    flag = true;
                }
                if (!idcMap.containsKey(jcTaskPsn.getIdc())) {
                    idcMap.put(jcTaskPsn.getIdc(), new ArrayList<String>());
                }
                idcMap.get(jcTaskPsn.getIdc()).add(StringUtils.objectToString(i + 1));
            }
            if (jcTaskPsn.getPostId() == null) {
                JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息监测岗位名称！");
                flag = true;
            } else if (!this.postSet.contains(StringUtils.objectToString(jcTaskPsn.getPostId()))) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息监测岗位名称必须在主动监测重点行业范围内！");
                flag = true;
            }
            if (jcTaskPsn.getOnguadrStateId() == null) {
                JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息在岗状态！");
                flag = true;
            }
            if (StringUtils.isBlank(jcTaskPsn.getRsnId())) {
                JsfUtil.addErrorMessage("请选择第" + (i + 1) + "条劳动者信息职业病主动监测因素！");
                flag = true;
            } else if (verifyRsnFailed(jcTaskPsn.getRsnId())) {
                JsfUtil.addErrorMessage("第" + (i + 1) + "条劳动者信息职业病主动监测因素必须在主动监测重点行业范围内！");
                flag = true;
            }
        }
        if (verifySole(idcMap)) {
            flag = true;
        }
        return flag;
    }

    /**
     * 验证危害因素是否在主动监测重点行业范围内
     *
     * @param rsnIds 危害因素id
     * @return 不在返回true
     */
    private boolean verifyRsnFailed(String rsnIds) {
        List<String> rsnIdList = StringUtils.string2list(rsnIds, ",");
        for (String rsnId : rsnIdList) {
            if (!this.rsnSet.contains(rsnId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证身份证件号唯一
     *
     * @param idcMap 身份证件号Map
     * @return 存在重复的返回true
     */
    private boolean verifySole(Map<String, List<String>> idcMap) {
        boolean flag = false;
        for (Map.Entry<String, List<String>> entry : idcMap.entrySet()) {
            if (entry.getValue().size() <= 1) {
                continue;
            }
            JsfUtil.addErrorMessage("第" + StringUtils.list2string(entry.getValue(), "、") + "条劳动者信息身份证件号重复！");
            flag = true;
        }
        return flag;
    }

    /**
     * 选择单位
     */
    public void selectCrptList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1080, 1050, 520, 505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        json.setBusType("1");
        json.setIfActiveMonitoringTask(Boolean.TRUE);
        json.setActiveMonitoringTaskRid(this.jcTask.getRid());
        json.setIfShowOtherZone(true);
        json.setIfSearchZoneDefault(false);
        json.setSearchCrptName(StringUtils.objectToString(this.jcTask.getCrptName()));
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }


    /**
     * 选择单位后
     */
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TbTjCrptIndepend tbTjCrptIndepend = (TbTjCrptIndepend) selectedMap.get("selectCrptIndepend");
            TbTjCrpt tbTjCrpt = tbTjCrptIndepend.getFkByCrptId();
            this.jcTask.setFkByCrptId(tbTjCrpt);
            this.jcTask.setCrptName(tbTjCrpt.getCrptName());
            this.jcTask.setFkByZoneId(tbTjCrpt.getTsZoneByZoneId());
            this.jcTask.setCreditCode(tbTjCrpt.getInstitutionCode());
            this.jcTask.setAddress(tbTjCrpt.getAddress());
            this.jcTask.setFkByIndusTypeId(tbTjCrpt.getTsSimpleCodeByIndusTypeId());
            this.jcTask.setFkByEconomyId(tbTjCrpt.getTsSimpleCodeByEconomyId());
            this.jcTask.setFkByCrptSizeId(tbTjCrpt.getTsSimpleCodeByCrptSizeId());
            this.jcTask.setLinkMan(tbTjCrpt.getLinkman2());
            this.jcTask.setLinkPhone(tbTjCrpt.getLinkphone2());
        }
    }

    /**
     * 劳动者花名册-添加
     */
    public void addJcTaskPsnAction() {
        if (verifyCrptInfoFailed()) {
            return;
        }
        TbTjJcTaskPsn jcTaskPsn = new TbTjJcTaskPsn();
        jcTaskPsn.setOnguadrStateId(this.whileOnDutyRid);
        jcTaskPsn.setFkByMainId(this.jcTask);
        jcTaskPsn.setFkByCardTypeId(new TsSimpleCode(this.idcTypeRid));
        jcTaskPsn.setJcTaskBadrsnList(new ArrayList<TbTjJcTaskBadrsn>());
        this.commService.preEntity(jcTaskPsn);
        this.jcTask.getJcTaskPsnList().add(jcTaskPsn);
    }

    /**
     * 劳动者花名册-删除
     *
     * @param index 下标
     */
    public void delJcTaskPsnAction(Integer index) {
        if (index == null) {
            return;
        }
        this.jcTask.getJcTaskPsnList().remove((int) index);
    }

    public CommServiceImpl getCommService() {
        return commService;
    }

    public ActiveMonitoringTaskServiceImpl getActiveMonitoringTaskService() {
        return activeMonitoringTaskService;
    }

    public Integer getUnitRid() {
        return unitRid;
    }

    public void setUnitRid(Integer unitRid) {
        this.unitRid = unitRid;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TbTjJcTask getJcTask() {
        return jcTask;
    }

    public void setJcTask(TbTjJcTask jcTask) {
        this.jcTask = jcTask;
    }

    public Integer getSearchYear() {
        return searchYear;
    }

    public void setSearchYear(Integer searchYear) {
        this.searchYear = searchYear;
    }

    public List<Integer> getSearchYearList() {
        return searchYearList;
    }

    public void setSearchYearList(List<Integer> searchYearList) {
        this.searchYearList = searchYearList;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchInstitutionCode() {
        return searchInstitutionCode;
    }

    public void setSearchInstitutionCode(String searchInstitutionCode) {
        this.searchInstitutionCode = searchInstitutionCode;
    }

    public String getSelectIndusTypeIds() {
        return selectIndusTypeIds;
    }

    public void setSelectIndusTypeIds(String selectIndusTypeIds) {
        this.selectIndusTypeIds = selectIndusTypeIds;
    }

    public String getSelectIndusTypeNames() {
        return selectIndusTypeNames;
    }

    public void setSelectIndusTypeNames(String selectIndusTypeNames) {
        this.selectIndusTypeNames = selectIndusTypeNames;
    }

    public String getSelectEconomyIds() {
        return selectEconomyIds;
    }

    public void setSelectEconomyIds(String selectEconomyIds) {
        this.selectEconomyIds = selectEconomyIds;
    }

    public String getSelectEconomyNames() {
        return selectEconomyNames;
    }

    public void setSelectEconomyNames(String selectEconomyNames) {
        this.selectEconomyNames = selectEconomyNames;
    }

    public String getSelectCrptSizeIds() {
        return selectCrptSizeIds;
    }

    public void setSelectCrptSizeIds(String selectCrptSizeIds) {
        this.selectCrptSizeIds = selectCrptSizeIds;
    }

    public List<TsSimpleCode> getCrptSizeList() {
        return crptSizeList;
    }

    public void setCrptSizeList(List<TsSimpleCode> crptSizeList) {
        this.crptSizeList = crptSizeList;
    }

    public String getSearchPsnName() {
        return searchPsnName;
    }

    public void setSearchPsnName(String searchPsnName) {
        this.searchPsnName = searchPsnName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public Integer[] getSearchState() {
        return searchState;
    }

    public void setSearchState(Integer[] searchState) {
        this.searchState = searchState;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String getSimpleCodeOpType() {
        return simpleCodeOpType;
    }

    public void setSimpleCodeOpType(String simpleCodeOpType) {
        this.simpleCodeOpType = simpleCodeOpType;
    }

    public Integer getSimpleCodeOpTag() {
        return simpleCodeOpTag;
    }

    public void setSimpleCodeOpTag(Integer simpleCodeOpTag) {
        this.simpleCodeOpTag = simpleCodeOpTag;
    }

    public Map<String, Integer> getEditOnguadrStateMap() {
        return editOnguadrStateMap;
    }

    public void setEditOnguadrStateMap(Map<String, Integer> editOnguadrStateMap) {
        this.editOnguadrStateMap = editOnguadrStateMap;
    }

    public Integer getWhileOnDutyRid() {
        return whileOnDutyRid;
    }

    public void setWhileOnDutyRid(Integer whileOnDutyRid) {
        this.whileOnDutyRid = whileOnDutyRid;
    }

    public Integer getIdcTypeRid() {
        return idcTypeRid;
    }

    public void setIdcTypeRid(Integer idcTypeRid) {
        this.idcTypeRid = idcTypeRid;
    }

    public Boolean getIfMultipleFactors() {
        return ifMultipleFactors;
    }

    public void setIfMultipleFactors(Boolean ifMultipleFactors) {
        this.ifMultipleFactors = ifMultipleFactors;
    }
}
