package com.chis.modules.heth.comm.entity;

import javax.persistence.*;

import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-4-18
 */
@Entity
@Table(name = "TB_TJ_JC_TASK_PSN")
@SequenceGenerator(name = "TbTjJcTaskPsn", sequenceName = "TB_TJ_JC_TASK_PSN_SEQ", allocationSize = 1)
public class TbTjJcTaskPsn implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private TbTjJcTask fkByMainId;
    private String psnName;
    private TsSimpleCode fkByCardTypeId;
    private String idc;
    private TsSimpleCode fkByOnguardStateid;
    private TsSimpleCode fkByPostId;
    private String rmk;
    private String bhkCode;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    /**
     * 职业病主动监测因素
     */
    private List<TbTjJcTaskBadrsn> jcTaskBadrsnList;
    /**
     * 在岗状态ID
     */
    private Integer onguadrStateId;
    /**
     * 岗位ID
     */
    private Integer postId;
    /**
     * 岗位名称
     */
    private String postName;
    /**
     * 职业病主动监测因素ID
     */
    private String rsnId;
    /**
     * 职业病主动监测因素名称
     */
    private String rsnName;

    public TbTjJcTaskPsn() {
    }

    public TbTjJcTaskPsn(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjJcTaskPsn")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "MAIN_ID")
    public TbTjJcTask getFkByMainId() {
        return fkByMainId;
    }

    public void setFkByMainId(TbTjJcTask fkByMainId) {
        this.fkByMainId = fkByMainId;
    }

    @Column(name = "PSN_NAME")
    public String getPsnName() {
        return psnName;
    }

    public void setPsnName(String psnName) {
        this.psnName = psnName;
    }

    @ManyToOne
    @JoinColumn(name = "CARD_TYPE_ID")
    public TsSimpleCode getFkByCardTypeId() {
        return fkByCardTypeId;
    }

    public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
        this.fkByCardTypeId = fkByCardTypeId;
    }

    @Column(name = "IDC")
    public String getIdc() {
        return idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    @ManyToOne
    @JoinColumn(name = "ONGUARD_STATEID")
    public TsSimpleCode getFkByOnguardStateid() {
        return fkByOnguardStateid;
    }

    public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
        this.fkByOnguardStateid = fkByOnguardStateid;
    }

    @ManyToOne
    @JoinColumn(name = "POST_ID")
    public TsSimpleCode getFkByPostId() {
        return fkByPostId;
    }

    public void setFkByPostId(TsSimpleCode fkByPostId) {
        this.fkByPostId = fkByPostId;
    }

    @Column(name = "RMK")
    public String getRmk() {
        return rmk;
    }

    public void setRmk(String rmk) {
        this.rmk = rmk;
    }

    @Column(name = "BHK_CODE")
    public String getBhkCode() {
        return bhkCode;
    }

    public void setBhkCode(String bhkCode) {
        this.bhkCode = bhkCode;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    @OrderBy(value = "rid")
    public List<TbTjJcTaskBadrsn> getJcTaskBadrsnList() {
        return jcTaskBadrsnList;
    }

    public void setJcTaskBadrsnList(List<TbTjJcTaskBadrsn> jcTaskBadrsnList) {
        this.jcTaskBadrsnList = jcTaskBadrsnList;
    }

    @Transient
    public Integer getOnguadrStateId() {
        return onguadrStateId;
    }

    public void setOnguadrStateId(Integer onguadrStateId) {
        this.onguadrStateId = onguadrStateId;
    }

    @Transient
    public Integer getPostId() {
        return postId;
    }

    public void setPostId(Integer postId) {
        this.postId = postId;
    }

    @Transient
    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    @Transient
    public String getRsnId() {
        return rsnId;
    }

    public void setRsnId(String rsnId) {
        this.rsnId = rsnId;
    }

    @Transient
    public String getRsnName() {
        return rsnName;
    }

    public void setRsnName(String rsnName) {
        this.rsnName = rsnName;
    }
}