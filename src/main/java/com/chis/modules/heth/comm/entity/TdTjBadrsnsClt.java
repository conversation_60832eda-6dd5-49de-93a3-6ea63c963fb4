package com.chis.modules.heth.comm.entity;

import java.util.Date;

import javax.persistence.*;

import com.chis.modules.heth.comm.logic.TdTjContraindCltWeb;
import com.chis.modules.heth.comm.logic.TdTjSupoccdiseCltWeb;
import com.chis.modules.system.entity.TsSimpleCode;

/**
 * @Description : 接触危害因素（数据录入）
 * @ClassAuthor: anjing
 * @Date : 2019/5/14 10:04
 **/
@Entity
@Table(name = "TD_TJ_BADRSNS_CLT", uniqueConstraints = @UniqueConstraint(columnNames = {"BHK_ID", "BADRSN_ID" }))
@SequenceGenerator(name = "TdTjBadrsnsClt_Seq", sequenceName = "TD_TJ_BADRSNS_CLT_SEQ", allocationSize = 1)
public class TdTjBadrsnsClt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**主键Id*/
    private Integer rid;
    /**关联：体检主表-录入数据（体检主表Id）*/
    private TdTjBhkClt fkByBhkId;
    /**关联：码表（危害因素Id）*/
    private TsSimpleCode fkByBadrsnId;
    /**创建日期*/
    private Date createDate;
    /**创建人*/
    private Integer createManid;
    /**修改日期*/
    private Date modifyDate;
    /**修改人*/
    private Integer modifyManid;

    private TsSimpleCode fkByExamConclusionId;

    private String qtjbName;
    private Integer examConclusionId;
    /** 职业禁忌症*/
    private TdTjContraindCltWeb contraindCltWeb;
    /** 疑似职业病*/
    private TdTjSupoccdiseCltWeb supoccdiseCltWeb;

    public TdTjBadrsnsClt() {
    }

    public TdTjBadrsnsClt(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjBadrsnsClt_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "BHK_ID" )
    public TdTjBhkClt getFkByBhkId() {
        return fkByBhkId;
    }

    public void setFkByBhkId(TdTjBhkClt fkByBhkId) {
        this.fkByBhkId = fkByBhkId;
    }

    @ManyToOne
    @JoinColumn(name = "BADRSN_ID" )
    public TsSimpleCode getFkByBadrsnId() {
        return fkByBadrsnId;
    }

    public void setFkByBadrsnId(TsSimpleCode fkByBadrsnId) {
        this.fkByBadrsnId = fkByBadrsnId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @ManyToOne
    @JoinColumn(name = "EXAM_CONCLUSION_ID" )
    public TsSimpleCode getFkByExamConclusionId() {
        return fkByExamConclusionId;
    }

    public void setFkByExamConclusionId(TsSimpleCode fkByExamConclusionId) {
        this.fkByExamConclusionId = fkByExamConclusionId;
    }

    @Column(name = "QTJB_NAME")
    public String getQtjbName() {
        return qtjbName;
    }

    public void setQtjbName(String qtjbName) {
        this.qtjbName = qtjbName;
    }

    @Transient
    public Integer getExamConclusionId() {
        return examConclusionId;
    }

    public void setExamConclusionId(Integer examConclusionId) {
        this.examConclusionId = examConclusionId;
    }

    @Transient
    public TdTjContraindCltWeb getContraindCltWeb() {
        return contraindCltWeb;
    }

    public void setContraindCltWeb(TdTjContraindCltWeb contraindCltWeb) {
        this.contraindCltWeb = contraindCltWeb;
    }

    @Transient
    public TdTjSupoccdiseCltWeb getSupoccdiseCltWeb() {
        return supoccdiseCltWeb;
    }

    public void setSupoccdiseCltWeb(TdTjSupoccdiseCltWeb supoccdiseCltWeb) {
        this.supoccdiseCltWeb = supoccdiseCltWeb;
    }
}
