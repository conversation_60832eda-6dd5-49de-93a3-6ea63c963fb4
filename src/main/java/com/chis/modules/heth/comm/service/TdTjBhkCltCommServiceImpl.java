package com.chis.modules.heth.comm.service;

import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.Reflections;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.sm.Sm4Util;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Description : 体检信息（数据录入）—业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/15 14:59
 **/
@Service
@Transactional(readOnly = true)
public class TdTjBhkCltCommServiceImpl extends AbstractTemplate {

    /***
     * @Description : 根据体检编号、单位Id查询体检主表（录入数据）集合
     * @MethodAuthor: anjing
     * @Date : 2019/5/29 14:11
     **/
    public List<TdTjBhkClt> selectBhkCltListByBhkCode(String bhkCode) {
        if(StringUtils.isNotBlank(bhkCode)) {
            StringBuilder sb = new StringBuilder("");
            sb.append(" select t from TdTjBhkClt t");
            sb.append(" where t.bhkCode = '").append(bhkCode).append("'");
            sb.append(" and t.fkByBhkorgId.tsUnit.rid =").append(Global.getUser().getTsUnit().getRid());
            sb.append(" and t.delMark = 0 ");
            return em.createQuery(sb.toString()).getResultList();
        } else {
            return null;
        }
    }

    /**
     * @Description : 根据Rid获取体检主表信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 14:32
     **/
    public TdTjBhkClt selectBhkCltByRid(Integer rid) {
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TdTjBhkClt t where 1=1 and t.rid=").append(rid);
        List<TdTjBhkClt> list = em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            for (TdTjBhkClt clt: list) {
                clt.getTdTjBadrsnsClts().size();
                clt.getTdTjEmhistoryClts().size();
                clt.getTdTjAnamnesisClts().size();
                clt.getTdTjExmsdataClts().size();
                clt.getTdTjSymptomClts().size();
                clt.getTdTjBhksubClts().size();
                clt.getTdTjMhkrstClts().size();
                clt.getTdTjSupoccdiseClts().size();
                clt.getTdTjContraindClts().size();
                clt.getTdTjChiefDoctorList().size();
                clt.getTdTjTchBadrsnsClts().size();
            }
            return list.get(0);
        }
        return null;
    }

    /**
     * @Description : 证件类型与号码查找人员信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 15:45
     **/
    public List<TdTjPerson> selectPersonInfoByIdc(String cardType, String idc) {
        StringBuilder sql = new StringBuilder("");
        sql.append(" select t from TdTjPerson t where t.idc =:idc");
        sql.append(" and t.fkByCardTypeId.rid =:cardType");
        Query query =em.createQuery(sql.toString());
        query.setParameter("idc",idc);
        query.setParameter("cardType",null == cardType ? null : Integer.parseInt(cardType));// 注意 一定要转换 否则会转换异常错误
        return query.getResultList();
    }

    /**
     * @Description : 新增或者更新人员信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/17 13:22
     **/
    public void saveOrUpdatePersonInfo(TdTjPerson tdTjPerson) {
        Object rid = Reflections.getFieldValue(tdTjPerson, "rid");
        if (rid == null) {
            preInsert(tdTjPerson);
            saveObj(tdTjPerson);
        } else {
            updateObj(tdTjPerson);
        }
    }

    /**
     * @Description : 基本信息-添加/修改
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 9:51
     **/
    @Transactional(readOnly = false)
    public void saveOrUpdate(TdTjBhkClt entity) {
        // 根据身份证号查找人员信息表，存在更新，不存在新增
        TdTjPerson tjPerson = null;
        List<TdTjPerson> personList = new ArrayList<>();
        //，若无证件号码，则沿用之前，若有证件号码，需要根据证件类型+证件号码重新查询，重新关联 若未查询到，则新增
        if(StringUtils.isNotBlank(entity.getIdc())){
            personList = selectPersonInfoByIdc(entity.getFkByCardTypeId().getRid().toString(),entity.getIdc());
            if(!CollectionUtils.isEmpty(personList)){
                tjPerson = personList.get(0);
            }
        }else if(null != entity.getFkByPersonId() && null != entity.getFkByPersonId().getRid()){
            tjPerson = this.em.find(TdTjPerson.class, entity.getFkByPersonId().getRid());
            //无证件号码，判断对比两次的证件类型
            if(tjPerson != null && !"88".equals(tjPerson.getFkByCardTypeId().getCodeNo())){
                //若以前的人员不是无证件号码的，则需要重新保存
                tjPerson = null;
            }
        }
        if(null ==tjPerson && CollectionUtils.isEmpty(personList)) {
            tjPerson = new TdTjPerson();
        }
        //更新对应字段
        tjPerson.setIdc(entity.getIdc());
        tjPerson.setPersonName(entity.getPersonName());
        tjPerson.setSex(entity.getSex());
        tjPerson.setBrth(entity.getBrth());
        tjPerson.setAge(entity.getAge());
        tjPerson.setIsxmrd(entity.getIsxmrd());
        tjPerson.setJob(entity.getWorkName());
        tjPerson.setDept(entity.getDpt());
        tjPerson.setJobcod(entity.getWrknum());
        tjPerson.setTel(entity.getLnktel());
        tjPerson.setFkByCardTypeId(entity.getFkByCardTypeId());
        // 保存关联人员信息
        saveOrUpdatePersonInfo(tjPerson);
        entity.setFkByPersonId(tjPerson);
        if(!CollectionUtils.isEmpty(entity.getTdTjTchBadrsnsClts())&&entity.getRid()!=null){
            this.executeSql("DELETE FROM TD_TJ_TCH_BADRSNS_CLT WHERE BHK_ID = "+entity.getRid(), null);
        }
        //防护用品佩戴情况
        if(new Integer("2").equals(entity.getJcType())){
            if(entity.getFkByProtectEquId()!=null && entity.getFkByProtectEquId().getRid()==null){
                entity.setFkByProtectEquId(null);
            }
        }else{
            entity.setFkByProtectEquId(null);
        }
        super.upsertEntity(entity);
    }

    /**
     * @Description : 体检问诊-添加/修改
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 15:47
     **/
    @Transactional(readOnly = false)
    public void saveExmsData(TdTjBhkClt entity) {
        if(!CollectionUtils.isEmpty(entity.getTdTjTchBadrsnsClts())&&entity.getRid()!=null){
            this.executeSql("DELETE FROM TD_TJ_TCH_BADRSNS_CLT WHERE BHK_ID = "+entity.getRid(), null);
        }
        if(new Integer("2").equals(entity.getJcType())){
            if(entity.getFkByProtectEquId()!=null && entity.getFkByProtectEquId().getRid()==null){
                entity.setFkByProtectEquId(null);
            }
        }else{
            entity.setFkByProtectEquId(null);
        }
        super.upsertEntity(entity);
    }

    /**
     * @Description : 体检结果-添加/修改
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 16:51
     **/
    @Transactional(readOnly = false)
    public void saveSymptom(TdTjBhkClt entity) {
        if(!CollectionUtils.isEmpty(entity.getTdTjTchBadrsnsClts())&&entity.getRid()!=null){
            this.executeSql("DELETE FROM TD_TJ_TCH_BADRSNS_CLT WHERE BHK_ID = "+entity.getRid(), null);
        }
        if(new Integer("2").equals(entity.getJcType())){
            if(entity.getFkByProtectEquId()!=null && entity.getFkByProtectEquId().getRid()==null){
                entity.setFkByProtectEquId(null);
            }
        }else{
            entity.setFkByProtectEquId(null);
        }
        super.upsertEntity(entity);
    }

    /**
     * @Description : 根据体检主表Id获取危害因素集合
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 10:30
     **/
    public List<TdTjBadrsnsClt> selectBadrsnsCltListByBhkId(Integer bhkId) {
        List<TdTjBadrsnsClt> badrsnsCltList = new ArrayList<>();
        if (null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjBadrsnsClt t ");
            sb.append(" where t.fkByBhkId.rid = ").append(bhkId);
            badrsnsCltList = CollectionUtil.castList(TdTjBadrsnsClt.class, findHqlResultList(sb.toString()));
        }
        return badrsnsCltList;
    }

    /**
     * @Description : 根据体检主表Id获取体检子表集合
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 10:30
     **/
    public List<TdTjBhksubClt> selectBhkSubCltListByBhkId(Integer bhkId) {
        if(null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjBhksubClt t ");
            sb.append(" where t.fkByBhkId.rid = ").append(bhkId);
            //sb.append(" order by t.isMust DESC,t.fkByItemId.itemCode");
            sb.append(" order by t.fkByItemId.tsSimpleCode.num, t.fkByItemId.tsSimpleCode.codeNo, t.fkByItemId.num, t.fkByItemId.itemCode");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
     * @Description : 根据项目Id获取定性项目描述集合
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 17:42
     **/
    public List<TbTjRstdesc> selectRstdescListByItemId(Integer itemId) {
        if(null != itemId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TbTjRstdesc t ");
            sb.append(" where t.tbTjItems.rid = ").append(itemId);
            sb.append(" order by t.tbTjItems.itemCode");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    public Integer countBadrsnsIdByBhkId(Integer bhkId) {
        StringBuilder sb=new StringBuilder();
        sb.append(" SELECT COUNT(BADRSN_ID) ");
        sb.append(" FROM TD_TJ_BADRSNS_CLT ");
        sb.append(" WHERE BHK_ID = ").append(bhkId);
        List<BigDecimal> list = em.createNativeQuery(sb.toString()).getResultList();
        Integer countObj = 0;
        if(!CollectionUtils.isEmpty(list)) {
            countObj = list.get(0)==null?0:list.get(0).intValue();
        }
        return countObj;
    }

    /**
     * @Description : 根据项目Id、性别、危害因素Id、在岗状态Id获取关联危害因素Id数量
     * @MethodAuthor: anjing
     * @Date : 2019/5/27 17:10
     **/
    public Integer countBadrsnsIdByBadrsnIdAndOnguardStateIdAndItemIdAndSex(Integer itemId, String badRsnIds, Integer stateId, String sex) {
        StringBuilder sb=new StringBuilder();
        sb.append(" SELECT COUNT(DISTINCT BAD_RSN_ID) ");
        sb.append(" FROM TB_TJ_ITEMS_SPE  ");
        sb.append(" WHERE ITEM_ID = ").append(itemId);
        sb.append(" AND BAD_RSN_ID IN (").append(badRsnIds).append(")");
        sb.append(" AND ONGUARD_STATEID = ").append(stateId);
        sb.append(" AND (sex IS NULL OR SEX = '").append(sex).append("') ");
        List<BigDecimal> list = em.createNativeQuery(sb.toString()).getResultList();
        Integer countObj = 0;
        if(!CollectionUtils.isEmpty(list)) {
            countObj = list.get(0)==null?0:list.get(0).intValue();
        }
        return countObj;
    }

    /**
     * @Description : 根据危害因素Id、在岗状态Id获取特殊标准
     * @MethodAuthor: anjing
     * @Date : 2019/5/27 10:37
     **/
    public List<TbTjItemsSpe> selectItemSpeListByBadrsnIdAndOnguardStateIdAndItemIdAndSex(Integer itemId, String badRsnIds, Integer stateId, String sex) {
        if(StringUtils.isNotBlank(badRsnIds) && null != stateId && null != itemId && StringUtils.isNotBlank(sex)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TbTjItemsSpe t ");
            sb.append(" where t.fkByBadRsnId.rid in (").append(badRsnIds).append(")");
            sb.append(" and t.fkByOnguardStateid.rid = ").append(stateId);
            sb.append(" AND (t.sex is null or t.sex = '").append(sex).append("') ");
            sb.append(" and t.fkByItemId.rid = ").append(itemId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    public List<TbTjRstdesc> selectRstdescListByBadrsnIdAndOnguardStateId(String badRsnIds, Integer stateId) {
        List<TbTjRstdesc> list = new ArrayList<>();
        if(StringUtils.isNotBlank(badRsnIds) && null != stateId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT DISTINCT T.RID, T.ITEM_ID, T.RST_DESC, T.EGB_TAG, T.NUM");
            sb.append(" FROM TB_TJ_RSTDESC T  ");
            sb.append(" INNER JOIN TB_TJ_ITEMS T1 ON T.ITEM_ID = T1.RID ");
//            sb.append(" INNER JOIN TB_ZWTJ_ITEMCMB_ITEMS T2 ON T2.ITEM_ID = T1.RID ");
//            sb.append(" INNER JOIN TB_ZWTJ_SCHEME_ITEMS T3 ON T2.ITEM_CMBID = T3.ITEM_CMBID  ");
//            sb.append(" INNER JOIN TB_ZWTJ_MAINSTD T4 ON T3.SCHEME_ID = T4.RID ");
//            sb.append(" WHERE T4.STOP_TAG = 1 ");
            sb.append(" WHERE 1 = 1 ");
//            sb.append(" AND T3.STOP_TAG = 1 ");
//            sb.append(" AND T1.STOP_TAG = 1 ");
//            sb.append(" AND T4.BADRSN_ID IN (").append(badRsnIds).append(")");
//            sb.append(" AND T4.WORK_STATEID = ").append(stateId);
            sb.append(" ORDER BY T.NUM ASC");
            List<Object[]> itemList = em.createNativeQuery(sb.toString()).getResultList();
            for(Object[] obj : itemList) {
                TbTjRstdesc tbTjRstdesc = new TbTjRstdesc();
                tbTjRstdesc.setRid(Integer.parseInt(obj[0].toString()));
                TbTjItems tbTjItems = new TbTjItems();




                tbTjItems.setRid(Integer.parseInt(obj[1].toString()));
                tbTjRstdesc.setTbTjItems(tbTjItems);
                tbTjRstdesc.setRstDesc(StringUtils.objectToString(obj[2]));
                tbTjRstdesc.setEgbTag(Short.parseShort(obj[3].toString()));
                list.add(tbTjRstdesc);
            }
        }
        return list;
    }

    public List<Object[]> selectSupoccdiseListByBadrsnIdAndOnguardStateId(String badRsnIds, Integer stateId) {
        if(StringUtils.isNotBlank(badRsnIds) && null != stateId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT T2.*, t1.BADRSN_ID ");
            sb.append(" FROM TB_ZWTJ_JJZS T ");
            sb.append(" INNER JOIN TB_ZWTJ_MAINSTD T1 ON T.SCHEME_ID = T1.RID ");
            sb.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T.CONTRAIND_ID = T2.RID ");
            sb.append(" WHERE T1.BADRSN_ID IN ( ").append(badRsnIds).append(")");
            sb.append(" AND T1.WORK_STATEID = ").append(stateId);
            sb.append(" AND T1.STOP_TAG = 1 ");
            sb.append(" AND T.STOP_TAG = 1 ");
            return  em.createNativeQuery(sb.toString()).getResultList();
        } else {
            return null;
        }
    }

    /***
     *  <p>方法描述：根据危害因素和在岗状态获取禁忌症码表</p>
     *
     * @MethodAuthor maox,2019年5月29日,findContraindClts
     * @param badRsnId
     * @param workStateId
     * @return
     */
    public List<TsSimpleCode> findContraindClts(Integer badRsnId, Integer workStateId){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TbZwtjMainstd t where t.stopTag=1 and t.tsSimpleCodeByBadrsnId.rid=").append(badRsnId);
        sb.append(" and t.tsSimpleCodeByWorkStateid.rid =").append(workStateId);
        List<TbZwtjMainstd> list = em.createQuery(sb.toString()).getResultList();
        List<TsSimpleCode> contraindClts = new ArrayList<TsSimpleCode>();
        if(null != list && list.size()>0){
            for (TbZwtjMainstd clt: list) {
                clt.getTbZwtjJjzses().size();
                List<TbZwtjJjzs> jjzs =  clt.getTbZwtjJjzses();
                if(jjzs != null && jjzs.size()>0){
                    for(TbZwtjJjzs jjz :jjzs){
                        contraindClts.add(jjz.getTsSimpleCode());
                    }
                }
            }

            return contraindClts;
        }
        return null;
    }

    /***
     *  <p>方法描述：根据危害因素和在岗状态获取疑似职业病码表</p>
     *
     * @MethodAuthor maox,2019年5月29日,findOccdiseClts
     * @param badRsnId
     * @param workStateId
     * @return
     */
    public List<TsSimpleCode> findOccdiseClts(Integer badRsnId,Integer workStateId){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TbZwtjMainstd t where t.stopTag=1 and t.tsSimpleCodeByBadrsnId.rid=").append(badRsnId);
        sb.append(" and t.tsSimpleCodeByWorkStateid.rid =").append(workStateId);
        List<TbZwtjMainstd> list = em.createQuery(sb.toString()).getResultList();
        List<TsSimpleCode> occdiseClts = new ArrayList<TsSimpleCode>();
        if(null != list && list.size()>0){
            for (TbZwtjMainstd clt: list) {
                clt.getTbZwtjOccdiseses().size();
                List<TbZwtjOccdises> occdises =  clt.getTbZwtjOccdiseses();
                if(occdises != null && occdises.size()>0){
                    for(TbZwtjOccdises occdise :occdises){
                        occdiseClts.add(occdise.getTsSimpleCode());
                    }
                }
            }

            return occdiseClts;
        }
        return null;
    }
    /***
     *  <p>方法描述：体检结论按照扩展字段2排序</p>
     *
     * @MethodAuthor maox,2019年5月29日,findallSimpleCodesByTypeIdOrderByExtends2
     * @param typeNo
     * @return
     */
    @Transactional(readOnly = true)
    public List<TsSimpleCode> findallSimpleCodesByTypeIdOrderByExtends2(String typeNo) {
        if (StringUtils.isNotBlank(typeNo)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TsSimpleCode t ");
            sb.append(" where t.tsCodeType.codeTypeName in (").append(typeNo).append(")");
            sb.append(" order by t.extendS2,t.codeNo ");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /***
     *  <p>方法描述：根据扩展字段2查询体检结论</p>
     *
     * @MethodAuthor maox,2019年5月29日,findTsSimpleCodeByextendS2
     * @return
     */
    @Transactional(readOnly = true)
    public TsSimpleCode findTsSimpleCodeByextendS2(Integer extendS2){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TsSimpleCode t where t.extendS2 =").append(extendS2);
        sb.append(" and t.tsCodeType.codeTypeName in (").append(5005).append(")");
        List<TsSimpleCode> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /***
     *  <p>方法描述：根据扩展字段1查询体检结论</p>
     *
     * @MethodAuthor maox,2019年5月29日,findTsSimpleCodeByextendS1
     * @return
     */
    @Transactional(readOnly = true)
    public TsSimpleCode findTsSimpleCodeByextendS1(Integer extendS1){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TsSimpleCode t where t.extendS1 ='").append(extendS1).append("'");
        sb.append(" and t.tsCodeType.codeTypeName in (").append(5005).append(")");
        List<TsSimpleCode> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /***
     *  <p>方法描述：根据id获取资质人员实体</p>
     *
     * @MethodAuthor maox,2019年5月30日,findTdZwPsninfobyID
     * @param rid
     * @return
     */
    public TdZwPsninfoComm findTdZwPsninfobyID(Integer rid){
        return em.find(TdZwPsninfoComm.class, rid);
    }

    /***
     *  <p>方法描述：保存体检主表实体</p>
     *
     * @MethodAuthor maox,2019年5月30日,saveOrUpdateTdTjBhkClt
     * @param entity
     */
    @Transactional(readOnly = false)
    public void saveOrUpdateTdTjBhkClt(TdTjBhkClt entity) {
        if(!CollectionUtils.isEmpty(entity.getTdTjTchBadrsnsClts())&&entity.getRid()!=null){
            this.executeSql("DELETE FROM TD_TJ_TCH_BADRSNS_CLT WHERE BHK_ID = "+entity.getRid(), null);
        }
        if(entity.getRid() ==null){
            saveObj(entity);
        }else{
            update(entity);
        }
    }

    /***
     *  <p>方法描述：根据体检编号找到体检主表</p>
     *
     * @MethodAuthor maox,2019年6月1日,findTdTjBhkByBhkcode
     * @param uuid
     * @return
     */
    @Transactional(readOnly = true)
    public TdTjBhk findTdTjBhkByUUID(String uuid){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TdTjBhk t where t.uuid = '").append(uuid).append("'");
        List<TdTjBhk> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /***
     *  <p>方法描述：业务体检表</p>
     *
     * @MethodAuthor maox,2019年6月1日,saveTdTjBhk
     * @param entity
     */
    @Transactional(readOnly = false)
    public void saveTdTjBhk(TdTjBhk entity) {
        saveObj(entity);
    }

    /***
     *  <p>方法描述：更新人员信息</p>
     *
     * @MethodAuthor maox,2019年6月3日,update
     * @param persion
     */
    @Transactional(readOnly = false)
    public void updateTdTjPerson(TdTjPerson persion){
        update(persion);
    }

    /***
     *  <p>方法描述：根据id找资质人员</p>
     *
     * @MethodAuthor maox,2019年6月5日,findTdZwPsninfoByID
     * @param rid
     * @return
     */
    @Transactional(readOnly = true)
    public TdZwPsninfoComm findTdZwPsninfoByID(Integer rid){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TdZwPsninfoComm t where t.rid =").append(rid);
        List<TdZwPsninfoComm> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /***
     *  <p>方法描述：找人员</p>
     *
     * @MethodAuthor maox,2019年6月5日,findTdTjPersonByID
     * @param rid
     * @return
     */
    @Transactional(readOnly = true)
    public TdTjPerson findTdTjPersonByID(Integer rid){
        return em.find(TdTjPerson.class, rid);
    }

    /***
     *  <p>方法描述：记录是否被引用</p>
     *
     * @MethodAuthor maox,2019年6月5日,findTdTjBhkListByBhkcode
     * @return
     */
   /* @Transactional(readOnly = true)
   	public List<TdTjBhk> findTdTjBhkListByBhkcode1(String lastbhkcode){
   		StringBuilder sb=new StringBuilder();
   		sb.append("select t from TdTjBhk t where t.lastBhkCode = '").append(lastbhkcode).append("'");
   		List<TdTjBhk> list=em.createQuery(sb.toString()).getResultList();
   		return list;
   	}*/

    @Transactional(readOnly = true)
    public List<Object[]> findRelateBhkList(String bhkIds){
        StringBuffer sb = new StringBuffer();
        sb.append("select b.LAST_BHK_ID,b.rid from  TD_TJ_BHK_CLT b ");
        sb.append(" where b.DEL_MARK =0  and b.LAST_BHK_ID in (").append(bhkIds).append(")");
        return em.createNativeQuery(sb.toString()).getResultList();
    }

    @Transactional(readOnly = true)
    public TsZone findTsZoneByZoneGb(String zoneGb){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TsZone t where t.zoneGb = '").append(zoneGb).append("' ");
        List<TsZone> list = em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
    * @Description : 根据所属机构Id获取标准值配置状态表
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 9:57
    **/
    @Transactional(readOnly = true)
    public TbTjStadItemsStatus findTbTjStadItemsStatusByOrgId(String orgId){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TbTjStadItemsStatus t where t.fkByOrgId.rid = '").append(orgId).append("' ");
        List<TbTjStadItemsStatus> list = em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
    * @Description : 根据所属机构Id获取标准值配置
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 15:30
    **/
    public List<TbTjStadItems> findTbTjStadItemsByOrgId(String orgId,String sex) {
        List<TbTjStadItems> list = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT T.RID, T.MAIN_ID, T.ITEM_ID, T.MINVAL, T.MAXVAL,T.JDGPTN,T.SEX,T.MSRUNT_ID,T2.CODE_NAME ");
        sb.append(" FROM TB_TJ_STAD_ITEMS T ");
        sb.append(" LEFT JOIN TB_TJ_STAD_ITEMS_STATUS T1 ON T.MAIN_ID = T1.RID ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T.MSRUNT_ID = T2.RID ");
        sb.append(" WHERE T1.STATE = 1 AND T1.ORG_ID = '").append(orgId).append("' ");
        if("男".equals(sex)) {
    		sb.append(" AND NVL(T.SEX, 0) IN (0,1)");
        } else {
        	sb.append(" AND NVL(T.SEX, 0) IN (0,2)");
        }
        List<Object[]> objList = em.createNativeQuery(sb.toString()).getResultList();
        if(!CollectionUtils.isEmpty(objList)) {
            for(Object[] obj : objList) {
                TbTjStadItems tbTjStadItems = new TbTjStadItems();
                tbTjStadItems.setRid(Integer.valueOf(obj[0].toString()));
                tbTjStadItems.setFkByMainId(new TbTjStadItemsStatus(Integer.valueOf(obj[1].toString())));
                tbTjStadItems.setFkByItemId(new TbTjItems(Integer.valueOf(obj[2].toString())));
                tbTjStadItems.setMinval(obj[3]==null?null:new BigDecimal(obj[3].toString()));
                tbTjStadItems.setMaxval(obj[4]==null?null:new BigDecimal(obj[4].toString()));
                if (null!=obj[5]) {
                	tbTjStadItems.setJdgptn(new Integer(obj[5].toString()));
                }
                if (null!=obj[6]) {
                	tbTjStadItems.setSex(new Integer(obj[6].toString()));
				}
                if (null!=obj[7]) {
                	TsSimpleCode msrunt = new TsSimpleCode();
                	msrunt.setRid(new Integer(obj[7].toString()));
                	msrunt.setCodeName(StringUtils.objectToString(obj[8]));
                	tbTjStadItems.setFkByMsruntId(msrunt);
				}
                list.add(tbTjStadItems);
            }
        }
        return list;
    }

    @Transactional(readOnly = true)
    public TsSimpleCode findTsSimpleCode(String codeName,String code){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TsSimpleCode t where 1=1 and t.tsCodeType.codeTypeName in (").append(codeName).append(")");
         sb.append(" and t.codeNo = ").append(code);

        List<TsSimpleCode> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    @Transactional(readOnly = true)
    public TdTjBhk findTdTjBhkByBhkCode(String bhkCode){
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TdTjBhk t where t.bhkCode = '").append(bhkCode).append("'");
        List<TdTjBhk> list=em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    @Transactional(readOnly = true)
    public List<Object[]> findSimpleByParam(String typeNo, String rids) {
        StringBuffer buffer = new StringBuffer();
        Map<String, Object> paramMap  = new HashMap<>();
        buffer.append(
                "SELECT T.RID,T.CODE_NAME,T.CODE_NO,T.CODE_LEVEL_NO,T.SPLSHT,T.EXTENDS1,T.EXTENDS2 FROM TS_SIMPLE_CODE T LEFT JOIN TS_CODE_TYPE T1 ON T1.RID = T.CODE_TYPE_ID WHERE 1=1 ");
        buffer.append(" AND T1.CODE_TYPE_NAME = :CODE_TYPE_NAME");
        paramMap.put("CODE_TYPE_NAME",  StringUtils.trim(typeNo) );
        buffer.append(" AND T.IF_REVEAL = 1 ");
        if (StringUtils.isNotBlank(rids)) {
            buffer.append("AND T.RID IN (:RID)");
            List<String> list = Arrays.asList(rids.split(","));
            paramMap.put("RID",  list );
        }else {
            buffer.append("and 1=2");
        }
        buffer.append(" order by t.num,t.CODE_NO");
        List<Object[]> sqlResultList = super.findDataBySqlNoPage(buffer.toString(),paramMap);
        return sqlResultList;
    }

    @Transactional(readOnly = true)
    public List<TsSimpleCode> findSimpleCodesByTypeNo(String typeNo) {
        if (StringUtils.isNotBlank(typeNo)) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select new TsSimpleCode(t.rid, t.codeName, t.tsCodeType.codeTypeName, t.codeLevelNo) from TsSimpleCode t ");
            sb.append(" where t.ifReveal=1 and t.tsCodeType.codeTypeName in (")
                    .append(typeNo)
                    .append(") order by t.num,t.codeLevelNo,t.codeNo");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
     * @Description : 根据危害因素Id、在岗状态Id获取职业禁忌证Rids
     * @MethodAuthor: anjing
     * @Date : 2021/3/25 11:05
     **/
    public String findContraindCltIds(Integer badRsnId, Integer workStateId){
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT WM_CONCAT(T.CONTRAIND_ID) ");
        sb.append(" FROM TB_ZWTJ_JJZS T ");
        sb.append(" LEFT JOIN TB_ZWTJ_MAINSTD T1 ON T.SCHEME_ID = T1.RID ");
        sb.append(" WHERE T1.STOP_TAG = 1 ");
        sb.append(" AND T1.BADRSN_ID = ").append(badRsnId);
        sb.append(" AND WORK_STATEID = ").append(workStateId);
        List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            if(null != list.get(0)) {
                return list.get(0).toString();
            }
        }
        return null;
    }

    /**
    * @Description : 根据危害因素Id、在岗状态Id获取疑似职业病Rids
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 11:24
    **/
    public String findOccdiseCltIds(Integer badRsnId, Integer workStateId) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT wm_concat(T.OCC_DISEID) ");
        sb.append(" FROM TB_ZWTJ_OCCDISES T ");
        sb.append(" LEFT JOIN TB_ZWTJ_MAINSTD T1 ON T.SCHEME_ID = T1.RID ");
        sb.append(" WHERE T1.STOP_TAG = 1 ");
        sb.append(" AND T1.BADRSN_ID = ").append(badRsnId);
        sb.append(" AND WORK_STATEID = ").append(workStateId);
        sb.append("  ");
        List<Object> list = em.createNativeQuery(sb.toString()).getResultList();
        if(null != list && list.size() > 0) {
            if(null != list.get(0)) {
                return list.get(0).toString();
            }
        }
        return null;
    }

    /**
     * @Description : 根据人员属性获取资质人员信息
     * @MethodAuthor: anjing
     * @Date : 2021/3/25 14:24
     **/
    public List<TdZwPsninfoComm> getTdZwPsninfoList(String entends1) {
        StringBuilder sb = new StringBuilder();
        TsUnit tsUnit = Global.getUser().getTsUnit();
        sb.append(" SELECT T.RID, T.EMP_NAME ");
        sb.append(" FROM TD_ZW_PSNINFO T ");
        sb.append(" INNER JOIN TD_ZW_TJORGPSNS T1 ON T1.EMP_ID = T.RID ");
        sb.append(" INNER JOIN TD_ZW_TJORGINFO T2 ON T1.ORG_ID = T2.RID ");
        sb.append(" INNER JOIN TD_ZW_PSN_TYPE T3 ON T3.MAIN_ID = T.RID ");
        sb.append(" INNER JOIN TS_SIMPLE_CODE T4 ON T3.PSN_TYPE = T4.RID ");
        sb.append(" WHERE 1=1 ");
        sb.append(" AND T2.STATE = 1 ");
        sb.append(" AND T2.ORG_ID = ").append(tsUnit.getRid());
        sb.append(" AND T1.ON_DUTY = 1 ");
        sb.append(" AND T4.EXTENDS3 IN (").append(entends1).append(")");
        sb.append(" GROUP BY T.RID, T.EMP_NAME ");
        List<Object[]> list = em.createNativeQuery(sb.toString()).getResultList();
        List<TdZwPsninfoComm> tdZwPsninfoCommList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(list)) {
            for(Object[] obj : list) {
                Integer rid = Integer.valueOf(obj[0].toString());
                String empName = StringUtils.objectToString(obj[1]);
                TdZwPsninfoComm tdZwPsninfoComm = new TdZwPsninfoComm(rid);
                tdZwPsninfoComm.setEmpName(empName);
                tdZwPsninfoCommList.add(tdZwPsninfoComm);
            }
        }
        return tdZwPsninfoCommList;
    }

    /**
    * @Description : 根据体检主表ID
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 14:50
    **/
    public List<TdTjChiefDoctor> getTdTjChiefDoctorListByBhkId(Integer bhkId) {
        if(null != bhkId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdTjChiefDoctor t ");
            sb.append(" where t.fkByBhkId.rid = ").append(bhkId);
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }
    @Transactional(readOnly = true)
    public List<Object[]> findBhkBadRsnList(String bhkIds){
        StringBuffer sb = new StringBuffer();
        sb.append("select T.BHK_ID,T1.CODE_NAME from TD_TJ_BADRSNS_CLT T ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T1.RID = T.BADRSN_ID");
        sb.append(" where T.BHK_ID in (").append(bhkIds).append(")");
        return em.createNativeQuery(sb.toString()).getResultList();
    }

    /**
    * @Description : 获取电测听项目修正规则
    * @MethodAuthor: anjing
    * @Date : 2021/4/10 14:06
    **/
    public List<TbTjAudioItemRule> findTbTjAudioItemRuleList() {
        StringBuilder sb = new StringBuilder();
        sb.append(" select t from TbTjAudioItemRule t ");
        List<TbTjAudioItemRule> list = em.createQuery(sb.toString()).getResultList();
        if(!CollectionUtils.isEmpty(list)) {
            for(TbTjAudioItemRule rule : list) {
                rule.getTbTjAdoItmRuleSubList().size();
            }
        }
        return list;
    }

    /**
     * <p>方法描述：通过id删除体检子表记录</p>
     * @MethodAuthor： yzz
     * @Date：2021-07-14
     **/
    public void deleteBhkSubById(Integer id){
        StringBuffer sql=new StringBuffer();
        sql.append(" delete from TD_TJ_BHKSUB_CLT where rid=");
        sql.append(id);
        this.em.createNativeQuery(sql.toString()).executeUpdate();
    }

    /**
     * <p>描述 根据体检主表id查询子表数量</p>
     *
     * @param mainId
     * @MethodAuthor gongzhe,2022/8/11 14:35,findTdTjBhksubCltCountsByMainId
     * @return int
     */
    public int findTdTjBhksubCltCountsByMainId(Integer mainId){
        String sql = "SELECT COUNT(1) FROM TD_TJ_BHKSUB_CLT WHERE BHK_ID = "+mainId;
        return this.findCountBySql(sql);
    }

    /**
     * 找出所有在岗状态及危害因素为空地体检项目特殊标准
     *
     * @return 所有在岗状态及危害因素为空地体检项目特殊标准
     */
    public List<TbTjItemsSpe> findSpeItemsNoBadRsn() {
        String hql = "select t from TbTjItemsSpe t where t.fkByOnguardStateid is null and t.fkByBadRsnId is null and t.sex is not null ";
        return CollectionUtil.castList(TbTjItemsSpe.class, em.createQuery(hql).getResultList());
    }

    /**
     * 查询所有未停用的项目（仅查询RID）
     *
     * @return 所有未停用的项目（仅查询RID）
     */
    public List<TbTjItems> findAllTbTjItems() {
        String hql = "select new TbTjItems(t.rid) from TbTjItems t where t.stopTag is null or t.stopTag = 0 ";
        return CollectionUtil.castList(TbTjItems.class, em.createQuery(hql).getResultList());
    }
}
