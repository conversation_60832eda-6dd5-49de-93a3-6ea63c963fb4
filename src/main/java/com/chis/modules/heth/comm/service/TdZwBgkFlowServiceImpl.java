package com.chis.modules.heth.comm.service;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.utils.BgkMarkUtils;
import com.chis.modules.system.entity.TsUserInfo;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

/**
 * <p>类描述： </p>
 *`
 * @ClassAuthor: yzz
 * @date： 2022年01月29日
 **/
@Service
@Transactional(rollbackFor = Exception.class)
public class TdZwBgkFlowServiceImpl extends AbstractTemplate {

    private TdZwBgkLastStaServiceCommImpl bgkLastStaService = SpringContextHolder.getBean(TdZwBgkLastStaServiceCommImpl.class);

    /**
     * <p>方法描述：添加操作流程</p>
     * @MethodAuthor： yzz
     * @Date：2022-01-29
     **/
    public void saveCheckProcess(TdTjBhk tdTjBhk,TdZwBgkFlow bgkFlow, Date minDaysBeforeTheWorkingDayDate){

        //更新体检记录
        this.update(tdTjBhk);

        //插入新纪录
        if(bgkFlow.getPreviousOperFlag()!=null&&(21==bgkFlow.getPreviousOperFlag()||33==bgkFlow.getPreviousOperFlag()||44==bgkFlow.getPreviousOperFlag())){
                TdZwBgkFlow lastFlow=new TdZwBgkFlow();
                lastFlow.setCartType(9);
                lastFlow.setOperFlag(bgkFlow.getPreviousOperFlag());
                lastFlow.setBusId(bgkFlow.getBusId());
                lastFlow.setBhkCode(bgkFlow.getBhkCode());
                lastFlow.setFkByBhkorgId(bgkFlow.getFkByBhkorgId());
                TdTjBhk tjBhk= this.find(TdTjBhk.class,bgkFlow.getBusId());
                if(null!=tjBhk){
                    //接收日期
                    lastFlow.setRcvDate(tjBhk.getDealCompleteDate());
                }
                this.upsertEntity(lastFlow);
        }

        //更新上一步流程
        StringBuffer hql=new StringBuffer();
        hql.append(" select T from TdZwBgkFlow T where T.cartType=9 and T.busId=").append(bgkFlow.getBusId());
        hql.append(" and T.operFlag=").append(bgkFlow.getPreviousOperFlag());
        hql.append(" order by  T.createDate desc");
        List<TdZwBgkFlow> lastBgkFlows = em.createQuery(hql.toString()).getResultList();
        if(!CollectionUtils.isEmpty(lastBgkFlows)){
            //计算及时性
            TdZwBgkFlow lastBgkFlow=lastBgkFlows.get(0);
            if (lastBgkFlow.getRcvDate() != null && minDaysBeforeTheWorkingDayDate != null) {
                boolean inTime = lastBgkFlow.getRcvDate().before(minDaysBeforeTheWorkingDayDate);
                lastBgkFlow.setIfInTime(inTime ? 0 : 1);
            }
            lastBgkFlow.setOperDate(new Date());
            lastBgkFlow.setFkByOperPsnId(Global.getUser());
            this.upsertEntity(lastBgkFlow);
        }

        //插入流程记录
        this.upsertEntity(bgkFlow);
    }


    /**
     * <p>方法描述：计算超期</p>
     * @MethodAuthor： yzz
     * @Date：2022-01-29
     **/
    @Deprecated
    public int calLimitTime(Date sDate) {
        if (null == sDate) {
            return 0;
        }
        String bhkLimitTime = PropertyUtils.getValue("bhkLimitTime");
        int day = HolidayUtil.calRemainingDate(sDate, new Date(),bhkLimitTime);
        if (day == 0) {
            return -1;
        }
        return day;
    }

    /**
     * 计算前N个工作日的日期以及到今天距离日期N个工作日之后剩余的工作日天数
     *
     * @return map <pre>key: 日期</pre><pre>value: 今天距离日期N个工作日之后剩余的工作日天数</pre>
     */
    public Map<Date, Integer> calDaysBeforeTheWorkingDay() {
        try {
            String bhkLimitTimeStr = PropertyUtils.getValueWithoutException("bhkLimitTime");
            Integer bhkLimitTime = ObjectUtil.convert(Integer.class, bhkLimitTimeStr, null);
            if (bhkLimitTime == null) {
                return new HashMap<>();
            }
            return HolidayUtil.calDaysBeforeTheWorkingDay(new Date(), bhkLimitTime);
        } catch (Exception e) {
            e.printStackTrace();
            return new HashMap<>();
        }
    }

    /**
     * <p>描述 根据传入的字段名更新对应RID主键的字段值</p>
     *
     * @param ids
     * @param valuesMap
     * @MethodAuthor gongzhe,2022/1/30 9:46,updateTdTjBhkListByIds
     * @return void
     */
    private int updateTdTjBhkListByIds(List<Integer> ids, Map<String,Object> valuesMap) {
        int resultNumber = 0;
        if (!CollectionUtils.isEmpty(ids) && MapUtils.isNotEmpty(valuesMap)) {
            StringBuilder sb = new StringBuilder("UPDATE TD_TJ_BHK T SET ");
            StringBuilder valStr = new StringBuilder();
            for (String field : valuesMap.keySet()) {
                Object value = valuesMap.get(field);
                if (value != null) {
                    valStr.append(",").append(field).append(" = ");
                    if (value instanceof Integer) {
                        valStr.append(value);
                    } else if (value instanceof String) {
                        valStr.append("'").append(value).append("'");
                    } else if (value instanceof Date) {
                        valStr.append(" TO_DATE('").append(DateUtils.formatDate((Date) value));
                        valStr.append(" 23:59:59', 'YYYY-MM-DD HH24:MI:SS') ");
                    }
                } else {
                    valStr.append(",").append(field).append(" = null ");
                }
            }
            sb.append(valStr.substring(1));
            sb.append(" WHERE T.RID IN (");
            int length = ids.size();
            int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
            for (int i = 0; i < allDataCount; i++) {
                int endIndex = Math.min((i + 1) * 1000, length);
                List<Integer> subList = ids.subList(i * 1000, endIndex);
                String sql = sb.toString()+StringUtils.list2string(subList, ",")+")";
                resultNumber += this.executeSql(sql,null);
            }
        }
        return resultNumber;
    }

    /**
     * 根据体检记录RID及上次操作标识获取上一次操作时间
     *
     * @param lastMark   上次操作标识
     * @param bhkRidList 体检记录RID
     * @return List[] {体检记录RID, 流程接收日期, 流程RID}
     */
    public List<Object[]> getProcessReceiptDateList(Integer lastMark, List<Integer> bhkRidList) {
        StringBuilder sb = new StringBuilder("SELECT T.BUS_ID,T.RCV_DATE,T.RID FROM TD_ZW_BGK_FLOW T WHERE CART_TYPE = 9 AND OPER_FLAG = " + lastMark);
        int length = bhkRidList.size();
        sb.append(" AND EXISTS (SELECT 1 FROM TD_ZW_BGK_FLOW T1 WHERE T1.BUS_ID = T.BUS_ID AND T1.BUS_ID IN (");
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        List<Object[]> list = new ArrayList<>();
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = bhkRidList.subList(i * 1000, endIndex);
            String sql = sb.toString() + StringUtils.list2string(subList, ",")+")) ORDER BY T.BUS_ID,T.CREATE_DATE DESC";
            list.addAll(this.findDataBySqlNoPage(sql,null));
        }
        List<Object[]> result = new ArrayList<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            if(i>0){
                String last = StringUtils.objectToString(list.get(i-1)[0]);
                String now = StringUtils.objectToString(list.get(i)[0]);
                //上一条和当前条的BUS_ID一致，直接跳过，同一BUS_ID仅记录CREATE_DATE最新的一条
                if(last!=null && last.equals(now)){
                    continue;
                }
            }
            result.add(list.get(i));
        }
        return result;
    }

    /**
     * 根据体检记录RID及上次操作标识获取上一次操作时间
     *
     * @param lastMark   上次操作标识
     * @param bhkRidList 体检记录RID
     * @return List[] {体检记录RID, 流程接收日期, 流程RID}
     */
    public List<Object[]> getProcessReceiptDateList(Integer lastMark, List<Integer> bhkRidList,Integer cartType) {
        StringBuilder sb = new StringBuilder("SELECT T.BUS_ID,T.RCV_DATE,T.RID FROM TD_ZW_BGK_FLOW T WHERE CART_TYPE = "+cartType+" AND OPER_FLAG = " + lastMark);
        int length = bhkRidList.size();
        sb.append(" AND EXISTS (SELECT 1 FROM TD_ZW_BGK_FLOW T1 WHERE T1.BUS_ID = T.BUS_ID AND T1.BUS_ID IN (");
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        List<Object[]> list = new ArrayList<>();
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = bhkRidList.subList(i * 1000, endIndex);
            String sql = sb.toString() + StringUtils.list2string(subList, ",")+")) ORDER BY T.BUS_ID,T.CREATE_DATE DESC";
            list.addAll(this.findDataBySqlNoPage(sql,null));
        }
        List<Object[]> result = new ArrayList<>(list.size());
        for (int i = 0; i < list.size(); i++) {
            if(i>0){
                String last = StringUtils.objectToString(list.get(i-1)[0]);
                String now = StringUtils.objectToString(list.get(i)[0]);
                //上一条和当前条的BUS_ID一致，直接跳过，同一BUS_ID仅记录CREATE_DATE最新的一条
                if(last!=null && last.equals(now)){
                    continue;
                }
            }
            result.add(list.get(i));
        }
        return result;
    }

    /**
     * 更新上一次操作记录
     *
     * @param ids    上一次操作记录RID
     * @param timely 是否及时
     */
    public void updateLastFlowBatch(List<Integer> ids, int timely) {
        StringBuilder sb = new StringBuilder("UPDATE TD_ZW_BGK_FLOW T ");
        sb.append("SET T.OPER_DATE = sysdate, ")
                .append("T.OPER_PSN_ID = ").append(Global.getUser().getRid()).append(", ")
                .append("T.IF_IN_TIME  = ").append(timely).append(" ")
                .append("WHERE T.RID IN ( ");
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            String sql = sb.toString() + StringUtils.list2string(subList, ",") + ")";
            this.executeSql(sql, null);
        }
    }

    /**
     * 插入初始流程记录
     *
     * @param ids           体检记录RID
     * @param operationFlag 操作标识
     */
    public void insertInitialFlowBatch(List<Integer> ids, int operationFlag) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        StringBuilder sb = new StringBuilder("INSERT INTO TD_ZW_BGK_FLOW (RID, CART_TYPE, BUS_ID, OPER_FLAG, RCV_DATE, CREATE_DATE, CREATE_MANID, BHK_CODE, BHKORG_ID) ");
        sb.append("SELECT TD_ZW_BGK_FLOW_SEQ.NEXTVAL, T.* FROM ( ");
        sb.append("SELECT 9 t1, B.RID t2, ").append(operationFlag).append(" t3, B.DEAL_COMPLETE_DATE t4, ")
                    .append("sysdate t5, ").append(Global.getUser().getRid()).append(" t6, ")
                    .append(" B.BHK_CODE t7, ")
                    .append(" B.BHKORG_ID t8 ")
                    .append("FROM TD_TJ_BHK B ")
                    .append("WHERE ")
                    .append("B.RID IN ( ");
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            String sql = sb.toString() + StringUtils.list2string(subList, ",") + ") ) T ";
            this.executeSql(sql, null);
        }
    }

    /**
     * 插入当前流程记录
     *
     * @param ids           体检记录RID
     * @param operationFlag 操作标识
     * @param checkRst      审核意见
     */
    public void insertCurrentFlowBatch(List<Integer> ids, int operationFlag, String checkRst) {
        StringBuilder sb = new StringBuilder("INSERT INTO TD_ZW_BGK_FLOW (RID, CART_TYPE, BUS_ID, OPER_FLAG, RCV_DATE, SMT_PSN_ID, AUDIT_ADV, AUDIT_MAN, CREATE_DATE, CREATE_MANID, BHK_CODE, BHKORG_ID) ");
        sb.append("SELECT TD_ZW_BGK_FLOW_SEQ.NEXTVAL, T.* FROM ( ");
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            StringBuilder sql = new StringBuilder(sb.toString());
            sql.append("SELECT 9 t1, B.RID t2, ").append(operationFlag).append(" t3, sysdate t4, ")
                    .append(Global.getUser().getRid()).append(" t5, ")
                    .append(" '").append(checkRst).append("' ").append(" t6, ")
                    .append(" '").append(Global.getUser().getUsername()).append("' ")
                    .append(" t7, sysdate t8, ")
                    .append(Global.getUser().getRid()).append(" t9, ")
                    .append(" B.BHK_CODE t10, ")
                    .append(" B.BHKORG_ID t11 ")
                    .append("FROM TD_TJ_BHK B ")
                    .append("WHERE ")
                    .append("B.RID IN ( ").append(StringUtils.list2string(subList, ",")).append(")");
            sql.append(" ) T ");
            this.executeSql(sql.toString(), null);
        }
    }

    /**
     * 批量审核数据库操作
     *
     * @param lastMarkBhkRidMap              key: 上次流程操作标识; value: 体检记录RID;
     * @param markBhkRidMapList              BHK表更新字段
     * @param bhkRidMapList                  BHK表更新rid
     * @param currentMarkBhkRidMap           key: 操作标识; value: BHK RID List;
     * @param checkRst                       审核意见
     * @param minDaysBeforeTheWorkingDayDate 最小接收日期
     */
    public int reviewBatchOperation(Map<Integer, List<Integer>> lastMarkBhkRidMap,
                                    List<Map<String, Object>> markBhkRidMapList,
                                    List<List<Integer>> bhkRidMapList,
                                    Map<Integer, List<Integer>> currentMarkBhkRidMap,
                                    String checkRst, Date minDaysBeforeTheWorkingDayDate) {
        int resultNumber = 0;
        //新增操作标识为21,33,44的操作记录
        insertInitialFlowBatch(lastMarkBhkRidMap.get(21), 21);
        insertInitialFlowBatch(lastMarkBhkRidMap.get(33), 33);
        insertInitialFlowBatch(lastMarkBhkRidMap.get(44), 44);

        //1、根据lastMarkBhkRidMap获取List<Object[]> processReceiptDateList {体检记录RID, 流程接收日期, 流程RID}
        List<Object[]> processReceiptDateList = new ArrayList<>();
        for (Integer lastMark : lastMarkBhkRidMap.keySet()) {
            if (!CollectionUtils.isEmpty(lastMarkBhkRidMap.get(lastMark))) {
                processReceiptDateList.addAll(getProcessReceiptDateList(lastMark, lastMarkBhkRidMap.get(lastMark)));
            }
        }
        //2、遍历processReceiptDateList计算是否及时，存于Map<> bhkRidTimelyMap key:是否及时; value: 流程RID List;
        Map<Integer, List<Integer>> bhkRidTimelyMap = new HashMap<>();
        bhkRidTimelyMap.put(0, new ArrayList<Integer>());
        bhkRidTimelyMap.put(1, new ArrayList<Integer>());
        for (Object[] objects : processReceiptDateList) {
            Date date = ObjectUtil.convert(Date.class, objects[1], null);
            if (date == null || minDaysBeforeTheWorkingDayDate == null || objects[2] == null) {
                continue;
            }
            int calLimitTime = date.before(minDaysBeforeTheWorkingDayDate) ? 0 : 1;
            bhkRidTimelyMap.get(calLimitTime).add(((BigDecimal) objects[2]).intValue());
        }

        //根据传入的字段名更新BHK表对应RID主键的字段值
        if (!CollectionUtils.isEmpty(bhkRidMapList) && !CollectionUtils.isEmpty(markBhkRidMapList)) {
            for (int i = 0; i < bhkRidMapList.size(); i++) {
                resultNumber += this.updateTdTjBhkListByIds(bhkRidMapList.get(i), markBhkRidMapList.get(i));
            }
        }
        //更新上次流程记录 OPER_DATE OPER_PSN_ID IF_IN_TIME
        for (Integer timely : bhkRidTimelyMap.keySet()) {
            if (!CollectionUtils.isEmpty(bhkRidTimelyMap.get(timely))) {
                updateLastFlowBatch(bhkRidTimelyMap.get(timely), timely);
            }
        }
        //新增当前流程记录
        for (Integer operationFlag : currentMarkBhkRidMap.keySet()) {
            if (!CollectionUtils.isEmpty(currentMarkBhkRidMap.get(operationFlag))) {
                insertCurrentFlowBatch(currentMarkBhkRidMap.get(operationFlag), operationFlag, checkRst);
            }
        }
        return resultNumber;
    }

    /**
     * <p>描述 根据体检机构rid查询职业健康检查基本信息</p>
     *
     * @param orgId
     * @MethodAuthor gongzhe,2022/4/6 17:44,selectTdZwTjorginfoByOrgId
     * @return java.util.List<java.lang.Object[]>
     */
    public List<Object[]> selectTdZwTjorginfoByOrgId(Integer orgId) {
        if (null != orgId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT T.RID,T.ORG_NAME,T.LINK_MAN,T.LINK_TEL FROM TD_ZW_TJORGINFO T ");
            sb.append(" WHERE T.ORG_ID = ").append(orgId);
            return this.findDataBySqlNoPage(sb.toString(),null);
        }
        return null;
    }
    /**
     *  <p>方法描述：查询历次审核意见</p>
     * @MethodAuthor hsj 2022-05-17 14:50
     */
    public List<Object[]> findHisotryList(Integer busId, Integer cartType) {
        if(null == busId || null == cartType){
            return null;
        }
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select T1.CART_TYPE,T1.AUDIT_ADV,T1.AUDIT_MAN,T1.CREATE_DATE,T2.CRPT_NAME,T3.IF_CITY_DIRECT,IF_PROV_DIRECT,T1.OPER_FLAG from TD_ZW_BGK_FLOW T1 ");
        if(cartType == 1){
            //职业健康检查汇总表/职业性有害因素监测卡
            sqlBuilder.append(" LEFT JOIN TD_ZW_HETH_CHK_SMARY T2 on T1.BUS_ID = T2.RID ");
            sqlBuilder.append(" LEFT JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID " );
        }else if(cartType == 4){
            //职业病报告卡
            sqlBuilder.append(" LEFT JOIN TD_ZW_OCCDIS_CARD_NEW T2 on T1.BUS_ID = T2.RID ");
            sqlBuilder.append(" LEFT JOIN TS_UNIT T4 on T2.DIAG_UNIT_ID = T4.RID ");
            sqlBuilder.append(" LEFT JOIN TS_ZONE T3 ON T4.ZONE_ID = T3.RID " );
        }
        sqlBuilder.append(" where T1.BUS_ID = ").append(busId).append(" and T1.CART_TYPE =  ").append(cartType);
        sqlBuilder.append(" ORDER BY T1.CREATE_DATE desc ");
        List<Object[]> sqlResultList = this.findSqlResultList(sqlBuilder.toString());
        return sqlResultList;
    }
   /**
    *  <p>方法描述：根据业务主键和报告卡类型查询最新状态表</p>
    * @MethodAuthor hsj 2022-05-17 15:09
    */
    public TdZwBgkLastSta findTdZwBgkLastSta(Integer budId, Integer cartType) {
        if (null != budId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TdZwBgkLastSta t ");
            sb.append(" where t.busId = ").append(budId);
            sb.append(" and t.cartType = ").append(cartType);
            List<TdZwBgkLastSta> list = em.createQuery(sb.toString()).getResultList();
            if(!org.springframework.util.CollectionUtils.isEmpty(list)) {
                return list.get(0);
            }
            return null;
        }
        return null;
    }

    /**
     * <p>方法描述：查询最大接收日期的流程记录</p>
     * @MethodAuthor qrr,2019年9月17日,selectMaxRevDateInfo
     * */
    private Object[] selectMaxRevDateInfo(Integer bussId, Integer cardType) {
        if (null == bussId || null == cardType) {
            return new Object[0];
        }
        StringBuffer sql = new StringBuffer();
        sql.append("select rid,max(CREATE_DATE),SMT_PSN_ID from TD_ZW_BGK_FLOW where CART_TYPE =")
                .append(cardType).append(" and BUS_ID =").append(bussId).append(" group by rid,SMT_PSN_ID ORDER BY max(CREATE_DATE) DESC");
        List<Object[]> list = this.findSqlResultList(sql.toString());
        if (null == list || list.size() == 0) {
            return new Object[0];
        }
        return list.get(0);
    }
    /**
     * <p>描述 保存填报机构流程信息</p>
     *
     * @param busId
     * @param cardType 报告卡类型
     * @param busDate 业务日期
     * @param ifInTime 是否及时，包含填报是否及时与区级或市级退回后是否操作及时
     * @param zone 填报区划
     * @MethodAuthor gongzhe,2022/4/25 15:41,saveOrUpdateOrgFlowInfoNew
     * @return void
     */
    @Transactional(readOnly = false)
    public void saveOrUpdateOrgFlowInfoNew(Integer busId, Integer cardType,
                                           Date busDate, Integer ifInTime, TsZone zone) {
        if (null == busId || null == cardType) {
            return;
        }
        Object[] maxRevFlow = selectMaxRevDateInfo(
                busId, cardType);

        TsUserInfo user = Global.getUser();
        if (maxRevFlow.length>0 && null != maxRevFlow[0]) {
            StringBuffer updateSql = new StringBuffer();
            updateSql.append("update TD_ZW_BGK_FLOW set OPER_DATE = to_date('")
                    .append(DateUtils.getDate()).append("','yyyy-MM-dd')");// 更新操作日期
            updateSql.append(",OPER_PSN_ID =").append(user.getRid());// 操作人
            // 及时性，区级或市级退回操作是否及时
            if(ifInTime!=null){
                updateSql.append(",IF_IN_TIME =").append(ifInTime);
            }
            updateSql.append(" where rid =").append(maxRevFlow[0]);
            this.executeSql(updateSql.toString(), null);
        } else {
            // 新增填报记录
            TdZwBgkFlow bgkFlow = new TdZwBgkFlow();
            bgkFlow.setBusId(busId);
            bgkFlow.setCartType(cardType);
            // 业务日期
            bgkFlow.setRcvDate(busDate);
            bgkFlow.setOperDate(new Date());
            bgkFlow.setIfInTime(ifInTime);
            bgkFlow.setFkByOperPsnId(user);
            bgkFlow.setOperFlag(12);
            preInsert(bgkFlow);
            this.save(bgkFlow);
        }
        // 新增流程记录
        TdZwBgkFlow bgkFlow = new TdZwBgkFlow();
        bgkFlow.setBusId(busId);
        bgkFlow.setCartType(cardType);
        bgkFlow.setRcvDate(new Date());
        bgkFlow.setFkBySmtPsnId(user);
        boolean twoLevelAudit = "2".equals(PropertyUtils.getValueWithoutException("checkLevel"));
        boolean threeLevelAudit = "3".equals(PropertyUtils.getValueWithoutException("checkLevel"));
        if (twoLevelAudit) {
            if ("1".equals(zone.getIfProvDirect())) {
                // 省直属
                bgkFlow.setOperFlag(44);
            } else {
                bgkFlow.setOperFlag(21);
            }
        } else if (threeLevelAudit) {
            if ("1".equals(zone.getIfCityDirect())) {
                // 市直属
                bgkFlow.setOperFlag(33);
            } else {
                bgkFlow.setOperFlag(21);
            }
        }
        preInsert(bgkFlow);
        this.save(bgkFlow);
    }
    /**
     * <p>描述 保存填报机构流程信息，不保存12标识，不计算及时性</p>
     *
     * @param busId
     * @param cardType 报告卡类型
     * @param zone 填报区划
     * @MethodAuthor gongzhe,2022/5/19 11:11,saveOrUpdateOrgFlowInfoNew
     * @return void
     */
    @Transactional(readOnly = false)
    public void saveOrUpdateOrgFlowInfoNew(Integer busId, Integer cardType, TsZone zone) {
        if (null == busId || null == cardType) {
            return;
        }
        TsUserInfo user = Global.getUser();
        // 新增流程记录
        TdZwBgkFlow bgkFlow = new TdZwBgkFlow();
        bgkFlow.setBusId(busId);
        bgkFlow.setCartType(cardType);
        bgkFlow.setRcvDate(new Date());
        bgkFlow.setFkBySmtPsnId(user);
        boolean twoLevelAudit = "2".equals(PropertyUtils.getValueWithoutException("checkLevel"));
        boolean threeLevelAudit = "3".equals(PropertyUtils.getValueWithoutException("checkLevel"));
        if (twoLevelAudit) {
            if ("1".equals(zone.getIfProvDirect())) {
                // 省直属
                bgkFlow.setOperFlag(44);
            } else {
                bgkFlow.setOperFlag(21);
            }
        } else if (threeLevelAudit) {
            if ("1".equals(zone.getIfCityDirect())) {
                // 市直属
                bgkFlow.setOperFlag(33);
            } else {
                bgkFlow.setOperFlag(21);
            }
        }
        preInsert(bgkFlow);
        this.save(bgkFlow);
    }
    /**
     *  <p>方法描述：更新报告卡最新状态/报告卡审批流程通用</p>
     *  busId:业务主表rid
     *  cardType:报告卡类型
     *  zone:地区
     *  newFlow：报告卡最新状态
     *  checkResult：审核结果 1：通过
     *  level:审核级别
     *  limitTime:审核期限
     * @MethodAuthor hsj 2022-05-17 15:59
     */
    @Transactional(readOnly = false)
    public void saveOrUpdateNewFlowOrBgkFlow(Integer busId, Integer cardType, TsZone zone, TdZwBgkLastSta newFlow, String checkResult, Integer level, String limitTime) {
        TdZwBgkFlow tdZwBgkFlow = new TdZwBgkFlow();
        tdZwBgkFlow.setCartType(cardType);
        tdZwBgkFlow.setBusId(busId);
        tdZwBgkFlow.setRcvDate(new Date());
        tdZwBgkFlow.setFkBySmtPsnId(new TsUserInfo(Global.getUser().getRid()));
        String checkLevel = PropertyUtils.getValueWithoutException("checkLevel");
        if ("2".equals(checkLevel)) {
            //二级审核
            switch (level) {
                case 0:
                    if ("1".equals(checkResult)) {
                        newFlow.setProRcvDate(new Date());
                        newFlow.setState(5);
                        tdZwBgkFlow.setOperFlag(43);
                    } else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(2);
                        tdZwBgkFlow.setOperFlag(11);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCountAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCountyChkPsn());
                    break;
                case 2:
                    if ("1".equals(checkResult)) {
                        newFlow.setProSmtDate(new Date());
                        newFlow.setState(7);
                        tdZwBgkFlow.setOperFlag(42);
                    } else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(6);
                        tdZwBgkFlow.setOperFlag(32);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getProAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getProChkPsn());
                    break;
                default:
                    break;
            }

        } else if ("3".equals(checkLevel)) {
            //三级审核
            switch (level) {
                case 0:
                    if ("1".equals(checkResult)) {
                        newFlow.setCityRcvDate(new Date());
                        newFlow.setState(3);
                        tdZwBgkFlow.setOperFlag(31);
                    } else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(2);
                        tdZwBgkFlow.setOperFlag(11);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCountAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCountyChkPsn());
                    break;
                case 1:
                    if ("1".equals(checkResult)) {
                        newFlow.setProRcvDate(new Date());
                        newFlow.setState(5);
                        tdZwBgkFlow.setOperFlag(41);
                    } else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(4);
                        if ("1".equals(zone.getIfCityDirect())) {//市直属
                            tdZwBgkFlow.setOperFlag(13);
                        } else {
                            tdZwBgkFlow.setOperFlag(22);
                        }
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getCityAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getCityChkPsn());
                    break;
                case 2:
                    if ("1".equals(checkResult)) {
                        newFlow.setProSmtDate(new Date());
                        newFlow.setState(7);
                        tdZwBgkFlow.setOperFlag(42);
                    } else {
                        newFlow.setOrgRcvDate(new Date());
                        newFlow.setState(6);
                        tdZwBgkFlow.setOperFlag(32);
                    }
                    tdZwBgkFlow.setAuditAdv(newFlow.getProAuditAdv());
                    tdZwBgkFlow.setAuditMan(newFlow.getProChkPsn());
                    break;
                default:
                    break;
            }
        }
        updateInsLastRecord(newFlow,tdZwBgkFlow,limitTime);
    }
   /**
    *  <p>方法描述：报告卡最新状态记录保存，审批流程表保存并更新上条记录</p>
    * @MethodAuthor hsj 2022-05-17 16:08
    */
    @Transactional(readOnly = false)
    public void updateInsLastRecord(TdZwBgkLastSta tdZwBgkLastSta,TdZwBgkFlow tdZwBgkFlow,String limitTime){
        //保存  报告卡最新状态
        this.upsertEntity(tdZwBgkLastSta);

        //获取 报告卡审批流程  上一条记录
        StringBuffer hql=new StringBuffer();
        hql.append("select T from TdZwBgkFlow T");
        hql.append(" where T.cartType=").append(tdZwBgkFlow.getCartType());
        hql.append(" and T.busId=").append(tdZwBgkLastSta.getBusId());
        hql.append(" order by T.createDate desc ");
        List<TdZwBgkFlow> tdZwBgkFlowList= this.findByHql(hql.toString(),TdZwBgkFlow.class);
        if(!org.springframework.util.CollectionUtils.isEmpty(tdZwBgkFlowList)){
            //创建时间倒序取第一条记录
            TdZwBgkFlow lastRecord= tdZwBgkFlowList.get(0);
            lastRecord.setOperDate(tdZwBgkFlow.getRcvDate());
            lastRecord.setFkByOperPsnId(tdZwBgkFlow.getFkBySmtPsnId());
            //判断是否超期
            if(lastRecord.getRcvDate()!=null&&lastRecord.getOperDate()!=null && StringUtils.isNotBlank(limitTime)){
                int num= HolidayUtil.calRemainingDate(lastRecord.getRcvDate(), lastRecord.getOperDate(),limitTime);
                if(num>0){
                    lastRecord.setIfInTime(1);
                }else{
                    lastRecord.setIfInTime(0);
                }
            }
            this.upsertEntity(lastRecord);
        }
        //保存   报告卡审批流程
        this.upsertEntity(tdZwBgkFlow);
    }
    /**
     *  <p>方法描述：职业病报告卡（诊断流程）</p>
     * @MethodAuthor hsj 2022-05-17 18:57
     */
    @Transactional(readOnly = false)
    public void saveOrUpdateNewFlowOrBgkFlowOrOccdiscase(TdZwOccdisCardNew disCard, TdZwBgkLastSta newFlow, String checkResult, Integer level, String limitTime) {
        //更新报告卡最新状态/报告卡审批流程
        saveOrUpdateNewFlowOrBgkFlow(disCard.getRid(),4,disCard.getFkByDiagUnitId().getTsZone(),newFlow,checkResult,level,limitTime);
        //终审通过-更新诊断记录状态为7“完成”（TD_ZW_OCCDISCASE.STATE_MARK）
        if("1".equals(checkResult) && level == 2){
            updateOccdiscaseStateByRid(disCard.getMainId());
        }
    }
    /**
     *  <p>方法描述：更新诊断记录状态为7“完成”</p>
     * @MethodAuthor hsj 2022-05-17 16:11
     */
    @Transactional(readOnly = false)
    public void updateOccdiscaseStateByRid(Integer rid) {
        String str = "UPDATE   TD_ZW_OCCDISCASE SET STATE_MARK = 7 WHERE RID = "+rid;
        this.executeSql(str, null);
    }

    /**
     * 批量审核通过
     *
     * @param disCardList 职业病报告卡审核
     * @param newFlowList 职业病报告卡流程
     * @param level       审核级别
     * @param limitTime   审核期限
     */
    @Transactional(readOnly = false)
    public void odBatchReviewPass(List<TdZwOccdisCardNew> disCardList, List<TdZwBgkLastSta> newFlowList, Integer level, String limitTime) {
        for (int i = 0; i < disCardList.size(); i++) {
            saveOrUpdateNewFlowOrBgkFlowOrOccdiscase(disCardList.get(i), newFlowList.get(i), "1", level, limitTime);
        }
    }


    /**
     * 插入当前流程记录
     *
     * @param ids           bus_id
     * @param operationFlag 操作标识
     * @param checkRst      审核意见
     */
    public void insertFlowBatch(List<Integer> ids,Integer operationFlag,Integer cartType,String checkRst) {
        StringBuilder sb = new StringBuilder("INSERT INTO TD_ZW_BGK_FLOW (RID, CART_TYPE, BUS_ID, OPER_FLAG, RCV_DATE, SMT_PSN_ID, AUDIT_ADV, AUDIT_MAN, CREATE_DATE, CREATE_MANID) ");
        sb.append("SELECT TD_ZW_BGK_FLOW_SEQ.NEXTVAL, T.* FROM ( ");
        int length = ids.size();
        int allDataCount = length % 1000 == 0 ? length / 1000 : ((length / 1000) + 1);
        for (int i = 0; i < allDataCount; i++) {
            int endIndex = Math.min((i + 1) * 1000, length);
            List<Integer> subList = ids.subList(i * 1000, endIndex);
            StringBuilder sql = new StringBuilder(sb.toString());
            for (int i1 = 0; i1 < subList.size(); i1++) {
                sql.append("SELECT  ")
                        .append(cartType).append(" t1, ")
                        .append(ids.get(i1)).append(" t2, ")
                        .append(operationFlag).append(" t3, ")
                        .append("sysdate t4, ")
                        .append(Global.getUser().getRid()).append(" t5, ")
                        .append(" '").append(checkRst).append("' ").append(" t6, ")
                        .append(" '").append(Global.getUser().getUsername()).append("' ")
                        .append(" t7, sysdate t8, ")
                        .append(Global.getUser().getRid()).append(" t9 ")
                        .append("FROM DUAL  ");
                if(i1!=(subList.size()-1)){
                    sql.append(" UNION ALL ");
                }
            }
            sql.append(" ) T ");
            this.executeSql(sql.toString(), null);
        }
    }

    /**
     * 处理最新状态表  历史流程表
     * @param bgkLastStaList
     * @param addBgkFlowMap
     * @param updateBgkFlowMap
     */
    public void dealBatchOperation(List<TdZwBgkLastSta> bgkLastStaList, Map<Integer, List<Integer>> addBgkFlowMap,
                                   Map<Integer, List<Integer>> updateBgkFlowMap,String limitTime,String checkRst,Integer cartType) {
        //1、更新最新状态表
        bgkLastStaService.batchUpdateLatestSta(bgkLastStaList);
        //2、查询出需要更新的历史流程表
        List<Object[]> processReceiptDateList = new ArrayList<>();
        for (Integer lastMark : updateBgkFlowMap.keySet()) {
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(updateBgkFlowMap.get(lastMark))) {
                processReceiptDateList.addAll(this.getProcessReceiptDateList(lastMark, updateBgkFlowMap.get(lastMark),cartType));
            }
        }

        //2、计算需要更新记录的及时性
        Map<Integer, List<Integer>> bhkRidTimelyMap = new HashMap<>();
        bhkRidTimelyMap.put(0, new ArrayList<Integer>());
        bhkRidTimelyMap.put(1, new ArrayList<Integer>());
        for (Object[] objects : processReceiptDateList) {
            int calLimitTime = BgkMarkUtils.calLimitTime((Timestamp) objects[1],limitTime);
            calLimitTime = calLimitTime < 0 ? 0 : 1;
            bhkRidTimelyMap.get(calLimitTime).add(((BigDecimal) objects[2]).intValue());
        }

        //3、更新上次流程记录 OPER_DATE OPER_PSN_ID IF_IN_TIME
        for (Integer timely : bhkRidTimelyMap.keySet()) {
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(bhkRidTimelyMap.get(timely))) {
                this.updateLastFlowBatch(bhkRidTimelyMap.get(timely), timely);
            }
        }

        //新增当前流程记录
        for (Integer operationFlag : addBgkFlowMap.keySet()) {
            if (!org.apache.commons.collections.CollectionUtils.isEmpty(addBgkFlowMap.get(operationFlag))) {
                this.insertFlowBatch(addBgkFlowMap.get(operationFlag),operationFlag,cartType,checkRst);
            }
        }
    }



    /**
     * <p>方法描述：根据bug_id获取历史审核意见</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-29
     **/
    public List<Object[]> findHisotryListByRid(Integer rid,Integer cartType,String tableName) {
        StringBuilder sql = new StringBuilder();
        sql.append("select T.CART_TYPE,T.AUDIT_ADV,T.AUDIT_MAN,T.CREATE_DATE,T2.UNITNAME,T3.IF_CITY_DIRECT,T3.IF_PROV_DIRECT,T.OPER_FLAG ");
        sql.append(" from TD_ZW_BGK_FLOW T ");
        sql.append(" LEFT JOIN ").append(tableName).append(" T1 on T.BUS_ID=T1.RID ");
        sql.append(" LEFT JOIN TS_UNIT T2 on T1.ORG_ID=T2.RID ");
        sql.append(" LEFT JOIN TS_ZONE T3 ON T2.ZONE_ID = T3.RID " );
        sql.append(" where T.CART_TYPE=").append(cartType).append(" and T1.RID= ").append(rid);
        sql.append(" and T.AUDIT_ADV is not null and T.AUDIT_MAN is not null ");
        sql.append(" ORDER BY T.CREATE_DATE desc ");
        return this.findSqlResultList(sql.toString());
    }

    /**
     * @description: 撤销更新
     * <AUTHOR>
     */
    public void updateTdTjBhkStateByRid(Integer upState,Integer rid, Integer operFlag, Integer lastOperFlag,Integer cartType) {
        //更新体检记录
        StringBuffer sb=new StringBuffer();
        sb.append("update TD_ZW_BGK_LAST_STA set STATE=");
        sb.append(upState);
        sb.append(" where BUS_ID=");
        sb.append(rid);
        sb.append(" and CART_TYPE=");
        sb.append(cartType);
        this.em.createNativeQuery(sb.toString()).executeUpdate();

        String selectDelSql="select * from TD_ZW_BGK_FLOW where CART_TYPE="+cartType+" and BUS_ID="+rid+" and OPER_FLAG="+lastOperFlag+" order by CREATE_DATE desc";
        List<Object[]> selectRes= this.em.createNativeQuery(selectDelSql).getResultList();
        if(!org.apache.commons.collections.CollectionUtils.isEmpty(selectRes)){
            //把提交的修改的字段更新成null
            String lastUpdateSql=" update TD_ZW_BGK_FLOW set OPER_DATE=null,OPER_PSN_ID=null,IF_IN_TIME=null where rid="+Integer.valueOf( selectRes.get(0)[0].toString());;
            this.em.createNativeQuery(lastUpdateSql).executeUpdate();

        }

        //删除报告卡审批流程
        StringBuffer sql=new StringBuffer();
        sql.append(" select T.* from TD_ZW_BGK_FLOW T ");
        sql.append(" where T.CART_TYPE=").append(cartType);
        sql.append(" and T.BUS_ID=").append(rid);
        sql.append(" and T.OPER_FLAG=").append(operFlag);
        sql.append(" order by  T.CREATE_DATE desc ");
        List<Object[]> list= this.em.createNativeQuery(sql.toString()).getResultList();
        if(!org.apache.commons.collections.CollectionUtils.isEmpty(list)){
            StringBuffer delSql=new StringBuffer();
            delSql.append(" delete from  TD_ZW_BGK_FLOW where RID=").append(Integer.valueOf(list.get(0)[0].toString()));
            this.em.createNativeQuery(delSql.toString()).executeUpdate();
        }
    }

    /**
     * 根据RID查询数据审核状态
     *
     * @param idList id
     * @return map
     */
    @Transactional(readOnly = true)
    public Map<Integer, Integer> findBhkDataCheckState(List<Integer> idList) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT B.RID, B.STATE " +
                "FROM TD_TJ_BHK B " +
                "WHERE B.RID IN (:rids)";
        paramMap.put("rids", idList);
        List<Object[]> resultList =
                CollectionUtil.castList(Object[].class, this.findDataBySqlNoPage(sql, paramMap));
        Map<Integer, Integer> resMap = new HashMap<>();
        for (Object[] res : resultList) {
            Integer rid = ObjectUtil.convert(Integer.class, res[0], null);
            Integer state = ObjectUtil.convert(Integer.class, res[1], null);
            if (rid == null || state == null) {
                continue;
            }
            resMap.put(rid, state);
        }
        return resMap;
    }

    /**
     * 查询地区下待审核的全部审核任务的待审核数
     *
     * @return Integer 地区下待审核的全部审核任务的待审核数 <pre>null 代表无待审核的全部审核任务</pre>
     */
    @Transactional(readOnly = true)
    public BigDecimal findAllCheckTaskAndCheckNum(String zoneGb) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT NVL(T.TOTAL_NUM, -1) " +
                "FROM TD_TJ_CHECK_TASK T " +
                "         LEFT JOIN TS_ZONE Z ON T.ZONE_ID = Z.RID " +
                "WHERE T.STATE = 0 AND Z.ZONE_GB = :zoneGb " +
                "ORDER BY T.CREATE_DATE DESC";
        paramMap.put("zoneGb", zoneGb);
        List<BigDecimal> resultList =
                CollectionUtil.castList(BigDecimal.class, this.findDataBySqlNoPage(sql, paramMap));
        if (ObjectUtil.isNotEmpty(resultList)) {
            return resultList.get(0);
        }
        return null;
    }

    /**
     * 存储全部审核任务
     *
     * @param task 全部审核任务
     */
    public void saveTdTjCheckTask(TdTjCheckTask task) {
        super.upsertEntity(task);
    }

}
