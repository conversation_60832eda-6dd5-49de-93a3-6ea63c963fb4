package com.chis.modules.heth.comm.service;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/***
 *  <p>类描述：将体检录入模块的信息传到业务体检表中</p>
 *
 * @ClassAuthor maox,2019年5月31日,TdTjBhkInitServiceImpl
 * <AUTHOR>
 *
 */
@Service
@Transactional(readOnly = false)
public class TdTjBhkInitCommServiceImpl extends AbstractTemplate {

    private TdTjBhkCltCommServiceImpl service = SpringContextHolder.getBean(TdTjBhkCltCommServiceImpl.class);
    /***
     *  <p>方法描述：将体检录入模块的信息传到业务体检表中</p>
     *
     * @MethodAuthor maox,2019年5月31日,initTdTjBhk
     * @param tdTjBhkClt
     */
    @Transactional(readOnly = false)
    public void initTdTjBhk(TdTjBhkClt tdTjBhkClt){
        if(null != tdTjBhkClt.getBrth()){
            tdTjBhkClt.setAge(DateUtils.calAgeByBirtDate(tdTjBhkClt.getBrth()));
        }
        /**更新人员*/
        if(tdTjBhkClt.getFkByPersonId() != null){
            TdTjPerson person =service.findTdTjPersonByID(tdTjBhkClt.getFkByPersonId().getRid());
            person.setPersonName(tdTjBhkClt.getPersonName());
            person.setIdc(tdTjBhkClt.getIdc());
            person.setSex(tdTjBhkClt.getSex());
            person.setBrth(tdTjBhkClt.getBrth());
            person.setIsxmrd(tdTjBhkClt.getIsxmrd());
            person.setTel(tdTjBhkClt.getLnktel());
            person.setDept(tdTjBhkClt.getDpt());
            person.setJob(tdTjBhkClt.getWorkName());
            person.setJobcod(tdTjBhkClt.getWrknum());
            person.setAge(tdTjBhkClt.getAge());
            TsSimpleCode code = tdTjBhkClt.getFkByCardTypeId();
            person.setFkByCardTypeId(code);
            service.updateTdTjPerson(person);
        }


        TdTjBhk tdTjBhk = new TdTjBhk();
        //体检主表信息赋值
        if(tdTjBhkClt != null){
            //加入监测类型
            tdTjBhk.setJcType(tdTjBhkClt.getJcType());
            if(new Integer("2").equals(tdTjBhk.getJcType())){
                if(tdTjBhkClt.getFkByProtectEquId()!=null && tdTjBhkClt.getFkByProtectEquId().getRid()!=null){
                    tdTjBhk.setFkByProtectEquId(tdTjBhkClt.getFkByProtectEquId());
                }
            }else{
                tdTjBhk.setFkByProtectEquId(null);
            }
            tdTjBhk.setBhkCode(tdTjBhkClt.getBhkCode());
            tdTjBhk.setTbTjSrvorg(tdTjBhkClt.getFkByBhkorgId());
            tdTjBhk.setTbTjCrpt(tdTjBhkClt.getFkByCrptId());
            tdTjBhk.setTdTjPerson(tdTjBhkClt.getFkByPersonId());
            tdTjBhk.setPersonName(tdTjBhkClt.getPersonName());
            tdTjBhk.setSex(tdTjBhkClt.getSex());
            tdTjBhk.setIdc(tdTjBhkClt.getIdc());
            tdTjBhk.setBrth(tdTjBhkClt.getBrth());
            tdTjBhk.setAge(tdTjBhkClt.getAge());
            tdTjBhk.setIsxmrd(tdTjBhkClt.getIsxmrd());
            tdTjBhk.setLnktel(tdTjBhkClt.getLnktel());
            tdTjBhk.setDpt(tdTjBhkClt.getDpt());
            tdTjBhk.setWrknum(tdTjBhkClt.getWrknum());
            if(tdTjBhkClt.getWrklnt() != null){
                String a = tdTjBhkClt.getWrklnt().toString();
                Double b = Double.parseDouble(a);
                tdTjBhk.setWrklnt(b);
            }

            tdTjBhk.setWrklntmonth(tdTjBhkClt.getWrklntmonth());

            if(tdTjBhkClt.getTchbadrsntim() != null){
                String a = tdTjBhkClt.getTchbadrsntim().toString();
                Double b = Double.parseDouble(a);
                tdTjBhk.setTchbadrsntim(b);
            }

            tdTjBhk.setTchbadrsnmonth(tdTjBhkClt.getTchbadrsnmonth());
            //接害日期
            Calendar cal = Calendar.getInstance();
            cal.setTime(tdTjBhkClt.getBhkDate());
            if(null ==tdTjBhkClt.getTchbadrsntim()) {
                tdTjBhkClt.setTchbadrsntim(new BigDecimal(0));
            }
            if(null ==tdTjBhkClt.getTchbadrsnmonth()) {
                tdTjBhkClt.setTchbadrsnmonth(0);
            }
            int month = Integer.parseInt(tdTjBhkClt.getTchbadrsntim().toString())*12+Integer.parseInt(tdTjBhkClt.getTchbadrsnmonth().toString());
            cal.add(Calendar.MONTH, 0-month);
            tdTjBhk.setHarmStartDate(cal.getTime());

            tdTjBhk.setWorkName(tdTjBhkClt.getWorkName());
            tdTjBhk.setBhkType(tdTjBhkClt.getBhkType());
            tdTjBhk.setTsSimpleCode(tdTjBhkClt.getFkByOnguardStateid());
            tdTjBhk.setBhkDate(tdTjBhkClt.getBhkDate());
            tdTjBhk.setBhkrst(tdTjBhkClt.getBhkrst());
            tdTjBhk.setMhkadv(tdTjBhkClt.getMhkadv());
            //职业卫生体检结论
            List<TdTjMhkrstClt> tdTjMhkrstClts = tdTjBhkClt.getTdTjMhkrstClts();
            String mhkrstClts ="";
            if(tdTjMhkrstClts != null && tdTjMhkrstClts.size()>0){
                for(TdTjMhkrstClt mhkrst:tdTjMhkrstClts){
                    mhkrstClts += mhkrst.getFkByBhkrstId().getCodeName() +",";
                }
                mhkrstClts = mhkrstClts.substring(0, mhkrstClts.length() -1)+"。";
            }
            tdTjBhk.setOcpBhkrstdes(mhkrstClts);

            //主检医师工号-null

            if(tdTjBhkClt.getFkByMhkdctId() != null){
                tdTjBhk.setMhkdct(tdTjBhkClt.getFkByMhkdctId().getEmpName());
            }

            tdTjBhk.setJdgdat(tdTjBhkClt.getJdgdat());
            tdTjBhk.setRptPrintDate(tdTjBhkClt.getRptPrintDate());
            //接触危害因素
            List<TdTjBadrsnsClt> tdTjBadrsnsClts  = tdTjBhkClt.getTdTjBadrsnsClts();
            String badrsnClts ="";
            if(tdTjBadrsnsClts != null && tdTjBadrsnsClts.size()>0){
                for(TdTjBadrsnsClt bads:tdTjBadrsnsClts){
                    badrsnClts += bads.getFkByBadrsnId().getCodeName() +",";
                }
                badrsnClts = badrsnClts.substring(0, badrsnClts.length() -1);
            }
            tdTjBhk.setBadrsn(badrsnClts);
            //是否缺项
            List<TdTjBhksubClt> tdTjBhksubClts = tdTjBhkClt.getTdTjBhksubClts();
            Integer IF_LACK = 0;
            if(tdTjBhksubClts != null && tdTjBhksubClts.size() >0){
                for(TdTjBhksubClt bhksub :tdTjBhksubClts){
                    if(bhksub.getIfLack().intValue() == 1){
                        IF_LACK = 1;
                    }
                }
            }
            tdTjBhk.setIfLackitm(IF_LACK);

            //是否有疑似职业病
            List<TdTjSupoccdiseClt> tdTjSupoccdiseClts = tdTjBhkClt.getTdTjSupoccdiseClts();
            if(tdTjSupoccdiseClts != null && tdTjSupoccdiseClts.size() >0){
                tdTjBhk.setIfTargetdis(1);
            }else{
                tdTjBhk.setIfTargetdis(0);
            }

            //是否有职业禁忌症
            List<TdTjContraindClt> tdTjContraindClts = tdTjBhkClt.getTdTjContraindClts();
            if(tdTjContraindClts != null && tdTjContraindClts.size() >0){
                tdTjBhk.setIfWrktabu(1);
            }else{
                tdTjBhk.setIfWrktabu(0);
            }

            //是否有必检项目未检 --null
            //必检项目未检处理信息 --null
            //必检项目未检处理标记置为0，自动处理
            tdTjBhk.setProcessLack(0);

            tdTjBhk.setIfRhk(tdTjBhkClt.getIfRhk());
            //

            //增加证件类型
            TsSimpleCode code = tdTjBhkClt.getFkByCardTypeId();
            if("88".equals(code.getCodeNo())){
                tdTjBhk.setPsnType(3);
            }else if("01".equals(code.getCodeNo())){
                tdTjBhk.setPsnType(1);
            }else{
                tdTjBhk.setPsnType(2);
            }

            tdTjBhk.setFkByCardTypeId(code);

            TdTjExmsdata exmsdata = null;
            //上次体检编号（复检使用）+初检编号（复检使用）--复检时存储
            if(tdTjBhkClt.getIfRhk().intValue() == 1){
                TdTjBhkClt fkByLastBhkId = service.find(TdTjBhkClt.class,tdTjBhkClt.getFkByLastBhkId().getRid());
                if(fkByLastBhkId != null){
                    tdTjBhk.setLastBhkCode(fkByLastBhkId.getBhkCode());
                    //通过体检编号找到业务表中的体检主表取LAST_FST_BHK_CODE
                    TdTjBhk bhk = service.findTdTjBhkByUUID(fkByLastBhkId.getUuid());
                    if(bhk != null ){
                        if(StringUtils.isNotBlank(bhk.getLastFstBhkCode())){
                            tdTjBhk.setLastFstBhkCode(bhk.getLastFstBhkCode());
                        }else{
                            tdTjBhk.setLastFstBhkCode(bhk.getBhkCode());
                        }
                        //存储关联的上次体检记录的吸烟史信息到当前记录的正式库记录
                        if (!CollectionUtils.isEmpty(bhk.getTdTjExmsdatas())) {
                            TdTjExmsdata exmsdataLast = bhk.getTdTjExmsdatas().get(0);
                            exmsdata = new TdTjExmsdata();
                            exmsdata.setTdTjBhk(tdTjBhk);
                            exmsdata.setFkBySmkstaId(exmsdataLast.getFkBySmkstaId());
                            exmsdata.setSmkyerqty(exmsdataLast.getSmkyerqty());
                            exmsdata.setSmkmthqty(exmsdataLast.getSmkmthqty());
                            exmsdata.setSmkdayble(exmsdataLast.getSmkdayble());
                            service.preEntity(exmsdata);
                        }
                        //20201127 任务20811 调用initTdTjBhk方法前 已经调用复检记录处理逻辑接口 处理 这里不需要再处理
                        /*if(StringUtils.isNotBlank(bhk.getLastFstBhkCode())){
                            dealTjBhkInfo(bhk.getLastFstBhkCode());
                        }else{
                            dealTjBhkInfo(bhk.getBhkCode());
                        }*/

                    }


                }
            }
            //工种
            tdTjBhk.setFkByWorkTypeId(tdTjBhkClt.getFkByWorkTypeId());
            tdTjBhk.setWorkOther(tdTjBhkClt.getWorkOther());

            TsSimpleCode work = tdTjBhkClt.getFkByWorkTypeId();
            if(null != work && null != work.getRid()){
                if("1".equals(work.getExtendS1())){
                    tdTjBhk.setWorkName(work.getCodeName()+"（"+tdTjBhkClt.getWorkOther()+"）");
                }else {
                    tdTjBhk.setWorkName(work.getCodeName());
                }
            }
            //创建日期为【体检结果录入】的首次提交日期
            tdTjBhk.setCreateDate(tdTjBhkClt.getFirstSubmitDate());
            //+数据报告是否及时
            tdTjBhk.setIfReportIntime(tdTjBhkClt.getIfReportIntime());
            tdTjBhk.setCreateManid(Global.getUser().getRid());

            tdTjBhk.setCrptName(tdTjBhkClt.getCrptName());
            tdTjBhk.setUuid(tdTjBhkClt.getUuid());

            //用工单位
            if (null!=tdTjBhkClt.getFkByEmpCrptId() && null!=tdTjBhkClt.getFkByEmpCrptId().getRid()) {
            	tdTjBhk.setFkByEntrustCrptId(tdTjBhkClt.getFkByEmpCrptId());
			}else {
				tdTjBhk.setFkByEntrustCrptId(null);
			}



            //体检结论，码表5005，通过扩展字段1看权重
            if(tdTjMhkrstClts != null && tdTjMhkrstClts.size()>0){
                //遍历 找出最大扩展字段
                Integer max = 1;
                for(TdTjMhkrstClt mhk :tdTjMhkrstClts){
                    String sMax = mhk.getFkByBhkrstId().getExtendS1();
                    Integer num = Integer.parseInt(sMax);
                    if(num > max){
                        max = num;
                    }
                }
                //通过扩展字段1找到结论
                TsSimpleCode rst = service.findTsSimpleCodeByextendS1(max);

                List<TdTjMhkrst> tdTjMhkrsts = new ArrayList<TdTjMhkrst>();
                TdTjMhkrst mhkrst = new TdTjMhkrst();
                mhkrst.setTdTjBhk(tdTjBhk);
                mhkrst.setTsSimpleCode(rst);
                mhkrst.setCreateDate(new Date());
                mhkrst.setCreateManid(Global.getUser().getRid());
                tdTjMhkrsts.add(mhkrst);

                tdTjBhk.setTdTjMhkrsts(tdTjMhkrsts);
            }
            tdTjBhk.setIfOnlyFs(0);
            //体检危害因素
            if(tdTjBadrsnsClts != null && tdTjBadrsnsClts.size() >0){
                List<TdTjBadrsns> tdTjBadrsnses = new ArrayList<TdTjBadrsns>();
                tdTjBhk.setIfOnlyFs(1);
                for(TdTjBadrsnsClt bad :tdTjBadrsnsClts){
                    TdTjBadrsns badrsn = new TdTjBadrsns();
                    badrsn.setTdTjBhk(tdTjBhk);
                    badrsn.setTsSimpleCode(bad.getFkByBadrsnId());
                    badrsn.setFkByExamConclusionId(bad.getFkByExamConclusionId());
                    badrsn.setQtjbName(bad.getQtjbName());
                    badrsn.setCreateDate(new Date());
                    badrsn.setCreateManid(Global.getUser().getRid());
                    if (bad.getFkByBadrsnId() == null || !"1".equals(bad.getFkByBadrsnId().getExtendS1())) {
                        tdTjBhk.setIfOnlyFs(0);
                    }
                    tdTjBadrsnses.add(badrsn);
                }
                tdTjBhk.setTdTjBadrsnses(tdTjBadrsnses);
                tdTjBhk.setOtherBadRsn(tdTjBhkClt.getOtherBadRsn());
            }
            //接触危害因素
            List<TdTjTchBadrsnsClt> tchBadrsnsCltList = tdTjBhkClt.getTdTjTchBadrsnsClts();
            if (ObjectUtil.isNotEmpty(tchBadrsnsCltList)) {
                List<TdTjTchBadrsns> tchBadrsnsList = new ArrayList<>();
                for (TdTjTchBadrsnsClt tchBadrsnsClt : tchBadrsnsCltList) {
                    TdTjTchBadrsns tchBadrsns = new TdTjTchBadrsns();
                    tchBadrsns.setFkByBhkId(tdTjBhk);
                    tchBadrsns.setFkByBadrsnId(tchBadrsnsClt.getFkByBadrsnId());
                    tchBadrsns.setCreateDate(new Date());
                    tchBadrsns.setCreateManid(Global.getUser().getRid());
                    tchBadrsnsList.add(tchBadrsns);
                }
                tdTjBhk.setTdTjTchBadrsns(tchBadrsnsList);
                tdTjBhk.setTchOtherBadrsn(tdTjBhkClt.getTchOtherBadrsn());
            }

            //疑似职业病
            if(tdTjSupoccdiseClts != null && tdTjSupoccdiseClts.size() >0){
                List<TdTjSupoccdiselist> tdTjSupoccdiselists = new ArrayList<TdTjSupoccdiselist>();
                for(TdTjSupoccdiseClt occdise :tdTjSupoccdiseClts){
                    TdTjSupoccdiselist supOccdise = new TdTjSupoccdiselist();
                    supOccdise.setTdTjBhk(tdTjBhk);
                    supOccdise.setTsSimpleCodeByBadrsnId(occdise.getFkByBadrsnId());
                    supOccdise.setTsSimpleCodeByOccDiseid(occdise.getFkByOccDiseid());
                    supOccdise.setCrptName(occdise.getCrptName());
                    supOccdise.setCreateDate(new Date());
                    supOccdise.setCreateManid(Global.getUser().getRid());
                    tdTjSupoccdiselists.add(supOccdise);
                }
                tdTjBhk.setTdTjSupoccdiselists(tdTjSupoccdiselists);
            }

            //问诊(非)放射职业史信息表
            List<TdTjEmhistoryClt> tdTjEmhistoryClts = tdTjBhkClt.getTdTjEmhistoryClts();
            if(tdTjEmhistoryClts != null && tdTjEmhistoryClts.size() >0){
                List<TdTjEmhistory> tdTjEmhistories = new ArrayList<TdTjEmhistory>();
                for(TdTjEmhistoryClt emClt:tdTjEmhistoryClts){
                    TdTjEmhistory em = new TdTjEmhistory();
                    em.setTdTjBhk(tdTjBhk);
                    em.setHisType(emClt.getHisType());
                    em.setNum(emClt.getNum());
                    em.setStastpDate(emClt.getStastpDate());
                    em.setUnitName(emClt.getUnitName());
                    em.setDepartment(emClt.getDepartment());
                    em.setWorkType(emClt.getWorkType());
                    em.setPrfraysrt(emClt.getPrfraysrt());
                    em.setDefendStep(emClt.getDefendStep());
                    em.setPrfwrklod(emClt.getPrfwrklod());
                    em.setPrfshnvlu(emClt.getPrfshnvlu());
                    em.setPrfexcshn(emClt.getPrfexcshn());
                    em.setPrfraysrt2(emClt.getPrfraysrt2());
                    em.setFsszl(emClt.getFsszl());
                    if(tdTjBhkClt.getFkByWzChkdoctId() != null){
                        em.setChkdoct(tdTjBhkClt.getFkByWzChkdoctId().getEmpName());
                    }
                    em.setChkdat(tdTjBhkClt.getWzChkdat());
                    em.setCreateManid(Global.getUser().getRid());
                    em.setCreateDate(new Date());
                    tdTjEmhistories.add(em);
                }

                tdTjBhk.setTdTjEmhistories(tdTjEmhistories);
            }

            //禁忌症
            if(tdTjContraindClts != null && tdTjContraindClts.size() >0){
                List<TdTjContraindlist> tdTjContraindlists = new ArrayList<TdTjContraindlist>();
                for(TdTjContraindClt conclt :tdTjContraindClts){
                    TdTjContraindlist con  = new TdTjContraindlist();
                    con.setTdTjBhk(tdTjBhk);
                    con.setTsSimpleCodeByBadrsnId(conclt.getFkByBadrsnId());
                    con.setTsSimpleCodeByContraindId(conclt.getFkByContraindId());
                    con.setCreateDate(new Date());
                    con.setCreateManid(Global.getUser().getRid());
                    tdTjContraindlists.add(con);
                }
                tdTjBhk.setTdTjContraindlists(tdTjContraindlists);
            }

            //既往病史
            List<TdTjAnamnesisClt> tdTjAnamnesisClts= tdTjBhkClt.getTdTjAnamnesisClts();
            if(tdTjAnamnesisClts != null && tdTjAnamnesisClts.size() >0){
                List<TdTjAnamnesis> tdTjAnamnesises = new ArrayList<TdTjAnamnesis>();
                for(TdTjAnamnesisClt anclt :tdTjAnamnesisClts){
                    TdTjAnamnesis an = new TdTjAnamnesis();
                    an.setTdTjBhk(tdTjBhk);
                    an.setHstnam(anclt.getHstnam());
                    an.setHstdat(anclt.getHstdat());
                    an.setHstunt(anclt.getHstunt());
                    an.setHstcruprc(anclt.getHstcruprc());
                    an.setHstlps(anclt.getHstlps());
                    an.setCreateDate(new Date());
                    an.setCreateManid(Global.getUser().getRid());
                    tdTjAnamnesises.add(an);
                }
                tdTjBhk.setTdTjAnamnesises(tdTjAnamnesises);
            }

            //问诊项目
            List<TdTjExmsdataClt> tdTjExmsdataClts = tdTjBhkClt.getTdTjExmsdataClts();
            if(tdTjExmsdataClts != null && tdTjExmsdataClts.size() >0){
                List<TdTjExmsdata> tdTjExmsdatas = new ArrayList<TdTjExmsdata>();
                for(TdTjExmsdataClt exmclt :tdTjExmsdataClts){
                    TdTjExmsdata exm = new TdTjExmsdata();
                    exm.setTdTjBhk(tdTjBhk);
                    exm.setMnrage(exmclt.getMnrage());
                    exm.setMns(exmclt.getMns());
                    exm.setCyc(exmclt.getCyc());
                    exm.setMnlage(exmclt.getMnlage());
                    exm.setIsxmns(exmclt.getIsxmns());
                    exm.setChldqty(exmclt.getChldqty());
                    exm.setAbrqty(exmclt.getAbrqty());
                    exm.setSlnkqty(exmclt.getSlnkqty());
                    exm.setStlqty(exmclt.getStlqty());
                    exm.setTrsqty(exmclt.getTrsqty());
                    exm.setMrydat(exmclt.getMrydat());
                    exm.setCplrdtcnd(exmclt.getCplrdtcnd());
                    exm.setCplprfhthcnd(exmclt.getCplprfhthcnd());
                    exm.setSmksta(exmclt.getSmksta());
                    exm.setSmkdayble(exmclt.getSmkdayble());
                    exm.setSmkyerqty(exmclt.getSmkyerqty());
                    exm.setSmkmthqty(exmclt.getSmkmthqty());
                    exm.setFkBySmkstaId(exmclt.getFkBySmkstaId());
                    exm.setWinsta(exmclt.getWinsta());
                    exm.setWindaymlx(exmclt.getWindaymlx());
                    exm.setWinyerqty(exmclt.getWinyerqty());
                    exm.setJzs(exmclt.getJzs());
                    exm.setGrs(exmclt.getGrs());
                    exm.setOth(exmclt.getOth());
                    exm.setCreateDate(new Date());
                    exm.setCreateManid(Global.getUser().getRid());
                    tdTjExmsdatas.add(exm);
                }
                tdTjBhk.setTdTjExmsdatas(tdTjExmsdatas);
            } else if (exmsdata != null) {
                //复检没有问诊记录，需要新增记录用于存储上一次体检记录吸烟史
                List<TdTjExmsdata> tdTjExmsdatas = new ArrayList<>();
                tdTjExmsdatas.add(exmsdata);
                tdTjBhk.setTdTjExmsdatas(tdTjExmsdatas);
            }

            //体检子表
            if(tdTjBhksubClts != null && tdTjBhksubClts.size() >0){
                List<TdTjBhksub> tdTjBhksubs = new ArrayList<TdTjBhksub>();
                for(TdTjBhksubClt subclt:tdTjBhksubClts){
                    TdTjBhksub sub = new TdTjBhksub();
                    sub.setTdTjBhk(tdTjBhk);
                    sub.setTbTjItems(subclt.getFkByItemId());
                    if(null != subclt.getFkByMsruntId() && null != subclt.getFkByMsruntId().getRid()){
                        sub.setMsruntId(subclt.getFkByMsruntId().getRid());
                    }
                    sub.setMsrunt(subclt.getMsrunt());
                    sub.setItemStdvalue(subclt.getItemStdvalue());
                    sub.setItemRst(subclt.getItemRst());
                    sub.setRgltag(subclt.getRgltag());
                    if(subclt.getRstDesc() != null){
                        sub.setRstDesc(subclt.getRstDesc().toString());
                    }
                    sub.setIfLack(subclt.getIfLack());
                    sub.setChkdat(subclt.getChkdat());
                    if(subclt.getFkByChkdoctId() != null){
                    	TdZwPsninfoComm a  =service.findTdZwPsninfoByID(subclt.getFkByChkdoctId().getRid());
                        sub.setChkdoct(a.getEmpName());
                    }

                    if(subclt.getJdgptn() != null){
                        sub.setJdgptn(Integer.parseInt(subclt.getJdgptn().toString()));
                    }

                    sub.setMinval(subclt.getMinval());
                    sub.setMaxval(subclt.getMaxval());
                    sub.setDiagRst(subclt.getDiagRest());
                    sub.setCreateDate(new Date());
                    sub.setCreateManid(Global.getUser().getRid());
                    if(null != subclt.getRstFlag()) {
                        sub.setRstFlag(subclt.getRstFlag());
                    }
                    if (subclt.getFkByDataVersionId() != null) {
                        sub.setFkByDataVersionId(subclt.getFkByDataVersionId());
                    }
                    tdTjBhksubs.add(sub);
                }
                tdTjBhk.setTdTjBhksubs(tdTjBhksubs);
            }

            //症状
            List<TdTjSymptomClt> tdTjSymptomClts = tdTjBhkClt.getTdTjSymptomClts();
            if(tdTjSymptomClts != null && tdTjSymptomClts.size() >0){
                List<TdTjSymptom> tdTjSymptoms = new ArrayList<TdTjSymptom>();
                for(TdTjSymptomClt symclt :tdTjSymptomClts){
                    TdTjSymptom sym = new TdTjSymptom();

                    sym.setTdTjBhk(tdTjBhk);
                    sym.setSymId(symclt.getFkBySymId());
                    sym.setOthsym(symclt.getOthsym());
                    sym.setChkdat(tdTjBhkClt.getWzChkdat());
                    if(tdTjBhkClt.getFkByWzChkdoctId() != null ){
                        sym.setChkdoct(tdTjBhkClt.getFkByWzChkdoctId().getEmpName());
                    }
                    sym.setCreateDate(new Date());
                    sym.setCreateManid(Global.getUser().getRid());
                    tdTjSymptoms.add(sym);
                }
                tdTjBhk.setTdTjSymptoms(tdTjSymptoms);
            }
            // 主检医师
            if(!CollectionUtils.isEmpty(tdTjBhkClt.getTdTjChiefDoctorList())) {
                StringBuilder chiefDoctorStr = new StringBuilder();
                for(TdTjChiefDoctor tdTjChiefDoctor : tdTjBhkClt.getTdTjChiefDoctorList()) {
                    if(null != tdTjChiefDoctor.getFkByMhkdctId() &&  StringUtils.isNotBlank(tdTjChiefDoctor.getFkByMhkdctId().getEmpName())) {
                        chiefDoctorStr.append("，").append(tdTjChiefDoctor.getFkByMhkdctId().getEmpName());
                    }
                }
                if(StringUtils.isNotBlank(chiefDoctorStr)) {
                    String chiefDoctor = chiefDoctorStr.deleteCharAt(0).toString();
                    tdTjBhk.setMhkdct(chiefDoctor);
                    if(chiefDoctor.length() > 25) {
                        tdTjBhk.setMhkdct(chiefDoctor.substring(0, 25));
                    }
                }
            }
            tdTjBhk.setDataSource(1);
            tdTjBhk.setSubmitChangedate(tdTjBhkClt.getSubmitChangedate());
            //保存体检业务表
            service.saveTdTjBhk(tdTjBhk);
        }
//        updateTjPersonArchNum(tdTjBhk.getTdTjPerson().getRid());
    }
    /**
 	 * <p>方法描述：更新体检人员档案份数</p>
 	 * @MethodAuthor qrr,2020年6月4日,updateTjPersonArchNum
     * */
    @Transactional(readOnly=false)
    public void updateTjPersonArchNum(Integer personId) {
    	if (null==personId) {
			return;
		}
    	StringBuffer sql = new StringBuffer();
    	sql.append("SELECT COUNT(1) FROM TD_TJ_BHK T ");
    	sql.append("INNER JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID");
    	sql.append(" WHERE T1.INTER_PRC_TAG =1");
    	sql.append(" AND T.PERSON_ID=").append(personId);
    	sql.append(" AND T.BHK_TYPE IN (3,4)");
    	int archNums = this.findCountBySql(sql.toString());
    	this.executeSql("UPDATE TD_TJ_PERSON SET ARCH_NUM ="+archNums+" WHERE RID ="+personId);
	}

    /***
     *  <p>方法描述：</p>
     *
     * @MethodAuthor maox,2019年6月3日,selectBhkByRid
     * @param uuid
     * @return
     */
    public TdTjBhk selectBhkByRid(String uuid) {
        StringBuilder sb=new StringBuilder();
        sb.append("select t from TdTjBhk t where 1=1 and t.uuid='").append(uuid).append("'");
        List<TdTjBhk> list = em.createQuery(sb.toString()).getResultList();
        if(null != list && list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * 根据体检编号和体检机构ID获取体检主表记录
     *
     * @param bhkCode  体检编号
     * @param bhkOrgId 体检机构ID
     * @return 体检主表记录
     */
    public TdTjBhk selectBhkByBhkCodeAndBhkOrgId(String bhkCode, Integer bhkOrgId) {
        StringBuilder sql = new StringBuilder();
        sql.append("select t from TdTjBhk t where ")
                .append(" t.bhkCode='").append(bhkCode).append("' ")
                .append(" and t.tbTjSrvorg.rid='").append(bhkOrgId).append("'");
        return findOneByHql(sql.toString(), TdTjBhk.class);
    }

    /**
     * 根据体检编号和体检机构ID获取体检主表报告打印日期
     *
     * @param bhkCode  体检编号
     * @param bhkOrgId 体检机构ID
     * @return 体检主表记录
     */
    public TdTjBhk selectBhkRptDateByBhkCodeAndBhkOrgId(String bhkCode, Integer bhkOrgId) {
        String sql = "select new TdTjBhk(t.rid, t.rptPrintDate) from TdTjBhk t where " +
                " t.bhkCode='" + bhkCode + "' " +
                " and t.tbTjSrvorg.rid='" + bhkOrgId + "'";
        return findOneByHql(sql, TdTjBhk.class);
    }

    /***
     *  <p>方法描述：删除体检业务数据</p>
     *
     * @MethodAuthor maox,2019年6月3日,delTdTjBhk
     */
    @Transactional(readOnly = false)
    public void delTdTjBhk(TdTjBhkClt clt) throws Exception{
        this.service.upsertEntity(clt);
    }

    /**
     * @MethodName: dealTjBhkInfo
     * @Description: 处理初检记录的表和标记
     * @Param: [bhkCode]
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-10
    **/
    private void dealTjBhkInfo(String bhkCode){
        TdTjBhk bhk1 = service.findTdTjBhkByBhkCode(bhkCode);
        StringBuilder sb = new StringBuilder();
        if(null != bhk1){
            sb = new StringBuilder();
            sb.append(" delete from TD_TJ_RESULT_JUDGE_ITEM where main_id = ").append(bhk1.getRid());
            super.em.createNativeQuery(sb.toString()).executeUpdate();

            sb = new StringBuilder();
            sb.append(" delete from TD_TJ_RST_JUDGE_TRANS where main_id = (select rid from TD_ZDZYB_BHK_TRANS where BHK_ID =").append(bhk1.getRid()).append(")");
            super.em.createNativeQuery(sb.toString()).executeUpdate();

            sb = new StringBuilder();
            sb.append(" delete from TD_ZDZYB_BHK_TRANS where bhk_id = ").append(bhk1.getRid());
            super.em.createNativeQuery(sb.toString()).executeUpdate();


            sb = new StringBuilder();
            sb.append(" delete from TD_ZDZYB_DATA_LOG where bhk_id = ").append(bhk1.getRid());
            super.em.createNativeQuery(sb.toString()).executeUpdate();

            sb = new StringBuilder();
            sb.append(" update TD_TJ_BHK set IF_INTO_ZDZYB_ANALY =null,CHECK_STATE=null,BACK_RSN=null,");
            sb.append(" NOT_CHECK_STATE=null,NOT_CHECK_RSN=null where rid = ").append(bhk1.getRid());
            super.em.createNativeQuery(sb.toString()).executeUpdate();


        }
    }
    /**
     * <p>Description：查询是否有重卡记录 </p>
     * <p>Author： yzz 2025-02-13 </p>
     */
    public List<String> findRepeatCards(Integer bhkOrgRid, Integer cardTypeRid, String idc, String bhkDate,Integer rid,Integer repeatDays) {
        StringBuilder sql=new StringBuilder();
        sql.append(" select BHK_CODE from TD_TJ_BHK_CLT ");
        sql.append(" where nvl(DEL_MARK,0)=0 and IF_RHK=0 and BHKORG_ID=").append(bhkOrgRid);
        sql.append(" and CARD_TYPE_ID=").append(cardTypeRid);
        sql.append(" and IDC='").append(idc).append("'");
        if(rid!=null){
            sql.append(" and RID!=").append(rid);
        }
        sql.append(" and BHK_DATE>=to_date('").append(bhkDate).append("','YYYY-MM-DD')- INTERVAL '").append(repeatDays).append("' DAY");
        sql.append(" and BHK_DATE<=to_date('").append(bhkDate).append("','YYYY-MM-DD')+ INTERVAL '").append(repeatDays).append("' DAY");
        return this.findDataBySqlNoPage(sql.toString(),null);
    }
}
