package com.chis.modules.heth.comm.web;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chis.common.entity.ExcelExportObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.AbnormalBadrsnExportVo;
import com.chis.modules.heth.comm.entity.TdTjBhk;
import com.chis.modules.heth.comm.entity.TdTjBhkCheckOrgReturnedDataExportEntity;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.system.entity.TdTjExport;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.context.RequestContext;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.model.SelectItem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;

/**
 * <p>类描述：职业健康检查审核情况查询-体检机构</p>
 *
 * @ClassAuthor yzz, 2021年5月13日, TdTjBHkCheckOrgBean
 */
@ManagedBean(name = "tdTjBHkCheckOrgBean")
@ViewScoped
public class TdTjBHkCheckOrgBean extends FacesEditBean implements IProcessData {
    private static final long serialVersionUID = 1L;
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private HethStaQueryCommServiceImpl hethStaQueryCommService = SpringContextHolder.getBean(HethStaQueryCommServiceImpl.class);
    private HethBaseCommServiceImpl hethBaseCommService = SpringContextHolder.getBean(HethBaseCommServiceImpl.class);
    /**
     * 查询条件：用工单位地区
     */
    private List<TsZone> entrustCrptZoneList;
    private String searchEntrustCrptZoneCode;
    private String searchEntrustCrptZoneName;
    /**
     * 查询条件：用工单位名称
     */
    private String searchEntrustCrptName;
    /**
     * 查询条件：用工单位社会信用代码
     */
    private String searchEntrustCreditCode;
    /**
     * 查询条件：用人单位地区
     */
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    /**
     * 查询条件：用人单位名称
     */
    private String searchCrptName;
    /**
     * 查询条件：用人单位社会信用代码
     */
    private String searchCreditCode;
    /**
     * 查询条件：姓名
     */
    private String searchPersonName;
    /**
     * 查询条件：身份证
     */
    private String searchIdc;
    /**
     * 查询条件：体检日期查询-开始日期
     */
    private Date searchBhkBdate;
    /**
     * 查询条件：体检日期查询-结束日期
     */
    private Date searchBhkEdate;
    /**
     * 查询条件-在岗状态
     */
    private List<TsSimpleCode> onGuardList;
    private String selectOnGuardNames;
    /**
     * 查询条件-在岗状态
     */
    private String selectOnGuardIds;
    /**
     * 查询条件-危害因素
     */
    private List<TsSimpleCode> badRsnList;
    private String selectBadRsnNames;
    /**
     * 查询条件-危害因素
     */
    private String selectBadRsnIds;
    /**
     * 查询条件：体检编号
     */
    private String searchBhkCode;
    /**
     * 查询条件：报告出具开始日期
     */
    private Date startRptPrintDate;
    /**
     * 查询条件：报告出具结束日期
     */
    private Date endRptPrintDate;
    /**
     * 查询条件：监测类别
     */
    private String[] jcTypes;
    /**
     * 查询条件：是否复检
     */
    private String[] ifRhks;
    /**
     * 查询条件：是否正常
     */
    private String[] ifAbnormals;
    /**
     * 选中的状态
     */
    private String[] states;
    private List<String> returnedStates;
    /**
     * 所有状态
     */
    private List<SelectItem> stateList = new ArrayList<>();
    /*体检主表 rid*/
    private Integer rid;
    /*详情 体检主表 接收类*/
    private TdTjBhk tdTjBhk;
    /**
     * 审核级别
     */
    private String checkLevel;
    /*详情界面显示审批意见 1：初审（退回）  2：初审+复审  3：初审+复审+终审*/
    private int showOption;
    /**
     * 异常情况
     */
    private List<String> unAbnormalsList = new ArrayList<>();
    /*体检基本信息*/
    private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();
    /*** 体检项目的布局表格*/
    private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
            OutputPanel.COMPONENT_TYPE);

    /*是否显示退回意见*/
    private boolean ifReturnPart;
    /*显示审核意见*/
    private String auditAdv;
    /*显示审核机构*/
    private String unitName;
    /*显示审核日期*/
    private Date smtDate;

    /**
     * 是否有国家上传工具
     */
    private String ifCountryUpload;
    /**
     * 选择的结果集
     */
    private List<Object[]> selectEntitys;
    /**
     * 情况说明弹出框 文本
     */
    private String infoTipMsg;

    /**
     * 企业规模码表MAP
     * <p>key: rid; value: 码表;</p>
     */
    private Map<Integer, TsSimpleCode> simpleCodeMap;

    private Map<Integer, List<AbnormalBadrsnExportVo>> abnormalBadrsnExportVoListMap;

    private List<Object[]> tbTjSrvorgList = new ArrayList<>();

    /** 查询条件异常信息 */
    private String searchAbnomalInfo;
    /** 查询条件退回原因 */
    private String searchBackUpRsn;
    private Map<String, TsSimpleCode> busTypeMap;
    private final Integer MAX_EXPORT_LOAD;
    private TdTjExport tdTjExport;
    private Map<String, String> stateMap;
    /**地区级别：2:省级3：市级4：区县级*/
    private Integer zoneType;

    public TdTjBHkCheckOrgBean() {
        this.ifSQL = true;
        this.checkLevel = PropertyUtils.getValue("checkLevel");
        /*初始化  审核状态*/
        initSearchStates();
        /*初始化 查询条件*/
        this.initParam();
        this.isnBusType();
        this.MAX_EXPORT_LOAD = PropertyUtils.getValue("asyncExport.times") == null ? 0 : Integer.parseInt(PropertyUtils.getValue("asyncExport.times"));
//        this.searchAction();
        this.tbTjSrvorgList = hethStaQueryCommService.findTjSvorgByRegUnitId(Global.getUser().getTsUnit().getRid());
    }

    /**
     * <p>方法描述：导出类型码表初始化</p>
     *
     * @MethodAuthor hsj 2024-08-22 17:12
     */
    private void isnBusType() {
        this.busTypeMap = new HashMap();
        List<TsSimpleCode> simpleCodes = this.commService.findLevelSimpleCodesByTypeId("5550");
        if (CollectionUtils.isEmpty(simpleCodes)) {
            return;
        }
        for (TsSimpleCode t : simpleCodes) {
            if (StringUtils.isBlank(t.getExtendS1())) {
                continue;
            }
            this.busTypeMap.put(t.getExtendS1(), t);
        }
    }

    /**
     * <p>方法描述：初始化查询条件</p>
     *
     * @MethodAuthor yzz, 2021年5月14日, initParam
     */
    public void initParam() {
        this.simpleCodeMap = new HashMap<>(16);
        //默认“上一年11月16号”
        this.startRptPrintDate = DateUtils.getFixedDate(-1,11,16);
        this.endRptPrintDate = new Date();
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        if(null==tsZone.getRealZoneType()){
            this.zoneType = tsZone.getZoneType().intValue();
        }else {
            this.zoneType = tsZone.getRealZoneType().intValue();
        }
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "");
        this.entrustCrptZoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb().substring(0, 2), true, "", "");
        this.searchEntrustCrptZoneCode = this.entrustCrptZoneList.get(0).getZoneCode();
        this.searchEntrustCrptZoneName = this.entrustCrptZoneList.get(0).getZoneName();
        this.searchZoneCode = "";
        this.searchZoneName = "";

        /*初始化在岗状态*/
        this.onGuardList = commService.findSimpleCodesByTypeId("5009");
        /*初始化危害因素*/
        this.badRsnList = commService.findSimpleCodesByTypeId("5007");
        //初始化行业类别
        initTsSimpleCodeMapHelper("5002", this.simpleCodeMap);
        //初始化企业规模
        initTsSimpleCodeMapHelper("5004", this.simpleCodeMap);
    }

    private void initTsSimpleCodeMapHelper(String typeCode, Map<Integer, TsSimpleCode> map) {
        List<TsSimpleCode> list = this.commService.findLevelSimpleCodesByTypeId(typeCode);
        if (!CollectionUtils.isEmpty(list)) {
            for (TsSimpleCode simpleCode : list) {
                map.put(simpleCode.getRid(), simpleCode);
            }
            list.clear();
        }
    }

    /**
     * <p>方法描述： 初始化  审核状态   </p>
     *
     * @MethodAuthor yzz，2021-05-14，initSearchStates
     **/
    public void initSearchStates() {
        this.stateMap = new HashMap<>();
        ifCountryUpload = commService.findParamValue("IF_HAVE_GS_UPLOAD");
        if ("3".equals(checkLevel)) {
            if ("1".equals(ifCountryUpload)) {
                this.states = new String[]{"0", "2", "4", "7"};
            } else {
                this.states = new String[]{"0", "2", "4"};
            }
            this.stateList.add(new SelectItem("1", "待初审"));
            this.stateList.add(new SelectItem("0", "初审退回"));
            this.stateList.add(new SelectItem("3", "待复审"));
            this.stateList.add(new SelectItem("2", "复审退回"));
            this.stateList.add(new SelectItem("5", "待终审"));
            this.stateList.add(new SelectItem("4", "终审退回"));
            this.stateList.add(new SelectItem("6", "终审通过"));
            if ("1".equals(ifCountryUpload)) {
                this.stateList.add(new SelectItem("7", "国家退回"));

            }
        } else if ("2".equals(checkLevel)) {
            if ("1".equals(ifCountryUpload)) {
                this.states = new String[]{"0", "4", "7"};
            } else {
                this.states = new String[]{"0", "4"};
            }
            this.stateList.add(new SelectItem("1", "待初审"));
            this.stateList.add(new SelectItem("0", "初审退回"));
            this.stateList.add(new SelectItem("5", "待终审"));
            this.stateList.add(new SelectItem("4", "终审退回"));
            this.stateList.add(new SelectItem("6", "终审通过"));
            if ("1".equals(ifCountryUpload)) {
                this.stateList.add(new SelectItem("7", "国家退回"));
            }
        }
        if (CollectionUtils.isEmpty(this.stateList)) {
            return;
        }
        for (SelectItem item : this.stateList) {
            this.stateMap.put(StringUtils.objectToString(item.getValue()), item.getLabel());
        }
    }


    @Override
    public String[] buildHqls() {
        return buildSql(1);
    }

    /**
     * 构建SQL语句
     *
     * @param type 1: 页面查询(默认); 2: 退回数据导出;
     * @return SQL语句
     */
    private String[] buildSql(int type) {
        String allParma;
        if (type == 2) {
            allParma = "T.RID, " +
                    "CASE WHEN T6.ZONE_TYPE > 2 THEN SUBSTR(T6.FULL_NAME, INSTR(T6.FULL_NAME, '_') + 1) ELSE T6.FULL_NAME END ZONE_NAME, " +
                    "T5.CRPT_NAME, " +
                    "T.PERSON_NAME, " +
                    "T.BHK_CODE, " +
                    "T2.CODE_NAME, " +
                    "T.JC_TYPE, " +
                    "T.BHK_DATE, " +
                    "T.IF_RHK, " +
                    "T.IF_ABNOMAL, " +
                    "0 a, " +
                    "T.STATE, " +
                    "case when T.STATE=0 then COUNTY_AUDIT_ADV when T.STATE=2 then CITY_AUDIT_ADV when T.STATE=4 then PRO_AUDIT_ADV else NULL end backreason, " +
                    "T.ERR_MSG, " +
                    "T6.if_prov_direct, " +
                    "T.IF_CRPT_SIZE_NOSTD as a15, " +
                    "T1.CRPT_SIZE_ID, " +
                    "T.IF_INDUS_TYPE_NOSTD, " +
                    "T1.INDUS_TYPE_ID, " +
                    "T.IF_INTEITM_LACK, " +
                    "T.LACK_MSG, " +
                    "T6.ZONE_GB, " +
                    "T.IF_WRK_AGE_NOSTD, " +
                    "T.RPT_PRINT_DATE ";
        } else {
            allParma = "T.RID, " +
                    "CASE WHEN T6.ZONE_TYPE > 2 THEN SUBSTR(T6.FULL_NAME, INSTR(T6.FULL_NAME, '_') + 1) ELSE T6.FULL_NAME END ZONE_NAME, " +
                    "T5.CRPT_NAME, " +
                    "T.PERSON_NAME, " +
                    "T.IDC, " +
                    "T2.CODE_NAME AS onguard, " +
                    "T.JC_TYPE, " +
                    "T.IF_RHK, " +
                    "T.IF_ABNOMAL, " +
                    "T3.UNIT_NAME, " +
                    "T.BHK_DATE, " +
                    "T.STATE, " +
                    "T6.ZONE_GB, " +
                    "T6.if_city_direct, " +
                    "T6.if_prov_direct, " +
                    "0 AS IFCHECK, " +
                    "case when T.STATE=0 then COUNTY_AUDIT_ADV when T.STATE=2 then CITY_AUDIT_ADV when T.STATE=4 then PRO_AUDIT_ADV else NULL end backreason, " +
                    "T.ERR_MSG, " +
                    "'' AS abnomalInfo, "+
                    "'' AS abnomalInfoTip, "+
                    "T.RPT_PRINT_DATE ";
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT * FROM (");
        sql.append(" select ").append(allParma);
        sql.append(" FROM TD_TJ_BHK T ");
        sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.ONGUARD_STATEID ");
        sql.append(" LEFT JOIN TB_TJ_SRVORG T3 ON T3.RID = T.BHKORG_ID ");
        sql.append(" LEFT JOIN TS_ZONE T4 ON T4.RID = T1.ZONE_ID ");
        sql.append(" LEFT JOIN TB_TJ_CRPT T5 ON T5.RID = T.ENTRUST_CRPT_ID ");
        sql.append(" LEFT JOIN TS_ZONE T6 ON T6.RID = T5.ZONE_ID ");
        sql.append(" WHERE 1=1 AND T.IF_INTO_CHECK =1 ");
        if (!CollectionUtils.isEmpty(this.tbTjSrvorgList)) {
        	sql.append(" AND T.BHKORG_ID= ").append(this.tbTjSrvorgList.get(0)[0]);
		}
        //用工单位地区
        if (StringUtils.isNotBlank(this.searchEntrustCrptZoneCode)) {
            sql.append(" AND T6.ZONE_GB LIKE :entrustCrptZoneCode escape '\\'");
            this.paramMap.put("entrustCrptZoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchEntrustCrptZoneCode).trim()) + "%");
        }
        //用工单位名称
        if (StringUtils.isNotBlank(this.searchEntrustCrptName)) {
            sql.append(" AND T5.CRPT_NAME LIKE :entrustCrptName  escape '\\'");
            this.paramMap.put("entrustCrptName", "%" + StringUtils.convertBFH(this.searchEntrustCrptName.trim()) + "%");
        }
        //用工单位社会信用代码
        if (StringUtils.isNotBlank(this.searchEntrustCreditCode)) {
            sql.append(" AND T5.INSTITUTION_CODE = :entrustCrptCreditCode");
            this.paramMap.put("entrustCrptCreditCode", this.searchEntrustCreditCode);
        }
        //用人单位地区
        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            sql.append(" AND T4.ZONE_GB LIKE :crptZoneCode escape '\\'");
            this.paramMap.put("crptZoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        //用人单位名称
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sql.append(" AND T1.CRPT_NAME LIKE :crptName  escape '\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        //用人单位社会信用代码
        if (StringUtils.isNotBlank(this.searchCreditCode)) {
            sql.append(" AND T1.INSTITUTION_CODE = :creditCode");
            this.paramMap.put("creditCode", this.searchCreditCode);
        }
        //人员姓名
        if (StringUtils.isNotBlank(this.searchPersonName)) {
            sql.append(" AND T.PERSON_NAME LIKE :personName  escape '\\'");
            this.paramMap.put("personName", "%" + StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
        }
        //人员身份证
        if (StringUtils.isNotBlank(this.searchIdc)) {
            sql.append(" AND T.IDC  = :personIdc ");
            this.paramMap.put("personIdc", StringUtils.convertBFH(this.searchIdc.trim()));
        }
        //体检编号
        if (StringUtils.isNotBlank(this.searchBhkCode)) {
            sql.append(" AND T.BHK_CODE  = :searchBhkCode ");
            this.paramMap.put("searchBhkCode", StringUtils.convertBFH(this.searchBhkCode.trim()));
        }

        // 体检日期
        if (null != this.searchBhkBdate) {
            sql.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBhkBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
        }
        if (null != this.searchBhkEdate) {
            sql.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchBhkEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
        }
        //报告出具日期
        if ( null != this.startRptPrintDate) {
            String startRptPrintDateStr = DateUtils.formatDate(this.startRptPrintDate, "yyyy-MM-dd");
            sql.append(" AND T.RPT_PRINT_DATE >= TO_DATE('").append(startRptPrintDateStr).append("','yyyy-MM-dd') ");
        }
        if ( null != this.endRptPrintDate) {
            String endRptPrintDateStr = DateUtils.formatDate(this.endRptPrintDate, "yyyy-MM-dd");
            sql.append(" AND T.RPT_PRINT_DATE <= TO_DATE('")
                    .append(endRptPrintDateStr).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss') ");
        }
        //在岗状态
        if (StringUtils.isNotBlank(selectOnGuardIds)) {
            sql.append(" AND T.ONGUARD_STATEID in (").append(this.selectOnGuardIds).append(")");
        }
        //危害因素
        if (StringUtils.isNotBlank(selectBadRsnIds)) {
            sql.append(" AND EXISTS(SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID AND TT.BADRSN_ID IN (").append(this.selectBadRsnIds).append("))");
        }
        // 监测类别
        if (null != jcTypes && jcTypes.length > 0) {
            sql.append(" AND T.JC_TYPE in (").append(StringUtils.array2string(jcTypes, ",")).append(")");
        }
        // 是否复检
        if (null != ifRhks && ifRhks.length > 0) {
            sql.append(" AND T.IF_RHK in (").append(StringUtils.array2string(ifRhks, ",")).append(")");
        }
        // 是否结论异常
        if (null != ifAbnormals && ifAbnormals.length > 0) {
            sql.append(" AND T.IF_ABNOMAL in (").append(StringUtils.array2string(ifAbnormals, ",")).append(")");
        }
        // 状态
        StringBuilder stateSb = new StringBuilder();

        if (type == 2) {
            if (null != this.returnedStates && this.returnedStates.size() > 0) {
                for (String state : this.returnedStates) {
                    stateSb.append(",").append(state);
                }
                sql.append(" AND (T.STATE IN (").append(stateSb.substring(1)).append(") ");
            }
        } else {
            if (null != states && states.length > 0) {
                for (String state : states) {
                    stateSb.append(",").append(state);
                }
            } else {
                if (null != stateList && stateList.size() > 0) {
                    for (SelectItem item : stateList) {
                        stateSb.append(",").append(item.getValue());
                    }
                }
            }
            sql.append(" AND (T.STATE IN (").append(stateSb.substring(1)).append(") ");
        }
        //异常信息
        if (StringUtils.isNotBlank(this.searchAbnomalInfo)) {
            //有异常信息 必须是异常
            sql.append(" AND T.IF_ABNOMAL=1 ");
            //GBZ188不规范或者异常信息表有匹配上
            sql.append(" AND (")
                    .append("  (")
                    .append("      T.IF_INTEITM_LACK=1 ")
                    .append("      AND T.LACK_MSG LIKE :searchAbnomalInfo  escape '\\' ")
                    .append("  )")
                    .append(" OR ")
                    .append(" EXISTS(")
                    .append("   SELECT 1 FROM TD_TJ_BHK_ABNOMAL K WHERE K.BHK_ID = T.RID AND K.ABNOMAL_INFO LIKE :searchAbnomalInfo  escape '\\' ")
                    .append(" ) ")
                    .append(")");
            this.paramMap.put("searchAbnomalInfo", "%" + StringUtils.convertBFH(this.searchAbnomalInfo.trim()) + "%");
        }
        //退回原因
        if (StringUtils.isNotBlank(this.searchBackUpRsn)) {
            sql.append("  AND ( ")
                    .append("   (")
                    .append("     T.STATE=0 AND T.COUNTY_AUDIT_ADV LIKE :searchBackUpRsn  escape '\\' ")
                    .append("   )")
                    .append("   OR ")
                    .append("   (")
                    .append("     T.STATE=2 AND T.CITY_AUDIT_ADV LIKE :searchBackUpRsn  escape '\\' ")
                    .append("   )")
                    .append("   OR ")
                    .append("   (")
                    .append("     T.STATE=4 AND T.PRO_AUDIT_ADV LIKE :searchBackUpRsn  escape '\\' ")
                    .append("   )")
                    .append("   OR ")
                    .append("   (")
                    .append("     T.STATE=7 AND T.ERR_MSG LIKE :searchBackUpRsn  escape '\\' ")
                    .append("   )")
                    .append(" )");
            this.paramMap.put("searchBackUpRsn", "%" + StringUtils.convertBFH(this.searchBackUpRsn.trim()) + "%");
        }
        sql.append(")");
        sql.append(")A");
        String h2 = "SELECT COUNT(*) FROM (" + sql + ")";
        String h1 = "SELECT * FROM (" + sql + ")AA  ORDER BY AA.ZONE_GB, AA.CRPT_NAME, AA.PERSON_NAME, AA.BHK_DATE";
        return new String[]{h1, h2};
    }


    @Override
    public void addInit() {

    }

    /*详情界面初始化*/
    @Override
    public void viewInit() {
        /*初始化体检基本信息*/
        tjBhkInfoBean.setIfShowBaseOnly(false);
        tjBhkInfoBean.setRid(rid);
        tjBhkInfoBean.setArchivePanel(archivePanel);
        tjBhkInfoBean.setIfManagedOrg(true);
        /*身份证号不加密*/
        tjBhkInfoBean.setIfManagedOrg(false);
        tjBhkInfoBean.initBhkInfo();
        tdTjBhk = hethStaQueryCommService.findTdTjBhkById(rid);
        this.unAbnormalsList = hethBaseCommService.initUnAbnormalsList(this.tdTjBhk);
        /*状态初始化*/
        this.ifReturnPart = false;
        this.auditAdv = "";
        this.unitName = "";
        this.smtDate = null;
        /*退回判断*/
        if (tdTjBhk.getState() != null && (tdTjBhk.getState() == 0 || tdTjBhk.getState() == 2 || tdTjBhk.getState() == 4)) {
            /*初审退回*/
            if (tdTjBhk.getFkByCountyChkOrgid() != null && tdTjBhk.getState() == 0) {
                this.ifReturnPart = true;
                this.auditAdv = tdTjBhk.getCountyAuditAdv();
                this.unitName = tdTjBhk.getFkByCountyChkOrgid().getUnitname();
                this.smtDate = tdTjBhk.getCountySmtDate();
            }
            /*复审退回*/
            if (tdTjBhk.getFkByCiytChkOrgid() != null && tdTjBhk.getState() == 2) {
                this.ifReturnPart = true;
                this.auditAdv = tdTjBhk.getCityAuditAdv();
                this.unitName = tdTjBhk.getFkByCiytChkOrgid().getUnitname();
                this.smtDate = tdTjBhk.getCitySmtDate();
            }
            /*终审退回*/
            if (tdTjBhk.getFkByProChkOrgid() != null && tdTjBhk.getState() == 4) {
                this.ifReturnPart = true;
                this.auditAdv = tdTjBhk.getProAuditAdv();
                this.unitName = tdTjBhk.getFkByProChkOrgid().getUnitname();
                this.smtDate = tdTjBhk.getProSmtDate();
            }
        }

        if (StringUtils.isBlank(tdTjBhk.getErrMsg())) {
            tdTjBhk.setErrMsg("无");
        }
        this.forwardViewPage();
    }

    @Override
    public void modInit() {


    }

    @Override
    public void saveAction() {

    }

    /**
     * <p>方法描述：退回数据导出验证-异步</p>
     *
     * @MethodAuthor hsj 2024-08-22 17:14
     */
    public void exportReturnDataAction() {
        TsSimpleCode busType = this.busTypeMap.get("4");
        if (null == busType) {
            JsfUtil.addErrorMessage("导出失败，导出类型不存在！");
            return;
        }
        Integer tdTjExportCount = this.hethStaQueryCommService.findTdTjExportByOperPsnId(busType.getRid().toString());
        boolean isExported = tdTjExportCount != null && this.MAX_EXPORT_LOAD != null && tdTjExportCount >= this.MAX_EXPORT_LOAD;
        if (isExported) {
            JsfUtil.addErrorMessage("导出次数达到上限，请稍后导出！");
            return;
        }
        this.exportData();
    }

    /**
     * <p>方法描述：退回数据导出数据存储-异步<</p>
     *
     * @MethodAuthor hsj 2024-08-22 17:14
     */
    public void exportData() {
        this.tdTjExport = new TdTjExport();
        TsSimpleCode busType = this.busTypeMap.get("4");
        this.tdTjExport.setFkByBusTypeId(busType);
        this.exportConditionData();
        this.tdTjExport.setState(0);
        this.tdTjExport.setExportDate(new Date());
        this.tdTjExport.setFkByOperUnitId(Global.getUser().getTsUnit());
        this.tdTjExport.setFkByOperPsnId(Global.getUser());
        this.tdTjExport.setExportFileName(busType.getExtendS3());
        this.hethStaQueryCommService.saveTdTjExport(this.tdTjExport);
        JsfUtil.addSuccessMessage("导出任务已发送，请至导出文件下载模块下载！");
    }

    /**
     * <p>方法描述：退回数据导出数据查询条件封装-异步</p>
     *
     * @MethodAuthor hsj 2024-08-22 17:16
     */
    private void exportConditionData() {
        JSONObject jsonObject = new JSONObject();
        StringBuilder str = new StringBuilder();
        if (StringUtils.isNotBlank(this.searchEntrustCrptZoneCode)) {
            jsonObject.put("searchEntrustCrptZoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchEntrustCrptZoneCode).trim()));
            str.append("用工单位地区@*@").append(StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchEntrustCrptZoneName).trim())).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchEntrustCrptName)) {
            jsonObject.put("searchEntrustCrptName", StringUtils.convertBFH(this.searchEntrustCrptName.trim()));
            str.append("用工单位名称@*@").append(StringUtils.convertBFH(this.searchEntrustCrptName.trim())).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchEntrustCreditCode)) {
            jsonObject.put("searchEntrustCreditCode", this.searchEntrustCreditCode);
            str.append("用工单位社会信用代码@*@").append(this.searchEntrustCreditCode).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchZoneCode)) {
            jsonObject.put("searchZoneCode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()));
            str.append("用人单位地区@*@").append(StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneName).trim())).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchCrptName)) {
            jsonObject.put("searchCrptName", StringUtils.convertBFH(this.searchCrptName.trim()));
            str.append("用人单位名称@*@").append(StringUtils.convertBFH(this.searchCrptName.trim())).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchCreditCode)) {
            jsonObject.put("searchCreditCode", this.searchCreditCode);
            str.append("用人单位社会信用代码@*@").append(this.searchCreditCode).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchBhkCode)) {
            jsonObject.put("searchBhkCode", StringUtils.convertBFH(this.searchBhkCode.trim()));
            str.append("体检编号@*@").append(StringUtils.convertBFH(this.searchBhkCode.trim())).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchIdc)) {
            jsonObject.put("searchIdc", StringUtils.convertBFH(this.searchIdc.trim()));
            str.append("证件号码@*@").append(StringUtils.convertBFH(this.searchIdc.trim())).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchPersonName)) {
            jsonObject.put("searchPersonName", StringUtils.convertBFH(this.searchPersonName.trim()));
            str.append("人员姓名@*@").append(StringUtils.convertBFH(this.searchPersonName.trim())).append("#*#");
        }

        String searchBhkBdate = "";
        String searchBhkEdate = "";
        if (this.searchBhkBdate != null) {
            searchBhkBdate = DateUtils.formatDate(this.searchBhkBdate, new Object[0]);
            jsonObject.put("searchBhkBdate", searchBhkBdate);
        }

        if (this.searchBhkEdate != null) {
            searchBhkEdate = DateUtils.formatDate(this.searchBhkEdate, new Object[0]);
            jsonObject.put("searchBhkEdate", searchBhkEdate);
        }

        if (this.searchBhkBdate != null || this.searchBhkEdate != null) {
            str.append("体检日期@*@").append(searchBhkBdate).append("~").append(searchBhkEdate).append("#*#");
        }

        if (StringUtils.isNotBlank(this.selectOnGuardIds)) {
            jsonObject.put("selectOnGuardIds", this.selectOnGuardIds);
            str.append("在岗状态@*@").append(this.selectOnGuardNames).append("#*#");
        }

        if (StringUtils.isNotBlank(this.selectBadRsnIds)) {
            jsonObject.put("selectBadRsnIds", this.selectBadRsnIds);
            str.append("体检危害因素@*@").append(this.selectBadRsnNames).append("#*#");
        }

        String startRptPrintDate = "";
        String endRptPrintDate = "";
        if (null != this.startRptPrintDate) {
            startRptPrintDate = DateUtils.formatDate(this.startRptPrintDate, new Object[0]);
            jsonObject.put("startRptPrintDate", startRptPrintDate);
        }

        if (null != this.endRptPrintDate) {
            endRptPrintDate = DateUtils.formatDate(this.endRptPrintDate, new Object[0]);
            jsonObject.put("endRptPrintDate", endRptPrintDate);
        }

        if (this.startRptPrintDate != null || this.endRptPrintDate != null) {
            str.append("报告出具日期@*@").append(startRptPrintDate).append("~").append(endRptPrintDate).append("#*#");
        }

        if (null != this.jcTypes && this.jcTypes.length > 0) {
            jsonObject.put("jcTypes", StringUtils.array2string(this.jcTypes, ","));
            str.append("监测类别@*@").append(StringUtils.array2string(this.transformStrings(this.jcTypes, 1), "，")).append("#*#");
        }

        if (null != this.ifRhks && this.ifRhks.length > 0) {
            jsonObject.put("ifRhks", StringUtils.array2string(this.ifRhks, ","));
            str.append("是否复检@*@").append(StringUtils.array2string(this.transformStrings(this.ifRhks, 2), "，")).append("#*#");
        }

        if (null != this.ifAbnormals && this.ifAbnormals.length > 0) {
            jsonObject.put("ifAbnormals", StringUtils.array2string(this.ifAbnormals, ","));
            str.append("是否异常@*@").append(StringUtils.array2string(this.transformStrings(this.ifAbnormals, 2), "，")).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchAbnomalInfo)) {
            jsonObject.put("searchAbnomalInfo", StringUtils.convertBFH(this.searchAbnomalInfo.trim()));
            str.append("异常信息@*@").append(StringUtils.convertBFH(this.searchAbnomalInfo.trim())).append("#*#");
        }

        if (StringUtils.isNotBlank(this.searchBackUpRsn)) {
            jsonObject.put("searchBackUpRsn", StringUtils.convertBFH(this.searchBackUpRsn.trim()));
            str.append("退回原因@*@").append(StringUtils.convertBFH(this.searchBackUpRsn.trim())).append("#*#");
        }


        if (null != this.states && this.states.length > 0) {
            List<String> states = new ArrayList<>();
            for (Object state : this.states) {
                states.add(Convert.toStr(state));
            }
            String statesStr = StringUtils.list2string(states, ",");
            jsonObject.put("states", statesStr);
            str.append("状态@*@").append(StringUtils.array2string(this.transformStrings(statesStr.split(","), 0), "，")).append("#*#");

        }
        if (!CollectionUtils.isEmpty(this.tbTjSrvorgList)) {
            jsonObject.put("searchUnitId", this.tbTjSrvorgList.get(0)[0]);
        }
        jsonObject.put("checkLevel", this.checkLevel);
        jsonObject.put("zoneType", zoneType);
        tdTjExport.setExportCondition(null == jsonObject ? null : JSON.toJSONString(jsonObject));
        tdTjExport.setExportConditionShow(str.toString());
    }

    /**
     * 根据给定的标记转换字符串数组
     *
     * @param strs 字符串数组，需要被转换
     * @param mark 转换标记，根据不同的标记值，转换逻辑会有所不同
     * @return 返回转换后的字符串数组
     * @MethodAuthor hsj 2024-08-22 17:28
     */
    private String[] transformStrings(String[] strs, Integer mark) {
        String[] strings = new String[strs.length];

        for (int i = 0; i < strs.length; ++i) {
            if (mark == 0) {
                Object value = this.stateMap.get(strs[i]);
                strings[i] = Convert.toStr(value);
                continue;
            }
            switch (strs[i]) {
                case "0":
                    strings[i] = (mark == 2) ? "否" : "";
                    break;
                case "1":
                    switch (mark) {
                        case 1:
                            strings[i] = "常规监测";
                            break;
                        case 2:
                            strings[i] = "是";
                            break;
                        default:
                            strings[i] = "";
                    }
                    break;
                case "2":
                    strings[i] = (mark == 1) ? "主动监测" : "";
                    break;
                default:
                    strings[i] = "";
                    break;
            }
        }
        return strings;
    }


    /**
     * 退回数据导出操作
     *
     * @return {@link StreamedContent 退回数据EXCEL}
     */
    public DefaultStreamedContent export() {
        RequestContext context = RequestContext.getCurrentInstance();
        context.execute("showStatus();");
        String[] sql = buildSql(2);
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql[0], this.paramMap));
        List<TdTjBhkCheckOrgReturnedDataExportEntity> returnedDataList = new ArrayList<>();
        List<Integer> bhkRidList = new ArrayList<>();
        for (Object[] o : dataList) {
            try {
                bhkRidList.add(((BigDecimal) o[0]).intValue());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        this.abnormalBadrsnExportVoListMap = new HashMap<>(16);
        List<AbnormalBadrsnExportVo> abnormalBadrsnExportVoList = findAbnormalBadrsnExportListByBhkRidList(bhkRidList);
        if (!CollectionUtils.isEmpty(abnormalBadrsnExportVoList)) {
            //过滤掉危害因素rid、危害因素名称或者不规范标记为空数据
            for (AbnormalBadrsnExportVo badrsnExportVo : abnormalBadrsnExportVoList) {
                if (null != badrsnExportVo.getBadRsnRid() &&
                        StringUtils.isNotBlank(badrsnExportVo.getBadRsnName()) &&
                        null != badrsnExportVo.getNostdFlag()) {
                    if (!this.abnormalBadrsnExportVoListMap.containsKey(badrsnExportVo.getBhkRid())) {
                        this.abnormalBadrsnExportVoListMap.put(badrsnExportVo.getBhkRid(), new ArrayList<AbnormalBadrsnExportVo>());
                    }
                    this.abnormalBadrsnExportVoListMap.get(badrsnExportVo.getBhkRid()).add(badrsnExportVo);
                }
            }
        }
        //根据体检rid集合 获取 主动监测异常信息 批量查询
        Map<Integer,List<Object[]>> abnomalMap=findBhkAbnomalsExportListByBhkRidList(bhkRidList);

        for (Object[] o : dataList) {
            TdTjBhkCheckOrgReturnedDataExportEntity returnedData = new TdTjBhkCheckOrgReturnedDataExportEntity();
            returnedData.setPropertyA(StringUtils.objectToString(o[1]), StringUtils.objectToString(o[2]), StringUtils.objectToString(o[3]), StringUtils.objectToString(o[4]), StringUtils.objectToString(o[5]));
            returnedData.setMonitoringTypes(getEscapeStringByObject(1, o[6]));
            returnedData.setBhkDate(DateUtils.formatDate((Timestamp) o[7], "yyyy-MM-dd"));
            returnedData.setRptPrintDate(DateUtils.formatDate((Timestamp) o[23], "yyyy-MM-dd"));
            returnedData.setRecheck(getEscapeStringByObject(2, o[8]));
            returnedData.setAbnormal(getEscapeStringByObject(3, o[9]));
            if ("是".equals(returnedData.getAbnormal())) {
                returnedData.setAbnormalSituation(getErrorMsg(abnomalMap.get(Integer.parseInt(o[0].toString())),o[20]));
            } else {
                returnedData.setAbnormalSituation("");
            }
            returnedData.setStatus(getEscapeStringByObject(4, o[11], o[14]));
            returnedData.setBackRsn(getEscapeStringByObject(5, o[11], o[12], o[13]));

            returnedDataList.add(returnedData);
        }

        ExcelExportUtil excelExportUtil = new ExcelExportUtil("职业健康检查审核退回数据导出", getReturnedDataExcelHeaders(), pakExcelExportDataList(returnedDataList));
        excelExportUtil.setFrozenPaneRowsNum(2);
        Workbook wb = excelExportUtil.exportExcel("");
        if (wb != null) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
                String fileName = "职业健康检查审核退回数据导出.xlsx";
                fileName = URLEncoder.encode(fileName, "UTF-8");
                wb.write(baos);
                baos.flush();
                byte[] aa = baos.toByteArray();
                context.execute("hideStatus();");
                return new DefaultStreamedContent(new ByteArrayInputStream(aa, 0, aa.length), "application/vnd.ms-excel", fileName);
            } catch (Exception e) {
                JsfUtil.addErrorMessage("文件导出错误，请联系管理员！");
            }
        }
        context.execute("hideStatus();");
        return null;
    }

    private List<ExcelExportObject[]> pakExcelExportDataList(List<TdTjBhkCheckOrgReturnedDataExportEntity> returnedDataList) {
        List<ExcelExportObject[]> dataList = new ArrayList<>();
        for (TdTjBhkCheckOrgReturnedDataExportEntity returnedData : returnedDataList) {
            ExcelExportObject[] objects = new ExcelExportObject[13];
            int index = 0;
            objects[index++] = new ExcelExportObject(returnedData.getZoneName());
            objects[index++] = new ExcelExportObject(returnedData.getEmployerName());
            objects[index++] = new ExcelExportObject(returnedData.getPersonnelName(), XSSFCellStyle.ALIGN_CENTER);
            objects[index++] = new ExcelExportObject(returnedData.getBhkCode(), XSSFCellStyle.ALIGN_CENTER);
            objects[index++] = new ExcelExportObject(returnedData.getJobStatus(), XSSFCellStyle.ALIGN_CENTER);
            objects[index++] = new ExcelExportObject(returnedData.getMonitoringTypes(), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(returnedData.getBhkDate(), XSSFCellStyle.ALIGN_CENTER);
            objects[index++].setDataFormat((short) 49);
            objects[index] = new ExcelExportObject(returnedData.getRptPrintDate(), XSSFCellStyle.ALIGN_CENTER);
            objects[index++].setDataFormat((short) 49);
            objects[index++] = new ExcelExportObject(returnedData.getRecheck(), XSSFCellStyle.ALIGN_CENTER);
            objects[index++] = new ExcelExportObject(returnedData.getAbnormal(), XSSFCellStyle.ALIGN_CENTER);
            objects[index++] = new ExcelExportObject(
                    returnedData.getAbnormalSituation(),
                    XSSFCellStyle.ALIGN_LEFT,
                    XSSFCellStyle.VERTICAL_CENTER,
                    true
            );
            objects[index++] = new ExcelExportObject(returnedData.getStatus(), XSSFCellStyle.ALIGN_CENTER);
            objects[index] = new ExcelExportObject(
                    returnedData.getBackRsn(),
                    XSSFCellStyle.ALIGN_LEFT,
                    XSSFCellStyle.VERTICAL_CENTER,
                    true
            );
            dataList.add(objects);
        }
        return dataList;
    }

    private String[] getReturnedDataExcelHeaders() {
        return new String[]{"用工单位地区", "用工单位名称", "姓名", "体检编号", "在岗状态", "监测类别", "体检日期", "报告出具日期",
                "是否复检", "是否异常", "异常情况", "状态", "退回原因"};
    }

    private String getEscapeStringByObject(int type, Object... o) {
        String str = "";
        BigDecimal bigDecimal1;
        BigDecimal bigDecimal2;
        try {
            switch (type) {
                case 1:
                    str = BigDecimal.ONE.equals(o[0]) ? "常规监测" :
                            BigDecimal.valueOf(2).equals(o[0]) ? "主动监测" : "";
                    break;
                case 2:
                case 3:
                    str = BigDecimal.ONE.equals(o[0]) ? "是" :
                            BigDecimal.ZERO.equals(o[0]) ? "否" : "";
                    break;
                case 4:
                    bigDecimal1 = (BigDecimal) o[0];
                    bigDecimal2 = (BigDecimal) o[1];
                    if (bigDecimal1 == null) {
                        if ("2".equals(this.checkLevel) && bigDecimal2 != null) {
                            if (BigDecimal.ONE.equals(bigDecimal2)) {
                                str = "待终审";
                            } else {
                                str = "待初审";
                            }
                        } else if ("3".equals(this.checkLevel)) {
                            str = "待初审";
                        }
                    } else {
                        if (BigDecimal.ZERO.equals(bigDecimal1)) {
                            str = "初审退回";
                        } else if (BigDecimal.ONE.equals(bigDecimal1)) {
                            str = "待初审";
                        } else if (BigDecimal.valueOf(2).equals(bigDecimal1)) {
                            str = "复审退回";
                        } else if (BigDecimal.valueOf(3).equals(bigDecimal1)) {
                            str = "待复审";
                        } else if (BigDecimal.valueOf(4).equals(bigDecimal1)) {
                            str = "终审退回";
                        } else if (BigDecimal.valueOf(5).equals(bigDecimal1)) {
                            str = "待终审";
                        } else if (BigDecimal.valueOf(6).equals(bigDecimal1)) {
                            str = "终审通过";
                        } else if (BigDecimal.valueOf(7).equals(bigDecimal1)) {
                            str = "国家退回";
                        }
                    }
                    break;
                case 5:
                    bigDecimal1 = (BigDecimal) o[0];
                    if (bigDecimal1 != null) {
                        if (BigDecimal.valueOf(7).equals(bigDecimal1)) {
                            str = StringUtils.objectToString(o[2]);
                        } else if (BigDecimal.ZERO.equals(bigDecimal1) ||
                                BigDecimal.valueOf(2).equals(bigDecimal1) ||
                                BigDecimal.valueOf(4).equals(bigDecimal1)) {
                            str = StringUtils.objectToString(o[1]);
                        }
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return str;
    }

    private String getErrorMsg(List<Object[]> abnomalList,Object lackMsg) {
        StringBuilder buffer = new StringBuilder();
        int num = 1;
        if(!CollectionUtils.isEmpty(abnomalList)){
            for (Object[] objects : abnomalList) {
                buffer.append(num++).append("、").append(objects[2].toString())
                        .append((char)10);
            }
        }
        if(lackMsg!=null&&StringUtils.isNotBlank(lackMsg.toString())){
            buffer.append(num).append("、").append(lackMsg).append((char)10);
        }
        if(buffer.length()>0){
            return buffer.substring(0,buffer.length()-1);
        }
        return buffer.toString();
    }
    
    private Map<Integer, List<AbnormalBadrsnExportVo>> getBadrsnExportVoMap(
            List<AbnormalBadrsnExportVo> badrsnExportVoList, int type) {
        Map<Integer, List<AbnormalBadrsnExportVo>> badrsnExportVoMap = new HashMap<>(16);
        for (AbnormalBadrsnExportVo abnormalBadrsnExportVo : badrsnExportVoList) {
            Integer i;
            if (type == 1) {
                i = abnormalBadrsnExportVo.getBadRsnRid();
            } else if (type == 2) {
                i = abnormalBadrsnExportVo.getNostdFlag();
            } else {
                return new HashMap<>(16);
            }
            if (!badrsnExportVoMap.containsKey(i)) {
                List<AbnormalBadrsnExportVo> badrsnExportVo = new ArrayList<>();
                badrsnExportVoMap.put(i, badrsnExportVo);
            }
            badrsnExportVoMap.get(i).add(abnormalBadrsnExportVo);
        }
        return badrsnExportVoMap;
    }

    public List<AbnormalBadrsnExportVo> findAbnormalBadrsnExportListByBhkRidList(List<Integer> bhkRidList) {
        if (CollectionUtils.isEmpty(bhkRidList)) {
            return new ArrayList<>();
        }
        List<AbnormalBadrsnExportVo> resultList = new ArrayList<>(bhkRidList.size());
        List<Integer> tmpRidList = new ArrayList<>(1000);
        for (Integer rid : bhkRidList) {
            tmpRidList.add(rid);
            if (tmpRidList.size() >= 1000) {
                List<AbnormalBadrsnExportVo> tmpResultList = findAbnormalBadrsnExportVoListByBhkRidList(tmpRidList);
                if (!CollectionUtils.isEmpty(tmpResultList)) {
                    resultList.addAll(tmpResultList);
                }
                tmpRidList.clear();
            }
        }
        if (!CollectionUtils.isEmpty(tmpRidList)) {
            List<AbnormalBadrsnExportVo> tmpResultList = findAbnormalBadrsnExportVoListByBhkRidList(tmpRidList);
            if (!CollectionUtils.isEmpty(tmpResultList)) {
                resultList.addAll(tmpResultList);
            }
        }
        return resultList;
    }


    /**
     * <p>方法描述：批量查询</p>
     * @MethodAuthor： yzz
     * @Date：2022-10-20
     **/
    public Map<Integer,List<Object[]>> findBhkAbnomalsExportListByBhkRidList(List<Integer> bhkRidList){
        if (CollectionUtils.isEmpty(bhkRidList)) {
            return new HashMap<>();
        }
        Map<Integer,List<Object[]>> resultMap = new HashMap<>();
        List<Integer> tmpRidList = new ArrayList<>(1000);
        for (Integer rid : bhkRidList) {
            tmpRidList.add(rid);
            if (tmpRidList.size() >= 1000) {
                Map<Integer,List<Object[]>>  resultTempMap = hethBaseCommService.findBhkAbnomalsByBhkRids(tmpRidList);
                if (!resultTempMap.isEmpty()) {
                    resultMap.putAll(resultTempMap);
                }
                tmpRidList.clear();
            }
        }
        if (!CollectionUtils.isEmpty(tmpRidList)) {
            Map<Integer,List<Object[]>>  resultTempMap = hethBaseCommService.findBhkAbnomalsByBhkRids(tmpRidList);
            if (!resultTempMap.isEmpty()) {
                resultMap.putAll(resultTempMap);
            }
        }
        return resultMap;
    }

    private List<AbnormalBadrsnExportVo> findAbnormalBadrsnExportVoListByBhkRidList(List<Integer> list) {
        String sql = "SELECT T.BHK_ID, " +
                "     	T.NOSTD_FLAG, " +
                "     	T3.RID AS BADRSNRID, " +
                "     	T3.CODE_NAME AS BADRSNNAME, " +
                "     	T4.ITEM_NAME AS ITEMNAME, " +
                "     	T2.ITEM_DESC AS ITMDESC, " +
                "     	T2.DETER_WAY " +
                "     FROM TD_TJ_BHK_ITEM_STD T " +
                "       INNER JOIN TD_ZW_BADRSN_ITEM T1 ON T.ITEM_STD_ID = T1.RID " +
                "       INNER JOIN TD_ZW_BADRSN_STD T2 ON T1.MAIN_ID = T2.RID " +
                "       INNER JOIN TS_SIMPLE_CODE T3 ON T3.RID = T2.BADRSN_ID " +
                "       INNER JOIN TB_TJ_ITEMS T4 ON T1.ITEM_ID = T4.RID " +
                "     WHERE 1=1 AND T.BHK_ID IN (:list) ";
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("list", list);
        List<Object[]> dataList = CollectionUtil.castList(Object[].class, this.commService.findDataBySqlNoPage(sql, paramMap));
        List<AbnormalBadrsnExportVo> abnormalBadrsnExportVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(dataList)) {
            return abnormalBadrsnExportVoList;
        }
        for (Object[] objects : dataList) {
            try {
                AbnormalBadrsnExportVo abnormalBadrsnExportVo = new AbnormalBadrsnExportVo();
                abnormalBadrsnExportVo.setBhkRid(((BigDecimal) objects[0]).intValue());
                abnormalBadrsnExportVo.setNostdFlag(((BigDecimal) objects[1]).intValue());
                abnormalBadrsnExportVo.setBadRsnRid(((BigDecimal) objects[2]).intValue());
                abnormalBadrsnExportVo.setBadRsnName(StringUtils.objectToString(objects[3]));
                abnormalBadrsnExportVo.setItemName(StringUtils.objectToString(objects[4]));
                abnormalBadrsnExportVo.setItmDesc(StringUtils.objectToString(objects[5]));
                abnormalBadrsnExportVo.setDeterWay(((BigDecimal) objects[6]).intValue());
                abnormalBadrsnExportVoList.add(abnormalBadrsnExportVo);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return abnormalBadrsnExportVoList;
    }

    private boolean judgeStatesSelect() {
        boolean statesSelect = false;
        boolean returnedStatesSelect = false;
        this.returnedStates = new ArrayList<>();
        Set<String> returnedStatesSet = new HashSet<>();
        returnedStatesSet.add("0");
        returnedStatesSet.add("2");
        returnedStatesSet.add("4");
        returnedStatesSet.add("7");
        for (String state : this.states) {
            if (StringUtils.isNotBlank(state)) {
                statesSelect = true;
            }
            if (returnedStatesSet.contains(state)) {
                this.returnedStates.add(state);
                returnedStatesSelect = true;
            }
        }
        if (this.states.length == 0 || !statesSelect) {
            returnedStatesSelect = true;
            this.returnedStates.add("0");
            this.returnedStates.add("2");
            this.returnedStates.add("4");
            this.returnedStates.add("7");
        }
        return returnedStatesSelect;
    }

    private int getTheNumberOfReturnedData(String countSql) {
        try {
            return this.commService.findTotalNumBySQL(countSql, this.paramMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    @Override
    public void processData(List<?> list) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<Object[]> resultList = (List<Object[]>) list;
        List<Integer> bhkRidList = new ArrayList<>();
        for (Object[] objects : resultList) {
            objects[4] = StringUtils.encryptIdc(StringUtils.objectToString(objects[4]));
            Integer bhkRid = null == objects[0] ? null : Integer.parseInt(objects[0].toString());
            if (null != bhkRid) {
                bhkRidList.add(bhkRid);
            }
        }
        //异常信息
        Map<Integer,List<String>> abnomalInfoMap = this.hethBaseCommService.findAbnormalInfoByBhkRids(bhkRidList);
        if (CollectionUtils.isEmpty(abnomalInfoMap)) {
            return;
        }
        for (Object[] objects : resultList) {
            Integer bhkRid = null == objects[0] ? null : Integer.parseInt(objects[0].toString());
            if (null == bhkRid) {
                continue;
            }
            String abnomalInfoTip = this.generateAbnomalInfo(abnomalInfoMap.get(bhkRid));
            objects[18] = StringUtils.isBlank(abnomalInfoTip) ? "" : abnomalInfoTip.replace("<br/>","；");
            objects[19] = abnomalInfoTip;
        }
    }

    /**
     * <p>方法描述：拼接异常信息 </p>
     * pw 2023/12/20
     **/
    private String generateAbnomalInfo(List<String> abnormalInfoList) {
        if (CollectionUtils.isEmpty(abnormalInfoList)) {
            return "";
        }
        StringBuffer buffer = new StringBuffer();
        int xh = 1;
        for (String abnomalInfo : abnormalInfoList) {
            buffer.append("<br/>").append(xh++).append("、").append(abnomalInfo);
        }
        return buffer.substring("<br/>".length());
    }


    /**
     * @Description: 情况说明弹出框
     * @MethodAuthor pw, 2022年01月26日
     */
    public void preInfoTipDiagShow() {
        if (CollectionUtils.isEmpty(this.selectEntitys)) {
            JsfUtil.addErrorMessage("请选择需要情况说明的数据！");
            return;
        }
        this.infoTipMsg = null;
        RequestContext.getCurrentInstance().execute("PF('InfoTipDialog').show()");
        RequestContext.getCurrentInstance().update("tabView:mainForm:infoTipDialog");
    }


    /**
     * @Description: 情况说明确认
     * @MethodAuthor pw, 2022年01月26日
     */
    public void sureCommitInfoTipMsg() {
        if (CollectionUtils.isEmpty(this.selectEntitys)) {
            return;
        }
        if (StringUtils.isBlank(this.infoTipMsg)) {
            JsfUtil.addErrorMessage("情况说明不允许为空！");
            return;
        }
        if (this.infoTipMsg.length() > 200) {
            JsfUtil.addErrorMessage("情况说明字数太多！");
            return;
        }
        List<Integer> bhkRidList = new ArrayList<>();
        //二级审核的省直属的rid集合
        List<Integer> bhkRidProvList = new ArrayList<>();
        for (Object[] objArr : this.selectEntitys) {
            Integer bhkRid = null == objArr[0] ? null : Integer.parseInt(objArr[0].toString());
            boolean ifProvFlag = "2".equals(this.checkLevel) && null != objArr[14] &&
                    "1".equals(objArr[14].toString().trim());
            if (null != bhkRid) {
                if (ifProvFlag) {
                    bhkRidProvList.add(bhkRid);
                } else {
                    bhkRidList.add(bhkRid);
                }
            }
        }
        Map<Integer, List<Integer>> stateWithRidListMap = new HashMap<>(2);
        if (!CollectionUtils.isEmpty(bhkRidProvList)) {
            stateWithRidListMap.put(5, bhkRidProvList);
        }
        if (!CollectionUtils.isEmpty(bhkRidList)) {
            stateWithRidListMap.put(1, bhkRidList);
        }
        try {
            this.hethBaseCommService.updateInfoMsg(stateWithRidListMap, this.infoTipMsg);
            this.searchAction();
            RequestContext.getCurrentInstance().execute("PF('InfoTipDialog').hide();datatableOffClick();");
            RequestContext.getCurrentInstance().update("tabView");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("情况说明添加失败！");
        }
    }
    /**
     *  <p>方法描述：跳转到导出文件下载模块</p>
     * @MethodAuthor hsj 2025-03-05 14:04
     */
    public void fileDownloadAction(){
        RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','导出文件下载','/webapp/system/tdTjExportList.faces','');");
    }
    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdTjBhk getTdTjBhk() {
        return tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    public String getCheckLevel() {
        return checkLevel;
    }

    public void setCheckLevel(String checkLevel) {
        this.checkLevel = checkLevel;
    }

    public int getShowOption() {
        return showOption;
    }

    public void setShowOption(int showOption) {
        this.showOption = showOption;
    }

    public List<String> getUnAbnormalsList() {
        return unAbnormalsList;
    }

    public void setUnAbnormalsList(List<String> unAbnormalsList) {
        this.unAbnormalsList = unAbnormalsList;
    }

    public List<TsZone> getEntrustCrptZoneList() {
        return entrustCrptZoneList;
    }

    public void setEntrustCrptZoneList(List<TsZone> entrustCrptZoneList) {
        this.entrustCrptZoneList = entrustCrptZoneList;
    }

    public String getSearchEntrustCrptZoneCode() {
        return searchEntrustCrptZoneCode;
    }

    public void setSearchEntrustCrptZoneCode(String searchEntrustCrptZoneCode) {
        this.searchEntrustCrptZoneCode = searchEntrustCrptZoneCode;
    }

    public String getSearchEntrustCrptZoneName() {
        return searchEntrustCrptZoneName;
    }

    public void setSearchEntrustCrptZoneName(String searchEntrustCrptZoneName) {
        this.searchEntrustCrptZoneName = searchEntrustCrptZoneName;
    }

    public String getSearchEntrustCrptName() {
        return searchEntrustCrptName;
    }

    public void setSearchEntrustCrptName(String searchEntrustCrptName) {
        this.searchEntrustCrptName = searchEntrustCrptName;
    }

    public String getSearchEntrustCreditCode() {
        return searchEntrustCreditCode;
    }

    public void setSearchEntrustCreditCode(String searchEntrustCreditCode) {
        this.searchEntrustCreditCode = searchEntrustCreditCode;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchCreditCode() {
        return searchCreditCode;
    }

    public void setSearchCreditCode(String searchCreditCode) {
        this.searchCreditCode = searchCreditCode;
    }

    public String getSearchPersonName() {
        return searchPersonName;
    }

    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public Date getSearchBhkBdate() {
        return searchBhkBdate;
    }

    public void setSearchBhkBdate(Date searchBhkBdate) {
        this.searchBhkBdate = searchBhkBdate;
    }

    public Date getSearchBhkEdate() {
        return searchBhkEdate;
    }

    public void setSearchBhkEdate(Date searchBhkEdate) {
        this.searchBhkEdate = searchBhkEdate;
    }

    public List<TsSimpleCode> getOnGuardList() {
        return onGuardList;
    }

    public void setOnGuardList(List<TsSimpleCode> onGuardList) {
        this.onGuardList = onGuardList;
    }

    public String getSelectOnGuardNames() {
        return selectOnGuardNames;
    }

    public void setSelectOnGuardNames(String selectOnGuardNames) {
        this.selectOnGuardNames = selectOnGuardNames;
    }

    public String getSelectOnGuardIds() {
        return selectOnGuardIds;
    }

    public void setSelectOnGuardIds(String selectOnGuardIds) {
        this.selectOnGuardIds = selectOnGuardIds;
    }

    public List<TsSimpleCode> getBadRsnList() {
        return badRsnList;
    }

    public void setBadRsnList(List<TsSimpleCode> badRsnList) {
        this.badRsnList = badRsnList;
    }

    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public String getSelectBadRsnIds() {
        return selectBadRsnIds;
    }

    public void setSelectBadRsnIds(String selectBadRsnIds) {
        this.selectBadRsnIds = selectBadRsnIds;
    }

    public Date getStartRptPrintDate() {
        return startRptPrintDate;
    }

    public void setStartRptPrintDate(Date startRptPrintDate) {
        this.startRptPrintDate = startRptPrintDate;
    }

    public Date getEndRptPrintDate() {
        return endRptPrintDate;
    }

    public void setEndRptPrintDate(Date endRptPrintDate) {
        this.endRptPrintDate = endRptPrintDate;
    }

    public String[] getJcTypes() {
        return jcTypes;
    }

    public void setJcTypes(String[] jcTypes) {
        this.jcTypes = jcTypes;
    }

    public String[] getIfRhks() {
        return ifRhks;
    }

    public void setIfRhks(String[] ifRhks) {
        this.ifRhks = ifRhks;
    }

    public String[] getIfAbnormals() {
        return ifAbnormals;
    }

    public void setIfAbnormals(String[] ifAbnormals) {
        this.ifAbnormals = ifAbnormals;
    }

    public String[] getStates() {
        return states;
    }

    public void setStates(String[] states) {
        this.states = states;
    }

    public List<SelectItem> getStateList() {
        return stateList;
    }

    public void setStateList(List<SelectItem> stateList) {
        this.stateList = stateList;
    }

    public String getSearchBhkCode() {
        return searchBhkCode;
    }

    public void setSearchBhkCode(String searchBhkCode) {
        this.searchBhkCode = searchBhkCode;
    }

    public TdTjBhkInfoBean getTjBhkInfoBean() {
        return tjBhkInfoBean;
    }

    public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
        this.tjBhkInfoBean = tjBhkInfoBean;
    }

    public OutputPanel getArchivePanel() {
        return archivePanel;
    }

    public void setArchivePanel(OutputPanel archivePanel) {
        this.archivePanel = archivePanel;
    }

    public String getIfCountryUpload() {
        return ifCountryUpload;
    }

    public void setIfCountryUpload(String ifCountryUpload) {
        this.ifCountryUpload = ifCountryUpload;
    }

    public String getAuditAdv() {
        return auditAdv;
    }

    public void setAuditAdv(String auditAdv) {
        this.auditAdv = auditAdv;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public Date getSmtDate() {
        return smtDate;
    }

    public void setSmtDate(Date smtDate) {
        this.smtDate = smtDate;
    }

    public boolean isIfReturnPart() {
        return ifReturnPart;
    }

    public void setIfReturnPart(boolean ifReturnPart) {
        this.ifReturnPart = ifReturnPart;
    }

    public List<Object[]> getSelectEntitys() {
        return selectEntitys;
    }

    public void setSelectEntitys(List<Object[]> selectEntitys) {
        this.selectEntitys = selectEntitys;
    }

    public String getInfoTipMsg() {
        return infoTipMsg;
    }

    public void setInfoTipMsg(String infoTipMsg) {
        this.infoTipMsg = infoTipMsg;
    }

    public String getSearchAbnomalInfo() {
        return searchAbnomalInfo;
    }

    public void setSearchAbnomalInfo(String searchAbnomalInfo) {
        this.searchAbnomalInfo = searchAbnomalInfo;
    }

    public String getSearchBackUpRsn() {
        return searchBackUpRsn;
    }

    public void setSearchBackUpRsn(String searchBackUpRsn) {
        this.searchBackUpRsn = searchBackUpRsn;
    }
}
