package com.chis.modules.heth.comm.entity;

import java.math.BigDecimal;
import java.util.*;

import javax.persistence.*;

import com.chis.modules.system.encrypt.annotation.EncryptField;
import com.chis.modules.system.encrypt.annotation.EncryptTable;
import com.chis.modules.system.entity.TsSimpleCode;

/**
 * @Description : 体检主表（数据录入）
 * @ClassAuthor: anjing
 * @Date : 2019/5/14 9:50
 **/
@Entity
@Table(name = "TD_TJ_BHK_CLT", uniqueConstraints = @UniqueConstraint(columnNames = {"BHK_CODE", "BHKORG_ID" }))
@SequenceGenerator(name = "TdTjBhkClt_Seq", sequenceName = "TD_TJ_BHK_CLT_SEQ", allocationSize = 1)
@EncryptTable
public class TdTjBhkClt implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**主键Id*/
    private Integer rid;
    /**UUID*/
    private String uuid;
    /**体检编号*/
    private String bhkCode;
    /**关联：资质服务机构注册信息（体检机构Id）*/
    private TbTjSrvorg fkByBhkorgId;
    /**关联：企业信息（企业Id）*/
    private TbTjCrpt fkByCrptId;
    /**企业名称*/
    private String crptName;
    /**关联：人员信息（人员Id）*/
    private TdTjPerson fkByPersonId;
    /**人员姓名*/
    private String personName;
    /**性别*/
    private String sex;
    /**身份证号*/
    @EncryptField
    private String idc;
    /**出生日期*/
    private Date brth;
    /**年龄*/
    private Integer age;
    /**婚否*/
    private String isxmrd;
    /**联系电话*/
    @EncryptField
    private String lnktel;
    /**部门*/
    private String dpt;
    /**工号*/
    private String wrknum;
    /**总工龄年数*/
    private BigDecimal wrklnt;
    /**总工龄月数*/
    private Integer wrklntmonth;
    /**接害工龄*/
    private BigDecimal tchbadrsntim;
    /**接害工龄月数*/
    private Integer tchbadrsnmonth;
    /**工种*/
    private String workName;
    /**体检类型：1 从业体检 2 职业普通体检 3 职业劳动体检 4 放射卫生体检 5 学校卫生体检 6 福利性体检*/
    private Integer bhkType;
    /**关联：码表（在岗状态Id）*/
    private TsSimpleCode fkByOnguardStateid;
    /**体检日期*/
    private Date bhkDate;
    /**体检结果*/
    private String bhkrst;
    /**主检建议*/
    private String mhkadv;
    /**关联：码表（主检医师Id）*/
    private TdZwPsninfoComm fkByMhkdctId;
    /**判定日期*/
    private Date jdgdat;
    /**是否为复检*/
    private Integer ifRhk;
    /**人员类型：1 正常 2 外籍人员 3 无身份证人员*/
    private Integer psnType;
    /**关联：体检主表-数据录入（上次体检记录Id）*/
    private TdTjBhkClt fkByLastBhkId;
    /**创建日期*/
    private Date createDate;
    /**创建人*/
    private Integer createManid;
    /**修改日期*/
    private Date modifyDate;
    /**修改人*/
    private Integer modifyManid;
    /**关联：人员信息（问诊检查医生Id）*/
    private TdZwPsninfoComm fkByWzChkdoctId;
    /**问诊检查日期*/
    private Date wzChkdat;
    /**体检录入状态：1 基本信息 2 体检问诊 3 检查结果 4 检查结论 5 完成*/
    private Integer bhkRcdState;
    /**标删：0 否 1 是*/
    private Integer delMark;
    /**数据状态：0 允许 1 不允许*/
    private Integer ifNotMod;
    //报告打印日期
    private Date rptPrintDate;

    private TsSimpleCode  fkByWorkTypeId;

    private String workOther;

    private Integer jcType;

    private  List<TsSimpleCode> badList =  new ArrayList<TsSimpleCode>(0);
    
    private String otherBadRsn;
    /**+首次提交日期20220119*/
    private Date firstSubmitDate;
    /**+首次数据报告是否及时20220122*/
    private Integer ifReportIntime;

    /**接触其他危害因素*/
    private String tchOtherBadrsn;

    /**最新提交日期*/
    private Date submitChangedate;

    public TdTjBhkClt() {
    }

    public TdTjBhkClt(Integer rid) {
        this.rid = rid;
    }

    /**体检危害因素（数据录入）*/
    private List<TdTjBadrsnsClt> tdTjBadrsnsClts = new ArrayList<TdTjBadrsnsClt>(0);
    /**接触危害因素（数据录入）*/
    private List<TdTjTchBadrsnsClt> tdTjTchBadrsnsClts = new ArrayList<TdTjTchBadrsnsClt>(0);
    /**问诊(非)放射职业史信息表（数据录入）*/
    private List<TdTjEmhistoryClt> tdTjEmhistoryClts = new ArrayList<TdTjEmhistoryClt>(0);
    /**问诊既往病史表（数据录入）*/
    private List<TdTjAnamnesisClt> tdTjAnamnesisClts= new ArrayList<TdTjAnamnesisClt>(0);
    /**问诊项目（数据录入）*/
    private List<TdTjExmsdataClt> tdTjExmsdataClts = new ArrayList<TdTjExmsdataClt>(0);
    /**问诊症状信息（数据录入）*/
    private List<TdTjSymptomClt> tdTjSymptomClts = new ArrayList<TdTjSymptomClt>(0);
    /**体检子表（数据录入）*/
    private List<TdTjBhksubClt> tdTjBhksubClts = new ArrayList<TdTjBhksubClt>(0);
    /**主检主题结论（数据录入）*/
    private List<TdTjMhkrstClt> tdTjMhkrstClts = new ArrayList<TdTjMhkrstClt>(0);
    /**疑似职业病人群（数据录入）*/
    private List<TdTjSupoccdiseClt> tdTjSupoccdiseClts = new ArrayList<TdTjSupoccdiseClt>(0);
    /**职业禁忌证人群（数据录入）*/
    private List<TdTjContraindClt> tdTjContraindClts = new ArrayList<TdTjContraindClt>(0);
    /**+主检医师（数据录入）20210322*/
    private List<TdTjChiefDoctor> tdTjChiefDoctorList = new ArrayList<TdTjChiefDoctor>(0);

    /************** 非实体字段 ***************/
    /**体检危害因素字符串*/
    private String badrsns;
    /**接触危害因素字符串*/
    private String touchBadrsns;
    private TsSimpleCode fkByCardTypeId;
    /**用工单位*/
    private TbTjCrpt fkByEmpCrptId;

    private TsSimpleCode  fkByProtectEquId;

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjBhkClt_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "UUID", length = 50)
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Column(name = "BHK_CODE" , length = 50)
    public String getBhkCode() {
        return bhkCode;
    }

    public void setBhkCode(String bhkCode) {
        this.bhkCode = bhkCode;
    }

    @ManyToOne
    @JoinColumn(name = "BHKORG_ID")
    public TbTjSrvorg getFkByBhkorgId() {
        return fkByBhkorgId;
    }

    public void setFkByBhkorgId(TbTjSrvorg fkByBhkorgId) {
        this.fkByBhkorgId = fkByBhkorgId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getFkByCrptId() {
        return fkByCrptId;
    }

    public void setFkByCrptId(TbTjCrpt fkByCrptId) {
        this.fkByCrptId = fkByCrptId;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @ManyToOne
    @JoinColumn(name = "PERSON_ID")
    public TdTjPerson getFkByPersonId() {
        return fkByPersonId;
    }

    public void setFkByPersonId(TdTjPerson fkByPersonId) {
        this.fkByPersonId = fkByPersonId;
    }

    @Column(name = "PERSON_NAME" , length = 200)
    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    @Column(name = "SEX" , length = 10)
    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @Column(name = "IDC", length = 50)
    public String getIdc() {
        return idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BRTH", length = 7)
    public Date getBrth() {
        return brth;
    }

    public void setBrth(Date brth) {
        this.brth = brth;
    }

    @Column(name = "AGE")
    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Column(name = "ISXMRD", length = 10)
    public String getIsxmrd() {
        return isxmrd;
    }

    public void setIsxmrd(String isxmrd) {
        this.isxmrd = isxmrd;
    }

    @Column(name = "LNKTEL", length = 50)
    public String getLnktel() {
        return lnktel;
    }

    public void setLnktel(String lnktel) {
        this.lnktel = lnktel;
    }

    @Column(name = "DPT", length = 50)
    public String getDpt() {
        return dpt;
    }

    public void setDpt(String dpt) {
        this.dpt = dpt;
    }

    @Column(name = "WRKNUM", length = 50)
    public String getWrknum() {
        return wrknum;
    }

    public void setWrknum(String wrknum) {
        this.wrknum = wrknum;
    }

    @Column(name = "WRKLNT")
    public BigDecimal getWrklnt() {
        return wrklnt;
    }

    public void setWrklnt(BigDecimal wrklnt) {
        this.wrklnt = wrklnt;
    }

    @Column(name = "WRKLNTMONTH")
    public Integer getWrklntmonth() {
        return wrklntmonth;
    }

    public void setWrklntmonth(Integer wrklntmonth) {
        this.wrklntmonth = wrklntmonth;
    }

    @Column(name = "TCHBADRSNTIM")
    public BigDecimal getTchbadrsntim() {
        return tchbadrsntim;
    }

    public void setTchbadrsntim(BigDecimal tchbadrsntim) {
        this.tchbadrsntim = tchbadrsntim;
    }

    @Column(name = "TCHBADRSNMONTH")
    public Integer getTchbadrsnmonth() {
        return tchbadrsnmonth;
    }

    public void setTchbadrsnmonth(Integer tchbadrsnmonth) {
        this.tchbadrsnmonth = tchbadrsnmonth;
    }

    @Column(name = "WORK_NAME", length = 100)
    public String getWorkName() {
        return workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }

    @Column(name = "BHK_TYPE" )
    public Integer getBhkType() {
        return bhkType;
    }

    public void setBhkType(Integer bhkType) {
        this.bhkType = bhkType;
    }

    @ManyToOne
    @JoinColumn(name = "ONGUARD_STATEID")
    public TsSimpleCode getFkByOnguardStateid() {
        return fkByOnguardStateid;
    }

    public void setFkByOnguardStateid(TsSimpleCode fkByOnguardStateid) {
        this.fkByOnguardStateid = fkByOnguardStateid;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BHK_DATE",  length = 7)
    public Date getBhkDate() {
        return bhkDate;
    }

    public void setBhkDate(Date bhkDate) {
        this.bhkDate = bhkDate;
    }

    @Lob
    @Column(name = "BHKRST" )
    public String getBhkrst() {
        return bhkrst;
    }

    public void setBhkrst(String bhkrst) {
        this.bhkrst = bhkrst;
    }

    @Lob
    @Column(name = "MHKADV")
    public String getMhkadv() {
        return mhkadv;
    }

    public void setMhkadv(String mhkadv) {
        this.mhkadv = mhkadv;
    }

    @ManyToOne
    @JoinColumn(name = "MHKDCT_ID")
    public TdZwPsninfoComm getFkByMhkdctId() {
        return fkByMhkdctId;
    }

    public void setFkByMhkdctId(TdZwPsninfoComm fkByMhkdctId) {
        this.fkByMhkdctId = fkByMhkdctId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "JDGDAT", length = 7)
    public Date getJdgdat() {
        return jdgdat;
    }

    public void setJdgdat(Date jdgdat) {
        this.jdgdat = jdgdat;
    }

    @Column(name = "IF_RHK")
    public Integer getIfRhk() {
        return ifRhk;
    }

    public void setIfRhk(Integer ifRhk) {
        this.ifRhk = ifRhk;
    }

    @Column(name = "PSN_TYPE")
    public Integer getPsnType() {
        return psnType;
    }

    public void setPsnType(Integer psnType) {
        this.psnType = psnType;
    }

    @ManyToOne
    @JoinColumn(name = "LAST_BHK_ID")
    public TdTjBhkClt getFkByLastBhkId() {
        return fkByLastBhkId;
    }

    public void setFkByLastBhkId(TdTjBhkClt fkByLastBhkId) {
        this.fkByLastBhkId = fkByLastBhkId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @ManyToOne
    @JoinColumn(name = "WZ_CHKDOCT_ID")
    public TdZwPsninfoComm getFkByWzChkdoctId() {
        return fkByWzChkdoctId;
    }

    public void setFkByWzChkdoctId(TdZwPsninfoComm fkByWzChkdoctId) {
        this.fkByWzChkdoctId = fkByWzChkdoctId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "WZ_CHKDAT" , length = 7)
    public Date getWzChkdat() {
        return wzChkdat;
    }

    public void setWzChkdat(Date wzChkdat) {
        this.wzChkdat = wzChkdat;
    }

    @Column(name = "BHK_RCD_STATE")
    public Integer getBhkRcdState() {
        return bhkRcdState;
    }

    public void setBhkRcdState(Integer bhkRcdState) {
        this.bhkRcdState = bhkRcdState;
    }

    @Column(name = "DEL_MARK")
    public Integer getDelMark() {
        return delMark;
    }

    public void setDelMark(Integer delMark) {
        this.delMark = delMark;
    }

    @Column(name = "IF_NOT_MOD")
    public Integer getIfNotMod() {
        return ifNotMod;
    }

    public void setIfNotMod(Integer ifNotMod) {
        this.ifNotMod = ifNotMod;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjBadrsnsClt> getTdTjBadrsnsClts() {
        return tdTjBadrsnsClts;
    }

    public void setTdTjBadrsnsClts(List<TdTjBadrsnsClt> tdTjBadrsnsClts) {
        this.tdTjBadrsnsClts = tdTjBadrsnsClts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjEmhistoryClt> getTdTjEmhistoryClts() {
        return tdTjEmhistoryClts;
    }

    public void setTdTjEmhistoryClts(List<TdTjEmhistoryClt> tdTjEmhistoryClts) {
        this.tdTjEmhistoryClts = tdTjEmhistoryClts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjAnamnesisClt> getTdTjAnamnesisClts() {
        return tdTjAnamnesisClts;
    }

    public void setTdTjAnamnesisClts(List<TdTjAnamnesisClt> tdTjAnamnesisClts) {
        this.tdTjAnamnesisClts = tdTjAnamnesisClts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjExmsdataClt> getTdTjExmsdataClts() {
        return tdTjExmsdataClts;
    }

    public void setTdTjExmsdataClts(List<TdTjExmsdataClt> tdTjExmsdataClts) {
        this.tdTjExmsdataClts = tdTjExmsdataClts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjSymptomClt> getTdTjSymptomClts() {
        return tdTjSymptomClts;
    }

    public void setTdTjSymptomClts(List<TdTjSymptomClt> tdTjSymptomClts) {
        this.tdTjSymptomClts = tdTjSymptomClts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjBhksubClt> getTdTjBhksubClts() {
        return tdTjBhksubClts;
    }

    public void setTdTjBhksubClts(List<TdTjBhksubClt> tdTjBhksubClts) {
        this.tdTjBhksubClts = tdTjBhksubClts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId" ,orphanRemoval=true)
    public List<TdTjMhkrstClt> getTdTjMhkrstClts() {
        return tdTjMhkrstClts;
    }

    public void setTdTjMhkrstClts(List<TdTjMhkrstClt> tdTjMhkrstClts) {
        this.tdTjMhkrstClts = tdTjMhkrstClts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId" ,orphanRemoval=true)
    public List<TdTjSupoccdiseClt> getTdTjSupoccdiseClts() {
        return tdTjSupoccdiseClts;
    }

    public void setTdTjSupoccdiseClts(List<TdTjSupoccdiseClt> tdTjSupoccdiseClts) {
        this.tdTjSupoccdiseClts = tdTjSupoccdiseClts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjContraindClt> getTdTjContraindClts() {
        return tdTjContraindClts;
    }

    public void setTdTjContraindClts(List<TdTjContraindClt> tdTjContraindClts) {
        this.tdTjContraindClts = tdTjContraindClts;
    }

    @Transient
    public String getBadrsns() {
        return badrsns;
    }

    public void setBadrsns(String badrsns) {
        this.badrsns = badrsns;
    }

    @Column(name = "RPT_PRINT_DATE")
    public Date getRptPrintDate() {
        return rptPrintDate;
    }

    public void setRptPrintDate(Date rptPrintDate) {
        this.rptPrintDate = rptPrintDate;
    }

    @ManyToOne
    @JoinColumn(name = "WORK_TYPE_ID" )
    public TsSimpleCode getFkByWorkTypeId() {
        return fkByWorkTypeId;
    }

    public void setFkByWorkTypeId(TsSimpleCode fkByWorkTypeId) {
        this.fkByWorkTypeId = fkByWorkTypeId;
    }

    @Column(name = "WORK_OTHER")
    public String getWorkOther() {
        return workOther;
    }

    public void setWorkOther(String workOther) {
        this.workOther = workOther;
    }

    @Transient
    public List<TsSimpleCode> getBadList() {
        return badList;
    }

    public void setBadList(List<TsSimpleCode> badList) {
        this.badList = badList;
    }

    @ManyToOne
    @JoinColumn(name = "CARD_TYPE_ID" )
    public TsSimpleCode getFkByCardTypeId() {
        return fkByCardTypeId;
    }

    public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
        this.fkByCardTypeId = fkByCardTypeId;
    }

    @Column(name = "JC_TYPE")
    public Integer getJcType() {
        return jcType;
    }

    public void setJcType(Integer jcType) {
        this.jcType = jcType;
    }
    @Column(name = "OTHER_BADRSN")
	public String getOtherBadRsn() {
		return otherBadRsn;
	}

	public void setOtherBadRsn(String otherBadRsn) {
		this.otherBadRsn = otherBadRsn;
	}

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjChiefDoctor> getTdTjChiefDoctorList() {
        return tdTjChiefDoctorList;
    }

    public void setTdTjChiefDoctorList(List<TdTjChiefDoctor> tdTjChiefDoctorList) {
        this.tdTjChiefDoctorList = tdTjChiefDoctorList;
    }
    @ManyToOne
    @JoinColumn(name = "EMP_CRPT_ID")
	public TbTjCrpt getFkByEmpCrptId() {
		return fkByEmpCrptId;
	}

	public void setFkByEmpCrptId(TbTjCrpt fkByEmpCrptId) {
		this.fkByEmpCrptId = fkByEmpCrptId;
	}
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "FIRST_SUBMIT_DATE" , length = 7)
    public Date getFirstSubmitDate() {
        return firstSubmitDate;
    }

    public void setFirstSubmitDate(Date firstSubmitDate) {
        this.firstSubmitDate = firstSubmitDate;
    }
    @Column(name = "IF_REPORT_INTIME")
    public Integer getIfReportIntime() {
        return ifReportIntime;
    }

    public void setIfReportIntime(Integer ifReportIntime) {
        this.ifReportIntime = ifReportIntime;
    }

    @Column(name = "TCH_OTHER_BADRSN")
    public String getTchOtherBadrsn() {
        return tchOtherBadrsn;
    }

    public void setTchOtherBadrsn(String tchOtherBadrsn) {
        this.tchOtherBadrsn = tchOtherBadrsn;
    }
    @Transient
    public String getTouchBadrsns() {
        return touchBadrsns;
    }

    public void setTouchBadrsns(String touchBadrsns) {
        this.touchBadrsns = touchBadrsns;
    }
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId",orphanRemoval=true)
    public List<TdTjTchBadrsnsClt> getTdTjTchBadrsnsClts() {
        return tdTjTchBadrsnsClts;
    }

    public void setTdTjTchBadrsnsClts(List<TdTjTchBadrsnsClt> tdTjTchBadrsnsClts) {
        this.tdTjTchBadrsnsClts = tdTjTchBadrsnsClts;
    }

    @ManyToOne
    @JoinColumn(name = "PROTECT_EQU_ID" )
    public TsSimpleCode getFkByProtectEquId() {
        return fkByProtectEquId;
    }
    public void setFkByProtectEquId(TsSimpleCode fkByProtectEquId) {
        this.fkByProtectEquId = fkByProtectEquId;
    }

    @Column(name = "SUBMIT_CHANGEDATE")
    public Date getSubmitChangedate() {
        return submitChangedate;
    }
    public void setSubmitChangedate(Date submitChangedate) {
        this.submitChangedate = submitChangedate;
    }
}
