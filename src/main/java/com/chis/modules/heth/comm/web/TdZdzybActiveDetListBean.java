package com.chis.modules.heth.comm.web;

import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TdZdzybAnalyDetailComm;
import com.chis.modules.heth.comm.entity.TdZdzybAnalyDetailSubComm;
import com.chis.modules.heth.comm.entity.TdZdzybAnalyTypeComm;
import com.chis.modules.heth.comm.entity.TdZdzybDetailSubRelComm;
import com.chis.modules.heth.comm.service.TdZdzybActiveDetServiceCommImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 主动监测统计类别维护 托管bean
 */
@ManagedBean(name = "tdZdzybActiveDetListBean")
@ViewScoped
public class TdZdzybActiveDetListBean extends FacesEditBean implements IProcessData {

    private TdZdzybActiveDetServiceCommImpl activeDetService = SpringContextHolder.getBean(TdZdzybActiveDetServiceCommImpl.class);
    /**
     * 重点职业病统计维度表
     */
    private TdZdzybAnalyTypeComm analyType;

    /**
     * 记录rid
     */
    private Integer rid;

    /**
     * 重点职业病显示列
     */
    private List<Object[]> showAllItemsList;

    /**
     * 编辑页面统计类别明细
     **/
    private TdZdzybAnalyDetailComm tdZdzybAnalyDetail;

    /**
     * 重点职业病统计维度明细 表rid
     */
    private Integer detailRid;

    /**
     * 子项目列表
     */
    private List<Object[]> industryList;

    /**
     * 子项rid
     */
    private Integer subRid;

    /**选中的岗位*/
    private String selPostRids;
    /**选中的危害因素*/
    private String selBadRsnRids;

    public TdZdzybActiveDetListBean() {
        this.ifSQL = true;
        //初始化时判断是否有重点职业病的数据 否则新增 为了编辑页面都用真保存
        this.searchAction();
    }

    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append("         SELECT 1                                num, ");
        sql.append("                 (CASE e.ANALY_TYPE ");
        sql.append("                 WHEN 3 THEN '重点行业' END) TYPE, ");
        sql.append("         d.NAME, ");
        sql.append("         e.RID ");
        sql.append(" FROM TD_ZDZYB_ANALY_TYPE e ");
        sql.append(" LEFT JOIN (SELECT a.MAIN_ID, LISTAGG(c.CODE_NAME, '，') WITHIN GROUP (ORDER BY a.XH ASC) NAME ");
        sql.append(" FROM TD_ZDZYB_ANALY_DETAIL a ");
        sql.append(" LEFT JOIN TS_SIMPLE_CODE c on a.ANALY_ITEM_ID = c.RID ");
        sql.append(" GROUP BY a.MAIN_ID) d on e.RID = d.MAIN_ID ");
        sql.append(" WHERE e.BUS_TYPE = 4 ");
        sql.append(" and e.ANALY_TYPE = 3 ");
        sql.append(" UNION ");
        sql.append(" SELECT 2 num, '重点行业' TYPE, '' NAME, null RID ");
        sql.append(" FROM dual ");
        StringBuffer searchSql = new StringBuffer();
        searchSql.append(" select * from (").append(sql).append(") TT order by TT.num ");
        StringBuffer countSql = new StringBuffer();
        countSql.append(" select count(*) from (").append(sql).append(")");
        return new String[]{searchSql.toString(), countSql.toString()};
    }


    @Override
    public void processData(List<?> list) {
        List<Object[]> tempList = (List<Object[]>) list;
        if (!CollectionUtils.isEmpty(tempList) && tempList.size() > 1) {
            //处理掉多余的行
            tempList.remove(tempList.size() - 1);
            //处理总条数
            this.getDataModel().setRowCount(tempList.size());
            int count = 1;
            for (Object[] objects : tempList) {
                objects[0] = count;
                count++;
            }
        }
    }


    /**
     * @description: 添加 重点行业 弹框
     * <AUTHOR>
     */
    public void addAnalyDetail() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList;
        paramList = new ArrayList<>(1);
        //标题
        paramList.add("重点行业");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>(1);
        //码表类型
        paramList.add("5548");
        paramMap.put("typeNo", paramList);
        //查询条件名称
        paramList = new ArrayList<>(1);
        paramList.add("行业名称");
        paramMap.put("searchName", paramList);
        //列名
        paramList = new ArrayList<>(1);
        paramList.add("行业名称");
        paramMap.put("colName", paramList);
        //已选数据不显示
        paramList = new ArrayList<>(1);
        paramList.add("1");
        paramMap.put("noContainsSelected", paramList);
        //已选数据
        String selRids = "";
        if (!CollectionUtils.isEmpty(this.showAllItemsList)) {
            for (Object[] objects : showAllItemsList) {
                selRids += "," + objects[2].toString();
            }
        }
        if (StringUtils.isNotBlank(selRids)) {
            paramList = new ArrayList<>(1);
            paramList.add(selRids.substring(1));
            paramMap.put("selectIds", paramList);
        }
        paramList = new ArrayList<>(1);
        paramList.add("1");
        paramMap.put("noContainsSelected", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }

    /**
     * @description: 重点行业 弹框 选择后事件
     * <AUTHOR>
     */
    public void onAnalyDetailSel(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            try {
                //保存主表和重点行业
                activeDetService.saveBatchAnalyDetail(this.analyType, list);
                this.rid=analyType.getRid();
                //刷新重点行业列表
                findItems();
                JsfUtil.addSuccessMessage("添加成功！");
                RequestContext.getCurrentInstance().update("tabView:editForm:detailTable");
            } catch (Exception e) {
                e.printStackTrace();
                JsfUtil.addErrorMessage("添加失败！");
            }
        }
    }

    /**
     * @description: 删除 重点行业
     * <AUTHOR>
     */
    public void deleteItemAction() {
        if (this.detailRid == null) {
            return;
        }
        try {
            activeDetService.delAnalyDetail(detailRid);
            //刷新重点行业列表
            findItems();
            JsfUtil.addSuccessMessage("删除成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
     * @description: 添加子项目 弹框
     * <AUTHOR>
     */
    public void addAnalyDetailSubDialog() {
        if (this.detailRid == null) {
            return;
        }
        //查询子项
        findIndustry();
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:selIndustryTable");
        dataTable.setFirst(0);
        dataTable.setRows(10);
        RequestContext.getCurrentInstance().execute("PF('AnalyDetailSubDialog').show();");
    }

    /**
    * @description: 查询子项记录 并且重新拼接选中的值
    * <AUTHOR>
    */
    private void findIndustry() {
        industryList = activeDetService.findIndustryByDetailRid(this.detailRid);
    }


    /**
     * @description: 添加 行业类别 弹框
     * <AUTHOR>
     */
    public void addIndustryDialog() {
        Map<String, Object> options = MapUtils.produceDialogMap(null, 800, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add("行业类别");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>();
        paramList.add("5002");
        paramMap.put("typeNo", paramList);

        paramList = new ArrayList<>();
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<>();
        paramList.add("1");
        paramMap.put("selLast", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/indusTypeCodeMulitySelectList", options, paramMap);
    }

    /**
     * @description: 添加 行业类别小类后 弹框
     * <AUTHOR>
     */
    public void onSimpleCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros"));
            //选择小类后直接真保存
            if (!CollectionUtils.isEmpty(list)) {
                List<TdZdzybAnalyDetailSubComm> subCommList = new ArrayList<>();
                for (TsSimpleCode simpleCode : list) {
                    TdZdzybAnalyDetailSubComm sub = new TdZdzybAnalyDetailSubComm();
                    sub.setFkByMainId(new TdZdzybAnalyDetailComm(this.detailRid));
                    sub.setFkByAnalyItemId(simpleCode);
                    activeDetService.preEntity(sub);
                    subCommList.add(sub);
                }
                if (!CollectionUtils.isEmpty(subCommList)) {
                    try {
                        //批量保存子项
                        activeDetService.saveAnalyDetailSub(subCommList);
                        //刷新子项表
                        findIndustry();
                        //刷新重点行业列表
                        findItems();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
    * @description: 删除 子项
    * <AUTHOR>
    */
    public void deleteSubAction(){
        if(this.subRid==null){
            return;
        }
        try {
            activeDetService.delSubRel(this.subRid);
            //刷新子项表
            findIndustry();
            //刷新重点行业表
            findItems();
            JsfUtil.addSuccessMessage("删除成功！");
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage("删除失败！");
        }
    }

    /**
    * @description: 添加 重点岗位/环节 弹框
    * <AUTHOR>
    */
    public void addImportPostlog(){
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList;
        paramList = new ArrayList<>(1);
        //标题
        paramList.add("重点岗位/环节");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>(1);
        //码表类型
        paramList.add("5595");
        paramMap.put("typeNo", paramList);

        //已选数据
        if(StringUtils.isNotBlank(selPostRids)){
            paramList = new ArrayList<>(1);
            paramList.add(selPostRids);
            paramMap.put("selectIds", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }

    /**
     * @description: 选中 重点岗位/环节 后事件
     * <AUTHOR>
     */
    public void onImportPostAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if(!CollectionUtils.isEmpty(list)){
                saveSubRel(list,1);
            }
        }
    }

    /**
    * @description: 保存子项关系表 抽取
    * <AUTHOR>
    */
    private void saveSubRel(List<TsSimpleCode> list,Integer type) {
        List<TdZdzybDetailSubRelComm> subRelComms=new ArrayList<>();
        for (TsSimpleCode simpleCode : list) {
            TdZdzybDetailSubRelComm subRelComm=new TdZdzybDetailSubRelComm();
            subRelComm.setFkByMainId(new TdZdzybAnalyDetailSubComm(this.subRid));
            subRelComm.setFkByAnalyItemId(simpleCode);
            subRelComm.setType(type);
            activeDetService.preEntity(subRelComm);
            subRelComms.add(subRelComm);
        }
        if(!CollectionUtils.isEmpty(subRelComms)){
            try{
                //保存关系表
                activeDetService.saveBatchSubRel(subRelComms,subRid,type);
                //重新查询
                industryList = activeDetService.findIndustryByDetailRid(this.detailRid);
                //同时刷新重点行业列表
                findItems();
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    /**
     * @description: 添加 重点危害因素 弹框
     * <AUTHOR>
     */
    public void addBadRsnDialog(){
        Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList;
        paramList = new ArrayList<>(1);
        //标题
        paramList.add("重点职业病危害因素");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<>(1);
        //码表类型
        paramList.add("5007");
        paramMap.put("typeNo", paramList);
        //已选数据
        if(StringUtils.isNotBlank(selBadRsnRids)){
            paramList = new ArrayList<>(1);
            paramList.add(selBadRsnRids);
            paramMap.put("selectIds", paramList);
        }
        //只能选最末级
       /* paramList = new ArrayList<>(1);
        paramList.add("1");
        paramMap.put("selLast", paramList);*/
        //查询大类
        paramList = new ArrayList<>(1);
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }
    /**
    * @description: 选中 重点危害因素 事件
    * <AUTHOR>
    */
    public void onBadRsnAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if(!CollectionUtils.isEmpty(list)){
                saveSubRel(list,2);
            }
        }
    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {
        if (this.rid == null) {
            analyType = new TdZdzybAnalyTypeComm();
        } else {
            analyType = activeDetService.find(TdZdzybAnalyTypeComm.class, this.rid);
            findItems();
        }
        DataTable dataTable = (DataTable) FacesContext
                .getCurrentInstance().getViewRoot()
                .findComponent("tabView:editForm:detailTable");
        dataTable.setFirst(0);
        dataTable.setRows(20);
    }

    /**
    * @description:  查询 统计明细列表
    * <AUTHOR>
    */
    private void findItems() {
        //查询重点行业的sql
        showAllItemsList = new ArrayList<>();
        showAllItemsList = activeDetService.findAnalyDetail(this.rid);
        //重点岗位  重点危害因素去重
        if(!CollectionUtils.isEmpty(showAllItemsList)){
            for (Object[] objects : showAllItemsList) {
                objects[4]=removeRepeatStr(objects[4]);
                objects[5]=removeRepeatStr(objects[5]);
                objects[6]=removeRepeatStr(objects[6]);
            }
        }
    }

    /**
    * @description:去重
    * <AUTHOR>
    */
    private Object removeRepeatStr(Object objects) {
        if(ObjectUtil.isNotEmpty(objects)){
            Set<String> postSet=new HashSet<>();
            List<String> newPost=new ArrayList<>();
            List<String> postList = StringUtils.string2list(objects.toString(), "，");
            for (String post : postList) {
                if(postSet.add(post)){
                    newPost.add(post);
                }
            }
            return StringUtils.list2string(newPost,"，");
        }
        return null;
    }

    @Override
    public void saveAction() {
        try {
            if(StringUtils.isNotBlank(analyType.getRmk()) && analyType.getRmk().length()>50){
                JsfUtil.addErrorMessage("备注长度不能超过50！");
                return;
            }
            //主表保存，序号保存
            activeDetService.upsertAnalyDetail(analyType, showAllItemsList);
            //主表rid回置
            this.rid=analyType.getRid();
            //刷新重点行业列表
            findItems();
            JsfUtil.addSuccessMessage("保存成功！");
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("保存失败！");
        }
    }


    public TdZdzybAnalyTypeComm getAnalyType() {
        return analyType;
    }

    public void setAnalyType(TdZdzybAnalyTypeComm analyType) {
        this.analyType = analyType;
    }

    public List<Object[]> getShowAllItemsList() {
        return showAllItemsList;
    }

    public void setShowAllItemsList(List<Object[]> showAllItemsList) {
        this.showAllItemsList = showAllItemsList;
    }

    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public TdZdzybAnalyDetailComm getTdZdzybAnalyDetail() {
        return tdZdzybAnalyDetail;
    }

    public void setTdZdzybAnalyDetail(TdZdzybAnalyDetailComm tdZdzybAnalyDetail) {
        this.tdZdzybAnalyDetail = tdZdzybAnalyDetail;
    }

    public Integer getDetailRid() {
        return detailRid;
    }

    public void setDetailRid(Integer detailRid) {
        this.detailRid = detailRid;
    }

    public List<Object[]> getIndustryList() {
        return industryList;
    }

    public void setIndustryList(List<Object[]> industryList) {
        this.industryList = industryList;
    }

    public Integer getSubRid() {
        return subRid;
    }

    public void setSubRid(Integer subRid) {
        this.subRid = subRid;
    }

    public String getSelPostRids() {
        return selPostRids;
    }

    public void setSelPostRids(String selPostRids) {
        this.selPostRids = selPostRids;
    }

    public String getSelBadRsnRids() {
        return selBadRsnRids;
    }

    public void setSelBadRsnRids(String selBadRsnRids) {
        this.selBadRsnRids = selBadRsnRids;
    }

}
