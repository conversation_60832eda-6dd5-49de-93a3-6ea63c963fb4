package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.TbTjItems;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.entity.TdZdzybAnalyDetailComm;
import com.chis.modules.heth.comm.logic.TjBhkSearchConditionPO;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.heth.comm.service.TdZdzybCommServiceImpl;
import com.chis.modules.system.entity.TdTjExport;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.*;

/**
 * <p>类描述：个案查询</p>
 * @ClassAuthor qrr,2020年4月22日,TbTjPersonSearchBean
 * */
@ManagedBean(name = "tbTjNewestRecSearcBean")
@ViewScoped
public class TbTjNewestRecSearcBean extends FacesEditBean implements IProcessData {

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
            .getBean(SystemModuleServiceImpl.class);
    private TdZdzybCommServiceImpl analyServiceImpl = SpringContextHolder.getBean(TdZdzybCommServiceImpl.class);
    private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder
            .getBean(HethStaQueryCommServiceImpl.class);

    private TjBhkSearchConditionPO conditionPO;
    private String searchUnitName;
    /***查询条件-用工单位地区*/
    private List<TsZone> zoneList;
    /***查询条件-用工单位地区名称*/
    private String searchZoneName;
    /**查询条件-在岗状态*/
    private List<TsSimpleCode> onGuardList;
    private String selectOnGuardNames;
    /**查询条件-危害因素*/
    private List<TsSimpleCode> badRsnList;
    private String selectBadRsnNames;
    /**查询条件-已选择的年龄*/
    private TreeNode ageSortTree;
    private TreeNode[] selectAges;
    private String selectAgeName;
    /**查询条件-已选择的工龄*/
    private TreeNode workSortTree;
    private String selectWorkName;
    private TreeNode[] selectWorkAges;
    /**查询条件：选择的主检结论名称*/
    private List<TsSimpleCode> searchBhkrstList;
    /**证件类型*/
    private List<TsSimpleCode> cardTypeList;

    private String searchItemNames;

    private String searchBhkrstName;

    /**d导出类型码表*/
    private Map<String,TsSimpleCode> busTypeMap;
    /**导出信息的拼接：rid,codeName*/
    private Map<Integer,String> tsSimpleCodeMap;
    /** 第一层集合 */
    private Set<String> firstLevelNoSet;
    /** 体检项目编码 */
    private Map<String, TbTjItems> codeMap = new HashMap<String, TbTjItems>();
    //非第一层集合
    private Set<String> levelNoSet;
    /** 体检项目缓存 */
    private Map<Integer, String> itemCachMap = new HashMap<Integer, String>();
    private Map<Integer, TbTjItems> itemCachMaps = new HashMap<Integer, TbTjItems>();

    /**菜单授权的菜单树*/
    private TreeNode exportTreeNode;
    /**菜单授权的已选择的树*/
    private TreeNode[] selectedExportNodes;
    /**正在导出次数限制参数配置*/
    private Integer  asyncExportTimes;
    /** 已选择过滤项目集合 */
    private Set<String> selectExportItemSet = new HashSet<String>();
    /**导出标记 1：导出，2：内部导出*/
    private Integer exportFlag;
    /**导出数据对象*/
    private TdTjExport tdTjExport;
    /** 过滤体检项目ID */
    private String exportItems;
    /** 过滤体检项目ID */
    private String selectExportNames;

    public TbTjNewestRecSearcBean(){
        this.ifSQL = true;
        tsSimpleCodeMap =new HashMap<>();
        conditionPO = new TjBhkSearchConditionPO();
        conditionPO.setSearchEndTime(new Date());
        conditionPO.setSearchStartTime(DateUtils.addYears(new Date(), -1));
        initTsSimp();
        initZone();
        initAgeSortTree();
        initWorkSortTree();
        initTjItem();
        //正在导出次数限制参数配置
        asyncExportTimes = PropertyUtils.getValue("asyncExport.times") == null ? 0 : Integer.parseInt(PropertyUtils.getValue("asyncExport.times"));
    }

    /**
     * <p>方法描述：体检项目</p>
     * @MethodAuthor： yzz
     * @Date：2022-06-23
     **/
    private void initTjItem()	{
        List<TsSimpleCode> codeList = this.commService.findLevelSimpleCodesByTypeId("5008");
        if(!CollectionUtils.isEmpty(codeList))	{
            firstLevelNoSet = new LinkedHashSet<String>();
            // 项目分类导出
            codeMap = new HashMap<String, TbTjItems>();
            for (TsSimpleCode tsSimpleCode : codeList) {
                if (StringUtils.containsNone(tsSimpleCode.getCodeLevelNo(), ".")) {
                    continue;
                }
                TbTjItems itm = new TbTjItems();
                itm.setRid(-1);
                itm.setItemName(tsSimpleCode.getCodeName());
                itm.setLevelCode(tsSimpleCode.getCodeLevelNo());
                firstLevelNoSet.add(tsSimpleCode.getCodeLevelNo());
                codeMap.put(tsSimpleCode.getCodeLevelNo(), itm);
            }

            //非第一层集合
            levelNoSet = new LinkedHashSet<String>();
            List<TbTjItems> findTjItemList = hethStaQueryServiceImpl.findTjItemList(null, null, Short.valueOf("1"), null, null);
            if( null != findTjItemList && findTjItemList.size() > 0 )	{
                for(  TbTjItems tbTjItems : findTjItemList )	{

                    itemCachMap.put(tbTjItems.getRid(), tbTjItems.getItemName());
                    itemCachMaps.put(tbTjItems.getRid(), tbTjItems);

                    //项目层级编码
                    String codeLevelNo = tbTjItems.getTsSimpleCode().getCodeLevelNo();

                    tbTjItems.setLevelCode(new StringBuffer(codeLevelNo).append(".").append(tbTjItems.getItemCode()).toString());
                    levelNoSet.add(tbTjItems.getLevelCode());

                    codeMap.put(tbTjItems.getLevelCode(), tbTjItems);
                }
            }

        }
    }

    /**
     * <p>方法描述：初始化年龄</p>
     * @MethodAuthor qrr,2020年4月22日,initWorkSortTree
     * */
    private void initAgeSortTree() {
        List<TdZdzybAnalyDetailComm> list = analyServiceImpl.findDetailListByAnalyType(1, 5);
        this.ageSortTree = new CheckboxTreeNode("root", null);
        this.initSortTree(list, this.ageSortTree);
    }
    /**
     * <p>方法描述：初始化工龄</p>
     * @MethodAuthor qrr,2020年4月22日,initWorkSortTree
     * */
    private void initWorkSortTree() {
        List<TdZdzybAnalyDetailComm> list = analyServiceImpl.findDetailListByAnalyType(1, 6);
        this.workSortTree = new CheckboxTreeNode("root", null);
        this.initSortTree(list, this.workSortTree);
    }
    /**
     * 初始化树
     */
    private void initSortTree(List<TdZdzybAnalyDetailComm> list,TreeNode sortTree) {
        if (null != list && list.size() > 0) {
            for (TdZdzybAnalyDetailComm detail : list) {
                new CheckboxTreeNode(detail, sortTree);
            }
        }
    }
    /**
     * <p>方法描述：初始化地区</p>
     * @MethodAuthor qrr,2020年4月23日,initZone
     * */
    private void initZone() {
        // 地区信息初始化
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if (null==tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        // 地区初始化
        if (null == this.zoneList || this.zoneList.size() <= 0) {
            this.zoneList = this.systemModuleService.findZoneListICanSee(false, tsZone.getZoneGb().substring(0,2));
            this.conditionPO.setSearchZoneCode(this.zoneList.get(0).getZoneGb());
            this.searchZoneName = this.zoneList.get(0).getZoneName();
        }
    }

    /**
     * <p>方法描述：查询体检机构</p>
     * @MethodAuthor qrr,2020年4月22日,
     * */
    public void selUnitAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,625,null,500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(conditionPO.getSearchUnitId());
        paramMap.put("selectIds", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
    }

    /**
     * <p>方法描述：码表初始化</p>
     * @MethodAuthor： yzz
     * @Date：2022-06-23
     **/
    public void initTsSimp(){
        this.searchBhkrstList = commService.findSimpleCodesByTypeId("5005");
        if(!CollectionUtils.isEmpty(searchBhkrstList)){
            for(TsSimpleCode t:searchBhkrstList){
                tsSimpleCodeMap.put(t.getRid(),t.getCodeName());
            }
        }
        this.onGuardList = commService.findSimpleCodesByTypeId("5009");
        if(!CollectionUtils.isEmpty(onGuardList)){
            for(TsSimpleCode t:onGuardList){
                tsSimpleCodeMap.put(t.getRid(),t.getCodeName());
            }
        }
        this.badRsnList = commService.findSimpleCodesByTypeId("5007");
        if(!CollectionUtils.isEmpty(badRsnList)){
            for(TsSimpleCode t:badRsnList){
                tsSimpleCodeMap.put(t.getRid(),t.getCodeName());
            }
        }
        this.cardTypeList = commService.findSimpleCodesByTypeId("5503");
        if(!CollectionUtils.isEmpty(cardTypeList)){
            for(TsSimpleCode t:cardTypeList){
                tsSimpleCodeMap.put(t.getRid(),t.getCodeName());
            }
        }
        //导出类型码表
        List<TsSimpleCode> simpleCodes = commService.findLevelSimpleCodesByTypeId("5550");
        busTypeMap =new HashMap<>();
        if(!CollectionUtils.isEmpty(simpleCodes)){
            for(TsSimpleCode t:simpleCodes){
                if(StringUtils.isNotBlank(t.getExtendS1())){
                    busTypeMap.put(t.getExtendS1(),t);
                }
            }
        }
    }


    @Override
    public String[] buildHqls() {
        StringBuffer sql = new StringBuffer();
        sql.append(" from TD_TJ_BHK T ");
        sql.append(" INNER JOIN TB_TJ_CRPT T1 ON T1.RID = T.ENTRUST_CRPT_ID ");
        sql.append(" INNER JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID ");
        sql.append(" INNER JOIN TS_SIMPLE_CODE T3 on T.CARD_TYPE_ID=T3.rid ");
        sql.append(" INNER JOIN TS_SIMPLE_CODE T4 on T.ONGUARD_STATEID=T4.rid ");
        sql.append(" INNER JOIN TB_TJ_SRVORG T5 on T.BHKORG_ID=T5.rid ");
        sql.append(" where T.IF_RHK=0 and T1.INTER_PRC_TAG = 1 and T.BHK_TYPE IN (3,4) ");
        //人员姓名
        if (StringUtils.isNotBlank(conditionPO.getSearchPersonName())) {
            sql.append(" AND T.PERSON_NAME LIKE :searchPersonName escape '\\\'");
            this.paramMap.put("searchPersonName", "%"+StringUtils.convertBFH(conditionPO.getSearchPersonName().trim())+"%");
        }
        //证件类型
        if (StringUtils.isNotBlank(conditionPO.getSearchPsnType())) {
            sql.append(" AND T.CARD_TYPE_ID =").append(conditionPO.getSearchPsnType());
        }
        //身份证号
        if (StringUtils.isNotBlank(conditionPO.getSearchIDC())) {
            sql.append(" AND T.IDC = :searchIDC");
            this.paramMap.put("searchIDC", StringUtils.convertBFH(conditionPO.getSearchIDC().trim()));
        }
        //用工单位地区
        String searchZoneCode = conditionPO.getSearchZoneCode();
        if (StringUtils.isNotBlank(searchZoneCode)) {
            sql.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(searchZoneCode)).append("%'");
        }
        //用工单位名称
        String searchCrptName = conditionPO.getSearchCrptName();
        if (StringUtils.isNotBlank(searchCrptName)) {
            sql.append(" AND T1.CRPT_NAME LIKE :searchCrptName escape '\\\'");
            this.paramMap.put("searchCrptName", "%"+StringUtils.convertBFH(searchCrptName.trim())+"%");
        }
        //体检类型
        String[] searchBhkType = conditionPO.getSearchBhkType();
        if (null!=searchBhkType && searchBhkType.length>0) {
            StringBuffer bhkType = new StringBuffer();
            for (String s : searchBhkType) {
                bhkType.append(",").append(s);
            }
            sql.append(" AND T.BHK_TYPE IN (").append(bhkType.substring(1)).append(")");
        }
        //体检日期
        Date searchStartTime = conditionPO.getSearchStartTime();
        if ( null != searchStartTime) {
            sql.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(searchStartTime, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        Date searchEndTime = conditionPO.getSearchEndTime();
        if ( null != searchEndTime) {
            sql.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(searchEndTime, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        //报告出具日期
        Date startRptPrintDate = conditionPO.getStartRptPrintDate();
        if ( null != startRptPrintDate) {
            sql.append(" AND T.RPT_PRINT_DATE >= TO_DATE('").append(DateUtils.formatDate(startRptPrintDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        Date endRptPrintDate = conditionPO.getEndRptPrintDate();
        if ( null != endRptPrintDate) {
            sql.append(" AND T.RPT_PRINT_DATE <= TO_DATE('").append(DateUtils.formatDate(endRptPrintDate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        //报告日期
        Date startCreateDate = conditionPO.getStartCreateDate();
        if ( null != startCreateDate) {
            sql.append(" AND T.CREATE_DATE >= TO_DATE('").append(DateUtils.formatDate(startCreateDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
        }
        Date endCreateDate = conditionPO.getEndCreateDate();
        if ( null != endCreateDate) {
            sql.append(" AND T.CREATE_DATE <= TO_DATE('").append(DateUtils.formatDate(endCreateDate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
        }
        //在岗状态
        String selectOnGuardIds = conditionPO.getSelectOnGuardIds();
        if (StringUtils.isNotBlank(selectOnGuardIds)) {
            sql.append(" AND T.ONGUARD_STATEID IN (").append(selectOnGuardIds).append(")");
        }
        //体检危害因素
        String selectBadRsnIds = conditionPO.getSelectBadRsnIds();
        if (StringUtils.isNotBlank(selectBadRsnIds)) {
            sql.append(" AND EXISTS ( SELECT 1 from TD_TJ_BADRSNS badRsn where badRsn.BHK_ID=T.RID AND ");
            sql.append(" badRsn.BADRSN_ID IN (").append(selectBadRsnIds).append(") )");
        }
        //监测类型
        String[] searchJcType = conditionPO.getSearchJcType();
        if (null!=searchJcType && searchJcType.length>0) {
            StringBuffer type = new StringBuffer();
            for (String s : searchJcType) {
                type.append(",").append(s);
            }
            sql.append(" AND T.JC_TYPE IN (").append(type.substring(1)).append(")");
        }
        //年龄
        List<TdZdzybAnalyDetailComm> selectAgeAnalyDetails = conditionPO.getSelectAgeAnalyDetails();
        if (null!=selectAgeAnalyDetails && selectAgeAnalyDetails.size()>0) {
            StringBuffer detail = new StringBuffer();
            for (TdZdzybAnalyDetailComm detailAnaly : selectAgeAnalyDetails) {
                //0-10 20-30
                detail.append(" or (");
                Integer geNum = detailAnaly.getGeNum();
                if (null!=geNum) {
                    detail.append("T.AGE >=").append(geNum);
                }
                Integer gtNum = detailAnaly.getGtNum();
                if (null!=gtNum) {
                    if (null!=geNum) {
                        detail.append(" AND ");
                    }
                    detail.append(" T.AGE >").append(gtNum);
                }
                Integer leNum = detailAnaly.getLeNum();
                if (null!=leNum) {
                    if (null!=geNum||null!=gtNum) {
                        detail.append(" AND ");
                    }
                    detail.append(" T.AGE <=").append(leNum);
                }
                Integer ltNum = detailAnaly.getLtNum();
                if (null!=ltNum) {
                    if (null!=geNum||null!=gtNum||null!=leNum) {
                        detail.append(" AND ");
                    }
                    detail.append(" T.AGE <").append(ltNum);
                }
                detail.append(")");
            }
            sql.append(" AND (").append(detail.substring(3)).append(")");
        }
        //接害工龄
        List<TdZdzybAnalyDetailComm> selectWorkAnalyDetails = conditionPO.getSelectWorkAnalyDetails();
        if (null!=selectWorkAnalyDetails && selectWorkAnalyDetails.size()>0) {
            StringBuffer detail = new StringBuffer();
            for (TdZdzybAnalyDetailComm detailAnaly : selectWorkAnalyDetails) {
                detail.append(" or (");
                if(null== detailAnaly.getGeNum() && null == detailAnaly.getGtNum() && null == detailAnaly.getLeNum() && null == detailAnaly.getLtNum()){
                    detail.append(" T.TCHBADRSNTIM is null ) ");
                    continue;
                }
                Integer geNum = detailAnaly.getGeNum();
                if (null!=geNum) {
                    detail.append(" T.TCHBADRSNTIM >=").append(geNum);
                }
                Integer gtNum = detailAnaly.getGtNum();
                if (null!=gtNum) {
                    if (null!=geNum) {
                        detail.append(" AND ");
                    }
                    detail.append(" T.TCHBADRSNTIM >").append(gtNum);
                }
                Integer leNum = detailAnaly.getLeNum();
                if (null!=leNum) {
                    if (null!=geNum||null!=gtNum) {
                        detail.append(" AND ");
                    }
                    detail.append(" T.TCHBADRSNTIM <=").append(leNum);
                }
                Integer ltNum = detailAnaly.getLtNum();
                if (null!=ltNum) {
                    if (null!=geNum||null!=gtNum||null!=leNum) {
                        detail.append(" AND ");
                    }
                    detail.append(" T.TCHBADRSNTIM <").append(ltNum);
                }
                detail.append(")");
            }
            sql.append(" AND (").append(detail.substring(3)).append(")");
        }
        //主检结论
        String searchSelBhkrstIds = conditionPO.getSearchSelBhkrstIds();
        if (StringUtils.isNotBlank(searchSelBhkrstIds)) {
            sql.append(" AND T.NEW_BHKRST_ID IN (").append(searchSelBhkrstIds).append(")");
        }
        //体检机构
        if (StringUtils.isNotBlank(conditionPO.getSearchUnitId()) ) {
            sql.append(" AND T.BHKORG_ID IN (").append(conditionPO.getSearchUnitId()).append(")");
        }
        //体检项目
        String searchItemIds = conditionPO.getSearchItemIds();
        if(StringUtils.isNotBlank(searchItemIds))	{
            sql.append(" AND EXISTS ( SELECT 1 from TD_TJ_BHKSUB TSUB where TSUB.BHK_ID=T.RID ");
            String[] split = searchItemIds.split(",");
            sql.append(" AND (");
            StringBuffer buffer = new StringBuffer();
            for (String s : split) {
                String[] split2 = s.split("@@");
                buffer.append(" OR (TSUB.ITEM_ID =").append(split2[0]);
                if (split2.length>1) {
                    buffer.append(" AND TSUB.RGLTAG = ").append(split2[1]);
                }
                buffer.append(")");
            }
            sql.append(buffer.substring(3)).append(")").append(")");
        }
        String countSql = "SELECT COUNT(*) "+sql.toString();
        String selectSql = "SELECT T.rid,substr(T2.FULL_NAME,instr(T2.FULL_NAME,'_')+1) as zoneName," +
                "T.PERSON_NAME,T3.CODE_NAME,"+
                "DECODE (LENGTH(T.IDC),15,SUBSTR(T.IDC,1,6) || '******' || SUBSTR (T.IDC,13),18,SUBSTR(T.IDC, 1,6) || '********' || SUBSTR(T.IDC,15),CASE WHEN T.IDC IS NOT NULL THEN SUBSTR(T.IDC, 1,LENGTH(T.IDC)-4) || '****' END) AS IDC,"+
                "T1.CRPT_NAME,T4.CODE_NAME as ONGUARD_STATE,"+
                "case when T.JC_TYPE=1 then '常规监测' when T.JC_TYPE=2 then '主动监测' end as JC_TYPE,"+
                " T.BHK_DATE,T.RPT_PRINT_DATE,T5.UNIT_NAME "+sql.toString()+" ORDER BY T2.ZONE_GB,T.BHK_DATE,T.PERSON_NAME";
        return new String[]{selectSql,countSql};
    }


    /**
     * <p>方法描述：导出方法</p>
     * @MethodAuthor： yzz
     * @Date：2022-06-23
     **/
    public void tjExportItemAction(){

        //验证是否达到次数
        String  typeId = busTypeMap.get("2") == null ? null : busTypeMap.get("2").getRid().toString();
        if(StringUtils.isBlank(typeId)){
            JsfUtil.addErrorMessage("导出失败，导出类型不存在！");
            return;
        }
        Integer tdTjExportCount = this.hethStaQueryServiceImpl.findTdTjExportByOperPsnId(typeId);
        if(tdTjExportCount >= asyncExportTimes){
            JsfUtil.addErrorMessage("导出次数达到上限，请稍后导出！");
            return;
        }
        this.selectedExportNodes = null;
        this.exportTreeNode = new CheckboxTreeNode("root", null);
        if (firstLevelNoSet.size() > 0 && codeMap.size() > 0) {
            for (String ln : firstLevelNoSet) {
                TbTjItems tbTjItems = codeMap.get(ln);
                TreeNode node = new CheckboxTreeNode(tbTjItems, this.exportTreeNode);

                this.addExportChildNode(ln, levelNoSet, codeMap, node, null);
            }
        }
        RequestContext.getCurrentInstance().execute("PF('ExportItemDialog').show()");
        
    }

    /**
     *  <p>方法描述：导出</p>
     * @MethodAuthor hsj
     */
    public void exportData(){
        exportItemAction();
        tdTjExport = new TdTjExport();
        //导出类型
        tdTjExport.setFkByBusTypeId(busTypeMap.get("2"));
        //导出条件
        exportConditionData();
        tdTjExport.setState(0);
        tdTjExport.setExportDate(new Date());
        tdTjExport.setFkByOperUnitId(Global.getUser().getTsUnit());
        tdTjExport.setFkByOperPsnId(Global.getUser());
        String fileName = busTypeMap.get("2").getExtendS3();
        if(null != exportFlag && exportFlag == 2){
            fileName = fileName + "（内部）";
        }
        tdTjExport.setExportFileName(fileName);
        this.hethStaQueryServiceImpl.saveTdTjExport(tdTjExport);
        JsfUtil.addSuccessMessage("导出任务已发送，请至导出文件下载模块下载！");
    }
    /**
     * 导出项目
     */
    public void exportItemAction() {
        exportItems = null;
        selectExportNames = null;

        //临时体检项目名称
        List<TbTjItems> items = new ArrayList<TbTjItems>();

        if(null != this.selectedExportNodes && this.selectedExportNodes.length > 0) {
            for(TreeNode node:this.selectedExportNodes) {
                this.chooseExportMenuIds(node,items);
            }
            compareTbTjItems(items);
        }else {
            if(null != this.exportTreeNode) {
                List<TreeNode> childs = exportTreeNode.getChildren();
                if (null!=childs && childs.size()>0) {
                    for (TreeNode node : childs) {
                        List<TreeNode> lastNodes = node.getChildren();
                        if (CollectionUtils.isEmpty(lastNodes)) {// 最末级
                            TbTjItems data = (TbTjItems)node.getData();
                            if( null != data.getRid() &&  data.getRid().intValue() != -1)	{
                                items.add(data);
                            }
                        }else {
                            for (TreeNode last : lastNodes) {
                                TbTjItems data = (TbTjItems)last.getData();
                                if( null != data.getRid() &&  data.getRid().intValue() != -1)	{
                                    items.add(data);
                                }
                            }
                        }
                    }
                }
            }
        }
        //临时体检项目ids
        if (!CollectionUtils.isEmpty(items)) {
            StringBuilder tempStr = new StringBuilder();
            StringBuilder tempNameStr = new StringBuilder();
            for (TbTjItems t : items) {
                tempStr.append(",").append(t.getRid());
                tempNameStr.append(",").append(t.getItemName());
            }
            exportItems = tempStr.substring(1);
            selectExportNames = tempNameStr.substring(1);
        }
        if(null != this.selectedExportNodes && this.selectedExportNodes.length > 0 && StringUtils.isNotEmpty(selectExportNames)) {
            conditionPO.setItemNames(selectExportNames.replaceAll(",","，"));
        }else{
            conditionPO.setItemNames(null);
        }
    }
    /**
     *  <p>方法描述：导出条件的封装</p>
     * @MethodAuthor hsj
     */
    private void exportConditionData() {
        JSONObject jsonObject= new JSONObject(true) ;
        StringBuffer str = new StringBuffer();
        //人员名称
        if(StringUtils.isNotBlank(conditionPO.getSearchPersonName())){
            jsonObject.put("searchPersonName",StringUtils.convertBFH(conditionPO.getSearchPersonName().trim()));
            str.append("人员名称@*@").append(StringUtils.convertBFH(conditionPO.getSearchPersonName().trim())).append("#*#");
        }
        //证件类型
        if(StringUtils.isNotBlank(conditionPO.getSearchPsnType())){
            jsonObject.put("searchPsnType",conditionPO.getSearchPsnType());
            str.append("证件类型@*@").append(tsSimpleCodeMap.get(Integer.valueOf(conditionPO.getSearchPsnType()))).append("#*#");
        }
        //证件号码
        if(StringUtils.isNotBlank(conditionPO.getSearchIDC())){
            jsonObject.put("searchIDC", StringUtils.convertBFH(conditionPO.getSearchIDC().trim()));
            str.append("证件号码@*@").append( StringUtils.convertBFH(conditionPO.getSearchIDC().trim())).append("#*#");
        }
        //用工单位地区
        if(StringUtils.isNotBlank(conditionPO.getSearchZoneCode())){
            jsonObject.put("searchZoneCode",ZoneUtil.zoneSelect(conditionPO.getSearchZoneCode()));
            str.append("用工单位地区@*@").append(searchZoneName).append("#*#");
        }
        //用工单位名称
        if(StringUtils.isNotBlank(conditionPO.getSearchCrptName())){
            jsonObject.put("searchCrptName",StringUtils.convertBFH(conditionPO.getSearchCrptName().trim()));
            str.append("用工单位名称@*@").append(StringUtils.convertBFH(conditionPO.getSearchCrptName().trim())).append("#*#");
        }
        //体检类型
        if(null != conditionPO.getSearchBhkType() && conditionPO.getSearchBhkType().length > 0){
            jsonObject.put("searchBhkType",conditionPO.getSearchBhkType());
            String s ="";
            for(int i = 0;i<conditionPO.getSearchBhkType().length;i++){
                if( "3".equals(conditionPO.getSearchBhkType()[i])){
                    s = s +"，职业健康检查";
                }else if("4".equals(conditionPO.getSearchBhkType()[i])){
                    s = s +"，放射卫生健康检查";
                }
            }
            str.append("体检类型@*@").append(s.substring(1)).append("#*#");
        }
        //体检日期
        if(null != conditionPO.getSearchStartTime()){
            jsonObject.put("searchStartTime",DateUtils.formatDate(conditionPO.getSearchStartTime()));
        }
        if(null != conditionPO.getSearchEndTime()){
            jsonObject.put("searchEndTime",DateUtils.formatDate(conditionPO.getSearchEndTime()));
        }
        if(null != conditionPO.getSearchStartTime() || null != conditionPO.getSearchEndTime()){
            StringBuffer s = new StringBuffer();
            s.append(conditionPO.getSearchStartTime() == null ? "" : DateUtils.formatDate(conditionPO.getSearchStartTime()));
            s.append("~");
            s.append(conditionPO.getSearchEndTime() == null ? "" : DateUtils.formatDate(conditionPO.getSearchEndTime()));
            str.append("体检日期@*@").append(s.toString()).append("#*#");
        }
        //报告出具日期
        if(null != conditionPO.getStartRptPrintDate()){
            jsonObject.put("startRptPrintDate",DateUtils.formatDate(conditionPO.getStartRptPrintDate()));
        }
        if(null != conditionPO.getEndRptPrintDate()){
            jsonObject.put("endRptPrintDate",DateUtils.formatDate(conditionPO.getEndRptPrintDate()));
        }
        if(null != conditionPO.getStartRptPrintDate() || null != conditionPO.getEndRptPrintDate()){
            StringBuffer s = new StringBuffer();
            s.append(conditionPO.getStartRptPrintDate() == null ? "" : DateUtils.formatDate(conditionPO.getStartRptPrintDate()));
            s.append("~");
            s.append(conditionPO.getEndRptPrintDate() == null ? "" : DateUtils.formatDate(conditionPO.getEndRptPrintDate()));
            str.append("报告出具日期@*@").append(s.toString()).append("#*#");
        }
        //报告日期
        if(null != conditionPO.getStartCreateDate()){
            jsonObject.put("startCreateDate",DateUtils.formatDate(conditionPO.getStartCreateDate()));
        }
        if(null != conditionPO.getEndCreateDate()){
            jsonObject.put("endCreateDate",DateUtils.formatDate(conditionPO.getEndCreateDate()));
        }
        if(null != conditionPO.getStartCreateDate() || null != conditionPO.getEndCreateDate()){
            StringBuffer s = new StringBuffer();
            s.append(conditionPO.getStartCreateDate() == null ? "" : DateUtils.formatDate(conditionPO.getStartCreateDate()));
            s.append("~");
            s.append(conditionPO.getEndCreateDate() == null ? "" : DateUtils.formatDate(conditionPO.getEndCreateDate()));
            str.append("报告日期@*@").append(s.toString()).append("#*#");
        }
        //在岗状态
        if(StringUtils.isNotBlank(conditionPO.getSelectOnGuardIds())){
            jsonObject.put("selectOnGuardIds",conditionPO.getSelectOnGuardIds());
            String[] ss = conditionPO.getSelectOnGuardIds().split(",");
            String s ="";
            for(int i = 0;i< ss.length;i++){
                s = s +"，"+tsSimpleCodeMap.get(Integer.valueOf(ss[i]));
            }
            str.append("在岗状态@*@").append(s.substring(1)).append("#*#");
        }
        //体检危害因素
        if(StringUtils.isNotBlank(conditionPO.getSelectBadRsnIds())){
            jsonObject.put("selectBadRsnIds",conditionPO.getSelectBadRsnIds());
            String[] ss = conditionPO.getSelectBadRsnIds().split(",");
            String s ="";
            for(int i = 0;i< ss.length;i++){
                s = s +"，"+tsSimpleCodeMap.get(Integer.valueOf(ss[i]));
            }
            str.append("体检危害因素@*@").append(s.substring(1)).append("#*#");
        }
        //监测类型
        if(null != conditionPO.getSearchJcType() && conditionPO.getSearchJcType().length > 0){
            jsonObject.put("searchJcType",conditionPO.getSearchJcType());
            String s ="";
            for(int i = 0;i<conditionPO.getSearchJcType().length;i++){
                if("1".equals(conditionPO.getSearchJcType()[i])){
                    s = s +"，常规监测";
                }else if("2".equals(conditionPO.getSearchJcType()[i])){
                    s = s +"，主动监测";
                }
            }
            str.append("监测类型@*@").append(s.substring(1)).append("#*#");
        }
        //年龄
        JSONArray jsonArray = new JSONArray();
        if(!CollectionUtils.isEmpty(conditionPO.getSelectAgeAnalyDetails())){
            StringBuffer detail = new StringBuffer();
            for (TdZdzybAnalyDetailComm detailAnaly : conditionPO.getSelectAgeAnalyDetails()) {
                detail.append("，").append(detailAnaly.getAnalyItem().getCodeName());
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("geNum",detailAnaly.getGeNum());
                jsonObject1.put("gtNum",detailAnaly.getGtNum());
                jsonObject1.put("leNum",detailAnaly.getLeNum());
                jsonObject1.put("ltNum",detailAnaly.getLtNum());
                jsonArray.add(jsonObject1);
            }
            str.append("年龄@*@").append(detail.substring(1)).append("#*#");
            jsonObject.put("selectAgeAnalyDetails",jsonArray);
        }
        //接害工龄
        JSONArray jsonArray1 = new JSONArray();
        if(!CollectionUtils.isEmpty(conditionPO.getSelectWorkAnalyDetails())){
            StringBuffer detail = new StringBuffer();
            for (TdZdzybAnalyDetailComm detailAnaly : conditionPO.getSelectWorkAnalyDetails()) {
                detail.append("，").append(detailAnaly.getAnalyItem().getCodeName());
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("geNum",detailAnaly.getGeNum());
                jsonObject1.put("gtNum",detailAnaly.getGtNum());
                jsonObject1.put("leNum",detailAnaly.getLeNum());
                jsonObject1.put("ltNum",detailAnaly.getLtNum());
                jsonArray1.add(jsonObject1);
            }
            str.append("接害工龄@*@").append(detail.substring(1)).append("#*#");
            jsonObject.put("selectWorkAnalyDetails",jsonArray1);
        }
        //主检结论
        if(StringUtils.isNotBlank(conditionPO.getSearchSelBhkrstIds())){
            jsonObject.put("searchSelBhkrstIds",conditionPO.getSearchSelBhkrstIds());
            String[] ss = conditionPO.getSearchSelBhkrstIds().split(",");
            String s ="";
            for(int i = 0;i< ss.length;i++){
                s = s +"，"+tsSimpleCodeMap.get(Integer.valueOf(ss[i]));
            }
            str.append("主检结论@*@").append(s.substring(1)).append("#*#");
        }
        //体检机构
        if(StringUtils.isNotBlank(conditionPO.getSearchUnitId())){
            jsonObject.put("searchUnitId",conditionPO.getSearchUnitId());
            str.append("体检机构@*@").append(searchUnitName).append("#*#");
        }
        //体检项目
        if(StringUtils.isNotBlank(conditionPO.getSearchItemIds())){
            jsonObject.put("searchItemIds",conditionPO.getSearchItemIds());
            str.append("体检项目@*@").append(searchItemNames).append("#*#");
        }
        //导出项目
        if(StringUtils.isNotBlank(exportItems)){
            jsonObject.put("exportItems",exportItems);
        }
        if(StringUtils.isNotBlank(conditionPO.getItemNames())){
            str.append("导出项目@*@").append(conditionPO.getItemNames()).append("#*#");
        }
        //导出类型
        jsonObject.put("type",exportFlag == null ? "1" : exportFlag);
        tdTjExport.setExportCondition(null == jsonObject  ? null : JSON.toJSONString(jsonObject));
        tdTjExport.setExportConditionShow(str.toString());
    }

    /**
     * <p>方法描述：选择体检项目，输入框显示名称排序</p>
     * Comparison method violates its general contract! 参考https://www.cnblogs.com/firstdream/p/7204067.html
     * @MethodAuthor qrr,2020年5月20日,compareTbTjItems
     * */
    private void compareTbTjItems(List<TbTjItems> items) {
        if(CollectionUtils.isEmpty(items)){
            return;
        }
        Collections.sort(items, new Comparator<TbTjItems>() {
            @Override
            public int compare(TbTjItems o1, TbTjItems o2) {
                TsSimpleCode t1 = o1.getTsSimpleCode();
                TsSimpleCode t2 = o2.getTsSimpleCode();
                if (null!=t1 && null!=t2) {
                    Integer num1 = t1.getNum();
                    Integer num2 = t2.getNum();
                    if (null!=num1 && null!=num2) {
                        int val = num1.compareTo(num2);
                        if (val != 0) {
                            return val;
                        }
                    }else {
                        int val = String.valueOf(t1.getCodeLevelNo()).equals(String.valueOf(t2.getCodeLevelNo()))
                                ? 0 : String.valueOf(t1.getCodeLevelNo())
                                .compareTo(String.valueOf(t2.getCodeLevelNo()));
                        if (val != 0) {
                            return String.valueOf(t1.getCodeLevelNo()).compareTo(
                                    String.valueOf(t2.getCodeLevelNo()));
                        }
                    }
                }
                Integer itemNum1 = o1.getNum();
                Integer itemNum2 = o2.getNum();
                if (null!=itemNum1 && null!=itemNum2) {
                    int result = itemNum1.compareTo(itemNum2);
                    if(0 != result){
                        return result;
                    }
                }else if(null != itemNum1 && null == itemNum2){
                    return 1;
                }else if(null != itemNum2 && null == itemNum1){
                    return -1;
                }
                String itemCode1 = o1.getItemCode();
                String itemCode2 = o2.getItemCode();
                if(null != itemCode1 && null != itemCode2){
                    return itemCode1.compareTo(itemCode2);
                }else if(null != itemCode1 && null == itemCode2){
                    return 1;
                }else if(null != itemCode2 && null == itemCode1){
                    return -1;
                }
                return 0;
            }

        });
    }

    /**
     * 根据树节点，将其与其父节点的对象ID放到集合中
     * @param
     * @param node 树节点，内容存的是菜单对象
     */
    private void chooseExportMenuIds(TreeNode node,List<TbTjItems> items) {
        if(null != node && !(node.equals(this.exportTreeNode))) {
            TbTjItems data = (TbTjItems)node.getData();
            if( null != data.getRid() &&  data.getRid().intValue() != -1)	{
                items.add(data);
            }
            this.chooseExportMenuIds(node.getParent(),items);
        }
    }

    /**
     * 构建菜单树
     * @param levelNo 菜单层级编码
     * @param levelNoSet 二级以及以上的菜单的层级编码集合
     * @param parentNode 上级树节点
     * @param selectedMenuMap 已有的菜单map
     */
    private void addExportChildNode(String levelNo, Set<String> levelNoSet, Map<String,TbTjItems> itemMap, TreeNode parentNode, Map<Integer, Integer> selectedMenuMap) {
        int level = StringUtils.countMatches(levelNo, ".");
        for(String ln: levelNoSet) {
            if(StringUtils.countMatches(ln, ".")==(level+1) && StringUtils.startsWith(ln,levelNo+".")) {
                TbTjItems t = itemMap.get(ln);
                TreeNode node = new CheckboxTreeNode(t, parentNode);
                if (selectExportItemSet.contains(t.getLevelCode())) {
                    node.setSelected(true);
                }
                this.addExportChildNode(ln, levelNoSet, itemMap, node, selectedMenuMap);
            }
        }
    }
    /**
     * <p>方法描述：查询方法</p>
     * @MethodAuthor： yzz
     * @Date：2022-06-23
     **/
    @Override
    public void searchAction(){
        Date searchStartTime = conditionPO.getSearchStartTime();
        Date searchEndTime = conditionPO.getSearchEndTime();
        if (null != searchStartTime && null != searchEndTime && searchStartTime.after(searchEndTime)) {
            JsfUtil.addErrorMessage("体检开始日期不允许大于体检结束日期！");
            return;
        }
        super.searchAction();
    }

    /**
     * <p>方法描述：处理选择的工龄</p>
     * @MethodAuthor qrr,2020年4月22日,
     * */
    public void hideWorkAgeAction() {
        // 遍历选择的危害因素，获取选择的id与名称
        this.selectWorkName = null;
        List<TdZdzybAnalyDetailComm> selectWorkAnalyDetails = new ArrayList<>();
        if (this.selectWorkAges != null && selectWorkAges.length > 0) {
            StringBuilder nameSb = new StringBuilder(); // 分类名称
            for (TreeNode treeNode : selectWorkAges) {
                TdZdzybAnalyDetailComm detail = (TdZdzybAnalyDetailComm) treeNode.getData();
                nameSb.append("，").append(
                        String.valueOf(detail.getAnalyItem().getCodeName()));
                selectWorkAnalyDetails.add(detail);
            }
            conditionPO.setSelectWorkAnalyDetails(selectWorkAnalyDetails);
            this.selectWorkName = nameSb.toString().substring(1);
        }
    }

    /**
     * <p>方法描述：处理选择的体检机构</p>
     * @MethodAuthor qrr,2020年4月22日,
     * */
    public void onSelectUnitAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
            if (null!=list && list.size()>0) {
                StringBuffer names = new StringBuffer();
                StringBuffer ids = new StringBuffer();
                for (TbTjSrvorg t : list) {
                    names.append("，").append(t.getUnitName());
                    ids.append(",").append(t.getRid());
                }
                conditionPO.setSearchUnitId(ids.substring(1));
                this.searchUnitName = names.substring(1);
            }
        }
    }

    /**
     * <p>方法描述：选择体检项目弹出框</p>
     * @MethodAuthor qrr,2020年5月25日,selItemAction
     * */
    public void selItemAction() {
        Map<String, Object> options = MapUtils.produceDialogMap(null,650,null,490);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(conditionPO.getSearchItemIds());
        paramMap.put("selectIds", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/tjItemMulitySelectList", options, paramMap);
    }

    /**
     * <p>方法描述：体检项目选择数据处理</p>
     * @MethodAuthor qrr,2020年5月25日,onItemAction
     * */
    public void onItemAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TbTjItems> list = (List<TbTjItems>) selectedMap.get("selectPros");
            if (null!=list && list.size()>0) {
                StringBuffer names = new StringBuffer();
                StringBuffer ids = new StringBuffer();
                for (TbTjItems t : list) {
                    names.append("，").append(t.getItemName());
                    ids.append(",").append(t.getRid());
                    if (t.isIfNotHg()) {
                        names.append("（不合格）");
                        ids.append("@@0");
                    }
                }
                conditionPO.setSearchItemIds(ids.substring(1));
                this.searchItemNames = names.substring(1);
            }
        }
    }

    /**
     * <p>方法描述：清空选择的体检项目</p>
     * @MethodAuthor qrr,2020年5月25日,clearSelectedItem
     * */
    public void clearSelectedItem() {
        conditionPO.setSearchItemIds(null);
        this.searchItemNames = null;
    }

    /**
     * <p>方法描述：清空单位</p>
     * @MethodAuthor qrr,2020年4月22日,
     * */
    public void clearUnit() {
        conditionPO.setSearchUnitId(null);
        this.searchUnitName = null;
    }

    /**
     * <p>方法描述：清空选择的工龄</p>
     * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
     * */
    public void clearSelectWorkAge() {
        this.selectWorkName = null;
        conditionPO.setSelectWorkAnalyDetails(null);
        this.selectWorkAges = null;
        List<TreeNode> children = this.workSortTree.getChildren();
        if (null!=children && children.size()>0) {
            for (TreeNode node : children) {
                node.setSelected(false);
                List<TreeNode> children2 = node.getChildren();
                if (null!=children2 && children2.size()>0) {
                    for (TreeNode node2 : children2) {
                        node2.setSelected(false);
                    }
                }
            }
        }
    }
    /**
     * <p>方法描述：处理选择的年龄</p>
     * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
     * */
    public void hideAgeAction() {
        // 遍历选择的危害因素，获取选择的id与名称
        this.selectAgeName = null;
        List<TdZdzybAnalyDetailComm> selectAgeAnalyDetails = new ArrayList<>();
        if (this.selectAges != null && selectAges.length > 0) {
            StringBuilder nameSb = new StringBuilder(); // 分类名称
            for (TreeNode treeNode : selectAges) {
                TdZdzybAnalyDetailComm detail = (TdZdzybAnalyDetailComm) treeNode.getData();
                nameSb.append("，").append(
                        String.valueOf(detail.getAnalyItem().getCodeName()));
                selectAgeAnalyDetails.add(detail);
            }
            conditionPO.setSelectAgeAnalyDetails(selectAgeAnalyDetails);
            this.selectAgeName = nameSb.toString().substring(1);
        }
    }

    /**
     * <p>方法描述：清空选择的年龄龄</p>
     * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
     * */
    public void clearSelectAge() {
        this.selectAgeName = null;
        conditionPO.setSelectAgeAnalyDetails(null);
        this.selectAges = null;
        List<TreeNode> children = this.ageSortTree.getChildren();
        if (null!=children && children.size()>0) {
            for (TreeNode node : children) {
                node.setSelected(false);
                List<TreeNode> children2 = node.getChildren();
                if (null!=children2 && children2.size()>0) {
                    for (TreeNode node2 : children2) {
                        node2.setSelected(false);
                    }
                }
            }
        }
    }
    /**
     *  <p>方法描述：跳转到导出文件下载模块</p>
     * @MethodAuthor hsj 2025-03-05 14:04
     */
    public void fileDownloadAction(){
        RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','导出文件下载','/webapp/system/tdTjExportList.faces','');");
    }
    @Override
    public void processData(List<?> list) {

    }

    @Override
    public void addInit() {

    }

    @Override
    public void viewInit() {

    }

    @Override
    public void modInit() {

    }

    @Override
    public void saveAction() {

    }


    public TjBhkSearchConditionPO getConditionPO() {
        return conditionPO;
    }

    public void setConditionPO(TjBhkSearchConditionPO conditionPO) {
        this.conditionPO = conditionPO;
    }

    public String getSearchUnitName() {
        return searchUnitName;
    }

    public void setSearchUnitName(String searchUnitName) {
        this.searchUnitName = searchUnitName;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public List<TsSimpleCode> getOnGuardList() {
        return onGuardList;
    }

    public void setOnGuardList(List<TsSimpleCode> onGuardList) {
        this.onGuardList = onGuardList;
    }

    public String getSelectOnGuardNames() {
        return selectOnGuardNames;
    }

    public void setSelectOnGuardNames(String selectOnGuardNames) {
        this.selectOnGuardNames = selectOnGuardNames;
    }

    public List<TsSimpleCode> getBadRsnList() {
        return badRsnList;
    }

    public void setBadRsnList(List<TsSimpleCode> badRsnList) {
        this.badRsnList = badRsnList;
    }

    public String getSelectBadRsnNames() {
        return selectBadRsnNames;
    }

    public void setSelectBadRsnNames(String selectBadRsnNames) {
        this.selectBadRsnNames = selectBadRsnNames;
    }

    public TreeNode getAgeSortTree() {
        return ageSortTree;
    }

    public void setAgeSortTree(TreeNode ageSortTree) {
        this.ageSortTree = ageSortTree;
    }

    public TreeNode[] getSelectAges() {
        return selectAges;
    }

    public void setSelectAges(TreeNode[] selectAges) {
        this.selectAges = selectAges;
    }

    public String getSelectAgeName() {
        return selectAgeName;
    }

    public void setSelectAgeName(String selectAgeName) {
        this.selectAgeName = selectAgeName;
    }

    public TreeNode getWorkSortTree() {
        return workSortTree;
    }

    public void setWorkSortTree(TreeNode workSortTree) {
        this.workSortTree = workSortTree;
    }

    public String getSelectWorkName() {
        return selectWorkName;
    }

    public void setSelectWorkName(String selectWorkName) {
        this.selectWorkName = selectWorkName;
    }

    public TreeNode[] getSelectWorkAges() {
        return selectWorkAges;
    }

    public void setSelectWorkAges(TreeNode[] selectWorkAges) {
        this.selectWorkAges = selectWorkAges;
    }

    public List<TsSimpleCode> getSearchBhkrstList() {
        return searchBhkrstList;
    }

    public void setSearchBhkrstList(List<TsSimpleCode> searchBhkrstList) {
        this.searchBhkrstList = searchBhkrstList;
    }

    public String getSearchBhkrstName() {
        return searchBhkrstName;
    }

    public void setSearchBhkrstName(String searchBhkrstName) {
        this.searchBhkrstName = searchBhkrstName;
    }


    public List<TsSimpleCode> getCardTypeList() {
        return cardTypeList;
    }

    public void setCardTypeList(List<TsSimpleCode> cardTypeList) {
        this.cardTypeList = cardTypeList;
    }

    public Map<String, TsSimpleCode> getBusTypeMap() {
        return busTypeMap;
    }

    public void setBusTypeMap(Map<String, TsSimpleCode> busTypeMap) {
        this.busTypeMap = busTypeMap;
    }

    public Map<Integer, String> getTsSimpleCodeMap() {
        return tsSimpleCodeMap;
    }

    public void setTsSimpleCodeMap(Map<Integer, String> tsSimpleCodeMap) {
        this.tsSimpleCodeMap = tsSimpleCodeMap;
    }

    public Set<String> getFirstLevelNoSet() {
        return firstLevelNoSet;
    }

    public void setFirstLevelNoSet(Set<String> firstLevelNoSet) {
        this.firstLevelNoSet = firstLevelNoSet;
    }

    public Map<String, TbTjItems> getCodeMap() {
        return codeMap;
    }

    public void setCodeMap(Map<String, TbTjItems> codeMap) {
        this.codeMap = codeMap;
    }

    public Set<String> getLevelNoSet() {
        return levelNoSet;
    }

    public void setLevelNoSet(Set<String> levelNoSet) {
        this.levelNoSet = levelNoSet;
    }

    public Map<Integer, String> getItemCachMap() {
        return itemCachMap;
    }

    public void setItemCachMap(Map<Integer, String> itemCachMap) {
        this.itemCachMap = itemCachMap;
    }

    public Map<Integer, TbTjItems> getItemCachMaps() {
        return itemCachMaps;
    }

    public void setItemCachMaps(Map<Integer, TbTjItems> itemCachMaps) {
        this.itemCachMaps = itemCachMaps;
    }

    public String getSearchItemNames() {
        return searchItemNames;
    }

    public void setSearchItemNames(String searchItemNames) {
        this.searchItemNames = searchItemNames;
    }

    public TreeNode getExportTreeNode() {
        return exportTreeNode;
    }

    public void setExportTreeNode(TreeNode exportTreeNode) {
        this.exportTreeNode = exportTreeNode;
    }

    public TreeNode[] getSelectedExportNodes() {
        return selectedExportNodes;
    }

    public void setSelectedExportNodes(TreeNode[] selectedExportNodes) {
        this.selectedExportNodes = selectedExportNodes;
    }

    public Integer getAsyncExportTimes() {
        return asyncExportTimes;
    }

    public void setAsyncExportTimes(Integer asyncExportTimes) {
        this.asyncExportTimes = asyncExportTimes;
    }

    public Set<String> getSelectExportItemSet() {
        return selectExportItemSet;
    }

    public void setSelectExportItemSet(Set<String> selectExportItemSet) {
        this.selectExportItemSet = selectExportItemSet;
    }

    public Integer getExportFlag() {
        return exportFlag;
    }

    public void setExportFlag(Integer exportFlag) {
        this.exportFlag = exportFlag;
    }
}
