package com.chis.modules.heth.comm.service;

import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbTjJcTask;
import com.chis.modules.heth.comm.entity.TbTjJcTaskBadrsn;
import com.chis.modules.heth.comm.entity.TbTjJcTaskPsn;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 主动监测任务用Service
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(readOnly = false)
public class ActiveMonitoringTaskServiceImpl extends AbstractTemplate {

    /**
     * 根据RID获取主动监测任务实体
     *
     * @param rid 主动监测任务RID
     * @return 主动监测任务实体
     */
    public TbTjJcTask findTbTjJcTaskByRid(Integer rid) {
        TbTjJcTask jcTask;
        try {
            if (rid == null) {
                jcTask = new TbTjJcTask();
            } else {
                jcTask = super.find(TbTjJcTask.class, rid);
                jcTask.getJcTaskPsnList().size();
            }
        } catch (Exception e) {
            e.printStackTrace();
            jcTask = new TbTjJcTask();
        }
        for (TbTjJcTaskPsn jcTaskPsn : jcTask.getJcTaskPsnList()) {
            //在岗状态
            if (jcTaskPsn.getFkByOnguardStateid() != null && jcTaskPsn.getFkByOnguardStateid().getRid() != null) {
                jcTaskPsn.setOnguadrStateId(jcTaskPsn.getFkByOnguardStateid().getRid());
            }
            //监测岗位
            if (jcTaskPsn.getFkByPostId() != null && jcTaskPsn.getFkByPostId().getRid() != null) {
                jcTaskPsn.setPostId(jcTaskPsn.getFkByPostId().getRid());
                jcTaskPsn.setPostName(jcTaskPsn.getFkByPostId().getCodeName());
            }
            //职业病主动监测因素
            jcTaskPsn.getJcTaskBadrsnList().size();
            List<String> jcTaskBadrsnRidList = new ArrayList<>();
            List<String> jcTaskBadrsnNameList = new ArrayList<>();
            for (TbTjJcTaskBadrsn jcTaskBadrsn : jcTaskPsn.getJcTaskBadrsnList()) {
                if (jcTaskBadrsn.getFkByBadrsnId() == null || jcTaskBadrsn.getFkByBadrsnId().getRid() == null) {
                    continue;
                }
                jcTaskBadrsnRidList.add(StringUtils.objectToString(jcTaskBadrsn.getFkByBadrsnId().getRid()));
                jcTaskBadrsnNameList.add(StringUtils.objectToString(jcTaskBadrsn.getFkByBadrsnId().getCodeName()));
            }
            jcTaskPsn.setRsnId(StringUtils.list2string(jcTaskBadrsnRidList, ","));
            jcTaskPsn.setRsnName(StringUtils.list2string(jcTaskBadrsnNameList, "，"));
        }
        return jcTask;
    }

    /**
     * 更新主动监测任务实体
     *
     * @param jcTask 主动监测任务实体
     */
    public void upsertTbTjJcTask(TbTjJcTask jcTask) {
        if (!CollectionUtils.isEmpty(jcTask.getJcTaskPsnList())) {
            for (TbTjJcTaskPsn jcTaskPsn : jcTask.getJcTaskPsnList()) {
                if (jcTaskPsn.getOnguadrStateId() == null) {
                    jcTaskPsn.setFkByOnguardStateid(null);
                } else {
                    jcTaskPsn.setFkByOnguardStateid(new TsSimpleCode(jcTaskPsn.getOnguadrStateId()));
                }
                if (jcTaskPsn.getPostId() == null) {
                    jcTaskPsn.setFkByPostId(null);
                } else {
                    jcTaskPsn.setFkByPostId(new TsSimpleCode(jcTaskPsn.getPostId()));
                }
                if (!CollectionUtils.isEmpty(jcTaskPsn.getJcTaskBadrsnList())) {
                    jcTaskPsn.getJcTaskBadrsnList().clear();
                } else {
                    jcTaskPsn.setJcTaskBadrsnList(new ArrayList<TbTjJcTaskBadrsn>());
                }
                if (StringUtils.isBlank(jcTaskPsn.getRsnId())) {
                    continue;
                }
                List<String> rsnIdList = StringUtils.string2list(jcTaskPsn.getRsnId(), ",");
                for (String rsnIdStr : rsnIdList) {
                    Integer rsnId = ObjectUtil.convert(Integer.class, rsnIdStr);
                    if (rsnId == null) {
                        continue;
                    }
                    TbTjJcTaskBadrsn jcTaskBadrsn = new TbTjJcTaskBadrsn();
                    jcTaskBadrsn.setFkByMainId(jcTaskPsn);
                    jcTaskBadrsn.setFkByBadrsnId(new TsSimpleCode(rsnId));
                    super.preEntity(jcTaskBadrsn);
                    jcTaskPsn.getJcTaskBadrsnList().add(jcTaskBadrsn);
                }
            }
        }
        super.upsertEntity(jcTask);
    }

    /**
     * 更新主动监测任务字段
     *
     * @param rid   主动监测任务RID
     * @param param 字段名称
     * @param value 字段值
     */
    public void updateTbTjJcTaskParamByRid(Integer rid, String param, Object value) {
        if (rid == null) {
            return;
        }
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "UPDATE TB_TJ_JC_TASK SET " + param + " = :" + param + ", MODIFY_DATE = :nowDate, MODIFY_MANID = :manId WHERE RID = :rid ";
        paramMap.put("rid", rid);
        paramMap.put(param, value);
        paramMap.put("nowDate", new Date());
        paramMap.put("manId", Global.getUser().getRid());
        super.executeSql(sql, paramMap);
    }
}
