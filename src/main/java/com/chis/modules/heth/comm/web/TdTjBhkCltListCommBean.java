package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.enumn.ReturnType;
import com.chis.modules.heth.comm.json.*;
import com.chis.modules.heth.comm.logic.*;
import com.chis.modules.heth.comm.service.*;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.EncryptFieldUtil;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description : 体检信息录入-列表页
 * @ClassAuthor : anjing
 * @Date : 2020/4/22 16:03
 **/
@ManagedBean(name = "tdTjBhkCltListCommBean")
@ViewScoped
public class TdTjBhkCltListCommBean extends FacesEditBean implements IProcessData {

    private Integer view;

    /**可否复检*/
    private Integer ifDraw;
    /**修改页面：体检信息Id*/
    private Integer rid;
    /**当前活动的标签，从0开始，默认为0,0:基本信息1：体检问诊2：检查结果3：检查结论*/
    private int addActiveTab;
    /**资质服务机构*/
    List<TbTjSrvorg> tbTjSrvorgList = new ArrayList<>();

    /**能看到的地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：企业名称*/
    private String searchCrptName;
    /**查询条件：组织机构代码*/
    private String searchInstitutionCode;
    /**查询条件：人员姓名*/
    private String searchPersonName;
    /**查询条件：身份证号码*/
    private String searchIdc;
    /**查询条件：开始体检日期*/
    private Date searchBhkBdate;
    /**查询条件：结束体检日期*/
    private Date searchBhkEdate;
    /**查询条件：体检编号*/
    private String searchBhkCode;
    /**查询条件：数据来源*/
    private List<Integer> jcTypeList;
    /**查询条件：是否复检*/
    private List<Integer> rhkStateList;
    /**查询条件：体检录入状态*/
    private List<Integer> bhkRcdStateList;

    /**查询条件：报告出具日期*/
    private Date searchRptSDate;
    private Date searchRptEDate;

    /**体检主表（录入数据）*/
    private TdTjBhkClt tdTjBhkClt;
    /**选择企业*/
    private TbTjCrpt selectCrptInfo = new TbTjCrpt();
    /**选择体检信息*/
    private TdTjBhkClt selectBhkClt = new TdTjBhkClt();
    /**添加页面：在岗状态id*/
    private String editOnguadrStateId;
    /**添加页面：在岗状态下拉*/
    private Map<String, String> editOnguadrStateMap = new HashMap<String, String>(0);

    private Map<Integer, TsSimpleCode> onguadrStateMap = new HashMap<>(0);

    private List<TsSimpleCode> onguardStateList;

    /**人员类型*/
    private String editPsnTypeId;
    private Map<String, String> editPsnTypeMap = new HashMap<String, String>(0);
    /** key:codeNo value:实体 */
    private Map<String, TsSimpleCode> psnTypeMap = new HashMap<String, TsSimpleCode>(0);
    private boolean ifIdcAble = Boolean.TRUE;

    private List<String> marrayList = new ArrayList<>();

    /**是否显示放射史：0 否 1 是*/
    private Integer ifShowRad;

    /**添加页面：问诊医生id*/
    private String editChkdocId;
    /**添加页面：问诊医生下拉*/
    private Map<String, String> editChkdocMap = new HashMap<String, String>(0);
    /**主检医师*/
    private String mainChkdocId;
    private Map<String, String> mainChkdocMap = new HashMap<String, String>(0);

    /**项目组合、必检状态Map*/
    Map<String, Integer> combMap = new HashMap<>();
    /**体检子表集合*/
    List<TdTjBhksubClt> bhksubCltList = new ArrayList<>();
    /**特殊项目Map*/
    Map<Integer, TbTjItems> itemsMap = new HashMap<>();
    /**方案标准Id*/
    private String schemeIds;
    /**必检项目集合*/
    private List<TbTjItemSort> mustItemSortList = new ArrayList<>();
    /** 选择查询项目编码 */
    private String searchItemCode;
    /** 选择查询项目名称 */
    private String searchItemName;
    /** 可选择的项目列表 */
    private List<TbZwtjSchemeItems> selectItemList;
    /** 过滤可选择的项目列表 */
    private List<TbZwtjSchemeItems> filterSelectItemList;
    /** 已选择的项目列表 */
    private List<TbZwtjSchemeItems> selectedItemList;

    /** GBZ188的项目列表 */
    private List<TbZwtjSchemeItems> gbz188ItemList;
    /** 可自由选择的项目列表 */
    private List<TbZwtjSchemeItems> freedomItemList;

    /** 选择计算 */
    private Integer selectCount;
    /** 已选择的项目组合Id集合 */
    private StringBuilder itemCmbIds = new StringBuilder();
    /** 选择的项目组合 */
    private TbZwtjSchemeItems selectTbzwItems;
    /**操作的体检子表*/
    private TdTjBhksubClt opBhkSubClt;
    /**体检子表-体检结果*/
    private TdTjBhksubClt passBhkSubClt;

    /**主题结论集合*/
    private List<TsSimpleCode> mhkrstList = new ArrayList<>();
    /**未见异常全选*/
    private boolean ncagSelect = Boolean.FALSE;
    /**复查*/
    private boolean recheckSelect = Boolean.FALSE;
    /**其他疾病或异常*/
    private boolean otherSelect = Boolean.FALSE;
    /**职业禁忌证*/
    private boolean contraindSelect = Boolean.FALSE;
    /**疑似职业病*/
    private boolean supoccdiseSelect = Boolean.FALSE;
    /**疑似职业病-体检结果页面显示*/
    private List<TdTjSupoccdiseCltWeb> supoccdiseCltWebList = new ArrayList<>();
    /**职业禁忌证-体检结果页面显示*/
    private List<TdTjContraindCltWeb> contraindCltWebList = new ArrayList<>();
    /**症状选中Id集合*/
    private List<Integer> symIdList = new ArrayList<>();
    /**标准值配置状态*/
    private Integer stadItemsStatus;
    /**当前用户所在单位Id*/
    private String unitId;
    /***/
    private String newsTitle;

    /**接害工龄是否不可输入*/
    private boolean tchbadrsntimAble = Boolean.FALSE;

    private Integer workTypeId;
    private String workTypeName;
    private boolean ifOtherWork;
    private String otherDesc;

    private Integer resultType;
    private String searchOtherBadName;
    private String searchReCheckBadName;
    private String searchNcagCheckBadName;

    /** 用于判断 是否体检录入进入 */
    private String ifTjlrCrpt;
    private boolean ifOnlyGBZ188 = Boolean.TRUE;

    private boolean ifOtherBadRsn = false;
    /**电测听项目修正规则Map<key: 性别&&项目类型, value: List<TbTjAdoItmRuleSub>>/>*/
    private Map<String, List<TbTjAdoItmRuleSub>> audioItemRuleMap;
    private TbTjCrpt lastCrpt;
    private TbTjCrpt lastEmpCrpt;
    /**是否非复检职业史必填 1 必填 0/空 不必填*/
    private String ifNonRecheckEmhistoryRequired;

    private  List<TsSimpleCode> chooseBadList =  new ArrayList<TsSimpleCode>();
    private  List<TsSimpleCode> reCheckBads = new ArrayList<TsSimpleCode>();
    private  List<TsSimpleCode> otherBads = new ArrayList<TsSimpleCode>();
    private  List<TsSimpleCode> ncagBads = new ArrayList<TsSimpleCode>();

    private TdTjEmhistoryCltListCommBean tdTjEmhistoryCltListBean;
    private TdTjAnamnesisCltListCommBean tdTjAnamnesisCltListBean;
    private TdTjExmsdataCltCommBean tdTjExmsdataCltBean;
    private TdTjSymptomCltCommBean tdTjSymptomCltBean;
    private TdTjChiefDoctorBean tdTjChiefDoctorBean;

    private SystemModuleServiceImpl systemModuleService = SpringContextHolder.getBean(SystemModuleServiceImpl.class);
    private TdTjBhkCltCommServiceImpl service = SpringContextHolder.getBean(TdTjBhkCltCommServiceImpl.class);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
    private TbTjSrvorgCommServiceImpl tbTjSrvorgService = SpringContextHolder.getBean(TbTjSrvorgCommServiceImpl.class);
    private TdTjBadrsnsCltCommServiceImpl tdTjBadrsnsCltService = SpringContextHolder.getBean(TdTjBadrsnsCltCommServiceImpl.class);
    private TdZwPsninfoCommServiceImpl tdZwPsninfoService = SpringContextHolder.getBean(TdZwPsninfoCommServiceImpl.class);
    private TbZwtjMainstdCommServiceImpl tbZwtjMainstdService = SpringContextHolder.getBean(TbZwtjMainstdCommServiceImpl.class);
    private TbTjItemsCommServiceImpl tbTjItemsService = SpringContextHolder.getBean(TbTjItemsCommServiceImpl.class);
    private TbZwtjSchemeItemsCommServiceImpl tbZwtjSchemeItemsService = SpringContextHolder.getBean(TbZwtjSchemeItemsCommServiceImpl.class);
    private TdTjBhkInitCommServiceImpl bhkService = SpringContextHolder.getBean(TdTjBhkInitCommServiceImpl.class);
    private TbTjItemsGjCommServiceImpl itemsGjCommService = (TbTjItemsGjCommServiceImpl) SpringContextHolder.getBean(TbTjItemsGjCommServiceImpl.class);

    /** 职业禁忌证/疑似职业病表格index */
    private Integer contraindIndex;
    /**体检机构标准值配置状态*/
    private TbTjStadItemsStatus tjStadItemsStatus;

    /**首次数据报告是否及时配置*/
    private String setRequiredUploadTime;
    /** 体检录入模块-基本信息体检编号自动生成配置，1自动生成只读，不配置默认或非1保持原有功能*/
    private String ifAutoGenBhkCode;
    private Boolean hasAutoGenBhkCode;
    /** 体检录入模块-当有参数且值为1时：【检查结论】区域页面显示调整为根据每个危害因素选择5大结论选项,不配置默认或非1保持原有功能*/
    private String bhkResultStyle;
    private Boolean hasBhkResultStyle;
    /** 检查结论（按危害因素选取体检结论）危害因素列表*/
    private List<TdTjBadrsnsClt> tdTjBadrsnsCltList = new ArrayList<>();
    /**体检问诊-是否验证既往史必填*/
    private String ifChkJzs;
    /**体检问诊-是否验证家族史必填*/
    private String ifChkAnamnesis;
    /**危害因素是否显示其他文本框*/
    private boolean ifOtherTouchBadRsn = false;
    /**危害因素主要粉尘是否只能选择一种主要粉尘*/
    private String ifMainDust;

    /**
     * 符号
     */
    private List<TsSimpleCode> dataVersionList = new ArrayList<>();
    private Integer dataVersionEqual;
    /** 是否身份证*/
    private Boolean ifIdc;
    /** 体检原始性别*/
    private String tjOriSex;
    /** 基本信息保存下一步前的提示信息*/
    private String saveBasicMessage;
    /**
     * 提示信息结果数组
     * 下标5：姓名不规范
     * 下标6：重卡
     */
    private String[] saveBasicMessageRst;

    private Boolean ifZysTimeRange;
    /** 职业史起止时间段 开始日期的最大日期*/
    private Date employmentHisCltStartDateMax;
    /** 职业史起止时间段 开始日期的最小日期*/
    private Date employmentHisCltStartDateMin;
    /** 职业史起止时间段 结束日期的最小日期*/
    private Date employmentHisCltStopDateMin;
    /** 每次修改时数据库中旧的体检危害因素*/
    private List<TdTjBadrsnsClt> oldBadrsns;
    /** 一级危害因素列表*/
    private List<TsSimpleCode> oneLevelBadrsnCodeList;
    /**缓存 国家接口标准数据 key:体检项目rid   value:实体 */
    private Map<String,List<TbTjItemsGj>>  ItemsGjMap=new HashMap<>();
    /** 体检用人单位是否显示健康企业情况 */
    private Boolean ifShowHealthContrust;
    /** FVC%、FEV1%、FVC1/FVC 结果大于100时弹框提示 */
    private String itemTagDiagTip;
    /**
     * 是否允许上传本年度重点职业病监测周期内的数据
     */
    private boolean reviseBhkInZdzyb;
    /**
     * 控制重点职业病监测周期的年份
     * */
    private List<String> reviseBhkInZdzybYearList;
    /**检查结论 环节 是否是上岗前*/
    private boolean ifBeforeOnPost=Boolean.FALSE;
    /**防护用品佩戴情况*/
    private List<TsSimpleCode> protectEquList;

    /**重卡天数*/
    private Integer repeatDays;
    /**姓名缓存*/
    private String psnNameCache;
    /**体检机构+证件类型+证件号码+体检日期缓存*/
    private String orgAidcAbhkdateCache;

    /**体检项目Map key:项目组合+判定方式 value：判断方式不为空的体检项目*/
    private Map<String, List<String>> cmbItemsMap;
    /**是否校验必检项目*/
    private boolean ifItemMust;

    /**判定方式为同时满足的项目*/
    private List<String> itemCodeSet;


    public TdTjBhkCltListCommBean() {
        this.initParam();
        initItemsGj();
        super.ifSQL = true;
        this.searchAction();
        initStadItemsStatus();
        setRequiredUploadTime=PropertyUtils.getValue("setRequiredUploadTime");
        ifAutoGenBhkCode=PropertyUtils.getValueWithoutException("ifAutoGenBhkCode");
        hasAutoGenBhkCode = "1".equals(ifAutoGenBhkCode);
        bhkResultStyle=PropertyUtils.getValueWithoutException("bhkResultStyle");
        hasBhkResultStyle = "1".equals(bhkResultStyle);
        ifZysTimeRange = "1".equals(commService.findParamValue("IF_ZYS_TIME_RANGE"));
        oneLevelBadrsnCodeList = this.commService.findTsSimpleCodesByTypeNo("5007",true);
        this.ifShowHealthContrust = "1".equals(commService.findParamValue("IF_SHOW_CONSTRUCT_CRPT"));
        this.reviseBhkInZdzyb = "1".equals(this.commService.findParamValue("REVISE_BHK_IN_ZDZYB"));
        String sysParam = this.commService.findParamValue("REVISE_BHK_IN_ZDZYB_YEAR");
        this.ifItemMust = !"1".equals(this.commService.findParamValue("BHK_MISSING_MUST_ITEMS_ARE_ALLOWED"));
        this.reviseBhkInZdzybYearList = new ArrayList<>();
        if (StringUtils.isNotBlank(sysParam)) {
            String[] arr = sysParam.split(",");
            for (String str : arr) {
                this.reviseBhkInZdzybYearList.add(str.trim());
            }
        }
        //初始化重卡天数
        String rDays = PropertyUtils.getValueWithoutException("repeatDays");
        if (!StringUtils.isBlank(rDays)&& !"0".equals(rDays.trim())) {
            try{
                this.repeatDays = Integer.parseInt(rDays);
            }catch (Exception e){
                e.printStackTrace();
                this.repeatDays=null;
            }
        }
    }

    /**
     * @Description : 初始化查询参数
     * @MethodAuthor: anjing
     * @Date : 2019/5/14 17:40
     **/
    private void initParam() {
        /**
         * 地区初始化（缓存，可查询全国地区；点击显示所有启用的省份，当前省份显示在第一个）
         */
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(null, true,null,null);

        this.searchBhkBdate = DateUtils.getMonthFirstDay(new Date()); // 默认：当月1号
        this.searchBhkEdate = new Date();
        this.rhkStateList = new ArrayList<>();
        this.bhkRcdStateList = new ArrayList<>();
        this.protectEquList=new ArrayList<>();

        this.unitId = Global.getUser().getTsUnit().getRid().toString();
        this.newsTitle = "标准值配置";
        this.ifNonRecheckEmhistoryRequired = commService.findParamValue("RHK_EMHISTORY_REQUIR");
        ifChkAnamnesis=commService.findParamValue("IF_CHK_ANAMNESIS");
        ifChkJzs=commService.findParamValue("IF_CHK_JZS");
        this.dataVersionList = this.commService.findLevelSimpleCodesByTypeId("5576");
        for (TsSimpleCode simpleCode : this.dataVersionList) {
            if ("1".equals(simpleCode.getExtendS1())) {
                this.dataVersionEqual = simpleCode.getRid();
            }
        }
        //在岗状态
        this.onguardStateList = this.commService.findLevelSimpleCodesByTypeId("5009");
        if(!CollectionUtils.isEmpty(onguardStateList)){
            for (TsSimpleCode tsSimpleCode : onguardStateList) {
                this.onguadrStateMap.put(tsSimpleCode.getRid(),tsSimpleCode);
            }
        }
        //佩戴情况
        this.protectEquList = this.commService.findLevelSimpleCodesByTypeId("5618");
    }


    /**
     * <p>方法描述：初始化缓存国家接口标准</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-17
     **/
    public void initItemsGj(){
        ItemsGjMap=new HashMap<>();
        List<TbTjItemsGj> itemsGjList= itemsGjCommService.findItemsGjByHql();
        if(!CollectionUtils.isEmpty(itemsGjList)){
            for (TbTjItemsGj tbTjItemsGj : itemsGjList) {
                if(tbTjItemsGj.getType()==2){
                    if(!ItemsGjMap.containsKey(tbTjItemsGj.getFkByItemId().getRid().toString())){
                        List<TbTjItemsGj> newItemsGjList=new ArrayList<>();
                        newItemsGjList.add(tbTjItemsGj);
                        ItemsGjMap.put(tbTjItemsGj.getFkByItemId().getRid().toString(),newItemsGjList);
                    }else{
                        ItemsGjMap.get(tbTjItemsGj.getFkByItemId().getRid().toString()).add(tbTjItemsGj);
                    }
                }
            }
        }
    }


    /**
     * @Description : 执行查询
     * @MethodAuthor: anjing
     * @Date : 2019/5/14 17:41
     **/
    @Override
    public void searchAction() {
        if(DateUtils.isDateAfter(searchBhkBdate, searchBhkEdate)){
            JsfUtil.addErrorMessage("结束日期大于等于体检开始日期！");
            return;
        }
        /*if(StringUtils.isNotBlank(searchIdc)) {
            String checkIDC = IdcUtils.checkIDC(searchIdc);
            if(StringUtils.isNotBlank(checkIDC)) {
                JsfUtil.addErrorMessage(checkIDC);
                return;
            }
        }*/
        super.searchAction();
    }

    /**
     * @Description : 拼接hql语句
     * @MethodAuthor: anjing
     * @Date : 2019/5/14 17:41
     **/
    @Override
    public String[] buildHqls() {
        StringBuilder sb = new StringBuilder();
        sb.append(" FROM TD_TJ_BHK_CLT T ");
        sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ");
        sb.append(" INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID  ");
        sb.append(" LEFT JOIN TB_TJ_CRPT T3 ON T.CRPT_ID = T3.RID ");
        sb.append(" LEFT JOIN TS_ZONE T4 ON T3.ZONE_ID = T4.RID ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T5 ON T.ONGUARD_STATEID = T5.RID ");
        sb.append(" WHERE T.DEL_MARK = 0 ");
        sb.append(" AND T2.RID = " ).append(Global.getUser().getTsUnit().getRid());
        if (StringUtils.isNotBlank(searchZoneCode)) {
            sb.append(" AND T4.ZONE_GB LIKE :zonecode escape '\\\'");
            this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
        }
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.CRPT_NAME LIKE :crptName escape '\\\'");
            this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
        }
        if (StringUtils.isNotBlank(this.searchInstitutionCode)) {
            sb.append(" AND T3.INSTITUTION_CODE LIKE :institutionCode escape '\\\'");
            this.paramMap.put("institutionCode", "%" + StringUtils.convertBFH(this.searchInstitutionCode.trim()) + "%");
        }
        if (StringUtils.isNotBlank(this.searchPersonName)) {
            sb.append(" AND T.PERSON_NAME LIKE :personName escape '\\\'");
            this.paramMap.put("personName", "%" + StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
        }
        if (StringUtils.isNotBlank(this.searchIdc)) {
            sb.append(" AND T.IDC = :idc ");
            //证件号码是否加密
            this.paramMap.put("idc", EncryptFieldUtil.strEncode(StringUtils.convertBFH(this.searchIdc.trim())));
        }
        if (null != this.searchBhkBdate) {
            sb.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBhkBdate, null)).append(" 00:00:00','yyyy-MM-dd hh24:mi:ss')");
        }
        if (null != this.searchBhkEdate) {
            sb.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchBhkEdate, null)).append(" 23:59:59','yyyy-MM-dd hh24:mi:ss')");
        }
        // 报告出具日期
        if (null != this.searchRptSDate) {
            sb.append(" AND T.RPT_PRINT_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchRptSDate, null)).append(" 00:00:00','yyyy-MM-dd hh24:mi:ss')");
        }
        if (null != this.searchRptEDate) {
            sb.append(" AND T.RPT_PRINT_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchRptEDate, null)).append(" 23:59:59','yyyy-MM-dd hh24:mi:ss')");
        }
        if (StringUtils.isNotBlank(this.searchBhkCode)) {
            sb.append(" AND T.BHK_CODE LIKE :bhkCode escape '\\\'");
            this.paramMap.put("bhkCode", "%" + StringUtils.convertBFH(this.searchBhkCode.trim()) + "%");
        }
        if (null != this.rhkStateList && this.rhkStateList.size() == 1) {
            sb.append(" AND T.IF_RHK = ").append(this.rhkStateList.get(0));
        }
        if (null != this.jcTypeList && this.jcTypeList.size() >0) {
            String s = this.jcTypeList.toString().replaceAll("\\[|\\]","").replaceAll(" ","");
            sb.append(" AND T.JC_TYPE IN (").append(s).append(")");
        }
        if (null != this.bhkRcdStateList && this.bhkRcdStateList.size() >0) {
            String s = this.bhkRcdStateList.toString().replaceAll("\\[|\\]","").replaceAll(" ","");
            sb.append(" AND T.BHK_RCD_STATE IN (").append(s).append(")");
        }
        String h2 = "SELECT COUNT(*) " + sb.toString();
        String h1 = " SELECT T.RID, CASE WHEN T4.ZONE_TYPE >2 THEN SUBSTR(T4.FULL_NAME, INSTR(T4.FULL_NAME,'_')+1) ELSE T4.FULL_NAME END, T.CRPT_NAME"
                + ", T.BHK_CODE, T.PERSON_NAME, T.IDC, T.BHK_DATE, T5.CODE_NAME, '' as BADRSNS, T.IF_RHK, T.BHK_RCD_STATE "
                + ",0, T.RPT_PRINT_DATE,T.SUBMIT_CHANGEDATE "
                + sb.append(" ORDER BY T4.ZONE_CODE ASC, T.BHK_DATE DESC, T.BHK_CODE ASC").toString();
        return new String[] { h1, h2 };
    }

    /**处理能否撤销*/
    /**
     * 有没有被关联的录入记录（去除标删）才能撤销*/
    @Override
    public void processData(List<?> list) {
        if (null != list && list.size() > 0) {
            List<Object[]> tempList = (List<Object[]>) list;
            String bhkIds = "";
            for (Object[] obj : tempList) {
                bhkIds +=obj[0].toString() +",";
            }
            bhkIds = bhkIds.substring(0,bhkIds.length()-1);

            List<Object[]> bhkCodeList =service.findRelateBhkList(bhkIds);
            HashSet<String> codeset = new HashSet<>();
            if(bhkCodeList != null && bhkCodeList.size()>0){
                for(Object[] o:bhkCodeList){
                    codeset.add(o[0].toString());
                }
            }

            for (Object[] obj : tempList) {
                if(codeset.contains(obj[0].toString())){
                    obj[11] = 1;
                }
            }
            List<Object[]> badRsnList = service.findBhkBadRsnList(bhkIds);
            if (!CollectionUtils.isEmpty(badRsnList)) {
            	Map<String, String> map = new HashMap<>();
				for (Object[] obj : badRsnList) {
					String key = StringUtils.objectToString(obj[0]);
					if (null==map.get(key)) {
						String value = StringUtils.objectToString(obj[1]);
						map.put(key, value);
					}else {
						String val = map.get(key);
						StringBuffer sb = new StringBuffer();
						sb.append(val).append("，").append(StringUtils.objectToString(obj[1]));
						map.put(key, sb.toString());
					}
				}
				for (Object[] obj : tempList) {
					String key = StringUtils.objectToString(obj[0]);
					if (null!=map.get(key)) {
						obj[8] = map.get(key);
					}
	            }
			}

        }
    }

    /**
     * @Description : 返回体检信息-列表页
     * @MethodAuthor: anjing
     * @Date : 2019/5/14 18:05
     **/
    @Override
    public void backAction() {
        this.view = null;
        setActiveTab(0);
        this.searchAction();
    }

    /**
     * @Description : addActiveTab：0：基本信息:，1：体检问诊，2：体检结果，3：体检结论
     * @MethodAuthor: anjing
     * @Date : 2019/5/14 18:02
     **/
    public void forwardPage(Integer addActiveTab){
        this.addActiveTab = addActiveTab;
    }

    /**
     * @Description : 初始化企业信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 13:28
     **/
    public void selectCrptList(Integer type) {
        Map<String, Object> options = MapUtils.produceDialogMap(1080,1050,520,505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<String>();
        paramList = new ArrayList<String>();
        CrptDialogParamJson json = new CrptDialogParamJson();
        //体检录入进入的 需要判断职工人数与接触职业病危害因素人数是否为空 如果为空需要修改后才能选择
        if(null != ifTjlrCrpt && ifTjlrCrpt.equals("1")){
            json.setSource("1");
        }
        json.setBusType("1");
        json.setIfSearchZoneDefault(Boolean.FALSE);
        json.setIfShowOtherZone(Boolean.TRUE);
        json.setIfShowHealthCrpt(this.ifShowHealthContrust);
        if(type != null){
            if(type == 1){
                json.setAreaValidate(1);
                json.setSearchCrptName(this.tdTjBhkClt.getCrptName());
            }else if(type == 2){
                json.setAreaValidate(2);
                json.setSearchCrptName(this.tdTjBhkClt.getFkByEmpCrptId() == null ? "" : this.tdTjBhkClt.getFkByEmpCrptId().getCrptName());
            }
        }
        paramList.add(JSON.toJSONString(json));
        paramMap.put("jsonParam", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        String useNewCrptSelect = this.commService.findParamValue("USE_NEW_CRPT_SELECT");
        String crptSelectPath = "/webapp/heth/comm/crptCommSelectList";
        if ("1".equals(useNewCrptSelect)) {
            crptSelectPath = "/webapp/heth/comm/crptCommSelectListNew";
        }
        requestContext.openDialog(crptSelectPath, options, paramMap);
    }

    /**
     * @Description : 选择企业后
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 13:29
     **/
    public void onCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            TbTjCrpt tbTjCrpt = (TbTjCrpt) selectedMap.get("selectCrpt");
            this.selectCrptInfo = tbTjCrpt;
            this.tdTjBhkClt.setFkByCrptId(this.selectCrptInfo);
            this.tdTjBhkClt.setCrptName(this.selectCrptInfo.getCrptName());
            this.lastCrpt = this.selectCrptInfo;
            if(!"2".equals(tbTjCrpt.getTsSimpleCodeByIndusTypeId().getExtendS1())){
                //非人力资源
                this.tdTjBhkClt.setFkByEmpCrptId(tbTjCrpt);
                this.lastEmpCrpt = tbTjCrpt;
            }else{
                this.tdTjBhkClt.setFkByEmpCrptId(new TbTjCrpt());
            }
        }
    }
    /**
 	 * <p>方法描述：用工单位选择后</p>
 	 * @MethodAuthor qrr,2021年6月7日,onEmpCrptSelect
     * */
    public void onEmpCrptSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            TbTjCrpt tbTjCrpt = (TbTjCrpt) selectedMap.get("selectCrpt");
            this.tdTjBhkClt.setFkByEmpCrptId(tbTjCrpt);
            this.lastEmpCrpt = tbTjCrpt;
        }
    }


    /**
     * @Description : 是否复检：0 否 1 是
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 14:39
     **/
    public void changeIfRhk() {
        if(this.tdTjBhkClt.getIfRhk() == 0) {
            if(this.tdTjBhkClt.getRid() == null) {
               String bhkcode =  tdTjBhkClt.getBhkCode();
               Date bhkDate =  tdTjBhkClt.getBhkDate();
                this.addInit();
                tdTjBhkClt.setBhkCode(bhkcode);
                tdTjBhkClt.setBhkDate(bhkDate);
                this.clearWorkType();
            } else {
                this.tdTjBhkClt.setFkByLastBhkId(null);
            }
            RequestContext.getCurrentInstance().update("tabView:editTabView:basicInfoForm:basicPanel");
            RequestContext.getCurrentInstance().execute("PF('basicButtonBlock').hide()");
        }
        if(this.tdTjBhkClt.getIfRhk() == 1) {
            RequestContext.getCurrentInstance().execute("PF('basicButtonBlock').show()");
        }
    }

    /**
     * @Description : 初始化可关联体检信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 14:40
     **/
    public void selectBhkCltList() {
        Map<String, Object> options = MapUtils.produceDialogMap(1130,1100,520,505);
        Map<String, List<String>> paramMap = new HashMap<>();
        List<String> paramList = new ArrayList<>();
        paramList.add("1");
        //参数值一样所以用一个list
        paramMap.put("ifAllZone",paramList);
        paramMap.put("ifEmployer",paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("selectBhkCltComm", options, paramMap);
    }

    /**
     * @Description : 选择体检信息后
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 15:47
     **/
    public void onBhkCltSelect(SelectEvent event) {
        if(null == this.tdTjBhkClt.getRid()) {
            String bhkCode = tdTjBhkClt.getBhkCode();
            Date bhkDate = tdTjBhkClt.getBhkDate();
            this.tdTjBhkClt = new TdTjBhkClt();
            tdTjBhkClt.setBhkCode(bhkCode);
            tdTjBhkClt.setBhkDate(bhkDate);
        }
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if(null != selectedMap && selectedMap.size() >0) {
            this.selectBhkClt = (TdTjBhkClt) selectedMap.get("selectBhkClt");
            this.editOnguadrStateId = this.selectBhkClt.getFkByOnguardStateid().getRid().toString();
            if(null != this.selectBhkClt.getFkByCardTypeId()){
                this.editPsnTypeId = this.selectBhkClt.getFkByCardTypeId().getRid().toString();
                TsSimpleCode tsSimpleCode = this.service.find(TsSimpleCode.class, this.selectBhkClt.getFkByCardTypeId().getRid());
                ifIdcAble = !"88".equals(tsSimpleCode.getCodeNo());
                this.tdTjBhkClt.setFkByCardTypeId(tsSimpleCode);
            }
            this.tdTjBhkClt.setFkByEmpCrptId(this.selectBhkClt.getFkByEmpCrptId());//用工单位
            this.tdTjBhkClt.setIfRhk(1);
            this.tdTjBhkClt.setFkByLastBhkId(this.selectBhkClt);
            this.tdTjBhkClt.setFkByBhkorgId(this.selectBhkClt.getFkByBhkorgId());
            this.tdTjBhkClt.setFkByBhkorgId(this.selectBhkClt.getFkByBhkorgId());
            this.tdTjBhkClt.setFkByCrptId(this.selectBhkClt.getFkByCrptId());
            this.tdTjBhkClt.setCrptName(this.selectBhkClt.getCrptName());
            this.tdTjBhkClt.setFkByPersonId(this.selectBhkClt.getFkByPersonId());
            this.tdTjBhkClt.setIdc(this.selectBhkClt.getIdc());
            this.tdTjBhkClt.setPersonName(this.selectBhkClt.getPersonName());
            this.tdTjBhkClt.setSex(this.selectBhkClt.getSex());
            this.tdTjBhkClt.setBrth(this.selectBhkClt.getBrth());
            this.tdTjBhkClt.setAge(this.selectBhkClt.getAge());
            this.tdTjBhkClt.setLnktel(this.selectBhkClt.getLnktel());
            this.tdTjBhkClt.setDpt(this.selectBhkClt.getDpt());
            this.tdTjBhkClt.setWrknum(this.selectBhkClt.getWrknum());
            this.tdTjBhkClt.setWrklnt(this.selectBhkClt.getWrklnt());
            this.tdTjBhkClt.setTchbadrsntim(this.selectBhkClt.getTchbadrsntim());
            this.tdTjBhkClt.setWorkName(this.selectBhkClt.getWorkName());
            this.tdTjBhkClt.setBhkType(this.selectBhkClt.getBhkType());
            this.tdTjBhkClt.setFkByOnguardStateid(this.selectBhkClt.getFkByOnguardStateid());
            //this.tdTjBhkClt.setBadrsns(this.selectBhkClt.getBadrsns());
            this.tdTjBhkClt.setIsxmrd(this.selectBhkClt.getIsxmrd());
            this.tdTjBhkClt.setWrklntmonth(this.selectBhkClt.getWrklntmonth());
            this.tdTjBhkClt.setTchbadrsnmonth(this.selectBhkClt.getTchbadrsnmonth());
            this.tdTjBhkClt.setDelMark(0);
            this.tdTjBhkClt.setBhkRcdState(1);
            this.tdTjBhkClt.setIfNotMod(0);
            this.tdTjBhkClt.setUuid(UUID.randomUUID().toString());
            this.tdTjBhkClt.setPsnType(1);
            this.tdTjBhkClt.setFkByWorkTypeId(selectBhkClt.getFkByWorkTypeId());
            this.tdTjBhkClt.setWorkOther(selectBhkClt.getWorkOther());
            this.tdTjBhkClt.setJcType(selectBhkClt.getJcType());// 监测类型
            //防护用品佩戴情况
            this.tdTjBhkClt.setFkByProtectEquId(new TsSimpleCode());
            if(new Integer("2").equals(selectBhkClt.getJcType())){
                if(this.selectBhkClt.getFkByProtectEquId()!=null && this.selectBhkClt.getFkByProtectEquId().getRid()!=null){
                    this.tdTjBhkClt.setFkByProtectEquId(selectBhkClt.getFkByProtectEquId());
                }
            }else{
                this.tdTjBhkClt.setFkByProtectEquId(null);
            }
            if(null != selectBhkClt.getFkByWorkTypeId() && null !=selectBhkClt.getFkByWorkTypeId().getRid()){
                TsSimpleCode tsSimpleCode = this.service.find(TsSimpleCode.class, selectBhkClt.getFkByWorkTypeId().getRid());

                workTypeName = tsSimpleCode.getCodeName();
                if("1".equals(tsSimpleCode.getExtendS1())){
                    ifOtherWork = true;
                }else{
                    ifOtherWork = false;
                }
            }else{
                workTypeName ="";
                ifOtherWork = false;
            }

            // 设置危害因素
            List<TdTjBadrsnsClt> badrsnsCltList = this.tdTjBadrsnsCltService.selectBadrsnsCltListByBhkId(this.selectBhkClt.getRid());
            StringBuffer buffer = new StringBuffer();
            if(!CollectionUtils.isEmpty(badrsnsCltList)) {
            	this.ifOtherBadRsn = false;
            	this.tdTjBhkClt.getTdTjBadrsnsClts().clear();
                for (TdTjBadrsnsClt tdTjBadrsnsClt : badrsnsCltList) {
                    TsSimpleCode code = tdTjBadrsnsClt.getFkByExamConclusionId();
                    //只有复检结论的危害因素才会被选上
                    if(null != code && null !=code.getExtendS2() && 2==code.getExtendS2()){
                        buffer.append("，").append(tdTjBadrsnsClt.getFkByBadrsnId().getCodeName());
                        TdTjBadrsnsClt badrsnsClt = new TdTjBadrsnsClt();
                        badrsnsClt.setCreateManid(Global.getUser().getRid());
                        badrsnsClt.setCreateDate(new Date());
                        badrsnsClt.setFkByBhkId(this.tdTjBhkClt);
                        badrsnsClt.setFkByBadrsnId(tdTjBadrsnsClt.getFkByBadrsnId());

                        //badrsnsClt.setFkByExamConclusionId(tdTjBadrsnsClt.getFkByExamConclusionId());
                        this.tdTjBhkClt.getTdTjBadrsnsClts().add(badrsnsClt);
                        if ("1".equals(tdTjBadrsnsClt.getFkByBadrsnId().getExtendS4())) {
                        	this.ifOtherBadRsn = true;
						}
                    }

                }
                this.tdTjBhkClt.setOtherBadRsn(selectBhkClt.getOtherBadRsn());
                this.tdTjBhkClt.setBadrsns(buffer.substring(1).toString());
            }

           List<TdTjTchBadrsnsClt> tchBadrsnsCltList=  service.findByHql("select T from TdTjTchBadrsnsClt T where T.fkByBhkId.rid="+this.selectBhkClt.getRid(),TdTjTchBadrsnsClt.class);
           StringBuffer buffer1 = new StringBuffer();
           if(!CollectionUtils.isEmpty(tchBadrsnsCltList)) {
                this.ifOtherTouchBadRsn=false;
                tdTjBhkClt.getTdTjTchBadrsnsClts().clear();
               for (TdTjTchBadrsnsClt tchBadrsnsClt : tchBadrsnsCltList) {
                   buffer1.append("，").append(tchBadrsnsClt.getFkByBadrsnId().getCodeName());
                   TdTjTchBadrsnsClt newBadrsnsClt=new TdTjTchBadrsnsClt();
                   newBadrsnsClt.setFkByBhkId(tdTjBhkClt);
                   newBadrsnsClt.setFkByBadrsnId(tchBadrsnsClt.getFkByBadrsnId());
                   newBadrsnsClt.setCreateDate(new Date());
                   newBadrsnsClt.setCreateManid(Global.getUser().getRid());
                   this.tdTjBhkClt.getTdTjTchBadrsnsClts().add(newBadrsnsClt);
                   if ("1".equals(tchBadrsnsClt.getFkByBadrsnId().getExtendS4())) {
                       this.ifOtherTouchBadRsn = true;
                   }
               }
               this.tdTjBhkClt.setTchOtherBadrsn(selectBhkClt.getTchOtherBadrsn());
               this.tdTjBhkClt.setTouchBadrsns(buffer1.substring(1));
           }

            onguadrStateChangeAction();
            //setPsnTypeId();
            RequestContext.getCurrentInstance().execute("PF('basicButtonBlock').hide()");
        }
    }

    /**
     * @Description : 根据身份证号码查找人员信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 8:35
     **/
    public void selectPersonInfo() {
    	String id = "tabView\\\\:editTabView\\\\:basicInfoForm\\\\:";
        if(StringUtils.isNotBlank(editPsnTypeId)) {

        if(StringUtils.isBlank(this.tdTjBhkClt.getIdc())) {
            Global.markErrorInfo(true, id+"idc", "证件号码不能为空！");
            return;
        }
        // 验证身份证号码合法性
        if(StringUtils.isNotBlank(this.tdTjBhkClt.getIdc())) {
            TsSimpleCode tsSimpleCode = this.service.find(TsSimpleCode.class, Integer.valueOf(this.editPsnTypeId));
            if(null != tsSimpleCode){
                String checkIDC = validateIdc(this.tdTjBhkClt.getIdc(), tsSimpleCode);
                if(StringUtils.isNotBlank(checkIDC)){
                    Global.markErrorInfo(true, id+"idc", checkIDC);
                    return;
                }
            }

        }
        } else {
        	Global.markErrorInfo(true, id+"psnType", "证件类型不能为空！");
            return;
        }

        // 查询人员信息
        List<TdTjPerson> list = this.service.selectPersonInfoByIdc(this.editPsnTypeId,this.tdTjBhkClt.getIdc());
        if(CollectionUtils.isEmpty(list)) {
            JsfUtil.addErrorMessage("未查询到该人员！");
            return;
        } else {
            TdTjPerson tjPerson = list.get(0);
            this.tdTjBhkClt.setFkByPersonId(tjPerson);
            this.tdTjBhkClt.setPersonName(tjPerson.getPersonName());
            this.tdTjBhkClt.setSex(tjPerson.getSex());
            this.tdTjBhkClt.setIdc(tjPerson.getIdc());
            this.tdTjBhkClt.setBrth(tjPerson.getBrth());
            this.tdTjBhkClt.setAge(tjPerson.getAge());
            this.tdTjBhkClt.setIsxmrd(tjPerson.getIsxmrd());
            this.tdTjBhkClt.setWorkName(tjPerson.getJob());
            this.tdTjBhkClt.setLnktel(tjPerson.getTel());
            this.tdTjBhkClt.setDpt(tjPerson.getDept());
            this.tdTjBhkClt.setWrknum(tjPerson.getJobcod());
            RequestContext.getCurrentInstance().update("basicPanel");
            JsfUtil.addSuccessMessage("查询成功！");
        }
    }

    /**
     * @Description : 初始化危害因素
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 8:36
     **/
    public void selectBadtree() {
    	Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>(1);
        paramList = new ArrayList<String>(1);
        //标题
        paramList.add("危害因素");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>(1);
        //码表类型
        paramList.add("5007");
        paramMap.put("typeNo", paramList);
        //已选择的rid
        String rids = "";
        if("1".equals(ifMainDust)) {
            List<TdTjBadrsnsClt> list = this.tdTjBhkClt.getTdTjBadrsnsClts();
            for (TdTjBadrsnsClt badrsnsClt : list) {
                rids += "," + badrsnsClt.getFkByBadrsnId().getRid();
            }
        }else{
            List<TdTjTchBadrsnsClt> list = this.tdTjBhkClt.getTdTjTchBadrsnsClts();
            for (TdTjTchBadrsnsClt badrsnsClt : list) {
                rids += "," + badrsnsClt.getFkByBadrsnId().getRid();
            }
        }
        paramList = new ArrayList<String>(1);
        paramList.add(rids.replaceFirst(",", ""));
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<String>();
        //查询条件是否显示大类
        paramList.add("true");
        paramMap.put("ifShowFirstCode", paramList);
        paramList = new ArrayList<String>();
        //为1时,选择时只选择最末级
        paramList.add("1");
        paramMap.put("selLast", paramList);
        //主要粉尘是否只能选择一种
        if("1".equals(ifMainDust)){
            paramList = new ArrayList<String>();
            paramList.add(this.ifMainDust);
            paramMap.put("ifMainDust", paramList);
        }
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList.xhtml", options, paramMap);
    }

    /**
     * @Description : 选择危害因素后
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 8:37
     **/
    public void onBadtreeSelect(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> badfactorList = (List<TsSimpleCode>) selectedMap.get("selectPros");
            StringBuffer buffer = new StringBuffer();
            if("1".equals(this.ifMainDust)){
                this.tdTjBhkClt.getTdTjBadrsnsClts().clear();
                this.ifOtherBadRsn = false;
            }else{
                this.tdTjBhkClt.getTdTjTchBadrsnsClts().clear();
                this.ifOtherTouchBadRsn=false;
            }
            for(TsSimpleCode tsSimpleCode : badfactorList) {
                buffer.append("，").append(tsSimpleCode.getCodeName());
                if("1".equals(this.ifMainDust)){
                    TdTjBadrsnsClt tdTjBadrsnsClt = new TdTjBadrsnsClt();
                    tdTjBadrsnsClt.setCreateManid(Global.getUser().getRid());
                    tdTjBadrsnsClt.setCreateDate(new Date());
                    tdTjBadrsnsClt.setFkByBhkId(this.tdTjBhkClt);
                    tdTjBadrsnsClt.setFkByBadrsnId(tsSimpleCode);
                    this.tdTjBhkClt.getTdTjBadrsnsClts().add(tdTjBadrsnsClt);
                }else{
                    TdTjTchBadrsnsClt tdTjTchBadrsnsClt=new TdTjTchBadrsnsClt();
                    tdTjTchBadrsnsClt.setCreateManid(Global.getUser().getRid());
                    tdTjTchBadrsnsClt.setCreateDate(new Date());
                    tdTjTchBadrsnsClt.setFkByBhkId(this.tdTjBhkClt);
                    tdTjTchBadrsnsClt.setFkByBadrsnId(tsSimpleCode);
                    this.tdTjBhkClt.getTdTjTchBadrsnsClts().add(tdTjTchBadrsnsClt);
                }
                if ("1".equals(tsSimpleCode.getExtendS4())) {//其他
                    if("1".equals(this.ifMainDust)){
                        this.ifOtherBadRsn = true;
                    }else{
                        this.ifOtherTouchBadRsn=true;
                    }
				}
            }
            //危害因素发生变化时保留数据库中的体检结论
            if("1".equals(this.ifMainDust)) {
                //源体检危害因素结论
                List<TdTjBadrsnsClt> oriBadrsn = this.service.selectBadrsnsCltListByBhkId(tdTjBhkClt.getRid());
                //体检危害因素发生变化，保留未修改的体检结论
                for (TdTjBadrsnsClt rsn : tdTjBhkClt.getTdTjBadrsnsClts()) {
                    for (TdTjBadrsnsClt badrsn : oriBadrsn) {
                        if (rsn.getFkByBadrsnId().getRid().equals(badrsn.getFkByBadrsnId().getRid())) {
                            //同危害因素，体检结论保留
                            rsn.setFkByExamConclusionId(badrsn.getFkByExamConclusionId());
                            rsn.setQtjbName(badrsn.getQtjbName());
                            break;
                        }
                    }
                }
            }
            if(!"1".equals(this.ifMainDust)&&!this.ifOtherTouchBadRsn){
                tdTjBhkClt.setTchOtherBadrsn(null);
            }

            if("1".equals(this.ifMainDust)){
                this.tdTjBhkClt.setBadrsns(buffer.deleteCharAt(0).toString());
            }else{
                this.tdTjBhkClt.setTouchBadrsns(buffer.deleteCharAt(0).toString());
            }
            if("1".equals(this.ifMainDust)){
                RequestContext.getCurrentInstance().update("tabView:editTabView:basicInfoForm:badRsnPanel");
            }else{
                RequestContext.getCurrentInstance().update("tabView:editTabView:basicInfoForm:touchBadRsnPanel");
            }
        }
    }

    /**
     * @Description : 获取在岗状态集合
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 13:26
     **/
    private Map<String, String> getOnguadrStateMap() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        if(CollectionUtils.isEmpty(this.onguardStateList)){
            return map;
        }
        for(TsSimpleCode entity : this.onguardStateList) {
            map.put(entity.getCodeName(), entity.getRid().toString());
            onguadrStateMap.put(entity.getRid(),entity);
        }
        return map;
    }

    private Map<String, String> getPsnTypeMap() {
        Map<String, String> map = new LinkedHashMap<String, String>();
        this.psnTypeMap=new HashMap<>();
        List<TsSimpleCode> tsSimpleCodeList = this.commService.findLevelSimpleCodesByTypeId("5503");
        for(TsSimpleCode entity : tsSimpleCodeList) {
            map.put(entity.getCodeName(), entity.getRid().toString());
            this.psnTypeMap.put(entity.getRid().toString(),entity);
        }
        return map;
    }

    /**
     * @Description : 根本身份证号自动填写出生日期、性别、年龄
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 10:28
     **/
    public void findFlowByIdc() {
        if(ifIdcAble) {
            if (StringUtils.isNotBlank(this.tdTjBhkClt.getIdc())) {
                String checkIDC = IdcUtils.checkIDC(this.tdTjBhkClt.getIdc());
                if (StringUtils.isBlank(checkIDC)) {
                    if (Integer.valueOf(tdTjBhkClt.getIdc().substring(
                            tdTjBhkClt.getIdc().length() - 2,
                            tdTjBhkClt.getIdc().length() - 1)) % 2 == 0) {
                        tdTjBhkClt.setSex("女");
                    } else {
                        tdTjBhkClt.setSex("男");
                    }// 性别
                    Date birthday = this.calBirthday(tdTjBhkClt.getIdc());
                    Integer age = this.calAge(tdTjBhkClt.getIdc());
                    tdTjBhkClt.setBrth(birthday);
                    tdTjBhkClt.setAge(age);
                }

            }
        }
    }

    /**
     * @Description : 出生日期的计算
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 10:28
     **/
    public Date calBirthday(String idc){
        Date brithday = null;
        // 日期转换格式
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        try {
            // 18位身份证号
            if (idc.length() == 18) {
                brithday = df.parse(idc.substring(6, 14));
            } else {
                // 15位身份证号前加上"19"
                brithday = df.parse(new StringBuilder("19").append(idc.substring(6, 12)).toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return brithday;
    }

    /**
     * @Description : 年龄的计算
     * @MethodAuthor: anjing
     * @Date : 2019/5/22 15:16
     **/
    public Integer calAge(String idc) {
        String year = "";
        String date = "";
        int age = 0;
        if (idc.length() == 18) {
            year = idc.substring(6, 10);
            date = idc.substring(6, 14);
        } else {
            year = new StringBuilder("19").append(idc.substring(6, 12)).toString();
            date = new StringBuilder("19").append(idc.substring(6, 14)).toString();
        }
        try {
            Date today = new Date();
            int yearNow = DateUtils.getYear(today);
            Date birthDate = DateUtils.parseDate(date, "yyyyMMdd");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(birthDate);
            calendar.set(Calendar.YEAR, DateUtils.getYearInt());
            long millis = calendar.getTimeInMillis();
            long todayMillis = DateUtils.getDateOnly(today).getTime();
            age = yearNow - Integer.valueOf(year);

            if (todayMillis - millis < 0) {
                age -= 1;
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return age;
    }

    /******************************************** 开始验证 *********************************************/
    /**
    * <p>Description：校验姓名规范和重卡 </p>
    * <p>Author： yzz 2025-02-13 </p>
    */
    public boolean vertyBasicAfter() {
        // 初始化提示信息的序号
        String[] list = saveBasicMessageRst;
        int count = 1;
        for (String s : list) {
            if ("1".equals(s)) {
                count++;
            }
        }
        // 初始化姓名
        String psnNameTemp = this.tdTjBhkClt.getPersonName();
        String orgAidcAbhkdateTemp = this.tdTjBhkClt.getFkByBhkorgId().getRid() + "&" + this.editPsnTypeId + "&" + this.tdTjBhkClt.getIdc() + "&" + DateUtils.formatDate(this.tdTjBhkClt.getBhkDate(), "yyyy-MM-dd");

        boolean flag = true;
        // 证件类型是身份证时，劳动者姓名发生变化  同时没通过校验，给提示信息
        if (psnTypeMap.containsKey(this.editPsnTypeId) && "01".equals(psnTypeMap.get(this.editPsnTypeId).getCodeNo()) && !psnNameTemp.equals(this.psnNameCache) && !StringUtils.isValidName(this.tdTjBhkClt.getPersonName())) {
            saveBasicMessageRst[4] = "1";
            flag = false;
            saveBasicMessage += (count > 1 ? "<br>" : "") + (count++) + "、劳动者姓名包含非中文字符，请确认是否继续？";
        }
        // 初检 关键信息点发生变化  同时数据库有再重卡天数内的记录，给提示信息
        List<String> bhkCodes = new ArrayList<>();
        if (this.repeatDays != null) {
            bhkCodes = vertyRepeatCard();
        }
        if (new Integer("0").equals(this.tdTjBhkClt.getIfRhk()) && !orgAidcAbhkdateTemp.equals(this.orgAidcAbhkdateCache) && !CollectionUtils.isEmpty(bhkCodes)) {
            saveBasicMessageRst[5] = "1";
            flag = false;
            saveBasicMessage += (count > 1 ? "<br>" : "") + (count++) + "、该劳动者已存在体检日期在" + this.repeatDays + "天内的记录（体检编号：" + StringUtils.list2string(bhkCodes, "、") + "），是否继续录入，请确认！";
        }
        return flag;
    }

    /**
     * <p>Description：验证重卡 </p>
     * <p>Author： yzz 2025-02-13 </p>
     */
    private List<String> vertyRepeatCard() {
        return this.bhkService.findRepeatCards(this.tdTjBhkClt.getFkByBhkorgId().getRid(), Integer.valueOf(this.editPsnTypeId), EncryptFieldUtil.strEncode(this.tdTjBhkClt.getIdc()), DateUtils.formatDate(this.tdTjBhkClt.getBhkDate(), "yyyy-MM-dd"),this.tdTjBhkClt.getRid(), this.repeatDays);
    }


    /**
     * @Description : 保存时验证-基本信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 10:34
     **/
    private boolean vertyBasicInfoBySave() {
    	String id = "tabView\\\\:editTabView\\\\:basicInfoForm\\\\:";
        boolean flag = true;
        // 体检编号本单位唯一
        if(StringUtils.isNotBlank(this.tdTjBhkClt.getBhkCode())) {
            List<TdTjBhkClt> bhkCltList = this.service.selectBhkCltListByBhkCode(this.tdTjBhkClt.getBhkCode());
            if((!CollectionUtils.isEmpty(bhkCltList) && tdTjBhkClt.getRid() == null) || (!CollectionUtils.isEmpty(bhkCltList) && tdTjBhkClt.getRid() != null && !tdTjBhkClt.getRid().equals(bhkCltList.get(0).getRid()))) {
                Global.markErrorInfo(true, id+"bhkCode", "体检编号不允许重复！");
                flag = false;
            }
        }
        if(null == this.tdTjBhkClt.getBhkDate()) {
        	Global.markErrorInfo(true, id+"bhkDate_input", "体检日期不能为空！");
            flag = false;
        }
        // 验证体检日期
        if(null != this.tdTjBhkClt.getBhkDate() && this.tdTjBhkClt.getIfRhk() == 1) {
        	TdTjBhkClt lastBhk = this.tdTjBhkClt.getFkByLastBhkId();//上传体检记录
        	if(null!=lastBhk && null != lastBhk.getBhkDate() && this.tdTjBhkClt.getBhkDate().before(lastBhk.getBhkDate())) {
        		Global.markErrorInfo(true, id+"bhkDate_input", "复检日期不能小于上次体检日期！");
        		flag = false;
        	}
        }
        if (this.tdTjBhkClt.getIfRhk() == 1) {
			if (null == this.tdTjBhkClt.getFkByLastBhkId()
					|| null == this.tdTjBhkClt.getFkByLastBhkId()) {
				Global.markErrorInfo(true, id+"ifRhk", "请关联上次体检记录！");
        		flag = false;
			}
		}
        if(null == this.tdTjBhkClt.getFkByCrptId()) {
        	Global.markErrorInfo(true, id+"crptName", "用人单位名称不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.tdTjBhkClt.getPersonName())) {
        	Global.markErrorInfo(true, id+"personName", "姓名不能为空！");
            flag = false;
        }

        if(ifIdcAble) {
            String tmpIdc = this.tdTjBhkClt.getIdc();
            if(StringUtils.isBlank(tmpIdc)) {
            	Global.markErrorInfo(true, id+"idc", "当选择证件类型为暂未获取时，证件号码才能为空！");
                flag = false;
            }else {
                TsSimpleCode tsSimpleCode = this.service.find(TsSimpleCode.class, Integer.valueOf(this.editPsnTypeId));
                if(null != tsSimpleCode){
                    String checkIDC = validateIdc(tmpIdc, tsSimpleCode);
                    if(StringUtils.isNotBlank(checkIDC)) {
                        Global.markErrorInfo(true, id+"idc", checkIDC);
                        flag = false;
                    }
                }

            }
        }

        // 验证联系电话合法性
        if(!StringUtils.isBlank(this.tdTjBhkClt.getLnktel())) {
            if(!StringUtils.vertyPhone(this.tdTjBhkClt.getLnktel())){
            	Global.markErrorInfo(true, id+"lnktel", "联系电话格式错误！");
                flag = false;
            }
        }

        if(ifOtherWork && StringUtils.isBlank(this.tdTjBhkClt.getWorkOther())){
            Global.markErrorInfo(true, id+"ifOtherWork", "工种选择其他时，工种其他必须填写！");
            flag = false;
        }

        if(ifOtherBadRsn && StringUtils.isBlank(this.tdTjBhkClt.getOtherBadRsn())){
            Global.markErrorInfo(true, id+"otherBadRsn", "其他体检危害因素不能为空！");
            flag = false;
        }
        if(ifOtherTouchBadRsn && StringUtils.isBlank(this.tdTjBhkClt.getTchOtherBadrsn())){
            Global.markErrorInfo(true, id+"tchOtherBadrsn", "其他接触危害因素不能为空！");
            flag = false;
        }

        // 总工龄、接害工龄月份不能超过12
        if(null != this.tdTjBhkClt.getWrklntmonth()) {
            if(this.tdTjBhkClt.getWrklntmonth() > 11) {
            	Global.markErrorInfo(true, id+"wrklntmonth", "总工龄月份必须小于等于11！");
                flag = false;
            }
        }

        if (!this.tchbadrsntimAble && null != this.tdTjBhkClt.getTchbadrsnmonth() && this.tdTjBhkClt.getTchbadrsnmonth() > 11) {
            Global.markErrorInfo(true, id + "tchbadrsnmonth", "接害工龄月份必须小于等于11！");
            flag = false;
        }

        // 接害工龄年数≤总工龄年数
        if(null != this.tdTjBhkClt.getWrklnt() || null != this.tdTjBhkClt.getWrklntmonth()) {
            BigDecimal totalWrklnt, totalTchbadrsntim;
            if (null != this.tdTjBhkClt.getWrklnt()) {
                totalWrklnt = this.tdTjBhkClt.getWrklnt().multiply(new BigDecimal(12)).add((this.tdTjBhkClt.getWrklntmonth() == null ? new BigDecimal(0) : new BigDecimal(this.tdTjBhkClt.getWrklntmonth())));
            } else {
                totalWrklnt = this.tdTjBhkClt.getWrklntmonth() == null ? new BigDecimal(0) : new BigDecimal(this.tdTjBhkClt.getWrklntmonth());
            }
            if (null != this.tdTjBhkClt.getTchbadrsntim()) {
                totalTchbadrsntim = this.tdTjBhkClt.getTchbadrsntim().multiply(new BigDecimal(12)).add((this.tdTjBhkClt.getTchbadrsnmonth() == null ? new BigDecimal(0) : new BigDecimal(this.tdTjBhkClt.getTchbadrsnmonth())));
            } else {
                totalTchbadrsntim = this.tdTjBhkClt.getTchbadrsnmonth() == null ? new BigDecimal(0) : new BigDecimal(this.tdTjBhkClt.getTchbadrsnmonth());
            }

            if (totalTchbadrsntim.compareTo(totalWrklnt) > 0) {
            	Global.markErrorInfo(true, id+"wrklnt"+","+id+"wrklntmonth"+","+id+"tchbadrsntim"+","+id+"tchbadrsnmonth", "接害工龄应小于等于总工龄！");
                flag = false;
            }
        }
        if(null != this.tdTjBhkClt.getWrklnt()&&this.tdTjBhkClt.getWrklnt().intValue()>80){
            Global.markErrorInfo(true, id+"wrklnt","总工龄年数不能大于80！");
            flag = false;
        }
        if(!this.tchbadrsntimAble && null != this.tdTjBhkClt.getTchbadrsntim()&&this.tdTjBhkClt.getTchbadrsntim().intValue()>80){
            Global.markErrorInfo(true, id+"tchbadrsntim","接害工龄年数不能大于80！");
            flag = false;
        }
        if(null == this.tdTjBhkClt.getJcType()){
            Global.markErrorInfo(true, id+"jctype", "监测类型必须选择！");
            flag = false;
        }
        if (!veryEmpCrpt()) {
        	flag = false;
		}
        return flag;
    }

    public String validateIdc(String tmpIdc, TsSimpleCode tsSimpleCode){
        String checkIDC = null;
        boolean flag = false;
        if(null != tsSimpleCode){
            if(tsSimpleCode.getCodeNo().equals("01")){
                // 验证身份证号合法性
                checkIDC = IdcUtils.checkIDC(tmpIdc);
                if(null != checkIDC){
                    flag = true;
                }
            }else if(tsSimpleCode.getCodeNo().equals("02") && !IdcUtils.isHousehold(tmpIdc)){
                // 验证户口簿
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("03") && !IdcUtils.isPassPort(tmpIdc)){
                // 验证护照
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("04") && !IdcUtils.isMilitary(tmpIdc)){
                // 验证军官证
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("06") && !IdcUtils.isHmCard(tmpIdc)){
                // 验证港澳通行证
                flag = true;
            }else if(tsSimpleCode.getCodeNo().equals("07") && !IdcUtils.isTaiWanEntry(tmpIdc)){
                // 验证台胞证
                flag = true;
            }
        }
        if(flag){
            checkIDC = "证件号码格式错误！";
        }
        return checkIDC;
    }

    /**
     * @Description : 下一步时验证-基本信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 14:42
     **/
    private boolean vertyBasicInfoByNext() {
        boolean flag = true;
        String id = "tabView\\\\:editTabView\\\\:basicInfoForm\\\\:";
        // 验证必填项
        if(!hasAutoGenBhkCode && StringUtils.isBlank(this.tdTjBhkClt.getBhkCode())) {
            Global.markErrorInfo(true, id+"bhkCode", "体检编号不能为空！");
            flag = false;
        }
        // 体检编号本单位唯一
        List<TdTjBhkClt> bhkCltList = this.service.selectBhkCltListByBhkCode(this.tdTjBhkClt.getBhkCode());
        if((!CollectionUtils.isEmpty(bhkCltList) && tdTjBhkClt.getRid() == null) || (!CollectionUtils.isEmpty(bhkCltList) && tdTjBhkClt.getRid() != null && !tdTjBhkClt.getRid().equals(bhkCltList.get(0).getRid()))) {
            Global.markErrorInfo(true, id+"bhkCode", "体检编号不允许重复！");
            flag = false;
        }
        if(null == this.tdTjBhkClt.getBhkDate()) {
        	Global.markErrorInfo(true, id+"bhkDate_input", "体检日期不能为空！");
            flag = false;
        }
        if(null == this.tdTjBhkClt.getIfRhk()) {
        	Global.markErrorInfo(true, id+"ifRhk", "是否复检不能为空！");
            flag = false;
        }
        // 验证体检日期
        if(null != this.tdTjBhkClt.getBhkDate() && this.tdTjBhkClt.getIfRhk() == 1) {
        	TdTjBhkClt lastBhk = this.tdTjBhkClt.getFkByLastBhkId();//上传体检记录
        	if (null!=lastBhk && null!=lastBhk.getBhkDate() && this.tdTjBhkClt.getBhkDate().before(lastBhk.getBhkDate())) {
        		Global.markErrorInfo(true, id+"bhkDate_input", "复检日期不能小于上次体检日期！");
        		flag = false;
        	}
        }
        if (this.tdTjBhkClt.getIfRhk() == 1) {
			if (null == this.tdTjBhkClt.getFkByLastBhkId()
					|| null == this.tdTjBhkClt.getFkByLastBhkId().getRid()) {
				Global.markErrorInfo(true, id+"ifRhk", "请关联上次体检记录！");
        		flag = false;
			}
		}
        if(null == this.tdTjBhkClt.getFkByCrptId()) {
        	Global.markErrorInfo(true, id+"crptName", "用人单位名称不能为空！");
            flag = false;
        }
        if(StringUtils.isBlank(this.tdTjBhkClt.getPersonName())) {
        	Global.markErrorInfo(true, id+"personName", "姓名不能为空！");
            flag = false;
        }

        // 证件类型不能为空
        if(StringUtils.isBlank(this.editPsnTypeId)) {
        	Global.markErrorInfo(true, id+"psnType", "证件类型不能为空！");
            flag = false;
        }

        if(ifIdcAble) {
            String tmpIdc = this.tdTjBhkClt.getIdc();
            if(StringUtils.isBlank(tmpIdc)) {
                Global.markErrorInfo(true, id+"idc", "当选择证件类型为暂未获取时，证件号码才能为空！");
                flag = false;
            }else {
                TsSimpleCode tsSimpleCode = this.service.find(TsSimpleCode.class, Integer.valueOf(this.editPsnTypeId));
                String checkIDC = null;
                if(null != tsSimpleCode){
                    checkIDC = validateIdc(tmpIdc,tsSimpleCode);
                }

                if(StringUtils.isNotBlank(checkIDC)) {
                    Global.markErrorInfo(true, id+"idc", checkIDC);
                    flag = false;
                }

            }
        }

        if(StringUtils.isBlank(this.tdTjBhkClt.getSex())) {
        	Global.markErrorInfo(true, id+"sex", "性别不能为空！");
            flag = false;
        }
        if(null == this.tdTjBhkClt.getBrth()) {
        	Global.markErrorInfo(true, id+"brth_input", "出生日期不能为空！");
            flag = false;
        }
        // 验证联系电话合法性
        if(StringUtils.isNotBlank(this.tdTjBhkClt.getLnktel())) {
            if(!StringUtils.vertyPhone(this.tdTjBhkClt.getLnktel())){
            	Global.markErrorInfo(true, id+"lnktel", "联系电话格式错误！");
                flag = false;
            }
        }else {
            Global.markErrorInfo(true, id+"lnktel", "联系电话不能为空！");
            flag = false;
        }

        if(StringUtils.isBlank(this.editOnguadrStateId)) {
        	Global.markErrorInfo(true, id+"editOnguadrStateId", "在岗状态不能为空！");
            flag = false;
        }
        //体检危害因素
        if(StringUtils.isBlank(this.tdTjBhkClt.getBadrsns())) {
        	Global.markErrorInfo(true, id+"badrsns", "体检危害因素不能为空！");
            flag = false;
        }
        if (this.ifOtherBadRsn) {
			if (StringUtils.isBlank(this.tdTjBhkClt.getOtherBadRsn())) {
				Global.markErrorInfo(true, id+"otherBadRsn", "其他体检危害因素不能为空！");
	            flag = false;
			}
		}
        //接触危害因素
        if(StringUtils.isBlank(this.tdTjBhkClt.getTouchBadrsns())) {
            Global.markErrorInfo(true, id+"touchBadrsns", "接触危害因素不能为空！");
            flag = false;
        }
        if (this.ifOtherTouchBadRsn) {
            if (StringUtils.isBlank(this.tdTjBhkClt.getTchOtherBadrsn())) {
                Global.markErrorInfo(true, id+"tchOtherBadrsn", "其他接触危害因素不能为空！");
                flag = false;
            }
        }

        if(null==this.tdTjBhkClt.getFkByWorkTypeId()){
            Global.markErrorInfo(true, id+"workTypeName", "工种不能为空！");
            flag = false;
        }

        // 总工龄、接害工龄月份不能超过12
        if(ifOtherWork && StringUtils.isBlank(this.tdTjBhkClt.getWorkOther())){
            Global.markErrorInfo(true, id+"ifOtherWork", "工种选择其他时，工种其他必须填写！");
            flag = false;
        }

        // 总工龄、接害工龄月份不能超过12
        if(null != this.tdTjBhkClt.getWrklnt() && null == this.tdTjBhkClt.getWrklntmonth()){
            Global.markErrorInfo(true, id+"wrklntmonth", "总工龄年份存在时，总工龄月份必须填写！");
            flag = false;
        }
        if(null == this.tdTjBhkClt.getWrklnt() && null != this.tdTjBhkClt.getWrklntmonth()){
            Global.markErrorInfo(true, id+"wrklnt", "总工龄月份存在时，总工龄年份必须填写！");
            flag = false;
        }
        if(null != this.tdTjBhkClt.getWrklnt()&&this.tdTjBhkClt.getWrklnt().intValue()>80){
            Global.markErrorInfo(true, id+"wrklnt","总工龄年数不能大于80！");
            flag = false;
        }
        if(null != this.tdTjBhkClt.getWrklntmonth()) {
            if(this.tdTjBhkClt.getWrklntmonth() > 11) {
                Global.markErrorInfo(true, id+"wrklntmonth", "总工龄月份必须小于等于11！");
                flag = false;
            }
        }

        if(!tchbadrsntimAble && null == this.tdTjBhkClt.getTchbadrsntim()){
            Global.markErrorInfo(true, id+"tchbadrsntim", "接害工龄年份必填！");
            flag = false;
        }
        if(!this.tchbadrsntimAble &&null != this.tdTjBhkClt.getTchbadrsntim()&&this.tdTjBhkClt.getTchbadrsntim().intValue()>80){
            Global.markErrorInfo(true, id+"tchbadrsntim","接害工龄年数不能大于80！");
            flag = false;
        }
        if(!tchbadrsntimAble && null == this.tdTjBhkClt.getTchbadrsnmonth()){
            Global.markErrorInfo(true, id+"tchbadrsnmonth", "接害工龄月份必填！");
            flag = false;
        }
        if (!this.tchbadrsntimAble && null != this.tdTjBhkClt.getTchbadrsnmonth() && this.tdTjBhkClt.getTchbadrsnmonth() > 11) {
            Global.markErrorInfo(true, id+"tchbadrsnmonth", "接害工龄月份必须小于等于11！");
            flag = false;
        }
        //当在岗状态非“上岗前”时，总月份大于0
        if (!tchbadrsntimAble && this.tdTjBhkClt.getTchbadrsntim() != null && this.tdTjBhkClt.getTchbadrsnmonth() != null
                && StringUtils.isNotBlank(this.editOnguadrStateId)
                && this.onguadrStateMap.containsKey(Integer.parseInt(this.editOnguadrStateId))
                && !new Integer("1").equals(this.onguadrStateMap.get(Integer.parseInt(this.editOnguadrStateId)).getExtendS2())) {
            if (this.tdTjBhkClt.getTchbadrsntim().compareTo(new BigDecimal(0)) == 0 && new Integer("0").equals(this.tdTjBhkClt.getTchbadrsnmonth())) {
                Global.markErrorInfo(true, id + "tchbadrsnmonth", "接害工龄年、月不能同时为0！");
                String tchbadrsntimRid=id+"tchbadrsntim";
                RequestContext.getCurrentInstance().execute("SYSTEM.markErrorInfo('"+tchbadrsntimRid+"')");
                flag = false;
            }
        }

        // 接害工龄年数≤总工龄年数
        if(!tchbadrsntimAble && null != this.tdTjBhkClt.getWrklnt() || null != this.tdTjBhkClt.getWrklntmonth()) {
            BigDecimal totalWrklnt, totalTchbadrsntim;
            if (null != this.tdTjBhkClt.getWrklnt()) {
                totalWrklnt = this.tdTjBhkClt.getWrklnt().multiply(new BigDecimal(12)).add((this.tdTjBhkClt.getWrklntmonth() == null ? new BigDecimal(0) : new BigDecimal(this.tdTjBhkClt.getWrklntmonth())));
            } else {
                totalWrklnt = this.tdTjBhkClt.getWrklntmonth() == null ? new BigDecimal(0) : new BigDecimal(this.tdTjBhkClt.getWrklntmonth());
            }
            if (null != this.tdTjBhkClt.getTchbadrsntim()) {
                totalTchbadrsntim = this.tdTjBhkClt.getTchbadrsntim().multiply(new BigDecimal(12)).add((this.tdTjBhkClt.getTchbadrsnmonth() == null ? new BigDecimal(0) : new BigDecimal(this.tdTjBhkClt.getTchbadrsnmonth())));
            } else {
                totalTchbadrsntim = this.tdTjBhkClt.getTchbadrsnmonth() == null ? new BigDecimal(0) : new BigDecimal(this.tdTjBhkClt.getTchbadrsnmonth());
            }

            if (totalTchbadrsntim.compareTo(totalWrklnt) > 0) {
            	Global.markErrorInfo(true, id+"wrklnt"+","+id+"wrklntmonth"+","+id+"tchbadrsntim"+","+id+"tchbadrsnmonth", "接害工龄应小于等于总工龄！");
                flag = false;
            }
        }
        if(null == this.tdTjBhkClt.getJcType()){
            Global.markErrorInfo(true, id+"jctype", "监测类型必须选择！");
            flag = false;
        }
        boolean isProtectEqu = new Integer("2").equals(this.tdTjBhkClt.getJcType()) && (this.tdTjBhkClt.getFkByProtectEquId()==null || this.tdTjBhkClt.getFkByProtectEquId().getRid()==null);
        if(isProtectEqu){
            Global.markErrorInfo(true, id+"protectEquId", "请选择防护用品佩戴情况！");
            flag = false;
        }
        if (!veryEmpCrpt()) {
        	flag = false;
		}
        return flag;
    }
    /**
 	 * <p>方法描述：验证用工单位，false验证不通过</p>
 	 * @MethodAuthor qrr,2021年6月4日,veryEmpCrpt
     * */
    private boolean veryEmpCrpt() {
        boolean flag = true;
    	String id = "tabView\\\\:editTabView\\\\:basicInfoForm\\\\:";
    	TbTjCrpt crpt = this.tdTjBhkClt.getFkByCrptId();
    	if (null!=crpt && null!=crpt.getRid()) {
    		//保存时重新查询企业信息，解决弹出框直接修改企业信息不再重新选择
        	TbTjCrpt find = service.find(TbTjCrpt.class, crpt.getRid());
        	this.tdTjBhkClt.setFkByCrptId(find);
        	this.tdTjBhkClt.setCrptName(find.getCrptName());
        	this.lastCrpt = find;
        	crpt = find;
    	}
    	TbTjCrpt empCrptId = this.tdTjBhkClt.getFkByEmpCrptId();
    	if (null!=empCrptId && null!=empCrptId.getRid()) {
    		TbTjCrpt findEmpCrpt = service.find(TbTjCrpt.class, empCrptId.getRid());
        	this.tdTjBhkClt.setFkByEmpCrptId(findEmpCrpt);
            this.lastEmpCrpt = findEmpCrpt;
        	empCrptId = findEmpCrpt;
    	}
        TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
        if(null == tsZone) {
            tsZone = Global.getUser().getTsUnit().getTsZone();
        }
        //本省的省级编码
        String compareZoneGb = StringUtils.isBlank(tsZone.getZoneGb()) || tsZone.getZoneGb().length() < 2 ? null :
                tsZone.getZoneGb().substring(0,2);
        //用人单位
        if (null!=crpt && null!=crpt.getRid()) {
            TsSimpleCode indusTypeId = crpt.getTsSimpleCodeByIndusTypeId();
            //单位地区编码
            String curZoneGb = null == crpt.getTsZoneByZoneId() ? null : crpt.getTsZoneByZoneId().getZoneGb();
            //用人单位的验证
            boolean isFlag = (StringUtils.isBlank(curZoneGb) || StringUtils.isBlank(compareZoneGb) || !curZoneGb.startsWith(compareZoneGb)) &&
                    null != indusTypeId && !"2".equals(indusTypeId.getExtendS1());
            if (isFlag) {
                //当选择的用人单位行业类别不为人力资源服务时，地区必须为当前职卫平台所属省份地区；若是外省地区则不可选择，提示：单位行业类别不为“人力资源服务”行业时，仅可选择本省范围内的单位！
                Global.markErrorInfo(true, id + "crptName", "用人单位行业类别不为“人力资源服务”行业时，仅可选择本省范围内的单位！");
                flag = false;
            }
        }
        //用工单位
        if (null == empCrptId || null == empCrptId.getRid()) {
            Global.markErrorInfo(true, id+"entrsutCrptName", "用工单位名称不能为空！");
            return false;
        }
        //用工单位地区编码
        String curZoneGb = null == empCrptId.getTsZoneByZoneId() ? null : empCrptId.getTsZoneByZoneId().getZoneGb();
        boolean isFlag = StringUtils.isBlank(curZoneGb) || StringUtils.isBlank(compareZoneGb) || !curZoneGb.startsWith(compareZoneGb)
                || "2".equals(empCrptId.getTsSimpleCodeByIndusTypeId().getExtendS1());
        //地区编码非本省的
        if(isFlag){
            Global.markErrorInfo(true, id+"entrsutCrptName", "用工单位仅可选择本省范围内且行业类别不能为“人力资源服务”行业的单位！");
            flag = false;
        }
        //用人和用工单位不是一家时验证单位名称是否一致
        isFlag = null!=crpt && null!=crpt.getRid() && null!=empCrptId && null!=empCrptId.getRid()
                && crpt.getRid().compareTo(empCrptId.getRid()) != 0 && crpt.getCrptName().equals(empCrptId.getCrptName())
        && "2".equals(crpt.getTsSimpleCodeByIndusTypeId().getExtendS1());
        if(isFlag){
            Global.markErrorInfo(true, id+"entrsutCrptName", "用工单位名称和用人单位名称不能相同！");
            flag = false;
        }
        return flag;
	}

    /**
     * @Description : 保存时验证-体检问诊
     * @MethodAuthor: anjing
     * @Date : 2019/6/5 12:22
     **/
    private boolean vertyExmsDataBySave() {
        boolean flag = true;
        //职业史起止时间
        if(ifZysTimeRange && this.tdTjEmhistoryCltListBean!=null && this.tdTjEmhistoryCltListBean.getEmploymentHisList()!=null){
            for(int i=0;i<this.tdTjEmhistoryCltListBean.getEmploymentHisList().size();i++){
                TdTjEmhistoryClt historyClt = this.tdTjEmhistoryCltListBean.getEmploymentHisList().get(i);
                if(historyClt.getStartDate()!=null && DateUtils.isCompareDate(historyClt.getStartDate(),">",this.tdTjBhkClt.getBhkDate())){
                    JsfUtil.addErrorMessage("职业史的第"+(i+1)+"条起止时间开始日期须小于等于体检日期（"+DateUtils.formatDate(this.tdTjBhkClt.getBhkDate())+"）！");
                    flag = false;
                }
                if(historyClt.getStartDate()!=null && DateUtils.isCompareDate(historyClt.getStartDate(),"<=",this.tdTjBhkClt.getBrth())){
                    JsfUtil.addErrorMessage("职业史的第"+(i+1)+"条起止时间开始日期须大于出生日期（"+DateUtils.formatDate(this.tdTjBhkClt.getBrth())+"）！");
                    flag = false;
                }
            }
        }
        String id = "tabView\\\\:editTabView\\\\:exmsdataListForm\\\\:";
        //当选中从不抽烟时，总月数>0,
        if(this.tdTjExmsdataCltBean!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt()!=null
                && this.tdTjExmsdataCltBean.getSmkSelRid()!=null && this.tdTjExmsdataCltBean.getSmklMap()!=null
                && this.tdTjExmsdataCltBean.getSmklMap().containsKey(this.tdTjExmsdataCltBean.getSmkSelRid())
                && !"1".equals(this.tdTjExmsdataCltBean.getSmklMap().get(this.tdTjExmsdataCltBean.getSmkSelRid()).getExtendS1())){
            if("0".equals(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkyerqty())
                    && new Integer("0").equals(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty())){
                Global.markErrorInfo(true, id+"smkmthqty", "吸烟史（年）、吸烟史（月）不能同时为0！");
                String aa=id+"smkyerqty";
                RequestContext.getCurrentInstance().execute("SYSTEM.markErrorInfo('"+aa+"')");
                flag = false;
            }
        }
        if(this.tdTjExmsdataCltBean!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt()!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty()!=null
                && !(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty()>=0 && this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty()<=11)){
            Global.markErrorInfo(true, id+"smkmthqty", "吸烟史（月）必须大于等于0同时小于等于11！");
            flag = false;
        }
        if (this.tdTjExmsdataCltBean!=null && tdTjExmsdataCltBean.getIsSmkEdit() && this.tdTjExmsdataCltBean.getSmkSelRid()!=null) {
            if(this.tdTjExmsdataCltBean.getTdTjExmsdataClt()!=null){
                if(StringUtils.isBlank(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkdayble())){
                    Global.markErrorInfo(true, id+"smkdayble", "平均每天吸烟量（支）不能为空！");
                    flag = false;
                }else if(StringUtils.isNotBlank(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkdayble()) && "0".equals(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkdayble())){
                    Global.markErrorInfo(true, id+"smkdayble", "平均每天吸烟量（支）必须大于0！");
                    flag = false;
                }
            }
        }


        if(!CollectionUtils.isEmpty(this.symIdList)) {
            if(this.tdTjSymptomCltBean.getExtends1() != null && this.tdTjSymptomCltBean.getExtends1().equals("1")) {
                if(StringUtils.isBlank(this.tdTjSymptomCltBean.getOthsym())) {
                	Global.markErrorInfo(true, id+"othsym", "其他症状不能为空！");
                    flag = false;
                }
            }
        }
        // 问诊日期不能大于体检日期
        if(null != this.tdTjBhkClt.getWzChkdat()) {
            if (this.tdTjBhkClt.getWzChkdat().before(this.tdTjBhkClt.getBhkDate())) {
            	Global.markErrorInfo(true, id+"wzChkdat_input", "问诊日期必须大于等于体检日期！");
                flag = false;
            }
        }
        return flag;
    }

    /**
     * @Description : 下一步时验证-体检问诊
     * @MethodAuthor: anjing
     * @Date : 2019/5/20 10:09
     **/
    private boolean vertyExmsDataByNext() {
        boolean flag = true;
        String id = "tabView\\\\:editTabView\\\\:exmsdataListForm\\\\:";
        // 问诊职业史根据配置判断是否必填，岗前不必填。（数据库系统参数，平台负责升级，默认不必填）
        //参数开启，且非岗前
        boolean ifBeforeOnguard = StringUtils.isNotBlank(this.ifNonRecheckEmhistoryRequired) && "1".equals(this.ifNonRecheckEmhistoryRequired) && (null == this.tdTjBhkClt.getFkByOnguardStateid().getExtendS1() || !"1".equals(this.tdTjBhkClt.getFkByOnguardStateid().getExtendS1()));
        //职业史判断
        if (ifBeforeOnguard && new Integer("0").equals(ifShowRad) && (null == this.tdTjEmhistoryCltListBean || CollectionUtils.isEmpty(this.tdTjEmhistoryCltListBean.getEmploymentHisList()))) {
            JsfUtil.addErrorMessage("职业史不能为空！");
            flag = false;
        }
        //职业史起止时间
        if(ifZysTimeRange && this.tdTjEmhistoryCltListBean !=null && !CollectionUtils.isEmpty(this.tdTjEmhistoryCltListBean.getEmploymentHisList())){
            for(int i=0;i<this.tdTjEmhistoryCltListBean.getEmploymentHisList().size();i++){
                TdTjEmhistoryClt historyClt = this.tdTjEmhistoryCltListBean.getEmploymentHisList().get(i);
                if(historyClt.getStartDate()!=null && DateUtils.isCompareDate(historyClt.getStartDate(),">",this.tdTjBhkClt.getBhkDate())){
                    JsfUtil.addErrorMessage("职业史的第"+(i+1)+"条起止时间开始日期须小于等于体检日期（"+DateUtils.formatDate(this.tdTjBhkClt.getBhkDate())+"）！");
                    flag = false;
                }
                if(historyClt.getStartDate()!=null && DateUtils.isCompareDate(historyClt.getStartDate(),"<=",this.tdTjBhkClt.getBrth())){
                    JsfUtil.addErrorMessage("职业史的第"+(i+1)+"条起止时间开始日期须大于出生日期（"+DateUtils.formatDate(this.tdTjBhkClt.getBrth())+"）！");
                    flag = false;
                }
            }
        }
        //根据系统参数判断问诊放射史必填 非岗前，且包含放射
        if (ifBeforeOnguard && new Integer("1").equals(this.ifShowRad) && (null == this.tdTjEmhistoryCltListBean || CollectionUtils.isEmpty(tdTjEmhistoryCltListBean.getRadiationHisList()))) {
            JsfUtil.addErrorMessage("放射史不能为空！");
            flag = false;
        }
        //根据系统参数判断问诊既往史必填
        if(StringUtils.isNotBlank(this.ifChkAnamnesis)&&"1".equals(this.ifChkAnamnesis)){
            if(null == this.tdTjAnamnesisCltListBean||CollectionUtils.isEmpty(tdTjAnamnesisCltListBean.getAnamnesisHisList())){
                JsfUtil.addErrorMessage("既往史至少有一条！");
                flag = false;
            }
        }
        //根据系统参数判断问诊家族史必填
        if(StringUtils.isNotBlank(this.ifChkJzs)&&"1".equals(this.ifChkJzs)){
            if(null == this.tdTjExmsdataCltBean||StringUtils.isBlank(tdTjExmsdataCltBean.getTdTjExmsdataClt().getJzs())){
                JsfUtil.addErrorMessage("家族史不能为空！");
                flag = false;
            }
        }
        //吸烟史
        if(this.tdTjExmsdataCltBean!=null && this.tdTjExmsdataCltBean.getSmkSelRid()==null){
            Global.markErrorInfo(true, id+"smkSelRid", "请选择目前吸烟情况！");
            flag = false;
        }
        if(this.tdTjExmsdataCltBean!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt()!=null
                && StringUtils.isBlank(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkyerqty())){
            Global.markErrorInfo(true, id+"smkyerqty", "吸烟史（年）不能为空！");
            flag = false;
        }
        if(this.tdTjExmsdataCltBean!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt()!=null
                && this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty()==null){
            Global.markErrorInfo(true, id+"smkmthqty", "吸烟史（月）不能为空！");
            flag = false;
        }
        if(this.tdTjExmsdataCltBean!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt()!=null
                && StringUtils.isBlank(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkdayble())) {
            Global.markErrorInfo(true, id + "smkdayble", "平均每天吸烟量（支）不能为空！");
            flag = false;
        }
        if(this.tdTjExmsdataCltBean!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt()!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty()!=null
                && !(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty()>=0 && this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty()<=11)){
            Global.markErrorInfo(true, id+"smkmthqty", "吸烟史（月）必须大于等于0同时小于等于11！");
            flag = false;
        }
        //当选中从不抽烟时，总月数>0,
        if(this.tdTjExmsdataCltBean!=null && this.tdTjExmsdataCltBean.getTdTjExmsdataClt()!=null
            && this.tdTjExmsdataCltBean.getSmkSelRid()!=null && this.tdTjExmsdataCltBean.getSmklMap()!=null
            && this.tdTjExmsdataCltBean.getSmklMap().containsKey(this.tdTjExmsdataCltBean.getSmkSelRid())
            && !"1".equals(this.tdTjExmsdataCltBean.getSmklMap().get(this.tdTjExmsdataCltBean.getSmkSelRid()).getExtendS1())){
            if("0".equals(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkyerqty())
                && new Integer("0").equals(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkmthqty())){
                Global.markErrorInfo(true, id+"smkmthqty", "吸烟史（年）、吸烟史（月）不能同时为0！");
                String smkyerqtyRid=id+"smkyerqty";
                RequestContext.getCurrentInstance().execute("SYSTEM.markErrorInfo('"+smkyerqtyRid+"')");
                flag = false;
            }
            if(tdTjExmsdataCltBean.getIsSmkEdit() && "0".equals(this.tdTjExmsdataCltBean.getTdTjExmsdataClt().getSmkdayble())){
                Global.markErrorInfo(true, id+"smkdayble", "平均每天吸烟量（支）必须大于0！");
                flag = false;
            }
        }

        //症状
        if(CollectionUtils.isEmpty(this.symIdList)) {
        	Global.markErrorInfo(true, id+"symCodeRowCommPOList", "症状不能为空！");
            flag = false;
        } else {
            if(this.tdTjSymptomCltBean.getExtends1() != null && "1".equals(this.tdTjSymptomCltBean.getExtends1())) {
                if(StringUtils.isBlank(this.tdTjSymptomCltBean.getOthsym())) {
                	Global.markErrorInfo(true, id+"othsym", "其他症状不能为空！");
                    flag = false;
                }
            }
        }

        if(StringUtils.isBlank(this.editChkdocId)) {
        	Global.markErrorInfo(true, id+"editChkdocId", "问诊医生不能为空！");
            flag = false;
        }

        if(null == this.tdTjBhkClt.getWzChkdat()) {
        	Global.markErrorInfo(true, id+"wzChkdat_input", "问诊日期不能为空！");
            flag = false;
        }
        // 问诊日期不能大于体检日期
        if(null != this.tdTjBhkClt.getWzChkdat()) {
            if (this.tdTjBhkClt.getWzChkdat().before(this.tdTjBhkClt.getBhkDate())) {
            	Global.markErrorInfo(true, id+"wzChkdat_input", "问诊日期必须大于等于体检日期！");
                flag = false;
            }
        }
        return flag;
    }

    /**
     * @Description : 保存时验证-体检结果
     * @MethodAuthor: anjing
     * @Date : 2019/6/5 12:22
     **/
    private boolean vertyBhkSubBySave() {
        boolean flag = true;
        boolean ifTip = true;//是否提示信息
        int  i = 0;
        String id = "tabView\\\\:editTabView\\\\:bhkSubListForm\\\\:";
        for(TbTjItemSort tbTjItemSort : this.mustItemSortList) {
        	// 检查日期不能大于体检日期
            if(null != tbTjItemSort.getBhksubCltList().get(0).getChkdat()) {
                if(tbTjItemSort.getBhksubCltList().get(0).getChkdat().before(this.tdTjBhkClt.getBhkDate())) {
                	Global.markErrorInfo(ifTip, id+"chkdat"+i+"_input","检查日期必须大于等于体检日期！");
                    flag = false;
                }
            }
            int j = 0;
            for (TdTjBhksubClt tdTjBhksubClt : tbTjItemSort.getBhksubCltList()) {
                //校验国家接口标准
                if (tdTjBhksubClt.getJdgptn() == 2 && tdTjBhksubClt.getIfLack() == 0) {
                    if (!verifyGjItemsRes(tdTjBhksubClt, i, j, flag&&ifTip)) {
                        flag = false;
                    }
                }
                j++;
            }
            if(!flag){
                ifTip = false;
            }
            i++;
        }
        return flag;
    }

    /**
    * @Description : 保存时验证-体检结果
    * @MethodAuthor: anjing
    * @Date : 2020/5/22 17:53
    **/
    private boolean verfySympomBySave() {
        boolean flag = true;
        boolean ifSubTip = true;
        Object[] sbp = new Object[2]; // 收缩压
        Object[] dbp = new Object[2]; // 舒张压
        String dbpItemRstId = "";
        int i = 0;
        String id = "tabView\\\\:editTabView\\\\:bhkSubListForm\\\\:";
        for(TbTjItemSort tbTjItemSort : this.mustItemSortList) {
            int j = 0;
            for (TdTjBhksubClt tdTjBhksubClt : tbTjItemSort.getBhksubCltList()) {
                if(StringUtils.isNotBlank(tdTjBhksubClt.getItemRst()) && tdTjBhksubClt.getIfLack() != 1) {
                    if (null != tdTjBhksubClt.getFkByItemId().getItemTag()) {
                        if (tdTjBhksubClt.getFkByItemId().getItemTag().intValue() == 1 && 2 == tdTjBhksubClt.getJdgptn()) {
                            sbp[0] = tdTjBhksubClt.getFkByItemId().getItemName();
                            sbp[1] = tdTjBhksubClt.getItemRst();
                        }
                        if (tdTjBhksubClt.getFkByItemId().getItemTag().intValue() == 2 && 2 == tdTjBhksubClt.getJdgptn()) {
                            dbp[0] = tdTjBhksubClt.getFkByItemId().getItemName();
                            dbp[1] = tdTjBhksubClt.getItemRst();
                            dbpItemRstId = "bhksubCltList" + i + "\\\\:" + j + "\\\\:itemRstInput";
                        }
                    }
                }
                //校验国家接口标准
                if (tdTjBhksubClt.getJdgptn() == 2 && tdTjBhksubClt.getIfLack() == 0) {
                    if (!verifyGjItemsRes(tdTjBhksubClt, i, j, flag && ifSubTip)) {
                        flag = false;
                    }
                }
                if (!flag) {
                    ifSubTip = false;
                }
                j++;
            }
            i++;
        }
        // 舒张压<收缩压
        if(null != dbp[1] && null != sbp[1]) {
            if(new BigDecimal(dbp[1].toString()).compareTo(new BigDecimal(sbp[1].toString())) >= 0) {
                Global.markErrorInfo(true, id+dbpItemRstId,dbp[0] + "结果应小于" + sbp[0] + "结果！");
                flag = false;
            }
        }
        return flag;
    }

    /**
     * @Description : 下一步时验证-体检结果
     * @MethodAuthor: anjing
     * @Date : 2019/5/30 8:33
     **/
    private boolean vertyBhkSubByNext() {
        boolean flag = true;
        boolean ifTip = true;
        boolean ifSubTip = true;//是否提示
        BigDecimal sbp = null; // 收缩压
        BigDecimal dbp = null; // 舒张压
        String dbpItemRstId = "";
        int i = 0;
        String id = "tabView\\\\:editTabView\\\\:bhkSubListForm\\\\:";
        // 未检的体检项目编码
        Set<String> ifLackSet=new HashSet<>();
        // key:体检项目编码  value:项目名称
        Map<String,String> itemNameMap=new HashMap<>();
        // key:体检项目编码  value:下标集合
        Map<String,List<Integer>> itemIndexMap=new HashMap<>();
        //体检项目子表存储的体检项目编码集合
        List<String> curItemCodes = new ArrayList<>();
        for(TbTjItemSort tbTjItemSort : this.mustItemSortList) {
            int j = 0;
            for (TdTjBhksubClt tdTjBhksubClt : tbTjItemSort.getBhksubCltList()) {
                curItemCodes.add(tdTjBhksubClt.getFkByItemId().getItemCode());
                if(ifItemMust && new Integer("1").equals(tdTjBhksubClt.getIfLack())){
                    ifLackSet.add(tdTjBhksubClt.getFkByItemId().getItemCode());
                    itemNameMap.put(tdTjBhksubClt.getFkByItemId().getItemCode(),tdTjBhksubClt.getFkByItemId().getItemName());
                    // 初始化下标map
                    List<Integer> indexList= new ArrayList<>();
                    indexList.add(i);
                    indexList.add(j);
                    itemIndexMap.put(tdTjBhksubClt.getFkByItemId().getItemCode(),indexList);
                }

            	 //原始值
				if (null != this.tjStadItemsStatus.getAudioModel()
						&& 1 == this.tjStadItemsStatus.getAudioModel()) {
					Integer itemTag = tdTjBhksubClt.getFkByItemId().getItemTag();
					if (null!=itemTag && itemTag.intValue()>=3 && itemTag.intValue()<=14
                            && 1 != tdTjBhksubClt.getIfLack()) {//双耳等需计算得出-无原始值 要注意是否缺项
						if (StringUtils.isBlank(tdTjBhksubClt.getItemRstOri())) {
							String itemRstId = "bhksubCltList"+i+"\\\\:"+j+"\\\\:itemRstInputOri";
							if(tdTjBhksubClt.getJdgptn() == 1){//定性，下拉组件
								itemRstId = "bhksubCltList"+i+"\\\\:"+j+"\\\\:itemRstOri";
							}
							Global.markErrorInfo(ifTip && ifSubTip, id+itemRstId,"原始值不能为空！");
							flag = false;
						}
					}
                }
            	if(StringUtils.isBlank(tdTjBhksubClt.getItemRst())) {
                	String itemRstId = "";
                	if(tdTjBhksubClt.getJdgptn() == 1){//定性，下拉组件
                		itemRstId = "bhksubCltList"+i+"\\\\:"+j+"\\\\:itemRst";
                	}else {//定量，输入框组件
                		itemRstId = "bhksubCltList"+i+"\\\\:"+j+"\\\\:itemRstInput";
					}
                	String extendS1 = tdTjBhksubClt.getFkByItemId().getTsSimpleCode().getExtendS1();
                    Global.markErrorInfo(ifTip && ifSubTip, id+itemRstId,("1".equals(extendS1)?"修正值":"项目结果")+"不能为空！");
                    flag = false;
                } else {
                    if(null != tdTjBhksubClt.getFkByItemId().getItemTag() && tdTjBhksubClt.getIfLack() != 1) {
                        if(tdTjBhksubClt.getFkByItemId().getItemTag().intValue() == 1 && 2 == tdTjBhksubClt.getJdgptn()) {
                            sbp = new BigDecimal(tdTjBhksubClt.getItemRst());
                        }
                        if(tdTjBhksubClt.getFkByItemId().getItemTag().intValue() == 2 && 2 == tdTjBhksubClt.getJdgptn()) {
                            dbp = new BigDecimal(tdTjBhksubClt.getItemRst());
                            dbpItemRstId = "bhksubCltList"+i+"\\\\:"+j+"\\\\:itemRstInput";
                        }
                    }
                }
            	// 结果判定必填校验
            	if(null != tdTjBhksubClt.getFkByItemId().getItemTag() && tdTjBhksubClt.getFkByItemId().getItemTag().intValue() == 30) {
                   if(null == tdTjBhksubClt.getRstFlag()) {
                       String rstFlagId = "bhksubCltList"+i+"\\\\:"+j+"\\\\:rstFlagRadio";
                       Global.markErrorInfo(ifTip && ifSubTip, id+rstFlagId,"结果判定不能为空！");
                       flag = false;
                   }
                }

                // 定性未缺项项目，检查结论必填
                if(tdTjBhksubClt.getJdgptn() == 1 && tdTjBhksubClt.getIfLack() != 1) {
                    if(StringUtils.isBlank(tbTjItemSort.getBhksubCltList().get(0).getDiagRest())) {
                    	Global.markErrorInfo(ifTip && ifSubTip, id+"diagRest"+i,"定性项目的检查结论必填！");
                        flag = false;
                    }
                }
                //校验国家接口标准
                if (tdTjBhksubClt.getJdgptn() == 2 && tdTjBhksubClt.getIfLack() == 0) {
                    if (!verifyGjItemsRes(tdTjBhksubClt, i, j, ifTip && ifSubTip)) {
                        flag = false;
                    }
                }
                if(!flag) {
                	ifSubTip = false;
                }
                j++;
            }

            // 检查日期、体检医师必填
            if(null ==  tbTjItemSort.getBhksubCltList().get(0).getChkdat()) {
            	Global.markErrorInfo(ifTip, id+"chkdat"+i+"_input","检查日期不能为空！");
                flag = false;
            }else {
            	// 检查日期不能大于体检日期
            	if(tbTjItemSort.getBhksubCltList().get(0).getChkdat().before(this.tdTjBhkClt.getBhkDate())) {
            		Global.markErrorInfo(ifTip, id+"chkdat"+i+"_input","检查日期必须大于等于体检日期！");
            		flag = false;
            	}
			}
            if(null == tbTjItemSort.getBhksubCltList().get(0).getChkdoctId()) {
            	Global.markErrorInfo(ifTip, id+"chkdoct"+i,"体检医师不能为空！");
                flag = false;
            }
            if(!flag) {
            	ifTip = false;
            }
            i++;
        }
        // 舒张压<收缩压
        if(null != dbp && null != sbp) {
            if(dbp.compareTo(sbp) >= 0) {
                Global.markErrorInfo(true, id+dbpItemRstId,"舒张压结果应小于收缩压结果！");
                flag = false;
            }
        }
        boolean ifShowDiag=false;
        //校验必检项目 参数开启并且初检
        if (flag && this.ifItemMust && new Integer("0").equals(this.tdTjBhkClt.getIfRhk()) && !this.cmbItemsMap.isEmpty()) {
            List<String> errorList=new ArrayList<>();
            for (Map.Entry<String, List<String>> entry : this.cmbItemsMap.entrySet()) {
                StringBuilder errItemCode = new StringBuilder();
                boolean ifShowLackDiag=false;
                if ("1".equals(entry.getKey().split("&")[1])) {
                    // 判定方式为任一满足
                    ifShowLackDiag=anyGrat(entry.getValue(),curItemCodes,ifLackSet);
                } else if ("2".equals(entry.getKey().split("&")[1])) {
                    // 判定方式为同时满足
                    ifShowLackDiag=togetherGrat(entry.getValue(),curItemCodes);
                }
                if(ifShowLackDiag){
                    flag = false;
                    ifShowDiag=true;
                    RequestContext.getCurrentInstance().execute("PF('ItemLackDiag').show()");
                    break;
                }
                List<String> itemCodeList = new ArrayList<>();
                for (String itemCode : entry.getValue()) {
                    // 判定方式为任一满足
                    if ("1".equals(entry.getKey().split("&")[1])) {
                        if(itemCodeSet.contains(itemCode)){
                            //如果项目存在同时满足里，则该项目组合不在验证
                            break;
                        }
                        if (ifLackSet.contains(itemCode)) {
                            if (itemNameMap.containsKey(itemCode)) {
                                errItemCode.append("或").append(itemNameMap.get(itemCode));
                                itemCodeList.add(itemCode);
                            }
                        } else {
                            errItemCode = new StringBuilder();
                            break;
                        }
                    } else if ("2".equals(entry.getKey().split("&")[1])) {
                        // 判定方式为同时满足
                        if (ifLackSet.contains(itemCode) && itemNameMap.containsKey(itemCode)) {
                            errItemCode.append("、").append(itemNameMap.get(itemCode));
                            itemCodeList.add(itemCode);
                        }
                    }
                }
                if (errItemCode.length() > 0) {
                    flag = false;
                    StringBuilder tipMsg = new StringBuilder();
                    if (!CollectionUtils.isEmpty(itemCodeList)) {
                        for (String code : itemCodeList) {
                            if (!itemIndexMap.containsKey(code)) {
                                continue;
                            }
                            List<Integer> index = itemIndexMap.get(code);
                            tipMsg.append(",").append(id + "bhksubCltList" + index.get(0) + "\\\\:" + index.get(1) + "\\\\:ifLack");
                        }
                    }
                    if ("1".equals(entry.getKey().split("&")[1])) {
                        errorList.add(tipMsg.substring(1)+"&"+errItemCode.substring(1) + (errItemCode.toString().substring(1).contains("或") ? "必检其中一项！" : "必检！"));
                    } else {
                        errorList.add(tipMsg.substring(1)+"&"+errItemCode.substring(1) + (errItemCode.toString().substring(1).contains("、") ? "全部必检！" : "必检！"));
                    }
                }
            }
            if(!ifShowDiag && !CollectionUtils.isEmpty(errorList)){
                for (String info : errorList) {
                    Global.markErrorInfo(true, info.split("&")[0], info.split("&")[1]);
                }
            }

        }
        return flag;
    }

    /**
    * <p>Description：任一满足 </p>
    * <p>Author： yzz 2025/3/5 </p>
    */
    public boolean anyGrat(List<String> items,List<String> curItemCodes,Set<String> ifLackSet){

        if(CollectionUtils.isEmpty(items)){
            return false;
        }
        //是否存在缺项项目
        boolean ifLack=false;
        //是否都是未检
        boolean ifAllLack=true;
        //是否项目都不存在
        boolean ifAllExist=true;
        for (String itemCode : items) {
            //如果体检项目在同时满足的项目里已存在，则跳过
            if(itemCodeSet.contains(itemCode)){
                return false;
            }
            ifAllExist = ifAllExist && !curItemCodes.contains(itemCode);
            //如果项目不存在 则continue
            if(!curItemCodes.contains(itemCode)){
                ifLack=true;
                continue;
            }
            ifAllLack=ifAllLack&&ifLackSet.contains(itemCode);
        }
        return (ifLack && ifAllLack) || ifAllExist;
    }


    /**
     * <p>Description：同时满足 </p>
     * <p>Author： yzz 2025/3/5 </p>
     */
    public boolean togetherGrat(List<String> items,List<String> curItemCodes){
        if(CollectionUtils.isEmpty(items)){
            return false;
        }
        for (String itemCode : items) {
            //有一个同时满足的体检项目在页面上没有的，则直接给提示
            if(!curItemCodes.contains(itemCode)){
                return true;
            }
        }
        return false;
    }


    /**
     * <p>方法描述： FVC%、FEV1%、FVC1/FVC 结果大于100判断 </p>
     * @MethodAuthor： pw 2023/5/12
     **/
    private boolean vertyBhkSubItemTagExt3ByNext() {
        StringBuffer buffer = new StringBuffer();
        for(TbTjItemSort tbTjItemSort : this.mustItemSortList) {
            for (TdTjBhksubClt tdTjBhksubClt : tbTjItemSort.getBhksubCltList()) {
                String ext3 = null == tdTjBhksubClt.getFkByItemId() || null == tdTjBhksubClt.getFkByItemId().getItemTagId() ? null :
                        tdTjBhksubClt.getFkByItemId().getItemTagId().getExtendS3();
                if(StringUtils.isNotBlank(tdTjBhksubClt.getItemRst()) && null != tdTjBhksubClt.getIfLack()
                        && 0 == tdTjBhksubClt.getIfLack() &&
                        ("31".equals(ext3) || "32".equals(ext3) || "33".equals(ext3))){
                    String itmRst = tdTjBhksubClt.getItemRst();
                    if(StringUtils.isNumber(itmRst) && (new BigDecimal(itmRst)).compareTo(new BigDecimal("100")) > 0){
                        buffer.append("，").append(tdTjBhksubClt.getFkByItemId().getItemName());
                    }
                }
            }
        }
        this.itemTagDiagTip = buffer.length() > 0 ? buffer.substring(1) : null;
        return StringUtils.isNotBlank(this.itemTagDiagTip);
    }

    private boolean vertyBhkSubByNextAndIfRfk() {
        boolean flag = true;
        if(CollectionUtils.isEmpty(this.mustItemSortList)) {
            JsfUtil.addErrorMessage("必须有一项体检项目！");
            flag = false;
        }
        return flag;
    }
    /******************************************** 结束验证 *********************************************/

    /******************************************** 开始保存 *********************************************/
    /**
     * <p>描述 初始化原始值 性别、在岗状态、体检危害因素</p>
     *
     * @MethodAuthor gongzhe,2022/8/18 15:45,initOriValue
     * @return void
     */
    private void initOriValue(){
        if(new Integer(1).equals(tdTjBhkClt.getIfNotMod())){
            tjOriSex = tdTjBhkClt.getSex();
            if (this.tdTjBhkClt.getFkByOnguardStateid() != null && this.tdTjBhkClt.getFkByOnguardStateid().getRid() != null) {
                editOnguadrStateId = this.tdTjBhkClt.getFkByOnguardStateid().getRid().toString();
            } else {
                this.tdTjBhkClt.setFkByOnguardStateid(new TsSimpleCode());
                editOnguadrStateId = null;
            }
            this.oldBadrsns = new ArrayList<>();
            for(TdTjBadrsnsClt badrsnsClt:this.tdTjBhkClt.getTdTjBadrsnsClts()){
                oldBadrsns.add(badrsnsClt);
            }
        }
        //防护用品佩戴情况
        if(tdTjBhkClt.getFkByProtectEquId()==null){
            tdTjBhkClt.setFkByProtectEquId(new TsSimpleCode());
        }
    }

    /**
     * <p>1、体检危害因素发生变化，将清空已录入的历史放射史记录，请确认</p>
     * <p>2、性别或在岗状态或体检危害因素发生变化，将重置检查结果的记录，请确认</p>
     * <p>3、性别发生变化，将重置问诊的记录，请确认</p>
     * <p>4、体检危害因素发生变化，将重置检查结论的记录，请确认</p>
     *
     * @MethodAuthor gongzhe,2022/8/10 18:34,saveBasicMessage
     * @return void
     * <p>saveBasicMessageRst包含4个字符串，0和1分别表示对应的提示是否出现</p>
     * <p>例如：0101 则表示：性别或在岗状态或体检危害因素发生变化，将重置检查结果的记录；性别发生变化，将重置问诊的记录</p>
     */
    private void saveBasicMessage(){
        List<String> msgList = new ArrayList<>();
        if(!new Integer(1).equals(tdTjBhkClt.getIfNotMod())) {
            return ;
        }
        boolean tjBadChange = false;
        //最新的危害因素
        List<TdTjBadrsnsClt>  newTdTjBadrsnsClts = tdTjBhkClt.getTdTjBadrsnsClts();
        if(oldBadrsns!=null){
            if(oldBadrsns.size() != newTdTjBadrsnsClts.size()){
                //新危害因素的数量与原危害因素数量不同，则危害因素一定改变
                tjBadChange = true;
            }else{
                for(TdTjBadrsnsClt clt:oldBadrsns){
                    boolean flag = false;
                    for(TdTjBadrsnsClt newClt:newTdTjBadrsnsClts){
                        if(clt.getFkByBadrsnId().getRid().equals(newClt.getFkByBadrsnId().getRid())){
                            flag = true;
                            break;
                        }
                    }
                    if(!flag){
                        //原危害因素在新的列表中不存在，即被删除，则危害因素改变
                        tjBadChange = true;
                        break;
                    }
                }
            }
        }
        //体检危害因素发生了变化，且不包含放射类危害因素，且有放射史数据提示
        if(tjBadChange && !this.ifContainsRadi()){
            boolean rad = false;
            List<TdTjEmhistoryClt> ehclist = this.tdTjBhkClt.getTdTjEmhistoryClts();
            for(TdTjEmhistoryClt his : ehclist){
                if(new Integer(1).equals(his.getHisType())){
                    //放射史
                    rad = true;
                    break;
                }
            }
            if(rad){
                msgList.add("体检危害因素发生变化，将清空已录入的历史放射史记录，请确认！");
                saveBasicMessageRst[0] = "1";
            }
        }
        //性别发生变化
        boolean sexChange = !tjOriSex.equals(this.tdTjBhkClt.getSex());
        //在岗状态发生变化
        String oldOnguardStateId;
        if (this.tdTjBhkClt.getFkByOnguardStateid() != null && this.tdTjBhkClt.getFkByOnguardStateid().getRid() != null) {
            oldOnguardStateId = this.tdTjBhkClt.getFkByOnguardStateid().getRid().toString();
        } else {
            oldOnguardStateId = "";
        }
        boolean stateChange = !oldOnguardStateId.equals(this.editOnguadrStateId);
        //查询当前主表关联的体检子表数据
        int subCount = this.service.findTdTjBhksubCltCountsByMainId(tdTjBhkClt.getRid());
        if(ObjectUtil.isNotEmpty(this.editOnguadrStateId) && subCount > 0 && (sexChange || stateChange || tjBadChange)){
            String changeStr = "";
            if(sexChange){
                changeStr += "、性别";
            }
            if(stateChange){
                changeStr += "、在岗状态";
            }
            if(tjBadChange){
                changeStr += "、体检危害因素";
            }
            msgList.add(changeStr.substring(1)+"发生变化，将重置检查结果的记录，请确认！");
            saveBasicMessageRst[1] = "1";
        }
        //性别发生变化，将重置问诊的记录，请确认！ 当前性别为男，即由女-->男，才提示
        if(!CollectionUtils.isEmpty(this.tdTjBhkClt.getTdTjExmsdataClts()) && sexChange && "男".equals(this.tdTjBhkClt.getSex())){
            msgList.add("性别发生变化，将重置问诊的记录，请确认！");
            saveBasicMessageRst[2] = "1";
        }
        if(tjBadChange){
            //存在体检结论
            boolean hasConclusion = false;
            //源体检危害因素结论，此处不能使用oldBadrsns，oldBadrsns是保存时基于tdTjBhkClt实体初始化的，并不是数据库中最新的
            List<TdTjBadrsnsClt> oriBadrsn = this.service.selectBadrsnsCltListByBhkId(tdTjBhkClt.getRid());
            for(TdTjBadrsnsClt clt:oriBadrsn){
                boolean flag = false;
                for(TdTjBadrsnsClt newClt:newTdTjBadrsnsClts){
                    if(clt.getFkByBadrsnId().getRid().equals(newClt.getFkByBadrsnId().getRid())){
                        flag = true;
                        break;
                    }
                }
                if(!flag && clt.getFkByExamConclusionId()!=null && clt.getFkByExamConclusionId().getRid()!=null){
                    //原危害因素在新的列表中不存在，即被删除，则危害因素改变
                    hasConclusion = true;
                    break;
                }
            }
            if(hasConclusion){
                msgList.add("体检危害因素发生变化，将重置检查结论的记录，请确认！");
                saveBasicMessageRst[3] = "1";
            }
        }
        for (int i = 0; i < msgList.size(); i++) {
            saveBasicMessage += (i+1)+"、"+msgList.get(i);
            if(i != msgList.size()-1){
                //换行
                saveBasicMessage += "<br>";
            }
        }
    }

    /**
     * <p>描述 保存</p>
     *
     * @MethodAuthor gongzhe,2022/8/10 19:01,beforeSaveBasicInfo
     * @return void
     */
    public void beforeSaveBasicInfo(){
        if(!this.vertyBasicInfoBySave()) {
            return ;
        }
        this.saveBasicMessage = "";
        this.saveBasicMessageRst = new String[]{"0","0","0","0","0","0"};
        if(new Integer(1).equals(this.tdTjBhkClt.getIfNotMod())){
            saveBasicMessage();
        }
        //劳动者姓名、关键信息点调整增加提示信息
        this.vertyBasicAfter();
        if(StringUtils.isNotBlank(saveBasicMessage)){
            RequestContext.getCurrentInstance().execute("PF('SaveBasicConfirmDialog').show()");
            RequestContext.getCurrentInstance().update("tabView:editTabView");
        }else{
            this.saveBasicInfo();
            JsfUtil.addSuccessMessage("保存成功！");
        }
    }
    /**
     * <p>描述 下一步</p>
     *
     * @MethodAuthor gongzhe,2022/8/10 18:54,beforeNextBasicInfo
     * @return void
     */
    public void beforeNextBasicInfo(){
        if(!this.vertyBasicInfoByNext()) {
            return;
        }
        this.saveBasicMessage = "";
        this.saveBasicMessageRst = new String[]{"0","0","0","0","0","0"};
        if(new Integer(1).equals(this.tdTjBhkClt.getIfNotMod())){
            saveBasicMessage();
        }
        //劳动者姓名、关键信息点调整增加提示信息
        this.vertyBasicAfter();
        if(StringUtils.isNotBlank(saveBasicMessage)){
            RequestContext.getCurrentInstance().execute("PF('NextBasicConfirmDialog').show()");
            RequestContext.getCurrentInstance().update("tabView:editTabView");
        }else{
            if(!ifIdcAble) {
                this.tdTjBhkClt.setIdc(null);
            }
            this.tdTjBhkClt.setIfNotMod(1);
            if(this.tdTjBhkClt.getIfRhk() == 1) {
                if(this.tdTjBhkClt.getBhkRcdState() < 3) {
                    this.tdTjBhkClt.setBhkRcdState(3);
                }
                this.saveBasicInfo();
                //复检-检查结果
                this.geItemCombidListByBadrsnIdAndWorkStateId();
                // 初始化体检结果页面
                this.getBhksubCltList();
                this.initItemSort();
                this.initChkDocMap();
                RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:bhkSubListForm:resultTitleGrid");
            } else {
                if(this.tdTjBhkClt.getBhkRcdState() < 2) {
                    this.tdTjBhkClt.setBhkRcdState(2);
                }
                this.saveBasicInfo();
                // 初始化体检问诊页面
                this.modExmsdataInit();
                /**问诊日期默认体检日期*/
                if(tdTjBhkClt.getWzChkdat() == null){
                    tdTjBhkClt.setWzChkdat(tdTjBhkClt.getBhkDate());
                }
                RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:exmsdataListForm:exmsdataTitleGrid");
            }
            // 初始化下一个页面
            this.setOnguadrStateId();
            this.setPsnTypeId();
            this.addActiveTab = addActiveTab + 1;
        }
    }


    /**
     * @Description : 保存基本信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/16 14:51
     **/
    public void saveBasicInfo() {
        if(StringUtils.isNotBlank(this.editOnguadrStateId)) {
            this.tdTjBhkClt.setFkByOnguardStateid(this.commService.findTsSimpleCodeByRid(Integer.parseInt(this.editOnguadrStateId)));
        } else {
            this.tdTjBhkClt.setFkByOnguardStateid(null);
        }
        if(StringUtils.isNotBlank(this.editPsnTypeId)) {
            this.tdTjBhkClt.setFkByCardTypeId(this.commService.findTsSimpleCodeByRid(Integer.parseInt(this.editPsnTypeId)));
            if(!ifIdcAble){
                this.tdTjBhkClt.setIdc(null);
            }
            if(null != this.tdTjBhkClt.getBrth()){
                this.tdTjBhkClt.setAge(DateUtils.calAgeByBirtDate(this.tdTjBhkClt.getBrth()));
            }
        }
        // 根据所选危害因素是否包含放射因素，决定体检类型
        if(this.ifContainsRadi()) {
            this.tdTjBhkClt.setBhkType(4);
        }else{
            this.tdTjBhkClt.setBhkType(3);
        }

        // “在岗状态”选择“上岗前”（码表5009扩展字段2，控制接害工龄是否不可输入，默认可以，0/空：可以，1：不可以），“接害工龄”灰掉不可输入
        if(null != this.tdTjBhkClt.getFkByOnguardStateid() && null != this.tdTjBhkClt.getFkByOnguardStateid().getRid()) {
            if(null != this.tdTjBhkClt.getFkByOnguardStateid().getExtendS2() && this.tdTjBhkClt.getFkByOnguardStateid().getExtendS2() == 1) {
                this.tdTjBhkClt.setTchbadrsntim(null);
                this.tdTjBhkClt.setTchbadrsnmonth(null);
            }
        }
        if (!ifOtherBadRsn) {
        	this.tdTjBhkClt.setOtherBadRsn(null);
		}
        if(!ifOtherTouchBadRsn){
            this.tdTjBhkClt.setTchOtherBadrsn(null);
        }
        if(hasAutoGenBhkCode && StringUtils.isBlank(this.tdTjBhkClt.getBhkCode())){
            //自动生成时，若重复，则重新生成一个，不做提示
            String code = DateUtils.getDate("yyyyMMddHHmmssSSS");
            List<TdTjBhkClt> bhkCltList = this.service.selectBhkCltListByBhkCode(code);
            if((!CollectionUtils.isEmpty(bhkCltList) && tdTjBhkClt.getRid() == null) || (!CollectionUtils.isEmpty(bhkCltList) && tdTjBhkClt.getRid() != null && !tdTjBhkClt.getRid().equals(bhkCltList.get(0).getRid()))) {
                code = DateUtils.getDate("yyyyMMddHHmmssSSS");
            }
            this.tdTjBhkClt.setBhkCode(code);
        }
        if("1".equals(this.saveBasicMessageRst[0])){
            //清空已录入的历史放射史记录
            List<TdTjEmhistoryClt> ehclist = this.tdTjBhkClt.getTdTjEmhistoryClts();
            Iterator<TdTjEmhistoryClt> iterator = ehclist.iterator();
            while(iterator.hasNext()){
                TdTjEmhistoryClt his = iterator.next();
                if(new Integer(1).equals(his.getHisType())){
                    //放射史，移除
                    iterator.remove();
                }
            }
        }
        if("1".equals(this.saveBasicMessageRst[1]) && ObjectUtil.isNotEmpty(this.tdTjBhkClt.getTdTjBhksubClts())){
            this.tdTjBhkClt.setTdTjBhksubClts(pakBhkSubCltList());
        }
        if("1".equals(this.saveBasicMessageRst[2])){
            //重置问诊的记录
            //由女-->男，清空女性相关问诊记录
            if(!CollectionUtils.isEmpty(this.tdTjBhkClt.getTdTjExmsdataClts()) && "男".equals(this.tdTjBhkClt.getSex())){
                TdTjExmsdataClt exmsdata = this.tdTjBhkClt.getTdTjExmsdataClts().get(0);
                if(exmsdata != null){
                    exmsdata.setMnrage(null);
                    exmsdata.setMns("");
                    exmsdata.setCyc("");
                    exmsdata.setMnlage(null);
                    exmsdata.setIsxmns(null);
                    exmsdata.setChldqty(null);
                    exmsdata.setAbrqty(null);
                    exmsdata.setSlnkqty(null);
                    exmsdata.setStlqty(null);
                    exmsdata.setTrsqty(null);
                }
            }
        }
        if("1".equals(this.saveBasicMessageRst[3])){
            //重置检查结论的记录
            //源体检危害因素结论
            List<TdTjBadrsnsClt> oriBadrsn = this.service.selectBadrsnsCltListByBhkId(tdTjBhkClt.getRid());
            //体检危害因素发生变化，保留未修改的体检结论
            for(TdTjBadrsnsClt rsn : tdTjBhkClt.getTdTjBadrsnsClts()){
                for(TdTjBadrsnsClt badrsn : oriBadrsn){
                    if(rsn.getFkByBadrsnId().getRid().equals(badrsn.getFkByBadrsnId().getRid())){
                        //同危害因素，体检结论保留
                        rsn.setFkByExamConclusionId(badrsn.getFkByExamConclusionId());
                        rsn.setQtjbName(badrsn.getQtjbName());
                        break;
                    }
                }
            }
            //保留同主题结论
            Iterator<TdTjMhkrstClt> iterator = tdTjBhkClt.getTdTjMhkrstClts().iterator();
            while(iterator.hasNext()){
                TdTjMhkrstClt his = iterator.next();
                boolean found = false;
                for(TdTjBadrsnsClt rsn : tdTjBhkClt.getTdTjBadrsnsClts()){
                    if(rsn.getFkByExamConclusionId()!=null && rsn.getFkByExamConclusionId().getRid().equals(his.getFkByBhkrstId().getRid())){
                        found = true;
                        break;
                    }
                }
                if(!found){
                    iterator.remove();
                }
            }
            //只保留选择的体检危害因素的疑似职业病记录
            Iterator<TdTjSupoccdiseClt> iterator1 = tdTjBhkClt.getTdTjSupoccdiseClts().iterator();
            while(iterator1.hasNext()){
                TdTjSupoccdiseClt his = iterator1.next();
                boolean found = false;
                for(TdTjBadrsnsClt rsn : tdTjBhkClt.getTdTjBadrsnsClts()){
                    if(rsn.getFkByBadrsnId().getRid().equals(his.getFkByBadrsnId().getRid())){
                        found = true;
                        break;
                    }
                }
                if(!found){
                    iterator1.remove();
                }
            }
            //只保留选择的体检危害因素的职业禁忌证记录
            Iterator<TdTjContraindClt> iterator2 = tdTjBhkClt.getTdTjContraindClts().iterator();
            while(iterator2.hasNext()){
                TdTjContraindClt his = iterator2.next();
                boolean found = false;
                for(TdTjBadrsnsClt rsn : tdTjBhkClt.getTdTjBadrsnsClts()){
                    if(rsn.getFkByBadrsnId().getRid().equals(his.getFkByBadrsnId().getRid())){
                        found = true;
                        break;
                    }
                }
                if(!found){
                    iterator2.remove();
                }
            }
        }
        if(this.psnTypeMap.containsKey(this.editPsnTypeId) && "01".equals(this.psnTypeMap.get(this.editPsnTypeId).getCodeNo())){
            this.psnNameCache = this.tdTjBhkClt.getPersonName();
        }else{
            this.psnNameCache=null;
        }
        this.orgAidcAbhkdateCache = this.tdTjBhkClt.getFkByBhkorgId().getRid() + "&" + this.tdTjBhkClt.getFkByCardTypeId().getRid() + "&" + this.tdTjBhkClt.getIdc() + "&" + DateUtils.formatDate(this.tdTjBhkClt.getBhkDate(), "yyyy-MM-dd");
        service.saveOrUpdate(this.tdTjBhkClt);
        initOriValue();
    }

    public List<TdTjBhksubClt> pakBhkSubCltList() {
        //根据当前登录用户所在单位获取已提交的项目标准值配置
        Map<String, TbTjStadItems> stadItemsMap = this.getStadItemsMap(this.tdTjBhkClt.getSex());
        Map<String, TbTjItemsSpe> speItemsMapNoBadRsn = this.getSpeItemsMapNoBadRsn(this.tdTjBhkClt.getSex());
        Set<Integer> itemsRidSet = getAllItemsRidSet();
        //获取定性描述
        Map<Integer, List<TbTjRstdesc>> rstDescMap = this.getRstdesc();
        //获取原体检子表项目
        List<TdTjBhksubClt> bhkSubCltList = this.service.selectBhkSubCltListByBhkId(this.tdTjBhkClt.getRid());
        List<TdTjBhksubClt> oriBhkSubCltList = ObjectCopyUtil.deepCopy(bhkSubCltList);
        //处理原体检子表项目
        dealOriBhkSubCltList(oriBhkSubCltList, itemsRidSet, rstDescMap, stadItemsMap, speItemsMapNoBadRsn);
        //获取新体检子表必检项目
        List<TdTjBhksubClt> newBhkSubCltList = pakNewBhkSubCltList(rstDescMap, stadItemsMap, speItemsMapNoBadRsn);
        //合并新旧体检子表项目
        mergeBhkSubCltList(newBhkSubCltList, oriBhkSubCltList);
        return newBhkSubCltList;
    }

    private void dealOriBhkSubCltList(List<TdTjBhksubClt> oriBhkSubCltList,
                                      Set<Integer> itemsRidSet,
                                      Map<Integer, List<TbTjRstdesc>> rstDescMap,
                                      Map<String, TbTjStadItems> stadItemsMap,
                                      Map<String, TbTjItemsSpe> speItemsMapNoBadRsn) {
        StringBuilder allItemId = new StringBuilder();
        Integer sex = "男".equals(this.tdTjBhkClt.getSex()) ? 1 : 2;
        for (int i = oriBhkSubCltList.size() - 1; i >= 0; i--) {
            TdTjBhksubClt oriBhkSubClt = oriBhkSubCltList.get(i);
            TbTjItems tjItems = oriBhkSubClt.getFkByItemId();
            //删除停用的项目以及项目性别不是当前选择性别的记录
            if (tjItems == null || tjItems.getRid() == null || itemsRidSet.contains(tjItems.getRid())
                    || (tjItems.getSex() != null && !tjItems.getSex().equals(sex))) {
                oriBhkSubCltList.remove(i);
                continue;
            }
            oriBhkSubClt.setIsMust(0);
            allItemId.append(",").append(oriBhkSubClt.getFkByItemId().getRid());
        }
        //获取体检项目标准库（GBZ-188）
        List<Object[]> itemList =
                this.tbTjItemsService.selectItemListByItemCmbIdByRid(allItemId.substring(1), this.tdTjBhkClt.getSex());
        //封装到Map中
        Map<Integer, TdTjBhksubClt> bhkSubCltMap = new HashMap<>(16);
        List<TdTjBhksubClt> bhkSubCltList = pakBhkSubCltList(itemList, stadItemsMap, speItemsMapNoBadRsn, rstDescMap);
        for (TdTjBhksubClt newBhkSubClt : bhkSubCltList) {
            bhkSubCltMap.put(newBhkSubClt.getFkByItemId().getRid(), newBhkSubClt);
        }
        //处理原体检子表项目
        for (TdTjBhksubClt oriBhkSubClt : oriBhkSubCltList) {
            TdTjBhksubClt newBhkSubClt = bhkSubCltMap.get(oriBhkSubClt.getFkByItemId().getRid());
            //判断模式不同
            boolean needClear = oriBhkSubClt.getJdgptn() == null
                    || !oriBhkSubClt.getJdgptn().equals(newBhkSubClt.getJdgptn());
            oriBhkSubClt.setIsMust(0);
            oriBhkSubClt.setJdgptn(newBhkSubClt.getJdgptn());
            if (newBhkSubClt.getJdgptn() != null && newBhkSubClt.getJdgptn() == 2) {
                //计量单位不同
                needClear = needClear || newBhkSubClt.getFkByMsruntId() == null || oriBhkSubClt.getFkByMsruntId() == null
                        || !newBhkSubClt.getFkByMsruntId().getRid().equals(oriBhkSubClt.getFkByMsruntId().getRid());
                oriBhkSubClt.setFkByMsruntId(newBhkSubClt.getFkByMsruntId());
                oriBhkSubClt.setMsrunt(newBhkSubClt.getMsrunt());
                oriBhkSubClt.setMinval(newBhkSubClt.getMinval());
                oriBhkSubClt.setMaxval(newBhkSubClt.getMaxval());
                oriBhkSubClt.setItemStdvalue(newBhkSubClt.getItemStdvalue());
            } else {
                oriBhkSubClt.setMsrunt(null);
                oriBhkSubClt.setFkByMsruntId(null);
                oriBhkSubClt.setMinval(null);
                oriBhkSubClt.setMaxval(null);
                oriBhkSubClt.setItemStdvalue(null);
            }
            if (needClear) {
                oriBhkSubClt.setItemRst(null);
                oriBhkSubClt.setRgltag(1);
                oriBhkSubClt.setRstDesc(null);
                oriBhkSubClt.setRstFlag(null);
                oriBhkSubClt.setFkByDataVersionId(null);
                oriBhkSubClt.setFkByDataVersionOriId(null);
            }
            //非缺项及定量项目需要重新计算合格标记
            if (!new Integer(1).equals(oriBhkSubClt.getIfLack()) && oriBhkSubClt.getJdgptn() == 2) {
                calRglTagAndRstDesc(oriBhkSubClt);
            }
        }
    }

    private List<TdTjBhksubClt> pakNewBhkSubCltList(Map<Integer, List<TbTjRstdesc>> rstDescMap,
                                                    Map<String, TbTjStadItems> stadItemsMap,
                                                    Map<String, TbTjItemsSpe> speItemsMapNoBadRsn) {
        List<TdTjBhksubClt> newBhkSubCltList = new ArrayList<>();

        //获取必检项目组合-this.combMap
        geItemCombidListByBadrsnIdAndWorkStateIdNew();
        Map<String, Integer> combMap = this.getMustCombidList("1");
        if (ObjectUtil.isNotEmpty(combMap)) {
            String combids = this.getCombIdList("1");
            //获取体检项目标准库（GBZ-188）
            List<Object[]> itemList = this.tbTjItemsService.selectItemListByItemCmbId(combids, this.tdTjBhkClt.getSex());
            return pakBhkSubCltList(itemList, stadItemsMap, speItemsMapNoBadRsn, rstDescMap);
        }
        return newBhkSubCltList;
    }

    private void mergeBhkSubCltList(List<TdTjBhksubClt> newBhkSubCltList, List<TdTjBhksubClt> oriBhkSubCltList) {
        //封装到Map中
        Map<Integer, TdTjBhksubClt> bhkSubCltMap = new HashMap<>(16);
        for (TdTjBhksubClt oriBhkSubClt : oriBhkSubCltList) {
            bhkSubCltMap.put(oriBhkSubClt.getFkByItemId().getRid(), oriBhkSubClt);
        }
        for (int i = newBhkSubCltList.size() - 1; i >= 0; i--) {
            TdTjBhksubClt newBhkSubClt = newBhkSubCltList.get(i);
            if (bhkSubCltMap.containsKey(newBhkSubClt.getFkByItemId().getRid())) {
                TdTjBhksubClt oriBhkSubClt = bhkSubCltMap.get(newBhkSubClt.getFkByItemId().getRid());
                oriBhkSubClt.setIsMust(1);
                newBhkSubCltList.remove(newBhkSubClt);
            }
        }
        newBhkSubCltList.addAll(oriBhkSubCltList);
    }

    private List<TdTjBhksubClt> pakBhkSubCltList(List<Object[]> itemList,
                                                 Map<String, TbTjStadItems> stadItemsMap,
                                                 Map<String, TbTjItemsSpe> speItemsMapNoBadRsn,
                                                 Map<Integer, List<TbTjRstdesc>> rstDescMap) {
        List<TdTjBhksubClt> bhkSubCltList = new ArrayList<>();
        if (ObjectUtil.isEmpty(itemList)) {
            return bhkSubCltList;
        }
        Map<Integer, TbTjItems> itemMap = new HashMap<>();
        List<TbTjItems> itemsList = new ArrayList<>();
        for (Object[] obj : itemList) {
            if (null == obj[0]) {
                continue;
            }
            Integer itemId = Integer.valueOf(obj[0].toString());
            if (!itemMap.containsKey(itemId)) {
                TbTjItems item = new TbTjItems();
                item.setRid(itemId);
                item.setItemCode(StringUtils.objectToString(obj[1]));
                item.setItemName(StringUtils.objectToString(obj[2]));
                item.setMsrunt(StringUtils.objectToString(obj[3]));
                if (!"".equals(StringUtils.objectToString(obj[15]))) {
                    item.setMsruntId(Integer.valueOf(StringUtils.objectToString(obj[15])));
                }
                item.setJdgptn(Short.parseShort(obj[4].toString()));
                item.setMinval(obj[5] == null ? null : new BigDecimal(obj[5].toString()));
                item.setMaxval(obj[6] == null ? null : new BigDecimal(obj[6].toString()));
                item.setItemStdvalue(StringUtils.objectToString(obj[7]));
                item.setDflt(StringUtils.objectToString(obj[8]));
                TsSimpleCode tsSimpleCode = new TsSimpleCode();
                tsSimpleCode.setRid(Integer.valueOf(obj[9].toString()));
                tsSimpleCode.setCodeName(StringUtils.objectToString(obj[10]));
                tsSimpleCode.setExtendS1(StringUtils.objectToString(obj[14]));
                item.setTsSimpleCode(tsSimpleCode);
                item.setIsMust(combMap.get(StringUtils.objectToString(obj[11])));
                item.setSex(obj[12] == null ? null : Integer.valueOf(obj[12].toString()));
                item.setItemTag(obj[13] == null ? null : Integer.valueOf(obj[13].toString()));
                itemsList.add(item);
                itemMap.put(Integer.parseInt(obj[0].toString()), item);
            }
        }

        for (TbTjItems tbTjItems : itemsList) {
            TdTjBhksubClt tdTjBhksubClt = new TdTjBhksubClt();
            tdTjBhksubClt.setCreateManid(Global.getUser().getRid());
            tdTjBhksubClt.setCreateDate(new Date());
            tdTjBhksubClt.setFkByBhkId(this.tdTjBhkClt);
            tdTjBhksubClt.setFkByItemId(tbTjItems);
            tdTjBhksubClt.setMsrunt(tbTjItems.getMsrunt());
            if (tbTjItems.getMsruntId() != null) {
                tdTjBhksubClt.setFkByMsruntId(new TsSimpleCode(tbTjItems.getMsruntId()));
            }
            if (null != tbTjItems.getMinval() && null != tbTjItems.getMaxval()) {
                tdTjBhksubClt.setItemStdvalue(tbTjItems.getMinval() + "-" + tbTjItems.getMaxval());
            }
            tdTjBhksubClt.setMinval(tbTjItems.getMinval());
            tdTjBhksubClt.setMaxval(tbTjItems.getMaxval());
            tdTjBhksubClt.setIsMust(tbTjItems.getIsMust());
            tdTjBhksubClt.setJdgptn(tbTjItems.getJdgptn());

            // 结果判定标记 0 未见异常
            if (new Integer(30).equals(tdTjBhksubClt.getFkByItemId().getItemTag())) {
                tdTjBhksubClt.setRstFlag(0);
            }
            tdTjBhksubClt.setIfrgltag(true);
            // 是否缺项 0 缺项 1 未缺(默认未缺)
            tdTjBhksubClt.setIfLack(0);
            tdTjBhksubClt.setLack(false);
            //检查日期默认体检日期
            tdTjBhksubClt.setChkdat(this.tdTjBhkClt.getBhkDate());

            // 根据单位设置-最大值、最小值、计量单位、判断模式
            if (null != stadItemsMap && stadItemsMap.size() > 0) {
                this.setBhkSubValueByOrg(tbTjItems, tdTjBhksubClt, stadItemsMap, speItemsMapNoBadRsn);
            }

            // 是否合格 1 合格 0 不合格(默认合格)
            tdTjBhksubClt.setRgltag(1);
            List<TbTjRstdesc> tbTjRstdescList = rstDescMap.get(tbTjItems.getRid());
            if (!CollectionUtils.isEmpty(tbTjRstdescList)) {
                for (TbTjRstdesc tbTjRstdesc : tbTjRstdescList) {
                    if (tbTjRstdesc.getEgbTag() == 1 && tdTjBhksubClt.getJdgptn() == 1) {
                        tdTjBhksubClt.setItemRst(tbTjRstdesc.getRstDesc());
                        break;
                    }
                }
            }

            if (tdTjBhksubClt.getJdgptn().intValue() == 1) {
                tdTjBhksubClt.setMinval(null);
                tdTjBhksubClt.setMaxval(null);
                tdTjBhksubClt.setItemStdvalue(null);
                tdTjBhksubClt.setMsrunt(null);
                if (!CollectionUtils.isEmpty(rstDescMap.get(tbTjItems.getRid()))) {
                    tdTjBhksubClt.setTbTjRstdescList(rstDescMap.get(tbTjItems.getRid()));
                }
            } else {
                // 根据特殊标准-设置最大值、最小值、计量单位
                this.setBhkSubValueNew(tbTjItems, tdTjBhksubClt);
            }
            bhkSubCltList.add(tdTjBhksubClt);
        }
        return bhkSubCltList;
    }

    /**
     * @Description : 保存问诊项目（数据录入）
     * @MethodAuthor: anjing
     * @Date : 2019/5/20 9:51
     **/
    public void saveExmsData() {
        if(StringUtils.isNotBlank(this.editChkdocId)) {
            this.tdTjBhkClt.setFkByWzChkdoctId(new TdZwPsninfoComm(Integer.parseInt(this.editChkdocId)));
        }
        if(this.tdTjBhkClt.getSex().equals("女")) {
            if (this.tdTjExmsdataCltBean.isIsxmns()) {
                this.tdTjExmsdataCltBean.getTdTjExmsdataClt().setIsxmns(1);
            } else {
                this.tdTjExmsdataCltBean.getTdTjExmsdataClt().setIsxmns(0);
            }
        }
        //目前吸烟情况赋值
        if(this.tdTjExmsdataCltBean.getSmkSelRid()!=null){
            this.tdTjExmsdataCltBean.getTdTjExmsdataClt().setFkBySmkstaId(new TsSimpleCode(this.tdTjExmsdataCltBean.getSmkSelRid()));
        }else{
            this.tdTjExmsdataCltBean.getTdTjExmsdataClt().setFkBySmkstaId(null);
        }
        // 保存问诊项目信息
        this.tdTjBhkClt.getTdTjExmsdataClts().clear();
        if(this.tdTjExmsdataCltBean.isIsxmns()) {
            this.tdTjExmsdataCltBean.getTdTjExmsdataClt().setIsxmns(1);
        } else {
            this.tdTjExmsdataCltBean.getTdTjExmsdataClt().setIsxmns(0);
        }
        this.tdTjBhkClt.getTdTjExmsdataClts().add(this.tdTjExmsdataCltBean.getTdTjExmsdataClt());

        // 保存症状信息
        /*this.tdTjBhkClt.getTdTjSymptomClts().clear();
        for(String str : this.tdTjSymptomCltBean.getSelectSymIds()) {
            TdTjSymptomCltComm tdTjSymptomClt = new TdTjSymptomCltComm();
            tdTjSymptomClt.setCreateManid(Global.getUser().getRid());
            tdTjSymptomClt.setCreateDate(new Date());
            tdTjSymptomClt.setFkByBhkId(this.tdTjBhkClt);
            tdTjSymptomClt.setFkBySymId(new TsSimpleCode(Integer.parseInt(str)));
            TsSimpleCode tsSimpleCode = this.commService.find(TsSimpleCode.class, Integer.parseInt(str));
            if(null != tsSimpleCode && null != tsSimpleCode.getExtendS1()) {
                tdTjSymptomClt.setFkBySymId(tsSimpleCode);
                tdTjSymptomClt.setOthsym(this.tdTjSymptomCltBean.getOthsym());
            }
            this.tdTjBhkClt.getTdTjSymptomClts().add(tdTjSymptomClt);
        }*/
        this.tdTjBhkClt.getTdTjSymptomClts().clear();
        for(SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
            for(SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                if(symCodeCommPO.isIfSelected()) {
                    TdTjSymptomClt tdTjSymptomClt = new TdTjSymptomClt();
                    tdTjSymptomClt.setCreateManid(Global.getUser().getRid());
                    tdTjSymptomClt.setCreateDate(new Date());
                    tdTjSymptomClt.setFkByBhkId(this.tdTjBhkClt);
                    TsSimpleCode tsSimpleCode = new TsSimpleCode(symCodeCommPO.getRid());
                    tsSimpleCode.setCodeName(StringUtils.objectToString(symCodeCommPO.getCodeName()));
                    tsSimpleCode.setCodeNo(StringUtils.objectToString(symCodeCommPO.getCodeNo()));
                    tsSimpleCode.setExtendS1(StringUtils.objectToString(symCodeCommPO.getExtendS1()));
                    tdTjSymptomClt.setFkBySymId(tsSimpleCode);
                    if ("1".equals(symCodeCommPO.getExtendS1())) {
                        tdTjSymptomClt.setOthsym(this.tdTjSymptomCltBean.getOthsym());
                    }
                    this.tdTjBhkClt.getTdTjSymptomClts().add(tdTjSymptomClt);
                }
            }
        }
        //职业史/放射史
        this.tdTjBhkClt.getTdTjEmhistoryClts().clear();
        this.tdTjBhkClt.getTdTjEmhistoryClts().addAll(this.tdTjEmhistoryCltListBean.getEmploymentHisList());
        this.tdTjBhkClt.getTdTjEmhistoryClts().addAll(this.tdTjEmhistoryCltListBean.getRadiationHisList());
        //既往史
        this.tdTjBhkClt.getTdTjAnamnesisClts().clear();
        this.tdTjBhkClt.getTdTjAnamnesisClts().addAll(this.tdTjAnamnesisCltListBean.getAnamnesisHisList());
        // this.tdTjBhkClt.setTdTjAnamnesisClts(new ArrayList<TdTjAnamnesisClt>());
        this.service.saveExmsData(this.tdTjBhkClt);
    }

    /**
     * @Description : 保存问诊症状信息（数据录入）
     * @MethodAuthor: anjing
     * @Date : 2019/5/20 9:51
     **/
    public void saveSymptom() {
        this.tdTjBhkClt.getTdTjBhksubClts().clear();

        Integer integer1 = 1;
        for(TbTjItemSort tbTjItemSort : this.mustItemSortList) {
            for(TdTjBhksubClt tdTjBhksubClt : tbTjItemSort.getBhksubCltList()) {
                tdTjBhksubClt.setDiagRest(tbTjItemSort.getBhksubCltList().get(0).getDiagRest());
                if(null != tbTjItemSort.getBhksubCltList().get(0).getChkdoctId()) {
                    tdTjBhksubClt.setFkByChkdoctId(new TdZwPsninfoComm(tbTjItemSort.getBhksubCltList().get(0).getChkdoctId()));
                } else {
                    tdTjBhksubClt.setFkByChkdoctId(null);
                }
                tdTjBhksubClt.setChkdat(tbTjItemSort.getBhksubCltList().get(0).getChkdat());
                if(null != tdTjBhksubClt.getIfLack() && 1 == tdTjBhksubClt.getIfLack().intValue()) {
                    tdTjBhksubClt.setItemRstOri("未检");
                    tdTjBhksubClt.setItemRst("未检");
                    tdTjBhksubClt.setDataVersionId(null);
                    tdTjBhksubClt.setDataVersionOriId(null);
                    if(null!=tdTjBhksubClt.getJdgptn() && 2==tdTjBhksubClt.getJdgptn()){
                        tdTjBhksubClt.setRstDesc(0);
                    }
                }
                if (tdTjBhksubClt.getFkByItemId() == null
                        || tdTjBhksubClt.getFkByItemId().getTsSimpleCode() == null
                        || !"1".equals(tdTjBhksubClt.getFkByItemId().getTsSimpleCode().getExtendS1())
                        || !integer1.equals(this.tjStadItemsStatus.getAudioModel())
                        || tdTjBhksubClt.getFkByItemId().getItemTag() == null
                        || tdTjBhksubClt.getFkByItemId().getItemTag() < 3
                        || tdTjBhksubClt.getFkByItemId().getItemTag() > 14) {
                    tdTjBhksubClt.setDataVersionOriId(null);
                }
                if (null != tdTjBhksubClt.getJdgptn() && 1 == tdTjBhksubClt.getJdgptn()) {
                    tdTjBhksubClt.setDataVersionId(null);
                    tdTjBhksubClt.setDataVersionOriId(null);
                }
                if (ObjectUtil.isEmpty(tdTjBhksubClt.getDataVersionId())) {
                    tdTjBhksubClt.setFkByDataVersionId(null);
                } else {
                    tdTjBhksubClt.setFkByDataVersionId(new TsSimpleCode(tdTjBhksubClt.getDataVersionId()));
                }
                if (ObjectUtil.isEmpty(tdTjBhksubClt.getDataVersionOriId())) {
                    tdTjBhksubClt.setFkByDataVersionOriId(null);
                } else {
                    tdTjBhksubClt.setFkByDataVersionOriId(new TsSimpleCode(tdTjBhksubClt.getDataVersionOriId()));
                }
                this.tdTjBhkClt.getTdTjBhksubClts().add(tdTjBhksubClt);
            }
        }
        this.service.saveSymptom(this.tdTjBhkClt);
    }

    /**
     * @Description : 体检问诊-保存体检主表信息（数据录入）
     * @MethodAuthor: anjing
     * @Date : 2019/5/22 15:17
     **/
    public void saveBasicInfoTwo() {
        service.upsertEntity(this.tdTjBhkClt);
    }
    /******************************************** 结束保存 *********************************************/

    /******************************************** 开始初始化 *********************************************/
    /**
     * @Description : 根据选择危害因素判断是否包含放射因素
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 14:30
     **/
    private boolean ifContainsRadi() {
        boolean flag = false;
        // 获取放射危害因素列表
        List<TsSimpleCode> fsBadrsnsCltList = this.commService.findallSimpleCodesByTypeIdAndExtends1("5007", 1);
        for(TdTjBadrsnsClt tdTjBadrsnsClt : this.tdTjBhkClt.getTdTjBadrsnsClts()) {
            for(TsSimpleCode tsSimpleCode : fsBadrsnsCltList) {
                if(tdTjBadrsnsClt.getFkByBadrsnId().getRid().equals(tsSimpleCode.getRid())) {
                    flag = true;
                    break;
                }
            }
        }
        return flag;
    }

    /**
     * @Description : 职业史/放射史-参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 18:29
     **/
    private void initEmhistoryCltList() {
        this.tdTjEmhistoryCltListBean = new TdTjEmhistoryCltListCommBean();
        this.tdTjEmhistoryCltListBean.setFkByBhkId(this.tdTjBhkClt);
        // 未接触放射危害因素，则只出现职业史；接触了放射危害因素，则出现职业史、放射史
        if(this.ifContainsRadi()) {
            this.ifShowRad = 1;
        } else {
            this.ifShowRad = 0;
        }
        this.tdTjEmhistoryCltListBean.initParam(this.ifShowRad);
    }

    /**
     * @Description : 既往史-参数初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/18 18:30
     **/
    private void initAnamnesisCltList() {
        this.tdTjAnamnesisCltListBean = new TdTjAnamnesisCltListCommBean();
        this.tdTjAnamnesisCltListBean.setFkByBhkId(this.tdTjBhkClt);
        this.tdTjAnamnesisCltListBean.initParam();
    }

    /**
     * @Description : 问诊项目-初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 9:01
     **/
    public void initExmsdataClt() {
        this.tdTjExmsdataCltBean = new TdTjExmsdataCltCommBean();
        this.tdTjExmsdataCltBean.setFkByBhkId(this.tdTjBhkClt);
        this.tdTjExmsdataCltBean.addInit();
        this.tdTjExmsdataCltBean.initParam();
    }

    /**
     * @Description : 问诊症状-初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 16:28
     **/
    public void initSymptomClt() {
        this.tdTjSymptomCltBean = new TdTjSymptomCltCommBean();
        this.tdTjSymptomCltBean.setFkByBhkId(this.tdTjBhkClt);
        this.tdTjSymptomCltBean.addInit();
        this.tdTjSymptomCltBean.initParam();
    }

    /**
     * @Description : 检查医生下拉初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/19 17:35
     **/
    private void initChkDocMap() {
        List<Object[]> tdZwPsninfoList = this.tdZwPsninfoService.selectPsninfoListByPsnType("'1','2','3'");
        for(Object[] obj : tdZwPsninfoList) {
            this.editChkdocMap.put(StringUtils.objectToString(obj[1]), StringUtils.objectToString(obj[0]));
        }
    }

    /***
     *  <p>方法描述：主检医生医生列表</p>
     *
     * @MethodAuthor maox,2019年5月31日,initMainChkDocMap
     */
    private void initMainChkDocMap() {
        List<Object[]> tdZwPsninfoList = this.tdZwPsninfoService.selectPsninfoListByPsnType("'2'");
        for(Object[] obj : tdZwPsninfoList) {
            this.mainChkdocMap.put(StringUtils.objectToString(obj[1]), StringUtils.objectToString(obj[0]));
        }
    }

    /**
     * @Description : 选择症状
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 10:18
     **/
    public void selectCheckAction() {
        this.tdTjSymptomCltBean.setExtends1(null);
        if (null != this.tdTjSymptomCltBean.getSelectSymIds() && this.tdTjSymptomCltBean.getSelectSymIds().length > 0) {
            List<TdTjSymptomClt> list = new ArrayList<>();

            for (String id : this.tdTjSymptomCltBean.getSelectSymIds()) {
                TdTjSymptomClt tdTjSymptomClt = new TdTjSymptomClt();
                tdTjSymptomClt.setFkByBhkId(this.tdTjBhkClt);
                TsSimpleCode tsSimpleCode = this.commService.findTsSimpleCodeByRid(Integer.parseInt(id));
                if(null != tsSimpleCode && null != tsSimpleCode.getExtendS1()) {
                    this.tdTjSymptomCltBean.setExtends1(tsSimpleCode.getExtendS1());
                }
                tdTjSymptomClt.setFkBySymId(tsSimpleCode);
                list.add(tdTjSymptomClt);
            }
        }
        RequestContext context = RequestContext.getCurrentInstance();
        context.update("tabView:editTabView:exmsdataListForm:symptomGrid");
        context.update("tabView:editTabView:exmsdataListForm:symGrid");
    }

    /**
     * @Description : 根据危害因素Id、在岗状态Id，获取职业监护方案标准集合
     * @MethodAuthor: anjing
     * @Date : 2019/5/20 13:09
     **/
    private void getMainstdListByBadrsnIdAndWorkStateId() {
        StringBuilder schemeIdss = new StringBuilder();
        StringBuilder badrsnIds = new StringBuilder();
        for(TdTjBadrsnsClt tdTjBadrsnsClt : this.tdTjBhkClt.getTdTjBadrsnsClts()) {
            badrsnIds.append(",").append(tdTjBadrsnsClt.getFkByBadrsnId().getRid());
        }
        List<TbZwtjMainstd> list = this.tbZwtjMainstdService.selectMainstdListByBadrsnIdAndWorkStateId(badrsnIds.deleteCharAt(0).toString(), Integer.parseInt(this.editOnguadrStateId));
        for(TbZwtjMainstd tbZwtjMainstd : list) {
            schemeIdss.append(",").append(tbZwtjMainstd.getRid());
        }
        if(StringUtils.isNotBlank(schemeIdss)) {
            this.schemeIds = schemeIdss.deleteCharAt(0).toString();
        }
    }

    /**
     * @Description : 根据危害因素Id、在岗状态Id获取项目组合id、是否必检项目组合
     * @MethodAuthor: anjing
     * @Date : 2019/6/5 8:54
     **/
    private void geItemCombidListByBadrsnIdAndWorkStateId() {
        this.combMap = new HashMap<>();
        String codeNos = PropertyUtils.getValue("codeNos");
        StringBuilder badrsnIds = new StringBuilder();
        List<TdTjBadrsnsClt> badrsnsCltList = this.tdTjBadrsnsCltService.selectBadrsnsCltListByBhkId(this.tdTjBhkClt.getRid());
        for(TdTjBadrsnsClt tdTjBadrsnsClt : badrsnsCltList) {
            badrsnIds.append(",").append(tdTjBadrsnsClt.getFkByBadrsnId().getRid());
        }
        if(StringUtils.isBlank(badrsnIds)){
            return;
        }
        // 根据危害因素+在岗状态 查询项目组合id
        List<Object[]> combList =  this.tbZwtjMainstdService.selectCombidListByBadrsnIdAndWorkStateId(badrsnIds.deleteCharAt(0).toString(), Integer.parseInt(this.editOnguadrStateId), codeNos);
        for(Object[] obj : combList) {
            if(this.combMap.containsKey(obj[0].toString())) {
                // 多个危害因素对应同一项目组合，且必检状态不同，则只要有一项必检就必检
                if(null != obj[1] && Integer.parseInt(obj[1].toString()) == 1) {
                    this.combMap.put(obj[0].toString(), 1);
                }
            } else {

                this.combMap.put(obj[0].toString(), obj[1]==null?0:Integer.parseInt(obj[1].toString()));
            }
        }
    }

    /**
     * 根据危害因素Id、在岗状态Id获取项目组合id、是否必检项目组合(根据当前选择体检危害因素)
     */
    private void geItemCombidListByBadrsnIdAndWorkStateIdNew () {
        this.combMap = new HashMap<>();
        String codeNos = PropertyUtils.getValue("codeNos");
        StringBuilder badrsnIds = new StringBuilder();
        List<TdTjBadrsnsClt> badrsnsCltList = this.tdTjBhkClt.getTdTjBadrsnsClts();
        for(TdTjBadrsnsClt tdTjBadrsnsClt : badrsnsCltList) {
            badrsnIds.append(",").append(tdTjBadrsnsClt.getFkByBadrsnId().getRid());
        }
        if(StringUtils.isBlank(badrsnIds)){
            return;
        }
        List<Object[]> combList =  this.tbZwtjMainstdService.selectCombidListByBadrsnIdAndWorkStateId(badrsnIds.deleteCharAt(0).toString(), Integer.parseInt(this.editOnguadrStateId), codeNos);
        for(Object[] obj : combList) {
            if(this.combMap.containsKey(obj[0].toString())) {
                // 多个危害因素对应同一项目组合，且必检状态不同，则只要有一项必检就必检
                if(null != obj[1] && Integer.parseInt(obj[1].toString()) == 1) {
                    this.combMap.put(obj[0].toString(), 1);
                }
            } else {

                this.combMap.put(obj[0].toString(), obj[1]==null?0:Integer.parseInt(obj[1].toString()));
            }
        }
    }

    /**
     * @Description : 根据是否必检获取必检/不必检项目组合
     * @MethodAuthor: anjing
     * @Date : 2019/6/5 8:54
     **/
    private Map<String, Integer> getMustCombidList(String isMust) {
        Map<String, Integer> mustCombMap = new HashMap<>();
        if(null != this.combMap && this.combMap.size() > 0) {
            for (Map.Entry entry : this.combMap.entrySet()) {
                if (entry.getValue().toString().equals(isMust)) {
                    mustCombMap.put(entry.getKey().toString(), (Integer) entry.getValue());
                }
            }
        }
        return mustCombMap;
    }

    private String getCombIdList(String isMust) {
        Map<String, Integer> combMap = new HashMap<>();
        if (this.tdTjBhkClt.getIfRhk() == 1) { // 复检：初始化所有项目组合
            combMap = this.combMap;
        } else { // 初检：初始化不必检项目组合
            combMap = getMustCombidList(isMust);
        }
        if (null != combMap && combMap.size() > 0) {
            StringBuilder combids = new StringBuilder();
            for (Map.Entry entry : combMap.entrySet()) {
                combids.append(",").append(entry.getKey().toString());
            }
            return combids.deleteCharAt(0).toString();
        }
        return null;
    }

    /**
     * @Description : 添加项目组合初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/22 13:58
     **/
    public void addSchemeItemsInit() {
        this.itemCmbIds = new StringBuilder();
        this.selectCount = 0;
        this.searchItemCode = null;
        this.searchItemName = null;
        this.selectedItemList = new LinkedList<TbZwtjSchemeItems>();
        this.filterSelectItemList = new ArrayList<>();
        this.gbz188ItemList = new ArrayList<>();
        this.freedomItemList = new ArrayList<>();
        String combids = this.getCombIdList("0");
        if(StringUtils.isNotBlank(combids)) {
            // 根据项目组合id获取项目组合列表
            this.gbz188ItemList = this.tbZwtjSchemeItemsService.selectSchemeItemsByCombIds2(combids);
            if(null == this.gbz188ItemList){
                this.gbz188ItemList = new ArrayList<>();
            }
        }
        String codeNos = PropertyUtils.getValue("codeNos");//问诊的编码
        List<TbZwtjSchemeItems> tmpFreedomItemList = this.tbZwtjSchemeItemsService.selectSchemeItemsByCombIds3(codeNos);
        if(!CollectionUtils.isEmpty(tmpFreedomItemList)){
            String comIds = this.getCombIdList("1");
            List<TbZwtjSchemeItems> mustItemList = this.tbZwtjSchemeItemsService.selectSchemeItemsByCombIds2(comIds);
            Map<Integer, TbZwtjSchemeItems> mustItemsMap = new HashMap<>();
            if(!CollectionUtils.isEmpty(mustItemList)){
                for(TbZwtjSchemeItems schemeItems : mustItemList){
                    if (!mustItemsMap.containsKey(schemeItems.getTsSimpleCode().getRid())) {
                        mustItemsMap.put(schemeItems.getTsSimpleCode().getRid(), schemeItems);
                    }
                }
                List<TbZwtjSchemeItems> removeItemList = new ArrayList<>();
                for(TbZwtjSchemeItems schemeItems : tmpFreedomItemList){
                    if (mustItemsMap.containsKey(schemeItems.getTsSimpleCode().getRid())) {
                        removeItemList.add(schemeItems);
                    }
                }
                if(!CollectionUtils.isEmpty(removeItemList)){
                    tmpFreedomItemList.removeAll(removeItemList);
                }
            }
        }
        this.selectItemList = CollectionUtils.isEmpty(gbz188ItemList) ? tmpFreedomItemList : gbz188ItemList;
        this.ifOnlyGBZ188 = CollectionUtils.isEmpty(gbz188ItemList) ? false : true;
        Map<Integer, TbZwtjSchemeItems> schemeItemsMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(selectItemList)){
            for (TbZwtjSchemeItems tbZwtjSchemeItems : this.selectItemList) {
                if (!schemeItemsMap.containsKey(tbZwtjSchemeItems.getTsSimpleCode().getRid())) {
                    schemeItemsMap.put(tbZwtjSchemeItems.getTsSimpleCode().getRid(), tbZwtjSchemeItems);
                    this.filterSelectItemList.add(tbZwtjSchemeItems);
                }
            }
        }
        //让对象保持一致
        if(!CollectionUtils.isEmpty(tmpFreedomItemList)){
            for (TbZwtjSchemeItems tbZwtjSchemeItems : tmpFreedomItemList) {
                TbZwtjSchemeItems tmpItems = CollectionUtils.isEmpty(gbz188ItemList) ? null :
                        schemeItemsMap.get(tbZwtjSchemeItems.getTsSimpleCode().getRid());
                if (null != tmpItems) {
                    freedomItemList.add(tmpItems);
                }else{
                    freedomItemList.add(tbZwtjSchemeItems);
                }
            }
        }
        if(CollectionUtils.isEmpty(selectItemList)){
            JsfUtil.addSuccessMessage("未查询到项目组合信息");
        }else{
            // 弹出待选框
            RequestContext.getCurrentInstance().execute("PF('schemeItemsDialog').show();");
            DataTable dataTable = (DataTable) FacesContext
                    .getCurrentInstance().getViewRoot()
                    .findComponent("tabView:editTabView:bhkSubListForm:selectItemListTable");
            dataTable.setFirst(0);
        }
    }

    /**
     * @Description : 根据查询条件过滤可选择的项目
     * @MethodAuthor: anjing
     * @Date : 2019/5/22 13:59
     **/
    public void filterSelectItem() {
        if(ifOnlyGBZ188){
            this.selectItemList = gbz188ItemList;
        }else{
            this.selectItemList = freedomItemList;
        }
        if(!CollectionUtils.isEmpty(this.selectedItemList) && !CollectionUtils.isEmpty(this.selectItemList)){
            this.selectItemList.removeAll(this.selectedItemList);
        }
        if(null == this.selectItemList){
            this.selectItemList = new ArrayList<>();
        }
        // 如果两个查询输入框都为空则不做过滤
        if ((null == this.searchItemCode || this.searchItemCode.trim().length() <= 0)
                || CollectionUtils.isEmpty(this.selectItemList)) {
            this.filterSelectItemList = this.selectItemList;
        } else {
            this.filterSelectItemList = new LinkedList<TbZwtjSchemeItems>();
            for (TbZwtjSchemeItems tbZwtjSchemeItems : this.selectItemList) {
                boolean codeFlag = false;
                boolean nameFlag = false;
                String splsht = tbZwtjSchemeItems.getTsSimpleCode().getSplsht();
                String codeName = tbZwtjSchemeItems.getTsSimpleCode().getCodeName();
                //这里 名称与编码 共用searchItemCode
                // 如果拼音码为空或拼音码符合条件
                if (null == this.searchItemCode || this.searchItemCode.trim().length() <= 0
                        || splsht.toUpperCase().indexOf(this.searchItemCode.toUpperCase().trim()) > -1) {
                    codeFlag = true;
                }
                // 如果名称为空或名称符合条件
                if (null == this.searchItemCode || this.searchItemCode.trim().length() <= 0
                        || codeName.indexOf(this.searchItemCode.trim()) > -1) {
                    nameFlag = true;
                }

                // 如果拼音码和名称的条件有一个成立就行
                if (codeFlag || nameFlag) {
                    this.filterSelectItemList.add(tbZwtjSchemeItems);
                }
            }
        }
    }

    /**
     * @Description : 选择项目组合
     * @MethodAuthor: anjing
     * @Date : 2019/5/22 13:59
     **/
    public void selectItemAction() {
        if (null == this.selectedItemList) {
            this.selectedItemList = new LinkedList<TbZwtjSchemeItems>();
        }
        this.selectedItemList.add(this.selectTbzwItems);
        this.filterSelectItemList.remove(this.selectTbzwItems);
        this.itemCmbIds.append(",").append(this.selectTbzwItems.getTsSimpleCode().getRid());
        this.selectCount += 1;
    }

    /**
     * @Description : 保存项目组合列表
     * @MethodAuthor: anjing
     * @Date : 2019/5/22 14:02
     **/
    public void saveSchemeItems() {
        Map<Integer, List<TbTjRstdesc>> rstdescMap = this.getRstdesc();
        // 根据当前登录用户所在单位获取已提交的项目标准值配置
        Map<String, TbTjStadItems> stadItemsMap = this.getStadItemsMap(this.tdTjBhkClt.getSex());
        Map<String, TbTjItemsSpe> speItemsMapNoBadRsn = this.getSpeItemsMapNoBadRsn(this.tdTjBhkClt.getSex());

        // 这边去掉原先判断combMap是否为空的条件 因如果没有维护危害因素体检项目组合combMap 会是空Map
        // 但这里需要将GBZ188外的体检项目组合选择进去
        List<TbTjItems> list = new ArrayList<>();
        List<Object[]> itemsList = new ArrayList<>();
        Map<Integer, TbTjItems> itemsMap = new HashMap<>();
        if (null != this.selectedItemList && this.selectedItemList.size() > 0) {
            itemsList = this.tbTjItemsService.selectItemListByItemCmbId(this.itemCmbIds.deleteCharAt(0).toString(), this.tdTjBhkClt.getSex());
            for (Object[] obj : itemsList) {
                Integer itemId = Integer.valueOf(obj[0].toString());
                if(!itemsMap.containsKey(Integer.parseInt(obj[0].toString()))) {
                    TbTjItems item = new TbTjItems();
                    item.setRid(Integer.valueOf(obj[0].toString()));
                    item.setItemCode(StringUtils.objectToString(obj[1]));
                    item.setItemName(StringUtils.objectToString(obj[2]));
                    item.setMsrunt(StringUtils.objectToString(obj[3]));
                    if (!"".equals(StringUtils.objectToString(obj[15]))) {
                        item.setMsruntId(Integer.valueOf(StringUtils.objectToString(obj[15])));
                    }
                    item.setJdgptn(Short.valueOf(obj[4].toString()));
                    item.setMinval(obj[5] == null ? null : new BigDecimal(obj[5].toString()));
                    item.setMaxval(obj[6] == null ? null : new BigDecimal(obj[6].toString()));
                    item.setItemStdvalue(StringUtils.objectToString(obj[7]));
                    item.setDflt(StringUtils.objectToString(obj[8]));
                    TsSimpleCode tsSimpleCode = new TsSimpleCode();
                    tsSimpleCode.setRid(Integer.valueOf(obj[9].toString()));
                    tsSimpleCode.setCodeName(StringUtils.objectToString(obj[10]));
                    tsSimpleCode.setExtendS1(StringUtils.objectToString(obj[14]));
                    item.setTsSimpleCode(tsSimpleCode);
                    item.setItemTag(obj[13] == null ? null : Integer.valueOf(obj[13].toString()));
                    item.setSex(obj[12] == null ? null : Integer.valueOf(obj[12].toString()));
                    //加入标记
                    Integer itmTagId = null == obj[16] ? null : Integer.parseInt(obj[16].toString());
                    if(null != itmTagId){
                        TsSimpleCode itmTag = new TsSimpleCode(itmTagId);
                        itmTag.setExtendS3(null == obj[17] ? null : obj[17].toString());
                        item.setItemTagId(itmTag);
                    }
                    if (this.tdTjBhkClt.getIfRhk() == 1) {
                        item.setIsMust(0);
                    } else {
                        item.setIsMust((CollectionUtils.isEmpty(combMap) ||
                                null == combMap.get(StringUtils.objectToString(obj[11]))) ? 0 :
                                combMap.get(StringUtils.objectToString(obj[11])));
                    }
                    list.add(item);
                    itemsMap.put(Integer.parseInt(obj[0].toString()), item);
                }
            }
        }

        for(TbTjItems tbTjItems : list) {
            if(!this.itemsMap.containsKey(tbTjItems.getRid())) {
                TdTjBhksubClt tdTjBhksubClt = new TdTjBhksubClt();
                tdTjBhksubClt.setCreateManid(Global.getUser().getRid());
                tdTjBhksubClt.setCreateDate(new Date());
                tdTjBhksubClt.setFkByBhkId(this.tdTjBhkClt);
                tdTjBhksubClt.setFkByItemId(tbTjItems);
                tdTjBhksubClt.setFkByMsruntId(tbTjItems.getMruntId());
                tdTjBhksubClt.setMsrunt(tbTjItems.getMsrunt());
                if (tbTjItems.getMsruntId() != null) {
                    tdTjBhksubClt.setFkByMsruntId(new TsSimpleCode(tbTjItems.getMsruntId()));
                }
                tdTjBhksubClt.setMinval(tbTjItems.getMinval());
                tdTjBhksubClt.setMaxval(tbTjItems.getMaxval());
                tdTjBhksubClt.setIsMust(tbTjItems.getIsMust());
                tdTjBhksubClt.setJdgptn(tbTjItems.getJdgptn());
                if(null != tbTjItems.getMinval() && null != tbTjItems.getMaxval()) {
                    tdTjBhksubClt.setItemStdvalue(tbTjItems.getMinval() + "-" + tbTjItems.getMaxval());
                }
                //tdTjBhksubClt.setItemStdvalue(tbTjItems.getItemStdvalue());

                // 结果判定标记 0 未见异常
                if(null != tdTjBhksubClt.getFkByItemId().getItemTag() && tdTjBhksubClt.getFkByItemId().getItemTag().intValue() == 30) {
                    tdTjBhksubClt.setRstFlag(0);
                }
                tdTjBhksubClt.setIfrgltag(true);
                // 是否缺项 0 缺项 1 未缺(默认未缺)
                tdTjBhksubClt.setIfLack(0);
                tdTjBhksubClt.setLack(false);
                /**检查日期默认体检日期*/
                tdTjBhksubClt.setChkdat(tdTjBhkClt.getBhkDate());

                // 根据单位设置-最大值、最小值、计量单位
                if(null != stadItemsMap && stadItemsMap.size() > 0) {
                    this.setBhkSubValueByOrg(tbTjItems, tdTjBhksubClt, stadItemsMap, speItemsMapNoBadRsn);
                }

                // 是否合格 1 合格 0 不合格(默认合格)
                tdTjBhksubClt.setRgltag(1);
                List<TbTjRstdesc> tbTjRstdescList=rstdescMap.get(tbTjItems.getRid());
                if(!CollectionUtils.isEmpty(tbTjRstdescList)){
                    for (TbTjRstdesc tbTjRstdesc : tbTjRstdescList) {
                        if(tbTjRstdesc.getEgbTag()==1 && tdTjBhksubClt.getJdgptn() == 1){
                            tdTjBhksubClt.setItemRst(tbTjRstdesc.getRstDesc());
                            break;
                        }
                    }
                }

                if(tdTjBhksubClt.getJdgptn().intValue()==1){
                    tdTjBhksubClt.setMinval(null);
                    tdTjBhksubClt.setMaxval(null);
                    tdTjBhksubClt.setItemStdvalue(null);
                    tdTjBhksubClt.setMsrunt(null);
                    if (null != rstdescMap) {
                        if (!CollectionUtils.isEmpty(rstdescMap.get(tbTjItems.getRid()))) {
                            tdTjBhksubClt.setTbTjRstdescList(rstdescMap.get(tbTjItems.getRid()));
                        }
                    }
                }else {
                    // 根据特殊标准-设置最大值、最小值、计量单位
                    this.setBhkSubValue(tbTjItems, tdTjBhksubClt);
                }
                this.bhksubCltList.add(tdTjBhksubClt);
                itemsMap.put(tbTjItems.getRid(), tbTjItems);
            }
        }
        this.initItemSort();
        RequestContext.getCurrentInstance().execute("PF('schemeItemsDialog').hide();");
    }
    /**
     * @Description : 移除项目
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 16:31
     **/
    public void deleteBhkSubClt() {
        if(this.opBhkSubClt.getIsMust() == 1) {
            JsfUtil.addErrorMessage("必检项目不能删除！");
            return;
        }
        if(null != this.opBhkSubClt.getRid()) {
            this.service.deleteBhkSubById(this.opBhkSubClt.getRid());
        }
        this.bhksubCltList.remove(this.opBhkSubClt);
        this.itemsMap.remove(this.opBhkSubClt.getFkByItemId().getRid());
        this.initItemSort();
        JsfUtil.addSuccessMessage("删除成功！");
    }

    /**
     * @Description : 获取体检子表
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 10:19
     **/
    private void getBhksubCltList() {
        this.itemsMap = new HashMap<>();
        this.bhksubCltList = new ArrayList<>();
        this.getMainstdListByBadrsnIdAndWorkStateId();
        Map<Integer, List<TbTjRstdesc>> rstdescMap = this.getRstdesc();
        List<TdTjBhksubClt> tdTjBhksubCltList = this.service.selectBhkSubCltListByBhkId(this.tdTjBhkClt.getRid());
        Map<Integer, TbTjItems> itemMap = new HashMap<>();

        // 根据当前登录用户所在单位获取已提交的项目标准值配置
        Map<String, TbTjStadItems> stadItemsMap = this.getStadItemsMap(this.tdTjBhkClt.getSex());
        Map<String, TbTjItemsSpe> speItemsMapNoBadRsn = this.getSpeItemsMapNoBadRsn(this.tdTjBhkClt.getSex());
        //参数开启并且初检时
        if(ifItemMust && this.tdTjBhkClt.getIfRhk() != 1) {
            // 获取必检项目
            initCmbItemsMap();
        }
        if(CollectionUtils.isEmpty( tdTjBhksubCltList)) {
            if(this.tdTjBhkClt.getIfRhk() != 1) {
                // 获取必检项目
                //List<TbTjItems> itemsList = this.tbTjItemsService.selectItemListBySchemeIdsAndCodeNosNotIn(this.schemeIds, codeNos, "1");
                Map<String, Integer> combMap = this.getMustCombidList("1");
                if(null != combMap && combMap.size() > 0) {
                    List<TbTjItems> itemsList = new ArrayList<>();
                    String combids = this.getCombIdList("1");
                    List<Object[]> itemList = this.tbTjItemsService.selectItemListByItemCmbId(combids, this.tdTjBhkClt.getSex());

                    for (Object[] obj : itemList) {
                    	if(null==obj[0]){
                    		continue;
                    	}
                    	Integer itemId = Integer.valueOf(obj[0].toString());
                        if(!itemMap.containsKey(itemId)) {
                            TbTjItems item = new TbTjItems();
                            item.setRid(itemId);
                            item.setItemCode(StringUtils.objectToString(obj[1]));
                            item.setItemName(StringUtils.objectToString(obj[2]));
                            item.setMsrunt(StringUtils.objectToString(obj[3]));
                            if (!"".equals(StringUtils.objectToString(obj[15]))) {
                                item.setMsruntId(Integer.valueOf(StringUtils.objectToString(obj[15])));
                            }
                            item.setJdgptn(Short.valueOf(obj[4].toString()));
                            item.setMinval(obj[5] == null ? null : new BigDecimal(obj[5].toString()));
                            item.setMaxval(obj[6] == null ? null : new BigDecimal(obj[6].toString()));
                            item.setItemStdvalue(StringUtils.objectToString(obj[7]));
                            item.setDflt(StringUtils.objectToString(obj[8]));
                            TsSimpleCode tsSimpleCode = new TsSimpleCode();
                            tsSimpleCode.setRid(Integer.valueOf(obj[9].toString()));
                            tsSimpleCode.setCodeName(StringUtils.objectToString(obj[10]));
                            tsSimpleCode.setExtendS1(StringUtils.objectToString(obj[14]));
                            item.setTsSimpleCode(tsSimpleCode);
                            item.setIsMust(combMap.get(StringUtils.objectToString(obj[11])));
                            item.setSex(obj[12] == null ? null : Integer.valueOf(obj[12].toString()));
                            item.setItemTag(obj[13] == null ? null : Integer.valueOf(obj[13].toString()));
                            itemsList.add(item);
                            itemMap.put(Integer.parseInt(obj[0].toString()), item);
                        }
                    }

                    for (TbTjItems tbTjItems : itemsList) {
                        TdTjBhksubClt tdTjBhksubClt = new TdTjBhksubClt();
                        tdTjBhksubClt.setCreateManid(Global.getUser().getRid());
                        tdTjBhksubClt.setCreateDate(new Date());
                        tdTjBhksubClt.setFkByBhkId(this.tdTjBhkClt);
                        tdTjBhksubClt.setFkByItemId(tbTjItems);
                        tdTjBhksubClt.setMsrunt(tbTjItems.getMsrunt());
                        if (tbTjItems.getMsruntId() != null) {
                            tdTjBhksubClt.setFkByMsruntId(new TsSimpleCode(tbTjItems.getMsruntId()));
                        }
                        tdTjBhksubClt.setFkByMsruntId(tbTjItems.getMruntId());
                        if(null != tbTjItems.getMinval() && null != tbTjItems.getMaxval()) {
                            tdTjBhksubClt.setItemStdvalue(tbTjItems.getMinval() + "-" + tbTjItems.getMaxval());
                        }
                        tdTjBhksubClt.setMinval(tbTjItems.getMinval());
                        tdTjBhksubClt.setMaxval(tbTjItems.getMaxval());
                        tdTjBhksubClt.setIsMust(tbTjItems.getIsMust());
                        tdTjBhksubClt.setJdgptn(tbTjItems.getJdgptn());

                        // 结果判定标记 0 未见异常
                        if(null != tdTjBhksubClt.getFkByItemId().getItemTag() && tdTjBhksubClt.getFkByItemId().getItemTag().intValue() == 30) {
                            tdTjBhksubClt.setRstFlag(0);
                        }
                        tdTjBhksubClt.setIfrgltag(true);
                        // 是否缺项 0 缺项 1 未缺(默认未缺)
                        tdTjBhksubClt.setIfLack(0);
                        tdTjBhksubClt.setLack(false);
                        /**检查日期默认体检日期*/
                        tdTjBhksubClt.setChkdat(tdTjBhkClt.getBhkDate());

                        // 根据单位设置-最大值、最小值、计量单位、判断模式
                        if(null != stadItemsMap && stadItemsMap.size() > 0) {
                            this.setBhkSubValueByOrg(tbTjItems, tdTjBhksubClt, stadItemsMap, speItemsMapNoBadRsn);
                        }

                        // 是否合格 1 合格 0 不合格(默认合格)
                        tdTjBhksubClt.setRgltag(1);
                        List<TbTjRstdesc> tbTjRstdescList=rstdescMap.get(tbTjItems.getRid());
                        if(!CollectionUtils.isEmpty(tbTjRstdescList)){
                            for (TbTjRstdesc tbTjRstdesc : tbTjRstdescList) {
                                if(tbTjRstdesc.getEgbTag()==1 && tdTjBhksubClt.getJdgptn() == 1){
                                    tdTjBhksubClt.setItemRst(tbTjRstdesc.getRstDesc());
                                    break;
                                }
                            }
                        }

                        if(tdTjBhksubClt.getJdgptn().intValue()==1){
                        	tdTjBhksubClt.setMinval(null);
                        	tdTjBhksubClt.setMaxval(null);
                        	tdTjBhksubClt.setItemStdvalue(null);
                        	tdTjBhksubClt.setMsrunt(null);
                        	if (null != rstdescMap) {
                        		if (!CollectionUtils.isEmpty(rstdescMap.get(tbTjItems.getRid()))) {
                        			tdTjBhksubClt.setTbTjRstdescList(rstdescMap.get(tbTjItems.getRid()));
                        		}
                            }
                        }else {
                        	// 根据特殊标准-设置最大值、最小值、计量单位
                        	this.setBhkSubValue(tbTjItems, tdTjBhksubClt);
						}
                        this.bhksubCltList.add(tdTjBhksubClt);
                    }
                }
            }
        } else{
            for(TdTjBhksubClt tdTjBhksubClt : tdTjBhksubCltList) {
                if (null != rstdescMap) {
                    if (tdTjBhksubClt.getJdgptn() == 1) {
                        if (!CollectionUtils.isEmpty(rstdescMap.get(tdTjBhksubClt.getFkByItemId().getRid()))) {
                            tdTjBhksubClt.setTbTjRstdescList(rstdescMap.get(tdTjBhksubClt.getFkByItemId().getRid()));
                        }
                    }
                }
                tdTjBhksubClt.setIfrgltag(tdTjBhksubClt.getRgltag()==1?true:false);
                tdTjBhksubClt.setLack(tdTjBhksubClt.getIfLack()==1?true:false);
                if(null != tdTjBhksubClt.getFkByChkdoctId() && null != tdTjBhksubClt.getFkByChkdoctId().getRid()) {
                    tdTjBhksubClt.setChkdoctId(tdTjBhksubClt.getFkByChkdoctId().getRid());
                }
                this.bhksubCltList.add(tdTjBhksubClt);
            }
        }
    }
    /**
    * <p>Description：根据项目组合获取体检项目 </p>
    * <p>Author： yzz 2025/2/24 </p>
    */
    public List<Object[]> initCmbItemsMap() {
        this.cmbItemsMap = new HashMap<>();
        this.itemCodeSet=new ArrayList<>();
        String combids = this.getCombIdList("1");
        List<Object[]> list = this.tbTjItemsService.selectItemListByItemCmbId(combids, this.tdTjBhkClt.getSex());
        // 是否允许校验必检项目缺项 为不允许  同时是初检
        if (!CollectionUtils.isEmpty(list)) {
            for (Object[] obj : list) {
                // 初始化项目组合map  key:项目组合rid+判定方式  value：判定方式不等于空的体检项目code
                if (null != obj[11] && obj[19] != null) {
                    if (!this.cmbItemsMap.containsKey(obj[11] + "&" + obj[19])) {
                        this.cmbItemsMap.put(obj[11] + "&" + obj[19], new ArrayList<String>());
                    }
                    this.cmbItemsMap.get(obj[11] + "&" + obj[19]).add(obj[1].toString());
                    //同时满足的项目
                    if(obj[19] != null && "2".equals(obj[19].toString())){
                        this.itemCodeSet.add(obj[1].toString());
                    }
                }
            }
        }
        return list;
    }

    /**
    * <p>Description：缺项的体检项目追加 </p>
    * <p>Author： yzz 2025/2/25 </p>
    */
    public void refreshLackItems() {

        //根据当前登录用户所在单位获取已提交的项目标准值配置
        Map<String, TbTjStadItems> stadItemsMap = this.getStadItemsMap(this.tdTjBhkClt.getSex());
        Map<String, TbTjItemsSpe> speItemsMapNoBadRsn = this.getSpeItemsMapNoBadRsn(this.tdTjBhkClt.getSex());
        //获取定性描述
        Map<Integer, List<TbTjRstdesc>> rstDescMap = this.getRstdesc();
        //获取最新的体检子表
        List<TdTjBhksubClt> newBhkSubCltList = pakNewBhkSubCltList(rstDescMap, stadItemsMap, speItemsMapNoBadRsn);
        //合并新旧体检子表项目
        mergeBhkSubCltList(newBhkSubCltList,this.bhksubCltList);
        this.bhksubCltList.clear();
        this.bhksubCltList.addAll(newBhkSubCltList);
        sortBhksubClt();
        this.initItemSort();
    }


    /**
    * <p>Description：排序 </p>
    * <p>Author： yzz 2025/2/25 </p>
    */
    public void sortBhksubClt() {
        if (CollectionUtils.isEmpty(this.bhksubCltList)) {
            return;
        }
        Collections.sort(this.bhksubCltList, new Comparator<TdTjBhksubClt>() {
            @Override
            public int compare(TdTjBhksubClt o1, TdTjBhksubClt o2) {
                // 获取并处理第一个比较字段：tsSimpleCode.num
                Integer num1 = getTsSimpleCodeNum(o1);
                Integer num2 = getTsSimpleCodeNum(o2);

                int result = nullSafeCompare(num1, num2);

                if (result == 0) {
                    // 如果num相等，比较tsSimpleCode.codeNo
                    String codeNo1 = getTsSimpleCodeCodeNo(o1);
                    String codeNo2 = getTsSimpleCodeCodeNo(o2);

                    result = nullSafeCompare(codeNo1, codeNo2);
                }

                if (result == 0) {
                    // 如果codeNo也相等，比较num
                    Integer itemNum1 = getItemNum(o1);
                    Integer itemNum2 = getItemNum(o2);

                    result = nullSafeCompare(itemNum1, itemNum2);
                }

                if (result == 0) {
                    // 如果以上都相等，比较itemCode
                    String itemCode1 = getItemCode(o1);
                    String itemCode2 = getItemCode(o2);

                    result = nullSafeCompare(itemCode1, itemCode2);
                }

                return result;
            }

            private Integer getTsSimpleCodeNum(TdTjBhksubClt obj) {
                return obj != null && obj.getFkByItemId() != null && obj.getFkByItemId().getTsSimpleCode() != null
                        ? obj.getFkByItemId().getTsSimpleCode().getNum() : null;
            }

            private String getTsSimpleCodeCodeNo(TdTjBhksubClt obj) {
                return obj != null && obj.getFkByItemId() != null && obj.getFkByItemId().getTsSimpleCode() != null
                        ? obj.getFkByItemId().getTsSimpleCode().getCodeNo() : null;
            }

            private Integer getItemNum(TdTjBhksubClt obj) {
                return obj != null && obj.getFkByItemId() != null
                        ? obj.getFkByItemId().getNum() : null;
            }

            private String getItemCode(TdTjBhksubClt obj) {
                return obj != null && obj.getFkByItemId() != null
                        ? obj.getFkByItemId().getItemCode() : null;
            }

            private int nullSafeCompare(Integer a, Integer b) {
                if (a == null && b == null) {
                    return 0;
                }
                if (a == null) {
                    return 1; // null排在后面
                }
                if (b == null) {
                    return -1; // null排在后面
                }
                return a.compareTo(b);
            }

            private int nullSafeCompare(String a, String b) {
                if (a == null && b == null) {
                    return 0;
                }
                if (a == null) {
                    return 1; // null排在后面
                }
                if (b == null) {
                    return -1; // null排在后面
                }
                return a.compareTo(b);
            }
        });
    }

    /**
     * @Description : 设置最大值、最小值、计量单位
     * @MethodAuthor: anjing
     * @Date : 2019/5/27 17:38
     **/
    private void setBhkSubValue(TbTjItems tbTjItems, TdTjBhksubClt tdTjBhksubClt) {
        StringBuilder badrsnIds = new StringBuilder();
        for(TdTjBadrsnsClt tdTjBadrsnsClt : this.tdTjBhkClt.getTdTjBadrsnsClts()) {
            badrsnIds.append(",").append(tdTjBadrsnsClt.getFkByBadrsnId().getRid());
        }

        int badrsnsNum = this.service.countBadrsnsIdByBhkId(this.tdTjBhkClt.getRid());
        // 特殊标准
        // 当前项目关联特殊标准数量
        int speBadrsnsNum = this.service.countBadrsnsIdByBadrsnIdAndOnguardStateIdAndItemIdAndSex(tbTjItems.getRid(), badrsnIds.deleteCharAt(0).toString(), Integer.parseInt(this.editOnguadrStateId), this.tdTjBhkClt.getSex());
        List<TbTjItemsSpe> tjItemsSpeList = this.service.selectItemSpeListByBadrsnIdAndOnguardStateIdAndItemIdAndSex(tbTjItems.getRid(), badrsnIds.toString(), Integer.parseInt(this.editOnguadrStateId), this.tdTjBhkClt.getSex());
        if(!CollectionUtils.isEmpty(tjItemsSpeList)) {
            // 接触危害因素个数与特殊标准中危害因素不一致时，优先取单位配置的标准，之后取通用标准合并处理
            // 如：甲同时接触危害因素A、危害因素B，危害因素A维护特殊标准，则取危害因素B通用标准及危害因素A特殊标准合并后生成最小值、最大值
            if (badrsnsNum != speBadrsnsNum) {
                if(null != tdTjBhksubClt.getMinval() && null != tdTjBhksubClt.getMaxval()) {
                    TbTjItemsSpe tbTjItemsSpe = new TbTjItemsSpe();
                    tbTjItemsSpe.setMinval(tdTjBhksubClt.getMinval());
                    tbTjItemsSpe.setMaxval(tdTjBhksubClt.getMaxval());
                    tjItemsSpeList.add(tbTjItemsSpe);
                }
            }
            // 设置最小值
            tdTjBhksubClt.setMinval(this.getMinVale(tjItemsSpeList));
            // 设置最大值
            tdTjBhksubClt.setMaxval(this.getMaxVale(tjItemsSpeList));
            // 设置参考值
            if(null != this.getMinVale(tjItemsSpeList) && null != this.getMaxVale(tjItemsSpeList)) {
                tdTjBhksubClt.setItemStdvalue(this.getMinVale(tjItemsSpeList) + "-" + this.getMaxVale(tjItemsSpeList));
            }
        }
    }

    /**
     * @Description : 设置最大值、最小值、计量单位
     * @MethodAuthor: anjing
     * @Date : 2019/5/27 17:38
     **/
    private void setBhkSubValueNew(TbTjItems tbTjItems, TdTjBhksubClt tdTjBhksubClt) {
        StringBuilder badrsnIds = new StringBuilder();
        Set<Integer> badrsnIdSet = new HashSet<>();
        for (TdTjBadrsnsClt tdTjBadrsnsClt : this.tdTjBhkClt.getTdTjBadrsnsClts()) {
            badrsnIds.append(",").append(tdTjBadrsnsClt.getFkByBadrsnId().getRid());
            Integer badrsnId = tdTjBadrsnsClt.getFkByBadrsnId().getRid();
            badrsnIdSet.add(badrsnId);
        }
        int badrsnsNum = 0;
        for (Integer ignored : badrsnIdSet) {
            badrsnsNum++;
        }
        // 特殊标准
        // 当前项目关联特殊标准数量
        int speBadrsnsNum = this.service.countBadrsnsIdByBadrsnIdAndOnguardStateIdAndItemIdAndSex(tbTjItems.getRid(), badrsnIds.deleteCharAt(0).toString(), Integer.parseInt(this.editOnguadrStateId), this.tdTjBhkClt.getSex());
        List<TbTjItemsSpe> tjItemsSpeList = this.service.selectItemSpeListByBadrsnIdAndOnguardStateIdAndItemIdAndSex(tbTjItems.getRid(), badrsnIds.toString(), Integer.parseInt(this.editOnguadrStateId), this.tdTjBhkClt.getSex());
        if(!CollectionUtils.isEmpty(tjItemsSpeList)) {
            // 接触危害因素个数与特殊标准中危害因素不一致时，优先取单位配置的标准，之后取通用标准合并处理
            // 如：甲同时接触危害因素A、危害因素B，危害因素A维护特殊标准，则取危害因素B通用标准及危害因素A特殊标准合并后生成最小值、最大值
            if (badrsnsNum != speBadrsnsNum) {
                if(null != tdTjBhksubClt.getMinval() && null != tdTjBhksubClt.getMaxval()) {
                    TbTjItemsSpe tbTjItemsSpe = new TbTjItemsSpe();
                    tbTjItemsSpe.setMinval(tdTjBhksubClt.getMinval());
                    tbTjItemsSpe.setMaxval(tdTjBhksubClt.getMaxval());
                    tjItemsSpeList.add(tbTjItemsSpe);
                }
            }
            // 设置最小值
            tdTjBhksubClt.setMinval(this.getMinVale(tjItemsSpeList));
            // 设置最大值
            tdTjBhksubClt.setMaxval(this.getMaxVale(tjItemsSpeList));
            // 设置参考值
            if(null != this.getMinVale(tjItemsSpeList) && null != this.getMaxVale(tjItemsSpeList)) {
                tdTjBhksubClt.setItemStdvalue(this.getMinVale(tjItemsSpeList) + "-" + this.getMaxVale(tjItemsSpeList));
            }
        }
    }

    /**
     * @Description : 获取集合中最小值
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 10:38
     **/
    private BigDecimal getMinVale(List<TbTjItemsSpe> itemSpeList) {
        return Collections.max(itemSpeList, new Comparator<TbTjItemsSpe>() {
            @Override
            public int compare(TbTjItemsSpe o1, TbTjItemsSpe o2) {
                return o1.getMinval().compareTo(o2.getMinval());
            }
        }).getMinval();
    }

    /**
     * @Description : 获取集合中的最大值
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 10:38
     **/
    private BigDecimal getMaxVale(List<TbTjItemsSpe> itemSpeList) {
        return Collections.min(itemSpeList, new Comparator<TbTjItemsSpe>() {
            @Override
            public int compare(TbTjItemsSpe o1, TbTjItemsSpe o2) {
                return o1.getMaxval().compareTo(o2.getMaxval());
            }
        }).getMaxval();
    }

    /**
     * @Description : 根据危害因素Id、在岗状态Id封装定性项目描述
     * @MethodAuthor: anjing
     * @Date : 2019/5/27 10:31
     **/
    public Map<Integer, List<TbTjRstdesc>> getRstdesc() {
        Map<Integer, List<TbTjRstdesc>> map = new HashMap<>();
        StringBuilder badrsnIds = new StringBuilder();
        for(TdTjBadrsnsClt tdTjBadrsnsClt : this.tdTjBhkClt.getTdTjBadrsnsClts()) {
            badrsnIds.append(",").append(tdTjBadrsnsClt.getFkByBadrsnId().getRid());
        }
        if(StringUtils.isNotBlank(badrsnIds)){
            List<TbTjRstdesc> tbTjRstdescList = this.service.selectRstdescListByBadrsnIdAndOnguardStateId(badrsnIds.deleteCharAt(0).toString(), Integer.parseInt(this.editOnguadrStateId));
            for(TbTjRstdesc tbTjRstdesc : tbTjRstdescList) {
                if(map.containsKey(tbTjRstdesc.getTbTjItems().getRid())) {
                    map.get(tbTjRstdesc.getTbTjItems().getRid()).add(tbTjRstdesc);
                } else {
                    List<TbTjRstdesc> list = new ArrayList<>();
                    list.add(tbTjRstdesc);
                    map.put(tbTjRstdesc.getTbTjItems().getRid(), list);
                }
            }
            return map;
        }else{
            return null;
        }

    }

    /**
     * @Description : 根据体检子表封装项目分组
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 11:19
     **/
    private void initItemSort() {
        // 把必检项目存入map，用于添加项目组合判断是否重复添加项目
        for(TdTjBhksubClt tdTjBhksubClt :  this.bhksubCltList) {
            this.itemsMap.put(tdTjBhksubClt.getFkByItemId().getRid(), tdTjBhksubClt.getFkByItemId());
        }

        // 根据项目分类封装项目
        Map<String, TbTjItemSort> map = new HashMap<>();
        TbTjItemSort tbTjItemSort = null;
        this.mustItemSortList = new ArrayList<>();
        for(TdTjBhksubClt bhksubClt : this.bhksubCltList) {
            if(null == bhksubClt.getFkByChkdoctId()) {
                TdZwPsninfoComm tdZwPsninfo = new TdZwPsninfoComm();
                bhksubClt.setFkByChkdoctId(tdZwPsninfo);
            }
            if (ObjectUtil.isEmpty(bhksubClt.getFkByDataVersionId())) {
                bhksubClt.setDataVersionId(this.dataVersionEqual);
            } else {
                bhksubClt.setDataVersionId(bhksubClt.getFkByDataVersionId().getRid());
            }
            if (ObjectUtil.isEmpty(bhksubClt.getFkByDataVersionOriId())) {
                bhksubClt.setDataVersionOriId(this.dataVersionEqual);
            } else {
                bhksubClt.setDataVersionOriId(bhksubClt.getFkByDataVersionOriId().getRid());
            }
            if(map.containsKey(bhksubClt.getFkByItemId().getTsSimpleCode().getRid().toString())) {
                tbTjItemSort = map.get(bhksubClt.getFkByItemId().getTsSimpleCode().getRid().toString());
                tbTjItemSort.getBhksubCltList().add(bhksubClt);
            } else {
                tbTjItemSort = new TbTjItemSort();
                List<TdTjBhksubClt> list = new ArrayList<>();
                tbTjItemSort.setRid(bhksubClt.getFkByItemId().getTsSimpleCode().getRid());
                tbTjItemSort.setCodeName(bhksubClt.getFkByItemId().getTsSimpleCode().getCodeName());
                tbTjItemSort.setExtendS1(bhksubClt.getFkByItemId().getTsSimpleCode().getExtendS1());
                list.add(bhksubClt);
                tbTjItemSort.setBhksubCltList(list);
                map.put(bhksubClt.getFkByItemId().getTsSimpleCode().getRid().toString(), tbTjItemSort);
                this.mustItemSortList.add(tbTjItemSort);
            }
        }

        // 处理是否显示结果判定
        if(!CollectionUtils.isEmpty(this.mustItemSortList)) {
            for(TbTjItemSort itemSort : this.mustItemSortList) {
                if(!CollectionUtils.isEmpty(itemSort.getBhksubCltList())) {
                    for(TdTjBhksubClt bhksubClt : itemSort.getBhksubCltList()) {
                        if(null != bhksubClt.getFkByItemId().getItemTag() && bhksubClt.getFkByItemId().getItemTag().intValue() == 30) {
                            itemSort.setIfShowRstFlag(true);
                        }
                    }
                }
            }
        }
    }

    /**
     * @Description : 合格/不合格change事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/30 9:02
     **/
    public void changeRgltag(TdTjBhksubClt dta,Integer index) {

        if(dta.isIfrgltag()) {
            boolean ifPass = getIsIfPass(dta);
            //项目结果值为合格
            dta.setRgltag(1);
            if(null != dta.getFkByItemId().getItemTag() && dta.getFkByItemId().getItemTag().intValue() == 30) {
                dta.setRstFlag(0);
            }
            if(!ifPass){
                passBhkSubClt = dta;
                //提示
                RequestContext.getCurrentInstance().execute("PF('ConfirmDialog"+index+"').show()");
            }
        } else {
            dta.setRstFlag(null);
            dta.setRgltag(0);
        }
    }
    /**
     *  <p>方法描述：项目不合格取消-改为不合格</p>
     * @MethodAuthor hsj 2022-08-24 9:32
     */
    public void changeSetRgltag() {
        passBhkSubClt.setIfrgltag(false);
        passBhkSubClt.setRstFlag(null);
        passBhkSubClt.setRgltag(0);
    }
    public boolean getIsIfPass(TdTjBhksubClt dta) {
        boolean ifPass = false;
        if(StringUtils.isNotBlank(dta.getItemRst())) {
            // 判断模式：1 定性 2 定量
            if (dta.getJdgptn() == 1) {
                List<TbTjRstdesc> tbTjRstdescList = this.service.selectRstdescListByItemId(dta.getFkByItemId().getRid());
                for (TbTjRstdesc tbTjRstdesc : tbTjRstdescList) {
                    if (dta.getItemRst().equals(tbTjRstdesc.getRstDesc())) {
                        if(tbTjRstdesc.getEgbTag() == 1) {
                            ifPass = true;
                        }
                        break;
                    }
                }
            }
            if (dta.getJdgptn() == 2 && dta.getIfLack() == 0) {
                BigDecimal itemRst = new BigDecimal(dta.getItemRst());
                // 合格标记：1 合格 0 不合格
                // 偏高偏低：0 正常 1 偏低 2 偏高
                if(null != dta.getMinval() && null != dta.getMaxval()) {
                    if (itemRst.compareTo(dta.getMinval()) >= 0 && itemRst.compareTo(dta.getMaxval()) <= 0) {
                        ifPass = true;
                    } else if (itemRst.compareTo(dta.getMinval()) < 0) {
                        ifPass = false;
                    } else if (itemRst.compareTo(dta.getMaxval()) > 0) {
                        ifPass = false;
                    }
                } else {
                    ifPass = true;
                }
            }
        }else {
            ifPass = true;
        }
        return ifPass;
    }

    /**
     * @Description : 缺项/未缺change事件
     * @MethodAuthor: anjing
     * @Date : 2019/5/30 9:02
     **/
    public void changeIfLack(TdTjBhksubClt dta) {
        if(dta.isLack()) {
            dta.setIfLack(1);
            dta.setItemRst("未检");
            dta.setRgltag(1);
            dta.setIfrgltag(true);
            dta.setItemRstOri("未检");
        } else {
            dta.setIfLack(0);
            dta.setItemRst(null);
            dta.setRgltag(0);
            dta.setIfrgltag(false);
            dta.setItemRstOri(null);
        }
        Integer itemTag = null == dta || null == dta.getFkByItemId() ? null : dta.getFkByItemId().getItemTag();
        if(null != itemTag && itemTag >= 3 && itemTag <= 14){
            // 自动计算 双耳高频等项目
            autoCalcItemRst();
        }
        // 胸片-判定结果赋值
        if(null != dta.getFkByItemId().getItemTag() && dta.getFkByItemId().getItemTag().intValue() == 30) {
            if(dta.isLack()) {
                dta.setRstFlag(3);
            } else {
                if(null != dta.getRgltag() && 1 == dta.getRgltag().intValue()) {
                    dta.setRstFlag(0);
                }
            }
        }
    }
    public void calRglTagAndRstDescNew(TdTjBhksubClt dta,Integer index,Integer rowIndex,Boolean ifTip) {
        calRglTagAndRstDesc(dta);
        if(StringUtils.isNotBlank(dta.getItemRst())) {
            // 判断模式：1 定性 2 定量
            if (dta.getJdgptn() == 2 && dta.getIfLack() == 0) {
                //国家接口标准
                verifyGjItemsRes(dta,index,rowIndex,ifTip);
            }
        }
    }
    /**
     * @Description : 根据输入结果计算是否合格、偏高偏低
     * @MethodAuthor: anjing
     * @Date : 2019/5/26 16:59
     **/
    public void calRglTagAndRstDesc(TdTjBhksubClt dta) {
        if(StringUtils.isNotBlank(dta.getItemRst())) {
            // 判断模式：1 定性 2 定量
            if (dta.getJdgptn() == 1) {
                List<TbTjRstdesc> tbTjRstdescList = this.service.selectRstdescListByItemId(dta.getFkByItemId().getRid());
                for (TbTjRstdesc tbTjRstdesc : tbTjRstdescList) {
                    if (dta.getItemRst().equals(tbTjRstdesc.getRstDesc())) {
                        if(tbTjRstdesc.getEgbTag() == 1) {
                            dta.setIfrgltag(true);
                        } else {
                            dta.setIfrgltag(false);
                        }
                        dta.setRgltag((int) tbTjRstdesc.getEgbTag());
                        break;
                    }
                }
            }
            if (dta.getJdgptn() == 2 && dta.getIfLack() == 0) {
                BigDecimal itemRst = new BigDecimal(dta.getItemRst());
                // 合格标记：1 合格 0 不合格
                // 偏高偏低：0 正常 1 偏低 2 偏高
                if(null != dta.getMinval() && null != dta.getMaxval()) {
                    if (itemRst.compareTo(dta.getMinval()) >= 0 && itemRst.compareTo(dta.getMaxval()) <= 0) {
                        dta.setIfrgltag(true);
                        dta.setRgltag(1);
                        dta.setRstDesc(0);
                    } else if (itemRst.compareTo(dta.getMinval()) < 0) {
                        dta.setIfrgltag(false);
                        dta.setRgltag(0);
                        dta.setRstDesc(1);
                    } else if (itemRst.compareTo(dta.getMaxval()) > 0) {
                        dta.setIfrgltag(false);
                        dta.setRgltag(0);
                        dta.setRstDesc(2);
                    }
                } else {
                    dta.setIfrgltag(true);
                    dta.setRgltag(1);
                    dta.setRstDesc(0);
                }
            }
        }
        Integer itemTag = null == dta || null == dta.getFkByItemId() ? null : dta.getFkByItemId().getItemTag();
        if(null != itemTag && itemTag >= 3 && itemTag <= 14){
            // 自动计算 双耳高频等项目
            autoCalcItemRst();
        }
        // 胸片-判定结果赋值
        dta.setRstFlag(null);
        if(null != dta.getFkByItemId().getItemTag() && dta.getFkByItemId().getItemTag().intValue() == 30) {
            if(null != dta.getRgltag() && 1 == dta.getRgltag().intValue()) {
                dta.setRstFlag(0);
            }
        }
    }

    /**
     * <p>方法描述：校验项目结果值与国家接口标准值</p>
     * @MethodAuthor： yzz
     * @Date：2022-09-19
     **/
    public Boolean verifyGjItemsRes(TdTjBhksubClt dta, Integer index, Integer rowIndex,boolean ifTip){
        boolean flag = true;
        String id="tabView\\\\:editTabView\\\\:bhkSubListForm\\\\:bhksubCltList";
        if (dta.getFkByItemId() != null && dta.getFkByItemId().getRid() != null && StringUtils.isNotBlank(dta.getItemRst()) &&
                this.ItemsGjMap != null && ItemsGjMap.containsKey(dta.getFkByItemId().getRid().toString()) &&
                dta.getFkByBhkId() != null && dta.getFkByBhkId().getSex() != null &&
                dta.getFkByMsruntId() != null && dta.getFkByMsruntId().getRid() != null) {
            BigDecimal itemRst = new BigDecimal(dta.getItemRst());
            List<TbTjItemsGj> itemsGjList = ItemsGjMap.get(dta.getFkByItemId().getRid().toString());
            if (!CollectionUtils.isEmpty(itemsGjList)) {
                for (TbTjItemsGj tbTjItemsGj : itemsGjList) {
                    if ((tbTjItemsGj.getSex()==null||tbTjItemsGj.getSex() == ("男".equals(dta.getFkByBhkId().getSex())?1:2)) &&
                            tbTjItemsGj.getFkByMsruntId().getRid().intValue() == dta.getFkByMsruntId().getRid().intValue()) {
                        BigDecimal minval = tbTjItemsGj.getMinval();
                        BigDecimal maxval = tbTjItemsGj.getMaxval();
                        if(!(itemRst.compareTo(minval)>=0&&itemRst.compareTo(maxval)<=0)){
                            Global.markErrorInfo(ifTip, id+index+"\\\\:"+rowIndex+"\\\\:itemRstInput",dta.getFkByItemId().getItemName() + "的结果值允许范围为" + minval + "~"+maxval+"！");
                            flag=false;
                        }
                    }
                }
            }
        }
        return flag;
    }

    /**
     * @Description : 检查结论初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/28 10:23
     * <p>修订内容：数据赋值</p>
     *
     * @MethodReviser maox,2019年5月30日,initMhkrstList

     **/
    public void initMhkrstList() {
        ncagSelect = false;
        recheckSelect = false;
        otherSelect = false;
        contraindSelect = false;
        supoccdiseSelect = false;

        this.mhkrstList = this.service.findallSimpleCodesByTypeIdOrderByExtends2("5005");
        //体检结论初始化
        List<TdTjMhkrstClt> mhkRsts= tdTjBhkClt.getTdTjMhkrstClts();
        if(mhkRsts != null && mhkRsts.size() >0){
            for(TdTjMhkrstClt mhkrst:mhkRsts){
                if(mhkrst.getFkByBhkrstId().getExtendS2() ==1){
                    ncagSelect = true;
                }else{
                    if(mhkrst.getFkByBhkrstId().getExtendS2() ==2){
                        recheckSelect = true;
                    }else if(mhkrst.getFkByBhkrstId().getExtendS2() ==3){
                        otherSelect = true;
                    }else if(mhkrst.getFkByBhkrstId().getExtendS2() ==4){
                        contraindSelect = true;
                    }else if(mhkrst.getFkByBhkrstId().getExtendS2() ==5){
                        supoccdiseSelect = true;
                    }
                }
            }
        }

        //处理复查，其他危害因素
        List<TdTjBadrsnsClt> badclts  = tdTjBhkClt.getTdTjBadrsnsClts();
        reCheckBads = new ArrayList<>();
        otherBads = new ArrayList<>();
        ncagBads = new ArrayList<>();
        searchNcagCheckBadName = "";
        searchReCheckBadName = "";
        searchOtherBadName ="";
        otherDesc ="";
        if(null != badclts && badclts.size()>0){
            for(TdTjBadrsnsClt clt:badclts){
                TsSimpleCode code = clt.getFkByExamConclusionId();
                if(null != code && null !=code.getRid()){
                    if(code.getExtendS2() == 1){
                        ncagBads.add(clt.getFkByBadrsnId());
                    }else if(code.getExtendS2() ==2){
                        reCheckBads.add(clt.getFkByBadrsnId());
                    }else if(code.getExtendS2() ==3){
                        otherBads.add(clt.getFkByBadrsnId());
                        otherDesc = clt.getQtjbName();
                    }
                }

            }

            //searchNcagCheckBadName
            if(null != ncagBads && ncagBads.size()>0){
                for(TsSimpleCode a:ncagBads){
                    searchNcagCheckBadName +=a.getCodeName()+"，";
                }
                searchNcagCheckBadName = searchNcagCheckBadName.substring(0,searchNcagCheckBadName.length()-1);
            }
            if(null != reCheckBads && reCheckBads.size()>0){
                for(TsSimpleCode a:reCheckBads){
                    searchReCheckBadName +=a.getCodeName()+"，";
                }
                searchReCheckBadName = searchReCheckBadName.substring(0,searchReCheckBadName.length()-1);
            }
            if(null != otherBads && otherBads.size()>0){
                for(TsSimpleCode a:otherBads){
                    searchOtherBadName +=a.getCodeName()+"，";
                }
                searchOtherBadName = searchOtherBadName.substring(0,searchOtherBadName.length()-1);
            }
        }
    }

    /**
     * @Description : 根据危害因素id、在岗状态Id封装职业禁忌证
     * @MethodAuthor: anjing
     * @Date : 2019/5/28 16:01
     **/
    private void getSupoccdise() {
        Map<String, List<TsSimpleCode>> supoccdiseMap = new HashMap<>();
        StringBuilder badrsnIds = new StringBuilder();
        for(TdTjBadrsnsClt tdTjBadrsnsClt : this.tdTjBhkClt.getTdTjBadrsnsClts()) {
            badrsnIds.append(",").append(tdTjBadrsnsClt.getFkByBadrsnId().getRid());
        }
        List<Object[]> list = this.service.selectSupoccdiseListByBadrsnIdAndOnguardStateId(badrsnIds.deleteCharAt(0).toString(), Integer.parseInt(this.editOnguadrStateId));
    }


    /**
     * @Description : 封装职业病
     * @MethodAuthor: anjing
     * @Date : 2019/5/28 14:24
     * <p>修订内容：职业病赋值</p>
     *
     * @MethodReviser maox,2019年5月30日,initSupoccdiseCltWeb

     **/
    private void initSupoccdiseCltWeb() {
        List<TdTjSupoccdiseClt> list = this.tdTjBhkClt.getTdTjSupoccdiseClts();
        supoccdiseCltWebList = new ArrayList<TdTjSupoccdiseCltWeb>();
        //初始化 在岗状态是否是 上岗前
        if(this.tdTjBhkClt!=null && tdTjBhkClt.getFkByOnguardStateid()!=null
                && this.tdTjBhkClt.getFkByOnguardStateid().getRid()!=null
                && this.onguadrStateMap.containsKey(tdTjBhkClt.getFkByOnguardStateid().getRid())
                && "1".equals(this.onguadrStateMap.get(tdTjBhkClt.getFkByOnguardStateid().getRid()).getExtendS1())){
            this.ifBeforeOnPost=true;
        }else{
            this.ifBeforeOnPost=false;
        }

        //同种危害因素合并疑似职业病
        Map<Integer, List<Integer>> map = new HashMap<>();
        Map<Integer, List<TsSimpleCode>> supoccdiseMap = new HashMap<>();
        //key: 危害因素rid+"&"+结论rid value:单位名称
        Map<String, String> crptNameMap = new HashMap<>();
        if(list != null && list.size()>0){
            for(TdTjSupoccdiseClt clt:list){
                if(map.containsKey(clt.getFkByBadrsnId().getRid())){
                    List<Integer> a =map.get(clt.getFkByBadrsnId().getRid());
                    a.add(clt.getFkByOccDiseid().getRid());
                }else{
                    List<Integer> a = new ArrayList<Integer>();
                    a.add(clt.getFkByOccDiseid().getRid());
                    map.put(clt.getFkByBadrsnId().getRid(), a);
                }
                if(supoccdiseMap.containsKey(clt.getFkByBadrsnId().getRid())) {
                    List<TsSimpleCode> simpleCodeList = supoccdiseMap.get(clt.getFkByBadrsnId().getRid());
                    simpleCodeList.add(clt.getFkByOccDiseid());
                } else {
                    List<TsSimpleCode> simpleCodeList = new ArrayList<>();
                    simpleCodeList.add(clt.getFkByOccDiseid());
                    supoccdiseMap.put(clt.getFkByBadrsnId().getRid(), simpleCodeList);
                }
                if(!crptNameMap.containsKey(clt.getFkByBadrsnId().getRid()+"&"+clt.getFkByOccDiseid().getRid()) && StringUtils.isNotBlank(clt.getCrptName())) {
                    crptNameMap.put(clt.getFkByBadrsnId().getRid()+"&"+clt.getFkByOccDiseid().getRid(),clt.getCrptName());
                }
            }

            //遍历map封装
            for (Map.Entry<Integer, List<Integer>> entry : map.entrySet()) {
                TdTjSupoccdiseCltWeb occdise = new TdTjSupoccdiseCltWeb();
                occdise.setBadRsnId(entry.getKey());
                occdise.setSupoccdiseIdList(entry.getValue());
                if(null != supoccdiseMap && supoccdiseMap.size() > 0) {
                    List<TsSimpleCode> simpleCodeList = supoccdiseMap.get(entry.getKey());
                    if(!CollectionUtils.isEmpty(simpleCodeList)) {
                        StringBuilder name = new StringBuilder();
                        for(TsSimpleCode tsSimpleCode : simpleCodeList) {
                            name.append("，").append(tsSimpleCode.getCodeName());
                            if(crptNameMap.containsKey(entry.getKey()+"&"+tsSimpleCode.getRid())){
                                occdise.setCrptName(crptNameMap.get(entry.getKey()+"&"+tsSimpleCode.getRid()));
                            }
                        }
                        occdise.setSupoccdise(name.deleteCharAt(0).toString());
                    }
                }
                //设置危害因素可能的职业病
                List<TsSimpleCode> occdiseClts = service.findOccdiseClts(entry.getKey(),tdTjBhkClt.getFkByOnguardStateid().getRid());
                if(CollectionUtils.isEmpty(occdiseClts)) {
                    occdiseClts = this.commService.findSimpleCodesByTypeId("5010");
                }
                occdise.setSupoccdiseCltList(occdiseClts);
                //页面展示疑似职业病
                supoccdiseCltWebList.add(occdise);
            }
        }
    }

    /**
     * @Description : 封装职业禁忌证
     * @MethodAuthor: anjing
     * @Date : 2019/5/28 14:24
     **/
    private void initContraindCltWeb() {
        List<TdTjContraindClt> list = this.tdTjBhkClt.getTdTjContraindClts();
        contraindCltWebList = new ArrayList<TdTjContraindCltWeb>();
        //同种危害因素合并疑似职业病
        Map<Integer, List<Integer>> map = new HashMap<>();
        Map<Integer, List<TsSimpleCode>> contraindMap = new HashMap<>();
        if(list != null && list.size()>0){
            for(TdTjContraindClt clt:list){
                if(map.containsKey(clt.getFkByBadrsnId().getRid())){
                    List<Integer> a =map.get(clt.getFkByBadrsnId().getRid());
                    a.add(clt.getFkByContraindId().getRid());
                }else{
                    List<Integer> a = new ArrayList<Integer>();
                    a.add(clt.getFkByContraindId().getRid());
                    map.put(clt.getFkByBadrsnId().getRid(), a);
                }
                if(contraindMap.containsKey(clt.getFkByBadrsnId().getRid())) {
                    List<TsSimpleCode> simpleCodeList = contraindMap.get(clt.getFkByBadrsnId().getRid());
                    simpleCodeList.add(clt.getFkByContraindId());
                } else{
                    List<TsSimpleCode> simpleCodeList = new ArrayList<>();
                    simpleCodeList.add(clt.getFkByContraindId());
                    contraindMap.put(clt.getFkByBadrsnId().getRid(), simpleCodeList);
                }
            }

            //遍历map封装
            for (Map.Entry<Integer, List<Integer>> entry : map.entrySet()) {
                TdTjContraindCltWeb contraind = new TdTjContraindCltWeb();
                contraind.setBadRsnId(entry.getKey());
                contraind.setContraindIdList(entry.getValue());
                //设置危害因素可能的职业病
                List<TsSimpleCode> contraidClts = service.findContraindClts(entry.getKey(),tdTjBhkClt.getFkByOnguardStateid().getRid());
                if(CollectionUtils.isEmpty(contraidClts)) {
                    contraidClts = this.commService.findSimpleCodesByTypeId("5011");
                }
                contraind.setContraindCltList(contraidClts);
                if(null != contraindMap && contraindMap.size() > 0) {
                    List<TsSimpleCode> simpleCodeList = contraindMap.get(entry.getKey());
                    if(!CollectionUtils.isEmpty(simpleCodeList)) {
                        StringBuilder name = new StringBuilder();
                        for(TsSimpleCode tsSimpleCode : simpleCodeList) {
                            name.append("，").append(tsSimpleCode.getCodeName());
                        }
                        contraind.setContraind(name.deleteCharAt(0).toString());
                    }
                }
                //页面展示疑似职业病
                contraindCltWebList.add(contraind);
            }
        }

    }
    /******************************************** 结束初始化 *********************************************/

    public boolean saveTab() {
        if(this.addActiveTab == 0) {
            /*if(null != this.tdTjBhkClt.getIfRhk() && this.tdTjBhkClt.getIfRhk() == 1) {
                if(StringUtils.isBlank(this.tdTjBhkClt.getIdc())) {
                    RequestContext.getCurrentInstance().execute("PF('basicButtonBlock').show()");
                }
            }*/
            if(!this.vertyBasicInfoBySave()) {
                return false;
            }
            this.saveBasicInfo();
        }
        if(this.addActiveTab == 1) {
            if(this.tdTjBhkClt.getIfRhk() == 1) {
                if(!this.verfySympomBySave()) {
                    return false;
                }
                this.saveSymptom();
                this.modBhkSubListInit();
            } else{
                if(!this.vertyExmsDataBySave()) {
                    return false;
                }
                this.saveExmsData();
            }
        }
        if(this.addActiveTab == 2) {
            if(!this.verfySympomBySave()) {
                return false;
            }
            this.saveSymptom();
            this.modBhkSubListInit();
        }
        return true;
    }

    /**
     * @Description : 上一步,刷新tab
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 10:59
     **/
    public void lastAction(Integer addActiveTab) {

        if(addActiveTab == 1 && !this.vertyExmsDataBySave()) {
            return;
        }
        if(addActiveTab == 2 && !this.vertyBhkSubBySave()) {
            return;
        }

        if(addActiveTab == 1) {
            this.tdTjBhkClt.setBhkRcdState(1);
            // 保存体检问诊信息
            this.saveExmsData();
            // 初始化基本信息页面
            this.initMarryList();
            this.modBasicInfoInit();
            this.addActiveTab = addActiveTab - 1;
        }
        if(addActiveTab == 2) {
            if(this.tdTjBhkClt.getIfRhk() == 1) {
                this.tdTjBhkClt.setBhkRcdState(1);
                // 初始化基本信息页面
                this.modBasicInfoInit();
                this.addActiveTab = addActiveTab - 2;
            } else {
                this.tdTjBhkClt.setBhkRcdState(2);
                // 初始化体检问诊页面
                this.modExmsdataInit();
                this.addActiveTab = addActiveTab - 1;
                RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:exmsdataListForm:exmsdataTitleGrid");
            }
            // 保存体检结果信息
            this.saveSymptom();
        }
        if(addActiveTab == 3) {
            this.tdTjBhkClt.setBhkRcdState(3);
            // 保存体检结论页面
            if(hasBhkResultStyle){
                if(verySaveMhkRsnNew1()){
                    return;
                }
            }else{
                if(verySaveMhkRsn()){
                    return;
                }
            }
            doMhkrsnSave();
            service.saveOrUpdateTdTjBhkClt(tdTjBhkClt);
            // 初始化体检结果页面
            this.setOnguadrStateId();
            this.setPsnTypeId();
            this.modBhkSubListInit();
            if(this.tdTjBhkClt.getIfRhk() == 1) {
                this.addActiveTab = addActiveTab - 2;
            } else{
                this.addActiveTab = addActiveTab - 1;
            }
            RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:bhkSubListForm:resultTitleGrid");
        }
    }

    /**
     * <p>方法描述：检查结果 下一步，前置校验 </p>
     * @MethodAuthor： pw 2023/5/12
     **/
    public void preNextAction(Integer addActiveTab){
        if(addActiveTab == 0) {
            if(!ifIdcAble) {
                this.tdTjBhkClt.setIdc(null);
            }
            if(!this.vertyBasicInfoByNext()) {
                return;
            }
        } else if(addActiveTab == 1 && !this.vertyExmsDataByNext()) {
            return;
        } else if(addActiveTab == 2) {
            if(!this.vertyBhkSubByNextAndIfRfk()){
                return;
            }
            if(!this.vertyBhkSubByNext()) {
                return;
            }
        }
        if(null != addActiveTab && 2 == addActiveTab &&
                this.vertyBhkSubItemTagExt3ByNext()){
            RequestContext.getCurrentInstance().execute("PF('ItemTagDiag').show()");
        }else{
            this.nextAction(addActiveTab);
        }
    }

    /**
     * @Description : 下一步，验证所有信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 11:01
     **/
    public void nextAction(Integer addActiveTab) {
        if(addActiveTab == 0) {
            if(!ifIdcAble) {
                this.tdTjBhkClt.setIdc(null);
            }
            if(!this.vertyBasicInfoByNext()) {
                return;
            }
        } else if(addActiveTab == 1 && !this.vertyExmsDataByNext()) {
            return;
        } else if(addActiveTab == 2) {
            /*if(this.tdTjBhkClt.getIfRhk() == 1 && !this.vertyBhkSubByNextAndIfRfk()) {
                return;
            }*/
            if(!this.vertyBhkSubByNextAndIfRfk()){
                return;
            }
            if(!this.vertyBhkSubByNext()) {
                return;
            }
        }

        if(addActiveTab == 0) {
            this.tdTjBhkClt.setIfNotMod(1);
            if(this.tdTjBhkClt.getIfRhk() == 1) {
                if(this.tdTjBhkClt.getBhkRcdState() < 3) {
                    this.tdTjBhkClt.setBhkRcdState(3);
                }
                this.saveBasicInfo();

                //复检-检查结果
                this.geItemCombidListByBadrsnIdAndWorkStateId();
                // 初始化体检结果页面
                this.getBhksubCltList();
                this.initItemSort();
                this.initChkDocMap();
                RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:bhkSubListForm:resultTitleGrid");
            } else {
                if(this.tdTjBhkClt.getBhkRcdState() < 2) {
                    this.tdTjBhkClt.setBhkRcdState(2);
                }
                this.saveBasicInfo();
                // 初始化体检问诊页面
                this.modExmsdataInit();
                /**问诊日期默认体检日期*/
                if(tdTjBhkClt.getWzChkdat() == null){
                    tdTjBhkClt.setWzChkdat(tdTjBhkClt.getBhkDate());
                }
                RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:exmsdataListForm:exmsdataTitleGrid");
            }
            // 初始化下一个页面
            this.setOnguadrStateId();
            this.setPsnTypeId();
            this.addActiveTab = addActiveTab + 1;
        }
        if(addActiveTab == 1 ) {
            if(this.tdTjBhkClt.getBhkRcdState() < 3) {
                this.tdTjBhkClt.setBhkRcdState(3);
            }
            this.saveExmsData();


            // 初始化下一个页面-检查结果
            this.geItemCombidListByBadrsnIdAndWorkStateId();
            this.getBhksubCltList();
            this.initItemSort();
            this.initChkDocMap();
            this.initTbTjAudioItemRuleList();
            this.addActiveTab = addActiveTab + 1;
            RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:bhkSubListForm:resultTitleGrid");
        }
        if(addActiveTab == 2) {//检测结果-下一步
            if(tdTjBhkClt.getRid() != null){
                tdTjBhkClt = service.selectBhkCltByRid(tdTjBhkClt.getRid());
            }

            if(this.tdTjBhkClt.getBhkRcdState() < 4) {
                this.tdTjBhkClt.setBhkRcdState(4);
            }
            this.saveSymptom();
            // 初始化下一个页面
            this.initMainChkDocMap();//初始化主检医生医生
            this.geItemCombidListByBadrsnIdAndWorkStateId();
            this.initMhkrstList();//主检结论
            this.initSupoccdiseCltWeb();
            this.initContraindCltWeb();
            this.initTdTjChiefDoctorBean();
            if(tdTjBhkClt.getFkByMhkdctId() !=  null){
                mainChkdocId = tdTjBhkClt.getFkByMhkdctId().getRid().toString();
            }else{
                mainChkdocId = "";
            }

            if(this.tdTjBhkClt.getIfRhk() == 1) {
                this.addActiveTab = addActiveTab;
            } else{
                this.addActiveTab = addActiveTab + 1;
            }

            /**主检日期默认体检日期*/
            if(tdTjBhkClt.getJdgdat() == null){
                tdTjBhkClt.setJdgdat(tdTjBhkClt.getBhkDate());
            }

            initBadList();
            RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:mhkrstListForm:mhkrstTitleGrid");
        }

    }

    /**
     * @Description : 确认录入：验证所有信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 11:03
     **/
    public void toUploadAction() {
        JsfUtil.addSuccessMessage("录入成功！");
        backAction();
    }

    private boolean beforAddInit() {
        boolean flag = true;
        TsUnit tsUnit = Global.getUser().getTsUnit();

        initStadItemsStatus();// 如果不加上 会出现标准值配置变化了 但stadItemsStatus未调整
        if(this.stadItemsStatus != 1){
            JsfUtil.addErrorMessage("请先在【标准值配置】模块检查并提交标准值！");
            flag = false;
        }
        this.tbTjSrvorgList = tbTjSrvorgService.selectSrvorgListByOrgId(tsUnit.getRid());
        if(CollectionUtils.isEmpty(tbTjSrvorgList)) {
            JsfUtil.addErrorMessage("资质服务机构未注册，请联系管理员！");
            flag = false;
        }
        List<Object[]> tdZwTjorginfoList = tbTjSrvorgService.selectTjorginfoListByOrgIdAndState(tsUnit.getRid(), 1);
        if(CollectionUtils.isEmpty(tdZwTjorginfoList)) {
            JsfUtil.addErrorMessage("健康检查资质信息不完整，请完善！");
            flag = false;
        }
        return flag;
    }

    /**
     * @Description : 婚姻状况初始化
     * @MethodAuthor: anjing
     * @Date : 2019/6/1 13:07
     **/
    private void initMarryList() {
        this.marrayList = new ArrayList<>();
        List<TsSimpleCode> tsSimpleCodeList = commService.findSimpleCodesByTypeNo("1003");
        for(TsSimpleCode entity : tsSimpleCodeList) {
            this.marrayList.add(entity.getCodeName());
        }
    }

    /**
     * @Description : 添加初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/17 16:47
     **/
    @Override
    public void addInit() {
        this.view = null;
        this.addActiveTab = 0;
        this.tdTjBhkClt = new TdTjBhkClt();
        this.tdTjBhkClt.setFkByBhkorgId(this.tbTjSrvorgList.get(0));
        this.tdTjBhkClt.setUuid(UUID.randomUUID().toString());
        this.tdTjBhkClt.setDelMark(0);

        this.tdTjBhkClt.setPsnType(1);
        List<TdTjBadrsnsClt> tdTjBadrsnsClts = new ArrayList<>();
        this.tdTjBhkClt.setTdTjBadrsnsClts(tdTjBadrsnsClts);
        this.tdTjBhkClt.setIfRhk(0);
        this.editOnguadrStateId = null;
        this.editOnguadrStateMap = getOnguadrStateMap();
        this.editPsnTypeMap = getPsnTypeMap();
        this.tdTjBhkClt.setBhkRcdState(1);
        this.tdTjBhkClt.setIfNotMod(0);
        this.tchbadrsntimAble = false;
        //缓存初始化
        this.psnNameCache=null;
        this.orgAidcAbhkdateCache=null;

        //初始化身份证
        if(null != this.editPsnTypeMap){
            for(String a:editPsnTypeMap.values()){
                editPsnTypeId = a;
                this.onPsnTypeChangeAction();
                break;
            }
        }
        this.ifIdcAble = true;
        this.tdTjBhkClt.setJcType(1);//监测类型默认常规监测
        this.initMarryList();
        this.ifOtherBadRsn = false;
        this.ifOtherTouchBadRsn=false;
        if(null != this.lastCrpt) {
            this.tdTjBhkClt.setFkByCrptId(this.lastCrpt);
            this.tdTjBhkClt.setCrptName(this.lastCrpt.getCrptName());
        }
        if(null != this.lastEmpCrpt) {
            this.tdTjBhkClt.setFkByEmpCrptId(this.lastEmpCrpt);
        }
        if(this.tdTjBhkClt.getFkByEmpCrptId() == null){
            this.tdTjBhkClt.setFkByEmpCrptId(new TbTjCrpt());
        }
        //佩戴情况
        if(this.tdTjBhkClt.getFkByProtectEquId() == null){
            this.tdTjBhkClt.setFkByProtectEquId(new TsSimpleCode());
        }
    }
    @Override
    public void addInitAction() {
        if(!beforAddInit()) {
            return;
        }
        workTypeName = "";
        ifOtherWork = false;
        super.addInitAction();
    }

    @Override
    public void viewInit() {
    }

    /*************************************************************** 修改初始化 **************************************************************************************/
    /**
     * @Description : 基本信息-修改初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/30 12:59
     **/
    private void modBasicInfoInit() {
        // 设置危害因素
        if(!CollectionUtils.isEmpty(this.tdTjBhkClt.getTdTjBadrsnsClts())) {
            StringBuffer buffer = new StringBuffer();
            for (TdTjBadrsnsClt tdTjBadrsnsClt : this.tdTjBhkClt.getTdTjBadrsnsClts()) {
                if(!buffer.toString().contains(","+tdTjBadrsnsClt.getFkByBadrsnId().getCodeName())){
                    buffer.append("，").append(tdTjBadrsnsClt.getFkByBadrsnId().getCodeName());
                }
            }
            this.tdTjBhkClt.setBadrsns(buffer.deleteCharAt(0).toString());
        }
        if(!CollectionUtils.isEmpty(this.tdTjBhkClt.getTdTjTchBadrsnsClts())) {
            StringBuffer buffer = new StringBuffer();
            for (TdTjTchBadrsnsClt tchBadrsnsClt : this.tdTjBhkClt.getTdTjTchBadrsnsClts()) {
                if(!buffer.toString().contains(","+tchBadrsnsClt.getFkByBadrsnId().getCodeName())){
                    buffer.append("，").append(tchBadrsnsClt.getFkByBadrsnId().getCodeName());
                }
            }
            this.tdTjBhkClt.setTouchBadrsns(buffer.deleteCharAt(0).toString());
        }
        if(null != tdTjBhkClt.getFkByWorkTypeId() && null !=tdTjBhkClt.getFkByWorkTypeId().getRid()){
            TsSimpleCode code = commService.findTsSimpleCodeByRid(tdTjBhkClt.getFkByWorkTypeId().getRid());
            workTypeName = code.getCodeName();
            if("1".equals(code.getExtendS1())){
                ifOtherWork = true;
            }else{
                ifOtherWork = false;
            }
        }else{
            workTypeName ="";
            ifOtherWork = false;
        }
        initOriValue();
        // 设置在岗状态
        this.editOnguadrStateMap = getOnguadrStateMap();
        this.editPsnTypeMap = getPsnTypeMap();
        this.setOnguadrStateId();
        this.setPsnTypeId();
    }

    /**
     * @Description : 设置在岗状态
     * @MethodAuthor: anjing
     * @Date : 2019/5/31 16:30
     **/
    private void setOnguadrStateId() {
        this.tchbadrsntimAble = false;
        if(this.tdTjBhkClt.getFkByOnguardStateid() != null && this.tdTjBhkClt.getFkByOnguardStateid().getRid() != null) {
            this.editOnguadrStateId = this.tdTjBhkClt.getFkByOnguardStateid().getRid().toString();
            // “在岗状态”选择“上岗前”（码表5009扩展字段2，控制接害工龄是否不可输入，默认可以，0/空：可以，1：不可以），“接害工龄”灰掉不可输入
            if(null != this.tdTjBhkClt.getFkByOnguardStateid().getExtendS2() && this.tdTjBhkClt.getFkByOnguardStateid().getExtendS2() == 1) {
                this.tchbadrsntimAble = true;
            }
        } else {
            this.editOnguadrStateId = null;
        }
    }

    private void setPsnTypeId() {
        this.ifIdcAble = true;
        this.ifIdc = false;
        if(this.tdTjBhkClt.getFkByCardTypeId() != null && this.tdTjBhkClt.getFkByCardTypeId().getRid() != null) {
            this.editPsnTypeId = this.tdTjBhkClt.getFkByCardTypeId().getRid().toString();
            if("88".equals(this.tdTjBhkClt.getFkByCardTypeId().getCodeNo())) {
                this.ifIdcAble = false;
            }if("01".equals(this.tdTjBhkClt.getFkByCardTypeId().getCodeNo())){
                this.ifIdc = true;
            }
        } else {
            this.ifIdcAble = true;
        }
    }



    /**
     * @Description : 体检问诊-修改初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/30 13:01
     **/
    private void modExmsdataInit() {
        this.initEmhistoryCltList();
        this.initAnamnesisCltList();
        this.initExmsdataClt();
        this.initSymptomClt();
        this.initChkDocMap();

        this.symIdList = new ArrayList<>();
        for(SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
            for (SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                symCodeCommPO.setIfSelected(false);
                for(TdTjSymptomClt tdTjSymptomClt : this.tdTjBhkClt.getTdTjSymptomClts()) {
                    if(tdTjSymptomClt.getFkBySymId().getRid().equals(symCodeCommPO.getRid())) {
                        symCodeCommPO.setIfSelected(true);
                        if("1".equals(tdTjSymptomClt.getFkBySymId().getExtendS1())) {
                            this.tdTjSymptomCltBean.setShowOtherInfo(Boolean.TRUE);
                        }
                    }
                }
            }
        }

        for(TdTjSymptomClt tdTjSymptomClt : this.tdTjBhkClt.getTdTjSymptomClts()) {
            if(StringUtils.isNotBlank(tdTjSymptomClt.getFkBySymId().getExtendS1())) {
                if("1".equals(tdTjSymptomClt.getFkBySymId().getExtendS1())) {
                    this.tdTjSymptomCltBean.setExtends1("1");
                    this.tdTjSymptomCltBean.setOthsym(tdTjSymptomClt.getOthsym());

                    for(SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                        for(SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                            if(StringUtils.isNotBlank(symCodeCommPO.getExtendS1()) && "2".equals(symCodeCommPO.getExtendS1())) {
                                symCodeCommPO.setSelectAble(false);
                                break;
                            }
                        }
                    }
                } else if("2".equals(tdTjSymptomClt.getFkBySymId().getExtendS1())) {
                    for(SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                        for(SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                            if(StringUtils.isBlank(symCodeCommPO.getExtendS1()) || "1".equals(symCodeCommPO.getExtendS1())) {
                                symCodeCommPO.setSelectAble(false);
                            }
                        }
                    }
                }
            } else {
                for(SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                    for(SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                        if(StringUtils.isNotBlank(symCodeCommPO.getExtendS1()) && "2".equals(symCodeCommPO.getExtendS1())) {
                            symCodeCommPO.setSelectAble(false);
                            break;
                        }
                    }
                }
            }
            this.symIdList.add(tdTjSymptomClt.getFkBySymId().getRid());
        }

        // 设置问诊医生
        if(null != this.tdTjBhkClt.getFkByWzChkdoctId() && null != this.tdTjBhkClt.getFkByWzChkdoctId().getRid()) {
            this.editChkdocId = this.tdTjBhkClt.getFkByWzChkdoctId().getRid().toString();
        } else {
            this.editChkdocId = null;
        }
        // 设置是否经期
        if(!CollectionUtils.isEmpty(this.tdTjBhkClt.getTdTjExmsdataClts())) {
            TdTjExmsdataClt tdTjExmsdataClt = this.tdTjBhkClt.getTdTjExmsdataClts().get(0);
            if(null != tdTjExmsdataClt.getIsxmns() && tdTjExmsdataClt.getIsxmns() == 1) {
                this.tdTjExmsdataCltBean.setIsxmns(true);
            } else {
                this.tdTjExmsdataCltBean.setIsxmns(false);
            }
            // 设置目前吸烟情况 回显状态
            if(tdTjExmsdataClt.getFkBySmkstaId()!=null
                    && tdTjExmsdataClt.getFkBySmkstaId().getRid()!=null){
                tdTjExmsdataCltBean.setSmkSelRid(tdTjExmsdataClt.getFkBySmkstaId().getRid());
                if(tdTjExmsdataCltBean.getSmklMap().containsKey(tdTjExmsdataClt.getFkBySmkstaId().getRid())
                    && "1".equals(tdTjExmsdataCltBean.getSmklMap().get(tdTjExmsdataClt.getFkBySmkstaId().getRid()).getExtendS1())){
                    tdTjExmsdataCltBean.setIsSmkEdit(false);
                }else{
                    tdTjExmsdataCltBean.setIsSmkEdit(true);
                }
            }
        }
    }

    /**
     * @Description : 体检结果-修改初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/30 13:03
     **/
    private void modBhkSubListInit() {
        Map<Integer, List<TbTjRstdesc>> rstdescMap = this.getRstdesc();
        List<TdTjBhksubClt> tdTjBhksubCltList = this.service.selectBhkSubCltListByBhkId(this.tdTjBhkClt.getRid());
        this.bhksubCltList = new ArrayList<>();
        if(CollectionUtils.isEmpty(tdTjBhksubCltList)) {
            this.getBhksubCltList();
        } else {
            if(ifItemMust && this.tdTjBhkClt.getIfRhk() != 1) {
                // 获取必检项目
                initCmbItemsMap();
            }
            for (TdTjBhksubClt tdTjBhksubClt : tdTjBhksubCltList) {
                if (null != rstdescMap) {
                    if (tdTjBhksubClt.getJdgptn() == 1) {
                        if (!CollectionUtils.isEmpty(rstdescMap.get(tdTjBhksubClt.getFkByItemId().getRid()))) {
                            tdTjBhksubClt.setTbTjRstdescList(rstdescMap.get(tdTjBhksubClt.getFkByItemId().getRid()));
                        }
                    }
                }
                tdTjBhksubClt.setIfrgltag(tdTjBhksubClt.getRgltag()==1?true:false);
                tdTjBhksubClt.setLack(tdTjBhksubClt.getIfLack()==1?true:false);
                if(null != tdTjBhksubClt.getFkByChkdoctId() && null != tdTjBhksubClt.getFkByChkdoctId().getRid()) {
                    tdTjBhksubClt.setChkdoctId(tdTjBhksubClt.getFkByChkdoctId().getRid());
                }
                this.bhksubCltList.add(tdTjBhksubClt);
            }
        }
        this.itemsMap = new HashMap<>();
        this.initItemSort();
        this.initChkDocMap();
        this.initTbTjAudioItemRuleList();
        this.getMainstdListByBadrsnIdAndWorkStateId();
    }

    /**
     * @Description : 修改初始化
     * @MethodAuthor: anjing
     * @Date : 2019/5/23 18:10
     **/
    @Override
    public void modInit() {
        this.tdTjBhkClt = this.service.selectBhkCltByRid(this.rid);
        //缓存初始化
        if(this.tdTjBhkClt.getFkByCardTypeId()!=null && "01".equals(this.tdTjBhkClt.getFkByCardTypeId().getCodeNo())){
            this.psnNameCache = this.tdTjBhkClt.getPersonName();
        }
        this.orgAidcAbhkdateCache = this.tdTjBhkClt.getFkByBhkorgId().getRid() + "&" + this.tdTjBhkClt.getFkByCardTypeId().getRid() + "&" + this.tdTjBhkClt.getIdc() + "&" + DateUtils.formatDate(this.tdTjBhkClt.getBhkDate(), "yyyy-MM-dd");

        if(!hasBhkResultStyle){
            //处理危害因素
            initBadList();
        }
        if(this.tdTjBhkClt.getBhkRcdState() == 5) {
            this.view = 1;
            this.initMarryList();
            this.modBasicInfoInit();
            this.modExmsdataInit();
            this.geItemCombidListByBadrsnIdAndWorkStateId();
            this.modBhkSubListInit();

            /**体检结论*/
            this.initMainChkDocMap();
            this.initMhkrstList();
            this.initSupoccdiseCltWeb();
            this.initContraindCltWeb();
            this.initTdTjChiefDoctorBean();
            mainChkdocId = null;
            if(tdTjBhkClt.getFkByMhkdctId() !=  null){
                mainChkdocId = tdTjBhkClt.getFkByMhkdctId().getRid().toString();
            }
            this.addActiveTab = 0;

        } else {
            this.view = null;
            if(this.tdTjBhkClt.getBhkRcdState() == 1) {
                this.addActiveTab = 0;
                // 初始化基本信息
                this.initMarryList();
                this.modBasicInfoInit();
            }
            if(this.tdTjBhkClt.getBhkRcdState() == 2) {
                this.addActiveTab = 1;
                // 初始化体检问诊信息
                // 设置在岗状态
                this.setOnguadrStateId();
                this.setPsnTypeId();
                this.modExmsdataInit();
            }

            if(this.tdTjBhkClt.getBhkRcdState() == 3) {
                if(this.tdTjBhkClt.getIfRhk() == 1) {
                    this.addActiveTab = 1;
                } else {
                    this.addActiveTab = 2;
                }
                // 初始化体检结果信息
                // 设置在岗状态
                this.setOnguadrStateId();
                this.setPsnTypeId();
                this.geItemCombidListByBadrsnIdAndWorkStateId();
                this.modBhkSubListInit();
            }

            if(this.tdTjBhkClt.getBhkRcdState() == 4) {
                if(this.tdTjBhkClt.getIfRhk()==1) {
                    this.addActiveTab = 2;
                } else{
                    this.addActiveTab = 3;
                }
                // 初始化体检结论信息
                this.initMainChkDocMap();
                this.initMhkrstList();
                this.initSupoccdiseCltWeb();
                this.initContraindCltWeb();
                this.initTdTjChiefDoctorBean();
                mainChkdocId = null;
                if(tdTjBhkClt.getFkByMhkdctId() !=  null){
                    mainChkdocId = tdTjBhkClt.getFkByMhkdctId().getRid().toString();
                }

                /**主检日期默认体检日期*/
                if(tdTjBhkClt.getJdgdat() == null){
                    tdTjBhkClt.setJdgdat(tdTjBhkClt.getBhkDate());
                }
            }
        }
        if(hasBhkResultStyle){
            //处理危害因素
            initBadList();
        }
        if (null==this.tdTjBhkClt.getFkByEmpCrptId()) {
        	this.tdTjBhkClt.setFkByEmpCrptId(new TbTjCrpt());
		}
    }

    /**
     * @MethodName: initBadList
     * @Description: 处理危害因素
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-08
    **/
    private void initBadList(){
        List<TsSimpleCode> codeList = new ArrayList<>();
        List<TsSimpleCode> chooseBadList = new ArrayList<>();
        List<TdTjBadrsnsClt> badClts =  tdTjBhkClt.getTdTjBadrsnsClts();
        List<TdTjTchBadrsnsClt> tchBadrsnsClts=tdTjBhkClt.getTdTjTchBadrsnsClts();
        Map<Integer,TdTjBadrsnsClt> badMap = new HashMap<>();
        Set<String> badset = new HashSet<>();
        this.ifOtherBadRsn = false;
        this.ifOtherTouchBadRsn=false;
        if(null != badClts && badClts.size()>0){
            for(TdTjBadrsnsClt clt:badClts){
                if(!badset.contains(clt.getFkByBadrsnId().getCodeNo())){
                    codeList.add(clt.getFkByBadrsnId());
                    badset.add(clt.getFkByBadrsnId().getCodeNo());
                }
                if(StringUtils.isNotBlank(clt.getQtjbName())){
                    otherDesc = clt.getQtjbName();
                }
                if ("1".equals(clt.getFkByBadrsnId().getExtendS4())) {
                	this.ifOtherBadRsn = true;
				}
                badMap.put(clt.getFkByBadrsnId().getRid(),clt);
            }
        }
        chooseBadList.addAll(codeList);
        tdTjBhkClt.setBadList(codeList);
        if(hasBhkResultStyle){
            tdTjBadrsnsCltList = new ArrayList<>();
            int index = 0;
            for (TsSimpleCode c : codeList){
                TdTjBadrsnsClt dto = badMap.get(c.getRid());
                if(dto.getFkByExamConclusionId()!=null){
                    dto.setExamConclusionId(dto.getFkByExamConclusionId().getRid());
                }
                for(TdTjContraindCltWeb contraindCltWeb: contraindCltWebList){
                    if(contraindCltWeb.getBadRsnId().equals(c.getRid())){
                        dto.setContraindCltWeb(contraindCltWeb);
                        break;
                    }
                }
                if(dto.getContraindCltWeb()==null){
                    dto.setContraindCltWeb(new TdTjContraindCltWeb());
                }
                for(TdTjSupoccdiseCltWeb supoccdiseCltWeb : supoccdiseCltWebList){
                    if(supoccdiseCltWeb.getBadRsnId().equals(c.getRid())){
                        dto.setSupoccdiseCltWeb(supoccdiseCltWeb);
                        break;
                    }
                }
                if(dto.getSupoccdiseCltWeb()==null){
                    dto.setSupoccdiseCltWeb(new TdTjSupoccdiseCltWeb());
                }
                tdTjBadrsnsCltList.add(dto);
                onExamConclusionChange(index++);
            }
        }

        if(!CollectionUtils.isEmpty(tchBadrsnsClts)){
            for (TdTjTchBadrsnsClt badrsnsClt : tchBadrsnsClts) {
                if ("1".equals(badrsnsClt.getFkByBadrsnId().getExtendS4())) {
                    this.ifOtherTouchBadRsn = true;
                }
            }
        }
    }

    @Override
    public void modInitAction() {
        if(!beforAddInit()) {
            return;
        }
        workTypeName = "";
        ifOtherWork = false;
        super.modInitAction();
    }

    @Override
    public void saveAction() {
        if(saveTab()){
            JsfUtil.addSuccessMessage("保存成功！");
        }
    }

    /**
     * @Description : 标删
     * @MethodAuthor: anjing
     * @Date : 2019/5/30 17:28
     **/
    public void delAction() {
        this.tdTjBhkClt = this.service.selectBhkCltByRid(this.rid);
        if(this.tdTjBhkClt.getBhkRcdState() != 5) {
            this.tdTjBhkClt.setDelMark(1);
            this.service.upsertEntity(this.tdTjBhkClt);
            this.searchAction();
            JsfUtil.addSuccessMessage("删除成功！");
        } else {
            JsfUtil.addErrorMessage("已完成体检录入信息不能被删除！");
        }
    }

    /********************************************体检结论********************************************************/

    /***
     *  <p>方法描述：体检结论页面禁忌症记录</p>
     *
     * @MethodAuthor maox,2019年5月29日,addJJZ
     */
    public void addContraind(){
        TdTjContraindCltWeb contraind = new TdTjContraindCltWeb();
        //体检结论页面禁忌症记录
        contraindCltWebList.add(contraind);
    }

    /***
     *  <p>方法描述：体检结论页面增加疑似职业病记录</p>
     *
     * @MethodAuthor maox,2019年5月29日,addOccdise
     */
    public void addOccdise(){
        TdTjSupoccdiseCltWeb occdise = new TdTjSupoccdiseCltWeb();
        supoccdiseCltWebList.add(occdise);
    }

    /***
     *  <p>方法描述：禁忌症的危害因素选择</p>
     *
     * @MethodAuthor maox,2019年5月29日,getContraindList
     */
    public void getContraindList(TdTjContraindCltWeb contraindCltWeb){
        //根据在岗状态和危害因素获取健康监护方案标准
        //获取职业禁忌证list
        Integer badRsnId =null;
        if(contraindCltWeb != null){
            badRsnId = contraindCltWeb.getBadRsnId();
            //切换清空
            contraindCltWeb.setContraind(null);
            contraindCltWeb.setContraindIdList(null);
            contraindCltWeb.setContraindCltList(null);
        }
        //有危害因素选择空，则清空职业禁忌证
        if(badRsnId == null){
            if(contraindCltWebList != null && contraindCltWebList.size() >0){
                for(TdTjContraindCltWeb contraind:contraindCltWebList){
                    if(contraind.getBadRsnId() == null){
                        contraind.setContraindIdList(null);
                        contraind.setContraindCltList(null);
                    }
                }
            }
        }

        if(badRsnId !=null && tdTjBhkClt.getFkByOnguardStateid() != null){
            List<TsSimpleCode> contraindClts = service.findContraindClts(badRsnId,tdTjBhkClt.getFkByOnguardStateid().getRid());
            if(CollectionUtils.isEmpty(contraindClts)) {
                contraindClts = this.commService.findSimpleCodesByTypeId("5011");
            }
            if(contraindCltWebList != null && contraindCltWebList.size() >0){
                for(TdTjContraindCltWeb contraind:contraindCltWebList){
                    if(contraind != null && contraind.getBadRsnId() != null && contraind.getBadRsnId().intValue() == badRsnId){
                        contraind.setContraindCltList(contraindClts);
                    }
                }
            }
        }
    }

    /***
     *  <p>方法描述：疑似职业病危害因素选择</p>
     *
     * @MethodAuthor maox,2019年5月29日,getOccdiseList
     */
    public void getOccdiseList(TdTjSupoccdiseCltWeb supOccdise){
        //根据在岗状态和危害因素获取健康监护方案标准
        //获取疑似职业病list
        Integer badRsnId =null;
        if(supOccdise != null){
            badRsnId = supOccdise.getBadRsnId();
            supOccdise.setSupoccdise(null);
            supOccdise.setCrptName(null);
            supOccdise.setSupoccdiseIdList(null);
            supOccdise.setSupoccdiseCltList(null);
        }

        //有危害因素选择空，则清空职业禁忌证
        if(badRsnId == null){
            if(supoccdiseCltWebList != null && supoccdiseCltWebList.size() >0){
                for(TdTjSupoccdiseCltWeb supoccdise:supoccdiseCltWebList){
                    if(supoccdise.getBadRsnId() == null){
                        supoccdise.setSupoccdiseIdList(null);
                        supoccdise.setSupoccdiseCltList(null);
                    }
                }
            }
        }

        if(badRsnId !=null && tdTjBhkClt.getFkByOnguardStateid() != null){
            List<TsSimpleCode> occdiseClts = service.findOccdiseClts(badRsnId,tdTjBhkClt.getFkByOnguardStateid().getRid());
            if(CollectionUtils.isEmpty(occdiseClts)) {
                occdiseClts = this.commService.findSimpleCodesByTypeId("5010");
            }
            if(supoccdiseCltWebList != null && supoccdiseCltWebList.size() >0){
                for(TdTjSupoccdiseCltWeb occdise:supoccdiseCltWebList){
                    if(occdise != null && occdise.getBadRsnId() != null && occdise.getBadRsnId().intValue() == badRsnId){
                        occdise.setSupoccdiseCltList(occdiseClts);
                    }
                }
            }
        }
    }

    /**
     * @MethodName: calChooseBads
     * @Description: 计算还可以选择的危害因素
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-09
    **/
    private void calChooseBads(){
        chooseBadList = new ArrayList<>();
        chooseBadList.addAll(tdTjBhkClt.getBadList());
        if(null !=supoccdiseCltWebList && supoccdiseCltWebList.size() >0){
            for(TdTjSupoccdiseCltWeb occdise:supoccdiseCltWebList){
                if(null !=tdTjBhkClt.getBadList() && tdTjBhkClt.getBadList().size() >0){
                    for(TsSimpleCode a :tdTjBhkClt.getBadList()){
                        if(a.getRid().intValue() == occdise.getBadRsnId()){
                            chooseBadList.remove(a);
                        }
                    }
                }
            }
        }
        if(null !=contraindCltWebList && contraindCltWebList.size() >0) {
            for (TdTjContraindCltWeb contraind : contraindCltWebList) {
                if(null !=tdTjBhkClt.getBadList() && tdTjBhkClt.getBadList().size() >0){
                    for(TsSimpleCode a :tdTjBhkClt.getBadList()){
                        if(a.getRid().intValue() == contraind.getBadRsnId()){
                            chooseBadList.remove(a);
                        }
                    }
                }
            }
        }
        if(null !=otherBads && otherBads.size() >0){
            for(TsSimpleCode code:otherBads){
                chooseBadList.remove(code);
            }
        }
        if(null !=reCheckBads && reCheckBads.size() >0){
            for(TsSimpleCode code:reCheckBads){
                chooseBadList.remove(code);
            }
        }
        if(null != ncagBads && ncagBads.size() > 0){
            for(TsSimpleCode code:ncagBads){
                chooseBadList.remove(code);
            }
        }
    }
    /***
     *  <p>方法描述：去除禁忌症</p>
     *
     * @MethodAuthor maox,2019年5月30日,delContraindList
     * @param contraindCltWeb
     */
    public void delContraindList(TdTjContraindCltWeb contraindCltWeb){
        contraindCltWebList.remove(contraindCltWeb);
    }

    public void delOccdiseList(TdTjSupoccdiseCltWeb supOccdise){
        supoccdiseCltWebList.remove(supOccdise);
    }

    /***
     *  <p>方法描述：体检结论保存</p>
     *
     * @MethodAuthor maox,2019年5月29日,saveMhkRst
     */
    public void saveMhkRst(){
        if(hasBhkResultStyle){
            if(verySaveMhkRsnNew1()){
                return;
            }
        }else{
            if(verySaveMhkRsn()){
                return;
            }
        }
        doMhkrsnSave();
        service.saveOrUpdateTdTjBhkClt(tdTjBhkClt);
        JsfUtil.addSuccessMessage("保存成功！");
    }

    /***
     *  <p>方法描述：提交体检结论</p>
     *
     * @MethodAuthor maox,2019年5月31日,submitMhkrst
     */
    public void submitMhkrst(String type){
        if(hasBhkResultStyle){
            if(verySubmitMhkRsnNew1() || verySaveMhkRsnNew1()){
                return;
            }
        }else{
            if(verySubmitMhkRsn() || verySaveMhkRsn()){
                return;
            }
        }
        TdTjBhk bhk = null;
        try {
            if (ObjectUtil.isNotEmpty(tdTjBhkClt.getBhkCode())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByBhkorgId())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByBhkorgId().getRid())) {
                bhk = this.bhkService.selectBhkByBhkCodeAndBhkOrgId(tdTjBhkClt.getBhkCode(), tdTjBhkClt.getFkByBhkorgId().getRid());
            }
            if (verifyRptPrintDateInZdzybDate(bhk, this.tdTjBhkClt) != 0) {
                JsfUtil.addErrorMessage("重点职业病监测统计中，不允许新增或修改监测周期范围内的数据！");
                return;
            }
        } catch (Exception e) {
            tdTjBhkClt.setBhkRcdState(4);//避免提交的时候 向数据库存储数据异常 导致赋值完成状态
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败，请联系系统管理员！");
        }
        //判断胸片
        //下一步按钮，需判断胸片结论
        if("1".equals(type)){
            boolean itemFlag = false;
            boolean conclusionFlag = false;
            List<TdTjBhksubClt> bhksubCltList = this.tdTjBhkClt.getTdTjBhksubClts();
            for(TdTjBhksubClt sub:bhksubCltList){
                if(new Integer(30).equals(sub.getFkByItemId().getItemTag()) && new Integer(1).equals(sub.getRstFlag())){
                    //胸片的项目，结果是尘肺样改变
                    itemFlag = true;
                    break;
                }
            }
            if(itemFlag){
                //判断粉尘类危害因素的体检结论是否有“目前未见异常”（码表5005扩展字段2为1）或“其他疾病或异常”（码表5005扩展字段2为3）时
                //两种版本的数据结构不同
                if(hasBhkResultStyle){
                    for(TdTjBadrsnsClt badrsnsClt:tdTjBadrsnsCltList){
                        TsSimpleCode code =  badrsnsClt.getFkByBadrsnId();
                        TsSimpleCode parent = null;
                        for(TsSimpleCode tsSimpleCode : this.oneLevelBadrsnCodeList){
                            if("1".equals(tsSimpleCode.getExtendS3()) && tsSimpleCode.getCodeLevelNo().equals(code.getCodeLevelNo().split("\\.")[0])){
                                parent = tsSimpleCode;
                                break;
                            }
                        }
                        if(parent!=null && badrsnsClt.getExamConclusionId()!=null && (badrsnsClt.getFkByExamConclusionId().getExtendS2()==1 || badrsnsClt.getFkByExamConclusionId().getExtendS2()==3)){
                            //粉尘类危害因素未见其他异常，其他疾病或异常,文本框必填
                            conclusionFlag = true;
                            break;
                        }
                    }
                }else{
                    if(ncagSelect && !CollectionUtils.isEmpty(ncagBads)){
                        for (TsSimpleCode code:ncagBads){
                            TsSimpleCode parent = null;
                            for(TsSimpleCode tsSimpleCode : this.oneLevelBadrsnCodeList){
                                if("1".equals(tsSimpleCode.getExtendS3()) && tsSimpleCode.getCodeLevelNo().equals(code.getCodeLevelNo().split("\\.")[0])){
                                    parent = tsSimpleCode;
                                    break;
                                }
                            }
                            if(parent!=null){
                                conclusionFlag = true;
                                break;
                            }
                        }
                    }
                    //未见异常中没有，才遍历其他异常
                    if(!conclusionFlag && otherSelect && !CollectionUtils.isEmpty(otherBads)){
                        for (TsSimpleCode code:otherBads){
                            TsSimpleCode parent = null;
                            for(TsSimpleCode tsSimpleCode : this.oneLevelBadrsnCodeList){
                                if("1".equals(tsSimpleCode.getExtendS3()) && tsSimpleCode.getCodeLevelNo().equals(code.getCodeLevelNo().split("\\.")[0])){
                                    parent = tsSimpleCode;
                                    break;
                                }
                            }
                            if(parent!=null){
                                conclusionFlag = true;
                                break;
                            }
                        }
                    }
                }
            }
            if(conclusionFlag){
                RequestContext.getCurrentInstance().execute("PF('SubmitMhkRstConfirmDialog').show()");
                RequestContext.getCurrentInstance().update("tabView:editTabView");
                return;
            }
        }
        try{
            // 调用删除服务
            requestDelServiceForSub(bhk);
            // 复检调用复检记录处理逻辑接口
            requestClearService();
        }catch (Exception e){
            e.printStackTrace();
            JsfUtil.addErrorMessage(e.getMessage());
            return;
        }
        doMhkrsnSave();
        /**流转到业务表*/
        try{
            //+首次提交日期20220119
            if(null == tdTjBhkClt.getFirstSubmitDate()){
                tdTjBhkClt.setFirstSubmitDate(new Date());
                //首次数据报告是否及时
                //设置的要求上传天数（用来计算及时性）:setRequiredUploadTime
                if (null != tdTjBhkClt.getRptPrintDate() && StringUtils.isNotBlank(setRequiredUploadTime)) {
                    //计算规则：首次提交日期（当前日期）-报告出具日期
                    int days;
                    days = DateUtils.getDistanceOfWorkDay(tdTjBhkClt.getRptPrintDate(), new Date());
                    if (days > Integer.valueOf(setRequiredUploadTime)) {
                        tdTjBhkClt.setIfReportIntime(0);
                    } else {
                        tdTjBhkClt.setIfReportIntime(1);
                    }
                }
            }
            // 最新提交日期
            tdTjBhkClt.setSubmitChangedate(new Date());
            bhkService.initTdTjBhk(tdTjBhkClt);
            bhk = null;
            if (ObjectUtil.isNotEmpty(tdTjBhkClt.getBhkCode())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByBhkorgId())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByBhkorgId().getRid())) {
                bhk = this.bhkService.selectBhkByBhkCodeAndBhkOrgId(tdTjBhkClt.getBhkCode(), tdTjBhkClt.getFkByBhkorgId().getRid());
            }

            updateNeedFjButNoAndNewBhkrstId(1, bhk);
            tdTjBhkClt.setBhkRcdState(5);//赋值的不可以放在验证前，否则验证失败但赋值成功了，保存的时候会保存值
            service.saveOrUpdateTdTjBhkClt(tdTjBhkClt);
            JsfUtil.addSuccessMessage("提交成功！");
            this.backAction();
        }catch(Exception e){
            tdTjBhkClt.setBhkRcdState(4);//避免提交的时候 向数据库存储数据异常 导致赋值完成状态
            e.printStackTrace();
            JsfUtil.addErrorMessage("提交失败，请联系系统管理员！");
        }
    }
    /**
     *  <p>方法描述：更新应复检未检和最新体检结论</p>
     * @MethodAuthor hsj 2022-06-17 16:47
     */
    public void updateNeedFjButNoAndNewBhkrstId(Integer type, TdTjBhk bhk) throws Exception{
        String returnJson = null;
        try {
            if(null==bhk ){
                return;
            }
            //封装数据
            NeedFjJson json = new NeedFjJson();
            List<NeedFjListJson> needFjListJsons = new ArrayList<>();
            NeedFjListJson needFjListJson = new NeedFjListJson();
            needFjListJson.setType(type);
            needFjListJson.setBhkOrgRid(bhk.getTbTjSrvorg().getRid());
            needFjListJson.setPersionRid(bhk.getTdTjPerson().getRid());
            Integer ifFj = bhk.getIfRhk();
            if(ifFj == 1 && !StringUtils.isEmpty(bhk.getLastFstBhkCode())){
                needFjListJson.setLastFstBhkCode(bhk.getLastFstBhkCode());
                needFjListJson.setIsFj(1);
            }else {
                needFjListJson.setLastFstBhkCode(bhk.getBhkCode());
                needFjListJson.setIsFj(2);
            }
            needFjListJsons.add(needFjListJson);
            json.setBhkList(needFjListJsons);
            String requestJson = JSON.toJSONString(json);

            String encodeJson = null;
            String debug = PropertyUtils.getValue("encrypt.debug");
            String encryptKey = PropertyUtils.getValue("encrypt.key");
            if ("true".equals(debug)) {
                encodeJson = requestJson;
            }else {
                //加密
                encodeJson = AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
            }
            //调用接口
            String delUrl = PropertyUtils.getValue("delUrl");
            String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl+"/bhkNeedFjListUpdate",encodeJson);

            //解密
            if ("true".equals(debug)) {
                returnJson = reposeJson;
            }else {
                returnJson = AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("处理失败，请联系管理员！");
        }
        if(StringUtils.isNotBlank(returnJson)){
            ReturnDTO returnDTO = JSON.parseObject(returnJson, ReturnDTO.class);
            if(null == returnDTO || !ReturnType.SUCCESS_PROCESS.getTypeNo().equals(returnDTO.getType())){
                throw new Exception(null == returnDTO ? "处理失败，请联系管理员！" : returnDTO.getMess());
            }
        }
    }
    /** 调用接口清空初检记录的重点职业病相关标记及重点职业病结论的4张表以及删除本条复检记录关联的历次体检记录的重点危害因素删除日志 */
    public void requestClearService() throws Exception{
        //上次体检编号（复检使用）+初检编号（复检使用）--复检时存储
        if(null != tdTjBhkClt.getIfRhk() && tdTjBhkClt.getIfRhk().intValue() == 1){
            TdTjBhkClt fkByLastBhkId = service.find(TdTjBhkClt.class,tdTjBhkClt.getFkByLastBhkId().getRid());
            if(fkByLastBhkId != null){
                //通过体检编号找到业务表中的体检主表取LAST_FST_BHK_CODE
                TdTjBhk bhk = service.findTdTjBhkByUUID(fkByLastBhkId.getUuid());
                if(bhk != null && null != bhk.getRid()){
                    //接口中 可以传递初检rid或者初检下任一复检的rid 不需要限定初检或者复检
                    String returnJson = null;
                    try{
                        DeleteRidJson ridJson = new DeleteRidJson();
                        ridJson.setRid(bhk.getRid());
                        String requestJson = JSON.toJSONString(ridJson);
                        String encodeJson = null;
                        String debug = PropertyUtils.getValue("encrypt.debug");
                        String encryptKey = PropertyUtils.getValue("encrypt.key");
                        if ("true".equals(debug)) {
                            encodeJson = requestJson;
                        }else {
                            //加密
                            encodeJson = AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
                        }
                        //调用接口
                        String delUrl = PropertyUtils.getValue("delUrl");
                        String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl+"/beforeBhkfjAddClear",encodeJson);

                        //解密
                        if ("true".equals(debug)) {
                            returnJson = reposeJson;
                        }else {
                            returnJson = AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        throw new Exception("提交失败！");
                    }
                    if(StringUtils.isNotBlank(returnJson)){
                        ReturnDTO returnDTO = JSON.parseObject(returnJson, ReturnDTO.class);
                        if(null == returnDTO || !ReturnType.SUCCESS_PROCESS.getTypeNo().equals(returnDTO.getType())){
                            throw new Exception(null == returnDTO ? "提交失败！" : returnDTO.getMess());
                        }
                    }
                }
            }
        }
    }

    /***
     *  <p>方法描述：体检结论验证保存逻辑</p>
     *
     * @MethodAuthor maox,2019年5月30日,verySaveMhkRsn
     */
    public boolean verySubmitMhkRsn(){
    	String id = "tabView\\\\:editTabView\\\\:mhkrstListForm\\\\:";
    	boolean flag = false;
        /**必定勾选一结论*/
        if(!ncagSelect && !recheckSelect&&!otherSelect&&!contraindSelect&&!supoccdiseSelect){
        	Global.markErrorInfo(true, id+"ncagSelect"+","+id+"recheckSelect"+","+id+"otherSelect"+","+id+"contraindSelect"+","+id+"supoccdiseSelect", "请选择检查结论！");
            flag = true;
        }
        List<TsSimpleCode> allbadList = tdTjBhkClt.getBadList();
        List<String> badNames =new ArrayList<>();
        if(null != allbadList && allbadList.size() >0){
            for(TsSimpleCode a:allbadList){
                badNames.add(a.getCodeName());
            }
        }
        if(ncagSelect){
            if(null ==ncagBads || ncagBads.size() ==0){
                JsfUtil.addErrorMessage("请选择目前未见异常的危害因素！");
                flag = true;

            }else{
                for(TsSimpleCode a:ncagBads){
                    badNames.remove(a.getCodeName());
                }
            }
        }
        if(recheckSelect){
            if(null ==reCheckBads || reCheckBads.size() ==0){
                JsfUtil.addErrorMessage("请选择复查的危害因素！");
                flag = true;

            }else{
                for(TsSimpleCode a:reCheckBads){
                    badNames.remove(a.getCodeName());
                }
            }
        }
        if(otherSelect){
            if(StringUtils.isBlank(otherDesc)){
                JsfUtil.addErrorMessage("请输入其他疾病或异常描述！");
                flag = true;
            }
            if(null ==otherBads || otherBads.size() ==0){
                JsfUtil.addErrorMessage("请选择其他疾病或异常的危害因素！");
                flag = true;
            }else{
                for(TsSimpleCode a:otherBads){
                    badNames.remove(a.getCodeName());
                }
            }
        }
        //禁忌症选择后
        if(contraindSelect){
        	if(contraindCltWebList == null || contraindCltWebList.size() ==0){
        		JsfUtil.addErrorMessage("请选择禁忌证！");
                flag = true;
        	}else {
        		boolean ifTip = true;
        		int i = 0;
        		for(TdTjContraindCltWeb a:contraindCltWebList){
        			if(null == a.getBadRsnId()){
        				Global.markErrorInfo(ifTip, id+"contraindTable\\\\:"+i+"\\\\:badRsnId", "请选择禁忌证接触危害因素！");
        				ifTip = false;
        				flag = true;
        			}else{
                        TsSimpleCode b =commService.findTsSimpleCodeByRid(a.getBadRsnId());
                        badNames.remove(b.getCodeName());
                    }
                    i++;
        		}
			}
        }
        //疑似职业病选择后
        if(supoccdiseSelect){
            if(supoccdiseCltWebList == null || supoccdiseCltWebList.size() ==0){
                JsfUtil.addErrorMessage("请选择疑似职业病！");
                flag = true;
            }else{
            	boolean ifTip = true;
                int i = 0;
                for(TdTjSupoccdiseCltWeb a :supoccdiseCltWebList){
                	if(null == a.getBadRsnId()){
                		Global.markErrorInfo(ifTip, id+"supoccdiseTable\\\\:"+i+"\\\\:badRsnId0", "请选择疑似职业病接触危害因素！");
        				ifTip = false;
        				flag = true;
                	}else{
                        TsSimpleCode b =commService.findTsSimpleCodeByRid(a.getBadRsnId());
                        badNames.remove(b.getCodeName());
                    }
                    i++;
                }
            }
        }
        //@变更记录：由于勾选未见异常后，此判断不再继续，导致存在危害因素没有体检结论的情况，故去除未见异常已勾选的判断
        //@Date 2022年5月5日 11:55:04
        if(null != badNames && badNames.size()>0){
            String error = "";
            for(String a:badNames){
                error +=a+"、";
            }
            error = error.substring(0,error.length()-1);
            JsfUtil.addErrorMessage("请选择危害因素："+error+"的结论归属！");
            flag = true;
        }
        /**体检结果*/
        if(StringUtils.isBlank(tdTjBhkClt.getBhkrst())){
        	Global.markErrorInfo(true, id+"bhkrst", "请填写体检结果！");
            flag = true;
        }
        /**综合建议*/
        if(StringUtils.isBlank(tdTjBhkClt.getMhkadv())){
        	Global.markErrorInfo(true, id+"mhkadv", "请填写综合建议！");
            flag = true;
        }
        /*if(StringUtils.isBlank(mainChkdocId)){
        	Global.markErrorInfo(true, id+"mainChkdocId", "请选择主检医师！");
            flag = true;
        }*/
        if(CollectionUtils.isEmpty(this.tdTjChiefDoctorBean.getChiefDoctorList())) {
            Global.markErrorInfo(true, id+"mainChkdocId", "请选择主检医师！");
            flag = true;
        }
        if(tdTjBhkClt.getJdgdat() == null){
        	Global.markErrorInfo(true, id+"jdgdat_input", "请选择主检日期！");
            flag = true;
        }
        if(tdTjBhkClt.getRptPrintDate() == null){
        	Global.markErrorInfo(true, id+"rptPrintDate_input", "请选择报告出具日期！");
            flag = true;
        } else {
            if(this.tdTjBhkClt.getRptPrintDate().after(new Date())) {
                Global.markErrorInfo(true, id+"rptPrintDate_input", "报告出具日期应小于等于当天！");
                flag = true;
            }
        }
        return flag;
    }
    /**
     * <p>描述 提交验证（按危害因素选）</p>
     *
     * @MethodAuthor gongzhe,2022/4/28 15:49,verySubmitMhkRsnNew1
     * @return boolean
     */
    public boolean verySubmitMhkRsnNew1(){
    	String id = "tabView\\\\:editTabView\\\\:mhkrstListForm\\\\:";
    	boolean flag = false;
        /**每个危害因素都要选体检结论*/
        for(TdTjBadrsnsClt badrsnsClt:tdTjBadrsnsCltList){
            if(badrsnsClt.getExamConclusionId()==null){
                JsfUtil.addErrorMessage("请选择"+badrsnsClt.getFkByBadrsnId().getCodeName()+"的体检结论！");
                flag = true;
            }else if(badrsnsClt.getFkByExamConclusionId().getExtendS2()==3){
                //其他疾病或异常,文本框必填
                if(StringUtils.isBlank(badrsnsClt.getQtjbName())){
                    JsfUtil.addErrorMessage("请输入"+badrsnsClt.getFkByBadrsnId().getCodeName()+"的其他疾病或异常描述！");
                    flag = true;
                }
            }else if(badrsnsClt.getFkByExamConclusionId().getExtendS2()==4){
                TdTjContraindCltWeb contraindCltWeb = badrsnsClt.getContraindCltWeb();
                if(contraindCltWeb == null || CollectionUtils.isEmpty(contraindCltWeb.getContraindIdList())){
                    JsfUtil.addErrorMessage("请选择"+badrsnsClt.getFkByBadrsnId().getCodeName()+"的禁忌证！");
                    flag = true;
                }
            }else if(badrsnsClt.getFkByExamConclusionId().getExtendS2()==5){
                TdTjSupoccdiseCltWeb supoccdiseCltWeb = badrsnsClt.getSupoccdiseCltWeb();
                if(supoccdiseCltWeb == null || CollectionUtils.isEmpty(supoccdiseCltWeb.getSupoccdiseIdList())){
                    JsfUtil.addErrorMessage("请选择"+badrsnsClt.getFkByBadrsnId().getCodeName()+"的疑似职业病！");
                    flag = true;
                }
                if(ifBeforeOnPost && supoccdiseCltWeb!=null) {
                    if (StringUtils.isBlank(supoccdiseCltWeb.getCrptName())) {
                        JsfUtil.addErrorMessage("接触" + badrsnsClt.getFkByBadrsnId().getCodeName() + "的用人单位名称不能为空！");
                        flag = true;
                    } else if ((tdTjBhkClt.getFkByCrptId() != null
                            && supoccdiseCltWeb.getCrptName().equals(tdTjBhkClt.getFkByCrptId().getCrptName()))
                            || (tdTjBhkClt.getFkByEmpCrptId() != null
                            && supoccdiseCltWeb.getCrptName().equals(tdTjBhkClt.getFkByEmpCrptId().getCrptName()))) {
                        JsfUtil.addErrorMessage("接触" + badrsnsClt.getFkByBadrsnId().getCodeName() + "的用人单位名称不能和用人/用工单位名称相同！");
                        flag = true;
                    }
                }
            }
        }
        /**体检结果*/
        if(StringUtils.isBlank(tdTjBhkClt.getBhkrst())){
        	Global.markErrorInfo(true, id+"bhkrst", "请填写体检结果！");
            flag = true;
        }
        /**综合建议*/
        if(StringUtils.isBlank(tdTjBhkClt.getMhkadv())){
        	Global.markErrorInfo(true, id+"mhkadv", "请填写综合建议！");
            flag = true;
        }
        /*if(StringUtils.isBlank(mainChkdocId)){
        	Global.markErrorInfo(true, id+"mainChkdocId", "请选择主检医师！");
            flag = true;
        }*/
        if(CollectionUtils.isEmpty(this.tdTjChiefDoctorBean.getChiefDoctorList())) {
            Global.markErrorInfo(true, id+"mainChkdocId", "请选择主检医师！");
            flag = true;
        }
        if(tdTjBhkClt.getJdgdat() == null){
        	Global.markErrorInfo(true, id+"jdgdat_input", "请选择主检日期！");
            flag = true;
        }
        if(tdTjBhkClt.getRptPrintDate() == null){
        	Global.markErrorInfo(true, id+"rptPrintDate_input", "请选择报告出具日期！");
            flag = true;
        } else {
            if(this.tdTjBhkClt.getRptPrintDate().after(new Date())) {
                Global.markErrorInfo(true, id+"rptPrintDate_input", "报告出具日期应小于等于当天！");
                flag = true;
            }
        }
        return flag;
    }

    /***
     *  <p>方法描述：</p>
     *
     * @MethodAuthor maox,2019年5月31日,dosave
     */
    public void doMhkrsnSave(){
        /**主检主题结论（数据录入）*/
        List<TdTjMhkrstClt> mhkrstClts = new ArrayList<TdTjMhkrstClt>(0);
        /**疑似职业病人群（数据录入）*/
        List<TdTjSupoccdiseClt> supoccdiseClts = new ArrayList<TdTjSupoccdiseClt>(0);
        /**职业禁忌证人群（数据录入）*/
        List<TdTjContraindClt> contraindClts = new ArrayList<TdTjContraindClt>(0);
        /**危害因素变化*/
        List<TdTjBadrsnsClt> tdTjBadrsnsClts = new ArrayList<TdTjBadrsnsClt>(0);
        /**+主检医师（数据录入）20210322*/
        List<TdTjChiefDoctor> tdTjChiefDoctorList = new ArrayList<TdTjChiefDoctor>(0);

        List<TsSimpleCode> Allbads = tdTjBhkClt.getBadList();
        Set<Integer> allRids = new HashSet<>();
        if(null != Allbads && Allbads.size()>0){
            for (TsSimpleCode a:Allbads){
                allRids.add(a.getRid());
            }
        }
        if(hasBhkResultStyle){
            //体检结论
            Set<Integer> examConclusionSet = new HashSet<>();
            //检查结论调整为根据危害因素选取体检结论
            for(TdTjBadrsnsClt badrsnsClt :tdTjBadrsnsCltList){
                TsSimpleCode badRsn = badrsnsClt.getFkByBadrsnId();
                if(badrsnsClt.getFkByExamConclusionId()!=null){
                    //主检主题结论,去重
                    if(!examConclusionSet.contains(badrsnsClt.getFkByExamConclusionId().getRid())){
                        mhkrstClts.add(initTdTjMhkrstClt(badrsnsClt.getFkByExamConclusionId().getExtendS2()));
                        examConclusionSet.add(badrsnsClt.getFkByExamConclusionId().getRid());
                    }
                    if(badrsnsClt.getFkByExamConclusionId().getExtendS2() != 3){
                        //非其他疾病，清空其他描述
                        badrsnsClt.setQtjbName(null);
                    }
                    if(badrsnsClt.getFkByExamConclusionId().getExtendS2() == 4){
                        //职业禁忌证人群
                        TdTjContraindCltWeb contraid = badrsnsClt.getContraindCltWeb();
                        List<Integer> contraindIdList = contraid.getContraindIdList();
                        for(Object ojjzId :contraindIdList){
                            //封装禁忌症实体
                            Integer jjzId = Integer.parseInt(ojjzId.toString());
                            TdTjContraindClt contraindClt = new TdTjContraindClt();
                            TsSimpleCode jjz = commService.findTsSimpleCodeByRid(jjzId);
                            contraindClt.setFkByBadrsnId(badRsn);
                            contraindClt.setFkByContraindId(jjz);
                            contraindClt.setFkByBhkId(tdTjBhkClt);
                            contraindClt.setCreateDate(new Date());
                            contraindClt.setCreateManid(Global.getUser().getRid());
                            contraindClts.add(contraindClt);
                        }
                    }else if(badrsnsClt.getFkByExamConclusionId().getExtendS2() == 5){
                        //疑似职业病人群
                        TdTjSupoccdiseCltWeb supoccdise = badrsnsClt.getSupoccdiseCltWeb();
                        List<Integer> supoccdiseIdList =  supoccdise.getSupoccdiseIdList();
                        for(Object ooccdiseId :supoccdiseIdList){
                            //封装疑似职业病实体
                            Integer occdiseId = Integer.parseInt(ooccdiseId.toString());
                            TdTjSupoccdiseClt supoccdiseClt = new TdTjSupoccdiseClt();
                            TsSimpleCode zyb = commService.findTsSimpleCodeByRid(occdiseId);
                            supoccdiseClt.setFkByBadrsnId(badRsn);
                            supoccdiseClt.setFkByOccDiseid(zyb);
                            supoccdiseClt.setFkByBhkId(tdTjBhkClt);
                            supoccdiseClt.setCrptName(this.ifBeforeOnPost?supoccdise.getCrptName():null);
                            supoccdiseClt.setCreateDate(new Date());
                            supoccdiseClt.setCreateManid(Global.getUser().getRid());
                            supoccdiseClts.add(supoccdiseClt);
                        }
                    }
                }
                //危害因素
                tdTjBadrsnsClts.add(badrsnsClt);
            }
        }else{
            //未见异常选择
            if(ncagSelect){
                //主见主题结论
                TdTjMhkrstClt mhkRst = initTdTjMhkrstClt(1);
                mhkrstClts.add(mhkRst);
                for(TsSimpleCode a:ncagBads){
                    if(allRids.contains(a.getRid())){
                        allRids.remove(a.getRid());
                    }
                    TdTjBadrsnsClt bad = new TdTjBadrsnsClt();
                    bad.setFkByBhkId(tdTjBhkClt);
                    bad.setFkByBadrsnId(a);
                    bad.setFkByExamConclusionId(mhkRst.getFkByBhkrstId());
                    bad.setCreateDate(new Date());
                    bad.setCreateManid(1);
                    tdTjBadrsnsClts.add(bad);
                }
            }
            //复查
            if(recheckSelect){
                TdTjMhkrstClt mhkRst = initTdTjMhkrstClt(2);
                mhkrstClts.add(mhkRst);

                for(TsSimpleCode a:reCheckBads){
                    if(allRids.contains(a.getRid())){
                        allRids.remove(a.getRid());
                    }
                    TdTjBadrsnsClt bad = new TdTjBadrsnsClt();
                    bad.setFkByBhkId(tdTjBhkClt);
                    bad.setFkByBadrsnId(a);
                    bad.setFkByExamConclusionId(mhkRst.getFkByBhkrstId());
                    bad.setCreateDate(new Date());
                    bad.setCreateManid(1);
                    tdTjBadrsnsClts.add(bad);
                }
            }
            //其他疾病或异常
            if(otherSelect){
                TdTjMhkrstClt mhkRst = initTdTjMhkrstClt(3);
                mhkrstClts.add(mhkRst);

                for(TsSimpleCode a:otherBads){
                    if(allRids.contains(a.getRid())){
                        allRids.remove(a.getRid());
                    }
                    TdTjBadrsnsClt bad = new TdTjBadrsnsClt();
                    bad.setFkByBhkId(tdTjBhkClt);
                    bad.setFkByBadrsnId(a);
                    bad.setFkByExamConclusionId(mhkRst.getFkByBhkrstId());
                    bad.setQtjbName(otherDesc);
                    bad.setCreateDate(new Date());
                    bad.setCreateManid(1);
                    tdTjBadrsnsClts.add(bad);
                }
            }
            //禁忌症
            if(contraindSelect){
                TdTjMhkrstClt mhkRst = initTdTjMhkrstClt(4);
                mhkrstClts.add(mhkRst);

                //验证不能为空，不能重复危害因素
                if(contraindCltWebList != null && contraindCltWebList.size() !=0){
                    Set<String>  badSet = new HashSet<>();
                    //体检主表禁忌症封装
                    contraindClts = initContraindClts(contraindCltWebList);
                    for(TdTjContraindClt clt:contraindClts){
                        if(badSet.contains(clt.getFkByBadrsnId().getCodeNo())){
                            continue;
                        }else{
                            badSet.add(clt.getFkByBadrsnId().getCodeNo());
                        }
                        TdTjBadrsnsClt bad = new TdTjBadrsnsClt();
                        bad.setFkByBhkId(tdTjBhkClt);
                        bad.setFkByBadrsnId(clt.getFkByBadrsnId());
                        if(allRids.contains(clt.getFkByBadrsnId().getRid())){
                            allRids.remove(clt.getFkByBadrsnId().getRid());
                        }
                        bad.setFkByExamConclusionId(mhkRst.getFkByBhkrstId());
                        bad.setCreateDate(new Date());
                        bad.setCreateManid(1);
                        tdTjBadrsnsClts.add(bad);
                    }
                }

            }
            //职业病
            if(supoccdiseSelect){
                TdTjMhkrstClt mhkRst = initTdTjMhkrstClt(5);
                mhkrstClts.add(mhkRst);

                //验证不能为空，不能重复危害因素
                if(supoccdiseCltWebList != null && supoccdiseCltWebList.size() !=0){
                    //体检主表禁忌症封装
                    supoccdiseClts = initSupoccdiseClts(supoccdiseCltWebList);
                    Set<String>  badSet = new HashSet<>();
                    for(TdTjSupoccdiseClt clt:supoccdiseClts){
                        if(badSet.contains(clt.getFkByBadrsnId().getCodeNo())){
                            continue;
                        }else{
                            badSet.add(clt.getFkByBadrsnId().getCodeNo());
                        }
                        TdTjBadrsnsClt bad = new TdTjBadrsnsClt();
                        bad.setFkByBhkId(tdTjBhkClt);
                        bad.setFkByBadrsnId(clt.getFkByBadrsnId());
                        if(allRids.contains(clt.getFkByBadrsnId().getRid())){
                            allRids.remove(clt.getFkByBadrsnId().getRid());
                        }
                        bad.setFkByExamConclusionId(mhkRst.getFkByBhkrstId());
                        bad.setCreateDate(new Date());
                        bad.setCreateManid(1);
                        tdTjBadrsnsClts.add(bad);
                    }
                }
            }
            if(null != allRids && allRids.size()>0){
                for(Integer a:allRids){
                    if(null != Allbads && Allbads.size()>0){
                        for(TsSimpleCode code:Allbads){
                            if(a.equals(code.getRid())){
                                TdTjBadrsnsClt bad = new TdTjBadrsnsClt();
                                bad.setFkByBhkId(tdTjBhkClt);
                                bad.setFkByBadrsnId(code);
                                bad.setCreateDate(new Date());
                                bad.setCreateManid(1);
                                tdTjBadrsnsClts.add(bad);
                            }
                        }

                    }
                }
            }
        }

        //设置主表的体检结论
        tdTjBhkClt.setTdTjMhkrstClts(mhkrstClts);
        tdTjBhkClt.setTdTjSupoccdiseClts(supoccdiseClts);
        tdTjBhkClt.setTdTjContraindClts(contraindClts);
        tdTjBhkClt.setTdTjBadrsnsClts(tdTjBadrsnsClts);
        //主检医生
        if(StringUtils.isNotBlank(mainChkdocId)){
            TdZwPsninfoComm fkByMhkdctId = service.findTdZwPsninfobyID(Integer.parseInt(mainChkdocId));
            tdTjBhkClt.setFkByMhkdctId(fkByMhkdctId);
        }else{
            tdTjBhkClt.setFkByMhkdctId(null);
        }

        // 主检医师设置
        if(!CollectionUtils.isEmpty(this.tdTjChiefDoctorBean.getChiefDoctorList())) {
            for(TdZwPsninfoComm tdZwPsninfoComm : this.tdTjChiefDoctorBean.getChiefDoctorList()) {
                TdTjChiefDoctor tdTjChiefDoctor = new TdTjChiefDoctor();
                tdTjChiefDoctor.setFkByBhkId(this.tdTjBhkClt);
                tdTjChiefDoctor.setFkByMhkdctId(tdZwPsninfoComm);
                tdTjChiefDoctor.setCreateDate(new Date());
                tdTjChiefDoctor.setCreateManid(Global.getUser().getRid());
                tdTjChiefDoctorList.add(tdTjChiefDoctor);
            }
        }
        tdTjBhkClt.setTdTjChiefDoctorList(tdTjChiefDoctorList);
    }

    /***
     *  <p>方法描述：保存验证</p>
     *
     * @MethodAuthor maox,2019年5月31日,verySaveMhkRsn
     */
    public boolean verySaveMhkRsn(){
    	String id = "tabView\\\\:editTabView\\\\:mhkrstListForm\\\\:";
        boolean flag = false;
        Set<Integer> badIds = new HashSet<>();
        Set<String> badNames = new HashSet<>();
        if(ncagSelect){
            if(null !=ncagBads && ncagBads.size()>0){
                for(TsSimpleCode a :ncagBads){
                    if(badIds.contains(a.getRid())){
                        badNames.add(a.getCodeName());
                        flag =true;
                    }else{
                        badIds.add(a.getRid());
                    }
                }
            }
        }
        if(recheckSelect){
            if(null !=reCheckBads && reCheckBads.size()>0){
                for(TsSimpleCode a :reCheckBads){
                    if(badIds.contains(a.getRid())){
                        badNames.add(a.getCodeName());
                        flag =true;
                    }else{
                        badIds.add(a.getRid());
                    }
                }
            }
        }

        if(otherSelect){
            if(null !=otherBads && otherBads.size()>0){
                for(TsSimpleCode a :otherBads){
                    if(badIds.contains(a.getRid())){
                        badNames.add(a.getCodeName());
                        flag =true;
                    }else{
                        badIds.add(a.getRid());
                    }
                }
            }
        }


        /**禁忌症选择*/
        if(contraindSelect){
            //验证不能为空，不能重复危害因素
            if(contraindCltWebList != null && contraindCltWebList.size() !=0){
                int size = contraindCltWebList.size();
                HashSet<Integer> set = new HashSet<Integer>();
                //检查是否选择危害因素
                int i = 0;
                for(TdTjContraindCltWeb contraind:contraindCltWebList){
                    if(contraind != null){
                        if(contraind.getBadRsnId() == null){
                            size = size -1;
                            continue;
                        }else if(contraind.getContraindIdList() == null || contraind.getContraindIdList().size()==0){
                            set.add(contraind.getBadRsnId());
                            TsSimpleCode a =commService.findTsSimpleCodeByRid(contraind.getBadRsnId());
                            if(badIds.contains(a.getRid())){
                                badNames.add(a.getCodeName());
                            }else{
                                badIds.add(a.getRid());
                            }
                            Global.markErrorInfo(true, id+"contraindTable\\\\:"+i+"\\\\:types", "请选择"+a.getCodeName()+"的禁忌证！");
                            flag = true;
                        }else{
                            set.add(contraind.getBadRsnId());
                            TsSimpleCode a =commService.findTsSimpleCodeByRid(contraind.getBadRsnId());
                            if(badIds.contains(a.getRid())){
                                badNames.add(a.getCodeName());
                            }else{
                                badIds.add(a.getRid());
                            }
                        }
                    }
                    i++;

                }
            }
        }
        //职业病
        //验证不能为空，不能重复危害因素
        if(supoccdiseSelect && supoccdiseCltWebList != null && supoccdiseCltWebList.size() !=0) {
            int size = supoccdiseCltWebList.size();
            //检查是否选择危害因素
            int i = 0;
            for (TdTjSupoccdiseCltWeb supOccdise : supoccdiseCltWebList) {
                if (supOccdise == null || supOccdise.getBadRsnId() == null) {
                    size = size - 1;
                    continue;
                }
                TsSimpleCode badRsn = commService.findTsSimpleCodeByRid(supOccdise.getBadRsnId());
                if (badRsn == null) {
                    continue;
                }
                if (badIds.contains(badRsn.getRid())) {
                    badNames.add(badRsn.getCodeName());
                } else {
                    badIds.add(badRsn.getRid());
                }
                if (supOccdise.getSupoccdiseIdList() == null || supOccdise.getSupoccdiseIdList().size() == 0) {
                    Global.markErrorInfo(true, id + "supoccdiseTable\\\\:" + i + "\\\\:occdisetypes", "请选择" + badRsn.getCodeName() + "的疑似职业病！");
                    flag = true;
                }
                if (this.ifBeforeOnPost) {
                    if (StringUtils.isBlank(supOccdise.getCrptName())) {
                        Global.markErrorInfo(true, id + "supoccdiseTable\\\\:" + i + "\\\\:occdisetypes", "接触" + badRsn.getCodeName() + "的用人单位名称不能为空！");
                        flag = true;
                    }else if (((tdTjBhkClt.getFkByCrptId() != null && supOccdise.getCrptName().equals(tdTjBhkClt.getFkByCrptId().getCrptName())) ||
                            (tdTjBhkClt.getFkByEmpCrptId() != null
                                    && supOccdise.getCrptName().equals(tdTjBhkClt.getFkByEmpCrptId().getCrptName())))) {
                        Global.markErrorInfo(true, id + "supoccdiseTable\\\\:" + i + "\\\\:occdisetypes", "接触" + badRsn.getCodeName() + "职业病危害因素的用人单位名称不能和用人/用工单位名称相同！");
                        flag = true;
                    }
                }

            }
        }

        if(null!=badNames &&badNames.size()>0){
            flag = true;
            String repetNames = "";
            for(String a:badNames){
                repetNames +=a+"、";
            }
            repetNames = repetNames.substring(0,repetNames.length()-1);
            JsfUtil.addErrorMessage("危害因素："+repetNames+"的结论重复！");
        }
        //判定日期需要大于检查日期
        Date maxChkdat=null;
        if(!CollectionUtils.isEmpty(tdTjBhkClt.getTdTjBhksubClts())) {
            for (TdTjBhksubClt bhksubClt : tdTjBhkClt.getTdTjBhksubClts()) {
                if (bhksubClt.getChkdat() == null) {
                    continue;
                }
                if (maxChkdat == null) {
                    maxChkdat = bhksubClt.getChkdat();
                    continue;
                }
                if (maxChkdat.before(bhksubClt.getChkdat())) {
                    maxChkdat = bhksubClt.getChkdat();
                }
            }
        }
        if (tdTjBhkClt.getJdgdat() != null && maxChkdat != null && tdTjBhkClt.getJdgdat().before(maxChkdat)) {
            Global.markErrorInfo(true, id+"jdgdat_input", "主检日期必须大于等于检查日期:"+DateUtils.formatDate(maxChkdat, "yyyy-MM-dd")+"！");
            flag = true;
        }

        if(null != this.tdTjBhkClt.getRptPrintDate()) {
            if(this.tdTjBhkClt.getRptPrintDate().after(new Date())) {
                Global.markErrorInfo(true, id+"jdgdat_input", "报告出具日期应小于等于当天！");
                flag = true;
            }
        }

        //报告出具日期大于等于主检日期
        if(null != tdTjBhkClt.getJdgdat() && null != tdTjBhkClt.getRptPrintDate() && DateUtils.isDateAfter(tdTjBhkClt.getJdgdat(), tdTjBhkClt.getRptPrintDate())){
        	Global.markErrorInfo(true, id+"rptPrintDate_input", "报告出具日期应大于等于主检日期:"+DateUtils.formatDate(tdTjBhkClt.getJdgdat(), "yyyy-MM-dd")+"！");
            flag = true;
        }

        return flag;
    }
    /**
     * <p>描述 保存验证（新需求-按危害因素选）</p>
     *
     * @MethodAuthor gongzhe,2022/4/28 15:44,verySaveMhkRsnNew1
     * @return boolean
     */
    public boolean verySaveMhkRsnNew1(){
    	String id = "tabView\\\\:editTabView\\\\:mhkrstListForm\\\\:";
        boolean flag = false;

        //校验单位名称不能与用人单位、用工单位名称相同
        for(TdTjBadrsnsClt badrsnsClt:tdTjBadrsnsCltList) {
            if (badrsnsClt.getFkByExamConclusionId()==null || !new Integer("5").equals(badrsnsClt.getFkByExamConclusionId().getExtendS2())) {
                continue;
            }
            TdTjSupoccdiseCltWeb supoccdiseCltWeb = badrsnsClt.getSupoccdiseCltWeb();
            if (!ifBeforeOnPost || supoccdiseCltWeb == null) {
                continue;
            }
            if (StringUtils.isNotBlank(supoccdiseCltWeb.getCrptName())
                    && ((tdTjBhkClt.getFkByCrptId() != null
                    && supoccdiseCltWeb.getCrptName().equals(tdTjBhkClt.getFkByCrptId().getCrptName())) ||
                    (tdTjBhkClt.getFkByEmpCrptId() != null
                            && supoccdiseCltWeb.getCrptName().equals(tdTjBhkClt.getFkByEmpCrptId().getCrptName())))) {
                JsfUtil.addErrorMessage("接触" + badrsnsClt.getFkByBadrsnId().getCodeName() + "职业病危害因素的用人单位名称不能和用人/用工单位名称相同！");
                flag = true;
            }
        }

        //判定日期需要大于最大的检查日期
        Date maxChkdat=null;
        if(!CollectionUtils.isEmpty(tdTjBhkClt.getTdTjBhksubClts())) {
            for (TdTjBhksubClt bhksubClt : tdTjBhkClt.getTdTjBhksubClts()) {
                if (bhksubClt.getChkdat() == null) {
                    continue;
                }
                if (maxChkdat == null) {
                    maxChkdat = bhksubClt.getChkdat();
                    continue;
                }
                if (maxChkdat.before(bhksubClt.getChkdat())) {
                    maxChkdat = bhksubClt.getChkdat();
                }
            }
        }
        if (tdTjBhkClt.getJdgdat() != null && maxChkdat != null && tdTjBhkClt.getJdgdat().before(maxChkdat)) {
            Global.markErrorInfo(true, id + "jdgdat_input", "主检日期必须大于等于检查日期:" + DateUtils.formatDate(maxChkdat, "yyyy-MM-dd") + "！");
            flag = true;
        }

        if(null != this.tdTjBhkClt.getRptPrintDate()) {
            if(this.tdTjBhkClt.getRptPrintDate().after(new Date())) {
                Global.markErrorInfo(true, id+"jdgdat_input", "报告出具日期应小于等于当天！");
                flag = true;
            }
        }

        //报告出具日期大于等于主检日期
        if(null != tdTjBhkClt.getJdgdat() && null != tdTjBhkClt.getRptPrintDate() && DateUtils.isDateAfter(tdTjBhkClt.getJdgdat(), tdTjBhkClt.getRptPrintDate())){
        	Global.markErrorInfo(true, id+"rptPrintDate_input", "报告出具日期应大于等于主检日期:"+DateUtils.formatDate(tdTjBhkClt.getJdgdat(), "yyyy-MM-dd")+"！");
            flag = true;
        }

        return flag;
    }

    /***
     *  <p>方法描述：封装疑似职业病</p>
     *
     * @MethodAuthor maox,2019年5月30日,initSupoccdiseClts
     * @param supoccdises
     * @return
     */
    private List<TdTjSupoccdiseClt> initSupoccdiseClts(List<TdTjSupoccdiseCltWeb> supoccdises){
        List<TdTjSupoccdiseClt> supoccdiseClts = new ArrayList<>();
        if(supoccdises != null && supoccdises.size() >0){
            for(TdTjSupoccdiseCltWeb supoccdise:supoccdises){
                //第一层遍历危害因素
                if(supoccdise.getBadRsnId() !=null){
                    TsSimpleCode badRsn = commService.findTsSimpleCodeByRid(supoccdise.getBadRsnId());
                    //第二层遍历勾选的疑似职业病
                    if(supoccdise.getSupoccdiseIdList() != null && supoccdise.getSupoccdiseIdList().size() >0){
                        for(Object ooccdiseId :supoccdise.getSupoccdiseIdList()){
                            //封装疑似职业病实体
                            Integer occdiseId = Integer.parseInt(ooccdiseId.toString());
                            TdTjSupoccdiseClt supoccdiseClt = new TdTjSupoccdiseClt();
                            TsSimpleCode zyb = commService.findTsSimpleCodeByRid(occdiseId);
                            supoccdiseClt.setFkByBadrsnId(badRsn);
                            supoccdiseClt.setFkByOccDiseid(zyb);
                            supoccdiseClt.setFkByBhkId(tdTjBhkClt);
                            supoccdiseClt.setCrptName(ifBeforeOnPost?supoccdise.getCrptName():null);
                            supoccdiseClt.setCreateDate(new Date());
                            supoccdiseClt.setCreateManid(Global.getUser().getRid());
                            supoccdiseClts.add(supoccdiseClt);
                        }
                    }
                }
            }
        }
        return supoccdiseClts;
    }


    /***
     *  <p>方法描述：根据勾选的职业禁忌证封装体检主表的职业禁忌证字表list</p>
     *
     * @MethodAuthor maox,2019年5月30日,initContraindClts
     * @param contrainds
     * @return
     */
    private List<TdTjContraindClt> initContraindClts(List<TdTjContraindCltWeb> contrainds){
        List<TdTjContraindClt> contraindClts = new ArrayList<>();
        if(contrainds != null && contrainds.size() >0){
            for(TdTjContraindCltWeb contraid:contrainds){
                //第一层遍历危害因素
                if(contraid.getBadRsnId() !=null){
                    TsSimpleCode badRsn = commService.findTsSimpleCodeByRid(contraid.getBadRsnId());
                    //第二层遍历勾选的禁忌症
                    if(contraid.getContraindIdList() != null && contraid.getContraindIdList().size() >0){
                        List<Integer> aa = contraid.getContraindIdList();
                        for(Object ojjzId :aa){
                            //封装禁忌症实体
                            Integer jjzId = Integer.parseInt(ojjzId.toString());
                            TdTjContraindClt contraindClt = new TdTjContraindClt();
                            TsSimpleCode jjz = commService.findTsSimpleCodeByRid(jjzId);
                            contraindClt.setFkByBadrsnId(badRsn);
                            contraindClt.setFkByContraindId(jjz);
                            contraindClt.setFkByBhkId(tdTjBhkClt);
                            contraindClt.setCreateDate(new Date());
                            contraindClt.setCreateManid(Global.getUser().getRid());
                            contraindClts.add(contraindClt);
                        }
                    }
                }
            }
        }
        return contraindClts;
    }


    /***
     *  <p>方法描述：根据扩展字段2获取体检结论实体</p>
     *
     * @MethodAuthor maox,2019年5月30日,initTdTjMhkrstClt
     * @param extends2
     * @return
     */
    private TdTjMhkrstClt initTdTjMhkrstClt(Integer extends2){
        TdTjMhkrstClt mhkRst = new TdTjMhkrstClt();
        mhkRst.setFkByBhkId(tdTjBhkClt);//体检主表
        TsSimpleCode a =service.findTsSimpleCodeByextendS2(extends2);//体检结论
        mhkRst.setFkByBhkrstId(a);
        mhkRst.setCreateDate(new Date());
        mhkRst.setCreateManid(Global.getUser().getRid());
        return mhkRst;
    }

    /**
     * @Description : 撤销操作
     * @MethodAuthor: anjing
     * @Date : 2019/5/30 17:28
     **/
    public void repealAction() {
        if (!beforAddInit()) {
            return;
        }
        TdTjBhkClt tdTjBhkClt1 = this.service.selectBhkCltByRid(this.rid);
        TdTjBhk bhk = null;
        if (ObjectUtil.isNotEmpty(tdTjBhkClt1.getBhkCode())
                && ObjectUtil.isNotEmpty(tdTjBhkClt1.getFkByBhkorgId())
                && ObjectUtil.isNotEmpty(tdTjBhkClt1.getFkByBhkorgId().getRid())) {
            bhk = this.bhkService.selectBhkByBhkCodeAndBhkOrgId(tdTjBhkClt1.getBhkCode(), tdTjBhkClt1.getFkByBhkorgId().getRid());
        }
        if (verifyRptPrintDateInZdzybDate(bhk, null) != 0) {
            JsfUtil.addErrorMessage("重点职业病监测统计中，不允许撤销监测周期范围内的数据！");
            return;
        }
        this.tdTjBhkClt = tdTjBhkClt1;
        if (this.tdTjBhkClt.getBhkRcdState() != 5) {
            JsfUtil.addErrorMessage("未完成体检录入信息不能被撤销！");
            return;
        }
        if (ObjectUtil.isNotEmpty(bhk) && new Integer(6).equals(bhk.getState())) {
            String ifUpdateBhk = this.commService.findParamValue("IF_UPDATE_BHK");
            if ("0".equals(ifUpdateBhk)) {
                JsfUtil.addErrorMessage("体检数据已终审通过，若需更新请联系终审机构退回！");
                return;
            }
        }
        workTypeName = "";
        ifOtherWork = false;
        initBadList();
        try {
            requestDelService();
            updateNeedFjButNoAndNewBhkrstId(3, bhk);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage(e.getMessage());
            return;
        }
        try {
            this.tdTjBhkClt.setBhkRcdState(4);
            bhkService.delTdTjBhk(tdTjBhkClt);
        } catch (Exception e) {
            e.printStackTrace();
            JsfUtil.addErrorMessage("撤销失败！");
            return;
        }
        if (null != this.view) {
            if (tdTjBhkClt.getIfRhk().intValue() == 0) {
                addActiveTab = 3;
                RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:mhkrstListForm:mhkrstTitleGrid");
            } else {
                addActiveTab = 2;
                RequestContext.getCurrentInstance().scrollTo("tabView:editTabView:mhkrstListForm:mhkrstTitleGrid");
            }
        }
        this.view = null;
        JsfUtil.addSuccessMessage("撤销成功！");
    }

    /**
     * 根据当前体检记录以及当前录入体检记录，判断对应录入/初检记录报告打印日期是否在重点职业病体检日期内
     *
     * @param bhk        当前体检记录
     * @param tdTjBhkClt 当前录入体检记录
     * @return 对应初检记录报告打印日期是否在重点职业病体检日期内
     */
    private int verifyRptPrintDateInZdzybDate(TdTjBhk bhk, TdTjBhkClt tdTjBhkClt) {
        int type = 2;
        if (this.reviseBhkInZdzyb || CollectionUtils.isEmpty(this.reviseBhkInZdzybYearList)) {
            return 0;
        }
        boolean ifRhk = tdTjBhkClt != null && new Integer(1).equals(tdTjBhkClt.getIfRhk());
        //体检录入记录非复检且报告打印日期不为空
        boolean ifCltNeedVerifyDate = !ifRhk && tdTjBhkClt != null && tdTjBhkClt.getRptPrintDate() != null;
        if (bhk == null || bhk.getRptPrintDate() == null) {
            //先去找上一次体检
            if (ObjectUtil.isNotEmpty(tdTjBhkClt)
                    && new Integer(1).equals(tdTjBhkClt.getIfRhk())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByLastBhkId())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByLastBhkId().getBhkCode())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByLastBhkId().getFkByBhkorgId())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByLastBhkId().getFkByBhkorgId().getRid())) {
                bhk = this.bhkService.selectBhkByBhkCodeAndBhkOrgId(tdTjBhkClt.getFkByLastBhkId().getBhkCode(), tdTjBhkClt.getFkByLastBhkId().getFkByBhkorgId().getRid());
            }
        }
        if (bhk == null || bhk.getRptPrintDate() == null) {
            //校验当前录入报告打印日期是否在重点职业病体检日期内
            if (ifCltNeedVerifyDate && verifyDateIn2Date(tdTjBhkClt.getRptPrintDate())) {
                return 1;
            }
            return 0;
        }
        //复检去找初检
        if (bhk.getIfRhk().equals(1)) {
            type = 3;
            bhk = this.bhkService.selectBhkRptDateByBhkCodeAndBhkOrgId(bhk.getLastFstBhkCode(), bhk.getTbTjSrvorg().getRid());
        }
        if (bhk == null || bhk.getRptPrintDate() == null) {
            return 0;
        }
        //校验初检报告打印日期是否在重点职业病体检日期内
        if (verifyDateIn2Date(bhk.getRptPrintDate())) {
            return type;
        }
        //校验当前录入报告打印日期是否在重点职业病体检日期内
        if (ifCltNeedVerifyDate && verifyDateIn2Date(tdTjBhkClt.getRptPrintDate())) {
            return type;
        }
        return 0;
    }

    /**
     * 校验日期是否在上一年11月16号到当年11月15号之间
     *
     * @param date 日期
     * @return 日期是否在上一年11月16号到当年11月15号之间
     */
    private boolean verifyDateIn2Date(Date date) {
        int year = DateUtils.getYear(date);
        int curYear = DateUtils.getYear(new Date());
        int yearDiff = year-curYear;
        Date maxDate = DateUtils.getFixedDate(yearDiff,11,15);
        // 如果报告出具日期大于报告出具日期年份的11月15日 那么报告出具日期属于下一年份的重点职业病统计范围
        if (maxDate.before(date)) {
            year = year + 1;
        }
        // 监测年份内的数据 禁止修改
        if (this.reviseBhkInZdzybYearList.contains(String.valueOf(year))) {
            return true;
        }
        return false;
    }

    /**
 	 * <p>方法描述：请求删除服务</p>
     * @throws Exception
 	 * @MethodAuthor qrr,2020年9月16日,requestDelService
     * */
    private void requestDelService() throws Exception {
    	String returnJson = null;
    	try {
            TdTjBhk bhk = null;
            if (ObjectUtil.isNotEmpty(tdTjBhkClt.getBhkCode())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByBhkorgId())
                    && ObjectUtil.isNotEmpty(tdTjBhkClt.getFkByBhkorgId().getRid())) {
                bhk = this.bhkService.selectBhkByBhkCodeAndBhkOrgId(tdTjBhkClt.getBhkCode(), tdTjBhkClt.getFkByBhkorgId().getRid());
            }
            if(null==bhk){
                return;
            }
            returnJson = getDelServiceJson(bhk);
        } catch (Exception e) {
			e.printStackTrace();
			throw new Exception("撤销失败！");
		}
    	ReturnRidListDTO pojo = JSON.parseObject(returnJson,ReturnRidListDTO.class);
        List<ReturnRidDTO> ridList = pojo.getRidList();
        if(CollectionUtils.isEmpty(ridList)){
        	throw new Exception("撤销失败！");
        }
    	for (ReturnRidDTO dto: ridList) {
			if (ReturnType.NO_DATA.getTypeNo().equals(dto.getType())
					|| ReturnType.EXCEPTION_PROCESS.getTypeNo().equals(dto.getType())) {
				throw new Exception("撤销失败！");
			}
            if(!ReturnType.SUCCESS_PROCESS.getTypeNo().equals(dto.getType())){
            	throw new Exception("体检数据已被关联，无法撤销！");
            }
        }

	}

    /**
     * 提交操作-请求删除服务
     */
    private void requestDelServiceForSub(TdTjBhk bhk) throws Exception {
        String returnJson = null;
        try {
            if (bhk == null) {
                return;
            }
            returnJson = getDelServiceJson(bhk);
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("提交失败！");
        }
        ReturnRidListDTO pojo = JSON.parseObject(returnJson, ReturnRidListDTO.class);
        List<ReturnRidDTO> ridList = pojo.getRidList();
        if (CollectionUtils.isEmpty(ridList)) {
            throw new Exception("提交失败！");
        }
        for (ReturnRidDTO dto : ridList) {
            if (ReturnType.NO_DATA.getTypeNo().equals(dto.getType())
                    || ReturnType.EXCEPTION_PROCESS.getTypeNo().equals(dto.getType())) {
                throw new Exception("提交失败！");
            }
            if (!ReturnType.SUCCESS_PROCESS.getTypeNo().equals(dto.getType())) {
                throw new Exception("体检数据已被关联，无法提交！");
            }
        }
    }

    private String getDelServiceJson(TdTjBhk bhk) throws Exception {
        //封装数据
        DelListJson json = new DelListJson();
        json.setIfDelBhkTable("1");
        List<DeleteRidJson> delList = new ArrayList<>();
        DeleteRidJson ridJson = new DeleteRidJson();
        ridJson.setRid(bhk.getRid());
        delList.add(ridJson);
        json.setDelList(delList);
        String requestJson = JSON.toJSONString(json);

        String encodeJson = null;
        String debug = PropertyUtils.getValue("encrypt.debug");
        String encryptKey = PropertyUtils.getValue("encrypt.key");
        if ("true".equals(debug)) {
            encodeJson = requestJson;
        } else {
            //加密
            encodeJson = AesEncryptUtils.aesEncrypt(requestJson, encryptKey);
        }
        //调用接口
        String delUrl = PropertyUtils.getValue("delUrl");
        String reposeJson = HttpRequestUtil.httpRequestByRaw(delUrl + "/deleteBhkInfoByRids", encodeJson);

        //解密
        if ("true".equals(debug)) {
            return reposeJson;
        } else {
            return AesEncryptUtils.aesDecrypt(reposeJson, encryptKey);
        }
    }

    /**
    * @Description : 症状选择
    * @MethodAuthor: anjing
    * @Date : 2020/4/23 16:14
    **/
    public void selectSymCodeAction(SymCodeCommPO currentSymCodeCommPO) {
        if(currentSymCodeCommPO.isIfSelected()) {
            this.tdTjSymptomCltBean.setExtends1(null);
            this.symIdList.add(currentSymCodeCommPO.getRid());
            if(StringUtils.isNotBlank(currentSymCodeCommPO.getExtendS1())) {
                if("1".equals(currentSymCodeCommPO.getExtendS1())) {
                    this.tdTjSymptomCltBean.setExtends1(currentSymCodeCommPO.getExtendS1());
                    this.tdTjSymptomCltBean.setShowOtherInfo(currentSymCodeCommPO.isIfSelected());

                    for (SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                        for (SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                            if (StringUtils.isNotBlank(symCodeCommPO.getExtendS1()) && "2".equals(symCodeCommPO.getExtendS1())) {
                                symCodeCommPO.setSelectAble(false);
                            }
                        }
                    }
                } else if("2".equals(currentSymCodeCommPO.getExtendS1())) {
                    for (SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                        for (SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                            if (StringUtils.isBlank(symCodeCommPO.getExtendS1()) || "1".equals(symCodeCommPO.getExtendS1())) {
                                symCodeCommPO.setSelectAble(false);
                            }
                        }
                    }
                }
            } else {
                for (SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                    for (SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                        symCodeCommPO.setSelectAble(true);
                        if (StringUtils.isNotBlank(symCodeCommPO.getExtendS1()) && "2".equals(symCodeCommPO.getExtendS1())) {
                            symCodeCommPO.setSelectAble(false);
                        }
                    }
                }
            }
        } else {
            this.symIdList.remove(currentSymCodeCommPO.getRid());
            if(StringUtils.isNotBlank(currentSymCodeCommPO.getExtendS1()) && "1".equals(currentSymCodeCommPO.getExtendS1())) {
                this.tdTjSymptomCltBean.setExtends1(null);
                this.tdTjSymptomCltBean.setShowOtherInfo(currentSymCodeCommPO.isIfSelected());
            }

            if(CollectionUtils.isEmpty(this.symIdList)) {
                for (SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                    for (SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                        symCodeCommPO.setSelectAble(true);
                    }
                }
            } else {
                TsSimpleCode tsSimpleCode = this.service.find(TsSimpleCode.class, this.symIdList.get(0));
                if(StringUtils.isBlank(tsSimpleCode.getExtendS1()) || "1".equals(tsSimpleCode.getExtendS1())) {
                    for (SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                        for (SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                            if (StringUtils.isNotBlank(symCodeCommPO.getExtendS1()) && "2".equals(symCodeCommPO.getExtendS1())) {
                                symCodeCommPO.setSelectAble(false);
                            }
                        }
                    }
                } else if("2".equals(currentSymCodeCommPO.getExtendS1())) {
                    for (SymCodeRowCommPO symCodeRowCommPO : this.tdTjSymptomCltBean.getSymCodeRowCommPOList()) {
                        for (SymCodeCommPO symCodeCommPO : symCodeRowCommPO.getSymCodeList()) {
                            if (StringUtils.isBlank(symCodeCommPO.getExtendS1()) || "1".equals(symCodeCommPO.getExtendS1())) {
                                symCodeCommPO.setSelectAble(false);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
    * @Description : 标准值配置状态-初始化
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 9:44
    **/
    private void initStadItemsStatus() {
        this.stadItemsStatus = 0;
        this.tjStadItemsStatus = this.service.findTbTjStadItemsStatusByOrgId(Global.getUser().getTsUnit().getRid().toString());
        if(null != this.tjStadItemsStatus) {
            if(null != this.tjStadItemsStatus.getState()) {
                this.stadItemsStatus = this.tjStadItemsStatus.getState();
            }
        }

    }

    /**
    * @Description : 打开删除弹出框
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 10:06
    **/
    public void openDelConfirmDialog() {
        if(!beforAddInit()) {
            return;
        }
        workTypeName = "";
        ifOtherWork = false;
        RequestContext.getCurrentInstance().execute("PF('DelConfirmDialog').show()");
    }

    /**
     * 获取所有未停用的项目
     *
     * @return Set key：itemRid
     */
    public Set<Integer> getAllItemsRidSet() {
        Set<Integer> set = new HashSet<>();
        List<TbTjItems> list = this.service.findAllTbTjItems();
        if (!CollectionUtils.isEmpty(list)) {
            for (TbTjItems item : list) {
                set.add(item.getRid());
            }
        }
        return set;
    }

    /**
    * @Description : 根据当前登录用户所在单位获取配置项目Map
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 15:32
    **/
    public Map<String, TbTjStadItems> getStadItemsMap(String sex) {
        Map<String, TbTjStadItems> map = new HashMap<>();
        List<TbTjStadItems> list = this.service.findTbTjStadItemsByOrgId(Global.getUser().getTsUnit().getRid().toString(),sex);
        if(!CollectionUtils.isEmpty(list)) {
            for(TbTjStadItems tbTjStadItems : list) {
                map.put(tbTjStadItems.getFkByItemId().getRid().toString(), tbTjStadItems);
            }
        }
        return map;
    }

    /**
     * 获取体检项目无在岗状态、有害因素的特殊标准最小值最大值
     *
     * @param sex 性别
     * @return 体检项目无在岗状态、有害因素的特殊标准最小值最大值
     */
    public Map<String, TbTjItemsSpe> getSpeItemsMapNoBadRsn(String sex) {
        Map<String, TbTjItemsSpe> speMap = new HashMap<>();
        List<TbTjItemsSpe> speItemListNoBadRsn = this.service.findSpeItemsNoBadRsn();
        for (TbTjItemsSpe itemsSpe : speItemListNoBadRsn) {
            if (!itemsSpe.getSex().equals(sex)) {
                continue;
            }
            if (speMap.containsKey(itemsSpe.getFkByItemId().getRid().toString())) {
                TbTjItemsSpe tjItemsSpe1 = speMap.get(itemsSpe.getFkByItemId().getRid().toString());
                //取最小值的最大值
                if (tjItemsSpe1.getMinval() == null || tjItemsSpe1.getMinval().compareTo(itemsSpe.getMinval()) < 0) {
                    tjItemsSpe1.setMinval(itemsSpe.getMinval());
                }
                //取最大值的最小值
                if (tjItemsSpe1.getMaxval() == null || tjItemsSpe1.getMaxval().compareTo(itemsSpe.getMaxval()) > 0) {
                    tjItemsSpe1.setMaxval(itemsSpe.getMaxval());
                }
            } else {
                speMap.put(itemsSpe.getFkByItemId().getRid().toString(), itemsSpe);
            }
        }
        return speMap;
    }

    /**
    * @Description : 根据当前登录用户所在单位设置最大值、最小值、计量单位
    * @MethodAuthor: anjing
    * @Date : 2020/4/24 15:07
    **/
    private void setBhkSubValueByOrg(TbTjItems tbTjItems, TdTjBhksubClt tdTjBhksubClt,
                                     Map<String, TbTjStadItems> map, Map<String, TbTjItemsSpe> speItemsMapNoBadRsn) {
        TbTjStadItems tbTjStadItems = map.get(tbTjItems.getRid().toString());
        if(null != tbTjStadItems) {
        	tdTjBhksubClt.setMinval(tbTjStadItems.getMinval()==null?null:tbTjStadItems.getMinval());
        	tdTjBhksubClt.setMaxval(tbTjStadItems.getMaxval()==null?null:tbTjStadItems.getMaxval());
        	// 设置参考值
        	tdTjBhksubClt.setItemStdvalue(((tbTjStadItems.getMinval()==null?null:tbTjStadItems.getMinval()) + "-" + (tbTjStadItems.getMaxval()==null?null:tbTjStadItems.getMaxval())));
        	TsSimpleCode msruntId = tbTjStadItems.getFkByMsruntId();
        	if(null!=msruntId && null!=msruntId.getRid()){
        		tdTjBhksubClt.setMsrunt(msruntId.getCodeName());
                tdTjBhksubClt.setFkByMsruntId(msruntId);
        	}
        	if (null!=tbTjStadItems.getJdgptn()) {
        		tdTjBhksubClt.setJdgptn(new Short(tbTjStadItems.getJdgptn().toString()));
        	}
        } else if (speItemsMapNoBadRsn.containsKey(tbTjItems.getRid().toString())) {
            TbTjItemsSpe tjItemsSpe = speItemsMapNoBadRsn.get(tbTjItems.getRid().toString());
            tdTjBhksubClt.setMinval(tjItemsSpe.getMinval());
            tdTjBhksubClt.setMaxval(tjItemsSpe.getMaxval());
            tdTjBhksubClt.setItemStdvalue(tdTjBhksubClt.getMinval() + "-" + tdTjBhksubClt.getMaxval());
            Integer msruntId = tjItemsSpe.getFkByItemId().getMsruntId();
            if (null != msruntId) {
                tdTjBhksubClt.setFkByMsruntId(new TsSimpleCode(msruntId));
            }
            String msruntVal = tjItemsSpe.getMsrunt();
            if (ObjectUtil.isNotEmpty(msruntVal)) {
                tdTjBhksubClt.setMsrunt(msruntVal);
            }
        }
    }

    /**
    * @Description : 在岗状态切换
    * @MethodAuthor: anjing
    * @Date : 2020/5/22 15:45
    **/
    public void onguadrStateChangeAction() {
        // “在岗状态”选择“上岗前”（码表5009扩展字段2，控制接害工龄是否不可输入，默认可以，0/空：可以，1：不可以），“接害工龄”灰掉不可输入
        this.tchbadrsntimAble = false;
        if(StringUtils.isNotBlank(this.editOnguadrStateId)) {
            TsSimpleCode tsSimpleCode = this.service.find(TsSimpleCode.class, Integer.valueOf(this.editOnguadrStateId));
            if(null != tsSimpleCode) {
                if(null != tsSimpleCode.getExtendS2() && tsSimpleCode.getExtendS2() == 1) {
                    this.tchbadrsntimAble = true;
                }
            }
        }
    }

    /**
     * @MethodName: onPsnTypeChangeAction
     * @Description: 人员类型变换
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-15
    **/
    public void onPsnTypeChangeAction() {
        // “88”是无人员类型
        this.ifIdcAble = true;
        this.ifIdc = false;
        if(StringUtils.isNotBlank(this.editPsnTypeId)) {
            TsSimpleCode tsSimpleCode = this.service.find(TsSimpleCode.class, Integer.valueOf(this.editPsnTypeId));
            if(null != tsSimpleCode) {
                if("88".equals(tsSimpleCode.getCodeNo())) {
                    this.ifIdcAble = false;
                }else if("01".equals(tsSimpleCode.getCodeNo())){
                    this.ifIdc = true;
                    String checkIDC = IdcUtils.checkIDC(this.tdTjBhkClt.getIdc());
                    if (StringUtils.isNotBlank(checkIDC)) {
                        JsfUtil.addErrorMessage("居民身份证号码格式不正确！");
                    }else{
                        this.findFlowByIdc();
                    }
                }
            }
        }
    }


    /**
     * @MethodName: selectWorkTypeAction
     * @Description: 选择工种
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-08
    **/
    public void selectWorkTypeAction(){
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("width", 680);
        options.put("contentWidth", 625);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add("工种");
        paramMap.put("titleName", paramList);
        paramList = new ArrayList<String>();
        paramList.add(new Integer("2").equals(this.tdTjBhkClt.getJcType())?"5595":"5502");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifShowCode", paramList);

        paramList = new ArrayList<String>();
        paramList.add("true");
        paramMap.put("ifAllSelect", paramList);
        paramList = new ArrayList<String>();
        paramList.add("false");
        paramMap.put("ifShowFirstCode", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeRadioSelectListNew", options, paramMap);
    }

    /**
     * @MethodName: onWorkTypeSearch
     * @Description:
     * @Param: [event]
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-08
    **/
    public void onWorkTypeSearch(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            TsSimpleCode code = selectedMap.get("selectPro") == null ? null
                    : (TsSimpleCode) selectedMap.get("selectPro");
            code = systemModuleService.findSimpleCodeByRid(code.getRid());
            workTypeName = code.getCodeName();
            tdTjBhkClt.setFkByWorkTypeId(code);
            if("1".equals(code.getExtendS1())){
                ifOtherWork = true;
            }else{
                ifOtherWork = false;
            }
        }
    }

    /**
     * @MethodName: clearWorkType
     * @Description: 清除工种
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-08
    **/
    public void clearWorkType(){
        workTypeName = null;
        tdTjBhkClt.setFkByWorkTypeId(null);
        ifOtherWork = false;
        tdTjBhkClt.setWorkOther(null);
    }

    public void selItemCodeAction() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentWidth", 650);

        String chooseBads ="";
        if(null != tdTjBhkClt.getBadList() && tdTjBhkClt.getBadList().size()>0){
            for(TsSimpleCode code:tdTjBhkClt.getBadList()){
                chooseBads +=code.getRid()+",";
            }
            chooseBads = chooseBads.substring(0,chooseBads.length()-1);
        }

        String selectBadIds = "";
        if(resultType ==1){
            if(null != reCheckBads && reCheckBads.size()>0){
                for(TsSimpleCode code:reCheckBads){
                    selectBadIds +=code.getRid()+",";
                }
                selectBadIds = selectBadIds.substring(0,selectBadIds.length()-1);
            }
        }else if(resultType == 0){
            if(null != ncagBads && ncagBads.size() > 0){
                for(TsSimpleCode code:ncagBads){
                    selectBadIds +=code.getRid()+",";
                }
                selectBadIds = selectBadIds.substring(0,selectBadIds.length()-1);
            }
        }else {
            if(null != otherBads && otherBads.size()>0){
                for(TsSimpleCode code:otherBads){
                    selectBadIds +=code.getRid()+",";
                }
                selectBadIds = selectBadIds.substring(0,selectBadIds.length()-1);
            }
        }

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(chooseBads);
        paramMap.put("allBadIds", paramList);
        paramList = new ArrayList<String>();
        paramList.add(selectBadIds);
        paramMap.put("selectIds", paramList);
        paramList = new ArrayList<String>();
        paramList.add("5007");
        paramMap.put("typeNo", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/tjxxlr/badCodeMulitySelectList", options, paramMap);
    }

    /**
     * @MethodName: onItemCodeAction
     * @Description: 危害因素返回
     * @Param: [event]
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-09
    **/
    public void onItemCodeAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event
                .getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if (null != list && list.size() > 0) {
                if(resultType ==1){
                    searchReCheckBadName ="";
                    for(TsSimpleCode a:list){
                        searchReCheckBadName +=a.getCodeName()+",";
                    }
                    searchReCheckBadName = searchReCheckBadName.substring(0,searchReCheckBadName.length()-1);
                    reCheckBads = new ArrayList<>();
                    reCheckBads.addAll(list);
                }else if(resultType == 0){
                    searchNcagCheckBadName = "";
                    for(TsSimpleCode a:list){
                        searchNcagCheckBadName +=a.getCodeName()+",";
                    }
                    searchNcagCheckBadName = searchNcagCheckBadName.substring(0,searchNcagCheckBadName.length()-1);
                    ncagBads = new ArrayList<>();
                    ncagBads.addAll(list);
                }else{
                    searchOtherBadName ="";
                    for(TsSimpleCode a:list){
                        searchOtherBadName +=a.getCodeName()+",";
                    }
                    searchOtherBadName = searchOtherBadName.substring(0,searchOtherBadName.length()-1);
                    otherBads = new ArrayList<>();
                    otherBads.addAll(list);
                }
            }
        }
    }

    /**
     * @MethodName: clearItemCode
     * @Description: 清除复查或者其他的危害因素
     * @Param: []
     * @Return: void
     * @Author: maox
     * @Date: 2020-07-10
    **/
    public void clearItemCode(){
        if(1 ==resultType){
            searchReCheckBadName ="";
            reCheckBads = new ArrayList<>();
        }else if(0 == resultType){
            searchNcagCheckBadName = "";
            ncagBads = new ArrayList<>();
        }else{
            searchOtherBadName = "";
            otherBads = new ArrayList<>();
        }
    }

    /**
    * @Description : 打开职业禁忌证弹出框
    * @MethodAuthor: anjing
    * @Date : 2021/3/24 16:10
    **/
    public void selContraindAction() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentWidth", 650);

        TdTjContraindCltWeb contraindCltWeb = this.contraindCltWebList.get(this.contraindIndex);
        Integer badRsnId = null;
        if(null != contraindCltWeb) {
            badRsnId = contraindCltWeb.getBadRsnId();
        }
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        if(badRsnId != null && tdTjBhkClt.getFkByOnguardStateid() != null) {
            String showIds = this.service.findContraindCltIds(badRsnId, tdTjBhkClt.getFkByOnguardStateid().getRid());
            if(StringUtils.isNotBlank(showIds)) {
                paramList = new ArrayList<String>();
                paramList.add(showIds);
                paramMap.put("showIds", paramList);
            }
        }
        if(!CollectionUtils.isEmpty(contraindCltWeb.getContraindIdList())) {
            String selectIds = StringUtils.list2string(contraindCltWeb.getContraindIdList(), ",");
            paramList = new ArrayList<String>();
            paramList.add(selectIds);
            paramMap.put("selectIds", paramList);
        }
        paramList = new ArrayList<String>();
        paramList.add("5011");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("职业禁忌证");
        paramMap.put("titleName", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }

    /**
     * <p>描述 打开职业禁忌证弹出框 （新需求-按危害因素选）</p>
     *
     * @MethodAuthor gongzhe,2022/4/28 11:39,selContraindNew1Action
     * @return void
     */
    public void selContraindNew1Action() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentWidth", 650);

        TdTjBadrsnsClt contraindClt = this.tdTjBadrsnsCltList.get(this.contraindIndex);
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        if(contraindClt.getFkByBadrsnId()!=null && tdTjBhkClt.getFkByOnguardStateid() != null) {
            String showIds = this.service.findContraindCltIds(contraindClt.getFkByBadrsnId().getRid(), tdTjBhkClt.getFkByOnguardStateid().getRid());
            if(StringUtils.isNotBlank(showIds)) {
                paramList = new ArrayList<String>();
                paramList.add(showIds);
                paramMap.put("showIds", paramList);
            }
        }
        if(!CollectionUtils.isEmpty(contraindClt.getContraindCltWeb().getContraindIdList())) {
            String selectIds = StringUtils.list2string(contraindClt.getContraindCltWeb().getContraindIdList(), ",");
            paramList = new ArrayList<String>();
            paramList.add(selectIds);
            paramMap.put("selectIds", paramList);
        }
        paramList = new ArrayList<String>();
        paramList.add("5011");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("职业禁忌证");
        paramMap.put("titleName", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }

    /**
    * @Description : 职业禁忌证-清空操作
    * @MethodAuthor: anjing
    * @Date : 2021/3/24 17:12
    **/
    public void clearContraindAction() {
        TdTjContraindCltWeb contraindCltWeb = this.contraindCltWebList.get(this.contraindIndex);
        if(null != contraindCltWeb) {
            contraindCltWeb.setContraindIdList(new ArrayList<Integer>());
            contraindCltWeb.setContraind("");
        }
    }
    /**
     * <p>描述 职业禁忌证-清空操作（新需求-按危害因素选）</p>
     *
     * @MethodAuthor gongzhe,2022/4/28 11:39,clearContraindNew1Action
     * @return void
     */
    public void clearContraindNew1Action() {
        TdTjContraindCltWeb contraindCltWeb = this.tdTjBadrsnsCltList.get(this.contraindIndex).getContraindCltWeb();
        if(null != contraindCltWeb) {
            contraindCltWeb.setContraindIdList(new ArrayList<Integer>());
            contraindCltWeb.setContraind("");
        }
    }

    /**
    * @Description : 确定选择职业禁忌证
    * @MethodAuthor: anjing
    * @Date : 2021/3/24 14:18
    **/
    public void onSelContraindAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if(!CollectionUtils.isEmpty(list)) {
                TdTjContraindCltWeb contraindCltWeb = this.contraindCltWebList.get(this.contraindIndex);
                if(null != contraindCltWeb) {
                    contraindCltWeb.setContraindIdList(new ArrayList<Integer>());
                    StringBuilder name = new StringBuilder();
                    for(TsSimpleCode tsSimpleCode : list) {
                        contraindCltWeb.getContraindIdList().add(tsSimpleCode.getRid());
                        name.append("，").append(tsSimpleCode.getCodeName());
                    }
                    if(StringUtils.isNotBlank(name)) {
                        contraindCltWeb.setContraind(name.deleteCharAt(0).toString());
                    }
                }
            }
        }
    }
    /**
     * <p>描述 确定选择职业禁忌证 （新需求-按危害因素选）</p>
     *
     * @param event
     * @MethodAuthor gongzhe,2022/4/28 11:48,onSelContraindNew1Action
     * @return void
     */
    public void onSelContraindNew1Action(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if(!CollectionUtils.isEmpty(list)) {
                TdTjContraindCltWeb contraindCltWeb = this.tdTjBadrsnsCltList.get(this.contraindIndex).getContraindCltWeb();
                if(null != contraindCltWeb) {
                    contraindCltWeb.getContraindIdList().clear();
                    StringBuilder name = new StringBuilder();
                    for(TsSimpleCode tsSimpleCode : list) {
                        contraindCltWeb.getContraindIdList().add(tsSimpleCode.getRid());
                        name.append("，").append(tsSimpleCode.getCodeName());
                    }
                    if(StringUtils.isNotBlank(name)) {
                        contraindCltWeb.setContraind(name.deleteCharAt(0).toString());
                    }
                }
            }
        }
    }

    /**
     * @Description : 打开疑似职业病弹出框
     * @MethodAuthor: anjing
     * @Date : 2021/3/24 16:10
     **/
    public void selSupoccdiseAction() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentWidth", 650);

        TdTjSupoccdiseCltWeb supoccdiseCltWeb = this.supoccdiseCltWebList.get(this.contraindIndex);
        Integer badRsnId = null;
        if(null != supoccdiseCltWeb) {
            badRsnId = supoccdiseCltWeb.getBadRsnId();
        }
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        if(badRsnId != null && tdTjBhkClt.getFkByOnguardStateid() != null) {
            String showIds = this.service.findOccdiseCltIds(badRsnId, tdTjBhkClt.getFkByOnguardStateid().getRid());
            if(StringUtils.isNotBlank(showIds)) {
                paramList = new ArrayList<String>();
                paramList.add(showIds);
                paramMap.put("showIds", paramList);
            }
        }
        if(!CollectionUtils.isEmpty(supoccdiseCltWeb.getSupoccdiseIdList())) {
            String selectIds = StringUtils.list2string(supoccdiseCltWeb.getSupoccdiseIdList(), ",");
            paramList = new ArrayList<String>();
            paramList.add(selectIds);
            paramMap.put("selectIds", paramList);
        }
        paramList = new ArrayList<String>();
        paramList.add("5010");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("疑似职业病");
        paramMap.put("titleName", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }
    /**
     * <p>描述 疑似职业病弹出框（新需求-按危害因素选）</p>
     *
     * @MethodAuthor gongzhe,2022/4/28 11:36,selSupoccdiseNew1Action
     * @return void
     */
    public void selSupoccdiseNew1Action() {
        Map<String, Object> options = new HashMap<String, Object>();
        options.put("modal", true);
        options.put("draggable", true);
        options.put("resizable", false);
        options.put("contentWidth", 650);

        TdTjBadrsnsClt supoccdiseClt = this.tdTjBadrsnsCltList.get(this.contraindIndex);
        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        if(supoccdiseClt.getFkByBadrsnId() != null && tdTjBhkClt.getFkByOnguardStateid() != null) {
            String showIds = this.service.findOccdiseCltIds(supoccdiseClt.getFkByBadrsnId().getRid(), tdTjBhkClt.getFkByOnguardStateid().getRid());
            if(StringUtils.isNotBlank(showIds)) {
                paramList = new ArrayList<String>();
                paramList.add(showIds);
                paramMap.put("showIds", paramList);
            }
        }
        if(!CollectionUtils.isEmpty(supoccdiseClt.getSupoccdiseCltWeb().getSupoccdiseIdList())) {
            String selectIds = StringUtils.list2string(supoccdiseClt.getSupoccdiseCltWeb().getSupoccdiseIdList(), ",");
            paramList = new ArrayList<String>();
            paramList.add(selectIds);
            paramMap.put("selectIds", paramList);
        }
        paramList = new ArrayList<String>();
        paramList.add("5010");
        paramMap.put("typeNo", paramList);
        paramList = new ArrayList<String>();
        paramList.add("疑似职业病");
        paramMap.put("titleName", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
    }

    /**
     * @Description : 疑似职业病-清空操作
     * @MethodAuthor: anjing
     * @Date : 2021/3/24 17:12
     **/
    public void clearSupoccdiseAction() {
        TdTjSupoccdiseCltWeb supoccdiseCltWeb = this.supoccdiseCltWebList.get(this.contraindIndex);
        if(null != supoccdiseCltWeb) {
            supoccdiseCltWeb.setSupoccdiseIdList(new ArrayList<Integer>());
            supoccdiseCltWeb.setSupoccdise("");
        }
    }
    /**
     * <p>描述 疑似职业病-清空操作（新需求-按危害因素选）</p>
     *
     * @MethodAuthor gongzhe,2022/4/28 11:51,clearSupoccdiseNew1Action
     * @return void
     */
    public void clearSupoccdiseNew1Action() {
        TdTjSupoccdiseCltWeb supoccdiseCltWeb = this.tdTjBadrsnsCltList.get(this.contraindIndex).getSupoccdiseCltWeb();
        if(null != supoccdiseCltWeb) {
            supoccdiseCltWeb.setSupoccdiseIdList(new ArrayList<Integer>());
            supoccdiseCltWeb.setSupoccdise("");
        }
    }

    /**
    * @Description : 确定选择疑似职业病
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 11:17
    **/
    public void onSelSupoccdiseAction(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if(!CollectionUtils.isEmpty(list)) {
                TdTjSupoccdiseCltWeb supoccdiseCltWeb = this.supoccdiseCltWebList.get(this.contraindIndex);
                if(null != supoccdiseCltWeb) {
                    supoccdiseCltWeb.setSupoccdiseIdList(new ArrayList<Integer>());
                    StringBuilder name = new StringBuilder();
                    for(TsSimpleCode tsSimpleCode : list) {
                        supoccdiseCltWeb.getSupoccdiseIdList().add(tsSimpleCode.getRid());
                        name.append("，").append(tsSimpleCode.getCodeName());
                    }
                    if(StringUtils.isNotBlank(name)) {
                        supoccdiseCltWeb.setSupoccdise(name.deleteCharAt(0).toString());
                    }
                }
            }
        }
    }
    /**
     * <p>描述 确定选择疑似职业病（新需求-按危害因素选）</p>
     *
     * @param event
     * @MethodAuthor gongzhe,2022/4/28 11:51,onSelSupoccdiseNew1Action
     * @return void
     */
    public void onSelSupoccdiseNew1Action(SelectEvent event) {
        Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
        if (null != selectedMap && selectedMap.size() > 0) {
            List<TsSimpleCode> list = (List<TsSimpleCode>) selectedMap.get("selectPros");
            if(!CollectionUtils.isEmpty(list)) {
                TdTjSupoccdiseCltWeb supoccdiseCltWeb = this.tdTjBadrsnsCltList.get(this.contraindIndex).getSupoccdiseCltWeb();
                if(null != supoccdiseCltWeb) {
                    supoccdiseCltWeb.getSupoccdiseIdList().clear();
                    StringBuilder name = new StringBuilder();
                    for(TsSimpleCode tsSimpleCode : list) {
                        supoccdiseCltWeb.getSupoccdiseIdList().add(tsSimpleCode.getRid());
                        name.append("，").append(tsSimpleCode.getCodeName());
                    }
                    if(StringUtils.isNotBlank(name)) {
                        supoccdiseCltWeb.setSupoccdise(name.deleteCharAt(0).toString());
                    }
                }
            }
        }
    }

    /**
     * <p>描述 检查结果-危害因素-体检结论变更事件监听</p>
     *
     * @param r
     * @MethodAuthor gongzhe,2022/4/28 17:26,onExamConclusionChange
     * @return void
     */
    public void onExamConclusionChange(Integer r){
        TdTjBadrsnsClt contraindClt = this.tdTjBadrsnsCltList.get(r);
        if(contraindClt.getExamConclusionId()!=null){
            for(TsSimpleCode c:mhkrstList){
                if(c.getRid().equals(contraindClt.getExamConclusionId())){
                    contraindClt.setFkByExamConclusionId(c);
                    break;
                }
            }
        }else{
            //切换到了请选择
            contraindClt.setFkByExamConclusionId(null);
        }
        if(contraindClt.getFkByExamConclusionId()!=null){
            if(contraindClt.getFkByExamConclusionId().getExtendS2()==4 && tdTjBhkClt.getFkByOnguardStateid() != null){
                List<TsSimpleCode> contraindClts = service.findContraindClts(contraindClt.getFkByBadrsnId().getRid(),tdTjBhkClt.getFkByOnguardStateid().getRid());
                if(CollectionUtils.isEmpty(contraindClts)) {
                    contraindClts = this.commService.findSimpleCodesByTypeId("5011");
                }
                contraindClt.getContraindCltWeb().setContraindCltList(contraindClts);
            }
            if(contraindClt.getFkByExamConclusionId().getExtendS2()==5 && tdTjBhkClt.getFkByOnguardStateid() != null){
                List<TsSimpleCode> occdiseClts = service.findOccdiseClts(contraindClt.getFkByBadrsnId().getRid(),tdTjBhkClt.getFkByOnguardStateid().getRid());
                if(CollectionUtils.isEmpty(occdiseClts)) {
                    occdiseClts = this.commService.findSimpleCodesByTypeId("5010");
                }
                contraindClt.getSupoccdiseCltWeb().setSupoccdiseCltList(occdiseClts);
            }
        }

    }

    /**
    * @Description : 主检医师初始化
    * @MethodAuthor: anjing
    * @Date : 2021/3/25 15:18
    **/
    private void initTdTjChiefDoctorBean() {
        this.tdTjChiefDoctorBean = new TdTjChiefDoctorBean();
        this.tdTjChiefDoctorBean.setFkByBhkId(this.tdTjBhkClt);
        if(!CollectionUtils.isEmpty(this.tdTjBhkClt.getTdTjChiefDoctorList())) {
            for(TdTjChiefDoctor tdTjChiefDoctor : this.tdTjBhkClt.getTdTjChiefDoctorList()) {
                this.tdTjChiefDoctorBean.getChiefDoctorList().add(tdTjChiefDoctor.getFkByMhkdctId());
            }
        }
        this.tdTjChiefDoctorBean.initParam();
    }

    /**
    * @Description : 电测听项目修正规则初始化
    * @MethodAuthor: anjing
    * @Date : 2021/4/10 14:04
    **/
    private void initTbTjAudioItemRuleList() {
        this.audioItemRuleMap = new HashMap<>();
        List<TbTjAudioItemRule> ruleList = this.service.findTbTjAudioItemRuleList();
        if(!CollectionUtils.isEmpty(ruleList)) {
            for(TbTjAudioItemRule rule : ruleList) {
                String sex = rule.getSex().intValue()==1?"男":"女";
                String key = sex + "&&" + rule.getItemType();
                this.audioItemRuleMap.put(key, rule.getTbTjAdoItmRuleSubList());
            }
        }
    }

    /**
    * @Description : 根据电测听原始值就算修正值，合格标记
    * @MethodAuthor: anjing
    * @Date : 2021/4/10 9:47
    **/
    public void calDctItemRstAndRgltag(TdTjBhksubClt dta,Integer index,Integer rowIndex) {
        // 项目标记为3-20且标准值配置状态表电测听录入模式为1：原始值且项目未缺项
        if(null != this.tjStadItemsStatus.getAudioModel()  && this.tjStadItemsStatus.getAudioModel() == 1) {
            if(null != dta.getFkByItemId() && null != dta.getFkByItemId().getItemTag()) {
                int itemTag = dta.getFkByItemId().getItemTag();
                if(itemTag >= 3 && itemTag <= 20) {
                    if(null != dta.getIfLack() && 0 == dta.getIfLack().intValue()) {
                        // 计算修正值
                        if(StringUtils.isNotBlank(dta.getItemRstOri()) && null != dta.getFkByBhkId() && StringUtils.isNotBlank(dta.getFkByBhkId().getSex())
                                && null != dta.getFkByItemId() && null != dta.getFkByItemId().getItemTag()) {
                            // 获取比较规则
                            BigDecimal age = new BigDecimal(dta.getFkByBhkId().getAge());
                            BigDecimal itemRstOri = new BigDecimal(dta.getItemRstOri());
                            BigDecimal itemRst = null;
                            String key = dta.getFkByBhkId().getSex() + "&&" + (dta.getFkByItemId().getItemTag().intValue()-8);
                            if(dta.getFkByItemId().getItemTag().intValue() - 8 <= 0) {
                                key = dta.getFkByBhkId().getSex() + "&&" + (dta.getFkByItemId().getItemTag().intValue()-2);
                            }
                            if(!CollectionUtils.isEmpty(this.audioItemRuleMap)) {
                                List<TbTjAdoItmRuleSub> ruleSubList = this.audioItemRuleMap.get(key);
                                // 进行比较获取修正值
                                if(!CollectionUtils.isEmpty(ruleSubList)) {
                                    for(TbTjAdoItmRuleSub ruleSub : ruleSubList) {
                                        if(null != ruleSub.getAgeGe()) {
                                            if(age.compareTo(new BigDecimal(ruleSub.getAgeGe())) >= 0) {
                                                if(null != ruleSub.getAgeLe()) {
                                                    if(age.compareTo(new BigDecimal(ruleSub.getAgeLe())) <= 0) {
                                                        itemRst = itemRstOri.subtract(new BigDecimal(ruleSub.getMedVal()));
                                                    }
                                                }else if(null != ruleSub.getAgeLt()) {
                                                    if(age.compareTo(new BigDecimal(ruleSub.getAgeLt())) < 0) {
                                                        itemRst = itemRstOri.subtract(new BigDecimal(ruleSub.getMedVal()));
                                                    }
                                                }else if(null == ruleSub.getAgeLe() && null == ruleSub.getAgeLt()) {
                                                    itemRst = itemRstOri.subtract(new BigDecimal(ruleSub.getMedVal()));
                                                }
                                            }
                                        }else if(null != ruleSub.getAgeGt()) {
                                            if(age.compareTo(new BigDecimal(ruleSub.getAgeGt())) >= 0) {
                                                if(null != ruleSub.getAgeLe()) {
                                                    if(age.compareTo(new BigDecimal(ruleSub.getAgeLe())) <= 0) {
                                                        itemRst = itemRstOri.subtract(new BigDecimal(ruleSub.getMedVal()));
                                                    }
                                                }else if(null != ruleSub.getAgeLt()) {
                                                    if(age.compareTo(new BigDecimal(ruleSub.getAgeLt())) < 0) {
                                                        itemRst = itemRstOri.subtract(new BigDecimal(ruleSub.getMedVal()));
                                                    }
                                                }else if(null == ruleSub.getAgeLe() && null == ruleSub.getAgeLt()) {
                                                    itemRst = itemRstOri.subtract(new BigDecimal(ruleSub.getMedVal()));
                                                }
                                            }
                                        }else if(null != ruleSub.getAgeLe()) {
                                            if(age.compareTo(new BigDecimal(ruleSub.getAgeLe())) <= 0) {
                                                itemRst = itemRstOri.subtract(new BigDecimal(ruleSub.getMedVal()));
                                            }
                                        }else if(null != ruleSub.getAgeLt()) {
                                            if(age.compareTo(new BigDecimal(ruleSub.getAgeLt())) < 0) {
                                                itemRst = itemRstOri.subtract(new BigDecimal(ruleSub.getMedVal()));
                                            }
                                        }
                                        if(null != itemRst) {
                                            break;
                                        }
                                    }
                                }
                            }
                            if(null != itemRst) {
                                dta.setItemRst(itemRst.toString());
                            }
                        }
                    }
                }
            }
        }
        this.calRglTagAndRstDescNew(dta,index,rowIndex,true);
    }


    //双耳高频等项目自动计算
    public void autoCalcItemRst(){
        try{
            int topIndex = -1;
            for(TbTjItemSort itemSort : mustItemSortList){
                topIndex++;
                List<TdTjBhksubClt> subCltList = itemSort.getBhksubCltList();
                if(!CollectionUtils.isEmpty(subCltList)){
                    if(null == subCltList.get(0).getFkByItemId() || null == subCltList.get(0).getFkByItemId().getItemTag() || subCltList.get(0).getFkByItemId().getItemTag() < 3
                            || subCltList.get(0).getFkByItemId().getItemTag() > 20){
                        continue;
                    }
                    BigDecimal l500 = null;
                    BigDecimal l1000 = null;
                    BigDecimal l2000 = null;
                    BigDecimal l3000 = null;
                    BigDecimal l4000 = null;
                    BigDecimal l6000 = null;

                    BigDecimal r500 = null;
                    BigDecimal r1000 = null;
                    BigDecimal r2000 = null;
                    BigDecimal r3000 = null;
                    BigDecimal r4000 = null;
                    BigDecimal r6000 = null;

                    BigDecimal lMtmv = null;//左耳听阈加权
                    BigDecimal rMtmv = null;//右耳听阈加权

                    BigDecimal lFta = null;//左耳语频平均听阈
                    BigDecimal rFta = null;//右耳语频平均听阈

                    //双耳语频平均听阈
                    BigDecimal bFta = null;

                    //双耳高频平均听阈
                    BigDecimal bHfta = null;
                    for(TdTjBhksubClt subClt : subCltList){
                        if(null == subClt.getFkByItemId() || null == subClt.getFkByItemId().getItemTag()){
                            continue;
                        }
                        Integer itemTag = subClt.getFkByItemId().getItemTag();
                        if(itemTag.intValue()<3 || itemTag.intValue() > 14){
                            continue;
                        }
                        BigDecimal tmpRst = convertStr2BigDecimal(subClt.getItemRst());
                        switch(itemTag.intValue()){
                            case 3:
                                l500 = tmpRst;
                                break;
                            case 4:
                                l1000 = tmpRst;
                                break;
                            case 5:
                                l2000 = tmpRst;
                                break;
                            case 6:
                                l3000 = tmpRst;
                                break;
                            case 7:
                                l4000 = tmpRst;
                                break;
                            case 8:
                                l6000 = tmpRst;
                                break;
                            case 9:
                                r500 = tmpRst;
                                break;
                            case 10:
                                r1000 = tmpRst;
                                break;
                            case 11:
                                r2000 = tmpRst;
                                break;
                            case 12:
                                r3000 = tmpRst;
                                break;
                            case 13:
                                r4000 = tmpRst;
                                break;
                            case 14:
                                r6000 = tmpRst;
                                break;
                        }
                        //计算左右耳听阈加权
                        if(null != l500 && null != l1000 && null != l2000 && null != l4000){
                            //(l500+l1000+l2000)/3*0.9 + l4000*0.1
                            BigDecimal tmp = BigDecimal.ZERO;
                            tmp = tmp.add(l500).add(l1000).add(l2000);
                            tmp = tmp.multiply(new BigDecimal("0.3"));
                            lMtmv = tmp.add(l4000.multiply(new BigDecimal("0.1"))).setScale(0, RoundingMode.HALF_UP);
                        }

                        if(null != r500 && null != r1000 && null != r2000 && null != r4000){
                            BigDecimal tmp = BigDecimal.ZERO;
                            tmp = tmp.add(r500).add(r1000).add(r2000);
                            tmp = tmp.multiply(new BigDecimal("0.3"));
                            rMtmv = tmp.add(r4000.multiply(new BigDecimal("0.1"))).setScale(0, RoundingMode.HALF_UP);
                        }

                        //计算左右耳平均听阈
                        if(null != l500 && null != l1000 && null != l2000){
                            //(l500+l1000+l2000)/3
                            BigDecimal tmp = BigDecimal.ZERO;
                            tmp = tmp.add(l500).add(l1000).add(l2000);
                            lFta = tmp.divide(new BigDecimal("3"), 0, RoundingMode.HALF_UP);
                        }
                        if(null != r500 && null != r1000 && null != r2000){
                            BigDecimal tmp = BigDecimal.ZERO;
                            tmp = tmp.add(r500).add(r1000).add(r2000);
                            rFta = tmp.divide(new BigDecimal("3"), 0, RoundingMode.HALF_UP);
                        }

                        //计算双耳语频平均听阈
                        if(null != l500 && null != l1000 && null != l2000 && null != r500 && null != r1000 && null != r2000){
                            BigDecimal tmp = BigDecimal.ZERO;
                            tmp = tmp.add(l500).add(l1000).add(l2000).add(r500).add(r1000).add(r2000);
                            bFta = tmp.divide(new BigDecimal("6"), 0, RoundingMode.HALF_UP);
                        }

                        //计算双耳高频平均听阈
                        if(null != l3000 && null != l4000 && null != l6000 && null != r3000 && null != r4000 && null != r6000){
                            BigDecimal tmp = BigDecimal.ZERO;
                            tmp = tmp.add(l3000).add(l4000).add(l6000).add(r3000).add(r4000).add(r6000);
                            bHfta = tmp.divide(new BigDecimal("6"), 0, RoundingMode.HALF_UP);
                        }
                    }

                    //赋值 15：右耳听阈加权；16：左耳听阈加权；17：左耳语频平均听阈；18：右耳语频平均听阈；
                    // 19：双耳语频平均听阈；20：双耳高频平均听阈，（结果按四舍五入修约至整数）
                    int subIndex = -1;
                    for(TdTjBhksubClt subClt : subCltList){
                        subIndex++;
                        if(null == subClt.getFkByItemId() || null == subClt.getFkByItemId().getItemTag()){
                            continue;
                        }
                        Integer itemTag = subClt.getFkByItemId().getItemTag();
                        if(itemTag.intValue()<15 || itemTag.intValue() > 20){
                            continue;
                        }
                        switch(itemTag.intValue()){
                            case 15:
                                subClt.setItemRst(null == rMtmv ? null : String.valueOf(rMtmv.intValue()));
                                break;
                            case 16:
                                subClt.setItemRst(null == lMtmv ? null : String.valueOf(lMtmv.intValue()));
                                break;
                            case 17:
                                subClt.setItemRst(null == lFta ? null : String.valueOf(lFta.intValue()));
                                break;
                            case 18:
                                subClt.setItemRst(null == rFta ? null : String.valueOf(rFta.intValue()));
                                break;
                            case 19:
                                subClt.setItemRst(null == bFta ? null : String.valueOf(bFta.intValue()));
                                break;
                            case 20:
                                subClt.setItemRst(null == bHfta ? null : String.valueOf(bHfta.intValue()));
                                break;
                        }
                        String itemRstTmp = subClt.getItemRst();
                        if(StringUtils.isNotBlank(itemRstTmp)){
                            calRglTagAndRstDescNew(subClt,topIndex,subIndex,true);
                        }
                        //单行更新列
                        RequestContext.getCurrentInstance().update("tabView:editTabView:bhkSubListForm:bhksubCltList"+topIndex+":"+subIndex+":rstdesc");
                        RequestContext.getCurrentInstance().update("tabView:editTabView:bhkSubListForm:bhksubCltList"+topIndex+":"+subIndex+":rgltag");
                    }


                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
    }

    //字符串转BigDecimal 无法转换返回null
    private BigDecimal convertStr2BigDecimal(String sourceStr){
        try{
            return new BigDecimal(sourceStr);
        }catch(Exception e){
            return null;
        }
    }

    /**
     * <p>方法描述：同体检危害因素 链接事件</p>
     * @MethodAuthor： yzz
     * @Date：2022-07-21
     **/
    public void copyBadrsnsToTouch() throws Exception {
        if(tdTjBhkClt==null){
            return;
        }
        if(tdTjBhkClt.getTdTjBadrsnsClts()==null||CollectionUtils.isEmpty(tdTjBhkClt.getTdTjBadrsnsClts())){
            tdTjBhkClt.getTdTjTchBadrsnsClts().clear();
            tdTjBhkClt.setTouchBadrsns(null);
        }else{
            if(!CollectionUtils.isEmpty(tdTjBhkClt.getTdTjBadrsnsClts())){
                tdTjBhkClt.getTdTjTchBadrsnsClts().clear();
                List<TdTjTchBadrsnsClt> tchBadrsnsCltList=new ArrayList<>();
                StringBuffer tchBadrsns=new StringBuffer();
                for(TdTjBadrsnsClt badrsnsClt:tdTjBhkClt.getTdTjBadrsnsClts()){
                    TdTjTchBadrsnsClt tchBadrsnsClt=new TdTjTchBadrsnsClt();
                    ObjectCopyUtil.copyPropertiesInclude(badrsnsClt,tchBadrsnsClt,new String[] {"fkByBhkId", "fkByBadrsnId"});
                    tchBadrsnsClt.setCreateDate(new Date());
                    tchBadrsnsClt.setCreateManid(Global.getUser().getRid());
                    tchBadrsnsCltList.add(tchBadrsnsClt);
                    tchBadrsns.append("，").append(badrsnsClt.getFkByBadrsnId().getCodeName());
                }
                if(!CollectionUtils.isEmpty(tchBadrsnsCltList)){
                    tdTjBhkClt.getTdTjTchBadrsnsClts().addAll(tchBadrsnsCltList);
                    tdTjBhkClt.setTouchBadrsns(tchBadrsns.substring(1));
                    tdTjBhkClt.setTchOtherBadrsn(tdTjBhkClt.getOtherBadRsn());
                    this.ifOtherTouchBadRsn=ifOtherBadRsn;
                }
            }
        }
    }

    public void changeChkdoc() {
        RequestContext.getCurrentInstance().update("tabView:editTabView:exmsdataListForm:editChkdocId_panel");
    }

    /**
    * <p>Description：清空防护用品佩戴情况 </p>
    * <p>Author： yzz 2024-08-20 </p>
    */
    public void clearProtectEqu() {
        this.tdTjBhkClt.setFkByProtectEquId(new TsSimpleCode());
        clearWorkType();
    }


    public Integer getRid() {
        return rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchInstitutionCode() {
        return searchInstitutionCode;
    }

    public void setSearchInstitutionCode(String searchInstitutionCode) {
        this.searchInstitutionCode = searchInstitutionCode;
    }

    public String getSearchPersonName() {
        return searchPersonName;
    }

    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public Date getSearchBhkBdate() {
        return searchBhkBdate;
    }

    public void setSearchBhkBdate(Date searchBhkBdate) {
        this.searchBhkBdate = searchBhkBdate;
    }

    public Date getSearchBhkEdate() {
        return searchBhkEdate;
    }

    public void setSearchBhkEdate(Date searchBhkEdate) {
        this.searchBhkEdate = searchBhkEdate;
    }

    public String getSearchBhkCode() {
        return searchBhkCode;
    }

    public void setSearchBhkCode(String searchBhkCode) {
        this.searchBhkCode = searchBhkCode;
    }

    public List<Integer> getRhkStateList() {
        return rhkStateList;
    }

    public void setRhkStateList(List<Integer> rhkStateList) {
        this.rhkStateList = rhkStateList;
    }

    public List<Integer> getBhkRcdStateList() {
        return bhkRcdStateList;
    }

    public void setBhkRcdStateList(List<Integer> bhkRcdStateList) {
        this.bhkRcdStateList = bhkRcdStateList;
    }

    public int getAddActiveTab() {
        return addActiveTab;
    }

    public void setAddActiveTab(int addActiveTab) {
        this.addActiveTab = addActiveTab;
    }

    public TdTjBhkClt getTdTjBhkClt() {
        return tdTjBhkClt;
    }

    public void setTdTjBhkClt(TdTjBhkClt tdTjBhkClt) {
        this.tdTjBhkClt = tdTjBhkClt;
    }

    public TbTjCrpt getSelectCrptInfo() {
        return selectCrptInfo;
    }

    public void setSelectCrptInfo(TbTjCrpt selectCrptInfo) {
        this.selectCrptInfo = selectCrptInfo;
    }

    public TdTjBhkClt getSelectBhkClt() {
        return selectBhkClt;
    }

    public void setSelectBhkClt(TdTjBhkClt selectBhkClt) {
        this.selectBhkClt = selectBhkClt;
    }

    public String getEditOnguadrStateId() {
        return editOnguadrStateId;
    }

    public void setEditOnguadrStateId(String editOnguadrStateId) {
        this.editOnguadrStateId = editOnguadrStateId;
    }

    public Map<String, String> getEditOnguadrStateMap() {
        return editOnguadrStateMap;
    }

    public void setEditOnguadrStateMap(Map<String, String> editOnguadrStateMap) {
        this.editOnguadrStateMap = editOnguadrStateMap;
    }

    public TdTjEmhistoryCltListCommBean getTdTjEmhistoryCltListBean() {
        return tdTjEmhistoryCltListBean;
    }

    public void setTdTjEmhistoryCltListBean(TdTjEmhistoryCltListCommBean tdTjEmhistoryCltListBean) {
        this.tdTjEmhistoryCltListBean = tdTjEmhistoryCltListBean;
    }

    public TdTjAnamnesisCltListCommBean getTdTjAnamnesisCltListBean() {
        return tdTjAnamnesisCltListBean;
    }

    public void setTdTjAnamnesisCltListBean(TdTjAnamnesisCltListCommBean tdTjAnamnesisCltListBean) {
        this.tdTjAnamnesisCltListBean = tdTjAnamnesisCltListBean;
    }

    public TdTjExmsdataCltCommBean getTdTjExmsdataCltBean() {
        return tdTjExmsdataCltBean;
    }

    public void setTdTjExmsdataCltBean(TdTjExmsdataCltCommBean tdTjExmsdataCltBean) {
        this.tdTjExmsdataCltBean = tdTjExmsdataCltBean;
    }

    public TdTjSymptomCltCommBean getTdTjSymptomCltBean() {
        return tdTjSymptomCltBean;
    }

    public void setTdTjSymptomCltBean(TdTjSymptomCltCommBean tdTjSymptomCltBean) {
        this.tdTjSymptomCltBean = tdTjSymptomCltBean;
    }

    public String getEditChkdocId() {
        return editChkdocId;
    }

    public void setEditChkdocId(String editChkdocId) {
        this.editChkdocId = editChkdocId;
    }

    public Map<String, String> getEditChkdocMap() {
        return editChkdocMap;
    }

    public void setEditChkdocMap(Map<String, String> editChkdocMap) {
        this.editChkdocMap = editChkdocMap;
    }

    public List<TbTjItemSort> getMustItemSortList() {
        return mustItemSortList;
    }

    public void setMustItemSortList(List<TbTjItemSort> mustItemSortList) {
        this.mustItemSortList = mustItemSortList;
    }

    public String getSearchItemCode() {
        return searchItemCode;
    }

    public void setSearchItemCode(String searchItemCode) {
        this.searchItemCode = searchItemCode;
    }

    public String getSearchItemName() {
        return searchItemName;
    }

    public void setSearchItemName(String searchItemName) {
        this.searchItemName = searchItemName;
    }

    public List<TbZwtjSchemeItems> getSelectItemList() {
        return selectItemList;
    }

    public void setSelectItemList(List<TbZwtjSchemeItems> selectItemList) {
        this.selectItemList = selectItemList;
    }

    public List<TbZwtjSchemeItems> getFilterSelectItemList() {
        return filterSelectItemList;
    }

    public void setFilterSelectItemList(List<TbZwtjSchemeItems> filterSelectItemList) {
        this.filterSelectItemList = filterSelectItemList;
    }

    public List<TbZwtjSchemeItems> getSelectedItemList() {
        return selectedItemList;
    }

    public void setSelectedItemList(List<TbZwtjSchemeItems> selectedItemList) {
        this.selectedItemList = selectedItemList;
    }

    public Integer getSelectCount() {
        return selectCount;
    }

    public void setSelectCount(Integer selectCount) {
        this.selectCount = selectCount;
    }

    public StringBuilder getItemCmbIds() {
        return itemCmbIds;
    }

    public void setItemCmbIds(StringBuilder itemCmbIds) {
        this.itemCmbIds = itemCmbIds;
    }

    public TbZwtjSchemeItems getSelectTbzwItems() {
        return selectTbzwItems;
    }

    public void setSelectTbzwItems(TbZwtjSchemeItems selectTbzwItems) {
        this.selectTbzwItems = selectTbzwItems;
    }

    public Integer getIfShowRad() {
        return ifShowRad;
    }

    public void setIfShowRad(Integer ifShowRad) {
        this.ifShowRad = ifShowRad;
    }

    public TdTjBhksubClt getOpBhkSubClt() {
        return opBhkSubClt;
    }

    public void setOpBhkSubClt(TdTjBhksubClt opBhkSubClt) {
        this.opBhkSubClt = opBhkSubClt;
    }

    public List<TsSimpleCode> getMhkrstList() {
        return mhkrstList;
    }

    public void setMhkrstList(List<TsSimpleCode> mhkrstList) {
        this.mhkrstList = mhkrstList;
    }

    public boolean isNcagSelect() {
        return ncagSelect;
    }

    public void setNcagSelect(boolean ncagSelect) {
        this.ncagSelect = ncagSelect;
    }

    public boolean isRecheckSelect() {
        return recheckSelect;
    }

    public void setRecheckSelect(boolean recheckSelect) {
        this.recheckSelect = recheckSelect;
    }

    public boolean isOtherSelect() {
        return otherSelect;
    }

    public void setOtherSelect(boolean otherSelect) {
        this.otherSelect = otherSelect;
    }

    public boolean isContraindSelect() {
        return contraindSelect;
    }

    public void setContraindSelect(boolean contraindSelect) {
        this.contraindSelect = contraindSelect;
    }

    public boolean isSupoccdiseSelect() {
        return supoccdiseSelect;
    }

    public void setSupoccdiseSelect(boolean supoccdiseSelect) {
        this.supoccdiseSelect = supoccdiseSelect;
    }

    public List<TdTjSupoccdiseCltWeb> getSupoccdiseCltWebList() {
        return supoccdiseCltWebList;
    }

    public void setSupoccdiseCltWebList(List<TdTjSupoccdiseCltWeb> supoccdiseCltWebList) {
        this.supoccdiseCltWebList = supoccdiseCltWebList;
    }

    public List<TdTjContraindCltWeb> getContraindCltWebList() {
        return contraindCltWebList;
    }

    public void setContraindCltWebList(List<TdTjContraindCltWeb> contraindCltWebList) {
        this.contraindCltWebList = contraindCltWebList;
    }

    public String getMainChkdocId() {
        return mainChkdocId;
    }

    public void setMainChkdocId(String mainChkdocId) {
        this.mainChkdocId = mainChkdocId;
    }

    public Integer getView() {
        return view;
    }

    public void setView(Integer view) {
        this.view = view;
    }

    public Map<String, String> getMainChkdocMap() {
        return mainChkdocMap;
    }

    public void setMainChkdocMap(Map<String, String> mainChkdocMap) {
        this.mainChkdocMap = mainChkdocMap;
    }

    public List<String> getMarrayList() {
        return marrayList;
    }

    public void setMarrayList(List<String> marrayList) {
        this.marrayList = marrayList;
    }

    public Integer getIfDraw() {
        return ifDraw;
    }

    public void setIfDraw(Integer ifDraw) {
        this.ifDraw = ifDraw;
    }

    public Integer getStadItemsStatus() {
        return stadItemsStatus;
    }

    public void setStadItemsStatus(Integer stadItemsStatus) {
        this.stadItemsStatus = stadItemsStatus;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public String getNewsTitle() {
        return newsTitle;
    }

    public void setNewsTitle(String newsTitle) {
        this.newsTitle = newsTitle;
    }

    public boolean isTchbadrsntimAble() {
        return tchbadrsntimAble;
    }

    public void setTchbadrsntimAble(boolean tchbadrsntimAble) {
        this.tchbadrsntimAble = tchbadrsntimAble;
    }

    public Integer getWorkTypeId() {
        return workTypeId;
    }

    public void setWorkTypeId(Integer workTypeId) {
        this.workTypeId = workTypeId;
    }

    public String getWorkTypeName() {
        return workTypeName;
    }

    public void setWorkTypeName(String workTypeName) {
        this.workTypeName = workTypeName;
    }

    public boolean isIfOtherWork() {
        return ifOtherWork;
    }

    public void setIfOtherWork(boolean ifOtherWork) {
        this.ifOtherWork = ifOtherWork;
    }

    public String getOtherDesc() {
        return otherDesc;
    }

    public void setOtherDesc(String otherDesc) {
        this.otherDesc = otherDesc;
    }

    public List<TsSimpleCode> getReCheckBads() {
        return reCheckBads;
    }

    public void setReCheckBads(List<TsSimpleCode> reCheckBads) {
        this.reCheckBads = reCheckBads;
    }

    public List<TsSimpleCode> getOtherBads() {
        return otherBads;
    }

    public void setOtherBads(List<TsSimpleCode> otherBads) {
        this.otherBads = otherBads;
    }

    public List<TsSimpleCode> getChooseBadList() {
        return chooseBadList;
    }

    public void setChooseBadList(List<TsSimpleCode> chooseBadList) {
        this.chooseBadList = chooseBadList;
    }

    public Integer getResultType() {
        return resultType;
    }

    public void setResultType(Integer resultType) {
        this.resultType = resultType;
    }

    public String getSearchOtherBadName() {
        return searchOtherBadName;
    }

    public void setSearchOtherBadName(String searchOtherBadName) {
        this.searchOtherBadName = searchOtherBadName;
    }

    public String getSearchReCheckBadName() {
        return searchReCheckBadName;
    }

    public void setSearchReCheckBadName(String searchReCheckBadName) {
        this.searchReCheckBadName = searchReCheckBadName;
    }

    public Map<String, String> getEditPsnTypeMap() {
        return editPsnTypeMap;
    }

    public void setEditPsnTypeMap(Map<String, String> editPsnTypeMap) {
        this.editPsnTypeMap = editPsnTypeMap;
    }

    public String getEditPsnTypeId() {
        return editPsnTypeId;
    }

    public void setEditPsnTypeId(String editPsnTypeId) {
        this.editPsnTypeId = editPsnTypeId;
    }

    public boolean isIfIdcAble() {
        return ifIdcAble;
    }

    public void setIfIdcAble(boolean ifIdcAble) {
        this.ifIdcAble = ifIdcAble;
    }

    public String getSearchNcagCheckBadName() {
        return searchNcagCheckBadName;
    }

    public void setSearchNcagCheckBadName(String searchNcagCheckBadName) {
        this.searchNcagCheckBadName = searchNcagCheckBadName;
    }

    public List<TsSimpleCode> getNcagBads() {
        return ncagBads;
    }

    public void setNcagBads(List<TsSimpleCode> ncagBads) {
        this.ncagBads = ncagBads;
    }

    public String getIfTjlrCrpt() {
        return ifTjlrCrpt;
    }

    public void setIfTjlrCrpt(String ifTjlrCrpt) {
        this.ifTjlrCrpt = ifTjlrCrpt;
    }

    public boolean isIfOnlyGBZ188() {
        return ifOnlyGBZ188;
    }

    public void setIfOnlyGBZ188(boolean ifOnlyGBZ188) {
        this.ifOnlyGBZ188 = ifOnlyGBZ188;
    }

    public List<TbZwtjSchemeItems> getGbz188ItemList() {
        return gbz188ItemList;
    }

    public void setGbz188ItemList(List<TbZwtjSchemeItems> gbz188ItemList) {
        this.gbz188ItemList = gbz188ItemList;
    }

    public List<TbZwtjSchemeItems> getFreedomItemList() {
        return freedomItemList;
    }

    public void setFreedomItemList(List<TbZwtjSchemeItems> freedomItemList) {
        this.freedomItemList = freedomItemList;
    }

	public boolean isIfOtherBadRsn() {
		return ifOtherBadRsn;
	}

	public void setIfOtherBadRsn(boolean ifOtherBadRsn) {
		this.ifOtherBadRsn = ifOtherBadRsn;
	}

    public Integer getContraindIndex() {
        return contraindIndex;
    }

    public void setContraindIndex(Integer contraindIndex) {
        this.contraindIndex = contraindIndex;
    }

    public TdTjChiefDoctorBean getTdTjChiefDoctorBean() {
        return tdTjChiefDoctorBean;
    }

    public void setTdTjChiefDoctorBean(TdTjChiefDoctorBean tdTjChiefDoctorBean) {
        this.tdTjChiefDoctorBean = tdTjChiefDoctorBean;
    }

	public TbTjStadItemsStatus getTjStadItemsStatus() {
		return tjStadItemsStatus;
	}

	public void setTjStadItemsStatus(TbTjStadItemsStatus tjStadItemsStatus) {
		this.tjStadItemsStatus = tjStadItemsStatus;
	}

    public TbTjCrpt getLastCrpt() {
        return lastCrpt;
    }

    public void setLastCrpt(TbTjCrpt lastCrpt) {
        this.lastCrpt = lastCrpt;
    }

    public List<Integer> getJcTypeList() {
        return jcTypeList;
    }

    public void setJcTypeList(List<Integer> jcTypeList) {
        this.jcTypeList = jcTypeList;
    }

    public Boolean getHasAutoGenBhkCode() {
        return hasAutoGenBhkCode;
    }

    public void setHasAutoGenBhkCode(Boolean hasAutoGenBhkCode) {
        this.hasAutoGenBhkCode = hasAutoGenBhkCode;
    }

    public Boolean getHasBhkResultStyle() {
        return hasBhkResultStyle;
    }

    public void setHasBhkResultStyle(Boolean hasBhkResultStyle) {
        this.hasBhkResultStyle = hasBhkResultStyle;
    }

    public List<TdTjBadrsnsClt> getTdTjBadrsnsCltList() {
        return tdTjBadrsnsCltList;
    }

    public void setTdTjBadrsnsCltList(List<TdTjBadrsnsClt> tdTjBadrsnsCltList) {
        this.tdTjBadrsnsCltList = tdTjBadrsnsCltList;
    }

    public String getIfChkJzs() {
        return ifChkJzs;
    }

    public void setIfChkJzs(String ifChkJzs) {
        this.ifChkJzs = ifChkJzs;
    }

    public String getIfChkAnamnesis() {
        return ifChkAnamnesis;
    }

    public void setIfChkAnamnesis(String ifChkAnamnesis) {
        this.ifChkAnamnesis = ifChkAnamnesis;
    }

    public boolean isIfOtherTouchBadRsn() {
        return ifOtherTouchBadRsn;
    }

    public void setIfOtherTouchBadRsn(boolean ifOtherTouchBadRsn) {
        this.ifOtherTouchBadRsn = ifOtherTouchBadRsn;
    }

    public String getIfMainDust() {
        return ifMainDust;
    }

    public void setIfMainDust(String ifMainDust) {
        this.ifMainDust = ifMainDust;
    }

    public List<TsSimpleCode> getDataVersionList() {
        return dataVersionList;
    }

    public void setDataVersionList(List<TsSimpleCode> dataVersionList) {
        this.dataVersionList = dataVersionList;
    }

    public TdTjBhksubClt getPassBhkSubClt() {
        return passBhkSubClt;
    }

    public void setPassBhkSubClt(TdTjBhksubClt passBhkSubClt) {
        this.passBhkSubClt = passBhkSubClt;
    }

    public Boolean getIfIdc() {
        return ifIdc;
    }

    public void setIfIdc(Boolean ifIdc) {
        this.ifIdc = ifIdc;
    }

    public String getSaveBasicMessage() {
        return saveBasicMessage;
    }

    public void setSaveBasicMessage(String saveBasicMessage) {
        this.saveBasicMessage = saveBasicMessage;
    }

    public Boolean getIfZysTimeRange() {
        return ifZysTimeRange;
    }

    public void setIfZysTimeRange(Boolean ifZysTimeRange) {
        this.ifZysTimeRange = ifZysTimeRange;
    }

    public Date getEmploymentHisCltStartDateMax() {
        this.employmentHisCltStartDateMax = this.tdTjBhkClt.getBhkDate();
        if (this.tdTjEmhistoryCltListBean != null) {
            if (this.tdTjEmhistoryCltListBean.getEmploymentHisClt().getStopDate() != null && DateUtils.isCompareDate(employmentHisCltStartDateMax, ">", this.tdTjEmhistoryCltListBean.getEmploymentHisClt().getStopDate())) {
                //最大日期取体检日期和结束日期中间相对小的
                this.employmentHisCltStartDateMax = this.tdTjEmhistoryCltListBean.getEmploymentHisClt().getStopDate();
            }
        }
        return employmentHisCltStartDateMax;
    }

    public void setEmploymentHisCltStartDateMax(Date employmentHisCltStartDateMax) {
        this.employmentHisCltStartDateMax = employmentHisCltStartDateMax;
    }

    public Date getEmploymentHisCltStopDateMin() {
        this.employmentHisCltStopDateMin = DateUtils.addDateByDay(this.tdTjBhkClt.getBrth(),1);
        if (this.tdTjEmhistoryCltListBean != null) {
            if (this.tdTjEmhistoryCltListBean.getEmploymentHisClt().getStartDate() != null && DateUtils.isCompareDate(employmentHisCltStopDateMin, "<", this.tdTjEmhistoryCltListBean.getEmploymentHisClt().getStartDate())) {
                //最小日期取出生日期和开始日期中间相对大的
                this.employmentHisCltStopDateMin = this.tdTjEmhistoryCltListBean.getEmploymentHisClt().getStartDate();
            }
        }
        return employmentHisCltStopDateMin;
    }

    public void setEmploymentHisCltStopDateMin(Date employmentHisCltStopDateMin) {
        this.employmentHisCltStopDateMin = employmentHisCltStopDateMin;
    }

    public Date getEmploymentHisCltStartDateMin() {
        return employmentHisCltStartDateMin;
    }

    public void setEmploymentHisCltStartDateMin(Date employmentHisCltStartDateMin) {
        this.employmentHisCltStartDateMin = employmentHisCltStartDateMin;
    }

    public Map<String, List<TbTjItemsGj>> getItemsGjMap() {
        return ItemsGjMap;
    }

    public void setItemsGjMap(Map<String, List<TbTjItemsGj>> itemsGjMap) {
        ItemsGjMap = itemsGjMap;
    }

    public Boolean getIfShowHealthContrust() {
        return ifShowHealthContrust;
    }

    public void setIfShowHealthContrust(Boolean ifShowHealthContrust) {
        this.ifShowHealthContrust = ifShowHealthContrust;
    }

    public String getItemTagDiagTip() {
        return itemTagDiagTip;
    }

    public void setItemTagDiagTip(String itemTagDiagTip) {
        this.itemTagDiagTip = itemTagDiagTip;
    }

    public void setOnguadrStateMap(Map<Integer, TsSimpleCode> onguadrStateMap) {
        this.onguadrStateMap = onguadrStateMap;
    }

    public boolean isIfBeforeOnPost() {
        return ifBeforeOnPost;
    }

    public void setIfBeforeOnPost(boolean ifBeforeOnPost) {
        this.ifBeforeOnPost = ifBeforeOnPost;
    }

    public List<TsSimpleCode> getProtectEquList() {
        return protectEquList;
    }
    public void setProtectEquList(List<TsSimpleCode> protectEquList) {
        this.protectEquList = protectEquList;
    }

    public Date getSearchRptSDate() {
        return searchRptSDate;
    }
    public void setSearchRptSDate(Date searchRptSDate) {
        this.searchRptSDate = searchRptSDate;
    }
    public Date getSearchRptEDate() {
        return searchRptEDate;
    }
    public void setSearchRptEDate(Date searchRptEDate) {
        this.searchRptEDate = searchRptEDate;
    }
}
