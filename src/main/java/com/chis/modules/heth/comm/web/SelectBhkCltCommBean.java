package com.chis.modules.heth.comm.web;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;

import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.EncryptFieldUtil;
import org.primefaces.context.RequestContext;

import com.chis.common.utils.DateUtils;
import com.chis.common.utils.IdcUtils;
import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.heth.comm.entity.TbTjCrpt;
import com.chis.modules.heth.comm.entity.TbTjSrvorg;
import com.chis.modules.heth.comm.entity.TdTjBhkClt;
import com.chis.modules.heth.comm.entity.TdTjPerson;
import com.chis.modules.heth.comm.service.TdTjBhkCltCommServiceImpl;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesBean;


@ManagedBean(name="selectBhkCltCommBean")
@ViewScoped
public class SelectBhkCltCommBean extends FacesBean {

    /** 过滤选择的体检信息列表 */
    private List<TdTjBhkClt> bhkCltList;
    /** 选择体检信息 */
    private TdTjBhkClt selectBhkClt;

    /**能看到的地区集合*/
    private List<TsZone> zoneList;
    /**查询条件：地区名称*/
    private String searchZoneName;
    /**查询条件：地区编码*/
    private String searchZoneCode;
    /**查询条件：企业名称*/
    private String searchCrptName;
    /**查询条件：姓名*/
    private String searchPersonName;
    /**查询条件：身份证号*/
    private String searchIdc;
    /** 地区是否查询全国 */
    private Boolean ifAllZone = Boolean.FALSE;
    /** 查询条件是否显示为用人单位名称 */
    private Boolean ifEmployer = Boolean.FALSE;

    private TdTjBhkCltCommServiceImpl service = SpringContextHolder.getBean(TdTjBhkCltCommServiceImpl.class);
    private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
    private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);

    public SelectBhkCltCommBean() {
        if("1".equals(JsfUtil.getRequest().getParameter("ifAllZone"))){
            this.ifAllZone = Boolean.TRUE;
        }
        if("1".equals(JsfUtil.getRequest().getParameter("ifEmployer"))){
            this.ifEmployer = Boolean.TRUE;
        }
        this.initParam();
    }

    /**
     * @Description : 初始化查询参数
     * @MethodAuthor: anjing
     * @Date : 2019/5/14 17:40
     **/
    private void initParam() {
        this.searchZoneCode = sessionData.getUser().getTsUnit().getTsZone().getZoneGb();
        this.searchZoneName = sessionData.getUser().getTsUnit().getTsZone().getZoneName();
        Short zoneType = Global.getUser().getTsUnit().getTsZone().getZoneType();
        String newSearchZoneCode = this.searchZoneCode;
        if(zoneType > 2) {
            newSearchZoneCode = this.searchZoneCode.substring(0, 2) + "00000000";
        }

        /**
         * 地区初始化
         */
        this.zoneList = this.ifAllZone ? this.commService.findZoneListByGbAndTypeNoNation("", true, "", "") :
                this.commService.findZoneListByGbAndTypeNoNation(newSearchZoneCode, true, "", "");
        if(this.ifAllZone){
            this.searchZoneCode = null;
            this.searchZoneName = null;
        }
    }

    public void searchAction() {
        if (StringUtils.isBlank(this.searchCrptName)) {
            JsfUtil.addErrorMessage(this.ifEmployer ? "用人单位名称不能为空！" : "企业名称不能为空！");
            return;
        }
        if(StringUtils.isNotBlank(searchIdc)) {
            String checkIDC = IdcUtils.checkIDC(searchIdc);
            if(StringUtils.isNotBlank(checkIDC)) {
                JsfUtil.addErrorMessage(checkIDC);
                return;
            }
        }
        this.bhkCltList = new ArrayList<>();
        StringBuffer sb = new StringBuffer();
        Map<String, Object> paramMap = new HashMap<>();
        sb.append("SELECT Distinct T.RID, CASE WHEN T5.ZONE_TYPE >2 THEN substr(T5.FULL_NAME, INSTR(T5.FULL_NAME,'_')+1) ELSE T5.FULL_NAME END, T.CRPT_NAME, T.BHK_CODE, T.PERSON_NAME, " +
                "T.IDC, T.SEX, T.BRTH, T.LNKTEL, T.BHK_DATE, T6.CODE_NAME, T.IF_RHK, T.BHK_RCD_STATE, T.AGE, T.ISXMRD, T.DPT, T.WORK_NAME, T.WRKNUM, T.WRKLNT, T.TCHBADRSNTIM, " +
                "T6.RID AS ONGUARD_STATEID, T8.BADRSNS, T.CRPT_ID, T.BHK_TYPE, T.BHKORG_ID, T.PERSON_ID, T.WRKLNTMONTH, T.TCHBADRSNMONTH,t.CARD_TYPE_ID,t.WORK_TYPE_ID,t.WORK_OTHER,T.JC_TYPE,T.OTHER_BADRSN ");
        sb.append(",T4.INDUS_TYPE_ID,T10.EXTENDS1,T.EMP_CRPT_ID,T11.CRPT_NAME AS EMP_CRPT_NAME, T.TCH_OTHER_BADRSN ,T.PROTECT_EQU_ID ");
        sb.append(" FROM TD_TJ_BHK_CLT T");
        sb.append(" INNER JOIN TB_TJ_SRVORG T1 ON T.BHKORG_ID = T1.RID ");
        sb.append(" INNER JOIN TS_UNIT T2 ON T1.REG_ORGID = T2.RID  ");
        sb.append(" INNER JOIN TD_ZW_TJORGINFO T3 ON T3.ORG_ID = T2.RID ");
        sb.append(" INNER JOIN TB_TJ_CRPT T4 ON T.CRPT_ID = T4.RID ");
        sb.append(" INNER JOIN TS_ZONE T5 ON T4.ZONE_ID = T5.RID ");
        sb.append(" INNER JOIN TS_SIMPLE_CODE T6 ON T.ONGUARD_STATEID = T6.RID ");
        sb.append(" INNER JOIN TD_TJ_PERSON T9 ON T.PERSON_ID = T9.RID  ");
        sb.append(" LEFT JOIN TD_TJ_BHK_CLT T7 ON T7.LAST_BHK_ID = T.RID AND T7.DEL_MARK = 0 ");
        sb.append(" LEFT JOIN (SELECT  T.RID, (  SELECT LISTAGG(T2.CODE_NAME, ',') WITHIN GROUP (ORDER BY T2.CODE_NO)  ");
        sb.append(" FROM TD_TJ_BADRSNS_CLT T1 ");
        sb.append("  LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T1.BADRSN_ID  ");
        sb.append("  WHERE T1.BHK_ID = T.RID  ) AS BADRSNS ");
        sb.append("  FROM TD_TJ_BHK_CLT T) T8 ON T8.RID = T.RID ");
        sb.append(" LEFT JOIN TD_TJ_BADRSNS_CLT m1 ON T.rid =m1.BHK_ID ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE m2 ON m2.RID = m1.exam_conclusion_id ");
        sb.append(" LEFT JOIN TS_SIMPLE_CODE T10 ON T10.RID = T4.INDUS_TYPE_ID");
        sb.append(" LEFT JOIN TB_TJ_CRPT T11 ON T11.RID = T.EMP_CRPT_ID");
        sb.append(" WHERE T.DEL_MARK = 0 ");
        sb.append(" AND T3.STATE = 1 ");
        sb.append(" AND T7.RID IS NULL ");
        sb.append(" AND T2.RID = ").append(Global.getUser().getTsUnit().getRid());
        sb.append(" AND T.BHK_RCD_STATE = 5");
        sb.append(" AND m2.extends2 =2");
        if (StringUtils.isNotBlank(searchZoneCode)) {
            sb.append(" AND T5.ZONE_GB LIKE '").append(StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim())).append("%'");
        }
        if (StringUtils.isNotBlank(this.searchCrptName)) {
            sb.append(" AND T.CRPT_NAME LIKE :crptName");
            paramMap.put("crptName", "%" + this.searchCrptName.trim() + "%");
        }
        if (StringUtils.isNotBlank(this.searchIdc)) {
            sb.append(" AND upper(T.IDC) =upper( :idc)");
            paramMap.put("idc",EncryptFieldUtil.strEncode(StringUtils.convertBFH(this.searchIdc.trim())));
        }
        if (StringUtils.isNotBlank(this.searchPersonName)) {
            sb.append(" AND T.PERSON_NAME LIKE :psnName");
            paramMap.put("psnName", "%" + this.searchPersonName.trim() + "%");
        }
        List<Object[]> list = service.findDataBySqlNoPage(sb.toString(), paramMap);
        for (Object[] obj : list) {
            TdTjBhkClt tdTjBhkClt = new TdTjBhkClt();
            tdTjBhkClt.setRid(Integer.valueOf(obj[0].toString()));
            tdTjBhkClt.setBhkType(Integer.valueOf(obj[23].toString()));
            // 设置资质服务机构注册信息
            TbTjSrvorg tbTjSrvorg = new TbTjSrvorg();
            tbTjSrvorg.setRid(Integer.valueOf(obj[24].toString()));
            tdTjBhkClt.setFkByBhkorgId(tbTjSrvorg);
            // 设置企业信息
            TbTjCrpt tbTjCrpt = new TbTjCrpt();
            TsZone tsZone = new TsZone();
            tsZone.setFullName(StringUtils.objectToString(obj[1]));
            tbTjCrpt.setTsZoneByZoneId(tsZone);
            tbTjCrpt.setCrptName(StringUtils.objectToString(obj[2]));
            tbTjCrpt.setRid(Integer.valueOf(obj[22].toString()));
            if (null!=obj[33]) {
            	TsSimpleCode t = new TsSimpleCode();
            	t.setRid(Integer.valueOf(obj[33].toString()));
            	t.setExtendS1(StringUtils.objectToString(obj[34]));
            	tbTjCrpt.setTsSimpleCodeByIndusTypeId(t);
			}
            tdTjBhkClt.setFkByCrptId(tbTjCrpt);
            if (null!=obj[35]) {//用工单位
            	TbTjCrpt empCrpt = new TbTjCrpt();
            	empCrpt.setRid(Integer.valueOf(obj[35].toString()));
            	empCrpt.setCrptName(StringUtils.objectToString(obj[36]));
            	tdTjBhkClt.setFkByEmpCrptId(empCrpt);
			}
            // 设置人员信息
            TdTjPerson tjPerson = new TdTjPerson();
            tjPerson.setRid(Integer.valueOf(obj[25].toString()));
            tdTjBhkClt.setFkByPersonId(tjPerson);
            tdTjBhkClt.setCrptName(StringUtils.objectToString(obj[2]));
            tdTjBhkClt.setBhkCode(StringUtils.objectToString(obj[3]));
            tdTjBhkClt.setPersonName(StringUtils.objectToString(obj[4]));
            tdTjBhkClt.setIdc(EncryptFieldUtil.strDecode(StringUtils.objectToString(obj[5])));
            tdTjBhkClt.setLnktel(EncryptFieldUtil.strDecode(StringUtils.objectToString(obj[8])));
            tdTjBhkClt.setSex(StringUtils.objectToString(obj[6]));
            tdTjBhkClt.setBrth(DateUtils.parseDate(obj[7]));
            tdTjBhkClt.setBhkDate(DateUtils.parseDate(obj[9]));
            TsSimpleCode tsSimpleCode = new TsSimpleCode();
            tsSimpleCode.setCodeName(StringUtils.objectToString(obj[10]));
            tsSimpleCode.setRid(Integer.valueOf(obj[20].toString()));
            tdTjBhkClt.setFkByOnguardStateid(tsSimpleCode);
            tdTjBhkClt.setIfRhk(Integer.valueOf(obj[11].toString()));
            tdTjBhkClt.setBhkRcdState(Integer.valueOf(obj[12]==null?"0":obj[12].toString()));
            tdTjBhkClt.setAge(Integer.valueOf(obj[13]==null?"0":obj[13].toString()));
            tdTjBhkClt.setIsxmrd(StringUtils.objectToString(obj[14]));
            tdTjBhkClt.setDpt(StringUtils.objectToString(obj[15]));
            tdTjBhkClt.setWorkName(StringUtils.objectToString(obj[16]));
            tdTjBhkClt.setWrknum(StringUtils.objectToString(obj[17]));
            tdTjBhkClt.setWrklnt(obj[18]==null?null:new BigDecimal(StringUtils.objectToString(obj[18])));
            tdTjBhkClt.setTchbadrsntim(obj[19]==null?null:new BigDecimal(StringUtils.objectToString(obj[19])));
            tdTjBhkClt.setBadrsns(StringUtils.objectToString(obj[21]));
            tdTjBhkClt.setWrklntmonth(obj[26]==null?null:Integer.parseInt(obj[26].toString()));
            tdTjBhkClt.setTchbadrsnmonth(obj[27]==null?null:Integer.parseInt(obj[27].toString()));
            if(obj[28]!=null){
                tdTjBhkClt.setFkByCardTypeId(new TsSimpleCode(Integer.parseInt(obj[28].toString())));
            }
            if(obj[29]!=null){
                tdTjBhkClt.setFkByWorkTypeId(new TsSimpleCode(Integer.parseInt(obj[29].toString())));
            }
            tdTjBhkClt.setWorkOther(StringUtils.objectToString(obj[30]));
            if(null != obj[31]){
                tdTjBhkClt.setJcType(Integer.parseInt(obj[31].toString()));
            }
            if(null != obj[32]){
                tdTjBhkClt.setOtherBadRsn(StringUtils.objectToString(obj[32]));
            }
            if(null != obj[37]){
                tdTjBhkClt.setTchOtherBadrsn(StringUtils.objectToString(obj[37]));
            }
            if(null != obj[38]){
                tdTjBhkClt.setFkByProtectEquId(new TsSimpleCode(Integer.parseInt(obj[38].toString())));
            }
            this.bhkCltList.add(tdTjBhkClt);
        }
    }

    /**
     * @Description : 选择体检信息
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 15:28
     **/
    public void selectBhkCltAction(){
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("selectBhkClt", selectBhkClt);
        RequestContext.getCurrentInstance().closeDialog(map);
    }

    /**
     * @Description : 关闭弹窗
     * @MethodAuthor: anjing
     * @Date : 2019/5/15 16:03
     **/
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }

    public List<TdTjBhkClt> getBhkCltList() {
        return bhkCltList;
    }

    public void setBhkCltList(List<TdTjBhkClt> bhkCltList) {
        this.bhkCltList = bhkCltList;
    }

    public TdTjBhkClt getSelectBhkClt() {
        return selectBhkClt;
    }

    public void setSelectBhkClt(TdTjBhkClt selectBhkClt) {
        this.selectBhkClt = selectBhkClt;
    }

    public List<TsZone> getZoneList() {
        return zoneList;
    }

    public void setZoneList(List<TsZone> zoneList) {
        this.zoneList = zoneList;
    }

    public String getSearchZoneName() {
        return searchZoneName;
    }

    public void setSearchZoneName(String searchZoneName) {
        this.searchZoneName = searchZoneName;
    }

    public String getSearchZoneCode() {
        return searchZoneCode;
    }

    public void setSearchZoneCode(String searchZoneCode) {
        this.searchZoneCode = searchZoneCode;
    }

    public String getSearchCrptName() {
        return searchCrptName;
    }

    public void setSearchCrptName(String searchCrptName) {
        this.searchCrptName = searchCrptName;
    }

    public String getSearchPersonName() {
        return searchPersonName;
    }

    public void setSearchPersonName(String searchPersonName) {
        this.searchPersonName = searchPersonName;
    }

    public String getSearchIdc() {
        return searchIdc;
    }

    public void setSearchIdc(String searchIdc) {
        this.searchIdc = searchIdc;
    }

    public Boolean getIfAllZone() {
        return ifAllZone;
    }

    public void setIfAllZone(Boolean ifAllZone) {
        this.ifAllZone = ifAllZone;
    }

    public Boolean getIfEmployer() {
        return ifEmployer;
    }

    public void setIfEmployer(Boolean ifEmployer) {
        this.ifEmployer = ifEmployer;
    }
}
