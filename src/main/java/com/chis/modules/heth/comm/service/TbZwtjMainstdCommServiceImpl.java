package com.chis.modules.heth.comm.service;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.TbZwtjMainstd;
import com.chis.modules.system.service.AbstractTemplate;

/**
 * @Description : 职业健康监护方案标准（GBZ-188）-业务层
 * @ClassAuthor : anjing
 * @Date : 2019/5/20 13:16
 **/
@Service
@Transactional(readOnly = true)
public class TbZwtjMainstdCommServiceImpl extends AbstractTemplate {

    /**
     * @Description : 根据危害因素Id、在岗状态Id，获取职业监护方案标准集合
     * @MethodAuthor: anjing
     * @Date : 2019/5/20 13:25
     **/
    public List<TbZwtjMainstd> selectMainstdListByBadrsnIdAndWorkStateId(String badrsnIds, Integer stateId) {
        if(StringUtils.isNotBlank(badrsnIds) && null != stateId) {
            StringBuilder sb = new StringBuilder();
            sb.append(" select t from TbZwtjMainstd t ");
            sb.append(" where t.stopTag = 1 ");
            sb.append(" and t.tsSimpleCodeByBadrsnId.rid in (").append(badrsnIds).append(")");
            sb.append(" and t.tsSimpleCodeByWorkStateid.rid = ").append(stateId);
            sb.append(" order by t.tsSimpleCodeByBadrsnId.codeNo,t.tsSimpleCodeByWorkStateid.codeNo");
            return em.createQuery(sb.toString()).getResultList();
        }
        return null;
    }

    /**
    * <p>Description：根据危害因素+在岗状态查询项目组合 </p>
    * <p>Author： yzz 2025/2/24 </p>
    */
    public List<Object[]> selectCombidListByBadrsnIdAndWorkStateId(String badrsnIds, Integer stateId, String codeNos) {
        List<Object[]> combidList = new ArrayList<>();
        if(StringUtils.isNotBlank(badrsnIds) && null != stateId) {
            String[] codeNoArr = codeNos.split(",");
            StringBuffer codeNoBuffer = new StringBuffer();
            for(String codeNo : codeNoArr){
                if(StringUtils.isBlank(codeNo)){
                    continue;
                }
                codeNoBuffer.append(",").append("'").append(codeNo).append("'");
            }
            StringBuilder sb = new StringBuilder();
            sb.append(" SELECT T1.ITEM_CMBID, T1.IS_MUST ");
            sb.append(" FROM TB_ZWTJ_MAINSTD T  ");
            sb.append(" INNER JOIN TB_ZWTJ_SCHEME_ITEMS T1 ON T1.SCHEME_ID = T.RID  ");
            sb.append(" INNER JOIN TS_SIMPLE_CODE T2 ON T1.ITEM_CMBID = T2.RID ");
            sb.append(" WHERE T.BADRSN_ID IN (").append(badrsnIds).append(")");
            sb.append(" AND T.WORK_STATEID =").append(stateId);
            sb.append(" AND T2.CODE_NO NOT IN (").append(codeNoBuffer.substring(1)).append(")");
            sb.append(" AND T.STOP_TAG = 1 ");
            sb.append(" AND T1.STOP_TAG = 1 ");
            //sb.append(" GROUP BY T1.ITEM_CMBID, T1.IS_MUST ");
            sb.append(" GROUP BY T1.ITEM_CMBID, T1.IS_MUST, T2.NUM, T2.CODE_NO ");
            sb.append(" ORDER BY T2.NUM, T2.CODE_NO ");
            combidList = em.createNativeQuery(sb.toString()).getResultList();
        }
        return combidList;
    }
}
