package com.chis.modules.heth.comm.web;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.heth.comm.json.BhkAllCheckJson;
import com.chis.modules.heth.comm.json.SendBackCheckJson;
import com.chis.modules.heth.comm.service.HethBaseCommServiceImpl;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.heth.comm.service.TdZwBgkFlowServiceImpl;
import com.chis.modules.system.entity.*;
import com.chis.modules.system.enumn.SystemMessageEnum;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.utils.HolidayUtil;
import com.chis.modules.system.utils.ImportExcelUtil;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.ss.usermodel.Workbook;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.context.RequestContext;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

/**
 * <p>类描述：职业健康检查数据审核</p>
 * @ClassAuthor qrr,2021年5月12日,TdTjBHkCheckListBean
 * */
@ManagedBean(name = "tdTjBHkCheckListBean")
@ViewScoped
public class TdTjBHkCheckListBean extends FacesEditBean implements IProcessData{
	private static final long serialVersionUID = 1L;
	private CommServiceImpl commService = SpringContextHolder.getBean(CommServiceImpl.class);
	private HethStaQueryCommServiceImpl hethStaQueryCommService = SpringContextHolder.getBean(HethStaQueryCommServiceImpl.class);
	private HethBaseCommServiceImpl hethBaseCommService = SpringContextHolder.getBean(HethBaseCommServiceImpl.class);
	private TdZwBgkFlowServiceImpl bgkFlowService = SpringContextHolder.getBean(TdZwBgkFlowServiceImpl.class);
	/**查询条件：用人单位地区名称*/
    private String searchZoneName;
    /**查询条件：用人单位地区编码*/
    private String searchZoneCode;
    /**查询条件：地区集合*/
    private List<TsZone> zoneList;
	/**查询条件：用人单位地区集合*/
    private List<TsZone> zoneListEmp;
    /**查询条件：用人单位用人单位名称*/
    private String searchCrptName;
    /**查询条件：用人单位社会信用代码*/
    private String searchCreditCode;
	/**查询条件：用工单位地区名称*/
	private String searchZoneNameEmp;
	/**查询条件：用工单位地区编码*/
	private String searchZoneCodeEmp;
	/**查询条件：用工单位用人单位名称*/
	private String searchCrptNameEmp;
	/**查询条件：用工单位社会信用代码*/
	private String searchCreditCodeEmp;
    /**查询条件：姓名*/
    private String searchPersonName;
    /**查询条件：身份证*/
    private String searchIdc;
    /**查询条件：体检日期查询-开始日期*/
    private Date searchBhkBdate;
    /**查询条件：体检日期查询-结束日期*/
    private Date searchBhkEdate;
	/**查询条件-在岗状态*/
	private List<TsSimpleCode> onGuardList;
	private String selectOnGuardNames;
	/**查询条件-在岗状态*/
	private String selectOnGuardIds;
	/**查询条件-危害因素*/
	private List<TsSimpleCode> badRsnList;
	private String selectBadRsnNames;
	/**查询条件-危害因素*/
	private String selectBadRsnIds;
	/**查询条件：选择的主检结论名称*/
	private List<TsSimpleCode> searchBhkrstList;
	/**查询条件：选择的危害因素结论*/
	private String searchSelBhkrstIds;
	private String searchBhkrstName;
	/**查询条件：接收日期查询-开始日期*/
    private Date searchRcvBdate;
    /**查询条件：接收日期查询-结束日期*/
    private Date searchRcvEdate;
    /**查询条件：监测类别*/
    private String[] jcTypes;
    /**查询条件：是否复检*/
    private String[] ifRhks;
    /**查询条件：是否正常*/
    private String[] ifAbnormals;
	/**
	 * 异常原因
	 */
	private String searchAbnormals;
	/**查询条件：报告开始日期*/
	private Date startCreateDate;
	/**查询条件：报告结束日期*/
	private Date endCreateDate;
	/**查询条件：报告出具开始日期*/
	private Date startRptPrintDate;
	/**查询条件：报告出具结束日期*/
	private Date endRptPrintDate;
	/**查询条件：体检类型*/
	private String[] searchBhkType;

	/**
	 * 选择的检查机构ID(多选)
	 */
	private String searchUnitId;
	/**
	 * 选择的检查机构名称(多选)
	 */
	private String searchUnitName;
	/** 选中的状态 */
    private String[] states;
    /** 所有状态 */
    private List<SelectItem> stateList = new ArrayList<>();
	private Map<String, String> stateMap = new HashMap<>(16);
    /**地区级别：2:省级3：市级4：区县级*/
	private Integer zoneType;
    /** 选择的结果集 */
	private List<Object[]> selectEntitys;
	private Integer rid;
    /**默认审核意见*/
	private String defaultAuditAdv;
	/** 审核期限 */
	private String limitTime;
	/** 审核级别 */
	private String checkLevel;
	private boolean checkLevel2;
	private boolean checkLevel3;

	/*1:报告打印日期  2:体检日期*/
	private String receiveDate;
	/** 审核以及查询页的实体 */
    private TdTjBhk tdTjBhk;
    /**异常情况*/
    private List<String> unAbnormalsList = new ArrayList<>();
    /** 审核界面 审核结果 */
    private Integer checkState;
	/** 审核界面 审核意见 */
	private String checkRst;
	/** 审核、详情界面 是否显示初检部分 */
    private Boolean countyFlag;
	/** 审核、详情界面 是否显示复检部分 */
    private Boolean cityFlag;
	/** 详情界面 是否显示终审部分 */
    private Boolean proFlag;
    /** 详情界面 撤销按钮是否显示 */
    private Boolean cancelFlag;
    /** 是否编辑页 */
    private Boolean editFlag;

    /**批量审核页面 审核意见 */
    private String batchCheckRst;
    /**批量审核页面 审核结果 */
    private Integer batchCheckState;
	private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();
	/*** 体检项目的布局表格*/
	private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
			OutputPanel.COMPONENT_TYPE);

	/**
	 * 导出菜单授权的菜单树
	 */
	private TreeNode exportTreeNode;
	/**
	 * 导出菜单授权的已选择的树
	 */
	private TreeNode[] selectedExportNodes;
	/**
	 * 过滤体检项目ID
	 */
	private String exportItems;
	/**
	 * 过滤体检项目ID
	 */
	private String selectExportNames;
	/**
	 * 第一层集合
	 */
	private Set<String> firstLevelNoSet;
	/**
	 * 已选择过滤项目集合
	 */
	private Set<String> selectExportItemSet = new HashSet<String>();
	/**
	 * 体检项目编码
	 */
	private Map<String, TbTjItems> codeMap = new HashMap<>();
	/**
	 * 非第一层集合
	 */
	private Set<String> levelNoSet;
	/**
	 * 体检项目缓存
	 */
	private Map<Integer, String> itemCachMap = new HashMap<>();
	private Map<Integer, TbTjItems> itemCachMaps = new HashMap<>();

	/**
	 * 同时导出最大负载
	 */
	private final Integer MAX_EXPORT_LOAD;
	/**
	 * 导出数据对象
	 */
	private TdTjExport tdTjExport;
	/**
	 * d导出类型码表
	 */
	private Map<String, TsSimpleCode> busTypeMap;
	/**
	 * 导出信息的拼接：rid,codeName
	 */
	private Map<Integer, String> tsSimpleCodeMap;
	/** 审核类型 1批量审核2全部审核*/
	private String checkType = "";
	/**历次审核意见*/
	private List<Object[]> historyList;
	/**是否显示下一个*/
	private boolean nextFlag;
	/**待审核的rid List*/
	private List<Integer> nextList;
	/**待审核的rid*/
	private Integer nextRid;
	/** 全部审核参数标记位*/
	private boolean allCheckParamFlag = false;
	/** 体检机构提示信息：体检机构：xx 联系人：xx 联系电话：xx*/
	private String tipInfo;
	/** 是否需要脱敏 1需要  */
	private Integer needEnctryInfo;
	/** 是否具有内部导出权限 */
	private Boolean ifHasInnerExp;
	/** 是否导入权限 */
	private Boolean ifImportExp;
	/**
	 * 今天距离日期N个工作日之后剩余的工作日天数
	 * <pre>key: 日期</pre><pre>value: 今天距离日期N个工作日之后剩余的工作日天数</pre>
	 */
	private Map<Date, Integer> daysBeforeTheWorkingDayMap;
	private Date minDaysBeforeTheWorkingDayDate;
	/** 导入错误文件信息 */
	private Object[] errFileObj;
	/**是否显示错误下载信息按钮*/
	private boolean ifErrFileBnt;

    public TdTjBHkCheckListBean() {
    	this.ifSQL = true;
    	/**批量审核默认审核意见*/
    	this.defaultAuditAdv = PropertyUtils.getValue("defaultAuditAdv");
    	this.limitTime = PropertyUtils.getValue("bhkLimitTime");
    	this.checkLevel = PropertyUtils.getValue("checkLevel");
		this.checkLevel2 = "2".equals(this.checkLevel);
		this.checkLevel3 = "3".equals(this.checkLevel);
    	this.receiveDate=PropertyUtils.getValue("receiveDate");
    	TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		if(null==tsZone.getRealZoneType()){
			this.zoneType = tsZone.getZoneType().intValue();
		}else {
			this.zoneType = tsZone.getRealZoneType().intValue();
		}
    	this.initParam();
    	this.initSearchStates();
    	//this.searchAction();
		this.initTjItem();
		countyFlag = Boolean.FALSE;
		cityFlag = Boolean.FALSE;
		proFlag = Boolean.FALSE;
		cancelFlag = Boolean.FALSE;
		nextFlag = Boolean.FALSE;

		MAX_EXPORT_LOAD = PropertyUtils.getValue("asyncExport.times") == null ? 0 : Integer.parseInt(PropertyUtils.getValue("asyncExport.times"));
		this.ifHasInnerExp = Global.getBtnSet().contains("heth_zyjkjcsjsh_innerexp");
		//导入权限
		this.ifImportExp = Global.getBtnSet().contains("heth_zyjkjcsjsh_import");

		this.daysBeforeTheWorkingDayMap = this.bgkFlowService.calDaysBeforeTheWorkingDay();
		for (Date date : this.daysBeforeTheWorkingDayMap.keySet()) {
			if (date == null) {
				continue;
			}
			if (ObjectUtil.isEmpty(this.minDaysBeforeTheWorkingDayDate)) {
				this.minDaysBeforeTheWorkingDayDate = date;
				continue;
			}
			if (date.before(this.minDaysBeforeTheWorkingDayDate)) {
				this.minDaysBeforeTheWorkingDayDate = date;
			}
		}
		//查询是否存在错误信息
		initErrFilePath();
	}
    /**
 	 * <p>方法描述：初始化查询条件</p>
 	 * @MethodAuthor qrr,2021年5月12日,initParam
     * */
    private void initParam() {
		this.tsSimpleCodeMap =new HashMap<>();
    	this.searchBhkEdate = new Date();
    	TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
        this.searchZoneCodeEmp = tsZone.getZoneCode();
        this.searchZoneNameEmp = tsZone.getZoneName();
        this.zoneListEmp = this.commService.findZoneListByGbAndTypeNoNation( tsZone.getZoneGb(), true, null,null);
        this.zoneList = this.commService.findZoneListByGbAndTypeNoNation( null, true, null,null);
        this.onGuardList = commService.findSimpleCodesByTypeId("5009");
        this.badRsnList = commService.findSimpleCodesByTypeId("5007");
		this.searchBhkrstList = commService.findSimpleCodesByTypeId("5005");
		//导出类型码表
		List<TsSimpleCode> simpleCodes = commService.findLevelSimpleCodesByTypeId("5550");
		busTypeMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(simpleCodes)) {
			for (TsSimpleCode t : simpleCodes) {
				if (StringUtils.isNotBlank(t.getExtendS1())) {
					busTypeMap.put(t.getExtendS1(), t);
				}
			}
		}
    }
    /**
 	 * <p>方法描述：初始化状态</p>
 	 * @MethodAuthor qrr,2021年5月12日,initSearchStates
     * */
	private void initSearchStates() {
		this.stateList = new ArrayList<>();
		this.stateList.add(new SelectItem("1", "区县级待审"));
		this.stateList.add(new SelectItem("0", "区县级退回"));
		if (this.checkLevel3) {
			this.stateList.add(new SelectItem("3", "市级待审"));
			this.stateList.add(new SelectItem("2", "市级退回"));
		}
		this.stateList.add(new SelectItem("5", "省级待审"));
		this.stateList.add(new SelectItem("4", "省级退回"));
		this.stateList.add(new SelectItem("6", "省级通过"));
		this.stateList.add(new SelectItem("7", "国家退回"));
		if (new Integer(4).equals(this.zoneType)) {
			this.states = new String[]{"1"};
		} else if (new Integer(3).equals(this.zoneType)) {
			this.states = new String[]{"3"};
		} else if (new Integer(2).equals(this.zoneType)) {
			this.states = new String[]{"5"};
		}

		for (SelectItem selectItem : this.stateList) {
			this.stateMap.put(StringUtils.objectToString(selectItem.getValue()), selectItem.getLabel());
		}
	}

	/**
	 * 初始化导出tree
	 */
	private void initTjItem() {
		List<TsSimpleCode> codeList = this.commService.findLevelSimpleCodesByTypeId("5008");
		if (!CollectionUtils.isEmpty(codeList)) {
			firstLevelNoSet = new LinkedHashSet<>();
			// 项目分类导出
			codeMap = new HashMap<>();
			for (TsSimpleCode tsSimpleCode : codeList) {
				if (StringUtils.containsNone(tsSimpleCode.getCodeLevelNo(), ".")) {
					continue;
				}
				TbTjItems itm = new TbTjItems();
				itm.setRid(-1);
				itm.setItemName(tsSimpleCode.getCodeName());
				itm.setLevelCode(tsSimpleCode.getCodeLevelNo());
				firstLevelNoSet.add(tsSimpleCode.getCodeLevelNo());
				codeMap.put(tsSimpleCode.getCodeLevelNo(), itm);
			}

			//非第一层集合
			levelNoSet = new LinkedHashSet<>();
			List<TbTjItems> findTjItemList = hethStaQueryCommService.findTjItemList(null, null, Short.valueOf("1"), null, null);
			if (null != findTjItemList && findTjItemList.size() > 0) {
				for (TbTjItems tbTjItems : findTjItemList) {
					itemCachMap.put(tbTjItems.getRid(), tbTjItems.getItemName());
					itemCachMaps.put(tbTjItems.getRid(), tbTjItems);

					//项目层级编码
					String codeLevelNo = tbTjItems.getTsSimpleCode().getCodeLevelNo();

					tbTjItems.setLevelCode(codeLevelNo + "." + tbTjItems.getItemCode());
					levelNoSet.add(tbTjItems.getLevelCode());

					codeMap.put(tbTjItems.getLevelCode(), tbTjItems);
				}
			}

		}

	}
	/**
 	 * <p>方法描述：查询</p>
 	 * @MethodAuthor qrr,2021年5月12日,searchAction
	 * */
	@Override
    public void searchAction() {
		//查询是否存在错误信息
		initErrFilePath();
		if (!validateSearch()) {
			return;
		}
       super.searchAction();
    }

	/**
	 *  <p>方法描述：该地区改登录人是否存在</p>
	 * @MethodAuthor hsj 2025-03-28 11:38
	 */
	private void initErrFilePath() {
		this.ifErrFileBnt = Boolean.FALSE;//默认不显示
		this.errFileObj =  this.hethBaseCommService.findErrFilePath();
		if(ObjectUtil.isNotEmpty(this.errFileObj)){
			if(ObjectUtil.isNotNull(this.errFileObj[1]) || ObjectUtil.isNotEmpty(this.errFileObj[2])){
				this.ifErrFileBnt = Boolean.TRUE;
			}
		}
		RequestContext.getCurrentInstance().update("tabView:mainForm:buttonsPanel");
	}

	/**
 	 * <p>方法描述：查询前验证</p>
 	 * @MethodAuthor qrr,2021年5月12日,validateSearch
	 * */
	private boolean validateSearch() {
		boolean flag = true;
		if(DateUtils.isDateAfter(searchBhkBdate, searchBhkEdate)){
			JsfUtil.addErrorMessage("体检开始日期应小于等于体检结束日期！");
			flag = false;
		}
		if(DateUtils.isDateAfter(searchRcvBdate, searchRcvEdate)){
			JsfUtil.addErrorMessage("接收开始日期应小于等于接收结束日期！");
			flag = false;
		}
		return flag;
	}

	@Override
	public String[] buildHqls() {
		StringBuffer sql = new StringBuffer();
		sql.append(initSql());
        String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")AA  ORDER BY AA.revDate,AA.ZONE_GB, AA.CRPT_NAME, AA.PERSON_NAME ";
        return new String[] { h1, h2 };
	}
	/**
	 *  <p>方法描述：sql初始化</p>
	 * @MethodAuthor hsj
	 */
	private StringBuffer initSql() {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT * FROM (");
		this.buildDataSql(sql);
		this.buildNormalSqlParams(sql,"1");
		//异常情况 --存在异常情况是UNION ALL
		if(StringUtils.isNotBlank(this.searchAbnormals)){
			sql.append(" UNION ");
			this.buildDataSql(sql);
			this.buildNormalSqlParams(sql,"2");
		}
		sql.append(")A where 1=1 ");
		this.buildOtSqlParams(sql);
		return  sql;
	}

	/**
	 *  <p>方法描述：查询列表返回值</p>
	 * @MethodAuthor hsj 2024-12-25 9:15
	 */
	private void buildDataSql(StringBuffer sql) {
		sql.append(" select T.RID,  CASE WHEN T6.ZONE_TYPE > 2 THEN SUBSTR(T6.FULL_NAME, INSTR(T6.FULL_NAME, '_') + 1) ELSE T6.FULL_NAME END ZONE_NAME,");
		sql.append(" T5.CRPT_NAME, T.PERSON_NAME,T.IDC,T2.CODE_NAME AS onguard,T.JC_TYPE,T.IF_RHK,T.IF_ABNOMAL,T3.UNIT_NAME,T.BHK_DATE, ");
		sql.append(" T.STATE,T4.ZONE_GB,T6.if_city_direct,T6.if_prov_direct,0 AS IFCHECK,0 AS limiteDate, ");
		//接收日期
		if("2".equals(checkLevel)){//2级审核
			if (zoneType <= 3) {//终审
				/*省直属情况*/
				sql.append(" CASE ");
				sql.append(" WHEN T.STATE < 4 THEN NULL ");
				sql.append(" WHEN T6.if_prov_direct = 1 THEN T.DEAL_COMPLETE_DATE ");
				sql.append(" ELSE T.COUNTY_SMT_DATE END AS revDate ");
			}else {//初审
				sql.append(" T.DEAL_COMPLETE_DATE AS revDate ");
			}
		}else if ("3".equals(checkLevel)) {//3级审核
			if (zoneType == 2) {//终审
				sql.append(" CASE WHEN T.STATE < 4 THEN NULL WHEN T6.if_city_direct = 1 THEN T.COUNTY_SMT_DATE ELSE T.CITY_SMT_DATE END AS revDate");
			}else if (zoneType == 3){//复审
				/*市直属情况*/
				sql.append(" CASE WHEN T6.if_city_direct = 1 THEN T.DEAL_COMPLETE_DATE ");
				sql.append(" WHEN T.STATE < 2 THEN NULL ");
				sql.append(" ELSE T.COUNTY_SMT_DATE END AS revDate ");
			}else {//初审
				sql.append(" T.DEAL_COMPLETE_DATE AS revDate ");
			}
		}
		sql.append(" , '' stateStr,'' AS backUpOper ");
		sql.append(" FROM TD_TJ_BHK T ");
		sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID ");
		sql.append(" LEFT JOIN TB_TJ_CRPT T5 ON T5.RID = T.ENTRUST_CRPT_ID ");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.ONGUARD_STATEID ");
		sql.append(" LEFT JOIN TB_TJ_SRVORG T3 ON T3.RID = T.BHKORG_ID ");
		sql.append(" LEFT JOIN TS_ZONE T4 ON T4.RID = T1.ZONE_ID ");
		sql.append(" LEFT JOIN TS_ZONE T6 ON T6.RID = T5.ZONE_ID ");
		sql.append(" WHERE 1=1 AND T.IF_INTO_CHECK =1 ");
	}

	private void buildOtSqlParams(StringBuffer sql){
		// 接收日期
		if (null != this.searchRcvBdate) {
			sql.append(" AND revDate >= TO_DATE('").append(DateUtils.formatDate(this.searchRcvBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
		}
		if (null != this.searchRcvEdate) {
			sql.append(" AND revDate <= TO_DATE('").append(DateUtils.formatDate(this.searchRcvEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
	}

	private void buildNormalSqlParams (StringBuffer sql,String type) {
		if (StringUtils.isNotBlank(this.searchUnitId) ) {
			sql.append(" AND T.BHKORG_ID IN (").append(this.searchUnitId).append(")");
		}
		//用工单位地区
		if (StringUtils.isNotBlank(this.searchZoneCodeEmp)) {
			sql.append(" AND T6.ZONE_GB LIKE :zonecodeEmp escape '\\\'");
			this.paramMap.put("zonecodeEmp", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCodeEmp).trim()) + "%");
		}
		//用工单位企业名称
		if (StringUtils.isNotBlank(this.searchCrptNameEmp)) {
			sql.append(" AND T5.CRPT_NAME LIKE :crptNameEmp  escape '\\\'");
			this.paramMap.put("crptNameEmp", "%" + StringUtils.convertBFH(this.searchCrptNameEmp.trim()) + "%");
		}
		//用工单位社会信用代码
		if (StringUtils.isNotBlank(this.searchCreditCodeEmp)) {
			sql.append(" AND T5.INSTITUTION_CODE = :creditCodeEmp");
			this.paramMap.put("creditCodeEmp", this.searchCreditCodeEmp);
		}
		//用人单位地区
		if (StringUtils.isNotBlank(this.searchZoneCode)) {
			sql.append(" AND T4.ZONE_GB LIKE :zonecode escape '\\\'");
			this.paramMap.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()) + "%");
		}
		//用人单位企业名称
		if (StringUtils.isNotBlank(this.searchCrptName)) {
			sql.append(" AND T1.CRPT_NAME LIKE :crptName  escape '\\\'");
			this.paramMap.put("crptName", "%" + StringUtils.convertBFH(this.searchCrptName.trim()) + "%");
		}
		//用人单位社会信用代码
		if (StringUtils.isNotBlank(this.searchCreditCode)) {
			sql.append(" AND T1.INSTITUTION_CODE = :creditCode");
			this.paramMap.put("creditCode", this.searchCreditCode);
		}
		//人员姓名
		if (StringUtils.isNotBlank(this.searchPersonName)) {
			sql.append(" AND T.PERSON_NAME LIKE :personName  escape '\\\'");
			this.paramMap.put("personName", "%" + StringUtils.convertBFH(this.searchPersonName.trim()) + "%");
		}
		//人员身份证
		if (StringUtils.isNotBlank(this.searchIdc)) {
			sql.append(" AND T.IDC  = :personIdc ");
			this.paramMap.put("personIdc", StringUtils.convertBFH(this.searchIdc.trim()));
		}

		// 体检日期
		if (null != this.searchBhkBdate) {
			sql.append(" AND T.BHK_DATE >= TO_DATE('").append(DateUtils.formatDate(this.searchBhkBdate)).append(" 00:00:00','YYYY-MM-DD HH24:MI:SS')");
		}
		if (null != this.searchBhkEdate) {
			sql.append(" AND T.BHK_DATE <= TO_DATE('").append(DateUtils.formatDate(this.searchBhkEdate)).append(" 23:59:59','YYYY-MM-DD HH24:MI:SS')");
		}
		//在岗状态
		if(StringUtils.isNotBlank(selectOnGuardIds)){
			sql.append(" AND T.ONGUARD_STATEID in (").append(this.selectOnGuardIds).append(")");
		}
		//危害因素/单危害因素体检结论
		if(StringUtils.isNotBlank(selectBadRsnIds) && StringUtils.isNotBlank(searchSelBhkrstIds)){
			sql.append(" AND EXISTS(SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID AND TT.BADRSN_ID IN (").append(this.selectBadRsnIds).append(")");
			sql.append(" AND TT.EXAM_CONCLUSION_ID IN (").append(this.searchSelBhkrstIds).append("))");
		}else if (StringUtils.isNotBlank(selectBadRsnIds)) {
			sql.append(" AND EXISTS(SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID AND TT.BADRSN_ID IN (").append(this.selectBadRsnIds).append("))");
		}else if (StringUtils.isNotBlank(searchSelBhkrstIds)) {
			sql.append(" AND EXISTS(SELECT 1 FROM TD_TJ_BADRSNS TT WHERE TT.BHK_ID = T.RID AND TT.EXAM_CONCLUSION_ID IN (").append(this.searchSelBhkrstIds).append("))");
		}
		// 监测类别
		if(null != jcTypes && jcTypes.length > 0){
			sql.append(" AND T.JC_TYPE in (").append(StringUtils.array2string(jcTypes, ",")).append(")");
		}
		// 是否复检
		if(null != ifRhks && ifRhks.length > 0){
			sql.append(" AND T.IF_RHK in (").append(StringUtils.array2string(ifRhks, ",")).append(")");
		}
		// 是否结论异常
		if(null != ifAbnormals && ifAbnormals.length > 0){
			sql.append(" AND T.IF_ABNOMAL in (").append(StringUtils.array2string(ifAbnormals, ",")).append(")");
		}
		//异常情况
		if(StringUtils.isNotBlank(this.searchAbnormals)){
			if("1".equals(type)){
				sql.append(" AND T.LACK_MSG  LIKE :abnormals  escape '\\\'");
				this.paramMap.put("abnormals", "%" + StringUtils.convertBFH(this.searchAbnormals.trim()) + "%");
			}else{
				sql.append(" AND  EXISTS( SELECT 1 FROM TD_TJ_BHK_ABNOMAL abnomal WHERE abnomal.BHK_ID = T.RID AND abnomal.ABNOMAL_INFO LIKE :abnormal  escape '\\\'").append(")");
				this.paramMap.put("abnormal", "%" + StringUtils.convertBFH(this.searchAbnormals.trim()) + "%");
			}
		}
		// 状态
		sql.append(" AND (").append(pakStatusSearch()).append(") ");
		//体检类型
		if (null!=this.searchBhkType && this.searchBhkType.length>0) {
			StringBuffer bhkType = new StringBuffer();
			for (String s : searchBhkType) {
				bhkType.append(",").append(s);
			}
			sql.append(" AND T.BHK_TYPE IN (").append(bhkType.substring(1)).append(")");
		}
		//报告日期
		if ( null != this.startCreateDate) {
			sql.append(" AND T.CREATE_DATE >= TO_DATE('").append(DateUtils.formatDate(startCreateDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
		}
		if ( null != this.endCreateDate) {
			sql.append(" AND T.CREATE_DATE <= TO_DATE('").append(DateUtils.formatDate(endCreateDate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
		//报告出具日期
		if ( null != this.startRptPrintDate) {
			sql.append(" AND T.RPT_PRINT_DATE >= TO_DATE('").append(DateUtils.formatDate(startRptPrintDate, "yyyy-MM-dd")).append("','yyyy-MM-dd')");
		}
		if ( null != this.endRptPrintDate) {
			sql.append(" AND T.RPT_PRINT_DATE <= TO_DATE('").append(DateUtils.formatDate(endRptPrintDate, "yyyy-MM-dd")).append(" 23:59:59','yyyy-MM-dd HH24:mi:ss')");
		}
	}

	private String pakStatusSearch() {
		List<String> statusList = new ArrayList<>();
		StringBuilder sb1 = new StringBuilder();
		StringBuilder sb2 = new StringBuilder();
		StringBuilder sb3 = new StringBuilder();

		if (this.allCheckParamFlag) {
			if (new Integer(4).equals(this.zoneType)) {
				statusList.add("1");
			} else if (new Integer(3).equals(this.zoneType)) {
				statusList.add("3");
			} else if (new Integer(2).equals(this.zoneType)) {
				statusList.add("5");
			}
		} else if (ObjectUtil.isNotEmpty(this.states)) {
			statusList.addAll(Arrays.asList(this.states));
		} else {
			for (SelectItem item : this.stateList) {
				statusList.add(StringUtils.objectToString(item.getValue()));
			}
		}
		for (String status : statusList) {
			if ("0".equals(status)) {
				if (this.checkLevel2) {
					sb1.append(", 0");
				} else {
					sb2.append(" OR (T.STATE = 0 AND NVL(T6.IF_CITY_DIRECT, 0) = 0)");
				}
			} else if ("1".equals(status)) {
				if (this.checkLevel2) {
					sb1.append(", 1");
				} else {
					sb2.append(" OR (T.STATE = 1 AND NVL(T6.IF_CITY_DIRECT, 0) = 0)");
				}
			} else if ("2".equals(status)) {
				sb1.append(", 2");
				sb2.append(" OR (T.STATE = 0 AND NVL(T6.IF_CITY_DIRECT, 0) = 1)");
			} else if ("3".equals(status)) {
				sb1.append(", 3");
				sb2.append(" OR (T.STATE = 1 AND NVL(T6.IF_CITY_DIRECT, 0) = 1)");
			} else {
				sb1.append(", ").append(status);
			}
		}
		if (ObjectUtil.isNotEmpty(sb1)) {
			sb3.append(" T.STATE IN (").append(sb1.substring(2)).append(")");
		}
		if (ObjectUtil.isNotEmpty(sb2)) {
			sb3.append(ObjectUtil.isEmpty(sb3) ? sb2.substring(4) : sb2);
		}
		return sb3.toString();
	}

	@Override
	public void processData(List<?> list) {
		 if (null!=list && list.size()>0) {
			 Map<Date,Integer> limitMap = new HashMap<>();
             List<Object[]> result = (List<Object[]>) list;
             for (Object[] obj : result) {
            	 if (null!=obj[4]) {//身份证号加密
            		 obj[4] = StringUtils.encryptIdc(obj[4].toString());
            	 }
				 int status = 1;
				 if (ObjectUtil.isNotEmpty(obj[11])) {
					 status = Integer.parseInt(StringUtils.objectToString(obj[11]));
				 }
				 boolean ifCityDirect = "1".equals(StringUtils.objectToString(obj[13]));
				 obj[18] = pakStatusStr(status, ifCityDirect);

				 // 处理期限
				 Date date = ObjectUtil.convert(Date.class, obj[17], null);
				 if (ObjectUtil.isNotEmpty(date)) {
					 Integer daysRemaining = this.daysBeforeTheWorkingDayMap.get(DateUtil.date(DateUtils.getDateOnly(date)));
					 if (daysRemaining == null) {
						 obj[16] = 0;
					 } else {
						 obj[16] = daysRemaining;
					 }
				 }

				 if (new Integer(4).equals(this.zoneType)) {
					 obj[15] = status == 1 && ((this.checkLevel3 && !ifCityDirect) || (this.checkLevel2)) ? 1 : 0;
				 } else if (new Integer(3).equals(this.zoneType)) {
					 obj[15] = status == 3 || (status == 1 && this.checkLevel3 && ifCityDirect) ? 1 : 0;
				 } else if (new Integer(2).equals(this.zoneType)) {
					 obj[15] = status == 5 ? 1 : 0;
					 obj[19] = status == 6 ? 1 : 0;
				 }
             }
		 }
	}

	private String pakStatusStr(int status, boolean ifCityDirect) {
		switch (status) {
			case 0:
				if (this.checkLevel2 || !ifCityDirect) {
					return "区县级退回";
				} else {
					return "市级退回";
				}
			case 1:
				if (this.checkLevel2 || !ifCityDirect) {
					return "区县级待审";
				} else {
					return "市级待审";
				}
			case 2:
				return "市级退回";
			case 3:
				return "市级待审";
			case 4:
				return "省级退回";
			case 5:
				return "省级待审";
			case 6:
				return "省级通过";
			case 7:
				return "国家退回";
			default:
				return "";
		}
	}

	/**
	 * <p>方法描述：计算超期</p>
	 * @MethodAuthor rcj,2020年10月16日,calLimitTime
	 * */
	@Deprecated
	protected int calLimitTime(Date sDate) {
		if (null==sDate) {
			return -1;
		}
		//剩余天数
		int day = HolidayUtil.calRemainingDate(sDate, new Date(),limitTime);
		if (day == 0) {
			return -1;
		}
		return day;
	}

	/**
 	 * <p>方法描述：批量审核</p>
 	 * @MethodAuthor qrr,2021年5月12日,openReviewConfirmDialog
	 * */
	public void openReviewConfirmDialog() {
		//默认通过
		this.checkState=1;
		if(null != defaultAuditAdv){
			this.checkRst = defaultAuditAdv;
		}else{
			this.checkRst = null;
		}
		if("1".equals(checkType)){
			//批量审核
			if(this.selectEntitys == null || this.selectEntitys.size()==0){
				JsfUtil.addErrorMessage("请选择需要审批的数据！");
				return ;
			}
		}

		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null == tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		BigDecimal num = this.bgkFlowService.findAllCheckTaskAndCheckNum(tsZone.getZoneGb());
		if (new BigDecimal(-1).equals(num)) {
			JsfUtil.addErrorMessage("存在正在审核的数据，请耐心等待！");
			return;
		} else if (num != null) {
			JsfUtil.addErrorMessage("存在" + num + "条正在审核的数据，请耐心等待！");
			return;
		}

        RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').show()");
		RequestContext.getCurrentInstance().update("tabView:mainForm:reviewConfirmDialog");
	}
	@Override
	public void addInit() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void viewInit() {

	}

	@Override
	public void modInit() {
		//TODO 赋值 checkState checkRst = defaultAuditAdv
	}

	/**
	 * @Description : 批量审核
	 * @MethodAuthor: anjing
	 * @Date : 2020/7/22 18:32
	 **/
	public void reviewBatchAction() {
		if (null == this.checkState) {
			JsfUtil.addErrorMessage("审核结果不能为空！");
			return;
		}

		if (StringUtils.isBlank(this.checkRst)) {
			JsfUtil.addErrorMessage("审核意见不能为空！");
			return;
		}

		if (this.checkRst.length() > 200) {
			JsfUtil.addErrorMessage("审核意见内容超出长度限制！");
			return;
		}
		// 全部审核
		if ("2".equals(this.checkType)) {
			try {
				pakAndSaveAllCheckTask();
				JsfUtil.addSuccessMessage("审核中，请耐心等待！");
				RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').hide();datatableOffClick();");
			} catch (Exception e) {
				e.printStackTrace();
				JsfUtil.addErrorMessage("审核失败！");
			}
			return;
		} else if (!"1".equals(this.checkType)) {
			return;
		}

		//是否审核通过
		boolean approved = this.checkState == 1;
		//存储批量审核的数据
		List<Object[]> list = this.selectEntitys;

		if (ObjectUtil.isEmpty(list)) {
			return;
		}

		//key: 上次流程操作标识; value: 体检记录RID;
		Map<Integer, List<Integer>> lastMarkBhkRidMap = new HashMap<>();
		//key: 当前流程操作标识; value: 体检记录RID;
		Map<Integer, List<Integer>> currentMarkBhkRidMap = new HashMap<>();

		List<Map<String, Object>> markBhkRidMapList = new ArrayList<>();
		List<List<Integer>> bhkRidMapList = new ArrayList<>();

		//三级审核 初审方式
		bhkRidMapList.add(new ArrayList<Integer>());
		Map<String, Object> bhkField0 = new HashMap<>(16);
		bhkField0.put("STATE", approved ? 3 : 0);
		bhkField0.put("COUNTY_CHECK_WAY", 2);
		bhkField0.put("COUNTY_RST", checkState);
		bhkField0.put("COUNTY_AUDIT_ADV", checkRst);
		bhkField0.put("COUNTY_CHK_ORGID", Global.getUser().getTsUnit().getRid());
		bhkField0.put("COUNTY_SMT_DATE", new Date());
		markBhkRidMapList.add(bhkField0);

		//市直属 初审方式
		bhkRidMapList.add(new ArrayList<Integer>());
		Map<String, Object> bhkField1 = new HashMap<>(16);
		bhkField1.put("STATE", approved ? 5 : 0);
		bhkField1.put("COUNTY_CHECK_WAY", 2);
		bhkField1.put("COUNTY_RST", checkState);
		bhkField1.put("COUNTY_AUDIT_ADV", checkRst);
		bhkField1.put("COUNTY_CHK_ORGID", Global.getUser().getTsUnit().getRid());
		bhkField1.put("COUNTY_SMT_DATE", new Date());
		markBhkRidMapList.add(bhkField1);

		//非市直属 复审方式
		bhkRidMapList.add(new ArrayList<Integer>());
		Map<String, Object> bhkField2 = new HashMap<>(16);
		bhkField2.put("STATE", approved ? 5 : 2);
		bhkField2.put("CITY_RST", checkState);
		bhkField2.put("CITY_AUDIT_ADV", checkRst);
		bhkField2.put("CIYT_CHK_ORGID", Global.getUser().getTsUnit().getRid());
		bhkField2.put("CITY_SMT_DATE", new Date());
		bhkField2.put("CITY_CHECK_WAY", 2);
		markBhkRidMapList.add(bhkField2);

		// 省级操作终审结果
		bhkRidMapList.add(new ArrayList<Integer>());
		Map<String, Object> bhkField3 = new HashMap<>(16);
		bhkField3.put("STATE", approved ? 6 : 4);
		bhkField3.put("CITY_RST2", checkState);
		bhkField3.put("PRO_AUDIT_ADV", checkRst);
		bhkField3.put("PRO_CHK_ORGID", Global.getUser().getTsUnit().getRid());
		bhkField3.put("PRO_SMT_DATE", new Date());
		bhkField3.put("PRO_CHK_PSNID", Global.getUser().getRid());
		bhkField3.put("PRO_CHECK_WAY", 2);
		markBhkRidMapList.add(bhkField3);

		List<Integer> ridList = new ArrayList<>();
		for (Object[] objects : list) {
			Integer rid = ObjectUtil.convert(Integer.class, objects[0], null);
			if (rid == null) {
				continue;
			}
			ridList.add(rid);
		}
		// 体检数据最新状态映射 key: RID value: 状态
		Map<Integer, Integer> bhkStateMap = this.bgkFlowService.findBhkDataCheckState(ridList);

		for (Object[] obj : list) {
			Integer bhkRid = ObjectUtil.convert(Integer.class, obj[0], null);
			boolean cityDirect = "1".equals(StringUtils.objectToString(obj[13]));
            if (bhkRid == null || bhkStateMap.get(bhkRid) == null) {
                continue;
            }
			// 终审通过
			boolean theFinalReviewWasPassed = false;
			// 可被审核
			boolean canBeAudited = false;
			Integer status = bhkStateMap.get(bhkRid);
			if (new Integer(4).equals(this.zoneType)) {
				canBeAudited = status == 1 && ((this.checkLevel3 && !cityDirect) || (this.checkLevel2));
			} else if (new Integer(3).equals(this.zoneType)) {
				canBeAudited = status == 3 || (status == 1 && this.checkLevel3 && cityDirect);
			} else if (new Integer(2).equals(this.zoneType)) {
				canBeAudited = status == 5 || status == 6;
				theFinalReviewWasPassed = status == 6;
			}
			// 不可被审核的数据直接跳过
            if (!canBeAudited) {
                continue;
            }
			//终审通过的通过 不需要处理
			if(approved && theFinalReviewWasPassed){
				continue;
			}
			try {
				boolean provinceDirect = "1".equals(StringUtils.objectToString(obj[14]));
				int lastMark;
				int currentMark;
				if ("3".equals(this.checkLevel)) {
					if (this.zoneType > 3) {
						//初审-区级
						lastMark = 21;
						currentMark = approved ? 31 : 11;
						bhkRidMapList.get(0).add(bhkRid);
					} else if (this.zoneType == 3) {
						if (cityDirect) {
							//初审-市级
							lastMark = 33;
							currentMark = approved ? 41 : 13;
							bhkRidMapList.get(1).add(bhkRid);
						} else {
							//复审-市级
							lastMark = 31;
							currentMark = approved ? 41 : 22;
							bhkRidMapList.get(2).add(bhkRid);
						}
					} else {
						//终审-省级
						lastMark = 41;
						currentMark = approved ? 42 : 32;
						bhkRidMapList.get(3).add(bhkRid);
					}
				} else if ("2".equals(this.checkLevel)) {
					if (this.zoneType > 3) {
						//初审-区级
						lastMark = 21;
						currentMark = approved ? 43 : 11;
						bhkRidMapList.get(1).add(bhkRid);
					} else {
						//终审-市/省级
						currentMark = approved ? 42 : 32;
						if (provinceDirect) {
							//省直
							lastMark = 44;
						} else {
							//非省直
							lastMark = 43;
						}
						bhkRidMapList.get(3).add(bhkRid);
					}
				} else {
					return;
				}

				//终审通过的 需要退回
				boolean flag = !approved && theFinalReviewWasPassed;
				if(!flag){
					if (!lastMarkBhkRidMap.containsKey(lastMark)) {
						lastMarkBhkRidMap.put(lastMark, new ArrayList<Integer>());
					}
					lastMarkBhkRidMap.get(lastMark).add(bhkRid);
				}
				if (!currentMarkBhkRidMap.containsKey(currentMark)) {
					currentMarkBhkRidMap.put(currentMark, new ArrayList<Integer>());
				}
				currentMarkBhkRidMap.get(currentMark).add(bhkRid);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		try {
			// 保存插入操作
			// 根据markBhkRidMapList&bhkRidMapList更新BHK;
			// 根据bhkRidTimelyMap更新上一次流程 OPER_DATE OPER_PSN_ID IF_IN_TIME;
			// 根据currentMarkBhkRidMap新增当前流程记录.
			int resultNumber = this.bgkFlowService.reviewBatchOperation(
					lastMarkBhkRidMap, markBhkRidMapList, bhkRidMapList, currentMarkBhkRidMap, this.checkRst, this.minDaysBeforeTheWorkingDayDate
			);
			JsfUtil.addSuccessMessage("审核成功"+resultNumber+"条！");
			RequestContext.getCurrentInstance().execute("PF('ReviewConfirmDialog').hide();datatableOffClick();");
			this.searchAction();
			RequestContext.getCurrentInstance().update("tabView:mainForm:dataTable");
		} catch (Exception e) {
			JsfUtil.addErrorMessage("审核失败！");
			e.printStackTrace();
		}
	}

	/**
	 * 封装并存储全部审核任务
	 */
	public void pakAndSaveAllCheckTask() {
		TdTjCheckTask task = new TdTjCheckTask();
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null == tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		task.setFkByZoneId(new TsZone(tsZone.getRid()));
		task.setExportCondition(JSONObject.toJSONString(genBhkAllCheckJson()));
		task.setFkByCheckUnitId(new TsUnit(Global.getUser().getTsUnit().getRid()));
		task.setFkByCheckRsnId(new TsUserInfo(Global.getUser().getRid()));
		task.setCheckDate(new Date());
		task.setCheckRst(this.checkState);
		task.setCheckAdv(this.checkRst);
		task.setState(0);
		this.bgkFlowService.saveTdTjCheckTask(task);
	}

	/**
	 * 生成全部审核参数JSON
	 *
	 * @return BhkAllCheckJson JSON
	 */
	public BhkAllCheckJson genBhkAllCheckJson() {
		BhkAllCheckJson json = new BhkAllCheckJson();
	    json.setCheckLevel(this.checkLevel);
	    json.setZoneType(StringUtils.objectToString(this.zoneType));
		json.setUsername(Global.getUser().getUsername());
		if (ObjectUtil.isNotEmpty(this.searchZoneCodeEmp)) {
			json.setSearchZoneCodeEmp(StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCodeEmp).trim()));
		}
		if (ObjectUtil.isNotEmpty(this.searchCrptNameEmp)) {
			json.setSearchCrptNameEmp(StringUtils.convertBFH(this.searchCrptNameEmp.trim()));
		}
		if (ObjectUtil.isNotEmpty(this.searchCreditCodeEmp)) {
			json.setSearchCreditCodeEmp(this.searchCreditCodeEmp);
		}
		if (ObjectUtil.isNotEmpty(this.searchZoneCode)) {
			json.setSearchZoneCode(StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()));
		}
		if (ObjectUtil.isNotEmpty(this.searchCrptName)) {
			json.setSearchCrptName(StringUtils.convertBFH(this.searchCrptName.trim()));
		}
		if (ObjectUtil.isNotEmpty(this.searchCreditCode)) {
			json.setSearchCreditCode(this.searchCreditCode);
		}
		if (ObjectUtil.isNotEmpty(this.searchIdc)) {
			json.setSearchIdc(StringUtils.convertBFH(this.searchIdc.trim()));
		}
		if (ObjectUtil.isNotEmpty(this.searchPersonName)) {
			json.setSearchPersonName(StringUtils.convertBFH(this.searchPersonName.trim()));
		}
		if (ObjectUtil.isNotEmpty(this.searchBhkType)) {
			json.setSearchBhkType(StringUtils.array2string(this.searchBhkType, ","));
		}
		if (ObjectUtil.isNotEmpty(this.searchBhkBdate)) {
			json.setSearchBhkBdate(this.searchBhkBdate);
		}
		if (ObjectUtil.isNotEmpty(this.searchBhkEdate)) {
			json.setSearchBhkEdate(DateUtils.getDayLastTime(this.searchBhkEdate));
		}
		if (ObjectUtil.isNotEmpty(this.startCreateDate)) {
			json.setStartCreateDate(this.startCreateDate);
		}
		if (ObjectUtil.isNotEmpty(this.endCreateDate)) {
			json.setEndCreateDate(DateUtils.getDayLastTime(this.endCreateDate));
		}
		if (ObjectUtil.isNotEmpty(this.startRptPrintDate)) {
			json.setStartRptPrintDate(this.startRptPrintDate);
		}
		if (ObjectUtil.isNotEmpty(this.endRptPrintDate)) {
			json.setEndRptPrintDate(DateUtils.getDayLastTime(this.endRptPrintDate));
		}
		if (ObjectUtil.isNotEmpty(this.selectOnGuardIds)) {
			json.setSelectOnGuardIds(this.selectOnGuardIds);
		}
		if (ObjectUtil.isNotEmpty(this.selectBadRsnIds)) {
			json.setSelectBadRsnIds(this.selectBadRsnIds);
		}
		if (StringUtils.isNotBlank(this.searchSelBhkrstIds)) {
			json.setSearchSelBhkrstIds(this.searchSelBhkrstIds);
		}
		if (ObjectUtil.isNotEmpty(this.searchRcvBdate)) {
			json.setSearchRcvBdate(this.searchRcvBdate);
		}
		if (ObjectUtil.isNotEmpty(this.searchRcvEdate)) {
			json.setSearchRcvEdate(DateUtils.getDayLastTime(this.searchRcvEdate));
		}
		if (ObjectUtil.isNotEmpty(this.jcTypes)) {
			json.setJcTypes(StringUtils.array2string(this.jcTypes, ","));
		}
		if (ObjectUtil.isNotEmpty(this.ifRhks)) {
			json.setIfRhks(StringUtils.array2string(this.ifRhks, ","));
		}
		if (ObjectUtil.isNotEmpty(this.ifAbnormals)) {
			json.setIfAbnormals(StringUtils.array2string(this.ifAbnormals, ","));
		}
		if (StringUtils.isNotBlank(this.searchAbnormals)) {
			json.setSearchAbnormals(this.searchAbnormals);
		}
		if (ObjectUtil.isNotEmpty(this.searchUnitId)) {
			json.setSearchUnitId(this.searchUnitId);
		}
	    json.setLimitDate(this.minDaysBeforeTheWorkingDayDate);
		return json;
	}

	/**
	 * <p>方法描述： 状态撤销   </p>
	 * @MethodAuthor  yzz，2021-05-13，revokeCheck
	 **/
	public void revokeCheck(){
		//获取状态
		Integer status=tdTjBhk.getState(); Integer upState=null;
		TsZone tsZone = tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId();
		//记录撤销时需要删除的操作标识
		Integer previousOperFlag=null;
		//记录撤销时需要删除的记录（21,33,44）或者需要更新字段记录
		Integer lastOperFlag=null;
		if("3".equals(checkLevel)){
			if(status==3){
				upState=1;
				previousOperFlag=31;
				lastOperFlag=21;
			}else if(status==5){
				/*市直属*/
				if("1".equals(tsZone.getIfCityDirect())){
					upState=1;
					lastOperFlag=33;
				}else{
					upState=3;
					lastOperFlag=31;
				}
				previousOperFlag=41;
			}else if(status==6){
				upState=5;
				previousOperFlag=42;
				lastOperFlag=41;
			}
		}else if("2".equals(checkLevel)){
			if(status==5){
				upState=1;
				previousOperFlag=43;
				lastOperFlag=21;
			}else if(status==6){
				//包含省直属
				upState=5;
				previousOperFlag=42;
				if("1".equals(tsZone.getIfProvDirect())){
					lastOperFlag=44;
				}else{
					lastOperFlag=43;
				}
			}
		}
		//更新对象
		hethStaQueryCommService.updateTdTjBhkStateByRid(upState,tdTjBhk.getRid(),previousOperFlag,lastOperFlag);
		this.modView("1","0");
		JsfUtil.addSuccessMessage("撤销成功！");

	}


	/**
	 * @Description: 审核以及详情公共查询部分
	 *
	 * @MethodAuthor pw,2021年05月13日
	 */
	public void modView(String type,String ifNext){
		tdTjBhk = this.hethStaQueryCommService.findTdTjBhkById(rid);
		if(null == type || null == tdTjBhk){
			JsfUtil.addErrorMessage("未获取到数据");
			return;
		}
        tjBhkInfoBean.setIfShowBaseOnly(false);
        tjBhkInfoBean.setRid(rid);
        tjBhkInfoBean.setArchivePanel(archivePanel);
        tjBhkInfoBean.setIfManagedOrg(false);
        tjBhkInfoBean.initBhkInfo();

		boolean ifCityDirect = null != tdTjBhk.getFkByEntrustCrptId() && null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId() &&
				null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfCityDirect() &&
				!"0".equals(tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfCityDirect());
		boolean ifProvDirect = null != tdTjBhk.getFkByEntrustCrptId() && null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId() &&
				null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfProvDirect() &&
				!"0".equals(tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfProvDirect());
		initShowFlag(type, ifCityDirect, ifProvDirect);
		initCheckStateAndRst(ifCityDirect, ifProvDirect);
		//撤销状态置为false
		this.cancelFlag = Boolean.FALSE;
		//异常情况，用人单位信息以及体检信息区域数据初始化
		this.unAbnormalsList = hethBaseCommService.initUnAbnormalsList(this.tdTjBhk);
		if("1".equals(type)){
			editFlag = true;
			if(null != defaultAuditAdv && null != checkState && 1 == checkState && StringUtils.isBlank(checkRst)){
				checkRst = defaultAuditAdv;
			}
		}else{
			editFlag = false;
			//详情界面中 判断撤销按钮显示
			if("3".equals(checkLevel)){
				if(this.zoneType>3){//区级
					if(tdTjBhk.getState()==3){
						this.cancelFlag=true;
					}
				}else if(this.zoneType==3){//市级
					if(tdTjBhk.getState()==5){
						this.cancelFlag=true;
					}
				}else{
					if(tdTjBhk.getState()==6){
						this.cancelFlag=true;
					}
				}
			}else if("2".equals(checkLevel)){
				if(this.zoneType>3){//区级
					//待终审状态 显示撤销
					if(tdTjBhk.getState()==5){
						this.cancelFlag=true;
					}
				}else{
					//市/省级
					if(tdTjBhk.getState()==6){
						this.cancelFlag=true;
					}
				}
			}
		}
		//获取历次审核
		historyList = hethBaseCommService.findHisotryList(this.tdTjBhk.getBhkCode(), this.tdTjBhk.getTbTjSrvorg().getRid(),9);
		//初始化历次审核意见
		initHistoryCheckout();
		//下一次按钮是否显示
		nextFlag = Boolean.FALSE;
		if("1".equals(ifNext)){
			if(!CollectionUtils.isEmpty(nextList)){
				nextRid = nextList.get(0);
				nextFlag = Boolean.TRUE;
			}
		}
		this.tipInfo = "";
		if(tdTjBhk.getTbTjSrvorg()!=null && tdTjBhk.getTbTjSrvorg().getTsUnit()!=null){
			TsUnit tsUnit = tdTjBhk.getTbTjSrvorg().getTsUnit();
			try {
				List<Object[]> list = bgkFlowService.selectTdZwTjorginfoByOrgId(tsUnit.getRid());
				if(list != null && list.size() > 0){
					Object[] object = list.get(0);
					tipInfo = "体检机构："+StringUtils.objectToString(object[1])+"　｜　联系人："+StringUtils.objectToString(object[2])+"　｜　联系电话："+StringUtils.objectToString(object[3])+"　";
				}
			}catch (Exception e) {
				e.printStackTrace();
			}
		}
		if(tdTjBhk.getSubmitChangedate()!=null){
			tipInfo+=(StringUtils.isNotBlank(tipInfo)?"|　":"")+"最新提交时间："+DateUtils.formatDate(tdTjBhk.getSubmitChangedate(),"yyyy-MM-dd HH:mm:ss");
		}
        this.forwardEditPage();
	}
	/**
	 *  <p>方法描述：初始化历次审核意见</p>
	 * @MethodAuthor hsj
	 */
	private void initHistoryCheckout() {
		if(!CollectionUtils.isEmpty(historyList)){
			List<Object[]> checkOutList = new ArrayList<>();
			for(Object[] objArr:historyList){
				Integer opegFlag = null == objArr[7] ? 0 : Integer.parseInt(objArr[7].toString()) ;
				String type = null;
				//审核级别为3
				if("3".equals(checkLevel)){
					Integer ifCityDirect =  null == objArr[5] ? null : Integer.parseInt(objArr[5].toString()) ;
					//根据是否市直属判断审核类型
					if(Integer.valueOf(1).equals(ifCityDirect)){
						switch (opegFlag){
							case 41:
								type = "市级审核通过";
								break;
							case 13:
								type = "市级审核退回";
								break;
							case 42:
								type = "省级审核通过";
								break;
							case 32:
								type = "省级审核退回";
								break;
							default:
								break;
						}
					}else{
						switch (opegFlag) {
							case 31:
								type = "区县级审核通过";
								break;
							case 11:
								type = "区县级审核退回";
								break;
							case 41:
								type = "市级审核通过";
								break;
							case 22:
								type = "市级审核退回";
								break;
							case 42:
								type = "省级审核通过";
								break;
							case 32:
								type = "省级审核退回";
								break;
							default:
								break;
						}
					}
				}
				//审核级别为2
				if("2".equals(checkLevel)){
					Integer ifProvDirect =  null == objArr[6] ? null : Integer.parseInt(objArr[6].toString()) ;
					//根据是否省直属判断审核类型
					if(Integer.valueOf(1).equals(ifProvDirect)){
						switch (opegFlag){
							case 42:
								type = "省级审核通过";
								break;
							case 32:
								type = "省级审核退回";
								break;
							default:
								break;
						}
					}else{
						switch (opegFlag) {
							case 43:
								type = "区县级审核通过";
								break;
							case 11:
								type = "区县级审核退回";
								break;
							case 42:
								type = "省级审核通过";
								break;
							case 32:
								type = "省级审核退回";
								break;
							default:
								break;
						}
					}
				}
				objArr[4] = type;
				if(type != null){
					checkOutList.add(objArr);
				}
			}
			historyList = checkOutList;
		}
	}


	/**
	 * @Description: 初始化审核结果与审核意见
	 *
	 * @MethodAuthor pw,2021年05月14日
	 */
	private void initCheckStateAndRst(boolean ifCityDirect, boolean ifProvDirect){
		//审核结果 默认初审结果
		checkState = tdTjBhk.getCountyRst();
		//审核意见 默认初审意见
		checkRst = tdTjBhk.getCountyAuditAdv();
		Integer state = tdTjBhk.getState();
		if ("3".equals(checkLevel)){
			if(zoneType == 3){
				//非市直属 市级操作复审结果
				if(!ifCityDirect){
					checkState = tdTjBhk.getCityRst();
					checkRst = tdTjBhk.getCityAuditAdv();
				}
			}else if(zoneType == 2){
				//省级操作终审结果
				checkState = tdTjBhk.getCityRst2();
				checkRst = tdTjBhk.getProAuditAdv();
			}
		}else if("2".equals(checkLevel)){
			//省直属或者省级单位 操作终审结果
			if(ifProvDirect || zoneType <= 3){
				checkState = tdTjBhk.getCityRst2();
				checkRst = tdTjBhk.getProAuditAdv();
			}
		}
		if(checkState==null){
			checkState=1;
		}
	}


	/**
	 * @Description: 更新审核意见区域是否显示状态
	 *
	 * @MethodAuthor pw,2021年05月14日
	 */
	private void initShowFlag(String type, boolean ifCityDirect, boolean ifProvDirect){
		countyFlag = Boolean.FALSE;
		cityFlag = Boolean.FALSE;
		proFlag = Boolean.FALSE;
		Integer state = tdTjBhk.getState();
		//详情页面
		if(!"1".equals(type)){
			proFlag = Boolean.TRUE;//显示审核意见
		}
		if ("3".equals(checkLevel)){
			if(zoneType == 3){
				if(!ifCityDirect ){
					countyFlag = Boolean.TRUE;//显示初审意见
				}
			}else if(zoneType == 2){
				countyFlag = Boolean.TRUE;//显示初审意见
				if(!ifCityDirect){
					cityFlag = Boolean.TRUE;//显示复审意见
				}
			}
		}else if("2".equals(checkLevel)){
			if(zoneType <= 3 && !ifProvDirect){
				countyFlag = Boolean.TRUE;//显示初审意见
			}
		}
	}


    //审核暂存
	@Override
	public void saveAction() {
		if(StringUtils.isNotBlank(this.checkRst) && this.checkRst.length() > 200){
			JsfUtil.addErrorMessage("审核意见内容超出长度限制！");
			return;
		}
		tdTjBhk.setState(null == tdTjBhk.getState() ? 1 : tdTjBhk.getState());
		boolean ifCityDirect = null != tdTjBhk.getFkByEntrustCrptId() && null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId() &&
				null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfCityDirect() &&
				!"0".equals(tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfCityDirect());
		boolean ifProvDirect = null != tdTjBhk.getFkByEntrustCrptId() && null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId() &&
				null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfProvDirect() &&
				!"0".equals(tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfProvDirect());
		boolean flag = true;
		if ("3".equals(checkLevel)){
			if(zoneType == 3){
				//非市直属 市级操作复检
				if(!ifCityDirect){
					flag = false;
					tdTjBhk.setCityRst(checkState);
					tdTjBhk.setCityAuditAdv(checkRst);
					tdTjBhk.setFkByCiytChkOrgid(Global.getUser().getTsUnit());
					tdTjBhk.setCitySmtDate(new Date());
				}
			}else if(zoneType == 2){
				//省级操作终审结果
				flag = false;
				tdTjBhk.setState(5);
				tdTjBhk.setCityRst2(checkState);
				tdTjBhk.setProAuditAdv(checkRst);
				tdTjBhk.setFkByProChkOrgid(Global.getUser().getTsUnit());
				tdTjBhk.setProSmtDate(new Date());
				tdTjBhk.setFkByProChkPenid(Global.getUser());
			}
		}else if("2".equals(checkLevel)){
			//省直属或者省级单位 操作终审结果
			if(zoneType <= 3){
				flag = false;
				tdTjBhk.setState(5);
				tdTjBhk.setCityRst2(checkState);
				tdTjBhk.setProAuditAdv(checkRst);
				tdTjBhk.setFkByProChkOrgid(Global.getUser().getTsUnit());
				tdTjBhk.setProSmtDate(new Date());
				tdTjBhk.setFkByProChkPenid(Global.getUser());
			}
		}
		if(flag){
			//初审结果
			tdTjBhk.setCountyRst(checkState);
			tdTjBhk.setCountyAuditAdv(checkRst);
			tdTjBhk.setFkByCountyChkOrgid(Global.getUser().getTsUnit());
			tdTjBhk.setCountySmtDate(new Date());
		}
		try{
			this.commService.update(tdTjBhk);
			JsfUtil.addSuccessMessage("暂存成功！");
		}catch(Exception e){
			e.printStackTrace();
			JsfUtil.addErrorMessage("暂存失败！");
		}
	}


	/**
	 * @Description: 审核提交
	 *
	 * @MethodAuthor pw,2021年05月13日
	 */
	public void submitAction(){
		long curTime = System.currentTimeMillis();
		long caculTotal = System.currentTimeMillis();
		Boolean beforeFlag=false;
		if(null == tdTjBhk){
			JsfUtil.addErrorMessage("提交失败！");
			return;
		}
		if(StringUtils.isBlank(checkRst)){
			JsfUtil.addErrorMessage("审核意见不允许为空！");
			return;
		}
		if(this.checkRst.length() > 200){
			JsfUtil.addErrorMessage("审核意见内容超出长度限制！");
			return;
		}
		boolean ifCityDirect = null != tdTjBhk.getFkByEntrustCrptId() && null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId() &&
				null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfCityDirect() &&
				!"0".equals(tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfCityDirect());
		boolean ifProvDirect = null != tdTjBhk.getFkByEntrustCrptId() && null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId() &&
				null != tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfProvDirect() &&
				!"0".equals(tdTjBhk.getFkByEntrustCrptId().getTsZoneByZoneId().getIfProvDirect());

		List<Integer> ridList = new ArrayList<>();
		ridList.add(this.tdTjBhk.getRid());
		// 体检数据最新状态映射 key: RID value: 状态
		Map<Integer, Integer> bhkStateMap = this.bgkFlowService.findBhkDataCheckState(ridList);
		Integer status = bhkStateMap.get(this.tdTjBhk.getRid());
		if (status == null) {
			JsfUtil.addSuccessMessage("该数据已审核！");
			nextDataNew();
			this.modView("0","1");
			return;
		}
		// 可被审核
		boolean canBeAudited = false;
		if (new Integer(4).equals(this.zoneType)) {
			canBeAudited = status == 1 && ((this.checkLevel3 && !ifCityDirect) || (this.checkLevel2));
		} else if (new Integer(3).equals(this.zoneType)) {
			canBeAudited = status == 3 || (status == 1 && this.checkLevel3 && ifCityDirect);
		} else if (new Integer(2).equals(this.zoneType)) {
			canBeAudited = status == 5;
		}
		if (!canBeAudited) {
			JsfUtil.addSuccessMessage("该数据已审核！");
			nextDataNew();
			this.modView("0","1");
			return;
		}

		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null == tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		BigDecimal num = this.bgkFlowService.findAllCheckTaskAndCheckNum(tsZone.getZoneGb());
		if (new BigDecimal(-1).equals(num)) {
			JsfUtil.addErrorMessage("存在正在审核的数据，请耐心等待！");
			return;
		} else if (num != null) {
			JsfUtil.addErrorMessage("存在" + num + "条正在审核的数据，请耐心等待！");
			return;
		}

		boolean flag = true;
		TdZwBgkFlow bgkFlow=new TdZwBgkFlow();
		if ("3".equals(checkLevel)){
			if(zoneType > 3){
				tdTjBhk.setState(1 == checkState ? 3 : 0);
				bgkFlow.setOperFlag(1 == checkState?31:11);
				bgkFlow.setPreviousOperFlag(21);
			}else if(zoneType == 3){
				if(!ifCityDirect){
					flag = false;
					tdTjBhk.setCityRst(checkState);
					tdTjBhk.setCityAuditAdv(checkRst);
					tdTjBhk.setFkByCiytChkOrgid(Global.getUser().getTsUnit());
					tdTjBhk.setCitySmtDate(new Date());
					tdTjBhk.setCityCheckWay(1);
				}
				if(1 == checkState){
					tdTjBhk.setState(5);
				}else if(ifCityDirect){
					tdTjBhk.setState(0);//市直属 初审退回
				}else{
					tdTjBhk.setState(2);//非市直属 复审退回
				}
				if(ifCityDirect){
					bgkFlow.setOperFlag(1 == checkState?41:13);
					bgkFlow.setPreviousOperFlag(33);
				}else{
					bgkFlow.setOperFlag(1 == checkState?41:22);
					bgkFlow.setPreviousOperFlag(31);
				}
			}else if(zoneType == 2){
				//省级操作终审结果
				flag = false;
				tdTjBhk.setCityRst2(checkState);
				tdTjBhk.setProAuditAdv(checkRst);
				tdTjBhk.setFkByProChkOrgid(Global.getUser().getTsUnit());
				tdTjBhk.setState(1 == checkState ? 6 : 4);
				tdTjBhk.setProSmtDate(new Date());
				tdTjBhk.setFkByProChkPenid(Global.getUser());
				tdTjBhk.setProCheckWay(1);
				bgkFlow.setOperFlag(1 == checkState?42:32);
				bgkFlow.setPreviousOperFlag(41);
			}
		}else if("2".equals(checkLevel)){
			//省直属或者市级省级单位
			if(zoneType <= 3){
				flag = false;
				tdTjBhk.setCityRst2(checkState);
				tdTjBhk.setProAuditAdv(checkRst);
				tdTjBhk.setFkByProChkOrgid(Global.getUser().getTsUnit());
				tdTjBhk.setState(1 == checkState ? 6 : 4);
				tdTjBhk.setProSmtDate(new Date());
				tdTjBhk.setFkByProChkPenid(Global.getUser());
				tdTjBhk.setProCheckWay(1);
				bgkFlow.setOperFlag(1 == checkState?42:32);
				if(ifProvDirect){
					bgkFlow.setPreviousOperFlag(44);
				}else{
					bgkFlow.setPreviousOperFlag(43);
				}
			}else{
				tdTjBhk.setState(1 == checkState ? 5 : 0);
				bgkFlow.setOperFlag(1 == checkState?43:11);
				bgkFlow.setPreviousOperFlag(21);
			}
		}
		if(flag){
			//初审结果
			tdTjBhk.setCountyRst(checkState);
			tdTjBhk.setCountyAuditAdv(checkRst);
			tdTjBhk.setFkByCountyChkOrgid(Global.getUser().getTsUnit());
			tdTjBhk.setCountySmtDate(new Date());
			tdTjBhk.setCountyCheckWay(1);
		}
		bgkFlow.setAuditAdv(checkRst);
		bgkFlow.setAuditMan(Global.getUser().getUsername());
		bgkFlow.setCartType(9);
		bgkFlow.setBusId(tdTjBhk.getRid());
		bgkFlow.setRcvDate(new Date());
		bgkFlow.setFkBySmtPsnId(Global.getUser());
		bgkFlow.setBhkCode(tdTjBhk.getBhkCode());
		bgkFlow.setFkByBhkorgId(tdTjBhk.getTbTjSrvorg());
		try{
			Integer curUserId = Global.getUser().getRid();
			String userTip = "当前用户："+curUserId;
			//添加操作流程
			this.bgkFlowService.saveCheckProcess(tdTjBhk,bgkFlow, this.minDaysBeforeTheWorkingDayDate);
			System.out.println(userTip+"提交存储用时:"+(System.currentTimeMillis()-curTime)+"ms");
			curTime = System.currentTimeMillis();
			//是否显示下一个
			//nextData();
			nextDataNew();
			System.out.println(userTip+"提交后下一个用时:"+(System.currentTimeMillis()-curTime)+"ms");
			curTime = System.currentTimeMillis();
			JsfUtil.addSuccessMessage("提交成功！");
			this.modView("0","1");
			System.out.println(userTip+"提交后初始化修改页用时:"+(System.currentTimeMillis()-curTime)+"ms");
			curTime = System.currentTimeMillis();
			this.searchAction();
			System.out.println(userTip+"提交后查询列表页用时:"+(System.currentTimeMillis()-curTime)+"ms");
			System.out.println(userTip+"提交总用时:"+(System.currentTimeMillis()-caculTotal)+"ms");
		}catch(Exception e){
			e.printStackTrace();
			JsfUtil.addErrorMessage("提交失败！");
		}
	}

	private StringBuffer initNextSql() {
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT RID FROM (");
		this.buildNextDataSql(sql);
		this.buildNormalSqlParams(sql,"1");
		//异常情况 --存在异常情况是UNION ALL
		if(StringUtils.isNotBlank(this.searchAbnormals)){
			sql.append(" UNION ");
			this.buildNextDataSql(sql);
			this.buildNormalSqlParams(sql,"2");
		}
		sql.append(")A where 1=1 ");
		this.buildOtSqlParams(sql);
		//下一个 只要查询到数据就行
		sql.append(" AND ROWNUM=1 ");

		return  sql;
	}

	/**
	 *  <p>方法描述：下一个sql抽取</p>
	 * @MethodAuthor hsj 2024-12-25 9:18
	 */
	private void buildNextDataSql(StringBuffer sql) {
		sql.append(" select T.RID, ");
		//接收日期
		if("2".equals(checkLevel)){//2级审核
			if (zoneType <= 3) {//终审
				/*省直属情况*/
				sql.append(" CASE ");
				sql.append(" WHEN T.STATE < 4 THEN NULL ");
				sql.append(" WHEN T6.if_prov_direct = 1 THEN T.DEAL_COMPLETE_DATE ");
				sql.append(" ELSE T.COUNTY_SMT_DATE END AS revDate ");
			}else {//初审
				sql.append(" T.DEAL_COMPLETE_DATE AS revDate ");
			}
		}else if ("3".equals(checkLevel)) {//3级审核
			if (zoneType == 2) {//终审
				sql.append(" CASE WHEN T.STATE < 4 THEN NULL WHEN T6.if_city_direct = 1 THEN T.COUNTY_SMT_DATE ELSE T.CITY_SMT_DATE END AS revDate");
			}else if (zoneType == 3){//复审
				/*市直属情况*/
				sql.append(" CASE WHEN T6.if_city_direct = 1 THEN T.DEAL_COMPLETE_DATE ");
				sql.append(" WHEN T.STATE < 2 THEN NULL ");
				sql.append(" ELSE T.COUNTY_SMT_DATE END AS revDate ");
			}else {//初审
				sql.append(" T.DEAL_COMPLETE_DATE AS revDate ");
			}
		}
		sql.append(" FROM TD_TJ_BHK T ");
		sql.append(" LEFT JOIN TB_TJ_CRPT T1 ON T1.RID = T.CRPT_ID ");
		sql.append(" LEFT JOIN TB_TJ_CRPT T5 ON T5.RID = T.ENTRUST_CRPT_ID ");
		sql.append(" LEFT JOIN TS_SIMPLE_CODE T2 ON T2.RID = T.ONGUARD_STATEID ");
		sql.append(" LEFT JOIN TB_TJ_SRVORG T3 ON T3.RID = T.BHKORG_ID ");
		sql.append(" LEFT JOIN TS_ZONE T4 ON T4.RID = T1.ZONE_ID ");
		sql.append(" LEFT JOIN TS_ZONE T6 ON T6.RID = T5.ZONE_ID ");
		sql.append(" WHERE 1=1 AND T.IF_INTO_CHECK =1 ");
		if (zoneType>3) {
			sql.append(" AND NVL(T.STATE,1)=1 ");
		}else {
			if ("3".equals(checkLevel)){
				if (zoneType==2){
					sql.append(" AND T.STATE=5 ");
				}else if (zoneType==3) {
					sql.append(" AND ((NVL(T.STATE,1)=1 AND T6.if_city_direct=1) OR (T.STATE=3 AND NVL(T6.if_city_direct,0)=0)) ");
				}
			}else if ("2".equals(checkLevel)) {
				sql.append(" AND (T.STATE=5 OR (T.STATE IS NULL AND T6.if_prov_direct=1)) ");
			}
		}
	}

	/**
	 * <p>方法描述：单个待审核数据 </p>
	 * pw 2024/1/30
	 **/
	private void nextDataNew(){
		List<Object> list = commService.findDataBySqlNoPage(this.initNextSql().toString(),this.paramMap);
		this.nextList = new ArrayList<>();
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		for (Object obj : list) {
			this.nextList.add(Integer.parseInt(obj.toString()));
		}
	}

	/**
	 *  <p>方法描述：符合条件的待审核数据</p>
	 * @MethodAuthor hsj
	 */
	private void nextData() {
		StringBuffer sql = new StringBuffer();
		sql.append(initSql());
		String h1 = "SELECT * FROM (" + sql.toString() + ")AA  ORDER BY AA.ZONE_GB, AA.CRPT_NAME, AA.PERSON_NAME, AA.revDate";
		List<Object[]> list = commService.findDataBySqlNoPage(h1,this.paramMap);
		nextList = new ArrayList<>();
		if (!CollectionUtils.isEmpty(list)) {
			for (Object[] obj : list) {
				if (zoneType>3) {
					if(null==obj[11]||"1".equals(obj[11].toString())){
						nextList.add(Integer.valueOf(obj[0].toString()));
					}
				}else {
					if ("3".equals(checkLevel)){
						if (zoneType==2 && null!=obj[11] && "5".equals(obj[11].toString())){//3级审核省级
							nextList.add(Integer.valueOf(obj[0].toString()));
						}else if (zoneType==3) {
							if(null!=obj[13] && "1".equals(obj[13].toString())){//市直属
								if(null==obj[11]){
									nextList.add(Integer.valueOf(obj[0].toString()));
								}else if ("1".equals(obj[11].toString())){
									nextList.add(Integer.valueOf(obj[0].toString()));
								}
							}else {
								if (null!=obj[11] && "3".equals(obj[11].toString())) {
									nextList.add(Integer.valueOf(obj[0].toString()));
								}
							}
						}
					}else if ("2".equals(checkLevel)) {
						if(null!=obj[11]){
							if ("5".equals(obj[11].toString())) {
								nextList.add(Integer.valueOf(obj[0].toString()));
							}
						}else {
							if(null!=obj[14] && "1".equals(obj[14].toString())){//省直属
								nextList.add(Integer.valueOf(obj[0].toString()));
							}
						}
					}
				}
			}
		}
	}

	/**
	 *  <p>方法描述：下一个</p>
	 * @MethodAuthor hsj
	 */
	public void nextAction(){
		if(null != nextRid){
			rid = nextRid;
			this.modView("1","0");
		}
	}
	/**
	 * @Description: 撤销
	 *
	 * @MethodAuthor pw,2021年05月13日
	 */
	public void cancelAction(){
		RequestContext.getCurrentInstance().execute("PF('ConfirmViewDialog').show()");
	}


	/**
	 * @Description: 审核页面 审核结果change
	 *
	 * @MethodAuthor pw,2021年05月13日
	 */
	public void changeCheckState(){
		if(null != defaultAuditAdv && null != checkState && 1 == checkState && StringUtils.isBlank(checkRst)){
			checkRst = defaultAuditAdv;
		}
		if(null != defaultAuditAdv && null != checkState && 2 == checkState && defaultAuditAdv.equals(checkRst)){
			checkRst = "";
		}
	}

    /**
     * @Description: 批量审核页面 审核结果change
     *
     * @MethodAuthor pw,2021年05月13日
     */
    public void batchChangeCheckState(){
      if(batchCheckState==1){
        //批量审核通过
		  this.batchCheckRst = PropertyUtils.getValue("defaultAuditAdv");
      }else {
		  this.batchCheckRst = "";
	  }
    }

	/**
	 * 方法描述：查询体检机构
	 */
	public void selUnitAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 625, null, 500);

		Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
		List<String> paramList = new ArrayList<String>();
		paramList.add(this.searchUnitId);
		paramMap.put("selectIds", paramList);
		paramList = new ArrayList<String>();
		paramList.add("true");
		paramMap.put("showAllSel", paramList);
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
	}

	/**
	 * 方法描述：处理选择的体检机构
	 */
	public void onSelectUnitAction(SelectEvent event) {
		Map<String, Object> selectedMap = new HashMap<>();
		try {
			selectedMap = (Map<String, Object>) event.getObject();
		} catch (Exception e) {
			e.printStackTrace();
			this.searchUnitId = null;
			this.searchUnitName = null;
		}
		if (null != selectedMap && selectedMap.size() > 0) {
			List<TbTjSrvorg> list = CollectionUtil.castList(TbTjSrvorg.class, selectedMap.get("selectPros"));
			if (!CollectionUtils.isEmpty(list)) {
				StringBuilder names = new StringBuilder();
				StringBuilder ids = new StringBuilder();
				for (TbTjSrvorg t : list) {
					names.append("，").append(t.getUnitName());
					ids.append(",").append(t.getRid());
				}
				this.searchUnitId = ids.substring(1);
				this.searchUnitName = names.substring(1);
			}
		}
	}

	/**
	 * 方法描述：清空单位
	 */
	public void clearUnit() {
		this.searchUnitId = null;
		this.searchUnitName = null;
	}

	/**
	 * 导出事件初始化
	 */
	public void exportAction() {
		//验证是否达到次数
		Integer tdTjExportCount = null;
		TsSimpleCode busType = busTypeMap.get("3");
		if(null == busType){
			JsfUtil.addErrorMessage("导出失败，导出类型不存在！");
			return;
		}
		tdTjExportCount = this.hethStaQueryCommService.findTdTjExportByOperPsnId(busType.getRid().toString());
		if (tdTjExportCount != null && MAX_EXPORT_LOAD != null && tdTjExportCount >= MAX_EXPORT_LOAD) {
			JsfUtil.addErrorMessage("导出次数达到上限，请稍后导出！");
			return;
		}
		this.selectedExportNodes = null;
		this.exportTreeNode = new CheckboxTreeNode("root", null);
		if (firstLevelNoSet.size() > 0 && codeMap.size() > 0) {
			for (String ln : firstLevelNoSet) {
				TbTjItems tbTjItems = codeMap.get(ln);
				TreeNode node = new CheckboxTreeNode(tbTjItems, this.exportTreeNode);
				this.addExportChildNode(ln, levelNoSet, codeMap, node, null);
			}
		}
		RequestContext.getCurrentInstance().execute("PF('ExportItemDialog').show()");
	}

	/**
	 * 构建菜单树
	 *
	 * @param levelNo         菜单层级编码
	 * @param levelNoSet      二级以及以上的菜单的层级编码集合
	 * @param itemMap         菜单map
	 * @param parentNode      上级树节点
	 * @param selectedMenuMap 已有的菜单map
	 */
	private void addExportChildNode(String levelNo, Set<String> levelNoSet, Map<String, TbTjItems> itemMap, TreeNode parentNode, Map<Integer, Integer> selectedMenuMap) {
		int level = StringUtils.countMatches(levelNo, ".");
		for (String ln : levelNoSet) {
			if (StringUtils.countMatches(ln, ".") == (level + 1) && StringUtils.startsWith(ln, levelNo + ".")) {
				TbTjItems t = itemMap.get(ln);
				TreeNode node = new CheckboxTreeNode(t, parentNode);
				if (selectExportItemSet.contains(t.getLevelCode())) {
					node.setSelected(true);
				}
				this.addExportChildNode(ln, levelNoSet, itemMap, node, selectedMenuMap);
			}
		}
	}

	/**
	 * 生成导出任务
	 */
	public void exportData() {
		getExportItem();
		tdTjExport = new TdTjExport();
		TsSimpleCode busType = busTypeMap.get("3");
		if(null == busType){
			JsfUtil.addErrorMessage("导出失败，导出类型不存在！");
			return;
		}
		tdTjExport.setFkByBusTypeId(busType);
		//导出条件
		exportConditionData();
		tdTjExport.setState(0);
		tdTjExport.setExportDate(new Date());
		tdTjExport.setFkByOperUnitId(Global.getUser().getTsUnit());
		tdTjExport.setFkByOperPsnId(Global.getUser());
		tdTjExport.setExportFileName(busType.getExtendS3()+(null == this.needEnctryInfo || 1 != this.needEnctryInfo ? "（内部）" : ""));
		this.hethStaQueryCommService.saveTdTjExport(tdTjExport);
		JsfUtil.addSuccessMessage("导出任务已发送，请至导出文件下载模块下载！");
	}

	/**
	 * 获取导出选择项
	 */
	public void getExportItem() {
		exportItems = null;
		selectExportNames = null;

		//临时体检项目名称
		List<TbTjItems> items = new ArrayList<>();

		if (null != this.selectedExportNodes && this.selectedExportNodes.length > 0) {
			for (TreeNode node : this.selectedExportNodes) {
				this.chooseExportMenuIds(node, items);
			}
			compareTbTjItems(items);
		} else {
			if (null != this.exportTreeNode) {
				List<TreeNode> childs = exportTreeNode.getChildren();
				if (null != childs && childs.size() > 0) {
					for (TreeNode node : childs) {
						List<TreeNode> lastNodes = node.getChildren();
						if (CollectionUtils.isEmpty(lastNodes)) {// 最末级
							TbTjItems data = (TbTjItems) node.getData();
							if (null != data.getRid() && data.getRid() != -1) {
								items.add(data);
							}
						} else {
							for (TreeNode last : lastNodes) {
								TbTjItems data = (TbTjItems) last.getData();
								if (null != data.getRid() && data.getRid() != -1) {
									items.add(data);
								}
							}
						}
					}
				}
			}
		}
		//临时体检项目ids
		if (!CollectionUtils.isEmpty(items)) {
			StringBuilder tempStr = new StringBuilder();
			StringBuilder tempNameStr = new StringBuilder();
			for (TbTjItems t : items) {
				tempStr.append(",").append(t.getRid());
				tempNameStr.append(",").append(t.getItemName());
			}
			exportItems = tempStr.substring(1);
			selectExportNames = tempNameStr.substring(1);
		}
	}

	/**
	 * 根据树节点，将其与其父节点的对象ID放到集合中
	 *
	 * @param node  树节点，内容存的是菜单对象
	 * @param items 菜单ID集合
	 */
	private void chooseExportMenuIds(TreeNode node, List<TbTjItems> items) {
		if (null != node && !(node.equals(this.exportTreeNode))) {
			TbTjItems data = (TbTjItems) node.getData();
			if (null != data.getRid() && data.getRid() != -1) {
				items.add(data);
			}
			this.chooseExportMenuIds(node.getParent(), items);
		}
	}

	/**
	 * <p>方法描述：选择体检项目，输入框显示名称排序</p>
	 *
	 * @MethodAuthor qrr, 2020年5月20日, compareTbTjItems
	 */
	private void compareTbTjItems(List<TbTjItems> items) {
		if(CollectionUtils.isEmpty(items)){
			return;
		}
		Collections.sort(items, new Comparator<TbTjItems>() {
			@Override
			public int compare(TbTjItems o1, TbTjItems o2) {
				TsSimpleCode t1 = o1.getTsSimpleCode();
				TsSimpleCode t2 = o2.getTsSimpleCode();
				if (null != t1 && null != t2) {
					Integer num1 = t1.getNum();
					Integer num2 = t2.getNum();
					if (null != num1 && null != num2) {
						int val = num1.compareTo(num2);
						if (val != 0) {
							return val;
						}
					} else {
						int val = String.valueOf(t1.getCodeLevelNo())
								.compareTo(String.valueOf(t2.getCodeLevelNo()));
						if (val != 0) {
							return String.valueOf(t1.getCodeLevelNo()).compareTo(
									String.valueOf(t2.getCodeLevelNo()));
						}
					}
				}
				Integer itemNum1 = o1.getNum();
				Integer itemNum2 = o2.getNum();
				if (null!=itemNum1 && null!=itemNum2) {
					int result = itemNum1.compareTo(itemNum2);
					if(0 != result){
						return result;
					}
				}else if(null != itemNum1 && null == itemNum2){
					return 1;
				}else if(null != itemNum2 && null == itemNum1){
					return -1;
				}
				String itemCode1 = o1.getItemCode();
				String itemCode2 = o2.getItemCode();
				if(null != itemCode1 && null != itemCode2){
					return itemCode1.compareTo(itemCode2);
				}else if(null != itemCode1 && null == itemCode2){
					return 1;
				}else if(null != itemCode2 && null == itemCode1){
					return -1;
				}
				return 0;
			}

		});
	}

	private void exportConditionData() {
		JSONObject jsonObject = new JSONObject();
		StringBuilder str = new StringBuilder();
		//用工单位地区
		if(StringUtils.isNotBlank(this.searchZoneCodeEmp)){
			jsonObject.put("searchZoneCodeEmp", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCodeEmp).trim()));
			str.append("用工单位地区@*@").append(StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneNameEmp).trim()))
					.append("#*#");
		}
		//用工单位名称
		if(StringUtils.isNotBlank(this.searchCrptNameEmp)){
			jsonObject.put("searchCrptNameEmp", this.searchCrptNameEmp.trim());
			str.append("用工单位名称@*@").append(this.searchCrptNameEmp.trim())
					.append("#*#");
		}
		//用工单位社会信用代码
		if(StringUtils.isNotBlank(this.searchCreditCodeEmp)){
			jsonObject.put("searchCreditCodeEmp", this.searchCreditCodeEmp.trim());
			str.append("用工单位社会信用代码@*@").append(this.searchCreditCodeEmp.trim())
					.append("#*#");
		}
		//用人单位地区 searchZoneCode
		if (StringUtils.isNotBlank(this.searchZoneCode)) {
			jsonObject.put("zonecode", StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneCode).trim()));
			str.append("用人单位地区@*@").append(StringUtils.convertBFH(ZoneUtil.zoneSelect(this.searchZoneName).trim()))
					.append("#*#");
		}
		//用人单位名称 crptName
		if (StringUtils.isNotBlank(this.searchCrptName)) {
			jsonObject.put("crptName", StringUtils.convertBFH(this.searchCrptName.trim()));
			str.append("用人单位名称@*@").append(StringUtils.convertBFH(this.searchCrptName.trim())).append("#*#");
		}
		//社会信用代码 creditCode
		if (StringUtils.isNotBlank(this.searchCreditCode)) {
			jsonObject.put("creditCode", this.searchCreditCode);
			str.append("用人单位社会信用代码@*@").append(this.searchCreditCode).append("#*#");
		}
		//证件号码 personIdc
		if (StringUtils.isNotBlank(this.searchIdc)) {
			jsonObject.put("personIdc", StringUtils.convertBFH(this.searchIdc.trim()));
			str.append("证件号码@*@").append(StringUtils.convertBFH(this.searchIdc.trim())).append("#*#");
		}
		//人员姓名 personName
		if (StringUtils.isNotBlank(this.searchPersonName)) {
			jsonObject.put("personName", StringUtils.convertBFH(this.searchPersonName.trim()));
			str.append("人员姓名@*@").append(this.searchPersonName).append("#*#");
		}
		//体检类型
		if (null != searchBhkType && searchBhkType.length > 0) {
			jsonObject.put("bhkTypes", StringUtils.array2string(searchBhkType, ","));
			str.append("体检类型@*@").append(StringUtils.array2string(transformStrings(searchBhkType, 3), "，"))
					.append("#*#");
		}
		//体检日期 searchBhkBdate searchBhkEdate
		String searchBhkBdate = "";
		String searchBhkEdate = "";
		if (this.searchBhkBdate != null) {
			searchBhkBdate = DateUtils.formatDate(this.searchBhkBdate);
			jsonObject.put("searchBhkBdate", searchBhkBdate);
		}
		if (this.searchBhkEdate != null) {
			searchBhkEdate = DateUtils.formatDate(this.searchBhkEdate);
			jsonObject.put("searchBhkEdate", searchBhkEdate);
		}
		if (this.searchBhkBdate != null || this.searchBhkEdate != null) {
			str.append("体检日期@*@").append(searchBhkBdate).append("~").append(searchBhkEdate).append("#*#");
		}
		//报告日期
		String startCreateDate = "";
		String endCreateDate = "";
		if ( null != this.startCreateDate) {
			startCreateDate = DateUtils.formatDate(this.startCreateDate);
			jsonObject.put("startCreateDate", startCreateDate);
		}
		if ( null != this.endCreateDate) {
			endCreateDate = DateUtils.formatDate(this.endCreateDate);
			jsonObject.put("endCreateDate", endCreateDate);
		}
		if (this.startCreateDate != null || this.endCreateDate != null) {
			str.append("报告日期@*@").append(startCreateDate).append("~").append(endCreateDate).append("#*#");
		}
		//报告出具日期
		String startRptPrintDate = "";
		String endRptPrintDate = "";
		if ( null != this.startRptPrintDate) {
			startRptPrintDate = DateUtils.formatDate(this.startRptPrintDate);
			jsonObject.put("startRptPrintDate", startRptPrintDate);
		}
		if ( null != this.endRptPrintDate) {
			endRptPrintDate = DateUtils.formatDate(this.endRptPrintDate);
			jsonObject.put("endRptPrintDate", endRptPrintDate);
		}
		if (this.startRptPrintDate != null || this.endRptPrintDate != null) {
			str.append("报告出具日期@*@").append(startRptPrintDate).append("~").append(endRptPrintDate).append("#*#");
		}
		//在岗状态 selectOnGuardIds
		if (StringUtils.isNotBlank(this.selectOnGuardIds)) {
			jsonObject.put("selectOnGuardIds", this.selectOnGuardIds);
			str.append("在岗状态@*@").append(this.selectOnGuardNames).append("#*#");
		}
		//体检危害因素 selectBadRsnNames
		if (StringUtils.isNotBlank(this.selectBadRsnIds)) {
			jsonObject.put("selectBadRsnIds", this.selectBadRsnIds);
			str.append("体检危害因素@*@").append(this.selectBadRsnNames).append("#*#");
		}
		//单危害因素结论
		if(StringUtils.isNotBlank(this.searchSelBhkrstIds)){
			jsonObject.put("searchSelBhkrstIds",this.searchSelBhkrstIds);
			str.append("单危害因素结论@*@").append(this.searchBhkrstName).append("#*#");//searchBhkrstName
		}
		//接收日期 searchRcvBdate searchRcvEdate
		String searchRcvBdate = "";
		String searchRcvEdate = "";
		if (this.searchRcvBdate != null) {
			searchRcvBdate = DateUtils.formatDate(this.searchRcvBdate);
			jsonObject.put("searchRcvBdate", searchRcvBdate);
		}
		if (this.searchRcvEdate != null) {
			searchRcvEdate = DateUtils.formatDate(this.searchRcvEdate);
			jsonObject.put("searchRcvEdate", searchRcvEdate);
		}
		if (this.searchRcvBdate != null || this.searchRcvEdate != null) {
			str.append("接收日期@*@").append(searchRcvBdate).append("~").append(searchRcvEdate).append("#*#");
		}
		//监测类别 jcTypes
		if (null != jcTypes && jcTypes.length > 0) {
			jsonObject.put("jcTypes", StringUtils.array2string(jcTypes, ","));
			str.append("监测类别@*@").append(StringUtils.array2string(transformStrings(jcTypes, 1), "，"))
					.append("#*#");
		}
		//是否复检 ifRhks
		if (null != ifRhks && ifRhks.length > 0) {
			jsonObject.put("ifRhks", StringUtils.array2string(ifRhks, ","));
			str.append("是否复检@*@").append(StringUtils.array2string(transformStrings(ifRhks, 2), "，"))
					.append("#*#");
		}
		//是否异常 ifAbnormals
		if (null != ifAbnormals && ifAbnormals.length > 0) {
			jsonObject.put("ifAbnormals", StringUtils.array2string(ifAbnormals, ","));
			str.append("是否异常@*@").append(StringUtils.array2string(transformStrings(ifAbnormals, 2), "，"))
					.append("#*#");
		}
		if (StringUtils.isNotBlank(this.searchAbnormals)) {
			jsonObject.put("searchAbnormals", this.searchAbnormals);
			str.append("异常原因@*@").append(this.searchAbnormals).append("#*#");
		}
		//检查机构 searchUnitId
		if (StringUtils.isNotBlank(this.searchUnitId)) {
			jsonObject.put("searchUnitId", this.searchUnitId);
			str.append("检查机构@*@").append(this.searchUnitName).append("#*#");
		}
		//状态 states
		StringBuilder stateSb = new StringBuilder();
		if (null != states && states.length > 0) {
			for (String state : states) {
				stateSb.append(",").append(state);
			}
		} else {
			if (null != stateList && stateList.size() > 0) {
				for (SelectItem item : stateList) {
					stateSb.append(",").append(item.getValue());
				}
			}
		}
		jsonObject.put("states", stateSb.substring(1));
		str.append("状态@*@")
				.append(StringUtils.array2string(
						transformStrings(stateSb.substring(1).split(","), 0), "，")
				)
				.append("#*#");
		//checkLevel
		jsonObject.put("checkLevel", checkLevel);
		//zoneType
		jsonObject.put("zoneType", zoneType);
		//导出项目
		jsonObject.put("exportItems", exportItems);
		if (StringUtils.isNotBlank(exportItems)) {
			if (null != this.selectedExportNodes && this.selectedExportNodes.length > 0){
				str.append("导出项目@*@").append(selectExportNames.replaceAll(",", "，")).append("#*#");
			}
		}
		//是否需要脱敏
		jsonObject.put("ifNeedEnctryInfo", null != this.needEnctryInfo && 1 == this.needEnctryInfo);

		tdTjExport.setExportCondition(JSONObject.toJSONString(jsonObject));
		String showCon = null;
		if (str.length() > 0) {
			String showStr = str.toString();
			showStr = showStr.substring(0, showStr.lastIndexOf("#*#"));
			showCon = showStr + "";

		}
		tdTjExport.setExportConditionShow(showCon);
	}

	private String[] transformStrings(String[] strs, int mark) {
		String[] strings = new String[strs.length];
		for (int i = 0; i < strs.length; i++) {
			if (mark == 0) {
				strings[i] = stateMap.get(strs[i]);
				continue;
			}
			switch (strs[i]) {
				case "0":
					if (mark == 2) {
						strings[i] = "否";
					}
					break;
				case "1":
					switch (mark) {
						case 1:
							strings[i] = "常规监测";
							break;
						case 2:
							strings[i] = "是";
							break;
						default:
					}
					break;
				case "2":
					if (mark == 1) {
						strings[i] = "主动监测";
					}
					break;
				case "3":
					if (mark == 3) {
						strings[i] = "职业健康检查";
					}
					break;
				case "4":
					if (mark == 3) {
						strings[i] = "放射卫生健康检查";
					}
					break;
				default:
			}
		}
		return strings;
	}

	/**
	 *  <p>方法描述：跳转到导出文件下载模块</p>
	 * @MethodAuthor hsj 2025-03-05 14:04
	 */
	public void fileDownloadAction(){
		RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','导出文件下载','/webapp/system/tdTjExportList.faces','');");
	}
	/**
	 *  <p>方法描述：导入模板下载</p>
	 * @MethodAuthor hsj 2025-03-21 19:07
	 */
	public StreamedContent getTemplateFile() {
		String fileName = "职业健康检查数据审核退回导入模板.xlsx";
		if (StringUtils.isBlank(fileName)) {
			return null;
		}
		InputStream stream = null;
		try {
			String moudleFilePath = "/resources/template/excel/" + fileName;
			stream = FacesContext.getCurrentInstance().getExternalContext().getResourceAsStream(moudleFilePath);
			return new DefaultStreamedContent(stream, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", URLEncoder.encode(fileName, "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
			SystemMessageEnum.DOWNLOAD_FAIL.showMessage();
		} finally {
			if (stream != null) {
				try {
					stream.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}

		}
		return null;
	}
	/**
	 *  <p>方法描述：文件导入前验证</p>
     *  查询当前审核地区中是否有状态为待审核的任务
	 * @MethodAuthor hsj 2025-03-28 11:19
	 */
	public void importBefore() {
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null == tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		BigDecimal num = this.bgkFlowService.findAllCheckTaskAndCheckNum(tsZone.getZoneGb());
		if (new BigDecimal(-1).equals(num)) {
			JsfUtil.addErrorMessage("存在正在审核的数据，请耐心等待！");
			return;
		} else if (num != null) {
			JsfUtil.addErrorMessage("存在" + num + "条正在审核的数据，请耐心等待！");
			return;
		}
		RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').show();");
		RequestContext.getCurrentInstance().update("tabView:mainForm:uploadFileDialog");
	}

	/**
	 *  <p>方法描述：文件导入</p>
	 * @MethodAuthor hsj 2025-03-21 19:09
	 */
	public void importDataAction(FileUploadEvent event) {
		long startTime = System.currentTimeMillis();
		String updateFormId = "tabView:mainForm:uploadFileDialog";
		if (event == null || event.getFile() == null) {
			RequestContext.getCurrentInstance().update(updateFormId);
			RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
			SystemMessageEnum.IMPORT_EXCEPTION.showMessage();
			return;
		}
		try {
			UploadedFile file = event.getFile();
			//格式验证
			String fileName = file.getFileName();// 文件名称
			String contentType = file.getContentType().toLowerCase();
			String errorMsg = FileUtils.veryFile(file.getInputstream(),contentType, fileName, "5");
			if (StringUtils.isNotBlank(errorMsg)) {
				SystemMessageEnum.IMPORT_EXCEPTION.showMessage();
				RequestContext.getCurrentInstance().update(updateFormId);
				RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
				return;
			}
			Workbook wb = ImportExcelUtil.getWorkbook(file);
			int colSize = 4;
			//判断表头是否正确
			if(!ImportExcelUtil.getRowSize(wb,colSize)){
				SystemMessageEnum.IMPORT_EXCEPTION.showMessage();
				RequestContext.getCurrentInstance().update(updateFormId);
				RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
				return;
			}
			//判断是否存在数据
			if(!ImportExcelUtil.verifyWorkBook(wb,1,colSize)){
				SystemMessageEnum.IMPORT_EXCEL_NOT_DATA.showMessage();
				RequestContext.getCurrentInstance().update(updateFormId);
				RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
				return;
			}
			//heth/comm/module/bhkcheck
			//写入到文件
			String path = "heth/comm/module/bhkcheck/";
			String uuid = UUID.randomUUID().toString().replaceAll("-", "");
			String filePath = path + uuid + fileName.substring(fileName.lastIndexOf("."));
			//任务存储
			TdTjCheckTask task = dealTdTjCheckTask(filePath);
			FileUtils.copyFile(JsfUtil.getAbsolutePath()+filePath, file.getInputstream());
			this.hethBaseCommService.upsertEntity(task);
			long endTime = System.currentTimeMillis();
			System.out.println("职业健康检查数据审核退回导入耗时：" + (endTime - startTime));
			//查询是否存在错误信息
			initErrFilePath();
			SystemMessageEnum.IMPORT_EXCEL_SUCCESS_REFRESH.showMessage();
			RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
		} catch (Exception e) {
			e.printStackTrace();
			RequestContext.getCurrentInstance().update(updateFormId);
			RequestContext.getCurrentInstance().execute("PF('UploadFileDialog').hide()");
			SystemMessageEnum.IMPORT_EXCEPTION.showMessage();
		}
	}

	/**
	 *  <p>方法描述：退回导入任务存储</p>
	 * @MethodAuthor hsj 2025-03-26 10:16
	 */
	private TdTjCheckTask dealTdTjCheckTask(String filePath) {
		TdTjCheckTask task = new TdTjCheckTask();
		TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
		if (null == tsZone) {
			tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		task.setFkByZoneId(new TsZone(tsZone.getRid()));
		task.setFkByCheckUnitId(new TsUnit(Global.getUser().getTsUnit().getRid()));
		task.setFkByCheckRsnId(new TsUserInfo(Global.getUser().getRid()));
		task.setCheckDate(new Date());
		task.setCheckRst(2);
		task.setTaskType(1);
		task.setExportCondition(JSONObject.toJSONString(getSendBackCheckJson(filePath)));
		task.setState(0);
		return task;
	}
	/**
	 *  <p>方法描述：审核退回json封装</p>
	 * @MethodAuthor hsj 2025-03-26 16:24
	 */
	public SendBackCheckJson getSendBackCheckJson(String filePath) {
		SendBackCheckJson json = new SendBackCheckJson();
		json.setFilePath(filePath);
		json.setCheckLevel(this.checkLevel);
		json.setZoneType(StringUtils.objectToString(this.zoneType));
		json.setUsername(Global.getUser().getUsername());
		json.setLimitDate(this.minDaysBeforeTheWorkingDayDate);
		return json;
	}
	/**
	 *  <p>方法描述：错误文件下载</p>
	 * @MethodAuthor hsj 2025-03-28 10:21 
	 */
	public void errFileBefore(){
		if (ObjectUtil.isEmpty(this.errFileObj) ) {
			return;
		}
		String errFilePath = Convert.toStr(this.errFileObj[2],null);
		if(StringUtils.isBlank(errFilePath)){
			JsfUtil.addErrorMessage(Convert.toStr(this.errFileObj[1]));
			return;
		}
		RequestContext.getCurrentInstance().execute("getDownloadFileClick()");
	}
	public StreamedContent exportErr() {
		String errFilePath = Convert.toStr(this.errFileObj[2],null);
		InputStream stream;
		try {
			stream = new FileInputStream(JsfUtil.getAbsolutePath() + errFilePath);
			String contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
			String fileName = "职业健康检查数据审核退回导入错误数据.xlsx";
			if (errFilePath.endsWith(".xls")) {
				contentType = "application/vnd.ms-excel";
				fileName = "职业健康检查数据审核退回导入错误数据.xls";
			}
			return new DefaultStreamedContent(stream, contentType, URLEncoder.encode(fileName, "UTF-8"));
		} catch (Exception e) {
			e.printStackTrace();
			SystemMessageEnum.DOWNLOAD_FAIL.showMessage();
		}
		return null;
	}
	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public String getSearchZoneCode() {
		return searchZoneCode;
	}

	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public String getSearchCrptName() {
		return searchCrptName;
	}

	public void setSearchCrptName(String searchCrptName) {
		this.searchCrptName = searchCrptName;
	}

	public String getSearchPersonName() {
		return searchPersonName;
	}

	public void setSearchPersonName(String searchPersonName) {
		this.searchPersonName = searchPersonName;
	}

	public String getSearchIdc() {
		return searchIdc;
	}

	public void setSearchIdc(String searchIdc) {
		this.searchIdc = searchIdc;
	}

	public Date getSearchBhkBdate() {
		return searchBhkBdate;
	}

	public void setSearchBhkBdate(Date searchBhkBdate) {
		this.searchBhkBdate = searchBhkBdate;
	}

	public Date getSearchBhkEdate() {
		return searchBhkEdate;
	}

	public void setSearchBhkEdate(Date searchBhkEdate) {
		this.searchBhkEdate = searchBhkEdate;
	}

	public List<TsSimpleCode> getOnGuardList() {
		return onGuardList;
	}

	public void setOnGuardList(List<TsSimpleCode> onGuardList) {
		this.onGuardList = onGuardList;
	}

	public String getSelectOnGuardNames() {
		return selectOnGuardNames;
	}

	public void setSelectOnGuardNames(String selectOnGuardNames) {
		this.selectOnGuardNames = selectOnGuardNames;
	}

	public String getSelectOnGuardIds() {
		return selectOnGuardIds;
	}

	public void setSelectOnGuardIds(String selectOnGuardIds) {
		this.selectOnGuardIds = selectOnGuardIds;
	}

	public List<TsSimpleCode> getBadRsnList() {
		return badRsnList;
	}

	public void setBadRsnList(List<TsSimpleCode> badRsnList) {
		this.badRsnList = badRsnList;
	}

	public String getSelectBadRsnNames() {
		return selectBadRsnNames;
	}

	public void setSelectBadRsnNames(String selectBadRsnNames) {
		this.selectBadRsnNames = selectBadRsnNames;
	}

	public String getSelectBadRsnIds() {
		return selectBadRsnIds;
	}

	public void setSelectBadRsnIds(String selectBadRsnIds) {
		this.selectBadRsnIds = selectBadRsnIds;
	}

	public Date getSearchRcvBdate() {
		return searchRcvBdate;
	}

	public void setSearchRcvBdate(Date searchRcvBdate) {
		this.searchRcvBdate = searchRcvBdate;
	}

	public Date getSearchRcvEdate() {
		return searchRcvEdate;
	}

	public void setSearchRcvEdate(Date searchRcvEdate) {
		this.searchRcvEdate = searchRcvEdate;
	}

	public String[] getJcTypes() {
		return jcTypes;
	}

	public void setJcTypes(String[] jcTypes) {
		this.jcTypes = jcTypes;
	}

	public String[] getIfRhks() {
		return ifRhks;
	}

	public void setIfRhks(String[] ifRhks) {
		this.ifRhks = ifRhks;
	}

	public String[] getIfAbnormals() {
		return ifAbnormals;
	}

	public void setIfAbnormals(String[] ifAbnormals) {
		this.ifAbnormals = ifAbnormals;
	}

	public String[] getStates() {
		return states;
	}

	public void setStates(String[] states) {
		this.states = states;
	}
	public List<Object[]> getSelectEntitys() {
		return selectEntitys;
	}
	public void setSelectEntitys(List<Object[]> selectEntitys) {
		this.selectEntitys = selectEntitys;
	}
	public List<SelectItem> getStateList() {
		return stateList;
	}
	public void setStateList(List<SelectItem> stateList) {
		this.stateList = stateList;
	}
	public String getCheckLevel() {
		return checkLevel;
	}
	public void setCheckLevel(String checkLevel) {
		this.checkLevel = checkLevel;
	}
	public String getSearchCreditCode() {
		return searchCreditCode;
	}
	public void setSearchCreditCode(String searchCreditCode) {
		this.searchCreditCode = searchCreditCode;
	}
	public Integer getZoneType() {
		return zoneType;
	}
	public void setZoneType(Integer zoneType) {
		this.zoneType = zoneType;
	}

	public Integer getRid() {
		return rid;
	}

	public void setRid(Integer rid) {
		this.rid = rid;
	}

    public TdTjBhk getTdTjBhk() {
        return tdTjBhk;
    }

    public void setTdTjBhk(TdTjBhk tdTjBhk) {
        this.tdTjBhk = tdTjBhk;
    }

    public List<String> getUnAbnormalsList() {
        return unAbnormalsList;
    }

    public void setUnAbnormalsList(List<String> unAbnormalsList) {
        this.unAbnormalsList = unAbnormalsList;
    }

	public Integer getCheckState() {
		return checkState;
	}

	public void setCheckState(Integer checkState) {
		this.checkState = checkState;
	}

	public Boolean getCountyFlag() {
		return countyFlag;
	}

	public void setCountyFlag(Boolean countyFlag) {
		this.countyFlag = countyFlag;
	}

	public Boolean getCityFlag() {
		return cityFlag;
	}

	public void setCityFlag(Boolean cityFlag) {
		this.cityFlag = cityFlag;
	}

	public String getCheckRst() {
		return checkRst;
	}

	public void setCheckRst(String checkRst) {
		this.checkRst = checkRst;
	}

	public Boolean getProFlag() {
		return proFlag;
	}

	public void setProFlag(Boolean proFlag) {
		this.proFlag = proFlag;
	}

	public Boolean getCancelFlag() {
		return cancelFlag;
	}

	public void setCancelFlag(Boolean cancelFlag) {
		this.cancelFlag = cancelFlag;
	}

    public String getBatchCheckRst() {
        return batchCheckRst;
    }

    public void setBatchCheckRst(String batchCheckRst) {
        this.batchCheckRst = batchCheckRst;
    }

    public Integer getBatchCheckState() {
        return batchCheckState;
    }

    public void setBatchCheckState(Integer batchCheckState) {
        this.batchCheckState = batchCheckState;
    }

	public Boolean getEditFlag() {
		return editFlag;
	}

	public void setEditFlag(Boolean editFlag) {
		this.editFlag = editFlag;
	}

	public TdTjBhkInfoBean getTjBhkInfoBean() {
		return tjBhkInfoBean;
	}

	public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
		this.tjBhkInfoBean = tjBhkInfoBean;
	}

	public OutputPanel getArchivePanel() {
		return archivePanel;
	}

	public void setArchivePanel(OutputPanel archivePanel) {
		this.archivePanel = archivePanel;
	}

	public String getSearchUnitId() {
		return searchUnitId;
	}

	public void setSearchUnitId(String searchUnitId) {
		this.searchUnitId = searchUnitId;
	}

	public String getSearchUnitName() {
		return searchUnitName;
	}

	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}

	public TreeNode getExportTreeNode() {
		return exportTreeNode;
	}

	public void setExportTreeNode(TreeNode exportTreeNode) {
		this.exportTreeNode = exportTreeNode;
	}

	public TreeNode[] getSelectedExportNodes() {
		return selectedExportNodes;
	}

	public void setSelectedExportNodes(TreeNode[] selectedExportNodes) {
		this.selectedExportNodes = selectedExportNodes;
	}

	public String getReceiveDate() {
		return receiveDate;
	}

	public void setReceiveDate(String receiveDate) {
		this.receiveDate = receiveDate;
	}

	public String getExportItems() {
		return exportItems;
	}

	public void setExportItems(String exportItems) {
		this.exportItems = exportItems;
	}

	public String getSelectExportNames() {
		return selectExportNames;
	}

	public void setSelectExportNames(String selectExportNames) {
		this.selectExportNames = selectExportNames;
	}

	public Set<String> getFirstLevelNoSet() {
		return firstLevelNoSet;
	}

	public void setFirstLevelNoSet(Set<String> firstLevelNoSet) {
		this.firstLevelNoSet = firstLevelNoSet;
	}

	public Set<String> getSelectExportItemSet() {
		return selectExportItemSet;
	}

	public void setSelectExportItemSet(Set<String> selectExportItemSet) {
		this.selectExportItemSet = selectExportItemSet;
	}

	public Map<String, TbTjItems> getCodeMap() {
		return codeMap;
	}

	public void setCodeMap(Map<String, TbTjItems> codeMap) {
		this.codeMap = codeMap;
	}

	public Set<String> getLevelNoSet() {
		return levelNoSet;
	}

	public void setLevelNoSet(Set<String> levelNoSet) {
		this.levelNoSet = levelNoSet;
	}

	public Map<Integer, String> getItemCachMap() {
		return itemCachMap;
	}

	public void setItemCachMap(Map<Integer, String> itemCachMap) {
		this.itemCachMap = itemCachMap;
	}

	public Map<Integer, TbTjItems> getItemCachMaps() {
		return itemCachMaps;
	}

	public void setItemCachMaps(Map<Integer, TbTjItems> itemCachMaps) {
		this.itemCachMaps = itemCachMaps;
	}

	public TdTjExport getTdTjExport() {
		return tdTjExport;
	}

	public void setTdTjExport(TdTjExport tdTjExport) {
		this.tdTjExport = tdTjExport;
	}

	public Map<String, TsSimpleCode> getBusTypeMap() {
		return busTypeMap;
	}

	public void setBusTypeMap(Map<String, TsSimpleCode> busTypeMap) {
		this.busTypeMap = busTypeMap;
	}

	public Map<Integer, String> getTsSimpleCodeMap() {
		return tsSimpleCodeMap;
	}

	public void setTsSimpleCodeMap(Map<Integer, String> tsSimpleCodeMap) {
		this.tsSimpleCodeMap = tsSimpleCodeMap;
	}

	public String getCheckType() {
		return checkType;
	}

	public void setCheckType(String checkType) {
		this.checkType = checkType;
	}
	public List<Object[]> getHistoryList() {
		return historyList;
	}

	public void setHistoryList(List<Object[]> historyList) {
		this.historyList = historyList;
	}

	public boolean isNextFlag() {
		return nextFlag;
	}

	public void setNextFlag(boolean nextFlag) {
		this.nextFlag = nextFlag;
	}

	public List<Integer> getNextList() {
		return nextList;
	}

	public void setNextList(List<Integer> nextList) {
		this.nextList = nextList;
	}

	public Integer getNextRid() {
		return nextRid;
	}

	public void setNextRid(Integer nextRid) {
		this.nextRid = nextRid;
	}

	public String getTipInfo() {
		return tipInfo;
	}

	public void setTipInfo(String tipInfo) {
		this.tipInfo = tipInfo;
	}

	public Date getStartCreateDate() {
		return startCreateDate;
	}

	public void setStartCreateDate(Date startCreateDate) {
		this.startCreateDate = startCreateDate;
	}

	public Date getEndCreateDate() {
		return endCreateDate;
	}

	public void setEndCreateDate(Date endCreateDate) {
		this.endCreateDate = endCreateDate;
	}

	public Date getStartRptPrintDate() {
		return startRptPrintDate;
	}

	public void setStartRptPrintDate(Date startRptPrintDate) {
		this.startRptPrintDate = startRptPrintDate;
	}

	public Date getEndRptPrintDate() {
		return endRptPrintDate;
	}

	public void setEndRptPrintDate(Date endRptPrintDate) {
		this.endRptPrintDate = endRptPrintDate;
	}

	public String[] getSearchBhkType() {
		return searchBhkType;
	}

	public void setSearchBhkType(String[] searchBhkType) {
		this.searchBhkType = searchBhkType;
	}

	public String getSearchZoneNameEmp() {
		return searchZoneNameEmp;
	}

	public void setSearchZoneNameEmp(String searchZoneNameEmp) {
		this.searchZoneNameEmp = searchZoneNameEmp;
	}

	public String getSearchZoneCodeEmp() {
		return searchZoneCodeEmp;
	}

	public void setSearchZoneCodeEmp(String searchZoneCodeEmp) {
		this.searchZoneCodeEmp = searchZoneCodeEmp;
	}

	public String getSearchCrptNameEmp() {
		return searchCrptNameEmp;
	}

	public void setSearchCrptNameEmp(String searchCrptNameEmp) {
		this.searchCrptNameEmp = searchCrptNameEmp;
	}

	public String getSearchCreditCodeEmp() {
		return searchCreditCodeEmp;
	}

	public void setSearchCreditCodeEmp(String searchCreditCodeEmp) {
		this.searchCreditCodeEmp = searchCreditCodeEmp;
	}

	public List<TsZone> getZoneListEmp() {
		return zoneListEmp;
	}

	public void setZoneListEmp(List<TsZone> zoneListEmp) {
		this.zoneListEmp = zoneListEmp;
	}

	public Integer getNeedEnctryInfo() {
		return needEnctryInfo;
	}

	public void setNeedEnctryInfo(Integer needEnctryInfo) {
		this.needEnctryInfo = needEnctryInfo;
	}

	public Boolean getIfHasInnerExp() {
		return ifHasInnerExp;
	}

	public void setIfHasInnerExp(Boolean ifHasInnerExp) {
		this.ifHasInnerExp = ifHasInnerExp;
	}

	public List<TsSimpleCode> getSearchBhkrstList() {
		return searchBhkrstList;
	}

	public void setSearchBhkrstList(List<TsSimpleCode> searchBhkrstList) {
		this.searchBhkrstList = searchBhkrstList;
	}

	public String getSearchSelBhkrstIds() {
		return searchSelBhkrstIds;
	}

	public void setSearchSelBhkrstIds(String searchSelBhkrstIds) {
		this.searchSelBhkrstIds = searchSelBhkrstIds;
	}

	public String getSearchBhkrstName() {
		return searchBhkrstName;
	}

	public void setSearchBhkrstName(String searchBhkrstName) {
		this.searchBhkrstName = searchBhkrstName;
	}

	public String getSearchAbnormals() {
		return searchAbnormals;
	}

	public void setSearchAbnormals(String searchAbnormals) {
		this.searchAbnormals = searchAbnormals;
	}

	public Boolean getIfImportExp() {
		return ifImportExp;
	}

	public void setIfImportExp(Boolean ifImportExp) {
		this.ifImportExp = ifImportExp;
	}

	public Object[] getErrFileObj() {
		return errFileObj;
	}

	public void setErrFileObj(Object[] errFileObj) {
		this.errFileObj = errFileObj;
	}

	public boolean isIfErrFileBnt() {
		return ifErrFileBnt;
	}

	public void setIfErrFileBnt(boolean ifErrFileBnt) {
		this.ifErrFileBnt = ifErrFileBnt;
	}

}
