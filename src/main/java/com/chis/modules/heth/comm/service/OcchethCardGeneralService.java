package com.chis.modules.heth.comm.service;

import cn.hutool.core.convert.Convert;
import com.chis.common.utils.CollectionUtil;
import com.chis.common.utils.ObjectUtil;
import com.chis.common.utils.StringUtils;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.rptvo.TbYsjcLimitValVO;
import com.chis.modules.system.service.AbstractTemplate;
import com.chis.modules.system.utils.Global;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <p>类描述：职业卫生技术服务信息报送卡填报-标配版本</p>
 *
 * @ClassAuthor hsj 2025-05-12 17:16
 */
@Service
@Transactional(readOnly = false)
public class OcchethCardGeneralService extends AbstractTemplate {

    /**
     * 删除职业卫生技术服务信息报送卡（标删）
     *
     * @param rid 职业卫生技术服务信息报送卡RID
     */
    public void deleteOcchethCardByRid(Integer rid) {
        if (ObjectUtil.isEmpty(rid)) {
            return;
        }
        String sql = "UPDATE TD_ZW_OCCHETH_CARD SET DEL_MARK = 1 WHERE RID = :rid";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        super.executeSql(sql, paramMap);
    }

    /**
     * 查询单位职业卫生技术服务机构资质是否已提交
     *
     * @param orgId 单位RID
     * @return 单位职业卫生技术服务机构资质是否已提交
     */
    @Transactional(readOnly = true)
    public boolean ifTdZwOcchethInfoComplete(Integer orgId) {
        if (null != orgId) {
            Map<String, Object> paramMap = new HashMap<>();
            String sql = "SELECT COUNT(1) FROM TD_ZW_OCCHETH_INFO OI " +
                    "LEFT JOIN TS_UNIT U ON OI.ORG_ID = U.RID " +
                    "WHERE OI.STATE = 1 AND U.RID = :orgId ";
            paramMap.put("orgId", orgId);
            int countBySql = super.findCountBySql(sql, paramMap);
            return countBySql > 0;
        }
        return false;
    }

    /**
     * 查询单位职业卫生技术服务机构资质基本信息
     *
     * @param orgId 单位RID
     * @return 职业卫生技术服务机构资质基本信息
     */
    @Transactional(readOnly = true)
    public Object[] findTdZwOcchethBaseInfo(Integer orgId) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT OI.RID, OI.ORG_NAME, OI.ORG_FZ, OI.ORG_ADDR, OI.CERT_NO, OI.LINK_MAN, Z.ZONE_GB FROM TD_ZW_OCCHETH_INFO OI " +
                "LEFT JOIN TS_UNIT U ON OI.ORG_ID = U.RID " +
                "LEFT JOIN TS_ZONE Z ON U.ZONE_ID = Z.RID " +
                "WHERE U.RID = :orgId ";
        paramMap.put("orgId", orgId);
        List<Object[]> objectList = CollectionUtil.castList(Object[].class, super.findDataBySqlNoPage(sql, paramMap));
        return ObjectUtil.isNotEmpty(objectList) ? objectList.get(0) : null;
    }

    /**
     * 查询单位职业卫生技术服务机构资质项目负责人信息
     *
     * @param orgId 单位RID
     * @return 职业卫生技术服务机构资质项目负责人信息
     */
    @Transactional(readOnly = true)
    public Object[] findOcchethPsnInfo(String orgId) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT P.EMP_NAME, P.MOBILE_NO " +
                "FROM TD_ZW_OCCHETH_PSNS OP " +
                "         LEFT JOIN TD_ZW_PSNINFO P ON OP.EMP_ID = P.RID " +
                "         LEFT JOIN TD_ZW_PSN_TYPE PT ON P.RID = PT.MAIN_ID " +
                "         LEFT JOIN TS_SIMPLE_CODE SC ON PT.PSN_TYPE = SC.RID " +
                "WHERE OP.ORG_ID = :orgId AND PT.ZZJG_TYPE = 3 AND SC.EXTENDS3 = '5' AND OP.ON_DUTY = 1 ";
        paramMap.put("orgId", orgId);
        List<Object[]> objectList = CollectionUtil.castList(Object[].class, super.findDataBySqlNoPage(sql, paramMap));
        return ObjectUtil.isNotEmpty(objectList) ? objectList.get(0) : null;
    }

    /**
     * 查询单位职业卫生技术服务机构资质资质业务范围
     *
     * @param orgId 单位RID
     * @return 职业卫生技术服务机构资质资质业务范围
     */
    @Transactional(readOnly = true)
    public List<Object> findOcchethItemsRidList(String orgId) {
        Map<String, Object> paramMap = new HashMap<>();
        String sql = "SELECT RID FROM (SELECT DISTINCT SC.RID, SC.NUM " +
                "FROM TD_ZW_OCCHETH_ITEMS OIT " +
                "        LEFT JOIN TS_SIMPLE_CODE SC ON OIT.ITEM_CODE = SC.CODE_NO " +
                "        LEFT JOIN TS_CODE_TYPE CT ON SC.CODE_TYPE_ID = CT.RID " +
                "WHERE CT.CODE_TYPE_NAME = '5320' AND OIT.ORG_ID = :orgId ORDER BY SC.NUM)";
        paramMap.put("orgId", orgId);
        return CollectionUtil.castList(Object.class, super.findDataBySqlNoPage(sql, paramMap));
    }

    /**
     * 获取职业卫生技术服务信息报送卡实体
     *
     * @param rid rid
     * @return 职业卫生技术服务信息报送卡实体
     */
    @Transactional(readOnly = true)
    public TdZwOcchethCard findTdZwOcchethCardByRid(Integer rid) {
        TdZwOcchethCard occhethCard = super.find(TdZwOcchethCard.class, rid);
        occhethCard.getOcchethCardItemsList().size();
        occhethCard.getOcchethCardPsnList().size();
        for (TdZwOcchethCardPsn occhethCardPsn : occhethCard.getOcchethCardPsnList()) {
            occhethCardPsn.getOcchethCardItemList().size();
        }
        occhethCard.getOcchethCardServiceList().size();
        occhethCard.getOcchethCardZoneList().size();

        occhethCard.setOcchethJcBadrsns(super.findEntityListByMainId(TdZwOcchethJcBadrsn.class, rid));
        occhethCard.setOcchethPjBadrsns(super.findEntityListByMainId(TdZwOcchethPjBadrsn.class, rid));

        occhethCard.setBpBadrsnList(findOcchethCardResult(rid));
        return occhethCard;
    }
    /**
     *  <p>方法描述：职业病危害因素检测结果查询</p>
     * @MethodAuthor hsj 2025-05-23 14:29
     */
    public List<TdZwOcchethBpBadrsn> findOcchethCardResult(Integer mainId){
        List<TdZwOcchethBpBadrsn> list = super.findEntityListByMainId(TdZwOcchethBpBadrsn.class, mainId);
        if (ObjectUtil.isEmpty(list)) {
            return list;
        }
        for (TdZwOcchethBpBadrsn badrsn : list) {
            badrsn.getBadrsnSubList().size();
            if(!CollectionUtils.isEmpty(badrsn.getBadrsnSubList())){
                for (TdZwOcchethBpSub badrsnSub : badrsn.getBadrsnSubList()) {
                    badrsnSub.getBadrsnItems().size();
                }
            }
        }
        return list;
    }

    /**
     * 保存职业卫生技术服务信息报送卡实体
     *
     * @param occhethCard 职业卫生技术服务信息报送卡实体
     */
    public void saveOcchethCard(TdZwOcchethCard occhethCard) {
        if (ObjectUtil.isNotEmpty(occhethCard.getRid())) {
            String sql = "";
            //删除子表技术服务领域
            sql = "DELETE FROM TD_ZW_OCCHETH_CARD_SERVICE WHERE MAIN_ID = " + occhethCard.getRid();
            this.em.createNativeQuery(sql).executeUpdate();
            //删除子表服务地址
            sql = "DELETE FROM TD_ZW_OCCHETH_CARD_ZONE WHERE MAIN_ID = " + occhethCard.getRid();
            this.em.createNativeQuery(sql).executeUpdate();
            //删除子表参与人员（承担的服务事项）
            sql = "DELETE FROM TD_ZW_OCCHETH_CARD_ITEM I WHERE EXISTS(SELECT 1 FROM TD_ZW_OCCHETH_CARD_PSN P WHERE I.MAIN_ID = P.RID AND P.MAIN_ID = " + occhethCard.getRid() + ") ";
            this.em.createNativeQuery(sql).executeUpdate();
            //删除子表参与人员
            sql = "DELETE FROM TD_ZW_OCCHETH_CARD_PSN WHERE MAIN_ID = " + occhethCard.getRid();
            this.em.createNativeQuery(sql).executeUpdate();

            //删除 职业病危害因素检测—超标危害因素
            sql = "DELETE FROM TD_ZW_OCCHETH_JC_BADRSN WHERE MAIN_ID = " + occhethCard.getRid();
            this.em.createNativeQuery(sql).executeUpdate();
            //删除 职业病危害现状评价—超标危害因素
            sql = "DELETE FROM TD_ZW_OCCHETH_PJ_BADRSN WHERE MAIN_ID = " + occhethCard.getRid();
            this.em.createNativeQuery(sql).executeUpdate();

            //删除 职业病危害因素明细
            sql = "delete from TD_ZW_OCCHETH_BADRSN_ITEM T where exists(select 1 from TD_ZW_OCCHETH_BP_SUB T2 LEFT JOIN TD_ZW_OCCHETH_BP_BADRSN T1 ON T2.MAIN_ID =T1.RID  where T.MAIN_ID=T2.RID and T1.MAIN_ID=" + occhethCard.getRid()+") ";
            this.em.createNativeQuery(sql).executeUpdate();
            //删除 职业病危害因素明细
            sql = "delete from TD_ZW_OCCHETH_BP_SUB T where exists(select 1 from TD_ZW_OCCHETH_BP_BADRSN T1 where T.MAIN_ID=T1.RID and T1.MAIN_ID=" + occhethCard.getRid()+") ";
            this.em.createNativeQuery(sql).executeUpdate();
            //删除 职业病危害因素
            sql = "DELETE FROM TD_ZW_OCCHETH_BP_BADRSN WHERE MAIN_ID = " + occhethCard.getRid();
            this.em.createNativeQuery(sql).executeUpdate();

        }
        occhethCard.setDelMark(0);
        super.upsertEntity(occhethCard);

        if(!CollectionUtils.isEmpty(occhethCard.getOcchethJcBadrsns())){
            this.saveBatchObjs(occhethCard.getOcchethJcBadrsns());
        }
        if(!CollectionUtils.isEmpty(occhethCard.getOcchethPjBadrsns())){
            this.saveBatchObjs(occhethCard.getOcchethPjBadrsns());
        }
        if(!CollectionUtils.isEmpty(occhethCard.getBpBadrsnList())){
            this.saveBatchObjs(occhethCard.getBpBadrsnList());
        }
        //提交时 更新上传主表日志记录状态为0 报告卡未存储过 不需要更新上传主表日志
        if (null != occhethCard.getRid() && null != occhethCard.getState() && 1 == occhethCard.getState()) {
            StringBuffer sqlBuffer = new StringBuffer();
            sqlBuffer.append(" UPDATE TD_ZYWS_CARD_RCD SET STATE = 0 , ")
                    .append(" MODIFY_DATE=SYSDATE , ")
                    .append(" MODIFY_MANID=").append(Global.getUser().getRid())
                    .append(" WHERE BUS_TYPE=12 ").append(" AND BUS_ID= ").append(occhethCard.getRid());
            this.executeSql(sqlBuffer.toString(), null);
        }
    }

    /**
     * 撤销职业卫生技术服务信息报送卡
     *
     * @param rid 职业卫生技术服务信息报送卡RID
     */
    public void revokeOcchethCardByRid(Integer rid) {
        if (ObjectUtil.isEmpty(rid)) {
            return;
        }
        String sql = "UPDATE TD_ZW_OCCHETH_CARD SET STATE = 0 WHERE RID = :rid";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        super.executeSql(sql, paramMap);
    }

    /**
     * 删除职业卫生技术服务信息报送卡上传文书
     *
     * @param rid 职业卫生技术服务信息报送卡RID
     */
    public void deleteOcchethCardAnnexPathByRid(Integer rid) {
        if (ObjectUtil.isEmpty(rid)) {
            return;
        }
        String sql = "UPDATE TD_ZW_OCCHETH_CARD SET ANNEX_PATH = NULL WHERE RID = :rid";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("rid", rid);
        super.executeSql(sql, paramMap);
    }

   /**
    *  <p>方法描述：危害因素弹出框sql查询</p>
    * @MethodAuthor hsj 2025-05-19 10:24
    */
    public List<Object[]> findBadrsnList(Integer rid, Integer firstBadRsnId, String badrsnName, String excludeIds) {
        Map<String, Object> map = new HashMap<String, Object>();
        StringBuilder sql = new StringBuilder();
        sql.append(" select T.rid, T.RSN_CN_NAME,T.NUM ");
        sql.append(" from TB_YSJC_LIMIT_VAL T ");
        sql.append(" left join TS_SIMPLE_CODE T2 on T.BADRSN_ID=T2.RID where 1=1 ");

        if (null != firstBadRsnId) {
            sql.append(" AND T.BADRSN_ID =").append(firstBadRsnId);
        }
        if (StringUtils.isNotBlank(badrsnName)) {
            sql.append(" AND (");
            sql.append(" T.RSN_CN_NAME LIKE :badrsnName escape '\\\'");
            sql.append(" OR LOWER(T.PYM) LIKE :pym escape '\\\'");
            sql.append(")");
            map.put("badrsnName", "%" + StringUtils.convertBFH(badrsnName.trim()) + "%");
            map.put("pym", "%" + StringUtils.convertBFH(badrsnName.trim()).toLowerCase() + "%");
        }
        if (StringUtils.isNotBlank(excludeIds)) {
            sql.append(" and T.rid not in(").append(excludeIds).append(")");
        }
        return this.findDataBySqlNoPage(sql.toString(), map);
    }

    /**
     * <p>方法描述：通过危害因素查询配置的检测项目</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-08-27
     **/
    public List<Object[]> findItemByBadrsnId(int rid, Integer itemRid) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select T1.RID,decode(T1.ITEM_DESC,null,T2.CODE_NAME,T2.CODE_NAME||'（'||T1.ITEM_DESC||'）') as ITEMNAME,T1.ITEM_DESC,T3.rid as MSRUNT_ID,T3.CODE_NAME as MSRUNT_name,T4.rid as MSRUNT2_ID,T4.CODE_NAME as MSRUNT2_name,T1.NUM,'' as NO_SIGN_NAME,'' as  NO_SIGN_ITEMNAME ");
        sql.append(" from TB_YSJC_RSN_REL_ITEM T1 ");
        sql.append(" left join  TB_YSJC_LIMIT_VAL T on T.rid=T1.MAIN_ID ");
        sql.append(" left join TS_SIMPLE_CODE T2 on T1.ITEM_ID=T2.RID ");
        sql.append(" left join TS_SIMPLE_CODE T3 on T1.MSRUNT_ID=T3.RID ");
        sql.append(" left join TS_SIMPLE_CODE T4 on T1.ITEM2_MSRUNT_ID=T4.RID ");
        sql.append(" where T.RID=").append(rid);
        if (itemRid != null) {
            sql.append(" and T1.ITEM_ID=").append(itemRid);
        }
        sql.append(" group by T1.RID, T2.CODE_NAME,T1.ITEM_DESC,T3.rid,T3.CODE_NAME,T4.rid,T4.CODE_NAME,T1.NUM ");
        sql.append(" order by T1.NUM ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * <p>方法描述：根据rid查询计量单位</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-01
     **/
    public List<Object[]> findMsruntById(int rid) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select T.RID,T3.CODE_NAME as MSRUNT_ID,T4.CODE_NAME as ITEM2_MSRUNT_ID ");
        sql.append(" from TB_YSJC_RSN_REL_ITEM T ");
        sql.append(" left join TS_SIMPLE_CODE T3 on T.MSRUNT_ID=T3.RID ");
        sql.append(" left join TS_SIMPLE_CODE T4 on T.ITEM2_MSRUNT_ID=T4.RID ");
        sql.append(" where T.RID=").append(rid);
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * <p>方法描述：封装页面  职业病危害因素检测 数据</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-01
     **/
    public List<TdZwOcchethCardJc> packageCardJc(Integer rid) {
        StringBuilder hql = new StringBuilder();
        hql.append(" select T from TdZwOcchethCardJc T ");
        hql.append(" where T.fkByMainId.rid=").append(rid);
        hql.append(" order by T.rid ");
        List<TdZwOcchethCardJc> list = this.findByHql(hql.toString(), TdZwOcchethCardJc.class);
        if (!CollectionUtils.isEmpty(list)) {
            for (TdZwOcchethCardJc cardJc : list) {
                cardJc.getOcchethCardJcSubList().size();
            }
        }
        return list;
    }

    /**
     * <p>方法描述：封装页面  职业病危害现状评价 数据</p>
     *
     * @MethodAuthor： yzz
     * @Date：2022-09-01
     **/
    public List<TdZwOcchethCardPj> packageCardPj(Integer rid) {
        StringBuilder hql = new StringBuilder();
        hql.append(" select T from TdZwOcchethCardPj T ");
        hql.append(" where T.fkByMainId.rid=").append(rid);
        hql.append(" order by T.rid ");
        List<TdZwOcchethCardPj> list = this.findByHql(hql.toString(), TdZwOcchethCardPj.class);
        if (!CollectionUtils.isEmpty(list)) {
            for (TdZwOcchethCardPj cardPj : list) {
                cardPj.getOcchethCardPjSubList().size();
            }
        }
        return list;
    }

    /**
     * <p>方法描述：获取对应业务类型、业务主键、状态的上传日志表数量 </p>
     *
     * @MethodAuthor hsj 2022-12-14 14:57
     */
    public int queryRcdCountByParams(Integer busType, Integer busId, Integer state) {
        if (null == busType || null == busId || null == state) {
            return 0;
        }
        StringBuffer queryBuffer = new StringBuffer();
        queryBuffer.append(" SELECT COUNT(1) FROM TD_ZYWS_CARD_RCD T WHERE T.BUS_TYPE= ")
                .append(busType).append(" AND T.BUS_ID=")
                .append(busId).append(" AND T.STATE=")
                .append(state);
        return this.findCountBySql(queryBuffer.toString());

    }

    /**
     * <p>方法描述：根据检测任务id查询</p>
     *
     * @MethodAuthor hsj 2025-05-12 16:08
     */
    public Boolean findCardIdByOccHethCard(Integer contractId) {
        StringBuffer queryBuffer = new StringBuffer();
        queryBuffer.append(" SELECT COUNT(1) FROM TD_ZW_OCCHETH_CARD T WHERE T.CONTRACT_ID= ")
                .append(contractId);
        return this.findCountBySql(queryBuffer.toString()) > 0;
    }

    /**
     *  <p>方法描述：危害因素查询</p>
     * @param selBadrsnList :已选择
     * @MethodAuthor hsj 2025-05-17 18:55
     */
    public List<TbYsjcLimitValVO> findBadrsnByBadrsnIds(List<String> selBadrsnList, List<Integer> badRsnIds,Integer firstBadRsnId, String searchNamOrPy) {
        List<TbYsjcLimitValVO> valVOList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        Map<String, Object> paramMap = new HashMap<>();
        sql.append("SELECT ")
                .append("T.BADRSN_ID,  T1.CODE_NAME, T.RID,  T.RSN_CN_NAME  ")
                .append(" FROM TB_YSJC_LIMIT_VAL T ")
                .append(" LEFT JOIN TS_SIMPLE_CODE T1 ON T.BADRSN_ID = T1.RID ")
                .append(" WHERE 1=1 ");

        if (!CollectionUtils.isEmpty(selBadrsnList)) {
            sql.append(" AND  T.BADRSN_ID IN (:selBadrsnList)");
            paramMap.put("selBadrsnList", selBadrsnList);
        }
        if(ObjectUtil.isNotNull(firstBadRsnId)){
            sql.append(" AND T.BADRSN_ID = :firstBadRsnId ");
            paramMap.put("firstBadRsnId", firstBadRsnId);
        }
        if (StringUtils.isNotBlank(searchNamOrPy)) {
            sql.append(" AND (");
            sql.append(" T.RSN_CN_NAME LIKE :badrsnName escape '\\\'");
            sql.append(" OR LOWER(T.PYM) LIKE :pym escape '\\\'");
            sql.append(")");
            paramMap.put("badrsnName", "%"+StringUtils.convertBFH(searchNamOrPy.trim())+"%");
            paramMap.put("pym", "%"+StringUtils.convertBFH(searchNamOrPy.trim()).toLowerCase()+"%");
        }
        if(!CollectionUtils.isEmpty(badRsnIds)){
            sql.append("      AND T.RID not in  (:badRsnIds) ");
            paramMap.put("badRsnIds", badRsnIds);
        }
        sql.append(" ORDER BY T1.NUM, T1.CODE_NO,T.NUM");
       List<Object[]> list = this.findDataBySqlNoPage(sql.toString(), paramMap);
       if(CollectionUtils.isEmpty(list)){
           return valVOList;
       }
        List<Integer> badrsnIdList = new ArrayList<>();
       for (Object[] obj : list) {
           Integer badrId  = Convert.toInt(obj[0]);
           if(StringUtils.isBlank(searchNamOrPy) && !badrsnIdList.contains(badrId)){
               TbYsjcLimitValVO valVO = new TbYsjcLimitValVO();
               valVO.setBadRsnId(badrId);
               valVO.setBadRsnName(Convert.toStr(obj[1]));
               valVO.setIfParent(Boolean.TRUE);
               valVOList.add(valVO);
               badrsnIdList.add(badrId);
           }
           TbYsjcLimitValVO valVO = new TbYsjcLimitValVO();
           valVO.setBadRsnId(Convert.toInt(obj[2]));
           valVO.setBadRsnName(Convert.toStr(obj[3]));
           valVO.setParentId(badrId);
           valVO.setIfParent(Boolean.FALSE);
           valVOList.add(valVO);
       }
       return valVOList;
    }


    /**
     * <p>Description：根据检测任务rid获取资质人员信息 </p>
     * <p>Author： yzz 2025/5/20 </p>
     */
    public List<Object[]> findPsnByContractId(Integer contractId) {
        if(contractId==null){
            return new ArrayList<>();
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" select psn.RID, psn.EMP_NAME ");
        sql.append(" from MOBILE_EXE_PSN T ");
        sql.append(" inner join TD_ZW_PSNINFO psn on T.PSN_ID = psn.RID ");
        sql.append(" where exists(select 1 ");
        sql.append(" from MOBILE_EXE_PROCESS T1 ");
        sql.append(" inner join MOBILE_EXE_FATHER T2 on T1.MAIN_ID = T2.RID ");
        sql.append(" where T.MAIN_ID = T1.RID and T2.MAIN_ID = ").append(contractId).append(") ");
        sql.append(" group by psn.RID, psn.EMP_NAME ");
        sql.append(" order by psn.RID ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * <p>Description：获取手机端最小打卡时间、最大签退时间 </p>
     * <p>Author： yzz 2025/5/20 </p>
     */
    public List<Object[]> findAppDate(Integer contractId) {
        StringBuffer sql = new StringBuffer();
        sql.append("  with table1 as ( ");
        sql.append("          select T.IN_TIME,T.OUT_TIME,T.TASK_TYPE ");
        sql.append("          from MOBILE_EXE_PROCESS T ");
        sql.append("          where exists( ");
        sql.append("                  select 1 from  MOBILE_EXE_FATHER T1 where T.MAIN_ID = T1.RID and T1.MAIN_ID= ").append(contractId);
        sql.append("          ) ");
        sql.append("  ), table2 as (select min(case ");
        sql.append("        when T.IN_TIME is not null and T.OUT_TIME is not null then least(T.IN_TIME, T.OUT_TIME) ");
        sql.append("when T.IN_TIME is not null then T.IN_TIME ");
        sql.append("when T.OUT_TIME is not null then T.OUT_TIME ");
        sql.append("end)                   as IN_TIME, ");
        sql.append("max(case ");
        sql.append("        when T.IN_TIME is not null and T.OUT_TIME is not null ");
        sql.append("then greatest(T.IN_TIME, T.OUT_TIME) ");
        sql.append("when T.IN_TIME is not null then T.IN_TIME ");
        sql.append("when T.OUT_TIME is not null then T.OUT_TIME ");
        sql.append("end) as OUT_TIME ");
        sql.append("from table1 T ");
        sql.append("where T.TASK_TYPE = 1), ");
        sql.append("table3 as (select min(case ");
        sql.append("        when T.IN_TIME is not null and T.OUT_TIME is not null then least(T.IN_TIME, T.OUT_TIME) ");
        sql.append("when T.IN_TIME is not null then T.IN_TIME ");
        sql.append("when T.OUT_TIME is not null then T.OUT_TIME ");
        sql.append("end)                   as IN_TIME, ");
        sql.append("max(case ");
        sql.append("        when T.IN_TIME is not null and T.OUT_TIME is not null ");
        sql.append("then greatest(T.IN_TIME, T.OUT_TIME) ");
        sql.append("when T.IN_TIME is not null then T.IN_TIME ");
        sql.append("when T.OUT_TIME is not null then T.OUT_TIME ");
        sql.append("end) as OUT_TIME ");
        sql.append("from table1 T ");
        sql.append("where T.TASK_TYPE = 2) ");
        sql.append("  select to_char(IN_TIME,'yyyy-mm-dd'),to_char(OUT_TIME,'yyyy-mm-dd'),1 as type ");
        sql.append("  from table2 ");
        sql.append("  union ");
        sql.append("  select to_char(IN_TIME,'yyyy-mm-dd'),to_char(OUT_TIME,'yyyy-mm-dd'),2 as type ");
        sql.append("  from table3 ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
    * <p>Description：获取手机端打卡记录 </p>
    * <p>Author： yzz 2025/5/22 </p>
    */
    public List<Object[]> findPsnClockList(Integer contractId) {
        if(contractId==null){
            return new ArrayList<>();
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" with table1 as (select T.RID, T.IN_TIME, T.OUT_TIME,T.TASK_TYPE ");
        sql.append("         from MOBILE_EXE_PROCESS T ");
        sql.append("         where exists(select 1 from MOBILE_EXE_FATHER T1 where T.MAIN_ID = T1.RID and T1.MAIN_ID = ").append(contractId).append(")), ");
        sql.append("         table2 as (select T.RID, listagg(T2.EMP_NAME, '、') within group (order by T2.EMP_NAME) as psns ");
        sql.append("         from table1 T ");
        sql.append("         inner join MOBILE_EXE_PSN T1 on T.RID = T1.MAIN_ID ");
        sql.append("         inner join TD_ZW_PSNINFO T2 on T1.PSN_ID = T2.RID ");
        sql.append("         group by T.RID), ");
        sql.append("         table3 as (select T.RID, PHOTO_LOCAL as in_PHOTO_LOCAL ");
        sql.append("         from table1 T ");
        sql.append("         inner join MOBILE_EXE_FILE T1 on T.RID = T1.MAIN_ID ");
        sql.append("         where T1.FILE_TYPE = 1), ");
        sql.append("         table4 as (select T.RID, PHOTO_LOCAL as out_PHOTO_LOCAL ");
        sql.append("         from table1 T ");
        sql.append("         left join MOBILE_EXE_FILE T1 on T.RID = T1.MAIN_ID ");
        sql.append("         where T1.FILE_TYPE = 3), ");
        sql.append("         table5 as (select T.RID, count(T.RID) as photo_num ");
        sql.append("         from table1 T ");
        sql.append("         inner join MOBILE_EXE_FILE T1 on T.RID = T1.MAIN_ID ");
        sql.append("         group by T.RID) ");
        sql.append(" select rownum,T.TASK_TYPE,T1.psns, to_char(T.IN_TIME,  'yyyy-mm-dd hh24:mi:ss'), to_char(T.OUT_TIME,'yyyy-mm-dd hh24:mi:ss'),  T2.in_PHOTO_LOCAL, T3.out_PHOTO_LOCAL, T4.photo_num,T.RID ");
        sql.append(" from table1 T ");
        sql.append(" inner join table2 T1 on T.RID = T1.RID ");
        sql.append(" inner join table3 T2 on T.RID = T2.RID ");
        sql.append(" left join table4 T3 on T.RID = T3.RID ");
        sql.append(" inner join table5 T4 on T.RID = T4.RID ");
        sql.append(" order by T.IN_TIME ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
    * <p>Description：根据过程rid获取图片 </p>
    * <p>Author： yzz 2025/5/22 </p>
    */
    public List<Object[]> findPhotoByProcessRid(Integer processRid) {
        if(processRid==null){
            return new ArrayList<>();
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" select FILE_PATH,FILE_TYPE,'','' ");
        sql.append(" from MOBILE_EXE_FILE where MAIN_ID=").append(processRid);
        sql.append(" order by FILE_TYPE ");
        return em.createNativeQuery(sql.toString()).getResultList();
    }

    /**
     * <p>方法描述：更新二维码路径 </p>
     * pw 2025/5/23
     **/
    public void updateOccHethCardQrCodePath(String qrCodePath, Integer rid) {
        if (null == rid || StringUtils.isBlank(qrCodePath)) {
            return;
        }
        String sql = " update TD_ZW_OCCHETH_CARD set QRCODE_PATH ='"+qrCodePath+"',MODIFY_DATE=sysdate,MODIFY_MANID="+Global.getUser().getRid()+" where rid="+rid;
        this.executeSql(sql, null);
    }

}
