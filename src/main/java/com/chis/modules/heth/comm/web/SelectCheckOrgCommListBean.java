package com.chis.modules.heth.comm.web;

import com.chis.common.utils.JsfUtil;
import com.chis.common.utils.SpringContextHolder;
import com.chis.common.utils.StringUtils;
import com.chis.common.utils.ZoneUtil;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesSimpleBean;
import org.primefaces.context.RequestContext;

import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>类描述：考核机构弹出框</p>
 * @ClassAuthor qrr,2021年6月28日,SelectCheckOrgListBean
 * */
@ManagedBean(name = "selectCheckOrgCommListBean")
@ViewScoped
public class SelectCheckOrgCommListBean extends FacesSimpleBean {

    private static final long serialVersionUID = -738445454536625063L;

    private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
    /** 名称 或 拼音码*/
    private String searchName;
    /** 查询条件-地区*/
    private List<TsZone> zoneList;
    private String searchZoneCode;
    private String searchZoneName;
    private Object[] selectPro;
    /**考核类型*/
    private String checkType;

	/**
	 * 弹框标题名称
	 */
	private String title;

	/**
	 * 弹框单位查询条件和列表行的名称
	 */
	private String searchUnitName;
    
    public SelectCheckOrgCommListBean() {
    	this.ifSQL = true;
    	TsZone tsZone = Global.getUser().getTsUnit().getFkByManagedZoneId();
    	if (null==tsZone) {
    		tsZone = Global.getUser().getTsUnit().getTsZone();
		}
		if(null == this.zoneList || this.zoneList.size() <=0) {
			this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true,null,null);
		}
		this.searchZoneCode = this.zoneList.get(0).getZoneGb();
		this.searchZoneName = this.zoneList.get(0).getZoneName();
		this.checkType = JsfUtil.getRequest().getParameter("checkType");
		this.searchUnitName = JsfUtil.getRequest().getParameter("searchUnitName");
		this.searchAction();
    }
    @Override
	public String[] buildHqls() {
    	StringBuffer sql = new StringBuffer();
		sql.append(" SELECT  T1.RID,CASE WHEN T2.ZONE_TYPE >2 THEN SUBSTR(T2.FULL_NAME, INSTR(T2.FULL_NAME,'_')+1) ELSE T2.FULL_NAME END ZONE_NAME,T1.UNITNAME");
		if(StringUtils.isNotBlank(this.checkType)){
			sql.append(",T.ORG_ADDR,T2.RID as zoneId ");
		}
		if ("1".equals(this.checkType)) {
			sql.append(" FROM TD_ZW_TJORGINFO T ");
		}else if ("2".equals(this.checkType)) {
			sql.append(" FROM TD_ZW_DIAGORGINFO T ");
		}else if ("3".equals(this.checkType)) {
			sql.append(" FROM TD_ZW_SRVORGINFO T ");
		}else if ("4".equals(this.checkType)) {
			sql.append(" FROM TD_ZW_OCCHETH_INFO T ");
		}
		sql.append(" LEFT JOIN TS_UNIT T1 ON T1.RID = T.ORG_ID ");
		sql.append(" LEFT JOIN TS_ZONE T2 ON T2.RID = T1.ZONE_ID ");
		sql.append(" WHERE T.STATE = 1 ");
		if(StringUtils.isNotBlank(this.searchZoneCode)){
			sql.append(" AND T2.ZONE_GB LIKE '").append(ZoneUtil.zoneSelect(this.searchZoneCode)).append("%'");
		}
		if (StringUtils.isNotBlank(searchName)) {
			sql.append(" AND T1.UNITNAME LIKE :searchName escape '\\\'");
			this.paramMap.put("searchName", "%"+StringUtils.convertBFH(this.searchName.trim())+"%");
		}
		sql.append(" ORDER BY T2.ZONE_GB,T1.UNITNAME");
		String h2 = "SELECT COUNT(*) FROM (" + sql.toString() + ")";
        String h1 = "SELECT * FROM (" + sql.toString() + ")";
        return new String[]{h1,h2};
	}
    /**
 	 * <p>方法描述：提交</p>
 	 * @MethodAuthor qrr,2019年12月2日,submitAction
     * */
    public void submitAction() {
    	if (selectPro == null) {
			JsfUtil.addErrorMessage("请选择单位！");
			return;
		}
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("selected", selectPro);
		RequestContext.getCurrentInstance().closeDialog(map);
	}
    /**
 	 * <p>方法描述：关闭</p>
 	 * @MethodAuthor qrr,2018年4月9日,dialogClose
     */
    public void dialogClose() {
        RequestContext.getCurrentInstance().closeDialog(null);
    }
	public String getSearchName() {
		return searchName;
	}
	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}
	public List<TsZone> getZoneList() {
		return zoneList;
	}
	public String getSearchZoneCode() {
		return searchZoneCode;
	}
	public String getSearchZoneName() {
		return searchZoneName;
	}
	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}
	public void setSearchZoneCode(String searchZoneCode) {
		this.searchZoneCode = searchZoneCode;
	}
	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}
	public Object[] getSelectPro() {
		return selectPro;
	}
	public void setSelectPro(Object[] selectPro) {
		this.selectPro = selectPro;
	}

	public String getCheckType() {
		return checkType;
	}

	public void setCheckType(String checkType) {
		this.checkType = checkType;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getSearchUnitName() {
		return searchUnitName;
	}

	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}
}
