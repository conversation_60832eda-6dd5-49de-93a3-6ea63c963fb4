package com.chis.modules.heth.comm.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.chis.common.utils.*;
import com.chis.modules.heth.comm.entity.*;
import com.chis.modules.heth.comm.javabean.TdTjBhkInfoBean;
import com.chis.modules.heth.comm.logic.TjPersonSearchConditionPO;
import com.chis.modules.heth.comm.service.HethStaQueryCommServiceImpl;
import com.chis.modules.heth.comm.service.TdZdzybCommServiceImpl;
import com.chis.modules.system.entity.TdTjExport;
import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.interfaces.IProcessData;
import com.chis.modules.system.logic.SessionData;
import com.chis.modules.system.service.CommServiceImpl;
import com.chis.modules.system.service.SystemModuleServiceImpl;
import com.chis.modules.system.utils.Global;
import com.chis.modules.system.web.FacesEditBean;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.primefaces.component.outputpanel.OutputPanel;
import org.primefaces.context.RequestContext;
import org.primefaces.event.SelectEvent;
import org.primefaces.model.CheckboxTreeNode;
import org.primefaces.model.TreeNode;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.faces.bean.ManagedBean;
import javax.faces.bean.ViewScoped;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;
/**
 * <p>类描述：个案查询</p>
 * @ClassAuthor qrr,2020年4月22日,TbTjPersonSearchBean
 * */
@ManagedBean(name = "tbTjPersonSearchBean")
@ViewScoped
public class TbTjPersonSearchBean extends FacesEditBean implements IProcessData{
	private static final long serialVersionUID = -6289268801372060938L;

	/**
	 * 存在session中的对象
	 */
	private SessionData sessionData = (SessionData) JsfUtil.getSessionMap().get(SessionData.SESSION_DATA);
	/**
	 * 查询统计会话bean
	 */
	private HethStaQueryCommServiceImpl hethStaQueryServiceImpl = SpringContextHolder
			.getBean(HethStaQueryCommServiceImpl.class);
	/**
	 * ejb session bean
	 */
	private SystemModuleServiceImpl systemModuleService = (SystemModuleServiceImpl) SpringContextHolder
			.getBean(SystemModuleServiceImpl.class);
	private CommServiceImpl commService = (CommServiceImpl) SpringContextHolder.getBean(CommServiceImpl.class);
	private TdZdzybCommServiceImpl analyServiceImpl = SpringContextHolder.getBean(TdZdzybCommServiceImpl.class);

	private TjPersonSearchConditionPO conditionPO;
    private String searchUnitName;
	/***查询条件-用人单位地区*/
	private List<TsZone> zoneList;
	private List<TsZone> crptZoneList;
	/***查询条件-用人单位地区名称*/
	private String searchZoneName;
	/**查询条件-在岗状态*/
	private List<TsSimpleCode> onGuardList;
	private String selectOnGuardNames;
	/**查询条件-危害因素*/
	private List<TsSimpleCode> badRsnList;
	private String selectBadRsnNames;
	/**查询条件-已选择的年龄*/
    private TreeNode ageSortTree;
    private TreeNode[] selectAges;
    private String selectAgeName;
    /**查询条件-已选择的工龄*/
    private TreeNode workSortTree;
    private String selectWorkName;
    private TreeNode[] selectWorkAges;
    /**查询条件：选择的主检结论名称*/
    private List<TsSimpleCode> searchBhkrstList;
    private String searchBhkrstName;
	/**初检的数据*/
	private List<TdTjBhk> tdTjBhkList;
	/*** 主表集合的最新表 */
	private TdTjBhk tdTjBhk;
	/**
	 * 集合数量
	 */
	public int tdTjBhkCount;
	/**
	 * 查询首页信息传递
	 */
	private Integer personRid;
	/**
	 * 体检主表rid
	 */
	private Integer tdTjBhkRid;
	/**
	 * 体检项目的布局表格
	 */
	private OutputPanel archivePanel = (OutputPanel) JsfUtil.getApplication().createComponent(
			OutputPanel.COMPONENT_TYPE);
	/**
	 * 历次职业病集合
	 */
	private List<TdTjSupoccdiselist> tdTjSupoccdiselists;
	/**
	 * 历次禁忌症集合
	 */
	private List<TdTjContraindlist> tdTjContraindlists;
	/**
	 * 本次职业病集合
	 */
	private List<TdTjSupoccdiselist> btdTjSupoccdiselists;
	/**
	 * 本次禁忌症集合
	 */
	private List<TdTjContraindlist> btdTjContraindlists;

	private String exportFileName;
	
	
    /** 体检项目缓存 */
    private Map<Integer, String> itemCachMap = new HashMap<Integer, String>();
    private Map<Integer, TbTjItems> itemCachMaps = new HashMap<Integer, TbTjItems>();
	/** 胸片项目数 */
    Integer xpNum ;

    /**菜单授权的菜单树*/
    private TreeNode exportTreeNode;
    /**菜单授权的已选择的树*/
    private TreeNode[] selectedExportNodes;
    /** 过滤体检项目ID */
    private String exportItems;
    /** 过滤体检项目ID */
    private String selectExportNames;
    /** 已选择过滤项目集合 */
    private Set<String> selectExportItemSet = new HashSet<String>();
    
    /** 第一层集合 */
    private Set<String> firstLevelNoSet;
    /** 体检项目编码 */
    private Map<String, TbTjItems> codeMap = new HashMap<String, TbTjItems>();
    //非第一层集合
    private Set<String> levelNoSet;
    
    /**查询出来主表所有的集合*/
    private List<TdTjBhk> alltdTjBhkList;
    
    /**初检对象*/
    private Integer firstRid;
    /**存放复检数据*/
    private List<TdTjBhk> tdTjBhkFjList = new ArrayList<>();;
    /**以初检id为键，以复检对象为值存入map中*/
    Map<Integer,List<TdTjBhk>> fjmap = new HashMap<>();

    /**是否质控员*/
    private boolean ifAdmin;
    private TdTjBhkInfoBean tjBhkInfoBean = new TdTjBhkInfoBean();
    
    //内部导出
    private boolean ifNbExport;
    //导出标记 1：导出，2：内部导出
    private Integer exportFlag;
    private String searchItemNames;
    /**证件类型*/
    private List<TsSimpleCode> cardTypeList;
    /**正在导出次数限制参数配置*/
    private Integer  asyncExportTimes;
    /**导出数据对象*/
    private TdTjExport tdTjExport;
    /**d导出类型码表*/
	private Map<String,TsSimpleCode> busTypeMap;
	/**导出信息的拼接：rid,codeName*/
	private Map<Integer,String> tsSimpleCodeMap;
	@PostConstruct
	public void init() {
		if (this.sessionData.getBtnSet().contains("heth_comm_gacx_nbexport")) {
            this.ifNbExport = true;
        }else{
        	 this.ifNbExport = false;
        }
		this.ifSQL = true;
		tsSimpleCodeMap =new HashMap<>();
		this.searchBhkrstList = commService.findSimpleCodesByTypeId("5005");
		if(!CollectionUtils.isEmpty(searchBhkrstList)){
			for(TsSimpleCode t:searchBhkrstList){
				tsSimpleCodeMap.put(t.getRid(),t.getCodeName());
			}
		}
		this.onGuardList = commService.findSimpleCodesByTypeId("5009");
		if(!CollectionUtils.isEmpty(onGuardList)){
			for(TsSimpleCode t:onGuardList){
				tsSimpleCodeMap.put(t.getRid(),t.getCodeName());
			}
		}
		this.badRsnList = commService.findSimpleCodesByTypeId("5007");
		if(!CollectionUtils.isEmpty(badRsnList)){
			for(TsSimpleCode t:badRsnList){
				tsSimpleCodeMap.put(t.getRid(),t.getCodeName());
			}
		}
		this.cardTypeList = commService.findSimpleCodesByTypeId("5503");
		if(!CollectionUtils.isEmpty(cardTypeList)){
			for(TsSimpleCode t:cardTypeList){
				tsSimpleCodeMap.put(t.getRid(),t.getCodeName());
			}
		}
		//导出类型码表
		List<TsSimpleCode> simpleCodes = commService.findLevelSimpleCodesByTypeId("5550");
		busTypeMap =new HashMap<>();
		if(!CollectionUtils.isEmpty(simpleCodes)){
			for(TsSimpleCode t:simpleCodes){
				if(StringUtils.isNotBlank(t.getExtendS1())){
					busTypeMap.put(t.getExtendS1(),t);
				}
			}
		}
		this.ifAdmin = this.sessionData.getUser().isIfAdmin();

		conditionPO = new TjPersonSearchConditionPO();
		conditionPO.setSearchEndTime(new Date());
		conditionPO.setSearchStartTime(DateUtils.addYears(new Date(), -1));
		initZone();
		initAgeSortTree();
		initWorkSortTree();
		
		this.initTjItem();
        if (!ifAdmin) {//非质控员
			this.initTjSvorg();
			if (StringUtils.isBlank(this.conditionPO.getSearchUnitId())) {//资质未注册
				return;
			}
		}
        //胸片项目数
		xpNum = 0;
//		this.searchAction();
		//正在导出次数限制参数配置
		 asyncExportTimes = PropertyUtils.getValue("asyncExport.times") == null ? 0 : Integer.valueOf(PropertyUtils.getValue("asyncExport.times"));
	}
	private void initTjItem()	{
    	List<TsSimpleCode> codeList = this.commService.findLevelSimpleCodesByTypeId("5008");
		if(!CollectionUtils.isEmpty(codeList))	{
			firstLevelNoSet = new LinkedHashSet<String>();
			// 项目分类导出
			codeMap = new HashMap<String, TbTjItems>();
			for (TsSimpleCode tsSimpleCode : codeList) {
				if (StringUtils.containsNone(tsSimpleCode.getCodeLevelNo(), ".")) {
					continue;
				}
				TbTjItems itm = new TbTjItems();
				itm.setRid(-1);
				itm.setItemName(tsSimpleCode.getCodeName());
				itm.setLevelCode(tsSimpleCode.getCodeLevelNo());
				firstLevelNoSet.add(tsSimpleCode.getCodeLevelNo());
				codeMap.put(tsSimpleCode.getCodeLevelNo(), itm);
			}
			  
	        //非第一层集合
	        levelNoSet = new LinkedHashSet<String>();
	        List<TbTjItems> findTjItemList = hethStaQueryServiceImpl.findTjItemList(null, null, Short.valueOf("1"), null, null);
	        if( null != findTjItemList && findTjItemList.size() > 0 )	{
	        	for(  TbTjItems tbTjItems : findTjItemList )	{
	        		
	        		itemCachMap.put(tbTjItems.getRid(), tbTjItems.getItemName());
	        		itemCachMaps.put(tbTjItems.getRid(), tbTjItems);

	        		//项目层级编码
	        		String codeLevelNo = tbTjItems.getTsSimpleCode().getCodeLevelNo();
	        		
	        		tbTjItems.setLevelCode(new StringBuffer(codeLevelNo).append(".").append(tbTjItems.getItemCode()).toString());
	        		levelNoSet.add(tbTjItems.getLevelCode());
	        		
	        		codeMap.put(tbTjItems.getLevelCode(), tbTjItems);
	        	}
	        }
			
		}
         
    }
	/**
 	 * <p>方法描述：初始化地区</p>
 	 * @MethodAuthor qrr,2020年4月23日,initZone
	 * */
	private void initZone() {
		// 地区信息初始化
		TsZone tsZone = this.sessionData.getUser().getTsUnit().getFkByManagedZoneId();
		if (null==tsZone) {
			tsZone = this.sessionData.getUser().getTsUnit().getTsZone();
		}
		// 地区初始化
		if (ObjectUtil.isEmpty(this.zoneList)) {
			if (this.ifAdmin) {
				//质控员
				this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb(), true, "", "");
				//默认显示当前登录人单位业务管辖地区
				this.conditionPO.setSearchEntrustCrptZoneGb(tsZone.getZoneGb());
				this.conditionPO.setSearchEntrustCrptZoneName(tsZone.getZoneName());
			} else {
				this.zoneList = this.commService.findZoneListByGbAndTypeNoNation(tsZone.getZoneGb().substring(0,2), true, "", "");
				//默认当前省份
				this.conditionPO.setSearchEntrustCrptZoneGb(this.zoneList.get(0).getZoneGb());
				this.conditionPO.setSearchEntrustCrptZoneName(this.zoneList.get(0).getZoneName());
			}
		}
		//用人单位地区显示全部启用地区
		this.conditionPO.setSearchZoneCode("");
		this.searchZoneName = "";
		this.crptZoneList = this.commService.findZoneListByGbAndTypeNoNation("", true, "", "");
	}
	/**
 	 * <p>方法描述：</p>
 	 * @MethodAuthor qrr,2020年4月22日,initTjSvorg
	 * */
    private void initTjSvorg() {
		List<Object[]> list = hethStaQueryServiceImpl.findTjSvorgByRegUnitId(Global.getUser().getTsUnit().getRid());
		if (null!=list && list.size()>0) {
			Object[] obj = list.get(0);
			if (null!=obj[0]) {
				this.conditionPO.setSearchUnitId(obj[0].toString());
				this.searchUnitName = null!=obj[1]?obj[1].toString():null;
			}
		}
	}
    /**
 	 * <p>方法描述：初始化年龄</p>
 	 * @MethodAuthor qrr,2020年4月22日,initWorkSortTree
     * */
    private void initAgeSortTree() {
    	List<TdZdzybAnalyDetailComm> list = analyServiceImpl.findDetailListByAnalyType(1, 5);
    	this.ageSortTree = new CheckboxTreeNode("root", null);
    	this.initSortTree(list, this.ageSortTree);
    }
    /**
 	 * <p>方法描述：初始化工龄</p>
 	 * @MethodAuthor qrr,2020年4月22日,initWorkSortTree
     * */
    private void initWorkSortTree() {
    	List<TdZdzybAnalyDetailComm> list = analyServiceImpl.findDetailListByAnalyType(1, 6);
    	this.workSortTree = new CheckboxTreeNode("root", null);
    	this.initSortTree(list, this.workSortTree);
    }
    /**
	 * 初始化树
	 */
	private void initSortTree(List<TdZdzybAnalyDetailComm> list,TreeNode sortTree) {
		if (null != list && list.size() > 0) {
			for (TdZdzybAnalyDetailComm detail : list) {
				new CheckboxTreeNode(detail, sortTree);
			}
		}

	}
	
	//清空选择
	public void clearExportAction(){
		
		this.selectedExportNodes = null;
		this.selectExportNames = null;
		this.exportItems = null;
		
		selectExportItemSet.clear();
	}
	/**
	 * 导出项目
	 */
	public void exportItemAction() {
		exportItems = null;
		selectExportNames = null;
		
		//临时体检项目名称
		List<TbTjItems> items = new ArrayList<TbTjItems>();
		
		if(null != this.selectedExportNodes && this.selectedExportNodes.length > 0) {
			for(TreeNode node:this.selectedExportNodes) {
				this.chooseExportMenuIds(node,items);
			}
			compareTbTjItems(items);
		}else {
			if(null != this.exportTreeNode) {
				List<TreeNode> childs = exportTreeNode.getChildren();
				if (null!=childs && childs.size()>0) {
					for (TreeNode node : childs) {
						List<TreeNode> lastNodes = node.getChildren();
						if (CollectionUtils.isEmpty(lastNodes)) {// 最末级
							TbTjItems data = (TbTjItems)node.getData();
							if( null != data.getRid() &&  data.getRid().intValue() != -1)	{
								items.add(data);
							}
						}else {
							for (TreeNode last : lastNodes) {
								TbTjItems data = (TbTjItems)last.getData();
								if( null != data.getRid() &&  data.getRid().intValue() != -1)	{
									items.add(data);
								}
							}
						}
					}
				}
			}
		}
		//临时体检项目ids
		if (!CollectionUtils.isEmpty(items)) {
			StringBuilder tempStr = new StringBuilder();
			StringBuilder tempNameStr = new StringBuilder();
			for (TbTjItems t : items) {
				tempStr.append(",").append(t.getRid());
				tempNameStr.append(",").append(t.getItemName());
			}
			exportItems = tempStr.substring(1);
			selectExportNames = tempNameStr.substring(1); 
		}
		if(null != this.selectedExportNodes && this.selectedExportNodes.length > 0 && StringUtils.isNotEmpty(selectExportNames)) {
			conditionPO.setItemNames(selectExportNames.replaceAll(",","，"));
		}else{
			conditionPO.setItemNames(null);
		}
	}
	/**
 	 * <p>方法描述：选择体检项目，输入框显示名称排序</p>
	 * Comparison method violates its general contract! 参考https://www.cnblogs.com/firstdream/p/7204067.html
 	 * @MethodAuthor qrr,2020年5月20日,compareTbTjItems
	 * */
	private void compareTbTjItems(List<TbTjItems> items) {
		if(CollectionUtils.isEmpty(items)){
			return;
		}
		Collections.sort(items, new Comparator<TbTjItems>() {
			@Override
			public int compare(TbTjItems o1, TbTjItems o2) {
				TsSimpleCode t1 = o1.getTsSimpleCode();
				TsSimpleCode t2 = o2.getTsSimpleCode();
				if (null!=t1 && null!=t2) {
					Integer num1 = t1.getNum();
					Integer num2 = t2.getNum();
					if (null!=num1 && null!=num2) {
						int val = num1.compareTo(num2);
						if (val != 0) {
							return val;
						}
					}else {
						int val = String.valueOf(t1.getCodeLevelNo()).equals(String.valueOf(t2.getCodeLevelNo()))
								? 0 : String.valueOf(t1.getCodeLevelNo())
								.compareTo(String.valueOf(t2.getCodeLevelNo()));
						if (val != 0) {
							return String.valueOf(t1.getCodeLevelNo()).compareTo(
									String.valueOf(t2.getCodeLevelNo()));
						}
					}
				}
				Integer itemNum1 = o1.getNum();
				Integer itemNum2 = o2.getNum();
				if (null!=itemNum1 && null!=itemNum2) {
					int result = itemNum1.compareTo(itemNum2);
					if(0 != result){
						return result;
					}
				}else if(null != itemNum1 && null == itemNum2){
					return 1;
				}else if(null != itemNum2 && null == itemNum1){
					return -1;
				}
				String itemCode1 = o1.getItemCode();
				String itemCode2 = o2.getItemCode();
				if(null != itemCode1 && null != itemCode2){
					return itemCode1.compareTo(itemCode2);
				}else if(null != itemCode1 && null == itemCode2){
					return 1;
				}else if(null != itemCode2 && null == itemCode1){
					return -1;
				}
				return 0;
			}
			
		});
	}
    /**
     * 根据树节点，将其与其父节点的对象ID放到集合中
     * @param node 树节点，内容存的是菜单对象
     */
    private void chooseExportMenuIds(TreeNode node,List<TbTjItems> items) {
    	if(null != node && !(node.equals(this.exportTreeNode))) {
    		TbTjItems data = (TbTjItems)node.getData();
    		if( null != data.getRid() &&  data.getRid().intValue() != -1)	{
    			items.add(data);
    		}
    		this.chooseExportMenuIds(node.getParent(),items);
    	}
    }

	/**
	 * 体检项目查询条件初始化
	 */
	public void tjExportItemAction() {
        if (!ifAdmin) {//非质控员
            this.initTjSvorg();
            if (StringUtils.isBlank(this.conditionPO.getSearchUnitId())) {//资质未注册
                JsfUtil.addErrorMessage("导出失败，资质未注册！");
                return;
            }
        }
		//验证是否达到次数
		String  typeId = busTypeMap.get("1") == null ? null : busTypeMap.get("1").getRid().toString();
		if(StringUtils.isBlank(typeId)){
			JsfUtil.addErrorMessage("导出失败，导出类型不存在！");
			return;
		}
		Integer tdTjExportCount = this.hethStaQueryServiceImpl.findTdTjExportByOperPsnId(typeId);
		if(tdTjExportCount >= asyncExportTimes){
			JsfUtil.addErrorMessage("导出次数达到上限，请稍后导出！");
			return;
		}
		this.selectedExportNodes = null;
		this.exportTreeNode = new CheckboxTreeNode("root", null);
		if (firstLevelNoSet.size() > 0 && codeMap.size() > 0) {
			for (String ln : firstLevelNoSet) {
				TbTjItems tbTjItems = codeMap.get(ln);
				TreeNode node = new CheckboxTreeNode(tbTjItems, this.exportTreeNode);
				
				this.addExportChildNode(ln, levelNoSet, codeMap, node, null);
			}
		}
		RequestContext.getCurrentInstance().execute("PF('ExportItemDialog').show()");
	}

    /**
     * 构建菜单树
     * @param levelNo 菜单层级编码
     * @param levelNoSet 二级以及以上的菜单的层级编码集合
     * @param parentNode 上级树节点
     * @param selectedMenuMap 已有的菜单map
     */
    private void addExportChildNode(String levelNo, Set<String> levelNoSet, Map<String,TbTjItems> itemMap, TreeNode parentNode, Map<Integer, Integer> selectedMenuMap) {
    	int level = StringUtils.countMatches(levelNo, ".");
    	for(String ln: levelNoSet) {
    		if(StringUtils.countMatches(ln, ".")==(level+1) && StringUtils.startsWith(ln,levelNo+".")) {
    			TbTjItems t = itemMap.get(ln);
    			TreeNode node = new CheckboxTreeNode(t, parentNode);
    			if (selectExportItemSet.contains(t.getLevelCode())) {
    				node.setSelected(true);
    			}
    			this.addExportChildNode(ln, levelNoSet, itemMap, node, selectedMenuMap);
    		}
    	}
    }

    
	@Override
	public void searchAction()	{
		Date searchStartTime = conditionPO.getSearchStartTime();
		Date searchEndTime = conditionPO.getSearchEndTime();
		if (null != searchStartTime && null != searchEndTime && searchStartTime.after(searchEndTime)) {
			JsfUtil.addErrorMessage("体检开始日期不允许大于体检结束日期！");
			return;
		}
		if (!ifAdmin) {//非质控员
			if (StringUtils.isBlank(conditionPO.getSearchUnitId())) {//资质未注册
				return;
			}
		}
		super.searchAction();
	}
	/**
 	 * <p>方法描述：初始化查询条件sql</p>
 	 * @MethodAuthor qrr,2020年5月20日,initSearchConditionSql
	 * */
	private String initSearchConditionSql() {
		StringBuffer sb = new StringBuffer();
		sb.append(" FROM TD_TJ_BHK T1 ");
		sb.append(" INNER JOIN TD_TJ_PERSON T2 ON T2.RID = T1.PERSON_ID");
		sb.append(" INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID");
		sb.append(" INNER JOIN TS_ZONE T6 ON T6.RID = crpt.ZONE_ID");
		sb.append(" INNER JOIN TB_TJ_CRPT crpt1 ON crpt1.RID = T1.ENTRUST_CRPT_ID");
		sb.append(" INNER JOIN TS_ZONE T7 ON T7.RID = crpt1.ZONE_ID");
		sb.append(" WHERE 1=1 AND crpt.INTER_PRC_TAG = 1");
		sb.append(" AND T1.BHK_TYPE IN (3,4)");
		if (StringUtils.isNotBlank(conditionPO.getSearchPersonName())) {
			sb.append(" AND T2.PERSON_NAME LIKE :searchPersonName escape '\\\'");
			this.paramMap.put("searchPersonName", "%"+StringUtils.convertBFH(conditionPO.getSearchPersonName().trim())+"%");
		}
		String searchPsnType = conditionPO.getSearchPsnType();
		if (StringUtils.isNotBlank(searchPsnType)) {
			sb.append(" AND T2.CARD_TYPE_ID = :cardTypeId");
			this.paramMap.put("cardTypeId", searchPsnType);
		}
		if (StringUtils.isNotBlank(conditionPO.getSearchIDC())) {
			sb.append(" AND T2.IDC = :searchIDC");
			this.paramMap.put("searchIDC", conditionPO.getSearchIDC());
		}
		//是否管理员
		if (!ifAdmin) {
			sb.append(" AND T1.BHKORG_ID = :bhkorgId");//本机构
			this.paramMap.put("bhkorgId", StringUtils.convertBFH(conditionPO.getSearchUnitId().trim()));
		}else {
			//体检机构
			if (StringUtils.isNotBlank(conditionPO.getSearchUnitId()) ) {
				sb.append(" AND T1.BHKORG_ID IN (:bhkorgId)");
				this.paramMap.put("bhkorgId", StringUtils.string2list(conditionPO.getSearchUnitId(),","));
			}
		}
		//年龄
		List<TdZdzybAnalyDetailComm> selectAgeAnalyDetails = conditionPO.getSelectAgeAnalyDetails();
		if (null!=selectAgeAnalyDetails && selectAgeAnalyDetails.size()>0) {
			List<String> detailAge = new ArrayList();
			int count=0;
			for (TdZdzybAnalyDetailComm detailAnaly : selectAgeAnalyDetails) {
				List<String>  detail = new ArrayList();
				//0-10 20-30
				Integer geNum = detailAnaly.getGeNum();
				if (null!=geNum) {
					detail.add("T1.AGE >=:geAgeNum"+count);
					this.paramMap.put("geAgeNum"+count, geNum);
				}
				Integer gtNum = detailAnaly.getGtNum();
				if (null!=gtNum) {
					detail.add(" T1.AGE >:gtAgeNum"+count);
					this.paramMap.put("gtAgeNum"+count, gtNum);
				}
				Integer leNum = detailAnaly.getLeNum();
				if (null!=leNum) {
					detail.add(" T1.AGE <=:leAgeNum"+count);
					this.paramMap.put("leAgeNum"+count, leNum);
				}
				Integer ltNum = detailAnaly.getLtNum();
				if (null!=ltNum) {
					detail.add(" T1.AGE <:ltAgeNum"+count);
					this.paramMap.put("ltAgeNum"+count, ltNum);
				}
				if(!CollectionUtils.isEmpty(detail)){
					detailAge.add(" ( " +StringUtils.list2string(detail,"  AND ")+" )");
				}else{
					detailAge.add(" (  T1.AGE is null )");
				}
				count++;
			}
			if(!CollectionUtils.isEmpty(detailAge)){
				sb.append(" AND ( ").append(StringUtils.list2string(detailAge,"  or ")).append(" )");
			}

		}
		//工龄
		List<TdZdzybAnalyDetailComm> selectWorkAnalyDetails = conditionPO.getSelectWorkAnalyDetails();
		if (null!=selectWorkAnalyDetails && selectWorkAnalyDetails.size()>0) {
			StringBuffer detail = new StringBuffer();
			int count=0;
			for (TdZdzybAnalyDetailComm detailAnaly : selectWorkAnalyDetails) {
				detail.append(" or (");
				if(null== detailAnaly.getGeNum() && null == detailAnaly.getGtNum() && null == detailAnaly.getLeNum() && null == detailAnaly.getLtNum()){
					detail.append(" T1.TCHBADRSNTIM is null ) ");
					continue;
				}
				Integer geNum = detailAnaly.getGeNum();
				if (null!=geNum) {
					detail.append(" T1.TCHBADRSNTIM >=:geNum").append(count);
					this.paramMap.put("geNum"+count, geNum);
				}
				Integer gtNum = detailAnaly.getGtNum();
				if (null!=gtNum) {
					if (null!=geNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.TCHBADRSNTIM >:gtNum").append(count);
					this.paramMap.put("gtNum"+count, gtNum);
				}
				Integer leNum = detailAnaly.getLeNum();
				if (null!=leNum) {
					if (null!=geNum||null!=gtNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.TCHBADRSNTIM <=:leNum").append(count);
					this.paramMap.put("leNum"+count, leNum);
				}
				Integer ltNum = detailAnaly.getLtNum();
				if (null!=ltNum) {
					if (null!=geNum||null!=gtNum||null!=leNum) {
						detail.append(" AND ");
					}
					detail.append(" T1.TCHBADRSNTIM <:ltNum").append(count);
					this.paramMap.put("ltNum"+count, ltNum);
				}
				detail.append(")");
				count++;
			}
			sb.append(" AND (").append(detail.substring(3)).append(")");
		}
		//用工单位地区
		String searchEntrustCrptZoneGb = conditionPO.getSearchEntrustCrptZoneGb();
		if (StringUtils.isNotBlank(searchEntrustCrptZoneGb)) {
			sb.append(" AND T7.ZONE_GB LIKE :empZoneGb escape '\\\'");
			this.paramMap.put("empZoneGb", ZoneUtil.zoneSelect(searchEntrustCrptZoneGb.trim())+"%");
		}
		//用工单位名称
		String searchEntrustCrptName = conditionPO.getSearchEntrustCrptName();
		if (StringUtils.isNotBlank(searchEntrustCrptName)) {
			sb.append(" AND crpt1.CRPT_NAME LIKE :searchEntrustCrptName escape '\\\'");
			this.paramMap.put("searchEntrustCrptName", "%"+StringUtils.convertBFH(searchEntrustCrptName.trim())+"%");
		}
		//用人单位地区
		if (ObjectUtil.isNotEmpty(conditionPO.getSearchZoneCode())) {
			String searchZoneCode = conditionPO.getSearchZoneCode();
			if (StringUtils.isNotBlank(searchZoneCode)) {
				sb.append(" AND T6.ZONE_GB LIKE :zone_gb escape '\\\'");
				this.paramMap.put("zone_gb", ZoneUtil.zoneSelect(searchZoneCode)+"%");
			}
		}
		//用人单位名称
		if (ObjectUtil.isNotEmpty(conditionPO.getSearchCrptName())) {
			String searchCrptName = conditionPO.getSearchCrptName();
			if (StringUtils.isNotBlank(searchCrptName)) {
				sb.append(" AND crpt.CRPT_NAME LIKE :searchCrptName escape '\\\'");
				this.paramMap.put("searchCrptName", "%"+StringUtils.convertBFH(searchCrptName.trim())+"%");
			}
		}
		//体检类型
		String[] searchBhkType = conditionPO.getSearchBhkType();
		if (ObjectUtil.isNotEmpty(searchBhkType) && searchBhkType.length == 1) {
			if("3".equals(searchBhkType[0])){
				//仅勾选“职业健康检查”时，调整为查询 “体检类型 BHK_TYPE”为 3和4 ，并且“是否纯放射”为“0：否”或空的数据
				sb.append(" AND (T1.IF_ONLY_FS  IS NULL OR T1.IF_ONLY_FS = 0)");
			}else {
				//仅勾选“放射卫生健康检查”则查询体检类型为4的数据
				sb.append(" AND T1.BHK_TYPE =:bhk_type");
				this.paramMap.put("bhk_type",  searchBhkType[0]);
			}
		}
		//体检日期
		Date searchStartTime = conditionPO.getSearchStartTime();
		if ( null != searchStartTime) {
			sb.append(" AND T1.BHK_DATE >= TO_DATE(:searchStartTime,'yyyy-MM-dd')");
			this.paramMap.put("searchStartTime", DateUtils.formatDate(searchStartTime, "yyyy-MM-dd"));
		}
		Date searchEndTime = conditionPO.getSearchEndTime();
		if ( null != searchEndTime) {
			sb.append(" AND T1.BHK_DATE <= TO_DATE(:searchEndTime,'yyyy-MM-dd HH24:mi:ss')");
			this.paramMap.put("searchEndTime", DateUtils.formatDate(searchEndTime, "yyyy-MM-dd")+" 23:59:59");
		}
		//在岗状态
		String selectOnGuardIds = conditionPO.getSelectOnGuardIds();
		if (StringUtils.isNotBlank(selectOnGuardIds)) {
			sb.append(" AND T1.ONGUARD_STATEID IN (:selectOnGuardIds)");
			this.paramMap.put("selectOnGuardIds", StringUtils.string2list(selectOnGuardIds,","));
		}

		//体检危害因素
		String selectBadRsnIds = conditionPO.getSelectBadRsnIds();
		//危害因素结论
		String searchSelBhkrstIds = conditionPO.getSearchSelBhkrstIds();
		if(StringUtils.isNotBlank(selectBadRsnIds) && StringUtils.isNotBlank(searchSelBhkrstIds)){
			sb.append(" AND EXISTS ( ");
			sb.append("  SELECT 1 from TD_TJ_BADRSNS badRsn where badRsn.BHK_ID=T1.RID ")
					.append(" AND EXISTS(SELECT 1 FROM TEMP_TABLE TEMP WHERE badRsn.BADRSN_ID = TEMP.ID)  ")
					.append(" AND badRsn.EXAM_CONCLUSION_ID IN (:searchSelBhkrstIds)");
			sb.append(") ");
			this.paramMap.put("searchSelBhkrstIds", StringUtils.string2list(searchSelBhkrstIds,","));
		}else if (StringUtils.isNotBlank(selectBadRsnIds)) {
			sb.append(" AND EXISTS ( SELECT 1 from TD_TJ_BADRSNS badRsn where badRsn.BHK_ID=T1.RID ");
			sb.append(" AND EXISTS(SELECT 1 FROM TEMP_TABLE TEMP WHERE badRsn.BADRSN_ID = TEMP.ID) )");
		}else if (StringUtils.isNotBlank(searchSelBhkrstIds)) {
			sb.append(" AND EXISTS ( SELECT 1 FROM TD_TJ_BADRSNS BS WHERE BS.BHK_ID=T1.RID AND ");
			sb.append(" BS.EXAM_CONCLUSION_ID IN (:searchSelBhkrstIds) )");
			this.paramMap.put("searchSelBhkrstIds", StringUtils.string2list(searchSelBhkrstIds,","));
		}

		//主检结论
		String searchSelMhkrstIds = conditionPO.getSearchSelMhkrstIds();
		if (StringUtils.isNotBlank(searchSelMhkrstIds)) {
			sb.append(" AND EXISTS ( SELECT 1 FROM TD_TJ_MHKRST MR WHERE MR.BHK_ID=T1.RID AND ");
			sb.append(" MR.BHKRST_ID IN (:searchSelMhkrstIds) )");
			this.paramMap.put("searchSelMhkrstIds", StringUtils.string2list(searchSelMhkrstIds,","));
		}
		String searchItemIds = conditionPO.getSearchItemIds();
		if(StringUtils.isNotBlank(searchItemIds))	{
			sb.append(" AND EXISTS ( SELECT 1 from TD_TJ_BHKSUB TSUB where TSUB.BHK_ID=T1.RID ");
			String[] split = searchItemIds.split(",");
			sb.append(" AND( ");
			StringBuffer buffer = new StringBuffer();
			int count=0;
			for (String s : split) {
				String[] split2 = s.split("@@");
				buffer.append(" OR (TSUB.ITEM_ID =:itemIds").append(count);
				this.paramMap.put("itemIds"+count, split2[0]);
				if (split2.length>1) {
					buffer.append(" AND TSUB.RGLTAG =:rgltags").append(count);
					this.paramMap.put("rgltags"+count, split2[1]);
				}
				buffer.append(")");
				count++;
			}
			sb.append(buffer.substring(3)).append(")").append(")");
		}
		//监测类型
		String[] searchJcType = conditionPO.getSearchJcType();
		if (ObjectUtil.isNotEmpty(searchJcType)) {
			sb.append(" AND T1.JC_TYPE IN (:type)");
			this.paramMap.put("type", Arrays.asList(searchJcType));
		}
		//报告出具日期
		Date startRptPrintDate = conditionPO.getStartRptPrintDate();
		if ( null != startRptPrintDate) {
			sb.append(" AND T1.RPT_PRINT_DATE >= TO_DATE(:startRptPrintDate,'yyyy-MM-dd')");
			this.paramMap.put("startRptPrintDate", DateUtils.formatDate(startRptPrintDate, "yyyy-MM-dd"));
		}
		Date endRptPrintDate = conditionPO.getEndRptPrintDate();
		if ( null != endRptPrintDate) {
			sb.append(" AND T1.RPT_PRINT_DATE <= TO_DATE(:endRptPrintDate,'yyyy-MM-dd HH24:mi:ss')");
			this.paramMap.put("endRptPrintDate", DateUtils.formatDate(endRptPrintDate, "yyyy-MM-dd")+" 23:59:59");
		}
		//报告日期
		Date startCreateDate = conditionPO.getStartCreateDate();
		if ( null != startCreateDate) {
			sb.append(" AND T1.CREATE_DATE >= TO_DATE(:startCreateDate,'yyyy-MM-dd')");
			this.paramMap.put("startCreateDate", DateUtils.formatDate(startCreateDate, "yyyy-MM-dd"));
		}
		Date endCreateDate = conditionPO.getEndCreateDate();
		if ( null != endCreateDate) {
			sb.append(" AND T1.CREATE_DATE <= TO_DATE(:endCreateDate,'yyyy-MM-dd HH24:mi:ss')");
			this.paramMap.put("endCreateDate", DateUtils.formatDate(endCreateDate, "yyyy-MM-dd")+" 23:59:59");
		}
		return sb.toString();
	}
	/**
 	 * <p>方法描述：初始化countSql</p>
 	 * @MethodAuthor qrr,2020年5月20日,initCountSql
	 * */
	private String initCountSql() {
		StringBuffer sql = new StringBuffer();
		String searchUnitId = conditionPO.getSearchUnitId();
		String searchBhkNum = conditionPO.getSearchBhkNum();
		String selectBadRsnIds = conditionPO.getSelectBadRsnIds();
		if(StringUtils.isNotBlank(selectBadRsnIds)){
			sql.append(" WITH TEMP_TABLE AS ( ");
			sql.append(" SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID ");
			sql.append(" FROM (SELECT '").append(selectBadRsnIds).append("' IDS FROM DUAL) ");
			sql.append(" CONNECT BY LEVEL <= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1 ) ");
		}
		if (!ifAdmin && StringUtils.isNotBlank(searchBhkNum)) {
			sql.append(" SELECT COUNT(1) FROM (");
			sql.append(" SELECT T1.PERSON_ID");// /*+index(T1 INDEX_CRPTID)*/
			sql.append(initSearchConditionSql());
			sql.append(" GROUP BY T1.PERSON_ID");
			sql.append(")T ");
			sql.append(" INNER JOIN(");// 非质控员关联体检记录，过滤总档案数
			sql.append("SELECT /*+index(T1 INDEX_CRPTID)*/T1.PERSON_ID,COUNT(T1.RID) IDCS");
			sql.append(" FROM TD_TJ_BHK T1 ");
			sql.append(" INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID");
			sql.append(" WHERE  T1.BHK_TYPE IN (3,4)  AND crpt.INTER_PRC_TAG = 1");
			sql.append(" AND T1.BHKORG_ID =:searchUnitId");//本机构
			this.paramMap.put("searchUnitId", searchUnitId);
			sql.append(" GROUP BY T1.PERSON_ID");
			sql.append(")A ON A.PERSON_ID =T.PERSON_ID");
			sql.append(" where 1=1");
			sql.append(" AND A.IDCS >=:searchBhkNum");
			this.paramMap.put("searchBhkNum", searchBhkNum);
		}else {
			sql.append(" SELECT COUNT(DISTINCT T1.PERSON_ID) ");
			sql.append(initSearchConditionSql());
			if (StringUtils.isNotBlank(searchBhkNum)) {
				sql.append(" AND T2.ARCH_NUM >=:searchBhkNum");
				this.paramMap.put("searchBhkNum", searchBhkNum);
			}
		}
		return sql.toString();
	}
	/**
 	 * <p>方法描述：初始化selectSql</p>
 	 * @MethodAuthor qrr,2020年5月20日,initSelectSql
	 * */
	private String initSelectSql() {
		StringBuffer sql = new StringBuffer();
		String searchUnitId = conditionPO.getSearchUnitId();
		String searchBhkNum = conditionPO.getSearchBhkNum();
		String selectBadRsnIds = conditionPO.getSelectBadRsnIds();
		if(StringUtils.isNotBlank(selectBadRsnIds)){
			sql.append(" WITH TEMP_TABLE AS ( ");
			sql.append(" SELECT REGEXP_SUBSTR(IDS, '[^,]+', 1, LEVEL, 'i') AS ID ");
			sql.append(" FROM (SELECT '").append(selectBadRsnIds).append("' IDS FROM DUAL) ");
			sql.append(" CONNECT BY LEVEL <= LENGTH(IDS) - LENGTH(REGEXP_REPLACE(IDS, ',', '')) + 1 ) ");
		}

		if (!ifAdmin && StringUtils.isNotBlank(searchBhkNum)) {
			sql.append("SELECT T.PERSON_ID");
			sql.append(",T.PERSON_NAME,T.SEX");
			sql.append(",DECODE (LENGTH(T.IDC),15,SUBSTR(T.IDC,1,6) || '******' || SUBSTR (T.IDC,13),18,SUBSTR(T.IDC, 1,6) || '********' || SUBSTR(T.IDC,15),CASE WHEN T.IDC IS NOT NULL THEN SUBSTR(T.IDC, 1,LENGTH(T.IDC)-4) || '****' END) AS IDC");
			sql.append(",1 AS CREATE_DATE,1 AS UPDATE_DATE,A.IDCS ");
			sql.append(",1 AS ZYB,1 AS JJZ");
			sql.append(",1 AS CRPT_NAME");
			sql.append(" FROM (");
			sql.append("SELECT T1.PERSON_ID");
			sql.append(",T2.PERSON_NAME,T2.SEX,T2.IDC");
			sql.append(initSearchConditionSql());
			sql.append(" GROUP BY T1.PERSON_ID,T2.PERSON_NAME,T2.SEX,T2.IDC");
			sql.append(")T");
			sql.append(" INNER JOIN(");
			sql.append("SELECT /*+index(T1 INDEX_CRPTID)*/T1.PERSON_ID,COUNT(T1.RID) IDCS");
			sql.append(",MIN(T1.BHK_DATE) AS CREATE_DATE,MAX(T1.BHK_DATE) AS UPDATE_DATE");
			sql.append(",SUM(T1.IF_TARGETDIS) AS ZYB");
			sql.append(",SUM(T1.IF_WRKTABU) AS JJZ");
			sql.append(" FROM TD_TJ_BHK T1 ");
			sql.append(" INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID");
			sql.append(" WHERE  T1.BHK_TYPE IN (3,4)  AND crpt.INTER_PRC_TAG = 1");
			sql.append(" AND T1.BHKORG_ID =:searchUnitId ");//本机构
			this.paramMap.put("searchUnitId", searchUnitId);
			sql.append(" GROUP BY T1.PERSON_ID");
			sql.append(")A ON A.PERSON_ID =T.PERSON_ID");
			sql.append(" WHERE 1=1 ");
			sql.append(" AND A.IDCS >=:searchBhkNum");
			this.paramMap.put("searchBhkNum", searchBhkNum);
			sql.append(" ORDER BY T.PERSON_NAME,T.PERSON_ID");
		}else {
			sql.append("SELECT T1.PERSON_ID");
			sql.append(",T2.PERSON_NAME,T2.SEX");
			sql.append(",DECODE (LENGTH(T2.IDC),15,SUBSTR(T2.IDC,1,6) || '******' || SUBSTR (T2.IDC,13),18,SUBSTR(T2.IDC, 1,6) || '********' || SUBSTR(T2.IDC,15),CASE WHEN T2.IDC IS NOT NULL THEN SUBSTR(T2.IDC, 1,LENGTH(T2.IDC)-4) || '****' END) AS IDC");
			sql.append(",1 AS CREATE_DATE,1 AS UPDATE_DATE,T2.ARCH_NUM ");
			sql.append(",1 AS ZYB,1 AS JJZ");
			sql.append(",1 AS CRPT_NAME");
			sql.append(initSearchConditionSql());
			if (StringUtils.isNotBlank(searchBhkNum)) {
				sql.append(" AND T2.ARCH_NUM >=:searchBhkNum");
				this.paramMap.put("searchBhkNum", searchBhkNum);
			}
			sql.append(" GROUP BY T1.PERSON_ID,T2.PERSON_NAME,T2.SEX,T2.IDC,T2.ARCH_NUM");
			sql.append(" ORDER BY T2.PERSON_NAME,T1.PERSON_ID");
		}
		return sql.toString();
	}
	@Override
	public String[] buildHqls() {
		return new String[] { initSelectSql(), initCountSql() };
	}
	@Override
	public void processData(List<?> list) {
		long a = System.currentTimeMillis();
		if (!CollectionUtils.isEmpty(list)) {
			List<Object[]> result = (List<Object[]>) list;
			StringBuffer personIds = new StringBuffer();
			for (Object[] obj : result) {
				if (null==obj[0]) {
					continue;
				}
				personIds.append(",").append(obj[0].toString());
			}
			Map<String, Object[]> map = findNewestCrpt(personIds.substring(1));
			Map<String, Object[]> mapArchs = findTjPersonArchs(personIds.substring(1));
			for (Object[] obj : result) {
				if (null==obj[0]) {
					continue;
				}
				Object[] val = map.get(obj[0].toString());
				if (null!=val) {
					obj[7] = val[1];// 最新工作单位
				}
				if (null!=mapArchs) {
					Object[] archs = mapArchs.get(obj[0].toString());
					if (null!=archs) {
						obj[6] = archs[1];// 档案数
						obj[4] = archs[2];// 初次建档日期
						obj[5] = archs[3];// 更新日期
						obj[8] = archs[4];// 是否有职业禁忌证
						obj[9] = archs[5];// 是否有疑似职业病
					}
				}
			}
		}
		long b = System.currentTimeMillis();
		System.out.println("处理耗时："+(b-a));
	}
	/**
 	 * <p>方法描述：获取劳动者体检中的最新企业</p>
 	 * @MethodAuthor qrr,2020年5月20日,findNewestCrpt
	 * */
	private Map<String, Object[]> findNewestCrpt(String personId) {
		Map<String, Object[]> map = new HashMap<>();
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T1.PERSON_ID,crpt.CRPT_NAME");
		sql.append(" FROM TD_TJ_BHK T1 ");
		sql.append(" INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID");
		sql.append(" INNER JOIN TD_TJ_PERSON T2 ON T2.RID = T1.PERSON_ID");
		sql.append(" WHERE  T1.BHK_TYPE IN (3,4)  AND crpt.INTER_PRC_TAG = 1");
		if (!ifAdmin) {//非质控员
			sql.append(" AND T1.BHKORG_ID = ").append(conditionPO.getSearchUnitId());//本机构
		}
		sql.append(" AND T1.PERSON_ID IN (").append(personId).append(")");
		sql.append(" ORDER BY T1.BHK_DATE DESC");
		List<Object[]> list = hethStaQueryServiceImpl.findBySql(sql.toString());
		if (!CollectionUtils.isEmpty(list)) {
			for (Object[] obj : list) {
				if (null==obj[0]||null==obj[1]) {
					continue;
				}
				if (null==map.get(obj[0].toString())) {
					map.put(obj[0].toString(), obj);
				}
			}
		}
		return map;
	}
	
	/**
	 * <p>方法描述：获取劳动者体检中的档案数</p>
	 * @MethodAuthor qrr,2020年5月20日,findTjPersonArchs
	 * */
	private Map<String, Object[]> findTjPersonArchs(String personId) {
		Map<String, Object[]> map = new HashMap<>();
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT T1.PERSON_ID,COUNT(T1.RID)");
		sql.append(",MIN(T1.BHK_DATE) AS CREATE_DATE,MAX(T1.BHK_DATE) AS UPDATE_DATE");
		sql.append(",SUM(T1.IF_TARGETDIS) AS ZYB");
		sql.append(",SUM(T1.IF_WRKTABU) AS JJZ");
		sql.append(" FROM TD_TJ_BHK T1 ");
		sql.append(" INNER JOIN TB_TJ_CRPT crpt ON crpt.RID = T1.CRPT_ID");
		sql.append(" INNER JOIN TD_TJ_PERSON T2 ON T2.RID = T1.PERSON_ID");
		sql.append(" WHERE  T1.BHK_TYPE IN (3,4)  AND crpt.INTER_PRC_TAG = 1");
		if (!ifAdmin) {//非质控员
			sql.append(" AND T1.BHKORG_ID = ").append(conditionPO.getSearchUnitId());//本机构
		}
		sql.append(" AND T1.PERSON_ID IN (").append(personId).append(")");
		sql.append(" GROUP BY T1.PERSON_ID");
		List<Object[]> list = hethStaQueryServiceImpl.findBySql(sql.toString());
		if (!CollectionUtils.isEmpty(list)) {
			for (Object[] obj : list) {
				if (null==obj[0]) {
					continue;
				}
				if (null==map.get(obj[0].toString())) {
					map.put(obj[0].toString(), obj);
				}
			}
		}
		return map;
	}
	@Override
	public void addInit() {
	}

	@Override
	public void modInit() {
		// 根据人员信息查数据库，找到所有该人员的体检信息，按体检日期排序，得到主表集合
		alltdTjBhkList = hethStaQueryServiceImpl.findTdTjBhkAllListByPerson(personRid,ifAdmin?null:conditionPO.getSearchUnitId());
		/**初检的数据*/
		tdTjBhkList = new ArrayList<>();
		/**复检的数据*/
		tdTjBhkFjList= new ArrayList<>();
		// 以初检id为键，以复检对象为值存入map中
		fjmap = new HashMap<>();
		tdTjBhkCount = 0;
		if (CollectionUtils.isEmpty(this.alltdTjBhkList)) {
			return;
		}
		tdTjBhk = alltdTjBhkList.get(0);
		String idc = tdTjBhk.getIdc();
		if (null!=tdTjBhk.getPsnType() && StringUtils.isNotBlank(idc)) {
			if (1==tdTjBhk.getPsnType()) {
				//加密身份证号
				String encryptIdc = StringUtils.encryptIdc(idc);
				tdTjBhk.setIdc(encryptIdc);
			}else if (2==tdTjBhk.getPsnType()) {
				tdTjBhk.setIdc(idc.trim().length()>4 ? idc.trim().substring(0,idc.trim().length()-4)+"****" : "****");
			}
		}
		for (TdTjBhk bhk : alltdTjBhkList) {
			if(null != bhk.getIfRhk() && bhk.getIfRhk().intValue() == 0){
				/**只显示初检的数据*/
				tdTjBhkList.add(bhk);
			}else if(null != bhk.getIfRhk() && bhk.getIfRhk().intValue() == 1){
				/**复检集合存放复检数据*/
				tdTjBhkFjList.add(bhk);
			}
		}
		// 记录条数初始化
		tdTjBhkCount = alltdTjBhkList.size();
		/**无关联初检记录的复检记录*/
		Set<TdTjBhk> dislist = new HashSet<>();
		//key 体检机构rid+"&"+初检编号 value 对应复检集合
		Map<String,List<TdTjBhk>> fstSubMap = new HashMap<>();
		if (!CollectionUtils.isEmpty(this.tdTjBhkFjList)) {
			for (TdTjBhk fjBhk : this.tdTjBhkFjList) {
				Integer bhkOrgId = fjBhk.getTbTjSrvorg().getRid();
				String fstBhkCode = fjBhk.getLastFstBhkCode();
				if (null == bhkOrgId || StringUtils.isBlank(fstBhkCode)) {
					dislist.add(fjBhk);
					continue;
				}
				String key = bhkOrgId+"&"+fstBhkCode;
				List<TdTjBhk> curList = fstSubMap.get(key);
				if (null == curList) {
					curList = new ArrayList<>();
					fstSubMap.put(key, curList);
				}
				curList.add(fjBhk);
			}
		}
		//匹配到的初检 体检机构rid+"&"+编号
		Set<String> keySet = new HashSet<>();
		// 将初检数据和复检数据关联
		if(!CollectionUtils.isEmpty(this.tdTjBhkList) && !CollectionUtils.isEmpty(fstSubMap)){
			for (TdTjBhk bhk : tdTjBhkList) {
				String key = bhk.getTbTjSrvorg().getRid()+"&"+bhk.getBhkCode();
				/**有关联初检记录的复检记录*/
				List<TdTjBhk> list = fstSubMap.get(key);
				if (CollectionUtils.isEmpty(list)) {
					continue;
				}
				keySet.add(key);
				bhk.setIfFjNeedShow(1);
				fjmap.put(bhk.getRid(), list);
			}
		}

		if (!CollectionUtils.isEmpty(keySet)) {
			Set<String> curSet = new HashSet<>();
			curSet.addAll(fstSubMap.keySet());
			curSet.removeAll(keySet);
			keySet = curSet;
		}

		if (!CollectionUtils.isEmpty(keySet)) {
			//未匹配到的复检
			for (String key : keySet) {
				dislist.addAll(fstSubMap.get(key));
			}
		}
		/**若是没有初检记录的复检记录，也需要直接平铺开来显示*/
		if(!CollectionUtils.isEmpty(dislist)){
			for (TdTjBhk tdTjBhk : dislist) {
				tdTjBhkList.add(tdTjBhk);
			}
		}

		//list排序
		Collections.sort(tdTjBhkList, new Comparator<TdTjBhk>() {
			public int compare(TdTjBhk o1, TdTjBhk o2) {
				return  o1.getBhkDate().compareTo(o2.getBhkDate());
			}
		});
	}

	/**
	 * <p>方法描述：刷新复检项目</p>
 	 * 
 	 * @MethodAuthor rcj,2018年12月28日,searchFjItem
	 */
	public void searchFjItem(){
		/**获取该初检的复检数据*/
		List<TdTjBhk> list = fjmap.get(firstRid);
		int i = -1 ;
		if(tdTjBhkList!=null && tdTjBhkList.size()>0){
			for (TdTjBhk tdTjBhk : tdTjBhkList) {
				if(null != tdTjBhk.getRid() && null !=firstRid &&tdTjBhk.getRid().intValue() == firstRid){
					i= tdTjBhkList.indexOf(tdTjBhk);
					tdTjBhk.setIfFjNeedShow(2);
				}
			}
		}
		Collections.sort(list, new Comparator<TdTjBhk>() {
			public int compare(TdTjBhk o1, TdTjBhk o2) {
				return  o1.getBhkDate().compareTo(o2.getBhkDate());
			}
		});
		if(null != list && list.size() > 0 && i != -1){
			for (int j = 0; j < list.size(); j++) {
				tdTjBhkList.add(i+j+1, list.get(j));
			}
		}
	}
	
	/**
	 * <p>方法描述：将复检记录收回</p>
 	 * 
 	 * @MethodAuthor rcj,2019年1月3日,shrinkFjItem
	 */
	public void shrinkFjItem(){
		/**获取该初检的复检数据*/
		List<TdTjBhk> list = fjmap.get(firstRid);
		int i = -1 ;
		if(tdTjBhkList!=null && tdTjBhkList.size()>0){
			for (TdTjBhk tdTjBhk : tdTjBhkList) {
				if(null != tdTjBhk.getRid() && null !=firstRid &&tdTjBhk.getRid().intValue() == firstRid){
					i= tdTjBhkList.indexOf(tdTjBhk);
					tdTjBhk.setIfFjNeedShow(1);
				}
			}
		}
		if(null != list && list.size() > 0 && i != -1){
			for (int j = 0; j < list.size(); j++) {
				tdTjBhkList.remove(i+1);
			}
		}
	}

	/**
	 * 查历次疑似职业病
	 */
	public void zybInitAction() {
		tdTjSupoccdiselists = hethStaQueryServiceImpl.findSupoccdiselist(personRid,ifAdmin?null:conditionPO.getSearchUnitId());
	}

	/**
	 * 查历次禁忌症
	 */
	public void jjzInitAction() {
		tdTjContraindlists = hethStaQueryServiceImpl.findContraindlist(personRid,ifAdmin?null:conditionPO.getSearchUnitId());
	}

	/**
	 * 查本次体检疑似职业病
	 */
	public void bzybInitAction() {
		btdTjSupoccdiselists = hethStaQueryServiceImpl.findBSupoccdiselist(tdTjBhkRid);
	}

	/**
	 * 查本次体检禁忌症
	 */
	public void bjjzInitAction() {
		btdTjContraindlists = hethStaQueryServiceImpl.findBContraindlist(tdTjBhkRid);
	}

	@Override
	public void saveAction() {
	}

	@Override
	public void viewInit() {
		tjBhkInfoBean.setRid(tdTjBhkRid);
		tjBhkInfoBean.setArchivePanel(archivePanel);
		tjBhkInfoBean.setIfManagedOrg(true);
		tjBhkInfoBean.initBhkInfo();
	}

	/**
 	 * <p>方法描述：查询体检机构</p>
 	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void selUnitAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null,625,null,500);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(conditionPO.getSearchUnitId());
        paramMap.put("selectIds", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/selectTjOrgList", options, paramMap);
	}
	/**
 	 * <p>方法描述：处理选择的体检机构</p>
 	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void onSelectUnitAction(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			List<TbTjSrvorg> list = (List<TbTjSrvorg>) selectedMap.get("selectPros");
			if (null!=list && list.size()>0) {
				StringBuffer names = new StringBuffer();
				StringBuffer ids = new StringBuffer();
				for (TbTjSrvorg t : list) {
					names.append("，").append(t.getUnitName());
					ids.append(",").append(t.getRid());
				}
				conditionPO.setSearchUnitId(ids.substring(1));
				this.searchUnitName = names.substring(1);
			}
		}
	}
	/**
 	 * <p>方法描述：清空单位</p>
 	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void clearUnit() {
		conditionPO.setSearchUnitId(null);
		this.searchUnitName = null;
	}
	/**
 	 * <p>方法描述：处理选择的工龄</p>
 	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void hideWorkAgeAction() {
		// 遍历选择的危害因素，获取选择的id与名称
		this.selectWorkName = null;
		List<TdZdzybAnalyDetailComm> selectWorkAnalyDetails = new ArrayList<>();
		if (this.selectWorkAges != null && selectWorkAges.length > 0) {
			StringBuilder nameSb = new StringBuilder(); // 分类名称
			for (TreeNode treeNode : selectWorkAges) {
				TdZdzybAnalyDetailComm detail = (TdZdzybAnalyDetailComm) treeNode.getData();
				nameSb.append("，").append(
						String.valueOf(detail.getAnalyItem().getCodeName()));
				selectWorkAnalyDetails.add(detail);
			}
			conditionPO.setSelectWorkAnalyDetails(selectWorkAnalyDetails);
			this.selectWorkName = nameSb.toString().substring(1);
		}
	}
	/**
 	 * <p>方法描述：清空选择的工龄</p>
 	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void clearSelectWorkAge() {
		this.selectWorkName = null;
		conditionPO.setSelectWorkAnalyDetails(null);
		this.selectWorkAges = null;
		List<TreeNode> children = this.workSortTree.getChildren();
		if (null!=children && children.size()>0) {
			for (TreeNode node : children) {
				node.setSelected(false);
				List<TreeNode> children2 = node.getChildren();
				if (null!=children2 && children2.size()>0) {
					for (TreeNode node2 : children2) {
						node2.setSelected(false);
					}
				}
			}
		}
	}
	/**
 	 * <p>方法描述：处理选择的年龄</p>
 	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void hideAgeAction() {
		// 遍历选择的危害因素，获取选择的id与名称
		this.selectAgeName = null;
		List<TdZdzybAnalyDetailComm> selectAgeAnalyDetails = new ArrayList<>();
		if (this.selectAges != null && selectAges.length > 0) {
			StringBuilder nameSb = new StringBuilder(); // 分类名称
			for (TreeNode treeNode : selectAges) {
				TdZdzybAnalyDetailComm detail = (TdZdzybAnalyDetailComm) treeNode.getData();
				nameSb.append("，").append(
						String.valueOf(detail.getAnalyItem().getCodeName()));
				selectAgeAnalyDetails.add(detail);
			}
			conditionPO.setSelectAgeAnalyDetails(selectAgeAnalyDetails);
			this.selectAgeName = nameSb.toString().substring(1);
		}
	}
	/**
 	 * <p>方法描述：清空选择的年龄龄</p>
 	 * @MethodAuthor qrr,2020年4月22日,clearSelectWorkAge
	 * */
	public void clearSelectAge() {
		this.selectAgeName = null;
		conditionPO.setSelectAgeAnalyDetails(null);
		this.selectAges = null;
		List<TreeNode> children = this.ageSortTree.getChildren();
		if (null!=children && children.size()>0) {
			for (TreeNode node : children) {
				node.setSelected(false);
				List<TreeNode> children2 = node.getChildren();
				if (null!=children2 && children2.size()>0) {
					for (TreeNode node2 : children2) {
						node2.setSelected(false);
					}
				}
			}
		}
	}
	/**
	 *  <p>方法描述：导出</p>
	 * @MethodAuthor hsj
	 */
	public void exportData(){
		exportItemAction();
		tdTjExport = new TdTjExport();
		//导出类型
		tdTjExport.setFkByBusTypeId(busTypeMap.get("1"));
		//导出条件
		exportConditionData();
		tdTjExport.setState(0);
		tdTjExport.setExportDate(new Date());
		tdTjExport.setFkByOperUnitId(Global.getUser().getTsUnit());
		tdTjExport.setFkByOperPsnId(Global.getUser());
		String fileName = busTypeMap.get("1").getExtendS3();
		if(null != exportFlag && exportFlag == 2){
			fileName = fileName + "（内部）";
		}
		tdTjExport.setExportFileName(fileName);
		this.hethStaQueryServiceImpl.saveTdTjExport(tdTjExport);
		JsfUtil.addSuccessMessage("导出任务已发送，请至导出文件下载模块下载！");
	}
	/**
	 *  <p>方法描述：导出条件的封装</p>
	 * @MethodAuthor hsj
	 */
	private void exportConditionData() {
		JSONObject  jsonObject= new JSONObject(true) ;
		StringBuffer str = new StringBuffer();
		//人员姓名
		if(StringUtils.isNotBlank(conditionPO.getSearchPersonName())){
			jsonObject.put("searchPersonName",StringUtils.convertBFH(conditionPO.getSearchPersonName().trim()));
			str.append("人员姓名@*@").append(StringUtils.convertBFH(conditionPO.getSearchPersonName().trim())).append("#*#");
		}
		//证件类型
		if(StringUtils.isNotBlank(conditionPO.getSearchPsnType())){
			jsonObject.put("searchPsnType",conditionPO.getSearchPsnType());
			str.append("证件类型@*@").append(tsSimpleCodeMap.get(Integer.valueOf(conditionPO.getSearchPsnType()))).append("#*#");
		}
		//证件号码
		if(StringUtils.isNotBlank(conditionPO.getSearchIDC())){
			jsonObject.put("searchIDC", StringUtils.convertBFH(conditionPO.getSearchIDC().trim()));
			str.append("证件号码@*@").append( StringUtils.convertBFH(conditionPO.getSearchIDC().trim())).append("#*#");
		}
		//用工单位地区
		if(StringUtils.isNotBlank(conditionPO.getSearchEntrustCrptZoneGb())){
			jsonObject.put("searchEntrustCrptZoneGb",ZoneUtil.zoneSelect(conditionPO.getSearchEntrustCrptZoneGb()));
			str.append("用工单位地区@*@").append(conditionPO.getSearchEntrustCrptZoneName()).append("#*#");
		}
		//用工单位
		if(StringUtils.isNotBlank(conditionPO.getSearchEntrustCrptName())){
			jsonObject.put("searchEntrustCrptName",conditionPO.getSearchEntrustCrptName());
			str.append("用工单位@*@").append(conditionPO.getSearchEntrustCrptName()).append("#*#");
		}
		//体检类型
		if(null != conditionPO.getSearchBhkType() && conditionPO.getSearchBhkType().length > 0){
			jsonObject.put("searchBhkType",conditionPO.getSearchBhkType());
			String s ="";
			for(int i = 0;i<conditionPO.getSearchBhkType().length;i++){
				if( "3".equals(conditionPO.getSearchBhkType()[i])){
					s = s +"，职业健康检查";
				}else if("4".equals(conditionPO.getSearchBhkType()[i])){
					s = s +"，放射卫生健康检查";
				}
			}
			str.append("体检类型@*@").append(s.substring(1)).append("#*#");
		}
		//用人单位地区
		if(StringUtils.isNotBlank(conditionPO.getSearchZoneCode())){
			jsonObject.put("searchZoneCode",ZoneUtil.zoneSelect(conditionPO.getSearchZoneCode()));
			str.append("用人单位地区@*@").append(searchZoneName).append("#*#");
		}
		//用人单位
		if(StringUtils.isNotBlank(conditionPO.getSearchCrptName())){
			jsonObject.put("searchCrptName",StringUtils.convertBFH(conditionPO.getSearchCrptName().trim()));
			str.append("用人单位@*@").append(StringUtils.convertBFH(conditionPO.getSearchCrptName().trim())).append("#*#");
		}
		//监测类型
		if(null != conditionPO.getSearchJcType() && conditionPO.getSearchJcType().length > 0){
			jsonObject.put("searchJcType",conditionPO.getSearchJcType());
			String s ="";
			for(int i = 0;i<conditionPO.getSearchJcType().length;i++){
				if("1".equals(conditionPO.getSearchJcType()[i])){
					s = s +"，常规监测";
				}else if("2".equals(conditionPO.getSearchJcType()[i])){
					s = s +"，主动监测";
				}
			}
			str.append("监测类型@*@").append(s.substring(1)).append("#*#");
		}
		//体检日期
		if(null != conditionPO.getSearchStartTime()){
			jsonObject.put("searchStartTime",DateUtils.formatDate(conditionPO.getSearchStartTime()));
		}
		if(null != conditionPO.getSearchEndTime()){
			jsonObject.put("searchEndTime",DateUtils.formatDate(conditionPO.getSearchEndTime()));
		}
		if(null != conditionPO.getSearchStartTime() || null != conditionPO.getSearchEndTime()){
			StringBuffer s = new StringBuffer();
			s.append(conditionPO.getSearchStartTime() == null ? "" : DateUtils.formatDate(conditionPO.getSearchStartTime()));
			s.append("~");
			s.append(conditionPO.getSearchEndTime() == null ? "" : DateUtils.formatDate(conditionPO.getSearchEndTime()));
			str.append("体检日期@*@").append(s.toString()).append("#*#");
		}
		//报告出具日期
		if(null != conditionPO.getStartRptPrintDate()){
			jsonObject.put("startRptPrintDate",DateUtils.formatDate(conditionPO.getStartRptPrintDate()));
		}
		if(null != conditionPO.getEndRptPrintDate()){
			jsonObject.put("endRptPrintDate",DateUtils.formatDate(conditionPO.getEndRptPrintDate()));
		}
		if(null != conditionPO.getStartRptPrintDate() || null != conditionPO.getEndRptPrintDate()){
			StringBuffer s = new StringBuffer();
			s.append(conditionPO.getStartRptPrintDate() == null ? "" : DateUtils.formatDate(conditionPO.getStartRptPrintDate()));
			s.append("~");
			s.append(conditionPO.getEndRptPrintDate() == null ? "" : DateUtils.formatDate(conditionPO.getEndRptPrintDate()));
			str.append("报告出具日期@*@").append(s.toString()).append("#*#");
		}
		//报告日期
		if(null != conditionPO.getStartCreateDate()){
			jsonObject.put("startCreateDate",DateUtils.formatDate(conditionPO.getStartCreateDate()));
		}
		if(null != conditionPO.getEndCreateDate()){
			jsonObject.put("endCreateDate",DateUtils.formatDate(conditionPO.getEndCreateDate()));
		}
		if(null != conditionPO.getStartCreateDate() || null != conditionPO.getEndCreateDate()){
			StringBuffer s = new StringBuffer();
			s.append(conditionPO.getStartCreateDate() == null ? "" : DateUtils.formatDate(conditionPO.getStartCreateDate()));
			s.append("~");
			s.append(conditionPO.getEndCreateDate() == null ? "" : DateUtils.formatDate(conditionPO.getEndCreateDate()));
			str.append("报告日期@*@").append(s.toString()).append("#*#");
		}
		//在岗状态
		if(StringUtils.isNotBlank(conditionPO.getSelectOnGuardIds())){
			jsonObject.put("selectOnGuardIds",conditionPO.getSelectOnGuardIds());
			String[] ss = conditionPO.getSelectOnGuardIds().split(",");
			String s ="";
			for(int i = 0;i< ss.length;i++){
				s = s +"，"+tsSimpleCodeMap.get(Integer.valueOf(ss[i]));
			}
			str.append("在岗状态@*@").append(s.substring(1)).append("#*#");
		}
		//体检危害因素
		if(StringUtils.isNotBlank(conditionPO.getSelectBadRsnIds())){
			jsonObject.put("selectBadRsnIds",conditionPO.getSelectBadRsnIds());
			String[] ss = conditionPO.getSelectBadRsnIds().split(",");
			String s ="";
			for(int i = 0;i< ss.length;i++){
				s = s +"，"+tsSimpleCodeMap.get(Integer.valueOf(ss[i]));
			}
			str.append("体检危害因素@*@").append(s.substring(1)).append("#*#");
		}
		//单危害因素结论
		if(StringUtils.isNotBlank(conditionPO.getSearchSelBhkrstIds())){
			jsonObject.put("searchSelBhkrstIds",conditionPO.getSearchSelBhkrstIds());
			str.append("单危害因素结论@*@").append(searchBhkrstName).append("#*#");//searchBhkrstName
		}
		//年龄
		JSONArray jsonArray = new JSONArray();
		if(!CollectionUtils.isEmpty(conditionPO.getSelectAgeAnalyDetails())){
			StringBuffer detail = new StringBuffer();
			for (TdZdzybAnalyDetailComm detailAnaly : conditionPO.getSelectAgeAnalyDetails()) {
				detail.append("，").append(detailAnaly.getAnalyItem().getCodeName());
				JSONObject jsonObject1 = new JSONObject();
				jsonObject1.put("geNum",detailAnaly.getGeNum());
				jsonObject1.put("gtNum",detailAnaly.getGtNum());
				jsonObject1.put("leNum",detailAnaly.getLeNum());
				jsonObject1.put("ltNum",detailAnaly.getLtNum());
				jsonArray.add(jsonObject1);
			}
			str.append("年龄@*@").append(detail.substring(1)).append("#*#");
			jsonObject.put("selectAgeAnalyDetails",jsonArray);
		}
		//接害工龄
		JSONArray jsonArray1 = new JSONArray();
		if(!CollectionUtils.isEmpty(conditionPO.getSelectWorkAnalyDetails())){
			StringBuffer detail = new StringBuffer();
			for (TdZdzybAnalyDetailComm detailAnaly : conditionPO.getSelectWorkAnalyDetails()) {
				detail.append("，").append(detailAnaly.getAnalyItem().getCodeName());
				JSONObject jsonObject1 = new JSONObject();
				jsonObject1.put("geNum",detailAnaly.getGeNum());
				jsonObject1.put("gtNum",detailAnaly.getGtNum());
				jsonObject1.put("leNum",detailAnaly.getLeNum());
				jsonObject1.put("ltNum",detailAnaly.getLtNum());
				jsonArray1.add(jsonObject1);
			}
			str.append("接害工龄@*@").append(detail.substring(1)).append("#*#");
			jsonObject.put("selectWorkAnalyDetails",jsonArray1);
		}
		//主检结论
		if(StringUtils.isNotBlank(conditionPO.getSearchSelMhkrstIds())){
			jsonObject.put("searchSelMhkrstIds",conditionPO.getSearchSelMhkrstIds());
			String[] ss = conditionPO.getSearchSelMhkrstIds().split(",");
			String s ="";
			for(int i = 0;i< ss.length;i++){
				s = s +"，"+tsSimpleCodeMap.get(Integer.valueOf(ss[i]));
			}
			str.append("主检结论@*@").append(s.substring(1)).append("#*#");
		}
		//体检机构
		if(StringUtils.isNotBlank(conditionPO.getSearchUnitId())){
			jsonObject.put("searchUnitId",conditionPO.getSearchUnitId());
			str.append("体检机构@*@").append(searchUnitName).append("#*#");
		}
		//体检项目
		if(StringUtils.isNotBlank(conditionPO.getSearchItemIds())){
			jsonObject.put("searchItemIds",conditionPO.getSearchItemIds());
			str.append("体检项目@*@").append(searchItemNames).append("#*#");
		}
		//档案份数
		if(StringUtils.isNotBlank(conditionPO.getSearchBhkNum())){
			jsonObject.put("searchBhkNum",conditionPO.getSearchBhkNum());
			str.append("档案份数@*@").append(conditionPO.getSearchBhkNum()).append("#*#");
		}
		//导出项目
		if(StringUtils.isNotBlank(exportItems)){
			jsonObject.put("exportItems",exportItems);
		}
		if(StringUtils.isNotBlank(conditionPO.getItemNames())){
			str.append("导出项目@*@").append(conditionPO.getItemNames()).append("#*#");
		}
		//是否质控员
		jsonObject.put("ifAdmin",ifAdmin);
		//导出类型
		jsonObject.put("type",exportFlag == null ? "1" : exportFlag);
		tdTjExport.setExportCondition(null == jsonObject  ? null : JSON.toJSONString(jsonObject));
		tdTjExport.setExportConditionShow(str.toString());
	}

	public void preProcessXLS(Object document) {
		xpNum = 0;
		long a = System.currentTimeMillis();
		SXSSFWorkbook wb = (SXSSFWorkbook) document;
		Sheet sheet = wb.getSheetAt(0);
		// 设置表头字体
		Font headfont = wb.createFont();
		headfont.setFontName("宋体");
		headfont.setFontHeightInPoints((short) 10);// 字体大小
		headfont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);// 加粗
		// 设置表头样式
		CellStyle style = wb.createCellStyle();
		style.setAlignment(HSSFCellStyle.ALIGN_CENTER);// 设置水平居中
		style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 上下居中
		style.setFont(headfont);
//		// 创建第二行以及数据
		String[] cellValue = new String[] { "姓名", "体检编号", "性别", "年龄","证件类型",
				"证件号码","联系电话","用人单位名称", "用人单位联系人","用人单位联系电话", "用人单位所属地区","用人单位通讯地址","用人单位社会信用代码","用人单位规模","用人单位经济类型", "用人单位行业分类","在岗状态", "岗位", "工种", "总工龄",
				"接害工龄", "接触有害因素" };
		int rowInd = 0;
		org.apache.poi.ss.usermodel.Row row1 = sheet.createRow(rowInd);
		int colInd = 0;
		int secInd = 0;
		for( String colName : cellValue)	{
			Cell c1 = row1.createCell(colInd);
			c1.setCellValue(colName);
			c1.setCellStyle(style);
			CellRangeAddress region1 = new CellRangeAddress(rowInd, rowInd+1, colInd, colInd);
			sheet.addMergedRegion(region1);
			colInd++;
		}
		
		secInd = colInd;
		
		Cell c1 = row1.createCell(colInd);
		c1.setCellValue("人员职业史");
		c1.setCellStyle(style);
		CellRangeAddress region1 = new CellRangeAddress(rowInd, rowInd, colInd, colInd+3);
		sheet.addMergedRegion(region1);
		colInd+=4;
		
		Cell c2 = row1.createCell(colInd);
		c2.setCellValue("既往病史");
		c2.setCellStyle(style);
		CellRangeAddress region2 = new CellRangeAddress(rowInd, rowInd, colInd, colInd+4);
		sheet.addMergedRegion(region2);
		colInd+=5;
		
		Cell c10 = row1.createCell(colInd);
		c10.setCellValue("放射史");
		c10.setCellStyle(style);
		CellRangeAddress region10 = new CellRangeAddress(rowInd, rowInd, colInd, colInd+6);
		sheet.addMergedRegion(region10);
		colInd+=7;
		
		Cell c3 = row1.createCell(colInd);
		c3.setCellValue("其他");
		c3.setCellStyle(style);
		CellRangeAddress region3 = new CellRangeAddress(rowInd, rowInd, colInd, colInd+2);
		sheet.addMergedRegion(region3);
		colInd+=3;
		
		Cell c4 = row1.createCell(colInd);
		c4.setCellValue("症状");
		c4.setCellStyle(style);
		CellRangeAddress region4 = new CellRangeAddress(rowInd, rowInd, colInd, colInd+1);
		sheet.addMergedRegion(region4);
		colInd+=2;
		
		
		if(StringUtils.isNotBlank(exportItems))	{
			String[] split = exportItems.split(",");
			for(String itemId : split)	{
				TbTjItems tbTjItems = this.itemCachMaps.get(new Integer(itemId));
				//判断是否为胸片
				if(null != tbTjItems.getItemTag() && "30".equals(tbTjItems.getItemTag().toString()) ){
					xpNum ++ ;
				}
			}
			BigDecimal colInds = new BigDecimal(split.length).multiply(new BigDecimal(3)).add(new BigDecimal(xpNum));
			Cell c6 = row1.createCell(colInd);
			c6.setCellValue("体检项目");
			c6.setCellStyle(style);
			CellRangeAddress region6 = new CellRangeAddress(rowInd, rowInd, colInd, colInd + colInds.intValue()-1);
			sheet.addMergedRegion(region6);
			colInd+= colInds.intValue();
		}

		Cell c11 = row1.createCell(colInd);
		c11.setCellValue("主检结论");
		c11.setCellStyle(style);
		CellRangeAddress region11 = new CellRangeAddress(rowInd, rowInd+1, colInd, colInd);
		sheet.addMergedRegion(region11);
		colInd+=1;
		
		Cell c12 = row1.createCell(colInd);
		c12.setCellValue("体检机构");
		c12.setCellStyle(style);
		CellRangeAddress region12 = new CellRangeAddress(rowInd, rowInd+1, colInd, colInd);
		sheet.addMergedRegion(region12);
		colInd+=1;
		
		Cell c13 = row1.createCell(colInd);
		c13.setCellValue("体检日期");
		c13.setCellStyle(style);
		CellRangeAddress region13 = new CellRangeAddress(rowInd, rowInd+1, colInd, colInd);
		sheet.addMergedRegion(region13);
		colInd+=1;

		Cell c14 = row1.createCell(colInd);
		c14.setCellValue("报告出具日期");
		c14.setCellStyle(style);
		CellRangeAddress region14 = new CellRangeAddress(rowInd, rowInd+1, colInd, colInd);
		sheet.addMergedRegion(region14);
		colInd+=1;

		

		for(int i = 0 ; i < colInd ; i++)	{
			sheet.setColumnWidth(i, 5000);
		}
		
		//新建一行数据
		rowInd++;
		org.apache.poi.ss.usermodel.Row titleRow = sheet.createRow(rowInd);
		
		String[] secValue = new String[] { "起止日期", "工作单位名称", "部门车间", "防护措施",
				"既往病史疾病名称", "诊断日期", "诊断单位", "治疗经过", "转归","起止日期","工作单位名称","每日工作时数或工作量","职业史累积受照剂量","职业史过量照射史","职业照射种类","放射线种类","家族史", "个人史",
				"其他", "自觉症状", "其他症状" };

		for( String qtVal : secValue )	{
			Cell c7 = titleRow.createCell(secInd);
			c7.setCellValue(qtVal);
			c7.setCellStyle(style);
			
			secInd++;
		}
		//体检项目标题
		if(StringUtils.isNotBlank(exportItems))	{
			String[] split = exportItems.split(",");
			for(String itemId : split)	{
				String itemName = this.itemCachMap.get(new Integer(itemId));
				TbTjItems tbTjItems = this.itemCachMaps.get(new Integer(itemId));
				Cell c44 = titleRow.createCell(secInd);
				c44.setCellValue(itemName+"结果");
				c44.setCellStyle(style);
				secInd++;
				Cell c45 = titleRow.createCell(secInd);
				c45.setCellValue(itemName+"计量单位");
				c45.setCellStyle(style);
				secInd++;
				Cell c46 = titleRow.createCell(secInd);
				c46.setCellValue(itemName+"合格标记");
				c46.setCellStyle(style);
				secInd++;
				//判断是否为胸片
				if(null != tbTjItems.getItemTag() && "30".equals(tbTjItems.getItemTag().toString()) ){
					Cell c47 = titleRow.createCell(secInd);
					c47.setCellValue(itemName+"结果判定");
					c47.setCellStyle(style);
					secInd++;
				}

			}
		}
		rowInd++;

		if (!ifAdmin) {//非质控员
			if (StringUtils.isBlank(conditionPO.getSearchUnitId())) {//资质未注册
				return;
			}
		}
		long a2 = System.currentTimeMillis();
		//1、非质控员且查询条件档案数不为空，体检总人数2、其他，体检记录数
		Integer findBhkExportCount = hethStaQueryServiceImpl
				.findBhkNewExportCount(conditionPO, ifAdmin);
		System.out.println("满足条件总条数："+findBhkExportCount);
		System.out.println("耗时："+(a2-a));
		//一次查询1000条，进行写入EXCEL
		if( findBhkExportCount > 0 )	{
			int pagSize = 10000;
			//按pagSize条查询 处理次数
			int allDataCount = findBhkExportCount%pagSize==0?findBhkExportCount/pagSize:((findBhkExportCount/pagSize)+1);
			System.err.println("数据总条数："+findBhkExportCount+"，每次导出"+pagSize+"条数据切分次数："+allDataCount+"。");
			for( int i = 0 ; i < allDataCount ; i++ )	{
				long a3 = System.currentTimeMillis();
				List<Object[]> findBhkExportList = this.hethStaQueryServiceImpl
						.findBhkExportNewList(conditionPO, ifAdmin, exportFlag,
								i * pagSize, (i + 1) * pagSize);
				long a1 = System.currentTimeMillis();
				System.out.println("第"+i+"次批量查询"+pagSize+"条耗时："+(a1-a3));
				if( null != findBhkExportList && findBhkExportList.size() > 0 )	{
					System.err.println("本次导出序号："+i);
					
					List<String> bhkIdList = new ArrayList<>();
					StringBuilder bhkIds = new StringBuilder();
					int k = 1;
					for( Object[] objArr : findBhkExportList  )	{
						if (k>=1000) {
							// rid拼接超过1000，放入集合，并重新拼接
							bhkIdList.add(bhkIds.substring(1));
							k = 1;
							bhkIds = new StringBuilder();
						}
						Integer bhkId = new Integer(objArr[0].toString());
						bhkIds.append(",").append(bhkId);
						k++;
					}
					// 不满1000条体检记录
					if (bhkIds.length()>0) {
						bhkIdList.add(bhkIds.substring(1));
					}
					//体检ID#体检项目ID  ==  项目结果值
					Map<String,Object[]> itemRstMap = new HashMap<String,Object[]>();
					//人员Id = 第一条人员史
					Map<Integer,Object[]> psnHisMap = new HashMap<Integer,Object[]>();
					//人员Id = 第一条放射人员史
					Map<Integer,Object[]> fsHisMap = new HashMap<Integer,Object[]>();
					//人员Id = 第一条既往病史
					Map<Integer,Object[]> jwHisMap = new HashMap<Integer,Object[]>();
					//人员Id = 症状
					Map<Integer,Object[]> zzMap = new HashMap<Integer,Object[]>();
					searchBhkInfo(bhkIdList, itemRstMap, psnHisMap,
							fsHisMap, jwHisMap, zzMap);
					String[] itms = exportItems.split(",");
					BigDecimal projectNum = new BigDecimal(itms.length).multiply(new BigDecimal(3)).add(new BigDecimal(xpNum)) ;
					//导出人员
					for( Object[] objArr : findBhkExportList  )	{
						org.apache.poi.ss.usermodel.Row row = sheet.createRow(rowInd);
						//体检Id
						Integer bhkId = new Integer(objArr[0].toString());
						colInd = 0;
						for(int j = 1 ; j <= 26 ; j++)	{
							if(j == 11){
								Cell cc = row.createCell(colInd);
								String zoneName = StringUtils.objectToString(objArr[j]);
								int first = zoneName.indexOf("_");
								String substring = zoneName.substring(first+1);
								cc.setCellValue(substring);
								colInd++;
							}else if(j<=22){
								Cell cc = row.createCell(colInd);
								cc.setCellValue(StringUtils.objectToString(objArr[j]));
								colInd++;
							}else if(j == 23){
								int t = new BigDecimal(43).add(projectNum).intValue();
								Cell cc = row.createCell(t);
								cc.setCellValue(StringUtils.objectToString(objArr[23]));
							}else if(j == 24){
								int t =  new BigDecimal(44).add(projectNum).intValue();
								Cell cc = row.createCell(t);
								cc.setCellValue(StringUtils.objectToString(objArr[24]));
							}else if(j == 25){
								int t =  new BigDecimal(45).add(projectNum).intValue();
								Cell cc = row.createCell(t);
								cc.setCellValue(StringUtils.objectToString(objArr[25]));
							}else if(j == 26){
								int t = new BigDecimal(46).add(projectNum).intValue();
								Cell cc = row.createCell(t);
								cc.setCellValue(StringUtils.objectToString(objArr[26]));
							}
						}
						
						Object[] objects = psnHisMap.get(bhkId);
						if( null != objects && objects.length == 5)	{
							for(int j = 0 ; j < 4 ; j++ )	{
								Cell cc = row.createCell(colInd);
								cc.setCellValue( StringUtils.objectToString(objects[j]));
								colInd++;
							}
						}else{
							colInd+= 4;
						}
						
						Object[] objects2 = jwHisMap.get(bhkId);
						if( null != objects2 && objects2.length == 6)	{
							for(int j = 0 ; j < 5 ; j++ )	{
								Cell cc = row.createCell(colInd);
								cc.setCellValue( StringUtils.objectToString(objects2[j]));
								colInd++;
							}
						}else{
							colInd+= 5;
						}
						
						Object[] objects4 = fsHisMap.get(bhkId);
						if( null != objects4 && objects4.length == 8)	{
							for(int j = 0 ; j < 7 ; j++ )	{
								Cell cc = row.createCell(colInd);
								cc.setCellValue( StringUtils.objectToString(objects4[j]));
								colInd++;
							}
						}else{
							colInd+= 7;
						}
						Object[] objects3 = zzMap.get(bhkId);
						if( null != objects3 && objects3.length == 6)	{
							for(int j = 0 ; j < 5 ; j++ )	{
								Cell cc = row.createCell(colInd);
								cc.setCellValue( StringUtils.objectToString(objects3[j]));
								colInd++;
							}
						}else{
							colInd+= 5;
						}
						if(StringUtils.isNotBlank(exportItems))	{
							String[] split = exportItems.split(",");
							for(String itemId : split)	{
								Object[] rst = itemRstMap.get(new StringBuilder().append(bhkId).append("#").append(itemId).toString());
								if(null !=rst && rst.length >0){
									//结果-2
									Cell ccc1 = row.createCell(colInd);
									ccc1.setCellValue(StringUtils.objectToString(rst[2]));
									colInd++;
									//计量单位-3
									Cell ccc2 = row.createCell(colInd);
									ccc2.setCellValue(StringUtils.objectToString(rst[3]));
									colInd++;
									//合格标记-4
									Cell ccc3 = row.createCell(colInd);
									ccc3.setCellValue(StringUtils.objectToString(rst[4]));
									colInd++;
									//胸片：结果判定 -5
									if(null != rst[6] && "30".equals(rst[6].toString())){
										Cell ccc4 = row.createCell(colInd);
										ccc4.setCellValue(StringUtils.objectToString(rst[5]));
										colInd++;
									}
								}else{
									//没有值时
									TbTjItems tbTjItems = this.itemCachMaps.get(new Integer(itemId));
									//判断是否为胸片
									if(null != tbTjItems.getItemTag() && "30".equals(tbTjItems.getItemTag().toString()) ){
										colInd+=4;
									}else {
										colInd+=3;
									}
								}
							}
						}
						rowInd++;
					}
					long a4 = System.currentTimeMillis();
					System.out.println("数据处理（查询职业史、放射史等）耗时--"+(a4-a1));
				}
			}
		}
		long b = System.currentTimeMillis();
		System.out.println("导出总耗时--"+(b-a));
	}
	/**
 	 * <p>方法描述：查询体检记录相关项目结果、最新检查日期职业史等</p>
 	 * @MethodAuthor qrr,2020年5月22日,searchBhkInfo
	 * */
	private void searchBhkInfo(List<String> bhkIdList, Map<String, Object[]> itemRstMap,
			Map<Integer, Object[]> psnHisMap, Map<Integer, Object[]> fsHisMap,
			Map<Integer, Object[]> jwHisMap, Map<Integer, Object[]> zzMap) {
		if(!CollectionUtils.isEmpty(bhkIdList))	{
			long a1 = System.currentTimeMillis();
			List<Object[]> itemsRstList = this.hethStaQueryServiceImpl.findBhkItemsRstList(bhkIdList);
			if( null != itemsRstList && itemsRstList.size() > 0)	{
				for(Object[] itemRst : itemsRstList)	{
					String key = new StringBuilder(itemRst[0].toString()).append("#").append(itemRst[1].toString()).toString();
					itemRstMap.put(key, itemRst);
				}
			}
			long a2 = System.currentTimeMillis();
			System.out.println("5000体检记录，体检子表表查询总耗时："+(a2-a1));
			List<Object[]> findBhkJbsList = this.hethStaQueryServiceImpl.findBhkJbsNewList(bhkIdList);
			if( null != findBhkJbsList && findBhkJbsList.size() > 0)	{
				for(Object[] jbs : findBhkJbsList)	{
					Integer bhkId = new Integer(jbs[4].toString());
					psnHisMap.put(bhkId, jbs);
				}
			}
			long a3 = System.currentTimeMillis();
			System.out.println("5000体检记录，职业史表查询总耗时："+(a3-a2));
			List<Object[]> findBhkFssNewList = this.hethStaQueryServiceImpl.findBhkFssNewList(bhkIdList);
			if( null != findBhkFssNewList && findBhkFssNewList.size() > 0)	{
				for(Object[] jbs : findBhkFssNewList)	{
					Integer bhkId = new Integer(jbs[7].toString());
					fsHisMap.put(bhkId, jbs);
				}
			}
			long a4 = System.currentTimeMillis();
			System.out.println("5000体检记录，放射史表查询总耗时："+(a4-a3));
			List<Object[]> findBhkJwbsList = this.hethStaQueryServiceImpl.findBhkJwbsNewList(bhkIdList);
			if( null != findBhkJwbsList && findBhkJwbsList.size() > 0)	{
				for(Object[] jwbs : findBhkJwbsList)	{
					Integer bhkId = new Integer(jwbs[5].toString());
					jwHisMap.put(bhkId, jwbs);
				}
			}
			long a5 = System.currentTimeMillis();
			System.out.println("5000体检记录，既往病史表查询总耗时："+(a5-a4));
			List<Object[]> findBhkQtList = this.hethStaQueryServiceImpl.findBhkQtList(bhkIdList);
			if( null != findBhkQtList && findBhkQtList.size() > 0)	{
				for(Object[] qt : findBhkQtList)	{
					Integer bhkId = new Integer(qt[5].toString());
					zzMap.put(bhkId, qt);
				}
			}
			long a6 = System.currentTimeMillis();
			System.out.println("5000体检记录，问诊表查询总耗时："+(a6-a5));
			System.out.println("5000体检记录，子表查询总耗时："+(a6-a1));
		}
	}
	/**
 	 * <p>方法描述：选择体检项目弹出框</p>
 	 * @MethodAuthor qrr,2020年5月25日,selItemAction
	 * */
	public void selItemAction() {
		Map<String, Object> options = MapUtils.produceDialogMap(null,650,null,490);

        Map<String, List<String>> paramMap = new HashMap<String, List<String>>();
        List<String> paramList = new ArrayList<String>();
        paramList.add(conditionPO.getSearchItemIds());
		paramMap.put("selectIds", paramList);
        RequestContext requestContext = RequestContext.getCurrentInstance();
        requestContext.openDialog("/webapp/heth/comm/tjItemMulitySelectList", options, paramMap);
	}
	/**
 	 * <p>方法描述：体检项目选择数据处理</p>
 	 * @MethodAuthor qrr,2020年5月25日,onItemAction
	 * */
	public void onItemAction(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event
				.getObject();
		if (null != selectedMap && selectedMap.size() > 0) {
			List<TbTjItems> list = (List<TbTjItems>) selectedMap.get("selectPros");
			if (null!=list && list.size()>0) {
				StringBuffer names = new StringBuffer();
				StringBuffer ids = new StringBuffer();
				for (TbTjItems t : list) {
					names.append("，").append(t.getItemName());
					ids.append(",").append(t.getRid());
					if (t.isIfNotHg()) {
						names.append("（不合格）");
						ids.append("@@0");
					}
				}
				conditionPO.setSearchItemIds(ids.substring(1));
				this.searchItemNames = names.substring(1);
			}
		}
	}
	/**
 	 * <p>方法描述：清空选择的体检项目</p>
 	 * @MethodAuthor qrr,2020年5月25日,clearSelectedItem
	 * */
	public void clearSelectedItem() {
		conditionPO.setSearchItemIds(null);
		this.searchItemNames = null;
	}

	/**
	 * 体检危害因素5007码表弹出框
	 */
	public void selSimpleCode5007Action() {
		Map<String, Object> options = MapUtils.produceDialogMap(null, 700, null, 500);
		Map<String, List<String>> paramMap = new HashMap<>();
		List<String> paramList = new ArrayList<>();
		paramList.add("危害因素");
		paramMap.put("titleName", paramList);
		paramList = new ArrayList<>();
		paramList.add("5007");
		paramMap.put("typeNo", paramList);
		paramList = new ArrayList<>();
		paramList.add(this.conditionPO.getSelectBadRsnIds());
		paramMap.put("selectIds", paramList);
		paramList = new ArrayList<>();
		paramList.add("true");
		paramMap.put("ifShowFirstCode", paramList);
		RequestContext requestContext = RequestContext.getCurrentInstance();
		requestContext.openDialog("/webapp/system/codeMulitySelectList", options, paramMap);
	}

	/**
	 * 体检危害因素5007选择
	 */
	public void onSimpleCode5007Action(SelectEvent event) {
		Map<String, Object> selectedMap = (Map<String, Object>) event.getObject();
		if (null == selectedMap || selectedMap.size() == 0) {
			return;
		}
		List<TsSimpleCode> list = CollectionUtil.castList(TsSimpleCode.class, selectedMap.get("selectPros"));
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		StringBuilder names = new StringBuilder();
		StringBuilder ids = new StringBuilder();
		for (TsSimpleCode t : list) {
			names.append("，").append(t.getCodeName());
			ids.append(",").append(t.getRid());
		}
		this.conditionPO.setSelectBadRsnIds(ids.substring(1));
		this.selectBadRsnNames = names.substring(1);
	}

	/**
	 * 体检危害因素5007清空
	 */
	public void clearSimpleCode5007() {
		this.conditionPO.setSelectBadRsnIds(null);
		this.selectBadRsnNames = null;
	}
	/**
	 *  <p>方法描述：跳转到导出文件下载模块</p>
	 * @MethodAuthor hsj 2025-03-05 14:04
	 */
	public void fileDownloadAction(){
		RequestContext.getCurrentInstance().execute("top.ShortcutMenuClick('','导出文件下载','/webapp/system/tdTjExportList.faces','');");
	}

	public List<TdTjSupoccdiselist> getBtdTjSupoccdiselists() {
		return btdTjSupoccdiselists;
	}

	public void setBtdTjSupoccdiselists(List<TdTjSupoccdiselist> btdTjSupoccdiselists) {
		this.btdTjSupoccdiselists = btdTjSupoccdiselists;
	}

	public List<TdTjContraindlist> getBtdTjContraindlists() {
		return btdTjContraindlists;
	}

	public void setBtdTjContraindlists(List<TdTjContraindlist> btdTjContraindlists) {
		this.btdTjContraindlists = btdTjContraindlists;
	}

	public List<TdTjSupoccdiselist> getTdTjSupoccdiselists() {
		return tdTjSupoccdiselists;
	}

	public void setTdTjSupoccdiselists(List<TdTjSupoccdiselist> tdTjSupoccdiselists) {
		this.tdTjSupoccdiselists = tdTjSupoccdiselists;
	}

	public List<TdTjContraindlist> getTdTjContraindlists() {
		return tdTjContraindlists;
	}

	public void setTdTjContraindlists(List<TdTjContraindlist> tdTjContraindlists) {
		this.tdTjContraindlists = tdTjContraindlists;
	}

	public OutputPanel getArchivePanel() {
		return archivePanel;
	}

	public void setArchivePanel(OutputPanel archivePanel) {
		this.archivePanel = archivePanel;
	}

	public SessionData getSessionData() {
		return sessionData;
	}

	public void setSessionData(SessionData sessionData) {
		this.sessionData = sessionData;
	}

	public List<TsZone> getZoneList() {
		return zoneList;
	}

	public void setZoneList(List<TsZone> zoneList) {
		this.zoneList = zoneList;
	}

	public List<TsZone> getCrptZoneList() {
		return crptZoneList;
	}

	public void setCrptZoneList(List<TsZone> crptZoneList) {
		this.crptZoneList = crptZoneList;
	}

	public String getSearchZoneName() {
		return searchZoneName;
	}

	public void setSearchZoneName(String searchZoneName) {
		this.searchZoneName = searchZoneName;
	}

	public List<TdTjBhk> getTdTjBhkList() {
		return tdTjBhkList;
	}

	public void setTdTjBhkList(List<TdTjBhk> tdTjBhkList) {
		this.tdTjBhkList = tdTjBhkList;
	}

	public TdTjBhk getTdTjBhk() {
		return tdTjBhk;
	}

	public void setTdTjBhk(TdTjBhk tdTjBhk) {
		this.tdTjBhk = tdTjBhk;
	}

	public int getTdTjBhkCount() {
		return tdTjBhkCount;
	}

	public void setTdTjBhkCount(int tdTjBhkCount) {
		this.tdTjBhkCount = tdTjBhkCount;
	}

	public Integer getPersonRid() {
		return personRid;
	}

	public void setPersonRid(Integer personRid) {
		this.personRid = personRid;
	}

	public Integer getTdTjBhkRid() {
		return tdTjBhkRid;
	}

	public void setTdTjBhkRid(Integer tdTjBhkRid) {
		this.tdTjBhkRid = tdTjBhkRid;
	}

	public String getExportFileName() {
		try {
			// 需要转码
			exportFileName = new String("体检档案信息导出".getBytes("GBK"), "ISO-8859-1");
			return exportFileName;
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		return null;
	}


	public void setExportFileName(String exportFileName) {
		this.exportFileName = exportFileName;
	}

	public TreeNode getExportTreeNode() {
		return exportTreeNode;
	}

	public void setExportTreeNode(TreeNode exportTreeNode) {
		this.exportTreeNode = exportTreeNode;
	}

	public TreeNode[] getSelectedExportNodes() {
		return selectedExportNodes;
	}

	public void setSelectedExportNodes(TreeNode[] selectedExportNodes) {
		this.selectedExportNodes = selectedExportNodes;
	}

	public String getExportItems() {
		return exportItems;
	}

	public void setExportItems(String exportItems) {
		this.exportItems = exportItems;
	}

	public String getSelectExportNames() {
		return selectExportNames;
	}

	public void setSelectExportNames(String selectExportNames) {
		this.selectExportNames = selectExportNames;
	}

	public Set<String> getSelectExportItemSet() {
		return selectExportItemSet;
	}

	public void setSelectExportItemSet(Set<String> selectExportItemSet) {
		this.selectExportItemSet = selectExportItemSet;
	}

	public Set<String> getFirstLevelNoSet() {
		return firstLevelNoSet;
	}

	public void setFirstLevelNoSet(Set<String> firstLevelNoSet) {
		this.firstLevelNoSet = firstLevelNoSet;
	}

	public Map<String, TbTjItems> getCodeMap() {
		return codeMap;
	}

	public void setCodeMap(Map<String, TbTjItems> codeMap) {
		this.codeMap = codeMap;
	}

	public Set<String> getLevelNoSet() {
		return levelNoSet;
	}

	public void setLevelNoSet(Set<String> levelNoSet) {
		this.levelNoSet = levelNoSet;
	}
	
	public List<TdTjBhk> getAlltdTjBhkList() {
		return alltdTjBhkList;
	}

	public void setAlltdTjBhkList(List<TdTjBhk> alltdTjBhkList) {
		this.alltdTjBhkList = alltdTjBhkList;
	}

	public Integer getFirstRid() {
		return firstRid;
	}

	public void setFirstRid(Integer firstRid) {
		this.firstRid = firstRid;
	}

	public List<TdTjBhk> getTdTjBhkFjList() {
		return tdTjBhkFjList;
	}

	public void setTdTjBhkFjList(List<TdTjBhk> tdTjBhkFjList) {
		this.tdTjBhkFjList = tdTjBhkFjList;
	}

	public Map<Integer, List<TdTjBhk>> getFjmap() {
		return fjmap;
	}

	public void setFjmap(Map<Integer, List<TdTjBhk>> fjmap) {
		this.fjmap = fjmap;
	}

    public String getSearchBhkrstName() {
        return searchBhkrstName;
    }

    public void setSearchBhkrstName(String searchBhkrstName) {
        this.searchBhkrstName = searchBhkrstName;
    }
	public List<TsSimpleCode> getOnGuardList() {
		return onGuardList;
	}
	public String getSelectOnGuardNames() {
		return selectOnGuardNames;
	}
	public List<TsSimpleCode> getBadRsnList() {
		return badRsnList;
	}
	public String getSelectBadRsnNames() {
		return selectBadRsnNames;
	}
	public String getSearchUnitName() {
		return searchUnitName;
	}
	public List<TsSimpleCode> getSearchBhkrstList() {
		return searchBhkrstList;
	}
	public boolean isIfAdmin() {
		return ifAdmin;
	}
	public TreeNode getWorkSortTree() {
		return workSortTree;
	}
	public String getSelectWorkName() {
		return selectWorkName;
	}
	public TreeNode[] getSelectWorkAges() {
		return selectWorkAges;
	}
	public TreeNode getAgeSortTree() {
		return ageSortTree;
	}
	public TreeNode[] getSelectAges() {
		return selectAges;
	}
	public String getSelectAgeName() {
		return selectAgeName;
	}
	public void setOnGuardList(List<TsSimpleCode> onGuardList) {
		this.onGuardList = onGuardList;
	}
	public void setSelectOnGuardNames(String selectOnGuardNames) {
		this.selectOnGuardNames = selectOnGuardNames;
	}
	public void setBadRsnList(List<TsSimpleCode> badRsnList) {
		this.badRsnList = badRsnList;
	}
	public void setSelectBadRsnNames(String selectBadRsnNames) {
		this.selectBadRsnNames = selectBadRsnNames;
	}
	public void setSearchUnitName(String searchUnitName) {
		this.searchUnitName = searchUnitName;
	}
	public void setSearchBhkrstList(List<TsSimpleCode> searchBhkrstList) {
		this.searchBhkrstList = searchBhkrstList;
	}
	public void setIfAdmin(boolean ifAdmin) {
		this.ifAdmin = ifAdmin;
	}
	public void setWorkSortTree(TreeNode workSortTree) {
		this.workSortTree = workSortTree;
	}
	public void setSelectWorkName(String selectWorkName) {
		this.selectWorkName = selectWorkName;
	}
	public void setSelectWorkAges(TreeNode[] selectWorkAges) {
		this.selectWorkAges = selectWorkAges;
	}
	public void setAgeSortTree(TreeNode ageSortTree) {
		this.ageSortTree = ageSortTree;
	}
	public void setSelectAges(TreeNode[] selectAges) {
		this.selectAges = selectAges;
	}
	public void setSelectAgeName(String selectAgeName) {
		this.selectAgeName = selectAgeName;
	}
	public TdTjBhkInfoBean getTjBhkInfoBean() {
		return tjBhkInfoBean;
	}
	public void setTjBhkInfoBean(TdTjBhkInfoBean tjBhkInfoBean) {
		this.tjBhkInfoBean = tjBhkInfoBean;
	}
	public boolean isIfNbExport() {
		return ifNbExport;
	}
	public void setIfNbExport(boolean ifNbExport) {
		this.ifNbExport = ifNbExport;
	}
	public Integer getExportFlag() {
		return exportFlag;
	}
	public void setExportFlag(Integer exportFlag) {
		this.exportFlag = exportFlag;
	}
	public String getSearchItemNames() {
		return searchItemNames;
	}
	public void setSearchItemNames(String searchItemNames) {
		this.searchItemNames = searchItemNames;
	}
	public TjPersonSearchConditionPO getConditionPO() {
		return conditionPO;
	}
	public void setConditionPO(TjPersonSearchConditionPO conditionPO) {
		this.conditionPO = conditionPO;
	}
	public List<TsSimpleCode> getCardTypeList() {
		return cardTypeList;
	}
	public void setCardTypeList(List<TsSimpleCode> cardTypeList) {
		this.cardTypeList = cardTypeList;
	}

	public Integer getAsyncExportTimes() {
		return asyncExportTimes;
	}

	public void setAsyncExportTimes(Integer asyncExportTimes) {
		this.asyncExportTimes = asyncExportTimes;
	}

	public TdTjExport getTdTjExport() {
		return tdTjExport;
	}

	public void setTdTjExport(TdTjExport tdTjExport) {
		this.tdTjExport = tdTjExport;
	}

	public Map<String, TsSimpleCode> getBusTypeMap() {
		return busTypeMap;
	}

	public void setBusTypeMap(Map<String, TsSimpleCode> busTypeMap) {
		this.busTypeMap = busTypeMap;
	}

	public Map<Integer, String> getTsSimpleCodeMap() {
		return tsSimpleCodeMap;
	}

	public void setTsSimpleCodeMap(Map<Integer, String> tsSimpleCodeMap) {
		this.tsSimpleCodeMap = tsSimpleCodeMap;
	}
}
