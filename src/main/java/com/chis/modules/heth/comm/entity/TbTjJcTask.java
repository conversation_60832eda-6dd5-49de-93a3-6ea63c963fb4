package com.chis.modules.heth.comm.entity;

import javax.persistence.*;

import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsZone;
import com.chis.modules.system.entity.TsSimpleCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2023-4-18
 */
@Entity
@Table(name = "TB_TJ_JC_TASK")
@SequenceGenerator(name = "TbTjJcTask", sequenceName = "TB_TJ_JC_TASK_SEQ", allocationSize = 1)
public class TbTjJcTask implements java.io.Serializable {

    private static final long serialVersionUID = 1L;
    private Integer rid;
    private Integer year;
    private TsUnit fkByOrgId;
    private TbTjCrpt fkByCrptId;
    private String crptName;
    private TsZone fkByZoneId;
    private String creditCode;
    private String address;
    private TsSimpleCode fkByIndusTypeId;
    private TsSimpleCode fkByEconomyId;
    private TsSimpleCode fkByCrptSizeId;
    private String linkMan;
    private String linkPhone;
    private Integer state;
    private String backRsn;
    private Integer delMark;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    /**
     * 劳动者花名册
     */
    private List<TbTjJcTaskPsn> jcTaskPsnList;

    public TbTjJcTask() {
    }

    public TbTjJcTask(Integer rid) {
        this.rid = rid;
    }

    @Id
    @Column(name = "RID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TbTjJcTask")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @Column(name = "YEAR")
    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    @ManyToOne
    @JoinColumn(name = "ORG_ID")
    public TsUnit getFkByOrgId() {
        return fkByOrgId;
    }

    public void setFkByOrgId(TsUnit fkByOrgId) {
        this.fkByOrgId = fkByOrgId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getFkByCrptId() {
        return fkByCrptId;
    }

    public void setFkByCrptId(TbTjCrpt fkByCrptId) {
        this.fkByCrptId = fkByCrptId;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @ManyToOne
    @JoinColumn(name = "ZONE_ID")
    public TsZone getFkByZoneId() {
        return fkByZoneId;
    }

    public void setFkByZoneId(TsZone fkByZoneId) {
        this.fkByZoneId = fkByZoneId;
    }

    @Column(name = "CREDIT_CODE")
    public String getCreditCode() {
        return creditCode;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    @Column(name = "ADDRESS")
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @ManyToOne
    @JoinColumn(name = "INDUS_TYPE_ID")
    public TsSimpleCode getFkByIndusTypeId() {
        return fkByIndusTypeId;
    }

    public void setFkByIndusTypeId(TsSimpleCode fkByIndusTypeId) {
        this.fkByIndusTypeId = fkByIndusTypeId;
    }

    @ManyToOne
    @JoinColumn(name = "ECONOMY_ID")
    public TsSimpleCode getFkByEconomyId() {
        return fkByEconomyId;
    }

    public void setFkByEconomyId(TsSimpleCode fkByEconomyId) {
        this.fkByEconomyId = fkByEconomyId;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_SIZE_ID")
    public TsSimpleCode getFkByCrptSizeId() {
        return fkByCrptSizeId;
    }

    public void setFkByCrptSizeId(TsSimpleCode fkByCrptSizeId) {
        this.fkByCrptSizeId = fkByCrptSizeId;
    }

    @Column(name = "LINK_MAN")
    public String getLinkMan() {
        return linkMan;
    }

    public void setLinkMan(String linkMan) {
        this.linkMan = linkMan;
    }

    @Column(name = "LINK_PHONE")
    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Column(name = "BACK_RSN")
    public String getBackRsn() {
        return backRsn;
    }

    public void setBackRsn(String backRsn) {
        this.backRsn = backRsn;
    }

    @Column(name = "DEL_MARK")
    public Integer getDelMark() {
        return delMark;
    }

    public void setDelMark(Integer delMark) {
        this.delMark = delMark;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID")
    public Integer getCreateManid() {
        return createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByMainId", orphanRemoval = true)
    @OrderBy(value = "rid")
    public List<TbTjJcTaskPsn> getJcTaskPsnList() {
        return jcTaskPsnList;
    }

    public void setJcTaskPsnList(List<TbTjJcTaskPsn> jcTaskPsnList) {
        this.jcTaskPsnList = jcTaskPsnList;
    }
}