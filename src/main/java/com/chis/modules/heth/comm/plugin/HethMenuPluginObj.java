package com.chis.modules.heth.comm.plugin;

import com.chis.modules.system.entity.TsMenu;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 职业卫生平台-通用模块菜单
 */
public class HethMenuPluginObj {

    public static Set<TsMenu> menuSet;

    /**
     * 菜单默认图标
     */
    private static final String DEFAULT_MENU_ICON = "default.png";

    static {
        menuSet = new HashSet<TsMenu>();

        /*
        //职业健康检查业务
        menuSet.add(new TsMenu("051.003", "体检项目维护", "heth_comm_tjxmwh", "体检项目维护", Short.valueOf("1"), "/webapp/heth/comm/tbTjItemsList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 3));
        menuSet.add(new TsMenu("059.007", "项目组合维护", "heth_comm_xmzhwh", "项目组合维护", Short.valueOf("1"), "/webapp/heth/comm/tbZwtjItemListComm.faces", "customer-relation.png", "flowchart.png", new Date(), 1, 7));

        menuSet.add(new TsMenu("051.004", "标准值配置", "heth_comm_bzzpz", "标准值配置", Short.valueOf("1"), "/webapp/heth/comm/tbTjStadItemsStatusList.faces?unitId=0", "internal-coordination.png", "user-manage.png", new Date(), 1, 4));
        menuSet.add(new TsMenu("051.001", "体检信息录入", "heth_comm_tjxxlr", "体检信息录入", Short.valueOf("1"), "/webapp/heth/comm/tjxxlr/tdTjBhkCltListComm.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 1));
        menuSet.add(new TsMenu("051.002", "个案查询/劳动者档案查询", "heth_comm_gacx", "个案查询", Short.valueOf("1"), "/webapp/heth/comm/tbTjPersonSearchList.faces", "default.png", "portal2.png", new Date(), 1, 2));
        //健康检查数据审核、统计业务
        menuSet.add(new TsMenu("051.005", "体检最新档案导出", "heth_comm_tjzxdndc", "体检最新档案导出", Short.valueOf("1"), "/webapp/heth/comm/tbTjNewestRecSearchList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 4));
        menuSet.add(new TsMenu("031.016","体检复查结果查询","heth_cxtj_tjfcjgcx","体检复查结果查询", Short.valueOf("1"),"/webapp/heth/comm/recheckResultList.faces","eye.png","pie_chart_yellow.png",new Date(),1,16));
        menuSet.add(new TsMenu("202.016","国家体检数据导入","heth_comm_gjtjsjdr","国家体检数据导入", Short.valueOf("1"),"/webapp/heth/comm/countryBhkDataImport.faces","default.png","1826.png",new Date(),1,16));

        menuSet.add(new TsMenu("031.016", "职业健康检查数据审核", "heth_zyjkjcsjsh", "职业健康检查数据审核", Short.valueOf("1"), "/webapp/heth/comm/bhkcheck/tdTjBHkCheckList.faces", "customer-inquiries.png", "customer-system-upgrade.png", new Date(), 1, 16));
        menuSet.add(new TsMenu("059.006", "职业健康检查审核情况查询", "heth_zyjkjcsshqkcx", "职业健康检查审核情况查询", Short.valueOf("1"), "/webapp/heth/comm/bhkcheck/tdTjBHkCheckOrg.faces", "customer-inquiries.png", "customer-system-upgrade.png", new Date(), 1, 16));
        menuSet.add(new TsMenu("059.011", "职业健康检查数据上传情况跟踪", "heth_comm_zyjkjcsjscqkgz", "职业健康检查数据上传情况跟踪", Short.valueOf("1"), "/webapp/heth/comm/hethInspectDataTrackList.faces", "customer-inquiries.png", "knowledge-sort.png", new Date(), 1, 11));
        menuSet.add(new TsMenu("045.001.019", "体检疑似职业病报告情况", "heth_tjyszybbgqk", "体检疑似职业病报告情况", Short.valueOf("1"), "/webapp/heth/comm/tdTjBhkYszybReportList.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 19));
        menuSet.add(new TsMenu("045.001.026", "疑似职业病诊断情况", "heth_yszybzdqk", "疑似职业病诊断情况", Short.valueOf("1"), "/webapp/heth/comm/reportcard/sodReportCardDiagSituation.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 26));

        //吉林职卫特有
        menuSet.add(new TsMenu("033.005", "企业综合展示", "heth_comm_crpt_show", "企业综合展示", Short.valueOf("1"), "/webapp/heth/comm/crptshow/tbTjCrptShowList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 5));
        menuSet.add(new TsMenu("045.009", "重点危害因素检查情况查询", "heth_zdwhysjcqkcx", "重点危害因素检查情况查询", Short.valueOf("1"), "/webapp/heth/comm/tdZwZdBadRsnInspectSearchList.faces", "default.png", "task.png", new Date(), 1, 10));
        menuSet.add(new TsMenu("045.005", "重点危害因素检查情况审核", "heth_zdwhysjcqksh", "重点危害因素检查情况审核", Short.valueOf("1"), "/webapp/heth/comm/tdZwZdBadRsnInspectCheckList.faces", "default.png", "task.png", new Date(), 1, 6));
        menuSet.add(new TsMenu("045.006", "超范围服务预警处置-初审", "heth_cfwfwyjczsh1", "超范围服务预警处置-初审", Short.valueOf("1"), "/webapp/heth/comm/tdZwyjOtrWarnList.faces?tag=1", "default.png", "task.png", new Date(), 1, 7));
        menuSet.add(new TsMenu("045.007", "超范围服务预警处置-终审", "heth_cfwfwyjczsh2", "超范围服务预警处置-终审", Short.valueOf("1"), "/webapp/heth/comm/tdZwyjOtrWarnList.faces?tag=2", "default.png", "task.png", new Date(), 1, 8));
        menuSet.add(new TsMenu("045.008", "危急值查询", "heth_wjzcx", "危急值查询", Short.valueOf("1"), "/webapp/heth/comm/tdZwDangerValueSearchList.faces", "default.png", "task.png", new Date(), 1, 9));
        menuSet.add(new TsMenu("033.009", "用人单位综合管理", "heth_comm_crpt_audit", "用人单位综合管理", Short.valueOf("1"), "/webapp/heth/comm/crptaudit/crptAuditList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 9));
        menuSet.add(new TsMenu("033.010", "用人单位信息审核", "heth_comm_crpt_check", "用人单位信息审核", Short.valueOf("1"), "/webapp/heth/comm/crptaudit/tbTjCrptInvestCheckList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 10));
        menuSet.add(new TsMenu("033.011", "用人单位信息查询", "heth_comm_crpt_view", "用人单位信息查询", Short.valueOf("1"), "/webapp/heth/comm/crptaudit/tbTjCrptInvestViewList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 11));

        //通用-用人单位业务
        menuSet.add(new TsMenu("033.002","用人单位审核","heth_cxtj_yrdwsh","用人单位审核", Short.valueOf("1"),"/webapp/heth/comm/crpt/check/crptCheckList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,2));
        menuSet.add(new TsMenu("033.007", "场所监测数据导入", "heth_comm_csjcsjdr", "场所监测数据导入", Short.valueOf("1"), "/webapp/heth/comm/siteMonitoringData.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 7));
        menuSet.add(new TsMenu("033.012", "用人单位综合展示", "heth_comm_crpt_check_show", "用人单位综合展示", Short.valueOf("1"), "/webapp/heth/comm/crptaudit/tbTjCrptCheckShowList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 12));
        menuSet.add(new TsMenu("033.015", "用人单位综合展示（新）", "heth_comm_crpt_integrated_show", "用人单位综合展示（新）", Short.valueOf("1"), "/webapp/heth/comm/crptshow/crptIntegratedShowList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 15));
        menuSet.add(new TsMenu("033.016", "用人单位综合展示（修订）", "heth_comm_crpt_gs_integrated_show", "用人单位综合展示（修订）", Short.valueOf("1"), "/webapp/heth/comm/gscrptshow/gsCrptIntegratedShowList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 16));

        //甘肃预警特有，功能合并个案审核，不再使用
        menuSet.add(new TsMenu("059.001", "GBZ188不规范数据上报", "heth_gbz188bgfsjsb", "GBZ188不规范数据上报", Short.valueOf("1"), "/webapp/heth/comm/tdZwGbz188NostdListComm.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 1));
        menuSet.add(new TsMenu("059.002", "GBZ188不规范数据审核", "heth_bgfsjsh", "GBZ188不规范数据审核", Short.valueOf("1"), "/webapp/heth/comm/tdZwGbz188NostdCheckListComm.faces", "customer-inquiries.png", "customer-system-upgrade.png", new Date(), 1, 2));
        menuSet.add(new TsMenu("045.001.018", "GBZ188不规范数据查询", "heth_bgfsjcx_comm", "GBZ188不规范数据查询", Short.valueOf("1"), "/webapp/heth/comm/tdZwGbz188NostdSearchListComm.faces", "customer-inquiries.png", "customer-system-upgrade.png", new Date(), 1, 18));

        //报告卡业务
        //诊断机构录入的疑似职业病报告卡菜单
        menuSet.add(new TsMenu("045.001.022", "疑似职业病报告卡录入", "heth_yszybbgkfillin", "疑似职业病报告卡录入", Short.valueOf("1"), "/webapp/heth/comm/reportcard/sodReportCardFillIn.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 22));
        //体检机构填报的疑似职业病报告卡菜单
        menuSet.add(new TsMenu("045.001.011", "疑似职业病报告卡填报（新）", "heth_yszybbgknew", "疑似职业病报告卡填报", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwYszybRptList.faces?tag=1", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 11));
        menuSet.add(new TsMenu("045.001.018", "疑似职业病报告卡查询", "heth_yszybbgksearchnew", "疑似职业病报告卡查询", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwYszybRptSearchList.faces", "fswsxxtb-x.png", "fswsxxtb-d.png", new Date(), 1, 11));
        menuSet.add(new TsMenu("045.001.010", "疑似职业病报告卡审核（新）", "heth_yszybbgkshnew", "疑似职业病报告卡审核（新）", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwYszybRptCardCheckList.faces", "customer-inquiries.png", "customer-system-upgrade.png", new Date(), 1, 10));
        //职业病诊断流程内的职业病报告卡审核业务
        menuSet.add(new TsMenu("045.001.019","职业病报告卡审核","heth_zybbgksh","职业病报告卡审核", Short.valueOf("1"),"/webapp/heth/comm/reportcard/tdZwZybReportCardReviewList.faces","customer-inquiries.png","customer-system-upgrade.png",new Date(),1,19));

        menuSet.add(new TsMenu("042.005", "职业性有害因素监测卡填报", "heth_comm_zyxyhysjcktb", "职业性有害因素监测卡填报", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwHethChkSmaryList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 4));
        menuSet.add(new TsMenu("042.006", "职业性有害因素监测卡审核", "heth_comm_zyxyhysjcksh", "职业性有害因素监测卡审核", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwHethChkSmaryCheckList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 5));
        menuSet.add(new TsMenu("045.012", "职业病鉴定报告卡填报", "heth_comm_zybjdbgtb", "职业病鉴定报告卡填报", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwHethAppraisalRptCardList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 6));
        menuSet.add(new TsMenu("045.013", "职业病鉴定报告卡审核", "heth_comm_zybjdbgksh", "职业病鉴定报告卡审核", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwjdArchivesCardCheckList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 7));

        //职业病诊断业务
        menuSet.add(new TsMenu("032.001","完善单位信息(重庆)","diag_wsdwxx","完善单位信息", Short.valueOf("1"),"/webapp/heth/comm/perfectUnitInfoComm.faces","default.png","task.png",new Date(),1,1));

       //机构服务信息报送
        menuSet.add(new TsMenu("055.001", "职业卫生技术服务信息报送卡填报", "heth_comm_zywsjsfwxxbsktb", "职业卫生技术服务信息报送卡填报", Short.valueOf("1"), "/webapp/heth/comm/reportcard/occHethCardFillInList.faces", "default.png", "pie_chart_yellow.png", new Date(), 1, 1));
        menuSet.add(new TsMenu("055.002", "放射卫生技术服务信息报送卡填报", "heth_comm_fswsjsfwxxbs", "放射卫生技术服务信息报送卡填报", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwSrvorgCardList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 2));
        menuSet.add(new TsMenu("055.003", "职业卫生技术服务信息报送卡查询", "heth_comm_gs_zywsjsfwxxbskcx", "职业卫生技术服务信息报送卡查询", Short.valueOf("1"), "/webapp/heth/comm/reportcard/occHethCardFillInQuery.faces", "default.png", "pie_chart_yellow.png", new Date(), 1, 3));
        menuSet.add(new TsMenu("055.004", "放射卫生技术服务信息报送卡查询", "heth_comm_gs_fswsjsfwxxbscx", "放射卫生技术服务信息报送卡查询", Short.valueOf("1"), "/webapp/heth/comm/reportcard/tdZwSrvorgCardQuery.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 4));

        menuSet.add(new TsMenu("034.031", "技术服务申报统计", "heth_comm_jsfwsbtj", "技术服务申报统计", Short.valueOf("1"), "/webapp/heth/comm/tdZwOcchethRptTj.faces", "default.png", "MYGH-B-045.png", new Date(), 1, 1));

        //广西职卫静态页面演示模块
        menuSet.add(new TsMenu("990","广西职卫静态页面","gxjtymys","广西职卫静态页面", Short.valueOf("0"),"#","xiugai.png",null,new Date(),1,990));
        menuSet.add(new TsMenu("990.001", "诊断、鉴定工作流程管理界面", "gxjtymys_zdjdflowmana", "诊断、鉴定工作流程管理界面", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/diagProcessNodeManage.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 1));
        menuSet.add(new TsMenu("990.002", "农民工尘肺病病例报告", "gxjtymys_nmgcfb", "农民工尘肺病病例报告", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/migrantWorkersPneumoconiosis.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 2));
        menuSet.add(new TsMenu("990.003", "尘肺病患者救治救助信息查询", "gxjtymys_cfbhzjzjzxxcx", "尘肺病患者救治救助信息查询", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/cfbhzjzjzxxcxPage.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 3));
        menuSet.add(new TsMenu("990.004", "农民工尘肺病患者救助基本信息录入", "gxjtymys_cfbhzjzjzxxlr", "农民工尘肺病患者救助基本信息录入", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/cfbhzjzjzxxlrPage.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 4));
        menuSet.add(new TsMenu("990.005", "尘肺病患者救治救助信息统计", "gxjtymys_cfbhzjzxxtj", "尘肺病患者救治救助信息统计", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/patientBringAnaly.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 5));
        menuSet.add(new TsMenu("990.006", "灯横幅板块", "gxjtymys_dhf", "灯横幅板块", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/lampBanner.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 6));
        menuSet.add(new TsMenu("990.007", "职业健康体检受理审核", "gxjtymys_tjslsh", "职业健康体检受理审核", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/tjRecieveAudit.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 7));
        menuSet.add(new TsMenu("990.008", "放射诊疗机构检测受理", "gxjtymys_fszljgjcsl", "放射诊疗机构检测受理", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/radDiagInstitutionAccept.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 8));
        menuSet.add(new TsMenu("990.009", "职业健康检查机构能力保持情况表填报", "gxjtymys_zyjkjcjgnlbcqkbtb", "职业健康检查机构能力保持情况表填报", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/zyjkjcjgnlbcqkbtbPage.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 9));
        menuSet.add(new TsMenu("990.010", "职业健康检查项目表填报", "gxjtymys_zyjkjcxmb", "职业健康检查项目表填报", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/termList.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 10));
        menuSet.add(new TsMenu("990.011", "化学品毒性鉴定机构资质信息", "gxjtymys_hxpdxjdjgzzxx", "化学品毒性鉴定机构资质信息", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/chemicalToxicityAssessOrg.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 11));
        menuSet.add(new TsMenu("990.012", "化学品毒性鉴定报告查询", "gxjtymys_hxpdxjdbgcx", "化学品毒性鉴定报告查询", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/chemicalToxicityAssessReport.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 12));
        menuSet.add(new TsMenu("990.013", "放射诊疗机构医疗照射水平监测信息", "gxjtymys_fszljgylzsspjcxx", "放射诊疗机构医疗照射水平监测信息", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/radioClinicsMedical.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 13));
        menuSet.add(new TsMenu("990.014", "职业卫生技术服务统计", "gxjtymys_zywsjsfwtj", "职业卫生技术服务统计", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/technicalServiceStatistics.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 14));
        menuSet.add(new TsMenu("990.015", "职业卫生技术服务管理", "gxjtymys_zywsjsfwgl", "职业卫生技术服务管理", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/zywsjsfwglPage.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 15));
        menuSet.add(new TsMenu("990.016", "放射卫生服务情况统计", "gxjtymys_fswsfwqktj", "放射卫生服务情况统计", Short.valueOf("1"), "/webapp/heth/comm/gxjtymys/fswsfwqktjPage.faces", "internal-coordination.png", "user-manage.png", new Date(), 1, 16));

        menuSet.add(new TsMenu("041.017", "主动监测任务维护", "heth_comm_zdjcrwwh", "主动监测任务维护", Short.valueOf("1"), "/webapp/heth/comm/activeMonitoring/taskMaintenanceList.faces", "customer-inquiries.png", "knowledge-sort.png", new Date(), 1, 17));
        menuSet.add(new TsMenu("041.001", "主动监测统计类别维护", "heth_comm_zdjctjlbwh", "主动监测统计类别维护", Short.valueOf("1"), "/webapp/heth/comm/zdjctjlbwh/tdZdzybActiveDetListComm.faces", "customer-relation.png", "flowchart.png", new Date(), 1, 1));

     */
        // menuSet.add(new TsMenu("208", "职业健康检查常规监测汇总", "zyjkjccgjchz", "职业健康检查常规监测汇总", Short.valueOf("0"), "#", "xiugai.png", null, new Date(), 1, 208));
        // menuSet.add(new TsMenu("208.001", "职业健康检查常规监测汇总填报", "zyjkjccgjchztb", "职业健康检查常规监测汇总填报", Short.valueOf("1"), "/webapp/heth/comm/bhkcheck/routineMonitoring/summarizeAndFillInSearch.faces", "customer-inquiries.png", "knowledge-sort.png", new Date(), 1, 1));
        // menuSet.add(new TsMenu("208.002", "职业健康检查常规监测月度汇总", "zyjkjccgjcydhz", "职业健康检查常规监测月度汇总", Short.valueOf("1"), "/webapp/heth/comm/bhkcheck/routineMonitoring/tdZwMonthBhkProvList.faces", "customer-inquiries.png", "knowledge-sort.png", new Date(), 1, 2));
        // menuSet.add(new TsMenu("208.003", "职业健康检查常规监测月度/季度汇总", "zyjkjccgjcydjdhz", "职业健康检查常规监测月度/季度汇总", Short.valueOf("1"), "/webapp/heth/comm/bhkcheck/routineMonitoring/summarizeStatisticsByMonthOrQuarter.faces", "customer-inquiries.png", "knowledge-sort.png", new Date(), 1, 3));
        // menuSet.add(new TsMenu("208.004", "职业健康检查常规监测年度汇总", "zyjkjccgjcndhz", "职业健康检查常规监测年度汇总", Short.valueOf("1"), "/webapp/heth/comm/bhkcheck/routineMonitoring/annualSummary.faces", "customer-inquiries.png", "knowledge-sort.png", new Date(), 1, 4));
        //menuSet.add(new TsMenu("202.017", "国家体检数据导入", "heth_comm_sdgjtjsjdr", "国家体检数据导入", Short.valueOf("1"), "/webapp/heth/comm/tdGjBhkList.faces", "default.png", "knowledge-sort.png", new Date(), 1, 17));

        //职业卫生技术服务申报管理-gs(devtest)
        //menuSet.add(new TsMenu("037","职业卫生技术服务申报管理","jsffgl","职业卫生技术服务申报管理", Short.valueOf("0"),"#","default.png","MYGH-B-045.png",new Date(),1,3));
        //menuSet.add(new TsMenu("037.001","职业卫生技术服务申报（gs）","jsffgl_fwsb","职业卫生技术服务申报（gs）", Short.valueOf("1"),"/webapp/heth/comm/reportcard/tdZwOcchethRptCommList.faces","fswsxxtb-x.png","fswsxxtb-d.png",new Date(),1,1));
        //menuSet.add(new TsMenu("037.002","职业卫生技术服务档案查询（gs）","jsffgl_dacx","职业卫生技术服务档案查询（gs）", Short.valueOf("1"),"/webapp/heth/comm/reportcard/tdZwOcchethRptFindCommList.faces","default.png","portal2.png",new Date(),1,2));

        //职业卫生技术服务信息报送卡-标配
        //menuSet.add(new TsMenu("055.005", "职业卫生技术服务信息报送卡填报", "heth_comm_general_zywsjsfwxxbsktb", "职业卫生技术服务信息报送卡填报", Short.valueOf("1"), "/webapp/heth/comm/generalReportCard/occHethCardList.faces", "default.png", "pie_chart_yellow.png", new Date(), 1, 5));
        //menuSet.add(new TsMenu("055.006", "职业卫生技术服务信息报送卡查询", "heth_comm_general_zywsjsfwxxbskcx", "职业卫生技术服务信息报送卡查询", Short.valueOf("1"), "/webapp/heth/comm/generalReportCard/occHethCardQuery.faces", "default.png", "pie_chart_yellow.png", new Date(), 1, 6));

    }
}
