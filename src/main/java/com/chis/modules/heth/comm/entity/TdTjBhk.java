package com.chis.modules.heth.comm.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;

import com.chis.modules.system.entity.TsSimpleCode;
import com.chis.modules.system.entity.TsUnit;
import com.chis.modules.system.entity.TsUserInfo;

/**
 * 体检主表
 *
 * 修改人：lxk; 修改时间：2014-09-17<br/>
 * 修改内容：去除多余的精度属性配置<br/>
 *
 * <AUTHOR>
 * @createDate 2014-9-1
 *
 * <p>修订内容：新增字段上次体检编号（复检使用）LAST_BHK_CODE、上次初检编号（复检使用）LAST_FST_BHK_CODE</p>
 * @ClassReviser qrr,2018年12月21日,TdTjBhk
 */
@Entity
@Table(name = "TD_TJ_BHK", uniqueConstraints = @UniqueConstraint(columnNames = {
        "BHK_CODE", "BHKORG_ID" }))
@SequenceGenerator(name = "TdTjBhk_Seq", sequenceName = "TD_TJ_BHK_SEQ", allocationSize = 1)
public class TdTjBhk implements java.io.Serializable {

    private static final long serialVersionUID = 3020254852273595419L;
    private Integer rid;
    private TdTjPerson tdTjPerson;
    private TbTjSrvorg tbTjSrvorg;
    private TbTjCrpt tbTjCrpt;
    private TsSimpleCode tsSimpleCode;
    private String bhkCode;
    private String personName;
    private String sex;
    private String idc;
    private Date brth;
    private Integer age;
    private String isxmrd;
    private String lnktel;
    private String dpt;
    private String wrknum;
    private Double wrklnt;
    private Integer wrklntmonth;
    private Double tchbadrsntim;
    private Integer tchbadrsnmonth;
    private String workName;
    private Integer bhkType;
    private Date bhkDate;
    private String bhkrst;
    private String mhkadv;
    private String ocpBhkrstdes;
    private String mhkdctno;
    private String mhkdct;
    private Date jdgdat;
    private String badrsn;
    private Integer ifLackitm;
    private Integer ifTargetdis;
    private Integer ifWrktabu;
    private Integer ifInteitmLack;
    private Integer processLack;
    private String lackMsg;
    private Integer ifRhk;
    private Date createDate;
    private Integer createManid;
    private Date modifyDate;
    private Integer modifyManid;
    private List<TdTjMhkrst> tdTjMhkrsts = new ArrayList<TdTjMhkrst>(0);
    private List<TdTjBadrsns> tdTjBadrsnses = new ArrayList<TdTjBadrsns>(0);
    private List<TdTjSupoccdiselist> tdTjSupoccdiselists = new ArrayList<TdTjSupoccdiselist>(
            0);
    private List<TdTjEmhistory> tdTjEmhistories = new ArrayList<TdTjEmhistory>(
            0);
    private List<TdTjContraindlist> tdTjContraindlists = new ArrayList<TdTjContraindlist>(
            0);
    private List<TdTjAnamnesis> tdTjAnamnesises = new ArrayList<TdTjAnamnesis>(
            0);
    private List<TdTjExmsdata> tdTjExmsdatas = new ArrayList<TdTjExmsdata>(0);
    private List<TdTjBhksub> tdTjBhksubs = new ArrayList<TdTjBhksub>(0);
    private List<TdTjSymptom> tdTjSymptoms = new ArrayList<TdTjSymptom>(0);
    private List<TdTjTchBadrsns> tdTjTchBadrsns=new ArrayList<>(0);

    /**人员类型：1 正常 2 外籍人员 3 无身份证人员*/
    private Integer psnType;
    /**上次体检编号（复检使用）*/
    private String lastBhkCode;
    /**上次初检编号（复检使用）*/
    private String lastFstBhkCode;
    /**复检数据是否需要显示“+”   1:显示加号  2：显示减号 */
    private Integer ifFjNeedShow;
    // Constructors
    private String crptName;
    /**UUID*/
    private String uuid;
    //报告打印日期
    private Date rptPrintDate;
    /**审核判定结论项目状态（重点职业病）*/
    private String checkState;
    /**退回原因（重点职业病）*/
    private String backRsn;

    private TsSimpleCode fkByWorkTypeId;
    private String workOther;
    private TsSimpleCode fkByCardTypeId;
    private Date harmStartDate;

    /** 监测类型 1：常规监测 2：主动监测 */
    private Integer jcType;

    private String otherBadRsn;

    /** 个案审核相关字段 start */
    private Integer ifIntoCheck;
    private Integer ifIndusTypeNostd;
    private Integer ifCrptSizeNostd;
    private Integer ifAbnomal;
    private Integer state;
    private Date countySmtDate;
    private Integer countyRst;
    private String countyAuditAdv;
    private TsUnit fkByCountyChkOrgid;
    private Date citySmtDate;
    private Integer cityRst;
    private String cityAuditAdv;
    private TsUnit fkByCiytChkOrgid;
    private Date proSmtDate;
    private Integer cityRst2;
    private String proAuditAdv;
    private TsUnit fkByProChkOrgid;
    private Date dealCompleteDate;
    /**用工单位*/
    private TbTjCrpt fkByEntrustCrptId;
    private TsUserInfo fkByProChkPenid;
    /**国家退回原因*/
    private String errMsg;
    /**数据来源*/
    private Integer dataSource;

    /** 机构情况说明（个案审核查询）20220122 */
    private String orgStateDesc;
    /** 初审-审核方式（个案审核）20220122 */
    private Integer countyCheckWay;
    /** 复审-审核方式（个案审核）20220122 */
    private Integer cityCheckWay;
    /** 终审-审核方式（个案审核）20220122 */
    private Integer proCheckWay;
    /**+首次数据报告是否及时20220122*/
    private Integer ifReportIntime;
    /**+应复检未检20220506*/
    private Integer needTjButNo;
    /**++最新主检结论ID20220426*/
    private TsSimpleCode newBhkrstId;
    /** +工龄是否不规范（个案审核）20220418 */
    private Integer ifWrkAgeNostd;
    /**
     * 接触其他危害因素名称20220704
     */
    private String tchOtherBadrsn;
    private Integer ifOnlyFs;
    /**接触危害因素名称-数据库中不存在*/
    private String  tchBadrsn;
    /**体检危害因素名称-数据库中不存在*/
    private String  bhkBadrsn;
    private TsSimpleCode  fkByProtectEquId;
    /**最新提交日期*/
    private Date submitChangedate;
    /** default constructor */
    public TdTjBhk() {
    }

    public TdTjBhk(Integer rid, Date rptPrintDate) {
        this.rid = rid;
        this.rptPrintDate = rptPrintDate;
    }

    /** minimal constructor */
    public TdTjBhk(Integer rid, TdTjPerson tdTjPerson, TbTjSrvorg tbTjSrvorg,
                   String bhkCode, String personName, String sex, Integer bhkType,
                   Date bhkDate, String bhkrst, Date createDate, Integer createManid) {
        this.rid = rid;
        this.tdTjPerson = tdTjPerson;
        this.tbTjSrvorg = tbTjSrvorg;
        this.bhkCode = bhkCode;
        this.personName = personName;
        this.sex = sex;
        this.bhkType = bhkType;
        this.bhkDate = bhkDate;
        this.bhkrst = bhkrst;
        this.createDate = createDate;
        this.createManid = createManid;
    }

    /** full constructor */
    public TdTjBhk(Integer rid, TdTjPerson tdTjPerson, TbTjSrvorg tbTjSrvorg,
                   TbTjCrpt tbTjCrpt, TsSimpleCode tsSimpleCode, String bhkCode,
                   String personName, String sex, String idc, Date brth, Integer age,
                   String isxmrd, String lnktel, String dpt, String wrknum,
                   Double wrklnt, Integer wrklntmonth, Double tchbadrsntim,
                   Integer tchbadrsnmonth, String workName, Integer bhkType,
                   Date bhkDate, String bhkrst, String mhkadv, String ocpBhkrstdes,
                   String mhkdctno, String mhkdct, Date jdgdat, String badrsn,
                   Integer ifLackitm, Integer ifTargetdis, Integer ifWrktabu,
                   Integer ifInteitmLack, Integer ifRhk, Date createDate,
                   Integer createManid, Date modifyDate, Integer modifyManid,
                   List<TdTjMhkrst> tdTjMhkrsts, List<TdTjBadrsns> tdTjBadrsnses,
                   List<TdTjSupoccdiselist> tdTjSupoccdiselists,
                   List<TdTjEmhistory> tdTjEmhistories,
                   List<TdTjContraindlist> tdTjContraindlists,
                   List<TdTjAnamnesis> tdTjAnamnesises,
                   List<TdTjExmsdata> tdTjExmsdatas, List<TdTjBhksub> tdTjBhksubs,
                   List<TdTjSymptom> tdTjSymptoms) {
        this.rid = rid;
        this.tdTjPerson = tdTjPerson;
        this.tbTjSrvorg = tbTjSrvorg;
        this.tbTjCrpt = tbTjCrpt;
        this.tsSimpleCode = tsSimpleCode;
        this.bhkCode = bhkCode;
        this.personName = personName;
        this.sex = sex;
        this.idc = idc;
        this.brth = brth;
        this.age = age;
        this.isxmrd = isxmrd;
        this.lnktel = lnktel;
        this.dpt = dpt;
        this.wrknum = wrknum;
        this.wrklnt = wrklnt;
        this.wrklntmonth = wrklntmonth;
        this.tchbadrsntim = tchbadrsntim;
        this.tchbadrsnmonth = tchbadrsnmonth;
        this.workName = workName;
        this.bhkType = bhkType;
        this.bhkDate = bhkDate;
        this.bhkrst = bhkrst;
        this.mhkadv = mhkadv;
        this.ocpBhkrstdes = ocpBhkrstdes;
        this.mhkdctno = mhkdctno;
        this.mhkdct = mhkdct;
        this.jdgdat = jdgdat;
        this.badrsn = badrsn;
        this.ifLackitm = ifLackitm;
        this.ifTargetdis = ifTargetdis;
        this.ifWrktabu = ifWrktabu;
        this.ifInteitmLack = ifInteitmLack;
        this.ifRhk = ifRhk;
        this.createDate = createDate;
        this.createManid = createManid;
        this.modifyDate = modifyDate;
        this.modifyManid = modifyManid;
        this.tdTjMhkrsts = tdTjMhkrsts;
        this.tdTjBadrsnses = tdTjBadrsnses;
        this.tdTjSupoccdiselists = tdTjSupoccdiselists;
        this.tdTjEmhistories = tdTjEmhistories;
        this.tdTjContraindlists = tdTjContraindlists;
        this.tdTjAnamnesises = tdTjAnamnesises;
        this.tdTjExmsdatas = tdTjExmsdatas;
        this.tdTjBhksubs = tdTjBhksubs;
        this.tdTjSymptoms = tdTjSymptoms;
    }

    @Id
    @Column(name = "RID", unique = true )
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "TdTjBhk_Seq")
    public Integer getRid() {
        return this.rid;
    }

    public void setRid(Integer rid) {
        this.rid = rid;
    }

    @ManyToOne
    @JoinColumn(name = "PERSON_ID" )
    public TdTjPerson getTdTjPerson() {
        return this.tdTjPerson;
    }

    public void setTdTjPerson(TdTjPerson tdTjPerson) {
        this.tdTjPerson = tdTjPerson;
    }

    @ManyToOne
    @JoinColumn(name = "BHKORG_ID" )
    public TbTjSrvorg getTbTjSrvorg() {
        return this.tbTjSrvorg;
    }

    public void setTbTjSrvorg(TbTjSrvorg tbTjSrvorg) {
        this.tbTjSrvorg = tbTjSrvorg;
    }

    @ManyToOne
    @JoinColumn(name = "CRPT_ID")
    public TbTjCrpt getTbTjCrpt() {
        return this.tbTjCrpt;
    }

    public void setTbTjCrpt(TbTjCrpt tbTjCrpt) {
        this.tbTjCrpt = tbTjCrpt;
    }

    @ManyToOne
    @JoinColumn(name = "ONGUARD_STATEID")
    public TsSimpleCode getTsSimpleCode() {
        return this.tsSimpleCode;
    }

    public void setTsSimpleCode(TsSimpleCode tsSimpleCode) {
        this.tsSimpleCode = tsSimpleCode;
    }

    @Column(name = "BHK_CODE" , length = 50)
    public String getBhkCode() {
        return this.bhkCode;
    }

    public void setBhkCode(String bhkCode) {
        this.bhkCode = bhkCode;
    }

    @Column(name = "PERSON_NAME" , length = 200)
    public String getPersonName() {
        return this.personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    @Column(name = "SEX" , length = 10)
    public String getSex() {
        return this.sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    @Column(name = "IDC", length = 50)
    public String getIdc() {
        return this.idc;
    }

    public void setIdc(String idc) {
        this.idc = idc;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BRTH", length = 7)
    public Date getBrth() {
        return this.brth;
    }

    public void setBrth(Date brth) {
        this.brth = brth;
    }

    @Column(name = "AGE")
    public Integer getAge() {
        return this.age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    @Column(name = "ISXMRD", length = 10)
    public String getIsxmrd() {
        return this.isxmrd;
    }

    public void setIsxmrd(String isxmrd) {
        this.isxmrd = isxmrd;
    }

    @Column(name = "LNKTEL", length = 50)
    public String getLnktel() {
        return this.lnktel;
    }

    public void setLnktel(String lnktel) {
        this.lnktel = lnktel;
    }

    @Column(name = "DPT", length = 50)
    public String getDpt() {
        return this.dpt;
    }

    public void setDpt(String dpt) {
        this.dpt = dpt;
    }

    @Column(name = "WRKNUM", length = 50)
    public String getWrknum() {
        return this.wrknum;
    }

    public void setWrknum(String wrknum) {
        this.wrknum = wrknum;
    }

    @Column(name = "WRKLNT")
    public Double getWrklnt() {
        return this.wrklnt;
    }

    public void setWrklnt(Double wrklnt) {
        this.wrklnt = wrklnt;
    }

    @Column(name = "WRKLNTMONTH")
    public Integer getWrklntmonth() {
        return this.wrklntmonth;
    }

    public void setWrklntmonth(Integer wrklntmonth) {
        this.wrklntmonth = wrklntmonth;
    }

    @Column(name = "TCHBADRSNTIM")
    public Double getTchbadrsntim() {
        return this.tchbadrsntim;
    }

    public void setTchbadrsntim(Double tchbadrsntim) {
        this.tchbadrsntim = tchbadrsntim;
    }

    @Column(name = "TCHBADRSNMONTH")
    public Integer getTchbadrsnmonth() {
        return this.tchbadrsnmonth;
    }

    public void setTchbadrsnmonth(Integer tchbadrsnmonth) {
        this.tchbadrsnmonth = tchbadrsnmonth;
    }

    @Column(name = "WORK_NAME", length = 100)
    public String getWorkName() {
        return this.workName;
    }

    public void setWorkName(String workName) {
        this.workName = workName;
    }

    @Column(name = "BHK_TYPE" )
    public Integer getBhkType() {
        return this.bhkType;
    }

    public void setBhkType(Integer bhkType) {
        this.bhkType = bhkType;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BHK_DATE" , length = 7)
    public Date getBhkDate() {
        return this.bhkDate;
    }

    public void setBhkDate(Date bhkDate) {
        this.bhkDate = bhkDate;
    }

    @Lob
    @Column(name = "BHKRST" )
    public String getBhkrst() {
        return this.bhkrst;
    }

    public void setBhkrst(String bhkrst) {
        this.bhkrst = bhkrst;
    }

    @Lob
    @Column(name = "MHKADV")
    public String getMhkadv() {
        return this.mhkadv;
    }

    public void setMhkadv(String mhkadv) {
        this.mhkadv = mhkadv;
    }

    @Column(name = "OCP_BHKRSTDES", length = 500)
    public String getOcpBhkrstdes() {
        return this.ocpBhkrstdes;
    }

    public void setOcpBhkrstdes(String ocpBhkrstdes) {
        this.ocpBhkrstdes = ocpBhkrstdes;
    }

    @Column(name = "MHKDCTNO", length = 50)
    public String getMhkdctno() {
        return this.mhkdctno;
    }

    public void setMhkdctno(String mhkdctno) {
        this.mhkdctno = mhkdctno;
    }

    @Column(name = "MHKDCT", length = 50)
    public String getMhkdct() {
        return this.mhkdct;
    }

    public void setMhkdct(String mhkdct) {
        this.mhkdct = mhkdct;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "JDGDAT", length = 7)
    public Date getJdgdat() {
        return this.jdgdat;
    }

    public void setJdgdat(Date jdgdat) {
        this.jdgdat = jdgdat;
    }

    @Column(name = "BADRSN", length = 1000)
    public String getBadrsn() {
        return this.badrsn;
    }

    public void setBadrsn(String badrsn) {
        this.badrsn = badrsn;
    }

    @Column(name = "IF_LACKITM")
    public Integer getIfLackitm() {
        return this.ifLackitm;
    }

    public void setIfLackitm(Integer ifLackitm) {
        this.ifLackitm = ifLackitm;
    }

    @Column(name = "IF_TARGETDIS")
    public Integer getIfTargetdis() {
        return this.ifTargetdis;
    }

    public void setIfTargetdis(Integer ifTargetdis) {
        this.ifTargetdis = ifTargetdis;
    }

    @Column(name = "IF_WRKTABU")
    public Integer getIfWrktabu() {
        return this.ifWrktabu;
    }

    public void setIfWrktabu(Integer ifWrktabu) {
        this.ifWrktabu = ifWrktabu;
    }

    @Column(name = "IF_INTEITM_LACK")
    public Integer getIfInteitmLack() {
        return this.ifInteitmLack;
    }

    public void setIfInteitmLack(Integer ifInteitmLack) {
        this.ifInteitmLack = ifInteitmLack;
    }

    @Column(name = "PROCESS_LACK")
    public Integer getProcessLack() {
        return processLack;
    }

    public void setProcessLack(Integer processLack) {
        this.processLack = processLack;
    }

    @Column(name = "LACK_MSG")
    public String getLackMsg() {
        return lackMsg;
    }

    public void setLackMsg(String lackMsg) {
        this.lackMsg = lackMsg;
    }

    @Column(name = "IF_RHK")
    public Integer getIfRhk() {
        return this.ifRhk;
    }

    public void setIfRhk(Integer ifRhk) {
        this.ifRhk = ifRhk;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATE_DATE" )
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "CREATE_MANID" )
    public Integer getCreateManid() {
        return this.createManid;
    }

    public void setCreateManid(Integer createManid) {
        this.createManid = createManid;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MODIFY_DATE")
    public Date getModifyDate() {
        return this.modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Column(name = "MODIFY_MANID")
    public Integer getModifyManid() {
        return this.modifyManid;
    }

    public void setModifyManid(Integer modifyManid) {
        this.modifyManid = modifyManid;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjMhkrst> getTdTjMhkrsts() {
        return this.tdTjMhkrsts;
    }

    public void setTdTjMhkrsts(List<TdTjMhkrst> tdTjMhkrsts) {
        this.tdTjMhkrsts = tdTjMhkrsts;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjBadrsns> getTdTjBadrsnses() {
        return this.tdTjBadrsnses;
    }

    public void setTdTjBadrsnses(List<TdTjBadrsns> tdTjBadrsnses) {
        this.tdTjBadrsnses = tdTjBadrsnses;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjSupoccdiselist> getTdTjSupoccdiselists() {
        return this.tdTjSupoccdiselists;
    }

    public void setTdTjSupoccdiselists(
            List<TdTjSupoccdiselist> tdTjSupoccdiselists) {
        this.tdTjSupoccdiselists = tdTjSupoccdiselists;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjEmhistory> getTdTjEmhistories() {
        return this.tdTjEmhistories;
    }

    public void setTdTjEmhistories(List<TdTjEmhistory> tdTjEmhistories) {
        this.tdTjEmhistories = tdTjEmhistories;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjContraindlist> getTdTjContraindlists() {
        return this.tdTjContraindlists;
    }

    public void setTdTjContraindlists(List<TdTjContraindlist> tdTjContraindlists) {
        this.tdTjContraindlists = tdTjContraindlists;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjAnamnesis> getTdTjAnamnesises() {
        return this.tdTjAnamnesises;
    }

    public void setTdTjAnamnesises(List<TdTjAnamnesis> tdTjAnamnesises) {
        this.tdTjAnamnesises = tdTjAnamnesises;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjExmsdata> getTdTjExmsdatas() {
        return this.tdTjExmsdatas;
    }

    public void setTdTjExmsdatas(List<TdTjExmsdata> tdTjExmsdatas) {
        this.tdTjExmsdatas = tdTjExmsdatas;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjBhksub> getTdTjBhksubs() {
        return this.tdTjBhksubs;
    }

    public void setTdTjBhksubs(List<TdTjBhksub> tdTjBhksubs) {
        this.tdTjBhksubs = tdTjBhksubs;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "tdTjBhk")
    public List<TdTjSymptom> getTdTjSymptoms() {
        return this.tdTjSymptoms;
    }

    public void setTdTjSymptoms(List<TdTjSymptom> tdTjSymptoms) {
        this.tdTjSymptoms = tdTjSymptoms;
    }
    @Column(name="LAST_BHK_CODE")
    public String getLastBhkCode() {
        return lastBhkCode;
    }

    public void setLastBhkCode(String lastBhkCode) {
        this.lastBhkCode = lastBhkCode;
    }
    @Column(name="LAST_FST_BHK_CODE")
    public String getLastFstBhkCode() {
        return lastFstBhkCode;
    }

    public void setLastFstBhkCode(String lastFstBhkCode) {
        this.lastFstBhkCode = lastFstBhkCode;
    }

    @Column(name = "PSN_TYPE")
    public Integer getPsnType() {
        return psnType;
    }

    public void setPsnType(Integer psnType) {
        this.psnType = psnType;
    }

    @Transient
    public Integer getIfFjNeedShow() {
        return ifFjNeedShow;
    }

    public void setIfFjNeedShow(Integer ifFjNeedShow) {
        this.ifFjNeedShow = ifFjNeedShow;
    }

    @Column(name = "CRPT_NAME")
    public String getCrptName() {
        return crptName;
    }

    public void setCrptName(String crptName) {
        this.crptName = crptName;
    }

    @Column(name = "UUID", length = 50)
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    @Column(name = "RPT_PRINT_DATE")
    public Date getRptPrintDate() {
        return rptPrintDate;
    }

    public void setRptPrintDate(Date rptPrintDate) {
        this.rptPrintDate = rptPrintDate;
    }
    @Column(name = "CHECK_STATE")
    public String getCheckState() {
        return checkState;
    }
    public void setCheckState(String checkState) {
        this.checkState = checkState;
    }

    @Column(name = "BACK_RSN")
    public String getBackRsn() {
        return backRsn;
    }

    public void setBackRsn(String backRsn) {
        this.backRsn = backRsn;
    }

    @ManyToOne
    @JoinColumn(name = "WORK_TYPE_ID")
    public TsSimpleCode getFkByWorkTypeId() {
        return fkByWorkTypeId;
    }

    public void setFkByWorkTypeId(TsSimpleCode fkByWorkTypeId) {
        this.fkByWorkTypeId = fkByWorkTypeId;
    }

    @Column(name = "WORK_OTHER")
    public String getWorkOther() {
        return workOther;
    }

    public void setWorkOther(String workOther) {
        this.workOther = workOther;
    }

    @ManyToOne
    @JoinColumn(name = "CARD_TYPE_ID")
    public TsSimpleCode getFkByCardTypeId() {
        return fkByCardTypeId;
    }

    public void setFkByCardTypeId(TsSimpleCode fkByCardTypeId) {
        this.fkByCardTypeId = fkByCardTypeId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "HARM_START_DATE")
    public Date getHarmStartDate() {
        return harmStartDate;
    }

    public void setHarmStartDate(Date harmStartDate) {
        this.harmStartDate = harmStartDate;
    }

    @Column(name = "JC_TYPE")
    public Integer getJcType() {
        return jcType;
    }

    public void setJcType(Integer jcType) {
        this.jcType = jcType;
    }
    @Column(name = "OTHER_BADRSN")
    public String getOtherBadRsn() {
        return otherBadRsn;
    }

    public void setOtherBadRsn(String otherBadRsn) {
        this.otherBadRsn = otherBadRsn;
    }

    @Column(name = "IF_INTO_CHECK")
    public Integer getIfIntoCheck() {
        return ifIntoCheck;
    }

    public void setIfIntoCheck(Integer ifIntoCheck) {
        this.ifIntoCheck = ifIntoCheck;
    }

    @Column(name = "IF_INDUS_TYPE_NOSTD")
    public Integer getIfIndusTypeNostd() {
        return ifIndusTypeNostd;
    }

    public void setIfIndusTypeNostd(Integer ifIndusTypeNostd) {
        this.ifIndusTypeNostd = ifIndusTypeNostd;
    }

    @Column(name = "IF_CRPT_SIZE_NOSTD")
    public Integer getIfCrptSizeNostd() {
        return ifCrptSizeNostd;
    }

    public void setIfCrptSizeNostd(Integer ifCrptSizeNostd) {
        this.ifCrptSizeNostd = ifCrptSizeNostd;
    }

    @Column(name = "IF_ABNOMAL")
    public Integer getIfAbnomal() {
        return ifAbnomal;
    }

    public void setIfAbnomal(Integer ifAbnomal) {
        this.ifAbnomal = ifAbnomal;
    }

    @Column(name = "STATE")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "COUNTY_SMT_DATE")
    public Date getCountySmtDate() {
        return countySmtDate;
    }

    public void setCountySmtDate(Date countySmtDate) {
        this.countySmtDate = countySmtDate;
    }

    @Column(name = "COUNTY_RST")
    public Integer getCountyRst() {
        return countyRst;
    }

    public void setCountyRst(Integer countyRst) {
        this.countyRst = countyRst;
    }

    @Column(name = "COUNTY_AUDIT_ADV")
    public String getCountyAuditAdv() {
        return countyAuditAdv;
    }

    public void setCountyAuditAdv(String countyAuditAdv) {
        this.countyAuditAdv = countyAuditAdv;
    }

    @ManyToOne
    @JoinColumn(name = "COUNTY_CHK_ORGID")
    public TsUnit getFkByCountyChkOrgid() {
        return fkByCountyChkOrgid;
    }

    public void setFkByCountyChkOrgid(TsUnit fkByCountyChkOrgid) {
        this.fkByCountyChkOrgid = fkByCountyChkOrgid;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CITY_SMT_DATE")
    public Date getCitySmtDate() {
        return citySmtDate;
    }

    public void setCitySmtDate(Date citySmtDate) {
        this.citySmtDate = citySmtDate;
    }

    @Column(name = "CITY_RST")
    public Integer getCityRst() {
        return cityRst;
    }

    public void setCityRst(Integer cityRst) {
        this.cityRst = cityRst;
    }

    @Column(name = "CITY_AUDIT_ADV")
    public String getCityAuditAdv() {
        return cityAuditAdv;
    }

    public void setCityAuditAdv(String cityAuditAdv) {
        this.cityAuditAdv = cityAuditAdv;
    }

    @ManyToOne
    @JoinColumn(name = "CIYT_CHK_ORGID")
    public TsUnit getFkByCiytChkOrgid() {
        return fkByCiytChkOrgid;
    }

    public void setFkByCiytChkOrgid(TsUnit fkByCiytChkOrgid) {
        this.fkByCiytChkOrgid = fkByCiytChkOrgid;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "PRO_SMT_DATE")
    public Date getProSmtDate() {
        return proSmtDate;
    }

    public void setProSmtDate(Date proSmtDate) {
        this.proSmtDate = proSmtDate;
    }

    @Column(name = "CITY_RST2")
    public Integer getCityRst2() {
        return cityRst2;
    }

    public void setCityRst2(Integer cityRst2) {
        this.cityRst2 = cityRst2;
    }

    @Column(name = "PRO_AUDIT_ADV")
    public String getProAuditAdv() {
        return proAuditAdv;
    }

    public void setProAuditAdv(String proAuditAdv) {
        this.proAuditAdv = proAuditAdv;
    }

    @ManyToOne
    @JoinColumn(name = "PRO_CHK_ORGID")
    public TsUnit getFkByProChkOrgid() {
        return fkByProChkOrgid;
    }

    public void setFkByProChkOrgid(TsUnit fkByProChkOrgid) {
        this.fkByProChkOrgid = fkByProChkOrgid;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "DEAL_COMPLETE_DATE")
    public Date getDealCompleteDate() {
        return dealCompleteDate;
    }

    public void setDealCompleteDate(Date dealCompleteDate) {
        this.dealCompleteDate = dealCompleteDate;
    }
    @ManyToOne
    @JoinColumn(name = "ENTRUST_CRPT_ID")
    public TbTjCrpt getFkByEntrustCrptId() {
        return fkByEntrustCrptId;
    }

    public void setFkByEntrustCrptId(TbTjCrpt fkByEntrustCrptId) {
        this.fkByEntrustCrptId = fkByEntrustCrptId;
    }

    @ManyToOne
    @JoinColumn(name = "PRO_CHK_PSNID")
    public TsUserInfo getFkByProChkPenid() {
        return fkByProChkPenid;
    }

    public void setFkByProChkPenid(TsUserInfo fkByProChkPenid) {
        this.fkByProChkPenid = fkByProChkPenid;
    }

    @Column(name = "ERR_MSG")
    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    @Column(name = "DATA_SOURCE")
    public Integer getDataSource() {
        return dataSource;
    }

    public void setDataSource(Integer dataSource) {
        this.dataSource = dataSource;
    }

    @Column(name = "ORG_STATE_DESC")
    public String getOrgStateDesc() {
        return orgStateDesc;
    }

    public void setOrgStateDesc(String orgStateDesc) {
        this.orgStateDesc = orgStateDesc;
    }

    @Column(name = "COUNTY_CHECK_WAY")
    public Integer getCountyCheckWay() {
        return countyCheckWay;
    }

    public void setCountyCheckWay(Integer countyCheckWay) {
        this.countyCheckWay = countyCheckWay;
    }

    @Column(name = "CITY_CHECK_WAY")
    public Integer getCityCheckWay() {
        return cityCheckWay;
    }

    public void setCityCheckWay(Integer cityCheckWay) {
        this.cityCheckWay = cityCheckWay;
    }

    @Column(name = "PRO_CHECK_WAY")
    public Integer getProCheckWay() {
        return proCheckWay;
    }

    public void setProCheckWay(Integer proCheckWay) {
        this.proCheckWay = proCheckWay;
    }

    @Column(name = "IF_REPORT_INTIME")
    public Integer getIfReportIntime() {
        return ifReportIntime;
    }

    public void setIfReportIntime(Integer ifReportIntime) {
        this.ifReportIntime = ifReportIntime;
    }
    @Column(name = "NEED_FJ_BUT_NO")
    public Integer getNeedTjButNo() {
        return needTjButNo;
    }

    public void setNeedTjButNo(Integer needTjButNo) {
        this.needTjButNo = needTjButNo;
    }
    @ManyToOne
    @JoinColumn(name = "NEW_BHKRST_ID")
    public TsSimpleCode getNewBhkrstId() {
        return newBhkrstId;
    }

    public void setNewBhkrstId(TsSimpleCode newBhkrstId) {
        this.newBhkrstId = newBhkrstId;
    }

    @Column(name = "IF_WRK_AGE_NOSTD")
    public Integer getIfWrkAgeNostd() {
        return ifWrkAgeNostd;
    }

    public void setIfWrkAgeNostd(Integer ifWrkAgeNostd) {
        this.ifWrkAgeNostd = ifWrkAgeNostd;
    }

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY, mappedBy = "fkByBhkId")
    public List<TdTjTchBadrsns> getTdTjTchBadrsns() {
        return tdTjTchBadrsns;
    }

    public void setTdTjTchBadrsns(List<TdTjTchBadrsns> tdTjTchBadrsns) {
        this.tdTjTchBadrsns = tdTjTchBadrsns;
    }

    @Column(name = "TCH_OTHER_BADRSN")
    public String getTchOtherBadrsn() {
        return tchOtherBadrsn;
    }

    public void setTchOtherBadrsn(String tchOtherBadrsn) {
        this.tchOtherBadrsn = tchOtherBadrsn;
    }
    @Column(name = "IF_ONLY_FS")
    public Integer getIfOnlyFs() {
        return ifOnlyFs;
    }

    public void setIfOnlyFs(Integer ifOnlyFs) {
        this.ifOnlyFs = ifOnlyFs;
    }

    @Transient
    public String getTchBadrsn() {
        return tchBadrsn;
    }

    public void setTchBadrsn(String tchBadrsn) {
        this.tchBadrsn = tchBadrsn;
    }
    @Transient
    public String getBhkBadrsn() {
        return bhkBadrsn;
    }

    public void setBhkBadrsn(String bhkBadrsn) {
        this.bhkBadrsn = bhkBadrsn;
    }

    @ManyToOne
    @JoinColumn(name = "PROTECT_EQU_ID" )
    public TsSimpleCode getFkByProtectEquId() {
        return fkByProtectEquId;
    }
    public void setFkByProtectEquId(TsSimpleCode fkByProtectEquId) {
        this.fkByProtectEquId = fkByProtectEquId;
    }

    @Column(name = "SUBMIT_CHANGEDATE")
    public Date getSubmitChangedate() {
        return submitChangedate;
    }
    public void setSubmitChangedate(Date submitChangedate) {
        this.submitChangedate = submitChangedate;
    }
}
