<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>职业卫生平台-质控考核</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_BADRSN_STAND';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_BADRSN_STAND (
                RID                  INTEGER              not null,
                CHECK_TYPE           NUMBER(1),
          CHECK_NAME           NVARCHAR2(200),
          STATE_MARK           NUMBER(1),
          XH                   NUMBER(3),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_BADRSN_STAND primary key (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_BADRSN_STAND_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_BADRSN_STAND_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_SCORES';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_SCORES (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          SCORE                NUMBER(5,2)          not null,
          SPECIAL_FLAG         NUMBER(2),
          XH                   NUMBER(3),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_SCORES primary key (RID),
                constraint FK_TB_ZW_ZK_SCORES1 foreign key (MAIN_ID)
              references TB_ZW_ZK_BADRSN_STAND (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_SCORES_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_SCORES_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_SCORE_INDEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_SCORE_INDEX (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          INDEX_ID             INTEGER              not null,
          INDEX_XH             NVARCHAR2(10),
          XH                   NUMBER(3),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_SCORE_INDEX primary key (RID),
                constraint FK_TB_ZW_ZK_SCORE_ITEM1 foreign key (MAIN_ID)
              references TB_ZW_ZK_SCORES (RID),
          constraint FK_TB_ZW_ZK_SCORE_ITEM2 foreign key (INDEX_ID)
              references TS_SIMPLE_CODE (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_SCORE_INDEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_SCORE_INDEX_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_MAIN (
                RID                  INTEGER              not null,
                CHECK_TYPE           NUMBER(1),
          ORG_ID               INTEGER,
          CHECK_DATE           DATE,
          TOTAL_CHECK_VAL      NUMBER(5,2),
          TOTAL_SCORE_VAL      NUMBER(5,2),
          STATE_MARK           NUMBER(1),
          RECORD_ORG_ID        INTEGER,
          IF_NEED_IMPROVE      NUMBER(1),
          IF_IMPROVE_END       NUMBER(1),
          IMPROVE_FILE_NAME    NVARCHAR2(100),
          IMPROVE_FILE_ADDR    NVARCHAR2(100),
          WRITE_PATH           NVARCHAR2(100),
          DEL_MARK             NUMBER(1),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_MAIN primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_MAIN1 foreign key (ORG_ID)
              references TS_UNIT (RID),
          constraint FK_TD_ZW_ZK_CHECK_MAIN2 foreign key (RECORD_ORG_ID)
              references TS_UNIT (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_MAIN_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_TABLE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_TABLE (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          CHECK_TABLE_ID       INTEGER,
          CHECK_PSN            NVARCHAR2(100),
          STATE_MARK           NUMBER(1),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_TABLE primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_TABLE1 foreign key (MAIN_ID)
              references TD_ZW_ZK_CHECK_MAIN (RID),
          constraint FK_TD_ZW_ZK_CHECK_TABLE2 foreign key (CHECK_TABLE_ID)
              references TB_ZW_ZK_BADRSN_STAND (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_TABLE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_TABLE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_ITEM (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          ITEM_ID              INTEGER,
          CHECK_VAL            NUMBER(5,2),
          SCORE_VAL            NUMBER(5,2),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_ITEM primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_ITEM1 foreign key (MAIN_ID)
              references TD_ZW_ZK_CHECK_TABLE (RID),
          constraint FK_TD_ZW_ZK_CHECK_ITEM2 foreign key (ITEM_ID)
              references TS_SIMPLE_CODE (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_ITEM_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_SUB (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          SCORE_ID             INTEGER,
          SCORE_VAL            NUMBER(5,2),
          RMK                  NVARCHAR2(2000),
          TECH_PSN             NUMBER(5),
          MEDIUM_PSN           NUMBER(5),
          MEDIUM_PSN_RATE      NUMBER(5,2),
          EXTERNAL_PSN         NUMBER(5),
          EXTERNAL_PSN_RATE    NUMBER(5,2),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_SUB primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_SUB1 foreign key (MAIN_ID)
              references TD_ZW_ZK_CHECK_ITEM (RID),
          constraint FK_TD_ZW_ZK_CHECK_SUB2 foreign key (SCORE_ID)
              references TB_ZW_ZK_SCORES (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_SUB_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_SCORE_DEDUCT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_SCORE_DEDUCT (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          DEDUCT_ID            INTEGER,
          XH                   NUMBER(3),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_SCORE_DEDUCT primary key (RID),
                constraint FK_TB_ZW_ZK_SCORE_DEDUCT1 foreign key (MAIN_ID)
              references TB_ZW_ZK_SCORES (RID),
          constraint FK_TB_ZW_ZK_SCORE_DEDUCT2 foreign key (DEDUCT_ID)
              references TS_SIMPLE_CODE (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_SCORE_DEDUCT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_SCORE_DEDUCT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_DEDUCT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_DEDUCT (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          DEDUCT_ID            INTEGER,
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_DEDUCT primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_DEDUCT1 foreign key (MAIN_ID)
              references TD_ZW_ZK_CHECK_SUB (RID),
          constraint FK_TD_ZW_ZK_CHECK_DEDUCT2 foreign key (DEDUCT_ID)
              references TB_ZW_ZK_SCORE_DEDUCT (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_DEDUCT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_DEDUCT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_PROVE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_PROVE (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          ANNEX_NAME           NVARCHAR2(100),
          ANNEX_ADDR           NVARCHAR2(100),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_PROVE primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_PROVE1 foreign key (MAIN_ID)
              references TD_ZW_ZK_CHECK_MAIN (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_PROVE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_PROVE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_SCORES')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('SCORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORES MODIFY SCORE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN3'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CONSTRAINT FK_TD_ZW_ZK_CHECK_MAIN3 FOREIGN KEY (CHECK_RST_ID)  REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('IF_HG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD IF_HG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_SCORES')
                AND COLUMN_NAME = UPPER('ITEM_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORES ADD ITEM_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_SCORES2'
                AND TABLE_NAME = 'TB_ZW_ZK_SCORES';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORES ADD CONSTRAINT FK_TB_ZW_ZK_SCORES2 FOREIGN KEY (ITEM_TYPE_ID)  REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_RST_RULE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_RST_RULE (
                RID                  INTEGER              not null,
                CHECK_TYPE           NUMBER(1),
          CHECK_GRAGE_ID       INTEGER,
          XH                   NUMBER(3),
          BAK                  NVARCHAR2(200),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_RST_RULE primary key (RID),
                constraint FK_TB_ZW_ZK_RST_RULE1 foreign key (CHECK_GRAGE_ID)
              references TS_SIMPLE_CODE (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_RST_RULE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_RST_RULE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ZK_RST_RULE_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TB_ZW_ZK_RST_RULE_SUB (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          ITEM_TYPE_ID         INTEGER,
          SCORE_RST_ID         INTEGER,
          SCORE_RST2_ID        INTEGER,
          CAL_TAG              NUMBER(1),
          CAL_NUM              NUMBER(3),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TB_ZW_ZK_RST_RULE_SUB primary key (RID),
                constraint FK_TB_ZW_ZK_RST_RULE_SUB1 foreign key (MAIN_ID)
              references TB_ZW_ZK_RST_RULE (RID),
            constraint FK_TB_ZW_ZK_RST_RULE_SUB2 foreign key (ITEM_TYPE_ID)
              references TS_SIMPLE_CODE (RID),
            constraint FK_TB_ZW_ZK_RST_RULE_SUB3 foreign key (SCORE_RST2_ID)
              references TS_SIMPLE_CODE (RID),
            constraint FK_TB_ZW_ZK_RST_RULE_SUB4 foreign key (SCORE_RST_ID)
              references TS_SIMPLE_CODE (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ZK_RST_RULE_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TB_ZW_ZK_RST_RULE_SUB_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_SUMMARY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZK_CHECK_SUMMARY (
                RID                  INTEGER              not null,
                MAIN_ID              INTEGER,
          ITEM_TYPE_ID         INTEGER,
          SCORE_RST_ID         INTEGER,
          ITEM_NUM             NUMBER(3),
          CREATE_DATE          TIMESTAMP            not null,
          CREATE_MANID         INTEGER              not null,
          MODIFY_DATE          TIMESTAMP,
          MODIFY_MANID         INTEGER,
                constraint PK_TD_ZW_ZK_CHECK_SUMMARY primary key (RID),
                constraint FK_TD_ZW_ZK_CHECK_SUMMARY1 foreign key (MAIN_ID)
              references TD_ZW_ZK_CHECK_MAIN (RID),
            constraint FK_TD_ZW_ZK_CHECK_SUMMARY2 foreign key (ITEM_TYPE_ID)
              references TS_SIMPLE_CODE (RID),
            constraint FK_TD_ZW_ZK_CHECK_SUMMARY3 foreign key (SCORE_RST_ID)
              references TS_SIMPLE_CODE (RID)
              )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_SUMMARY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_SUMMARY_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('SCORE_RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD SCORE_RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_SUB3'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD CONSTRAINT FK_TD_ZW_ZK_CHECK_SUB3 FOREIGN KEY (SCORE_RST_ID)  REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_BADRSN_STAND')
                AND COLUMN_NAME = UPPER('CHECK_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_BADRSN_STAND ADD CHECK_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_RST_RULE')
                AND COLUMN_NAME = UPPER('CHECK_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_RST_RULE ADD CHECK_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN4'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_ZK_CHECK_MAIN add constraint FK_TD_ZW_ZK_CHECK_MAIN4 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_RST_RULE2'
                AND TABLE_NAME = 'TB_ZW_ZK_RST_RULE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TB_ZW_ZK_RST_RULE add constraint FK_TB_ZW_ZK_RST_RULE2 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_BADRSN_STAND1'
                AND TABLE_NAME = 'TB_ZW_ZK_BADRSN_STAND';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TB_ZW_ZK_BADRSN_STAND add constraint FK_TB_ZW_ZK_BADRSN_STAND1 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_SCORES')
                AND COLUMN_NAME = UPPER('BUS_EXTENDS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORES ADD BUS_EXTENDS NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_SUMMARY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_CHECK_SUMMARY
                (
                   RID                  INTEGER              not null,
                   CHECK_TYPE_ID        INTEGER,
                   UNIT_ID              INTEGER,
                   CHECK_DATE           DATE,
                   CHECK_ADV            NVARCHAR2(1000),
                   IF_CONFIRM           NUMBER(1),
                   FILE_PATH            NVARCHAR2(100),
                   CHECK_EXPERTS        NVARCHAR2(100),
                   CHECK_LEADERS        NVARCHAR2(100),
                   CHECK_UNIT_ID        INTEGER,
                   STATE                NUMBER(1),
                   DEL_MARK             NUMBER(1),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_SUMMARY primary key (RID),
                   constraint FK_TD_ZW_CHECK_SUMMARY1 foreign key (CHECK_TYPE_ID)
                    references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZW_CHECK_SUMMARY2 foreign key (UNIT_ID)
                    references TS_UNIT (RID),
                   constraint FK_TD_ZW_CHECK_SUMMARY3 foreign key (CHECK_UNIT_ID)
                    references TS_UNIT (RID)

                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_SUMMARY_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_CHECK_SUMMARY_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_TECH_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_CHECK_TECH_MAIN
                (
                   RID                  INTEGER              not null,
                   CHECK_TYPE_ID        INTEGER,
                   UNIT_ID              INTEGER,
                   CHECK_ADDR           NVARCHAR2(100),
                   CHECK_DATE           DATE,
                   CHECK_CONTENT        NVARCHAR2(1000),
                   CHECK_DESC           NVARCHAR2(1000),
                   CHECK_RST            NUMBER(1),
                   CHECK_EXPERTS        NVARCHAR2(100),
                   IF_CONFIRM           NUMBER(1),
                   FILE_PATH            NVARCHAR2(100),
                   EXPERT_ADV           NVARCHAR2(1000),
                   LEADER_ADV           NVARCHAR2(1000),
                   CHECK_UNIT_ID        INTEGER,
                   STATE                NUMBER(1),
                   DEL_MARK             NUMBER(1),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_TECH_MAIN primary key (RID),
                   constraint FK_TD_ZW_CHECK_TECH_MAIN1 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZW_CHECK_TECH_MAIN2 foreign key (UNIT_ID) references TS_UNIT (RID),
                   constraint FK_TD_ZW_CHECK_TECH_MAIN3 foreign key (CHECK_UNIT_ID) references TS_UNIT (RID)
                )';
              END IF;
            END;
        ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_TECH_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_TECH_MAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
        ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_TECH_PSN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_TECH_PSN
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   PSN_NAME             NVARCHAR2(50),
                   TITLE_ID             INTEGER,
                   PSN_TYPE_ID          INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_TECH_PSN primary key (RID),
                   constraint FK_TD_ZW_CHECK_TECH_PSN1 foreign key (MAIN_ID)
                    references TD_ZW_CHECK_TECH_MAIN (RID),
                   constraint FK_TD_ZW_CHECK_TECH_PSN2 foreign key (TITLE_ID)
                    references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZW_CHECK_TECH_PSN3 foreign key (PSN_TYPE_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
        ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_TECH_PSN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_TECH_PSN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
        ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_TECH_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_TECH_ITEM
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ITEM_ID              INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_TECH_ITEM primary key (RID),
                   constraint FK_TD_ZW_CHECK_TECH_ITEM1 foreign key (MAIN_ID)
                    references TD_ZW_CHECK_TECH_MAIN (RID),
                   constraint FK_TD_ZW_CHECK_TECH_ITEM2 foreign key (ITEM_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
        ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_TECH_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_TECH_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                START WITH 1 INCREMENT BY 1 CACHE 20';
              END IF;
            END;
        ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_CONCLUSION';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_CONCLUSION
                    (
                       RID                  INTEGER              not null,
                       CHECK_TYPE_ID        INTEGER,
                       UNIT_ID              INTEGER,
                       CHECK_DATE           DATE,
                       CHECK_RST            NUMBER(1) default 1,
                       OTHERS               NVARCHAR2(1000),
                       SUMMARY              NVARCHAR2(1000),
                       CONCLUSION_ID        INTEGER,
                       IF_CONFIRM           NUMBER(1) default 1,
                       FILE_PATH            NVARCHAR2(100),
                       EXPERT_ADV           NVARCHAR2(1000),
                       LEADER_ADV           NVARCHAR2(1000),
                       CHECK_UNIT_ID        INTEGER,
                       REL_ZK_CHECK_ID      INTEGER,
                       STATE                NUMBER(1),
                       DEL_MARK             NUMBER(1),
                       CREATE_MANID         INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_CHECK_CONCLUSION primary key (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION1 foreign key (CHECK_TYPE_ID) references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION2 foreign key (UNIT_ID) references TS_UNIT (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION3 foreign key (CHECK_UNIT_ID) references TS_UNIT (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION4 foreign key (REL_ZK_CHECK_ID) references TD_ZW_ZK_CHECK_MAIN (RID),
                       constraint FK_TD_ZW_CHECK_CONCLUSION5 foreign key (CONCLUSION_ID) references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_CONCLUSION_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_CONCLUSION_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_ITEM_RST';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_ITEM_RST
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       ITEM_ID              INTEGER,
                       RST_ID               INTEGER,
                       NUMS                 NUMBER(3),
                       CREATE_MANID         INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_CHECK_ITEM_RST primary key (RID),
                       constraint FK_TD_ZW_CHECK_ITEM_RST1 foreign key (MAIN_ID) references TD_ZW_CHECK_CONCLUSION (RID),
                       constraint FK_TD_ZW_CHECK_ITEM_RST2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_ZW_CHECK_ITEM_RST3 foreign key (RST_ID) references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_ITEM_RST_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_ITEM_RST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_CLS_ITEM';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_CLS_ITEM
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       ITEM_ID              INTEGER,
                       IF_PASS              NUMBER(1),
                       CREATE_MANID         INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_CHECK_CLS_ITEM primary key (RID),
                       constraint FK_TD_ZW_CHECK_CLS_ITEM1 foreign key (MAIN_ID) references TD_ZW_CHECK_CONCLUSION (RID),
                       constraint FK_TD_ZW_CHECK_CLS_ITEM2 foreign key (ITEM_ID) references TS_SIMPLE_CODE (RID)
                    )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_CLS_ITEM_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_CLS_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_RPT';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_RPT
                        (
                           RID                  INTEGER              not null,
                           CRPT_ID              INTEGER,
                           CRPT_NAME            NVARCHAR2(50),
                           ZONE_ID              INTEGER,
                           CREDIT_CODE          NVARCHAR2(50),
                           ADDRESS              NVARCHAR2(100),
                           INDUS_TYPE_ID        INTEGER,
                           ECONOMY_ID           INTEGER,
                           CRPT_SIZE_ID         INTEGER,
                           LINK_MAN             NVARCHAR2(20),
                           LINK_PHONE           NVARCHAR2(20),
                           WORK_NAME            NVARCHAR2(500),
                           RPT_DATE             DATE,
                           RPT_NO               NVARCHAR2(50),
                           FILE_PATH            NVARCHAR2(100),
                           STATE                NUMBER(1),
                           UNIT_ID              INTEGER,
                           CREATE_MANID         INTEGER              not null,
                           CREATE_DATE          TIMESTAMP            not null,
                           MODIFY_DATE          TIMESTAMP,
                           MODIFY_MANID         INTEGER,
                           constraint PK_TD_ZW_CHECK_RPT primary key (RID),
                           constraint FK_TD_ZW_CHECK_RPT1 foreign key (CRPT_ID)
                             references TB_TJ_CRPT (RID),
                           constraint FK_TD_ZW_CHECK_RPT2 foreign key (ZONE_ID)
                             references TS_ZONE (RID),
                           constraint FK_TD_ZW_CHECK_RPT3 foreign key (INDUS_TYPE_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZW_CHECK_RPT4 foreign key (ECONOMY_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZW_CHECK_RPT5 foreign key (CRPT_SIZE_ID)
                             references TS_SIMPLE_CODE (RID),
                           constraint FK_TD_ZW_CHECK_RPT6 foreign key (UNIT_ID)
                             references TS_UNIT (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_RPT_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_RPT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('ORG_FZ');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD ORG_FZ NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('ORG_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD ORG_ADDR NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('JC_RPT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD JC_RPT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN5'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_ZK_CHECK_MAIN add constraint FK_TD_ZW_ZK_CHECK_MAIN5 foreign key (JC_RPT_ID) references TD_ZW_CHECK_RPT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_TABLE';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_TABLE
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               CHECK_TABLE_ID       INTEGER,
               CHECK_PSN            NVARCHAR2(100),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_CHECK_TABLE primary key (RID),
             constraint FK_TD_ZW_CHECK_TABLE1 foreign key (MAIN_ID)
                  references TD_ZW_CHECK_RPT (RID),
            constraint FK_TD_ZW_CHECK_TABLE2 foreign key (CHECK_TABLE_ID)
                  references TB_ZW_ZK_BADRSN_STAND (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_TABLE_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_TABLE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_ITEM';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_ITEM
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ITEM_ID              INTEGER,
               CHECK_VAL            NUMBER(5,2),
               SCORE_VAL            NUMBER(5,2),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_CHECK_ITEM primary key (RID),
            constraint FK_TD_ZW_CHECK_ITEM1 foreign key (MAIN_ID)
                  references TD_ZW_CHECK_TABLE (RID),
             constraint FK_TD_ZW_CHECK_ITEM2 foreign key (ITEM_ID)
                  references TS_SIMPLE_CODE (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_ITEM_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_SUB';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_SUB
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   SCORE_ID             INTEGER,
                   SCORE_VAL            NUMBER(5,2),
                   RMK                  NVARCHAR2(2000),
                   SCORE_RST_ID         INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_SUB primary key (RID),
                 constraint FK_TD_ZW_CHECK_SUB3 foreign key (SCORE_RST_ID)
                      references TS_SIMPLE_CODE (RID),
                constraint FK_TD_ZW_CHECK_SUB2 foreign key (SCORE_ID)
                      references TB_ZW_ZK_SCORES (RID),
                      constraint FK_TD_ZW_CHECK_SUB1 foreign key (MAIN_ID)
                      references TD_ZW_CHECK_ITEM (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_SUB_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TABLES
                  WHERE TABLE_NAME = 'TD_ZW_CHECK_DEDUCT';
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_DEDUCT
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   DEDUCT_ID            INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_DEDUCT primary key (RID),
                 constraint FK_TD_ZW_CHECK_DEDUCT1 foreign key (MAIN_ID)
                      references TD_ZW_CHECK_SUB (RID),
                 constraint FK_TD_ZW_CHECK_DEDUCT2 foreign key (DEDUCT_ID)
                      references TB_ZW_ZK_SCORE_DEDUCT (RID)
                        )';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  V1 NUMBER;
                BEGIN
                  SELECT COUNT(1)
                  INTO V1
                  FROM USER_SEQUENCES
                  WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_DEDUCT_SEQ';
                  IF V1 = 0 THEN
                    EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_DEDUCT_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999
                    START WITH 1 INCREMENT BY 1 CACHE 20';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_RAD_CHECK_RPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_RAD_CHECK_RPT
                (
                   RID                  INTEGER              not null,
                   UNIT_ID              INTEGER,
                   CRPT_NAME            NVARCHAR2(50),
                   ZONE_ID              INTEGER,
                   CREDIT_CODE          NVARCHAR2(50),
                   ADDRESS              NVARCHAR2(100),
                   LINK_MAN             NVARCHAR2(20),
                   LINK_PHONE           NVARCHAR2(20),
                   RPT_DATE             DATE,
                   RPT_NO               NVARCHAR2(50),
                   RPT_NAME             NVARCHAR2(100),
                   FILE_PATH            NVARCHAR2(100),
                   SOURCE_FILE_PATH     NVARCHAR2(100),
                   STATE                NUMBER(1),
                   CHECK_UNIT_ID        INTEGER,
                   CHECK_PSN_ID         INTEGER,
                   CHECK_DATE           DATE,
                   BACK_RAN             NVARCHAR2(100),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_RAD_CHECK_RPT primary key (RID),
                   constraint FK_TD_RAD_CHECK_RPT1 foreign key (UNIT_ID) references TS_UNIT (RID),
                   constraint FK_TD_RAD_CHECK_RPT2 foreign key (ZONE_ID) references TS_ZONE (RID),
                   constraint FK_TD_RAD_CHECK_RPT3 foreign key (CHECK_UNIT_ID) references TS_UNIT (RID),
                   constraint FK_TD_RAD_CHECK_RPT4 foreign key (CHECK_PSN_ID) references TS_USER_INFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_RAD_CHECK_RPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_RAD_CHECK_RPT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('RPT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD RPT_NAME NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('SOURCE_FILE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD SOURCE_FILE_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('BACK_RAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD BACK_RAN NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('CHECK_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD CHECK_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('CHECK_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD CHECK_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_CHECK_RPT7'
                AND TABLE_NAME = 'TD_ZW_CHECK_RPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_CHECK_RPT add constraint FK_TD_ZW_CHECK_RPT7 foreign key (CHECK_PSN_ID) references TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('USER_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD USER_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>79</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_CHECK_RPT8'
                AND TABLE_NAME = 'TD_ZW_CHECK_RPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_CHECK_RPT add constraint FK_TD_ZW_CHECK_RPT8 foreign key (USER_ID) references TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>80</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('PHONE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD PHONE_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>81</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_RECORD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_ZK_CHECK_RECORD
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               TYPE_ID              INTEGER,
               IF_HG                NUMBER(1),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_ZK_CHECK_RECORD primary key (RID),
            constraint FK_TD_ZW_ZK_CHECK_RECORD1 foreign key (MAIN_ID)
                  references TD_ZW_ZK_CHECK_MAIN (RID),
             constraint FK_TD_ZW_ZK_CHECK_RECORD2 foreign key (TYPE_ID)
                  references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>82</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_RECORD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_RECORD_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>83</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_LEADER');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_LEADER NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>84</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_RSN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>85</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('SCENE_CHECK_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD SCENE_CHECK_RSN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>86</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('RISK_RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD RISK_RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>87</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>88</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('IF_FINAL_RST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD IF_FINAL_RST NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>89</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM DBA_CONS_COLUMNS
              WHERE CONSTRAINT_NAME='FK_TD_ZW_ZK_CHECK_MAIN5'
              and TABLE_NAME='TD_ZW_ZK_CHECK_MAIN'
              and COLUMN_NAME='JC_RPT_ID';
              IF NUM > 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN DROP CONSTRAINT FK_TD_ZW_ZK_CHECK_MAIN5';
              END IF;
            END;
          ]]>
        </sql>
        <ver>90</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN5'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_ZK_CHECK_MAIN add constraint FK_TD_ZW_ZK_CHECK_MAIN5 foreign key (RISK_RST_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>91</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_TABLE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_TABLE_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>92</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_EXPERT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_ZK_CHECK_EXPERT
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   EXPERT_ID            INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_ZK_CHECK_EXPERT primary key (RID),
                   constraint FK_TD_ZW_ZK_CHECK_EXPERT1 foreign key (MAIN_ID)
                        references TD_ZW_ZK_CHECK_MAIN (RID),
                   constraint FK_TD_ZW_ZK_CHECK_EXPERT2 foreign key (EXPERT_ID)
                        references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>93</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_EXPERT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_ZK_CHECK_EXPERT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>94</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_EXPERT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_EXPERT
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   EXPERT_ID            INTEGER,
                   USER_NO              NVARCHAR2(50),
                   LOGIN_PWD            NVARCHAR2(50),
                   USER_ID              INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_CHECK_EXPERT primary key (RID),
                   constraint FK_TD_ZW_CHECK_EXPERT1 foreign key (MAIN_ID)
                    references TD_ZW_CHECK_RPT (RID),
                   constraint FK_TD_ZW_CHECK_EXPERT2 foreign key (EXPERT_ID)
                    references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZW_ZK_CHECK_EXPERT3 foreign key (USER_ID)
                    references TS_USER_INFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>95</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_EXPERT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZW_CHECK_EXPERT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>96</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('RISK_RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD RISK_RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>97</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM DBA_CONS_COLUMNS
              WHERE CONSTRAINT_NAME='FK_TD_ZW_ZK_CHECK_MAIN5'
              and TABLE_NAME='TD_ZW_ZK_CHECK_MAIN'
              and COLUMN_NAME='JC_RPT_ID';
              IF NUM > 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN DROP CONSTRAINT FK_TD_ZW_ZK_CHECK_MAIN5';
              END IF;
            END;
          ]]>
        </sql>
        <ver>98</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN5'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_ZK_CHECK_MAIN add constraint FK_TD_ZW_ZK_CHECK_MAIN5 foreign key (RISK_RST_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>99</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('IF_IMPROVE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD IF_IMPROVE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>100</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_SUB')
                AND COLUMN_NAME = UPPER('IF_IMPROVE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_SUB ADD IF_IMPROVE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>101</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_DEDUCT')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_DEDUCT ADD RMK NVARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>102</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_DEDUCT')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_DEDUCT ADD RMK NVARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>103</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_TABLE')
                AND COLUMN_NAME = UPPER('TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_TABLE ADD TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>104</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_TABLE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_TABLE_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>105</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('PHONE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD PHONE_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>106</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('ZPHONE_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD ZPHONE_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>107</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('IF_UPDATE_RISK_RST');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD IF_UPDATE_RISK_RST NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>108</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('FEED_BACK_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD FEED_BACK_PATH NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>109</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_BADRSN_STAND')
                AND COLUMN_NAME = UPPER('CHECK_XH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_BADRSN_STAND ADD CHECK_XH NVARCHAR2(10)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>110</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('DEDUCT_RSN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD DEDUCT_RSN NVARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>111</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('RPT_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD RPT_STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>112</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_RPT')
                AND COLUMN_NAME = UPPER('SITE_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RPT ADD SITE_STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>113</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_EXTRACT_BHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_BHK
                (
                   RID                  INTEGER              not null,
                   ZONE_ID              INTEGER,
                   ORG_ID               INTEGER,
                   BHK_CODE             NVARCHAR2(50),
                   EXTRACT_TYPE_ID      INTEGER,
                   PERSON_NAME          NVARCHAR2(100),
                   IDC                  NVARCHAR2(50),
                   TCHBADRSNTIM         NUMBER(9,2),
                   ONGUARD_STATEID      INTEGER,
                   WORK_TYPE_ID         INTEGER,
                   WORK_OTHER           NVARCHAR2(50),
                   IF_RHK               INTEGER,
                   BHK_DATE             DATE,
                   PRT_DATE             DATE,
                   CHEST_RST_ID         INTEGER,
                   HEAR_RST_ID          INTEGER,
                   BHK_RST_ID           INTEGER,
                   RE_BHK_RST_ID        INTEGER,
                   CRPT_ID              INTEGER,
                   ENTRUST_CRPT_ID      INTEGER,
                   EXTRACT_DATE         DATE,
                   EXTRACT_USER_ID      INTEGER,
                   STATE                NUMBER(2),
                   CHEST_NO             NVARCHAR2(50),
                   BHK_RPT_PATH         NVARCHAR2(100),
                   CHEST_PATH           NVARCHAR2(100),
                   BACK_RSN             NVARCHAR2(200),
                   EXPERT_CHEST_ID      INTEGER,
                   EXPERT_HEAR_RST_ID   INTEGER,
                   EXPERT_RST_ID        INTEGER,
                   CHEST_LEVEL_ID       INTEGER,
                   CHECK_UNIT_NAME      NVARCHAR2(100),
                   CHECK_PSN            NVARCHAR2(50),
                   CHECK_DATE           DATE,
                   CHECK_LINKTEL        NVARCHAR2(20),
                   CHECK_USER_ID        INTEGER,
                   PROV_CHEST_ID        INTEGER,
                   PROV_HEAR_RST_ID     INTEGER,
                   PROV_ADVICE          NUMBER(1),
                   PROV_OTHER_RMK       NVARCHAR2(100),
                   PROV_UNIT_NAME       NVARCHAR2(100),
                   PROV_CHECK_PSN       NVARCHAR2(50),
                   PROV_CHECK_DATE      DATE,
                   PROV_LINKTEL         NVARCHAR2(20),
                   PROV_USER_ID         INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_EXTRACT_BHK primary key (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK1 foreign key (ZONE_ID)
                        references TS_ZONE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK2 foreign key (ORG_ID)
                        references TB_TJ_SRVORG (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK3 foreign key (EXTRACT_TYPE_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK4 foreign key (ONGUARD_STATEID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK5 foreign key (WORK_TYPE_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK6 foreign key (CHEST_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK7 foreign key (HEAR_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK8 foreign key (BHK_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK9 foreign key (RE_BHK_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK10 foreign key (EXTRACT_USER_ID)
                        references TS_USER_INFO (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK11 foreign key (EXPERT_CHEST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK12 foreign key (EXPERT_HEAR_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK13 foreign key (EXPERT_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK14 foreign key (CHEST_LEVEL_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK15 foreign key (CHECK_USER_ID)
                        references TS_USER_INFO (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK16 foreign key (PROV_CHEST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK17 foreign key (PROV_HEAR_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK18 foreign key (PROV_USER_ID)
                        references TS_USER_INFO (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK19 foreign key (CRPT_ID)
                        references TB_TJ_CRPT (RID),
                   constraint FK_TD_ZK_EXTRACT_BHK20 foreign key (ENTRUST_CRPT_ID)
                        references TB_TJ_CRPT (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>114</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_EXTRACT_BHK_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_EXTRACT_BHK_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>115</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_BHK_TCH_BADRSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_BHK_TCH_BADRSNS
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       BADRSN_ID            INTEGER,
                       CREATE_MANID         INTEGER              not null,
                       CREATE_DATE          TIMESTAMP            not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZK_BHK_TCH_BADRSNS primary key (RID),
                       constraint FK_TD_ZK_BHK_TCH_BADRSNS1 foreign key (MAIN_ID)
                            references TD_ZK_EXTRACT_BHK (RID),
                       constraint FK_TD_ZK_BHK_TCH_BADRSNS2 foreign key (BADRSN_ID)
                            references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>116</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_BHK_TCH_BADRSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_BHK_TCH_BADRSNS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>117</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_BHK_BADRSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_BHK_BADRSNS
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   BADRSN_ID            INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_BHK_BADRSNS primary key (RID),
                   constraint FK_TD_ZK_BHK_BADRSNS1 foreign key (MAIN_ID)
                        references TD_ZK_EXTRACT_BHK (RID),
                   constraint FK_TD_ZK_BHK_BADRSNS2 foreign key (BADRSN_ID)
                        references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>118</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_BHK_BADRSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_BHK_BADRSNS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>119</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_BHK_EXPERT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_BHK_EXPERT
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ZONE_ID              INTEGER,
                   UNIT_NAME            NVARCHAR2(100),
                   USER_NAME            NVARCHAR2(50),
                   USER_ID              INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_BHK_EXPERT primary key (RID),
                   constraint FK_TD_ZK_BHK_EXPERT1 foreign key (MAIN_ID)
                        references TD_ZK_EXTRACT_BHK (RID),
                   constraint FK_TD_ZK_BHK_EXPERT2 foreign key (ZONE_ID)
                        references TS_ZONE (RID),
                   constraint FK_TD_ZK_BHK_EXPERT3 foreign key (USER_ID)
                        references TS_USER_INFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>120</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_BHK_EXPERT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_BHK_EXPERT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>121</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_EXTRACT_RULE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_RULE
                (
                   RID                  INTEGER              not null,
                   EXTRACT_TYPE_ID      INTEGER,
                   BHK_DATE_STR         DATE,
                   BHK_DATE_END         DATE,
                   RPT_DATE_STR         DATE,
                   RPT_DATE_END         DATE,
                   HEARING_RST_ID       INTEGER,
                   IF_WARN              NUMBER(1),
                   PRIORITY             INTEGER,
                   USER_ID              INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_EXTRACT_RULE primary key (RID),
                   constraint FK_TD_ZK_EXTRACT_RULE1 foreign key (EXTRACT_TYPE_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_RULE2 foreign key (HEARING_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                   constraint FK_TD_ZK_EXTRACT_RULE3 foreign key (USER_ID)
                        references TS_USER_INFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>122</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_EXTRACT_RULE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_EXTRACT_RULE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>123</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_ONGUARD_STATE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_ONGUARD_STATE
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ONGUARD_STATEID      INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_ONGUARD_STATE primary key (RID),
                   constraint FK_TD_ZK_ONGUARD_STATE1 foreign key (MAIN_ID)
                        references TD_ZK_EXTRACT_RULE (RID),
                   constraint FK_TD_ZK_ONGUARD_STATE2 foreign key (ONGUARD_STATEID)
                        references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>124</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_ONGUARD_STATE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_ONGUARD_STATE_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>125</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_BADRSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_BADRSNS
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   BADRSN_ID            INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_BADRSNS primary key (RID),
                   constraint FK_TD_ZK_BADRSNS1 foreign key (MAIN_ID)
                        references TD_ZK_EXTRACT_RULE (RID),
                   constraint FK_TD_ZK_BADRSNS2 foreign key (BADRSN_ID)
                        references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>126</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_BADRSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_BADRSNS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>127</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_BADRSNS_RST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_BADRSNS_RST
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   EXAM_CONCLUSION_ID   INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_BADRSNS_RST primary key (RID),
                   constraint FK_TD_ZK_BADRSNS_RST1 foreign key (MAIN_ID)
                        references TD_ZK_EXTRACT_RULE (RID),
                   constraint FK_TD_ZK_BADRSNS_RST2 foreign key (EXAM_CONCLUSION_ID)
                        references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>128</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_BADRSNS_RST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_BADRSNS_RST_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>129</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZK_CHEST_RESULT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZK_CHEST_RESULT
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   CHEST_RESULT         NUMBER(1),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_CHEST_RESULT primary key (RID),
                   constraint FK_TD_ZK_CHEST_RESULT1 foreign key (MAIN_ID)
                        references TD_ZK_EXTRACT_RULE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>130</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZK_CHEST_RESULT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '  CREATE SEQUENCE TD_ZK_CHEST_RESULT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1000       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>131</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TB_ZW_ZK_SCORE_OPTION';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TB_ZW_ZK_SCORE_OPTION (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              OPTION_ID INTEGER NOT NULL,
              CHECK_VAL NUMBER(5, 2),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TB_ZW_ZK_SCORE_OPTION PRIMARY KEY (RID),
              CONSTRAINT FK_TB_ZW_ZK_SCORE_OPTION1 FOREIGN KEY (MAIN_ID) REFERENCES TB_ZW_ZK_SCORES (RID),
              CONSTRAINT FK_TB_ZW_ZK_SCORE_OPTION2 FOREIGN KEY (OPTION_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>132</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TB_ZW_ZK_SCORE_OPTION';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>133</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>134</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_SUB4'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZW_ZK_CHECK_SUB add constraint FK_TD_ZW_ZK_CHECK_SUB4 foreign key (RST_ID) references TB_ZW_ZK_SCORE_OPTION (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>135</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZW_ZK_CHECK_RPT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZW_ZK_CHECK_RPT
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               JC_RPT_ID            INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_ZK_CHECK_RPT primary key (RID),
               constraint FK_TD_ZW_ZK_CHECK_RPT1 foreign key (MAIN_ID)
                  references TD_ZW_ZK_CHECK_MAIN (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>136</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZW_ZK_CHECK_RPT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>137</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('IF_JC_RPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD IF_JC_RPT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>138</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_BADRSN_STAND')
                AND COLUMN_NAME = UPPER('SPECIAL_FLAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_BADRSN_STAND ADD SPECIAL_FLAG NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>139</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_CHECK_SUB')
                AND COLUMN_NAME = UPPER('DEDUCT_SCORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_SUB ADD DEDUCT_SCORE NUMBER(5,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>140</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('DEDUCT_SCORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD DEDUCT_SCORE NUMBER(5,2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>141</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('ENTRUST_CRPT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD ENTRUST_CRPT_NAME NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>142</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('ENTRUST_CREDIT_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD ENTRUST_CREDIT_CODE NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>143</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('ENTRUST_ECONOMY_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD ENTRUST_ECONOMY_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>144</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZK_EXTRACT_BHK21'
                AND TABLE_NAME = 'TD_ZK_EXTRACT_BHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZK_EXTRACT_BHK add constraint FK_TD_ZK_EXTRACT_BHK21 foreign key (ENTRUST_ECONOMY_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>145</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('ENTRUST_INDUS_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD ENTRUST_INDUS_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>146</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZK_EXTRACT_BHK22'
                AND TABLE_NAME = 'TD_ZK_EXTRACT_BHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZK_EXTRACT_BHK add constraint FK_TD_ZK_EXTRACT_BHK22 foreign key (ENTRUST_INDUS_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>147</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('ENTRUST_CRPT_SIZE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD ENTRUST_CRPT_SIZE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>148</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZK_EXTRACT_BHK23'
                AND TABLE_NAME = 'TD_ZK_EXTRACT_BHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZK_EXTRACT_BHK add constraint FK_TD_ZK_EXTRACT_BHK23 foreign key (ENTRUST_CRPT_SIZE_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>149</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('EXTRACT_RPT_S_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD EXTRACT_RPT_S_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>150</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('EXTRACT_RPT_E_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD EXTRACT_RPT_E_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>151</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('ENTRUST_BHK_NUMS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD ENTRUST_BHK_NUMS INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>152</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('ORG_HEAR_RST_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD ORG_HEAR_RST_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>153</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZK_EXTRACT_BHK24'
                AND TABLE_NAME = 'TD_ZK_EXTRACT_BHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZK_EXTRACT_BHK add constraint FK_TD_ZK_EXTRACT_BHK24 foreign key (ORG_HEAR_RST_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>154</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('CHEST_SHOT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD CHEST_SHOT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>155</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZK_EXTRACT_BHK25'
                AND TABLE_NAME = 'TD_ZK_EXTRACT_BHK';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'alter table TD_ZK_EXTRACT_BHK add constraint FK_TD_ZK_EXTRACT_BHK25 foreign key (CHEST_SHOT_ID) references TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>156</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('IF_HAVE_BHK_RPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD IF_HAVE_BHK_RPT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>157</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('IF_HAVE_SUMRY_RPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD IF_HAVE_SUMRY_RPT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>158</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('SUMRY_RPT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD SUMRY_RPT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>159</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('SUMRY_RPT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD SUMRY_RPT_NO NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>160</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('RMK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD RMK NVARCHAR2(500)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>161</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_JOB';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_JOB
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               WORK_TYPE_ID      INTEGER,
               CREATE_MANID         INTEGER              not null,
               CREATE_DATE          TIMESTAMP            not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZK_EXTRACT_JOB primary key (RID),
                constraint FK_TD_ZK_EXTRACT_JOB1 foreign key (MAIN_ID)
                  references TD_ZK_EXTRACT_RULE (RID),
                constraint FK_TD_ZK_EXTRACT_JOB2 foreign key (WORK_TYPE_ID)
                  references TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>162</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_JOB';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>163</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_HEARING_EXCEPT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_HEARING_EXCEPT
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           EXCEPT_ID            INTEGER,
           CREATE_MANID         INTEGER              not null,
           CREATE_DATE          TIMESTAMP            not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_ZK_HEARING_EXCEPT primary key (RID),
            constraint FK_TD_ZK_HEARING_EXCEPT1 foreign key (MAIN_ID)
              references TD_ZK_EXTRACT_RULE (RID),
            constraint FK_TD_ZK_HEARING_EXCEPT2 foreign key (EXCEPT_ID)
              references TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>164</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_HEARING_EXCEPT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>165</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_ECONOMY';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_ECONOMY
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           ECONOMY_ID           INTEGER,
           CREATE_MANID         INTEGER              not null,
           CREATE_DATE          TIMESTAMP            not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_ZK_EXTRACT_ECONOMY primary key (RID),
        constraint FK_TD_ZK_EXTRACT_ECONOMY1 foreign key (MAIN_ID)
              references TD_ZK_EXTRACT_RULE (RID),
         constraint FK_TD_ZK_EXTRACT_ECONOMY2 foreign key (ECONOMY_ID)
              references TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>166</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_ECONOMY';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>167</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_INDUS';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_INDUS
        (
           RID                  INTEGER              not null,
           MAIN_ID              INTEGER,
           INDUS_ID             INTEGER,
           CREATE_MANID         INTEGER              not null,
           CREATE_DATE          TIMESTAMP            not null,
           MODIFY_DATE          TIMESTAMP,
           MODIFY_MANID         INTEGER,
           constraint PK_TD_ZK_EXTRACT_INDUS primary key (RID),
        constraint FK_TD_ZK_EXTRACT_INDUS1 foreign key (MAIN_ID)
              references TD_ZK_EXTRACT_RULE (RID),
         constraint FK_TD_ZK_EXTRACT_INDUS2 foreign key (INDUS_ID)
              references TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>168</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_INDUS';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>169</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_SIZE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_SIZE
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               CRPT_SIZE_ID         INTEGER,
               CREATE_MANID         INTEGER              not null,
               CREATE_DATE          TIMESTAMP            not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZK_EXTRACT_SIZE primary key (RID),
             constraint FK_TD_ZK_EXTRACT_SIZE1 foreign key (MAIN_ID)
                  references TD_ZK_EXTRACT_RULE (RID),
            constraint FK_TD_ZK_EXTRACT_SIZE2 foreign key (CRPT_SIZE_ID)
                  references TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>170</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_SIZE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>171</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_RULE')
                AND COLUMN_NAME = UPPER('WORK_YEAR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_RULE ADD WORK_YEAR INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>172</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_RULE')
                AND COLUMN_NAME = UPPER('AGE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_RULE ADD AGE INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>173</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_RULE')
                AND COLUMN_NAME = UPPER('BHK_NUMS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_RULE ADD BHK_NUMS INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>174</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_HEARING';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_HEARING
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   EXCEPT_ID            INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_EXTRACT_HEARING primary key (RID),
                   constraint FK_TD_ZK_EXTRACT_HEARING1 foreign key (MAIN_ID)
                        references TD_ZK_EXTRACT_BHK (RID),
                   constraint FK_TD_ZK_EXTRACT_HEARING2 foreign key (EXCEPT_ID)
                        references TS_SIMPLE_CODE (RID)
                )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>175</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_HEARING';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>176</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('TCHBADRSNMONTH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD TCHBADRSNMONTH INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>177</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_SUMRY_BHK';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_SUMRY_BHK
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               BHK_ID               INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZK_EXTRACT_SUMRY_BHK primary key (RID),
               constraint FK_TD_ZK_EXTRACT_SUMRY_BHK1 foreign key (MAIN_ID) references TD_ZK_EXTRACT_BHK (RID),
               constraint FK_TD_ZK_EXTRACT_SUMRY_BHK2 foreign key (BHK_ID) references TD_GJ_BHK (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>178</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_SUMRY_BHK';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>179</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_EXPERT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_EXTRACT_EXPERT
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       EXPERT_ID            INTEGER,
                       USER_NO              NVARCHAR2(50),
                       LOGIN_PWD            NVARCHAR2(50),
                       USER_ID              INTEGER,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZK_EXTRACT_EXPERT primary key (RID),
                       constraint FK_TD_ZK_EXTRACT_EXPERT1 foreign key (MAIN_ID)
                            references TD_ZK_EXTRACT_BHK (RID),
                       constraint FK_TD_ZK_EXTRACT_EXPERT2 foreign key (USER_ID)
                            references TS_USER_INFO (RID),
                       constraint FK_TD_ZK_EXTRACT_EXPERT3 foreign key (EXPERT_ID)
                            references TS_SIMPLE_CODE (RID)
                    )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>180</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_EXPERT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>181</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_TABLE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZK_EXTRACT_TABLE (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              CHECK_TABLE_ID INTEGER,
              CHECK_PSN NVARCHAR2 (100),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZK_EXTRACT_TABLE PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_TABLE1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZK_EXTRACT_BHK (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_TABLE2 FOREIGN KEY (CHECK_TABLE_ID) REFERENCES TB_ZW_ZK_BADRSN_STAND (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>182</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_TABLE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>183</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_ITEM';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZK_EXTRACT_ITEM (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              ITEM_ID INTEGER,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZK_EXTRACT_ITEM PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_ITEM1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZK_EXTRACT_TABLE (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_ITEM2 FOREIGN KEY (ITEM_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>184</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_ITEM';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>185</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_SUB';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZK_EXTRACT_SUB (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              SCORE_ID INTEGER,
              SCORE_RST_ID INTEGER,
              SCORE_VAL NUMBER(5, 2),
              RMK NVARCHAR2 (1000),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZK_EXTRACT_SUB PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_SUB1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZK_EXTRACT_ITEM (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_SUB3 FOREIGN KEY (SCORE_RST_ID) REFERENCES TB_ZW_ZK_SCORE_OPTION (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_SUB2 FOREIGN KEY (SCORE_ID) REFERENCES TB_ZW_ZK_SCORES (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>186</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_SUB';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>187</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_DEDUCT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZK_EXTRACT_DEDUCT (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              DEDUCT_ID INTEGER,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZK_EXTRACT_DEDUCT PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_DEDUCT1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZK_EXTRACT_SUB (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_DEDUCT2 FOREIGN KEY (DEDUCT_ID) REFERENCES TB_ZW_ZK_SCORE_DEDUCT (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>188</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_DEDUCT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>189</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_EXTRACT_SUMRY_PATH';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_ZK_EXTRACT_SUMRY_PATH (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              RPT_DATE DATE,
              RPT_NO NVARCHAR2 (50),
              RPT_PATH NVARCHAR2 (100),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_ZK_EXTRACT_SUMRY_PATH PRIMARY KEY (RID),
              CONSTRAINT FK_TD_ZK_EXTRACT_SUMRY_PATH1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZK_EXTRACT_BHK (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>190</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_EXTRACT_SUMRY_PATH';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>191</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_RST_RULE_SUB')
                AND COLUMN_NAME = UPPER('SCORE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_RST_RULE_SUB ADD SCORE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>192</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_RST_RULE_SUB5'
                  AND TABLE_NAME = 'TB_ZW_ZK_RST_RULE_SUB';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'alter table TB_ZW_ZK_RST_RULE_SUB add constraint FK_TB_ZW_ZK_RST_RULE_SUB5 foreign key (SCORE_ID) references TB_ZW_ZK_SCORES (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>193</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TB_ZW_ZK_RST_RULE_SUB')
                AND COLUMN_NAME = UPPER('DEDUCT_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_RST_RULE_SUB ADD DEDUCT_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>194</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_RST_RULE_SUB6'
                  AND TABLE_NAME = 'TB_ZW_ZK_RST_RULE_SUB';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'alter table TB_ZW_ZK_RST_RULE_SUB add constraint FK_TB_ZW_ZK_RST_RULE_SUB6 foreign key (DEDUCT_ID) references TB_ZW_ZK_SCORE_DEDUCT (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>195</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('IF_NOT_CHECK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD IF_NOT_CHECK NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>196</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_SRVORG_PLAN';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_SRVORG_PLAN
                (
                   RID                  INTEGER              not null,
                   PLAN_NAME            NVARCHAR2(50),
                   PLAN_DATE            DATE,
                   RMK                  NVARCHAR2(1000),
                   FILE_PATH            NVARCHAR2(200),
                   STATE                NUMBER(2),
                   PUBISH_UNIT_ID       INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_SRVORG_PLAN primary key (RID),
                   constraint FK_TD_ZK_SRVORG_PLAN1 foreign key (PUBISH_UNIT_ID)
                    references TS_UNIT (RID)
                )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>197</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_SRVORG_PLAN';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>198</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_SRVORG_LIST';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_SRVORG_LIST
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       ORG_ID               INTEGER,
                       STATE                NUMBER(2),
                       CHECK_STATE          NUMBER(2),
                       AUDIT_RST            NUMBER(1),
                       AUDIT_ADV            NVARCHAR2(200),
                       AUDIT_DATE           DATE,
                       AUDIT_PSN_ID         INTEGER,
                       INSPECT_DATE         DATE,
                       TOTAL_CHECK_VAL      NUMBER(5,2),
                       TOTAL_SCORE_VAL      NUMBER(5,2),
                       CHECK_RST_ID         INTEGER,
                       RISK_RST_ID          INTEGER,
                       WRITE_PATH           NVARCHAR2(100),
                       CHECK_TABLE_PATH     NVARCHAR2(100),
                       IMPROVE_FILE_ADDR    NVARCHAR2(100),
                       CORR_AUDIT_RST       NUMBER(1),
                       CORR_AUDIT_ADV       NVARCHAR2(200),
                       CORR_AUDIT_DATE      DATE,
                       CORR_AUDIT_PSN_ID    INTEGER,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZK_SRVORG_LIST primary key (RID),
                       constraint FK_TD_ZK_SRVORG_LIST1 foreign key (MAIN_ID)
                        references TD_ZK_SRVORG_PLAN (RID),
                       constraint FK_TD_ZK_SRVORG_LIST2 foreign key (ORG_ID)
                        references TD_ZW_SRVORGINFO (RID),
                       constraint FK_TD_ZK_SRVORG_LIST3 foreign key (AUDIT_PSN_ID)
                        references TS_USER_INFO (RID),
                       constraint FK_TD_ZK_SRVORG_LIST4 foreign key (CHECK_RST_ID)
                        references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_ZK_SRVORG_LIST5 foreign key (RISK_RST_ID)
                        references TS_SIMPLE_CODE (RID)
                    )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>199</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_SRVORG_LIST';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>200</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_SRVORG_RPT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_SRVORG_RPT
                    (
                       RID                  INTEGER              not null,
                       MAIN_ID              INTEGER,
                       UNIT_ID              INTEGER,
                       RPT_NO               NVARCHAR2(50),
                       RPT_NAME             NVARCHAR2(100),
                       RPT_DATE             DATE,
                       FILE_PATH            NVARCHAR2(100),
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZK_SRVORG_RPT primary key (RID),
                       constraint FK_TD_ZK_SRVORG_RPT1 foreign key (MAIN_ID)
                        references TD_ZK_SRVORG_LIST (RID),
                       constraint FK_TD_ZK_SRVORG_RPT2 foreign key (UNIT_ID)
                        references TB_TJ_RADHETH (RID)
                    )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>201</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_SRVORG_RPT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>202</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZW_EXPERT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZW_EXPERT
                (
                   RID                  INTEGER              not null,
                   UNIT_ID              INTEGER,
                   USER_NAME            NVARCHAR2(50),
                   IDC                  NVARCHAR2(50),
                   SEX                  NUMBER(1),
                   LINK_PHONE           NVARCHAR2(20),
                   TITLE_ID             INTEGER,
                   FILE_PATH            NVARCHAR2(100),
                   DATA_SOURCE          NUMBER(1),
                   DEL_MARK             NUMBER(1),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_EXPERT primary key (RID),
                   constraint FK_TD_ZW_EXPERT1 foreign key (UNIT_ID)
                     references TS_UNIT (RID),
                   constraint FK_TD_ZW_EXPERT2 foreign key (TITLE_ID)
                     references TS_SIMPLE_CODE (RID)
                )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>203</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZW_EXPERT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>204</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_FS_CHECK_EXPERT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_FS_CHECK_EXPERT
            (
               RID                  INTEGER              not null,
               YEAR                 INTEGER,
               EXPERT_ID            INTEGER,
               USER_ID              INTEGER,
               USER_NO              NVARCHAR2(50),
               LOGIN_PWD            NVARCHAR2(50),
               CREATE_MANID         INTEGER              not null,
               CREATE_DATE          TIMESTAMP            not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_FS_CHECK_EXPERT primary key (RID),
             constraint FK_TD_FS_CHECK_EXPERT1 foreign key (EXPERT_ID)
                  references TD_ZW_EXPERT (RID),
            constraint FK_TD_FS_CHECK_EXPERT2 foreign key (USER_ID)
                  references TS_USER_INFO (RID)
                )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>205</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_FS_CHECK_EXPERT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>206</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_FS_CHECK_EXPERT_TYPE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_FS_CHECK_EXPERT_TYPE
            (
             RID                  INTEGER              not null,
             MAIN_ID              INTEGER,
             EXPERT_ID            INTEGER,
             EXPERT_TYPE          NUMBER(1),
             STATE                NUMBER(2),
             CHECK_DATE           DATE,
             CHECK_PSN            NVARCHAR2(50),
             CREATE_MANID         INTEGER              not null,
             CREATE_DATE          TIMESTAMP            not null,
             MODIFY_DATE          TIMESTAMP,
             MODIFY_MANID         INTEGER,
               constraint PK_TD_FS_CHECK_EXPERT_TYPE primary key (RID),
            constraint FK_TD_FS_CHECK_EXPERT_TYPE1 foreign key (MAIN_ID)
                  references TD_ZK_SRVORG_LIST (RID),
            constraint FK_TD_FS_CHECK_EXPERT_TYPE2 foreign key (EXPERT_ID)
                  references TD_FS_CHECK_EXPERT (RID)
                )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>207</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_FS_CHECK_EXPERT_TYPE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>208</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_RULE')
                AND COLUMN_NAME = UPPER('BHK_NUM_MAX');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_RULE ADD BHK_NUM_MAX INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>209</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_ZK_TCH_BADRSNS';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'create table TD_ZK_TCH_BADRSNS
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   BADRSN_ID            INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZK_TCH_BADRSNS primary key (RID),
                   constraint FK_TD_ZK_TCH_BADRSNS1 foreign key (MAIN_ID) references TD_ZK_EXTRACT_RULE (RID),
                   constraint FK_TD_ZK_TCH_BADRSNS2 foreign key (BADRSN_ID) references TS_SIMPLE_CODE (RID)
                )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>210</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_ZK_TCH_BADRSNS';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>211</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
        DECLARE
          NUM INT;
        BEGIN
          SELECT COUNT(1)
          INTO NUM
          FROM USER_TAB_COLUMNS
          WHERE TABLE_NAME = UPPER('TB_ZW_ZK_BADRSN_STAND')
            AND COLUMN_NAME = UPPER('YEAR');
          IF NUM = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_BADRSN_STAND ADD YEAR INTEGER';
          END IF;
        END;
      ]]>
        </sql>
        <ver>212</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_FS_CHECK_TABLE';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_FS_CHECK_TABLE (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              EXPERT_ID INTEGER,
              TYPE NUMBER(1),
              CHECK_TABLE_ID INTEGER,
              CHECK_PSN NVARCHAR2 (100),
              STATE_MARK NUMBER(1),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_FS_CHECK_TABLE PRIMARY KEY (RID),
              CONSTRAINT FK_TD_FS_CHECK_TABLE1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZK_SRVORG_LIST (RID),
              CONSTRAINT FK_TD_FS_CHECK_TABLE2 FOREIGN KEY (CHECK_TABLE_ID) REFERENCES TB_ZW_ZK_BADRSN_STAND (RID),
              CONSTRAINT FK_TD_FS_CHECK_TABLE3 FOREIGN KEY (EXPERT_ID) REFERENCES TD_FS_CHECK_EXPERT_TYPE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>213</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_FS_CHECK_TABLE';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>214</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_FS_CHECK_ITEM';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_FS_CHECK_ITEM (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              ITEM_ID INTEGER,
              CHECK_VAL NUMBER(5, 2),
              SCORE_VAL NUMBER(5, 2),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_FS_CHECK_ITEM PRIMARY KEY (RID),
              CONSTRAINT FK_TD_FS_CHECK_ITEM1 FOREIGN KEY (MAIN_ID) REFERENCES TD_FS_CHECK_TABLE (RID),
              CONSTRAINT FK_TD_FS_CHECK_ITEM2 FOREIGN KEY (ITEM_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>215</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_FS_CHECK_ITEM';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>216</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_FS_CHECK_SUB';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_FS_CHECK_SUB (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              SCORE_ID INTEGER,
              SCORE_RST_ID INTEGER,
              SCORE_VAL NUMBER(5, 2),
              RMK NVARCHAR2 (2000),
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_FS_CHECK_SUB PRIMARY KEY (RID),
              CONSTRAINT FK_TD_FS_CHECK_SUB1 FOREIGN KEY (MAIN_ID) REFERENCES TD_FS_CHECK_ITEM (RID),
              CONSTRAINT FK_TD_FS_CHECK_SUB2 FOREIGN KEY (SCORE_ID) REFERENCES TB_ZW_ZK_SCORES (RID),
              CONSTRAINT FK_TD_FS_CHECK_SUB3 FOREIGN KEY (SCORE_RST_ID) REFERENCES TS_SIMPLE_CODE (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>217</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_FS_CHECK_SUB';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>218</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE NUM NUMBER;
            BEGIN
            SELECT
              COUNT(1) INTO NUM
            FROM
              USER_TABLES
            WHERE
              TABLE_NAME = 'TD_FS_CHECK_DEDUCT';
            IF NUM = 0 THEN EXECUTE IMMEDIATE 'CREATE TABLE TD_FS_CHECK_DEDUCT (
              RID INTEGER NOT NULL,
              MAIN_ID INTEGER,
              DEDUCT_ID INTEGER,
              CREATE_DATE TIMESTAMP NOT NULL,
              CREATE_MANID INTEGER NOT NULL,
              MODIFY_DATE TIMESTAMP,
              MODIFY_MANID INTEGER,
              CONSTRAINT PK_TD_FS_CHECK_DEDUCT PRIMARY KEY (RID),
              CONSTRAINT FK_TD_FS_CHECK_DEDUCT1 FOREIGN KEY (MAIN_ID) REFERENCES TD_FS_CHECK_SUB (RID),
              CONSTRAINT FK_TD_FS_CHECK_DEDUCT2 FOREIGN KEY (DEDUCT_ID) REFERENCES TB_ZW_ZK_SCORE_DEDUCT (RID)
            )';
            END IF;
            END;
          ]]>
        </sql>
        <ver>219</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
              V2 VARCHAR2(200);
              V3 VARCHAR2(200);
            BEGIN
              V2 := 'TD_FS_CHECK_DEDUCT';
              V3 := 'CREATE SEQUENCE ' || V2 || '_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20';
            SELECT COUNT(1) INTO V1 FROM USER_SEQUENCES
            WHERE
              SEQUENCE_NAME = V2 || '_SEQ';
            IF V1 = 0 THEN EXECUTE IMMEDIATE V3; END IF;
            END;
          ]]>
        </sql>
        <ver>220</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TB_ZW_ZK_SCORE_INDEX')
                    AND COLUMN_NAME = UPPER('FIRST_INDEX_ID');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TB_ZW_ZK_SCORE_INDEX ADD FIRST_INDEX_ID INTEGER';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>221</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
                NUM NUMBER;
            BEGIN
                SELECT COUNT(1)
                INTO NUM
                FROM USER_CONSTRAINTS
                WHERE CONSTRAINT_NAME = 'FK_TB_ZW_ZK_SCORE_ITEM3'
                  AND TABLE_NAME = 'TB_ZW_ZK_SCORE_INDEX';
                IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'alter table TB_ZW_ZK_SCORE_INDEX add constraint FK_TB_ZW_ZK_SCORE_ITEM3 foreign key (FIRST_INDEX_ID) references TS_SIMPLE_CODE (RID)';
                END IF;
            END;
            ]]>
        </sql>
        <ver>222</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_SUMRY_PATH')
                    AND COLUMN_NAME = UPPER('PHY_EXAM_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_SUMRY_PATH ADD PHY_EXAM_PATH NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>223</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_SUMRY_PATH')
                    AND COLUMN_NAME = UPPER('WP_RPT_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_SUMRY_PATH ADD WP_RPT_PATH NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>224</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM USER_TAB_COLUMNS
                  WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_SUMRY_PATH')
                    AND COLUMN_NAME = UPPER('WORKER_ROSTER_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_SUMRY_PATH ADD WORKER_ROSTER_PATH NVARCHAR2(100)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>225</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_NO_HG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    create table TD_ZW_ZK_CHECK_NO_HG
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   FLAG                 INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_ZK_CHECK_NO_HG primary key (RID),
               constraint FK_TD_ZW_ZK_CHECK_NO_HG1 foreign key (MAIN_ID)
                      references TD_ZW_ZK_CHECK_MAIN (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>226</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_NO_HG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_ZK_CHECK_NO_HG_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>227</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZK_CHECK_DETAILS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                   create table TD_ZW_ZK_CHECK_DETAILS
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               ITEM_ID              INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_ZK_CHECK_DETAILS primary key (RID),
            constraint FK_TD_ZW_ZK_CHECK_DETAILS1 foreign key (MAIN_ID)
                  references TD_ZW_ZK_CHECK_RECORD (RID),
            constraint FK_TD_ZW_ZK_CHECK_DETAILS2 foreign key (ITEM_ID)
                  references TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>228</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZK_CHECK_DETAILS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_ZK_CHECK_DETAILS_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>229</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('IF_DEVELOP');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD IF_DEVELOP INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>230</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('REVIEW_CONCLUSION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD REVIEW_CONCLUSION INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>231</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('IMPROVE_LINKMAN');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD IMPROVE_LINKMAN NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>232</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('IMPROVE_PHONE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD IMPROVE_PHONE NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>233</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('IMPROVE_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD IMPROVE_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>234</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_ORG_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_ORG_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>235</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN7'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CONSTRAINT FK_TD_ZW_ZK_CHECK_MAIN7 FOREIGN KEY (CHECK_ORG_ID)  REFERENCES TS_UNIT (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>236</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_OPINION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_OPINION NVARCHAR2(1000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>237</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_MAIN')
                AND COLUMN_NAME = UPPER('CHECK_PSN_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CHECK_PSN_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>238</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_MAIN8'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_MAIN ADD CONSTRAINT FK_TD_ZW_ZK_CHECK_MAIN8 FOREIGN KEY (CHECK_PSN_ID)  REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>239</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_PROVE')
                AND COLUMN_NAME = UPPER('ANNEX_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_PROVE ADD ANNEX_TYPE INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>240</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUB')
                AND COLUMN_NAME = UPPER('ASSESS_MARK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUB ADD ASSESS_MARK INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>241</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('DEL_MARK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD DEL_MARK INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>242</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('PERSON_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD PERSON_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>243</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZK_EXTRACT_BHK')
                AND COLUMN_NAME = UPPER('EXT_UUID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZK_EXTRACT_BHK ADD EXT_UUID NVARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>244</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_RECORD')
                AND COLUMN_NAME = UPPER('REVIEW_CONCLUSION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_RECORD ADD REVIEW_CONCLUSION INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>245</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TAB_COLUMNS
              WHERE TABLE_NAME = UPPER('TD_ZW_ZK_CHECK_SUMMARY')
                AND COLUMN_NAME = UPPER('TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUMMARY ADD TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>246</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ZK_CHECK_RECORD4'
                AND TABLE_NAME = 'TD_ZW_ZK_CHECK_SUMMARY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ZK_CHECK_SUMMARY ADD CONSTRAINT FK_TD_ZW_ZK_CHECK_RECORD4 FOREIGN KEY (TYPE_ID)  REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>247</ver>
    </sqlsentence>
</sqlsentences>
<!-- web-heth-zk-check-js 质控考核相关模块升级 -->