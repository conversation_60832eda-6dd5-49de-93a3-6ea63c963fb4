<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sqlsentences>
    <description>职业卫生平台-资质申报</description>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '    CREATE TABLE TD_ZW_TJORGINFO (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ORG_NAME             VARCHAR2(100)        NOT NULL,   ORG_ADDR             VARCHAR2(200)        NOT NULL,   ORG_FZ               VARCHAR2(50),   ORG_FZZW             VARCHAR2(50),   LINK_MAN             VARCHAR2(50),   LINK_MB              VARCHAR2(50),   LINK_TEL             VARCHAR2(50),   FAX                  VARCHAR2(50),   ZIPCODE              VARCHAR2(10),   EMAIL                VARCHAR2(50),   CERT_NO              VARCHAR2(50),   FIRST_GETDAY         DATE,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_TJORGINFO PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_TJORGINFO1 FOREIGN KEY (ORG_ID)      REFERENCES TS_UNIT (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>1</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_INSTINFO (   RID                  INTEGER              NOT NULL,   NUM                  NUMBER(4),   INST_NAME            VARCHAR2(100)        NOT NULL,   INST_MODEL           VARCHAR2(50),   CUST_NO              VARCHAR2(50),   PRDU_NO              VARCHAR2(50),   OUTTER               NUMBER(1),   ORG_ID               INTEGER,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_INSTINFO PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_INSTINFO1 FOREIGN KEY (ORG_ID)      REFERENCES TS_UNIT (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>2</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJINST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_TJINST (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   INST_ID              INTEGER              NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_TJINST PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_TJINST1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_TJORGINFO (RID),   CONSTRAINT FK_TD_ZW_TJINST2 FOREIGN KEY (INST_ID)      REFERENCES TD_ZW_INSTINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>3</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGGITEMS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_TJORGGITEMS (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ITEM_CODE            NUMBER(1)            NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_TJORGGITEMS PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_TJORGGITEMS1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_TJORGINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>4</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_TJORGANNEX (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ANNEX_TYPE           NUMBER(2)            NOT NULL,   FILE_NAME            VARCHAR2(100)        NOT NULL,   FILE_PATH            VARCHAR2(200)        NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_TJORGANNEX PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_TJORGANNEX1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_TJORGINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>5</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_PSNINFO (   RID                  INTEGER              NOT NULL,   EMP_NAME             VARCHAR2(100)        NOT NULL,   SEX                  VARCHAR2(10)         NOT NULL,   BIRTHDAY             VARCHAR2(20),   THETITLE             VARCHAR2(50),   POSITION             VARCHAR2(50),   PROF                 VARCHAR2(50),   ORG_ID               INTEGER,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_PSNINFO PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_PSNINFO1 FOREIGN KEY (ORG_ID)      REFERENCES TS_UNIT (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>6</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGPSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_TJORGPSNS (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   EMP_ID               INTEGER              NOT NULL,   CERT_NO              VARCHAR2(50),   CONSTRAINT PK_TD_ZW_TJORGPSNS PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_TJORGPSNS1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_TJORGINFO (RID),   CONSTRAINT FK_TD_ZW_TJORGPSNS2 FOREIGN KEY (EMP_ID)      REFERENCES TD_ZW_PSNINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>7</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PSNITEMS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_PSNITEMS (   RID                  INTEGER              NOT NULL,   EMP_ID               INTEGER              NOT NULL,   ITEM_CODE            NUMBER(1)            NOT NULL,   CONSTRAINT PK_TD_ZW_PSNITEMS PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_PSNITEMS1 FOREIGN KEY (EMP_ID)      REFERENCES TD_ZW_TJORGPSNS (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>8</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_WORKDEPT';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_WORKDEPT (   RID                  INTEGER              NOT NULL,   EMP_ID               INTEGER              NOT NULL,   DEPT_ID              INTEGER              NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_WORKDEPT PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_WORKDEPT1 FOREIGN KEY (EMP_ID)      REFERENCES TD_ZW_TJORGPSNS (RID),   CONSTRAINT FK_TD_ZW_WORKDEPT2 FOREIGN KEY (DEPT_ID)      REFERENCES TS_SIMPLE_CODE (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>9</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJITEMS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_TJITEMS (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ITEM_ID              INTEGER              NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_TJITEMS PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_TJITEMS1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_TJORGINFO (RID),   CONSTRAINT FK_TD_ZW_TJITEMS2 FOREIGN KEY (ITEM_ID)      REFERENCES TS_SIMPLE_CODE (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>10</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_DIAGORGINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '    CREATE TABLE TD_ZW_DIAGORGINFO (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ORG_NAME             VARCHAR2(100)        NOT NULL,   ORG_ADDR             VARCHAR2(200)        NOT NULL,   ORG_FZ               VARCHAR2(50),   ORG_FZZW             VARCHAR2(50),   LINK_MAN             VARCHAR2(50),   LINK_MB              VARCHAR2(50),   LINK_TEL             VARCHAR2(50),   FAX                  VARCHAR2(50),   ZIPCODE              VARCHAR2(10),   EMAIL                VARCHAR2(50),   CERT_NO              VARCHAR2(50),   FIRST_GETDAY         DATE,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_DIAGORGINFO PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_DIAGORGINFO1 FOREIGN KEY (ORG_ID)      REFERENCES TS_UNIT (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>11</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_DIAGINST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_DIAGINST (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   INST_ID              INTEGER              NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_DIAGINST PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_DIAGINST1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_DIAGORGINFO (RID),   CONSTRAINT FK_TD_ZW_DIAGINST2 FOREIGN KEY (INST_ID)      REFERENCES TD_ZW_INSTINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>12</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_DIAGITEMS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_DIAGITEMS (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ITEM_CODE            NUMBER(1)            NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_DIAGITEMS PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_DIAGITEMS1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_DIAGORGINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>13</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_DIAGANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_DIAGANNEX (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ANNEX_TYPE           NUMBER(2)            NOT NULL,   FILE_NAME            VARCHAR2(100)        NOT NULL,   FILE_PATH            VARCHAR2(200)        NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_DIAGANNEX PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_DIAGANNEX1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_DIAGORGINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>14</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_DIAGPSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_DIAGPSNS (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   EMP_ID               INTEGER              NOT NULL,   CERT_NO              VARCHAR2(50),   CONSTRAINT PK_TD_ZW_DIAGPSNS PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_DIAGPSNS1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_DIAGORGINFO (RID),   CONSTRAINT FK_TD_ZW_DIAGPSNS2 FOREIGN KEY (EMP_ID)      REFERENCES TD_ZW_PSNINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>15</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SRVORGINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '    CREATE TABLE TD_ZW_SRVORGINFO (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ORG_NAME             VARCHAR2(100)        NOT NULL,   ORG_ADDR             VARCHAR2(200)        NOT NULL,   ORG_FZ               VARCHAR2(50),   ORG_FZZW             VARCHAR2(50),   LINK_MAN             VARCHAR2(50),   LINK_MB              VARCHAR2(50),   LINK_TEL             VARCHAR2(50),   FAX                  VARCHAR2(50),   ZIPCODE              VARCHAR2(10),   EMAIL                VARCHAR2(50),   CERT_NO              VARCHAR2(50),   FIRST_GETDAY         DATE,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_SRVORGINFO PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_SRVORGINFO1 FOREIGN KEY (ORG_ID)      REFERENCES TS_UNIT (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>16</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ORGINST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_ORGINST (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   INST_ID              INTEGER              NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_ORGINST PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_OR_REFERENCE_TD_ZW_IN FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_INSTINFO (RID),   CONSTRAINT FK_TD_ZW_OR_REFERENCE_TD_ZW_SR FOREIGN KEY (INST_ID)      REFERENCES TD_ZW_SRVORGINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>17</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SRVORGITEMS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SRVORGITEMS (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ITEM_CODE            NUMBER(1)            NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_SRVORGITEMS PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_SRVORGITEMS1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_SRVORGINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>18</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ORGANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_ORGANNEX (   RID                  INTEGER              NOT NULL,   ORG_ID               INTEGER              NOT NULL,   ANNEX_TYPE           NUMBER(2)            NOT NULL,   FILE_NAME            VARCHAR2(100)        NOT NULL,   FILE_PATH            VARCHAR2(200)        NOT NULL,   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_ORGANNEX PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_ORGANNEX1 FOREIGN KEY (ORG_ID)      REFERENCES TD_ZW_SRVORGINFO (RID))    ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>19</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_TJORGINFO_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>20</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_INSTINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_INSTINFO_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>21</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGGITEMS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_TJORGGITEMS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>22</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_TJORGANNEX_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>23</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PSNINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_PSNINFO_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>24</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGPSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_TJORGPSNS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>25</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PSNITEMS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_PSNITEMS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>26</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_WORKDEPT_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_WORKDEPT_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>27</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJITEMS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_TJITEMS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>28</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_DIAGORGINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_DIAGORGINFO_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>29</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_DIAGINST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_DIAGINST_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>30</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_DIAGITEMS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_DIAGITEMS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>31</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_DIAGANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_DIAGANNEX_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>32</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_DIAGPSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_DIAGPSNS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>33</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SRVORGINFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_SRVORGINFO_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>34</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ORGINST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_ORGINST_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>35</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SRVORGITEMS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_SRVORGITEMS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>36</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ORGANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_ORGANNEX_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>37</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SRVORGPSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_SRVORGPSNS_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>38</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJINST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE '       CREATE SEQUENCE TD_ZW_TJINST_SEQ       MINVALUE 0       MAXVALUE 9999999999999999999999999       START WITH 1       INCREMENT BY 1       CACHE 20      ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>39</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SRVORGPSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' CREATE TABLE TD_ZW_SRVORGPSNS ( RID                  INTEGER              NOT NULL, ORG_ID               INTEGER              NOT NULL, EMP_ID               INTEGER              NOT NULL, CERT_NO              VARCHAR2(50), CONSTRAINT PK_TD_ZW_SRVORGPSNS PRIMARY KEY (RID), CONSTRAINT FK_TD_ZW_SRVORGPSNS1 FOREIGN KEY (ORG_ID) REFERENCES TD_ZW_SRVORGINFO (RID), CONSTRAINT FK_TD_ZW_SRVORGPSNS2 FOREIGN KEY (EMP_ID) REFERENCES TD_ZW_PSNINFO (RID) ) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>40</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>41</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>42</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                AND COLUMN_NAME = UPPER('STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO ADD STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>43</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('ORG_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO MODIFY ORG_NAME NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>44</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('ORG_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO MODIFY ORG_ADDR NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>45</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('ORG_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO MODIFY ORG_NAME NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>46</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('ORG_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO MODIFY ORG_ADDR NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>47</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('ORG_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO MODIFY ORG_NAME NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>48</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('ORG_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO MODIFY ORG_ADDR NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>49</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>50</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_KIND_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_KIND_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>51</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_KIND_DETAL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_KIND_DETAL_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>52</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_FACTORY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_FACTORY VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>53</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_PRO_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_PRO_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>54</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('WHERE_STORE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD WHERE_STORE VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>55</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>56</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('ACPT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD ACPT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>57</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('ACPT_CONCLU');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD ACPT_CONCLU VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>58</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_INSTINFO2'
                AND TABLE_NAME = 'TD_ZW_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO   ADD CONSTRAINT FK_TD_ZW_INSTINFO2 FOREIGN KEY (INST_KIND_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>59</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_INSTINFO3'
                AND TABLE_NAME = 'TD_ZW_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO   ADD CONSTRAINT FK_TD_ZW_INSTINFO3 FOREIGN KEY (INST_KIND_DETAL_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>60</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGPSNS')
                AND COLUMN_NAME = UPPER('JL_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGPSNS ADD JL_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>61</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGPSNS')
                AND COLUMN_NAME = UPPER('LJ_JL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGPSNS ADD LJ_JL VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>62</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_INST_JCMAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_INST_JCMAIN (   RID                  INTEGER              NOT NULL,   INST_ID              INTEGER              NOT NULL,   JC_DATE              DATE,   JC_RST               VARCHAR2(200),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_INST_JCMAIN PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_INST_JCMAIN1 FOREIGN KEY (INST_ID) REFERENCES TD_ZW_INSTINFO(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>63</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_INST_JCSUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_INST_JCSUB (   RID                  INTEGER              NOT NULL,   MAIN_ID              INTEGER              NOT NULL,   DESCR                VARCHAR2(100),   RST                  VARCHAR2(50),   RST_UNIT             VARCHAR2(20),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_INST_JCSUB PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_INST_JCSUB1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZW_INST_JCMAIN(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>64</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_INST_SBJCMAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_INST_SBJCMAIN (   RID                  INTEGER              NOT NULL,   INST_ID              INTEGER              NOT NULL,   JC_DATE              DATE,   JC_RST               VARCHAR2(200),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_INST_SBJCMAIN PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_INST_SBJCMAIN1 FOREIGN KEY (INST_ID) REFERENCES TD_ZW_INSTINFO(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>65</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_INST_SBJCSUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_INST_SBJCSUB (   RID                  INTEGER              NOT NULL,   MAIN_ID              INTEGER              NOT NULL,   DESCR                VARCHAR2(100),   RST                  VARCHAR2(50),   RST_UNIT             VARCHAR2(20),   CREATE_DATE          TIMESTAMP            NOT NULL,   CREATE_MANID         INTEGER              NOT NULL,   CONSTRAINT PK_TD_ZW_INST_SBJCSUB PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_INST_SBJCSUB1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZW_INST_SBJCMAIN(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>66</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PSNTJ_MAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_PSNTJ_MAIN (   RID                  INTEGER              NOT NULL,   EMP_ID               INTEGER              NOT NULL,   TJ_DATE              DATE                 NOT NULL,   TJ_CONCL             VARCHAR2(200),   TJ_TYPE              NUMBER(1)            NOT NULL,   CONSTRAINT PK_TD_ZW_PSNTJ_MAIN PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_PSNTJ_MAIN1 FOREIGN KEY (EMP_ID) REFERENCES TD_ZW_PSNINFO(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>67</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PSNTJ_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_PSNTJ_SUB (   RID                  INTEGER              NOT NULL,   MAIN_ID              INTEGER              NOT NULL,   DESCR                VARCHAR2(100),   RST                  VARCHAR2(50),   RST_UNIT             VARCHAR2(20),   CONSTRAINT PK_TD_ZW_PSNTJ_SUB PRIMARY KEY (RID),   CONSTRAINT FK_TD_ZW_PSNTJ_SUB1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZW_PSNTJ_MAIN(RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>68</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_INST_JCMAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_INST_JCMAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>69</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_INST_JCSUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_INST_JCSUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>70</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_INST_SBJCMAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_INST_SBJCMAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>71</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_INST_SBJCSUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_INST_SBJCSUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>72</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PSNTJ_MAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_PSNTJ_MAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>73</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PSNTJ_SUB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_PSNTJ_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 3000 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>74</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGPSNS')
                AND COLUMN_NAME = UPPER('CERT_NO')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGPSNS MODIFY CERT_NO VARCHAR2(100) NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>75</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGPSNS')
                AND COLUMN_NAME = UPPER('CERT_NO')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGPSNS MODIFY CERT_NO VARCHAR2(100) NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>76</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGPSNS')
                AND COLUMN_NAME = UPPER('CERT_NO')
                AND NULLABLE = 'N';
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGPSNS MODIFY CERT_NO VARCHAR2(100) NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>77</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGGITEMS')
                AND COLUMN_NAME = UPPER('ITEM_CODE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGGITEMS MODIFY ITEM_CODE NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>78</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGITEMS')
                AND COLUMN_NAME = UPPER('ITEM_CODE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGITEMS MODIFY ITEM_CODE NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>79</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGITEMS')
                AND COLUMN_NAME = UPPER('ITEM_CODE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGITEMS MODIFY ITEM_CODE NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>80</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNITEMS')
                AND COLUMN_NAME = UPPER('ITEM_CODE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNITEMS MODIFY ITEM_CODE NUMBER(4)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>81</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND COLUMN_NAME = UPPER('CANCEL_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD CANCEL_STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>82</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND COLUMN_NAME = UPPER('CANCEL_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD CANCEL_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>83</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND COLUMN_NAME = UPPER('VALID_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD VALID_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>84</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('IDC_CARD');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD IDC_CARD VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>85</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('NATIONALITY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD NATIONALITY VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>86</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('NATION');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD NATION INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>87</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_PSNINFO'
                AND TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO   ADD CONSTRAINT FK_TD_ZW_PSNINFO FOREIGN KEY (NATION)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>88</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('LINK_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD LINK_ADDR VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>89</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TEL_PHONE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD TEL_PHONE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>90</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('MOBILE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD MOBILE_NO VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>91</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('POSTCODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD POSTCODE VARCHAR2(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>92</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('EDUCATION_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD EDUCATION_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>93</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_PSNINFO3'
                AND TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO   ADD CONSTRAINT FK_TD_ZW_PSNINFO3 FOREIGN KEY (EDUCATION_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>94</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('GRAD_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD GRAD_NO VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>95</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('CERT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD CERT_NO VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>96</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_CERT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_CERT_NO VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>97</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_CERT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_CERT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>98</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_LEVEL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_LEVEL_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>99</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_PSNINFO4'
                AND TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO   ADD CONSTRAINT FK_TD_ZW_PSNINFO4 FOREIGN KEY (DOCT_LEVEL_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>100</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>101</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_PSNINFO5'
                AND TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO   ADD CONSTRAINT FK_TD_ZW_PSNINFO5 FOREIGN KEY (DOCT_TYPE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>102</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_SPEC_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_SPEC_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>103</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_PSNINFO6'
                AND TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO   ADD CONSTRAINT FK_TD_ZW_PSNINFO6 FOREIGN KEY (DOCT_SPEC_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>104</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_PRA_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_PRA_NO VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>105</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_PRA_PLACE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_PRA_PLACE VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>106</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_PRA_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_PRA_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>107</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TITLE_CERT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD TITLE_CERT_NO VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>108</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TECH_TITLE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD TECH_TITLE VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>109</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('SPECIAL_TAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD SPECIAL_TAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>110</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('LAST_ACPT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD LAST_ACPT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>111</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('LAST_ACPT_CIRCLE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD LAST_ACPT_CIRCLE INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>112</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJITEMS')
                AND COLUMN_NAME = UPPER('IS_OUTTER');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJITEMS ADD IS_OUTTER NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>113</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND COLUMN_NAME = UPPER('CANCEL_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO ADD CANCEL_STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>114</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND COLUMN_NAME = UPPER('CANCEL_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO ADD CANCEL_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>115</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND COLUMN_NAME = UPPER('VALID_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO ADD VALID_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>116</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                AND COLUMN_NAME = UPPER('CANCEL_STATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO ADD CANCEL_STATE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>117</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                AND COLUMN_NAME = UPPER('CANCEL_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO ADD CANCEL_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>118</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                AND COLUMN_NAME = UPPER('VALID_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO ADD VALID_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>119</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PSN_ZZLB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_PSN_ZZLB  (    RID                  INTEGER              not null,    PSN_ID               INTEGER              not null,    QUAL_ID              INTEGER              not null,    CENT_NO              VARCHAR2(50)         not null,    SEND_DATE            DATE                 not null,    VALID_DATE           DATE                 not null,    CREATE_DATE          TIMESTAMP            not null,    CREATE_MANID         INTEGER              not null,    MODIFY_DATE          TIMESTAMP,    MODIFY_MANID         INTEGER,    constraint PK_TD_ZW_PSN_ZZLB primary key (RID),    constraint FK_TD_ZW_PS_REFERENCE_TD_ZW_PS foreign key (PSN_ID) references TD_ZW_PSNINFO (RID),    constraint FK_TD_ZW_PSN_ZZLB1 foreign key (QUAL_ID) references TS_SIMPLE_CODE (RID) )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>120</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ZZLB_RANGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE ' create table TD_ZW_ZZLB_RANGE  (    RID                  INTEGER              not null,    MAIN_ID              INTEGER              not null,    ZZ_RANGE_ID          INTEGER              not null,    CREATE_DATE          TIMESTAMP            not null,    CREATE_MANID         INTEGER              not null,    MODIFY_DATE          TIMESTAMP,    MODIFY_MANID         INTEGER,    constraint PK_TD_ZW_ZZLB_RANGE primary key (RID),    constraint FK_TD_ZW_ZZ_REFERENCE_TD_ZW_PS foreign key (MAIN_ID) references TD_ZW_PSN_ZZLB (RID),    constraint FK_TD_ZW_ZZLB_RANGE1 foreign key (ZZ_RANGE_ID) references TS_SIMPLE_CODE (RID) )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>121</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PSN_ZZLB_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_PSN_ZZLB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20 ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>122</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ZZLB_RANGE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_ZZLB_RANGE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 CACHE 20 ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>123</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TITLE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD TITLE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>124</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_PSNINFO8'
                AND TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO   ADD CONSTRAINT FK_TD_ZW_PSNINFO8 FOREIGN KEY (TITLE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>125</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('PSN_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD PSN_TYPE INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>126</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND COLUMN_NAME = UPPER('AGEIN_SEND_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB ADD AGEIN_SEND_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>127</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND COLUMN_NAME = UPPER('AGEIN_VALID_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB ADD AGEIN_VALID_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>128</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND NULLABLE = 'N'
                AND COLUMN_NAME = UPPER('VALID_DATE');
              IF NUM = 1 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB MODIFY VALID_DATE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>129</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ORGANNEX')
                AND COLUMN_NAME = UPPER('REG_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ORGANNEX ADD REG_ZONE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>130</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ORGANNEX')
                AND COLUMN_NAME = UPPER('UNIT_LEVEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ORGANNEX ADD UNIT_LEVEL INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>131</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_ORGANNEX2'
                AND TABLE_NAME = 'TD_ZW_ORGANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ORGANNEX   ADD CONSTRAINT FK_TD_ZW_ORGANNEX2 FOREIGN KEY (REG_ZONE_ID)      REFERENCES TS_ZONE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>132</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGANNEX')
                AND COLUMN_NAME = UPPER('REG_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGANNEX ADD REG_ZONE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>133</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGANNEX')
                AND COLUMN_NAME = UPPER('UNIT_LEVEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGANNEX ADD UNIT_LEVEL INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>134</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGANNEX')
                AND COLUMN_NAME = UPPER('REG_ZONE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGANNEX ADD REG_ZONE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>135</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGANNEX')
                AND COLUMN_NAME = UPPER('UNIT_LEVEL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGANNEX ADD UNIT_LEVEL INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>136</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGANNEX')
                AND COLUMN_NAME = UPPER('UNIT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGANNEX ADD UNIT_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>137</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGANNEX')
                AND COLUMN_NAME = UPPER('UNIT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGANNEX ADD UNIT_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>138</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_ORGANNEX')
                AND COLUMN_NAME = UPPER('UNIT_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_ORGANNEX ADD UNIT_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>139</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('EMAIL');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD EMAIL VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>140</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('ON_DUTY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD ON_DUTY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>141</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('BE_JOB');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD BE_JOB VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>142</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('BE_ZY_WORK_YEARS');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD BE_ZY_WORK_YEARS NUMBER(2)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>143</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_OTHER_PLACE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_OTHER_PLACE VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>144</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('IDC_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD IDC_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>145</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('IDC_ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD IDC_ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>146</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_QUAL_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_QUAL_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>147</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('QUAL_ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD QUAL_ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>148</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DOCT_PRA_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DOCT_PRA_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>149</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('PRA_ANNXE_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD PRA_ANNXE_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>150</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('DEGREE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD DEGREE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>151</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('EMP_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO MODIFY EMP_NAME NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>152</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('SEX');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO MODIFY SEX NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>153</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND COLUMN_NAME = UPPER('ANNEX_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB ADD ANNEX_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>154</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND COLUMN_NAME = UPPER('ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB ADD ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>155</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('QUAL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB MODIFY QUAL_ID NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>156</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('CENT_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB MODIFY CENT_NO NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>157</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND nullable = UPPER('Y')
                AND COLUMN_NAME = UPPER('SEND_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB MODIFY SEND_DATE NULL';
              END IF;
            END;
          ]]>
        </sql>
        <ver>158</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('RECEIVE_EMP_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD RECEIVE_EMP_NAME VARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>159</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('RECEIVE_LINK_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD RECEIVE_LINK_ADDR VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>160</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('RECEIVE_MOBILE_NO');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD RECEIVE_MOBILE_NO VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>161</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('RECEIVE_POSTCODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD RECEIVE_POSTCODE VARCHAR2(6)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>162</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('GRAD_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD GRAD_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>163</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('HOLD_CENT_ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD HOLD_CENT_ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>164</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('HOLD_CENT_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD HOLD_CENT_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>165</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('GRAD_ANNXE_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD GRAD_ANNXE_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>166</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('EMPLOY_CENT_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD EMPLOY_CENT_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>167</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('EMPLOY_CENT_ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD EMPLOY_CENT_ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>168</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TITLE_LEVEL_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD TITLE_LEVEL_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>169</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_PSNINFO7'
                AND TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD CONSTRAINT FK_TD_ZW_PSNINFO7 FOREIGN KEY (TITLE_LEVEL_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>170</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TITLE_CENT_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD TITLE_CENT_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>171</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TITLE_CENT_ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD TITLE_CENT_ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>172</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('IF_CHECK_EXPERT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD IF_CHECK_EXPERT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>173</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGPSNS')
                AND COLUMN_NAME = UPPER('ON_DUTY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGPSNS ADD ON_DUTY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>174</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGPSNS')
                AND COLUMN_NAME = UPPER('ON_DUTY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGPSNS ADD ON_DUTY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>175</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGPSNS')
                AND COLUMN_NAME = UPPER('ON_DUTY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGPSNS ADD ON_DUTY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>176</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND COLUMN_NAME = UPPER('CREDIT_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO ADD CREDIT_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>177</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('SOCIAL_ANNEX_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD SOCIAL_ANNEX_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>178</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('SOCIAL_ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD SOCIAL_ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>179</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('MAJOR_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD MAJOR_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>180</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_PSNINFO9'
                AND TABLE_NAME = 'TD_ZW_PSNINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO   ADD CONSTRAINT FK_TD_ZW_PSNINFO9 FOREIGN KEY (MAJOR_ID)      REFERENCES TS_SIMPLE_CODE (RID) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>181</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND COLUMN_NAME = UPPER('CREDIT_CODE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD  CREDIT_CODE VARCHAR2(50)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>182</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND COLUMN_NAME = UPPER('UPDATETAG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO ADD UPDATETAG NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>183</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                AND COLUMN_NAME = UPPER('ERROR_MSG');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO ADD ERROR_MSG VARCHAR2(2000)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>184</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_PSN_TYPE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_PSN_TYPE ( RID                  INTEGER              not null,MAIN_ID              INTEGER              not null, PSN_TYPE             INTEGER              not null,CREATE_DATE          TIMESTAMP            not null, CREATE_MANID         INTEGER              not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER, constraint PK_TD_ZW_PSN_TYPE primary key (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>185</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_PSN_TYPE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_PSN_TYPE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>186</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TRAIN_QUAL_ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO  ADD TRAIN_QUAL_ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>187</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('TRAIN_QUAL_ANNEX_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD TRAIN_QUAL_ANNEX_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>188</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('FS_QUAL_ANNEX_NAME');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD FS_QUAL_ANNEX_NAME VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>189</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('FS_QUAL_ANNEX_PATH');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD FS_QUAL_ANNEX_PATH VARCHAR2(200)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>190</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('ADD_SOURCE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD ADD_SOURCE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>191</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_ZZLB')
                AND COLUMN_NAME = UPPER('ZZ_FETCH_WAY');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_ZZLB ADD ZZ_FETCH_WAY NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>192</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_OCCHETH_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_OCCHETH_INFO (   RID                  INTEGER              not null,   ORG_ID               INTEGER              not null,   ORG_NAME             VARCHAR2(100)        not null,   ORG_ADDR             VARCHAR2(200)        not null,   ORG_FZ               VARCHAR2(50),   ORG_FZZW             VARCHAR2(50),   LINK_MAN             VARCHAR2(50),   LINK_MB              VARCHAR2(50),   LINK_TEL             VARCHAR2(50),   FAX                  VARCHAR2(50),   ZIPCODE              VARCHAR2(10),   EMAIL                VARCHAR2(50),   CERT_NO              VARCHAR2(50),   FIRST_GETDAY         DATE,   VALID_DATE           DATE,   STATE                NUMBER(1),   CANCEL_STATE         NUMBER(1),   CANCEL_DATE          DATE,   LEVEL_ID             INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_ZW_OCCHETH_INFO primary key (RID),constraint FK_TD_ZW_OCCHETH_INFO1 foreign key (ORG_ID)      references TS_UNIT (RID),constraint FK_TD_ZW_OCCHETH_INFO2 foreign key (LEVEL_ID)      references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>193</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_OCCHETH_INFO_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_OCCHETH_INFO_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>194</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_OCCHETH_ITEMS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_OCCHETH_ITEMS (   RID                  INTEGER              not null,   ORG_ID               INTEGER              not null,   ITEM_CODE            NUMBER(4)            not null,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_ZW_OCCHETH_ITEMS primary key (RID),constraint FK_TD_ZW_OCCHETH_ITEMS1 foreign key (ORG_ID)      references TD_ZW_OCCHETH_INFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>195</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_OCCHETH_ITEMS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_OCCHETH_ITEMS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>196</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_OCCHETH_INST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_OCCHETH_INST (   RID                  INTEGER              not null,   ORG_ID               INTEGER              not null,   INST_ID              INTEGER              not null,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_ZW_OCCHETH_INST primary key (RID),constraint FK_TD_ZW_OCCHETH_INST1 foreign key (ORG_ID)      references TD_ZW_OCCHETH_INFO (RID),constraint FK_TD_ZW_OCCHETH_INST2 foreign key (INST_ID)      references TD_ZW_INSTINFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>197</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_OCCHETH_INST_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_OCCHETH_INST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>198</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_OCCHETH_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_OCCHETH_PSNS (   RID                  INTEGER              not null,   ORG_ID               INTEGER              not null,   EMP_ID               INTEGER              not null,   CERT_NO              VARCHAR2(50),   ON_DUTY              NUMBER(1),   constraint PK_TD_ZW_OCCHETH_PSNS primary key (RID),constraint FK_TD_ZW_OCCHETH_PSNS1 foreign key (ORG_ID)      references TD_ZW_OCCHETH_INFO (RID),constraint FK_TD_ZW_OCCHETH_PSNS2 foreign key (EMP_ID)      references TD_ZW_PSNINFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>199</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_OCCHETH_PSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_OCCHETH_PSNS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>200</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_OCCHETH_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_OCCHETH_ANNEX (   RID                  INTEGER              not null,   ORG_ID               INTEGER              not null,   ANNEX_TYPE           NUMBER(2)            not null,   FILE_NAME            VARCHAR2(100)        not null,   FILE_PATH            VARCHAR2(200)        not null,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   constraint PK_TD_ZW_OCCHETH_ANNEX primary key (RID),constraint FK_TD_ZW_OCCHETH_ANNEX1 foreign key (ORG_ID)      references TD_ZW_OCCHETH_INFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>201</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_OCCHETH_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_OCCHETH_ANNEX_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>202</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                AND COLUMN_NAME = UPPER('BIRTH_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD BIRTH_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>203</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_OUT_RANGE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE
              'create table TD_ZW_TJORG_OUT_RANGE
				(
				   RID                  INTEGER              not null,
				   ORG_ID               INTEGER              not null,
				   ZONE_ID              INTEGER              not null,
				   CREATE_DATE          TIMESTAMP            not null,
				   CREATE_MANID         INTEGER              not null,
				   MODIFY_DATE          TIMESTAMP,
				   MODIFY_MANID         INTEGER,
				   constraint PK_TD_ZW_TJORG_OUT_RANGE primary key (RID),
				   constraint FK_TD_ZW_TJORG_OUT_RANGE1 foreign key (ORG_ID)
      				references TD_ZW_TJORGINFO (RID),
      			   constraint FK_TD_ZW_TJORG_OUT_RANGE2 foreign key (ZONE_ID)
      			    references TS_ZONE (RID)
				)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>204</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_OUT_RANGE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_OUT_RANGE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>205</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                    AND COLUMN_NAME = UPPER('OUT_WORK_POWER');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD OUT_WORK_POWER NUMBER(1)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>206</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_RECORD';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZW_TJORG_RECORD (
	                RID                  INTEGER              not null,
	                MAIN_ID              INTEGER,
	                ZONE_ID          INTEGER,
	                RCD_NO               VARCHAR2(50),
	                CERT_DATE            DATE,
	                STATE_MARK           NUMBER(1),
	                LOGOUT_DATE          DATE,
	                CREATE_DATE          TIMESTAMP            not null,
	                CREATE_MANID         INTEGER              not null,
	                MODIFY_DATE          TIMESTAMP,
	                MODIFY_MANID         INTEGER,
	                constraint PK_TD_ZW_TJORG_RECORD primary key (RID),
	                constraint FK_TD_ZW_TJORG_RECORD1 foreign key (MAIN_ID)
	                	references TD_ZW_TJORGINFO (RID),
	                constraint FK_TD_ZW_TJORG_RECORD2 foreign key (ZONE_ID)
	                	references TS_ZONE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>207</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_RECORD_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_RECORD_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>208</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_RCD_ITEM';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                create table TD_ZW_TJORG_RCD_ITEM (
	                RID                  INTEGER              not null,
	                MAIN_ID              INTEGER,
	                ITEM_ID          	 INTEGER,
	                CREATE_DATE          TIMESTAMP            not null,
	                CREATE_MANID         INTEGER              not null,
	                MODIFY_DATE          TIMESTAMP,
	                MODIFY_MANID         INTEGER,
	                constraint PK_TD_ZW_TJORG_RCD_ITEM primary key (RID),
	                constraint FK_TD_ZW_TJORG_RCD_ITEM1 foreign key (MAIN_ID)
	                	references TD_ZW_TJORG_RECORD (RID),
	                constraint FK_TD_ZW_TJORG_RCD_ITEM2 foreign key (ITEM_ID)
	                	references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>209</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_RCD_ITEM_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_RCD_ITEM_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>210</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_PSN_TYPE')
                    AND COLUMN_NAME = UPPER('OTHER_PSN_TYPE');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_TYPE ADD OTHER_PSN_TYPE VARCHAR2(50)';
                  END IF;
                END;
              ]]>
        </sql>
        <ver>211</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_QUAL_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE '
                    CREATE TABLE TD_ZW_TJORG_QUAL_ANNEX (
                        RID INTEGER NOT NULL,
                        MAIN_ID INTEGER,
                        RULE_ID INTEGER,
                        OTHER_RULE_NAME VARCHAR2 (50),
                        IF_HAS NUMBER (1),
                        ANNEX_PATH VARCHAR2 (200),
                        CREATE_DATE TIMESTAMP NOT NULL,
                        CREATE_MANID INTEGER NOT NULL,
                        MODIFY_DATE TIMESTAMP,
                        MODIFY_MANID INTEGER,
                        CONSTRAINT PK_TD_ZW_TJORG_QUAL_ANNEX PRIMARY KEY (RID),
                        CONSTRAINT FK_TD_ZW_TJORG_QUAL_ANNEX1 FOREIGN KEY (MAIN_ID) REFERENCES TD_ZW_TJORGINFO (RID),
                        CONSTRAINT FK_TD_ZW_TJORG_QUAL_ANNEX2 FOREIGN KEY (RULE_ID) REFERENCES TS_SIMPLE_CODE (RID)
                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>212</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_QUAL_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_QUAL_ANNEX_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>213</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('IF_NEED_ACPT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD IF_NEED_ACPT NUMBER(1) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>214</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('IF_SUPT_OUT_CHK');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD IF_SUPT_OUT_CHK NUMBER(1) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>215</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND COLUMN_NAME = UPPER('LAST_SMT_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD LAST_SMT_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>216</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_OCCHETH_INFO')
                AND COLUMN_NAME = UPPER('UNIT_TYPE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO ADD UNIT_TYPE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>217</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_OCCHETH_INFO3'
                AND TABLE_NAME = 'TD_ZW_OCCHETH_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO ADD CONSTRAINT FK_TD_ZW_OCCHETH_INFO3 FOREIGN KEY (UNIT_TYPE_ID)
                	REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>218</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_OCCHETH_INFO')
                AND COLUMN_NAME = UPPER('LAB_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO ADD LAB_ADDR NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>219</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_OCCHETH_INFO')
                AND COLUMN_NAME = UPPER('COMMUNI_ADDR');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO ADD COMMUNI_ADDR NVARCHAR2(100)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>220</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_JSFFJG_COMM_ANNEX';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_JSFFJG_COMM_ANNEX (  RID                  INTEGER              not null,ORG_ID               INTEGER              not null,ANNEX_TYPE           NUMBER(2)            not null,FILE_NAME            VARCHAR2(200)        not null,FILE_PATH            VARCHAR2(200)        not null,REG_ZONE_ID          INTEGER,UNIT_LEVEL           INTEGER,UNIT_NAME            VARCHAR2(200),CREATE_MANID         INTEGER              not null,CREATE_DATE          TIMESTAMP            not null, constraint PK_TD_ZW_JSFFJG_COMM_ANNEX primary key (RID),constraint FK_TD_ZW_JSFFJG_COMM_ANNEX1 foreign key (ORG_ID) references TS_UNIT (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>221</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_JSFFJG_COMM_ANNEX_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_JSFFJG_COMM_ANNEX_SEQ  MINVALUE 0  MAXVALUE 9999999999999999999999999  START WITH 1  INCREMENT BY 1  CACHE 20';
              END IF;
            END;
          ]]>
        </sql>
        <ver>222</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('ACPT_REQ_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD ACPT_REQ_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>223</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_INSTINFO4'
                AND TABLE_NAME = 'TD_ZW_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO   ADD CONSTRAINT FK_TD_ZW_INSTINFO4 FOREIGN KEY (ACPT_REQ_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>224</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('INST_STATE_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD INST_STATE_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>225</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_INSTINFO5'
                AND TABLE_NAME = 'TD_ZW_INSTINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO   ADD CONSTRAINT FK_TD_ZW_INSTINFO5 FOREIGN KEY (INST_STATE_ID)      REFERENCES TS_SIMPLE_CODE (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>226</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                AND COLUMN_NAME = UPPER('PURCHASE_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD PURCHASE_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>227</ver>
    </sqlsentence>

    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_PSN_TYPE')
                AND COLUMN_NAME = UPPER('ZZJG_TYPE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSN_TYPE ADD ZZJG_TYPE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>228</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SRVORGITEMS_SUBS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_SRVORGITEMS_SUBS (   RID                  INTEGER              not null,   MAIN_ID              INTEGER,   SUB_ITEM_ID          INTEGER,   CREATE_DATE          TIMESTAMP            not null,   CREATE_MANID         INTEGER              not null,   MODIFY_DATE          TIMESTAMP,   MODIFY_MANID         INTEGER,   constraint PK_TD_ZW_SRVORGITEMS_SUBS primary key (RID),    constraint FK_TD_ZW_SRVORGITEMS_SUBS1 foreign key (MAIN_ID)     references TD_ZW_SRVORGITEMS (RID),   constraint FK_TD_ZW_SRVORGITEMS_SUBS2 foreign key (SUB_ITEM_ID)     references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>229</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SRVORGITEMS_SUBS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SRVORGITEMS_SUBS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>230</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_OUTCAR';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORG_OUTCAR (RID                  INTEGER              not null,MAIN_ID              INTEGER,CAR_NO               NVARCHAR2(10),CAR_BAND             NVARCHAR2(20),CAR_STYLE            NVARCHAR2(30),CAR_FRAME_NO         NVARCHAR2(30),PRODUCTION_DATE      DATE,STATE_MARK           NUMBER(1),CREATE_MANID         INTEGER              not null,CREATE_DATE          TIMESTAMP            not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_ZW_TJORG_OUTCAR primary key (RID),constraint FK_TD_ZW_TJORG_OUTCAR1 foreign key (MAIN_ID) references TD_ZW_TJORGINFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>231</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_OUTCAR_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_OUTCAR_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>232</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_OUTC_INST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORG_OUTC_INST (RID                  INTEGER              not null,CAR_ID               INTEGER,INST_ID              INTEGER,CREATE_MANID         INTEGER              not null,CREATE_DATE          TIMESTAMP            not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_ZW_TJORG_OUTC_INST primary key (RID),constraint FK_TD_ZW_TJORG_OUTC_INST1 foreign key (CAR_ID) references TD_ZW_TJORG_OUTCAR (RID),constraint FK_TD_ZW_TJORG_OUTC_INST2 foreign key (INST_ID) references TD_ZW_INSTINFO (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>233</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_OUTC_INST_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_OUTC_INST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>234</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_ITM_DETAIL';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORG_ITM_DETAIL(RID                  INTEGER              not null,MAIN_ID              INTEGER,ITEM_DETAIL_ID       INTEGER,CREATE_MANID         INTEGER              not null,CREATE_DATE          TIMESTAMP            not null,MODIFY_DATE          TIMESTAMP,MODIFY_MANID         INTEGER,constraint PK_TD_ZW_TJORG_ITM_DETAIL primary key (RID),constraint FK_TD_ZW_TJORG_ITM_DETAIL1 foreign key (MAIN_ID) references TD_ZW_TJORGGITEMS (RID),constraint FK_TD_ZW_TJORG_ITM_DETAIL2 foreign key (ITEM_DETAIL_ID) references TS_SIMPLE_CODE (RID))';
              END IF;
            END;
          ]]>
        </sql>
        <ver>235</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_ITM_DETAIL_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_ITM_DETAIL_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>236</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGINFO_APPLY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORGINFO_APPLY
                    (
                       RID                  INTEGER              not null,
                       ORG_ID               INTEGER,
                       ZONE_ID              INTEGER,
                       ORG_NAME             NVARCHAR2(50),
                       QUAL_TYPE_ID         INTEGER,
                       APPLY_TYPE_ID        INTEGER,
                       APPLY_DATE           DATE,
                       STATE                NUMBER(1),
                       FST_CHK_DATE         DATE,
                       FST_CHK_ORG_ID       INTEGER,
                       FST_CHK_PSN_ID       INTEGER,
                       FST_BACK_RSN         NVARCHAR2(200),
                       FINAL_CHK_DATE       DATE,
                       FINAL_CHK_ORG_ID     INTEGER,
                       FINAL_CHK_PSN_ID     INTEGER,
                       FINAL_BACK_RSN       NVARCHAR2(200),
                       ORG_ADDR             NVARCHAR2(100),
                       ORG_FZ               NVARCHAR2(25),
                       ORG_FZZW             NVARCHAR2(25),
                       LINK_MAN             NVARCHAR2(25),
                       LINK_MB              NVARCHAR2(25),
                       LINK_TEL             NVARCHAR2(25),
                       FAX                  NVARCHAR2(25),
                       ZIPCODE              NVARCHAR2(10),
                       EMAIL                NVARCHAR2(25),
                       OUT_WORK_POWER       NUMBER(1),
                       CERT_NO              NVARCHAR2(25),
                       FIRST_GETDAY         DATE,
                       FILING_DATE          DATE,
                       CREATE_DATE          TIMESTAMP            not null,
                       CREATE_MANID         INTEGER              not null,
                       MODIFY_DATE          TIMESTAMP,
                       MODIFY_MANID         INTEGER,
                       constraint PK_TD_ZW_TJORGINFO_APPLY primary key (RID),
                       constraint FK_TD_ZW_TJORGINFO_APPLY1 foreign key (ZONE_ID)
                        references TS_ZONE (RID),
                       constraint FK_TD_ZW_TJORGINFO_APPLY2 foreign key (ORG_ID)
                        references TS_UNIT (RID),
                       constraint FK_TD_ZW_TJORGINFO_APPLY3 foreign key (QUAL_TYPE_ID)
                        references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_ZW_TJORGINFO_APPLY4 foreign key (APPLY_TYPE_ID)
                        references TS_SIMPLE_CODE (RID),
                       constraint FK_TD_ZW_TJORGINFO_APPLY5 foreign key (FST_CHK_ORG_ID)
                        references TS_UNIT (RID),
                       constraint FK_TD_ZW_TJORGINFO_APPLY6 foreign key (FST_CHK_PSN_ID)
                        references TS_USER_INFO (RID),
                       constraint FK_TD_ZW_TJORGINFO_APPLY7 foreign key (FINAL_CHK_ORG_ID)
                        references TS_UNIT (RID),
                       constraint FK_TD_ZW_TJORGINFO_APPLY8 foreign key (FINAL_CHK_PSN_ID)
                        references TS_USER_INFO (RID)

                    )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>237</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGINFO_APPLY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORGINFO_APPLY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>238</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGGITEMS_APPLY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORGGITEMS_APPLY
                (
                   RID                  INTEGER              not null,
                   ORG_ID               INTEGER,
                   ITEM_ID              INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_ZW_TJORGGITEMS_APPLY primary key (RID),
                   constraint FK_TD_ZW_TJORGGITEMS_APPLY1 foreign key (ORG_ID)
                    references TD_ZW_TJORGINFO_APPLY (RID),
                   constraint FK_TD_ZW_TJORGGITEMS_APPLY2 foreign key (ITEM_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>239</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGGITEMS_APPLY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORGGITEMS_APPLY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>240</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_ITM_DETAIL_APY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORG_ITM_DETAIL_APY
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   ITEM_DETAIL_ID       INTEGER,
                   IF_EXTER_INSPECT     NUMBER(1),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_TJORG_ITM_DETAIL_APY primary key (RID),
                   constraint FK_TD_ZW_TJORG_ITM_DETAIL_APY1 foreign key (MAIN_ID)
                    references TD_ZW_TJORGGITEMS_APPLY (RID),
                   constraint FK_TD_ZW_TJORG_ITM_DETAIL_APY2 foreign key (ITEM_DETAIL_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>241</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_ITM_DETAIL_APY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_ITM_DETAIL_APY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>242</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_OUTCAR_APPLY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORG_OUTCAR_APPLY
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   CAR_NO               NVARCHAR2(10),
                   CAR_BAND             NVARCHAR2(20),
                   CAR_STYLE            NVARCHAR2(30),
                   CAR_FRAME_NO         NVARCHAR2(30),
                   PRODUCTION_DATE      DATE,
                   STATE_MARK           NUMBER(1),
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_TJORG_OUTCAR_APPLY primary key (RID),
                   constraint FK_TD_ZW_TJORG_OUTCAR_APPLY1 foreign key (MAIN_ID)
                    references TD_ZW_TJORGINFO_APPLY (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>243</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_OUTCAR_APPLY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_OUTCAR_APPLY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>244</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_OUTC_INST_APY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORG_OUTC_INST_APY
                (
                   RID                  INTEGER              not null,
                   CAR_ID               INTEGER,
                   INST_ID              INTEGER,
                   CREATE_MANID         INTEGER              not null,
                   CREATE_DATE          TIMESTAMP            not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_TJORG_OUTC_INST_APY primary key (RID),
                   constraint FK_TD_ZW_TJORG_OUTC_INST_APY1 foreign key (CAR_ID)
                    references TD_ZW_TJORG_OUTCAR_APPLY (RID),
                   constraint FK_TD_ZW_TJORG_OUTC_INST_APY2 foreign key (INST_ID)
                    references TD_ZW_INSTINFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>245</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_OUTC_INST_APY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_OUTC_INST_APY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>246</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGANNEX_APPLY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORGANNEX_APPLY
                (
                   RID                  INTEGER              not null,
                   ORG_ID               INTEGER,
                   ANNEX_TYPE           NUMBER(2),
                   FILE_NAME            NVARCHAR2(100),
                   FILE_PATH            NVARCHAR2(200),
                   REG_ZONE_ID          INTEGER,
                   UNIT_LEVEL           INTEGER,
                   UNIT_NAME            NVARCHAR2(100),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_ZW_TJORGANNEX_APPLY primary key (RID),
                   constraint FK_TD_ZW_TJORGANNEX_APPLY1 foreign key (ORG_ID)
                    references TD_ZW_TJORGINFO_APPLY (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>247</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGANNEX_APPLY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORGANNEX_APPLY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>248</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORG_QUAL_ANNEX_APY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORG_QUAL_ANNEX_APY
                (
                   RID                  INTEGER              not null,
                   MAIN_ID              INTEGER,
                   RULE_ID              INTEGER,
                   OTHER_RULE_NAME      NVARCHAR2(25),
                   IF_HAS               NUMBER(1),
                   ANNEX_PATH           NVARCHAR2(200),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   MODIFY_DATE          TIMESTAMP,
                   MODIFY_MANID         INTEGER,
                   constraint PK_TD_ZW_TJORG_QUAL_ANNEX_APY primary key (RID),
                   constraint FK_TD_ZW_TJORG_QUAL_ANNEX_APY1 foreign key (MAIN_ID)
                    references TD_ZW_TJORGINFO_APPLY (RID),
                   constraint FK_TD_ZW_TJORG_QUAL_ANNEX_APY2 foreign key (RULE_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>249</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORG_QUAL_ANNEX_APY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORG_QUAL_ANNEX_APY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>250</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJITEMS_APPLY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJITEMS_APPLY
                (
                   RID                  INTEGER              not null,
                   ORG_ID               INTEGER,
                   ITEM_ID              INTEGER,
                   IS_OUTTER            NUMBER(1),
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_ZW_TJITEMS_APPLY primary key (RID),
                   constraint FK_TD_ZW_TJITEMS_APPLY1 foreign key (ORG_ID)
                    references TD_ZW_TJORGINFO_APPLY (RID),
                   constraint FK_TD_ZW_TJITEMS_APPLY2 foreign key (ITEM_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>251</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJITEMS_APPLY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJITEMS_APPLY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>252</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJINST_APPLY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJINST_APPLY
                (
                   RID                  INTEGER              not null,
                   ORG_ID               INTEGER,
                   INST_ID              INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_ZW_TJINST_APPLY primary key (RID),
                   constraint FK_TD_ZW_TJINST_APPLY1 foreign key (ORG_ID)
                    references TD_ZW_TJORGINFO_APPLY (RID),
                   constraint FK_TD_ZW_TJINST_APPLY2 foreign key (INST_ID)
                     references TD_ZW_INSTINFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>253</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJINST_APPLY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJINST_APPLY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>254</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_TJORGPSNS_APPLY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_TJORGPSNS_APPLY
                (
                   RID                  INTEGER              not null,
                   ORG_ID               INTEGER,
                   EMP_ID               INTEGER,
                   constraint PK_TD_ZW_TJORGPSNS_APPLY primary key (RID),
                   constraint FK_TD_ZW_TJORGPSNS_APPLY1 foreign key (ORG_ID)
                    references TD_ZW_TJORGINFO_APPLY (RID),
                   constraint FK_TD_ZW_TJORGPSNS_APPLY2 foreign key (EMP_ID)
                    references TD_ZW_PSNINFO (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>255</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_TJORGPSNS_APPLY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_TJORGPSNS_APPLY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>256</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_WORKDEPT_APPLY';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_WORKDEPT_APPLY
                (
                   RID                  INTEGER              not null,
                   EMP_ID               INTEGER,
                   DEPT_ID              INTEGER,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_ZW_WORKDEPT_APPLY primary key (RID),
                   constraint FK_TD_ZW_WORKDEPT_APPLY1 foreign key (EMP_ID)
                    references TD_ZW_TJORGPSNS_APPLY (RID),
                   constraint FK_TD_ZW_WORKDEPT_APPLY2 foreign key (DEPT_ID)
                    references TS_SIMPLE_CODE (RID)
                )';
              END IF;
            END;
          ]]>
        </sql>
        <ver>257</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_WORKDEPT_APPLY_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_WORKDEPT_APPLY_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>258</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
           DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND COLUMN_NAME = UPPER('FILING_DATE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD FILING_DATE DATE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>259</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
           DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_ITM_DETAIL')
                AND COLUMN_NAME = UPPER('IF_EXTER_INSPECT');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_ITM_DETAIL ADD IF_EXTER_INSPECT NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>260</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
           DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_JSFFJG_COMM_ANNEX')
                AND COLUMN_NAME = UPPER('ADD_SOURCE');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_JSFFJG_COMM_ANNEX ADD ADD_SOURCE NUMBER(1)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>261</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
           DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                AND COLUMN_NAME = UPPER('RCD_ORG_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO ADD RCD_ORG_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>262</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
           DECLARE
              NUM INT;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM COLS
              WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                AND COLUMN_NAME = UPPER('RCD_ORG_ID');
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO ADD RCD_ORG_ID INTEGER';
              END IF;
            END;
          ]]>
        </sql>
        <ver>263</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_OUTCAR')
                    AND COLUMN_NAME = UPPER('DRIVING_ANNEX_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_OUTCAR ADD DRIVING_ANNEX_PATH NVARCHAR2(200)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>264</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_OUTCAR')
                    AND COLUMN_NAME = UPPER('CAR_PHOTO_ANNEX_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_OUTCAR ADD CAR_PHOTO_ANNEX_PATH NVARCHAR2(200)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>265</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_OUTCAR')
                    AND COLUMN_NAME = UPPER('CAR_DR_ANNEX_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_OUTCAR ADD CAR_DR_ANNEX_PATH NVARCHAR2(200)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>266</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_OUTCAR_APPLY')
                    AND COLUMN_NAME = UPPER('DRIVING_ANNEX_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_OUTCAR_APPLY ADD DRIVING_ANNEX_PATH NVARCHAR2(200)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>267</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_OUTCAR_APPLY')
                    AND COLUMN_NAME = UPPER('CAR_PHOTO_ANNEX_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_OUTCAR_APPLY ADD CAR_PHOTO_ANNEX_PATH NVARCHAR2(200)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>268</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_OUTCAR_APPLY')
                    AND COLUMN_NAME = UPPER('CAR_DR_ANNEX_PATH');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_OUTCAR_APPLY ADD CAR_DR_ANNEX_PATH NVARCHAR2(200)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>269</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                    AND COLUMN_NAME = UPPER('BE_FS_WORK_YEARS');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO ADD BE_FS_WORK_YEARS NUMBER(2)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>270</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                  NUM INT;
                BEGIN
                  SELECT COUNT(1)
                  INTO NUM
                  FROM COLS
                  WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                    AND COLUMN_NAME = UPPER('CHECK_ANNEX_NAME');
                  IF NUM = 0 THEN
                    EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO ADD CHECK_ANNEX_NAME VARCHAR2(200)';
                  END IF;
                END;
            ]]>
        </sql>
        <ver>271</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_INSTINFO')
                      AND COLUMN_NAME = UPPER('LAST_ACPT_CIRCLE_DOT');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_INSTINFO add LAST_ACPT_CIRCLE_DOT number(5,1)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>272</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_CHECK_RST';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'create table TD_ZW_CHECK_RST
                (
                    RID                  INTEGER              not null,
                    CHECK_TYPE_ID        INTEGER,
                    UNIT_ID              INTEGER,
                    EXPERT_LEADERS       NVARCHAR2(200),
                    EXPERT_MEMBERS       NVARCHAR2(200),
                    PROBLEMS             NVARCHAR2(1000),
                    CHECK_RST_ID         INTEGER,
                    CHECK_DATE           DATE,
                    ZG_RST_ID            INTEGER,
                    NOTICE_FILE_PATH     NVARCHAR2(100),
                    ZG_FILE_PATH         NVARCHAR2(100),
                    CHECK_UNIT_ID        INTEGER,
                    STATE                NUMBER(1),
                    DEL_MARK             NUMBER(1),
                    CREATE_MANID         INTEGER              not null,
                    CREATE_DATE          TIMESTAMP            not null,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    constraint PK_TD_ZW_CHECK_RST primary key (RID)
                )';
              END IF;
            END;
            ]]>
        </sql>
        <ver>273</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_CHECK_RST_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_CHECK_RST_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>274</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
        DECLARE
          NUM INT;
        BEGIN
          SELECT COUNT(1)
          INTO NUM
          FROM USER_CONSTRAINTS
          WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_CHECK_RST1')
            AND TABLE_NAME = UPPER('TD_ZW_CHECK_RST');
          IF NUM = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RST ADD CONSTRAINT FK_TD_ZW_CHECK_RST1 FOREIGN KEY (CHECK_TYPE_ID) REFERENCES TS_SIMPLE_CODE (RID)';
          END IF;
        END;
      ]]>
        </sql>
        <ver>275</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
        DECLARE
          NUM INT;
        BEGIN
          SELECT COUNT(1)
          INTO NUM
          FROM USER_CONSTRAINTS
          WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_CHECK_RST2')
            AND TABLE_NAME = UPPER('TD_ZW_CHECK_RST');
          IF NUM = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RST ADD CONSTRAINT FK_TD_ZW_CHECK_RST2 FOREIGN KEY (UNIT_ID) REFERENCES TS_UNIT (RID)';
          END IF;
        END;
      ]]>
        </sql>
        <ver>276</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
        DECLARE
          NUM INT;
        BEGIN
          SELECT COUNT(1)
          INTO NUM
          FROM USER_CONSTRAINTS
          WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_CHECK_RST3')
            AND TABLE_NAME = UPPER('TD_ZW_CHECK_RST');
          IF NUM = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RST ADD CONSTRAINT FK_TD_ZW_CHECK_RST3 FOREIGN KEY (CHECK_RST_ID) REFERENCES TS_SIMPLE_CODE (RID)';
          END IF;
        END;
      ]]>
        </sql>
        <ver>277</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
        DECLARE
          NUM INT;
        BEGIN
          SELECT COUNT(1)
          INTO NUM
          FROM USER_CONSTRAINTS
          WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_CHECK_RST4')
            AND TABLE_NAME = UPPER('TD_ZW_CHECK_RST');
          IF NUM = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RST ADD CONSTRAINT FK_TD_ZW_CHECK_RST4 FOREIGN KEY (ZG_RST_ID) REFERENCES TS_SIMPLE_CODE (RID)';
          END IF;
        END;
      ]]>
        </sql>
        <ver>278</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
        DECLARE
          NUM INT;
        BEGIN
          SELECT COUNT(1)
          INTO NUM
          FROM USER_CONSTRAINTS
          WHERE CONSTRAINT_NAME = UPPER('FK_TD_ZW_CHECK_RST5')
            AND TABLE_NAME = UPPER('TD_ZW_CHECK_RST');
          IF NUM = 0 THEN
            EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_CHECK_RST ADD CONSTRAINT FK_TD_ZW_CHECK_RST5 FOREIGN KEY (CHECK_UNIT_ID) REFERENCES TS_UNIT (RID)';
          END IF;
        END;
      ]]>
        </sql>
        <ver>279</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                      AND COLUMN_NAME = UPPER('LABOR_REL_ANNEX_PATH');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO add LABOR_REL_ANNEX_PATH NVARCHAR2(200)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>280</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_PSNINFO')
                      AND COLUMN_NAME = UPPER('LABOR_REL_ANNEX_NAME');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_PSNINFO add LABOR_REL_ANNEX_NAME NVARCHAR2(200)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>281</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORG_RECORD')
                      AND COLUMN_NAME = UPPER('UNIT_NAME');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORG_RECORD add UNIT_NAME NVARCHAR2(100)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>282</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                      AND COLUMN_NAME = UPPER('RCD_UNIT_NAME');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO add RCD_UNIT_NAME NVARCHAR2(100)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>283</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                      AND COLUMN_NAME = UPPER('RCD_UNIT_NAME');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO add RCD_UNIT_NAME NVARCHAR2(100)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>284</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_OCCHETH_INFO')
                      AND COLUMN_NAME = UPPER('RCD_UNIT_NAME');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO add RCD_UNIT_NAME NVARCHAR2(100)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>285</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                      AND COLUMN_NAME = UPPER('CHECK_RST');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO add CHECK_RST NUMBER(1)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>286</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                      AND COLUMN_NAME = UPPER('AUDIT_ADV');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO add AUDIT_ADV NVARCHAR2(200)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>287</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_TJORGINFO')
                      AND COLUMN_NAME = UPPER('CHK_PSN_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO add CHK_PSN_ID INTEGER';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>288</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ORG_WARN_CONFIG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TB_ZW_ORG_WARN_CONFIG
            (
               RID                  INTEGER              not null,
               BUS_TYPE             NUMBER(1),
               WARN_TYPE            NUMBER(2),
               ITEM_ID              INTEGER,
               WARN_TYPE_DESC       NVARCHAR2(50),
               XH                   NUMBER(3),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_ZW_ORG_WARN_CONFIG primary key (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>289</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ORG_WARN_CONFIG_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_ZW_ORG_WARN_CONFIG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>290</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TB_ZW_ORG_WARN_SUB';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TB_ZW_ORG_WARN_SUB
            (
               RID                  INTEGER              not null,
               MAIN_ID              INTEGER,
               WARN_TYPE_ID         INTEGER,
               XH                   NUMBER(3),
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TB_ZW_ORG_WARN_SUB primary key (RID),
               constraint FK_TB_ZW_ORG_WARN_SUB1 foreign key (MAIN_ID)
                 references TB_ZW_ORG_WARN_CONFIG (RID),
               constraint FK_TB_ZW_ORG_WARN_SUB2 foreign key (WARN_TYPE_ID)
                 references TS_SIMPLE_CODE (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>291</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TB_ZW_ORG_WARN_SUB_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TB_ZW_ORG_WARN_SUB_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>292</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_ORG_WARN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_ORG_WARN
            (
               RID                  INTEGER              not null,
               BUS_TYPE             NUMBER(1),
               BUS_ID               INTEGER,
               WARN_ID              INTEGER,
               CREATE_DATE          TIMESTAMP            not null,
               CREATE_MANID         INTEGER              not null,
               MODIFY_DATE          TIMESTAMP,
               MODIFY_MANID         INTEGER,
               constraint PK_TD_ZW_ORG_WARN primary key (RID),
               constraint FK_TB_ZW_ORG_WARN1 foreign key (WARN_ID)
                  references TB_ZW_ORG_WARN_CONFIG (RID)) ';
              END IF;
            END;
          ]]>
        </sql>
        <ver>293</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_ORG_WARN_SEQ';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_ORG_WARN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
            ]]>
        </sql>
        <ver>294</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                      AND COLUMN_NAME = UPPER('CHECK_RST');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO add CHECK_RST NUMBER(1)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>295</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                      AND COLUMN_NAME = UPPER('AUDIT_ADV');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO add AUDIT_ADV NVARCHAR2(200)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>296</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_SRVORGINFO')
                      AND COLUMN_NAME = UPPER('CHK_PSN_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO add CHK_PSN_ID INTEGER';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>297</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                      AND COLUMN_NAME = UPPER('CHECK_RST');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO add CHECK_RST NUMBER(1)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>298</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                      AND COLUMN_NAME = UPPER('AUDIT_ADV');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO add AUDIT_ADV NVARCHAR2(200)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>299</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_DIAGORGINFO')
                      AND COLUMN_NAME = UPPER('CHK_PSN_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO add CHK_PSN_ID INTEGER';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>300</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_OCCHETH_INFO')
                      AND COLUMN_NAME = UPPER('CHECK_RST');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO add CHECK_RST NUMBER(1)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>301</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_OCCHETH_INFO')
                      AND COLUMN_NAME = UPPER('AUDIT_ADV');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO add AUDIT_ADV NVARCHAR2(200)';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>302</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
                DECLARE
                    NUM INT;
                BEGIN
                    SELECT COUNT(1)
                    INTO NUM
                    FROM COLS
                    WHERE TABLE_NAME = UPPER('TD_ZW_OCCHETH_INFO')
                      AND COLUMN_NAME = UPPER('CHK_PSN_ID');
                    IF NUM = 0 THEN
                        EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO add CHK_PSN_ID INTEGER';
                    END IF;
                END;
            ]]>
        </sql>
        <ver>303</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_SRVORGINFO2'
                AND TABLE_NAME = 'TD_ZW_SRVORGINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_SRVORGINFO   ADD CONSTRAINT FK_TD_ZW_SRVORGINFO2 FOREIGN KEY (CHK_PSN_ID)      REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>304</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_TJORGINFO3'
                AND TABLE_NAME = 'TD_ZW_TJORGINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_TJORGINFO   ADD CONSTRAINT FK_TD_ZW_TJORGINFO3 FOREIGN KEY (CHK_PSN_ID)      REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>305</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_DIAGORGINFO3'
                AND TABLE_NAME = 'TD_ZW_DIAGORGINFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_DIAGORGINFO   ADD CONSTRAINT FK_TD_ZW_DIAGORGINFO3 FOREIGN KEY (CHK_PSN_ID)      REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>306</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_CONSTRAINTS
              WHERE CONSTRAINT_NAME = 'FK_TD_ZW_OCCHETH_INFO4'
                AND TABLE_NAME = 'TD_ZW_OCCHETH_INFO';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'ALTER TABLE TD_ZW_OCCHETH_INFO   ADD CONSTRAINT FK_TD_ZW_OCCHETH_INFO4 FOREIGN KEY (CHK_PSN_ID)      REFERENCES TS_USER_INFO (RID)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>307</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_ORG';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_ORG
                (
                    RID                  INTEGER              not null,
                   ORG_ID               INTEGER              not null,
                   ORG_NAME             VARCHAR2(100)        not null,
                   ORG_ADDR             VARCHAR2(200)        not null,
                   ORG_FZ               VARCHAR2(50),
                   ORG_FZZW             VARCHAR2(50),
                   LINK_MAN             VARCHAR2(50),
                   LINK_MB              VARCHAR2(50),
                   LINK_TEL             VARCHAR2(50),
                   FAX                  VARCHAR2(50),
                   ZIPCODE              VARCHAR2(10),
                   EMAIL                VARCHAR2(50),
                   STATE                NUMBER(1),
                   CANCEL_STATE         NUMBER(1),
                   CANCEL_DATE          DATE,
                   CREATE_DATE          TIMESTAMP            not null,
                   CREATE_MANID         INTEGER              not null,
                   constraint PK_TD_ZW_SUPPORT_ORG primary key (RID),
				   constraint FK_TD_ZW_SUPPORT_ORG1 foreign key (ORG_ID)
                        references TS_UNIT (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>308</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_ORG_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_ORG_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>309</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_PSNS
                (
                     RID                  INTEGER              not null,
                   ORG_ID               INTEGER              not null,
                   EMP_ID               INTEGER              not null,
                   ON_DUTY              NUMBER(1),
                   constraint PK_TD_ZW_SUPPORT_PSNS primary key (RID),
				   constraint FK_TD_ZW_SUPPORT_PSNS1 foreign key (ORG_ID)
                        references TD_ZW_SUPPORT_ORG (RID),
                   constraint FK_TD_ZW_SUPPORT_PSNS2 foreign key (EMP_ID)
                        references TD_ZW_PSNINFO (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>310</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_PSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_PSNS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>311</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_TRAIN';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_TRAIN
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    YEAR                 NUMBER(4),
                    TRAIN_ID             INTEGER,
                    PARTAKE_NUMBER       NUMBER(3),
                    EVIDENCE_FILE_PATH   NVARCHAR2(100),
                    CREATE_DATE          TIMESTAMP            not null,
                    CREATE_MANID         INTEGER              not null,
                    MODIFY_MANID         INTEGER,
                    MODIFY_DATE          TIMESTAMP,
                    constraint PK_TD_ZW_SUPPORT_TRAIN primary key (RID),
                    constraint FK_TD_ZW_SUPPORT_TRAIN1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_ORG (RID),
                    constraint FK_TD_ZW_SUPPORT_TRAIN2 foreign key (TRAIN_ID) references TS_SIMPLE_CODE (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>312</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_TRAIN_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_TRAIN_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>313</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_THESIS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_THESIS
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    YEAR                 NUMBER(4),
                    THESIS_NAME          NVARCHAR2(200),
                    PUBLICATION_ID       INTEGER,
                    PUBLICATION_NAME     NVARCHAR2(200),
                    IMPACT_FACTORS       NUMBER(6,3),
                    SUPPORT_FILE_PATH    NVARCHAR2(100),
                    CREATE_DATE          TIMESTAMP            not null,
                    CREATE_MANID         INTEGER              not null,
                    MODIFY_MANID         INTEGER,
                    MODIFY_DATE          TIMESTAMP,
                    constraint PK_TD_ZW_SUPPORT_THESIS primary key (RID),
                    constraint FK_TD_ZW_SUPPORT_THESIS1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_ORG (RID),
                    constraint FK_TD_ZW_SUPPORT_THESIS2 foreign key (PUBLICATION_ID) references TS_SIMPLE_CODE (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>314</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_THESIS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_THESIS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>315</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_THESIS_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_THESIS_PSNS
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    EMP_ID               INTEGER              not null,
                    CREATE_MANID         INTEGER              not null,
                    CREATE_DATE          TIMESTAMP            not null,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    constraint PK_TD_ZW_SUPPORT_THESIS_PSNS primary key (RID),
                    constraint FK_SUPPORT_THESIS_PSNS1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_THESIS (RID),
                    constraint FK_SUPPORT_THESIS_PSNS2 foreign key (EMP_ID) references TD_ZW_PSNINFO (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>316</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_THESIS_PSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_THESIS_PSNS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>317</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_MOMOGRAPH';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_MOMOGRAPH
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    YEAR                 NUMBER(4),
                    MOMOGRAPH_NAME       NVARCHAR2(200),
                    PRESS_NAME           NVARCHAR2(200),
                    SUPPORT_FILE_PATH    NVARCHAR2(100),
                    CREATE_DATE          TIMESTAMP            not null,
                    CREATE_MANID         INTEGER              not null,
                    MODIFY_MANID         INTEGER,
                    MODIFY_DATE          TIMESTAMP,
                    constraint PK_TD_ZW_SUPPORT_MOMOGRAPH primary key (RID),
                    constraint FK_TD_ZW_SUPPORT_MOMOGRAPH1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_ORG (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>318</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_MOMOGRAPH_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_MOMOGRAPH_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>319</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_MOMOGRAPH_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_MOMOGRAPH_PSNS
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    EMP_TYPE             NUMBER(1),
                    EMP_ID               INTEGER              not null,
                    CREATE_MANID         INTEGER              not null,
                    CREATE_DATE          TIMESTAMP            not null,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    constraint PK_TD_ZW_SUPPORT_MOMOGRAPH_PSN primary key (RID),
                    constraint FK_SUPPORT_MOMOGRAPH_PSNS1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_MOMOGRAPH (RID),
                    constraint FK_SUPPORT_MOMOGRAPH_PSNS2 foreign key (EMP_ID) references TD_ZW_PSNINFO (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>320</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_S_MOMOGRAPH_PSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_S_MOMOGRAPH_PSNS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>321</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_TOPIC';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_TOPIC
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    TOPIC_TYPE           NUMBER(1),
                    YEAR                 NUMBER(4),
                    TOPIC_NAME           NVARCHAR2(200),
                    PRIZE_NAME           NVARCHAR2(100),
                    AWARD_DEPARTMENT     NVARCHAR2(100),
                    PRIZE_LEVEL_ID       INTEGER,
                    SUPPORT_FILE_PATH    NVARCHAR2(100),
                    CREATE_DATE          TIMESTAMP            not null,
                    CREATE_MANID         INTEGER              not null,
                    MODIFY_MANID         INTEGER,
                    MODIFY_DATE          TIMESTAMP,
                    constraint PK_TD_ZW_SUPPORT_TOPIC primary key (RID),
                    constraint FK_TD_ZW_SUPPORT_TOPIC1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_ORG (RID),
                    constraint FK_TD_ZW_SUPPORT_TOPIC2 foreign key (PRIZE_LEVEL_ID) references TS_SIMPLE_CODE (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>322</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_TOPIC_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_TOPIC_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>323</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_TOPIC_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_TOPIC_PSNS
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    EMP_ID               INTEGER              not null,
                    CREATE_MANID         INTEGER              not null,
                    CREATE_DATE          TIMESTAMP            not null,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    constraint PK_TD_ZW_SUPPORT_TOPIC_PSNS primary key (RID),
                    constraint FK_UPPORT_TOPIC_PSNS1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_TOPIC (RID),
                    constraint FK_UPPORT_TOPIC_PSNS2 foreign key (EMP_ID) references TD_ZW_PSNINFO (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>324</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_TOPIC_PSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_TOPIC_PSNS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>325</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_POSITIVE';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_POSITIVE
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    POSITIVE_TYPE        NUMBER(1),
                    YEAR                 NUMBER(4),
                    PATENT_NAME          NVARCHAR2(200),
                    PATENT_ID            INTEGER,
                    OTHER_PATENT_TYPE    NVARCHAR2(50),
                    PATENT_NO            NVARCHAR2(100),
                    STANDARD_ID          INTEGER,
                    SUPPORT_FILE_PATH    NVARCHAR2(100),
                    CREATE_DATE          TIMESTAMP            not null,
                    CREATE_MANID         INTEGER              not null,
                    MODIFY_MANID         INTEGER,
                    MODIFY_DATE          TIMESTAMP,
                    constraint PK_TD_ZW_SUPPORT_POSITIVE primary key (RID),
                    constraint FK_TD_ZW_SUPPORT_POSITIVE1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_ORG (RID),
                    constraint FK_TD_ZW_SUPPORT_POSITIVE2 foreign key (PATENT_ID) references TS_SIMPLE_CODE (RID),
                    constraint FK_TD_ZW_SUPPORT_POSITIVE3 foreign key (STANDARD_ID) references TS_SIMPLE_CODE (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>326</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_SUPPORT_POSITIVE_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_SUPPORT_POSITIVE_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>327</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              NUM NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO NUM
              FROM USER_TABLES
              WHERE TABLE_NAME = 'TD_ZW_SUPPORT_POSITIVE_PSNS';
              IF NUM = 0 THEN
                EXECUTE IMMEDIATE 'CREATE TABLE TD_ZW_SUPPORT_POSITIVE_PSNS
                (
                    RID                  INTEGER              not null,
                    MAIN_ID              INTEGER              not null,
                    EMP_ID               INTEGER              not null,
                    CREATE_MANID         INTEGER              not null,
                    CREATE_DATE          TIMESTAMP            not null,
                    MODIFY_DATE          TIMESTAMP,
                    MODIFY_MANID         INTEGER,
                    constraint PK_TD_ZW_SUPPORT_POSITIVE_PSNS primary key (RID),
                    constraint FK_SUPPORT_POSITIVE_PSNS1 foreign key (MAIN_ID) references TD_ZW_SUPPORT_POSITIVE (RID),
                    constraint FK_SUPPORT_POSITIVE_PSNS2 foreign key (EMP_ID) references TD_ZW_PSNINFO (RID)
             	)';
              END IF;
            END;
          ]]>
        </sql>
        <ver>328</ver>
    </sqlsentence>
    <sqlsentence>
        <sql>
            <![CDATA[
            DECLARE
              V1 NUMBER;
            BEGIN
              SELECT COUNT(1)
              INTO V1
              FROM USER_SEQUENCES
              WHERE SEQUENCE_NAME = 'TD_ZW_S_POSITIVE_PSNS_SEQ';
              IF V1 = 0 THEN
                EXECUTE IMMEDIATE 'CREATE SEQUENCE TD_ZW_S_POSITIVE_PSNS_SEQ MINVALUE 0 MAXVALUE 9999999999999999999999999 START WITH 1 INCREMENT BY 1 NOCACHE';
              END IF;
            END;
          ]]>
        </sql>
        <ver>329</ver>
    </sqlsentence>
</sqlsentences>

