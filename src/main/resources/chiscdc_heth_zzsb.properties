#4类资质申报通用配置
#是否显示外出开展职业健康检查工作能力地区(外检车辆)，显示配置true，不显示配置false
zzsb.ifHethFj=false
#资质人员是否显示收件人信息，1是；0否
zzsb.ifShowReceiveInfo=0
#检查机构是否有质量管理体系 1是； 0否
zzsb.ifQualAnnex=0
#职业健康检查机构资质申报-->是否有服务明细，空或0：不显示；1：显示（不能与“是否按地区备案”参数zzsb.ifZoneRecord同时为1）
zzsb.ifServiceDetails=0

#职业健康检查机构资质申报-->人员属性一行显示个数配置
zzsb.tjorginfoPsnTypeNum=3
#放射卫生服务机构资质申报-->人员属性一行显示个数配置
zzsb.srvorginfoPsnTypeNum=3
#职业病诊断机构资质申报-->人员属性一行显示个数配置
zzsb.diagorginfoPsnTypeNum=3
#职业卫生技术服务机构资质申报-->人员属性一行显示个数配置
zzsb.occhethInfoPsnTypeNum=4
#技术支撑机构资质申报-->人员属性一行显示个数配置
zzsb.supportOrgPsnTypeNum=4

#吉林相关参数
#职业健康检查机构是否按地区备案，0否；1是(该参数必须配置)
zzsb.ifZoneRecord=0
#职业健康检查机构是否有人员汇总及填报说明人员汇总，0否；1是(该参数必须配置)
zzsb.ifPersonSummary=0
#职业病诊断机构是否显示备案单位名称、放射卫生技术服务机构与职业卫生技术服务机构是否显示资质批准单位，0否；1是
zzsb.ifShowApproveUnit=0
#四类资质的资质附件是否显示房屋所有权证或租赁合同，0否；1是
zzsb.ifHaveHouseAnnex=0
#四类资质申报是否有审核（检查机构预警）功能（0：无；1：有）
zzsb.ifCheckOrg=0

#陕西相关参数
#职业健康检查机构资质申报-->检查机构资质备案有无有效期限 空或0：无 1：有
zzsb.ifValidityPeriod=0
#职业健康检查机构资质申报-->有无资质机构备案审核流程 空或0：无 1：有
zzsb.ifReviewProcess=0
#外检车辆版本（1：车辆信息必填且上传照片，陕西；其它或不配置：无）
zzsb.outCarVersion=0
#资质人员的资质信息是否陕西版本("中级以上（含）专业技术任职资格证书附件"显示调整为"职业病诊断资格证书附件"，"中级以上（含）专业技术任职聘任书附件"显示调整为"专业人员任命文件附件"等陕西特有变动)
zzsb.ifRequiredAnnex = 0

#黑龙江相关参数
#职业健康检查机构资质填报/职业病诊断机构资质填报-->是否显示备案管理机构 1：显示；0或不配置：不显示
zzsb.ifShowRcdOrg=0

#辽宁相关参数
#职业健康检查机构资质填报-->检查机构资质有无批准的执业区域 1：有；0或不配置：无
zzsb.ifZoneApprove=0
#【职业健康检查资质申报】、【职业病诊断资质申报】、【资质机构查询】模块是否显示【质量控制结果】0：不显示；1：显示
zzsb.zkCheckRst=0
#职业健康检查、职业病诊断机构资质填报页面备案编号提示内容
zzsb.certNoTip=示例：卫健委公示第*批第*号
#“职业卫生服务机构查询”中“职业健康检查机构”是否显示“服务概况”（1：显示；0或不配置：不显示）
zzsb.ifShowCheckOrgAnaly=0

#江苏相关参数
#资质申报-->支撑机构有无科研、学术信息。【支撑机构信息管理】模块是否显示【科研课题信息】【学术培训信息】标签页0：不显示；1：显示
zzsb.hasResearchAndAcademiaInfo=0