<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">


    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{zwOrgInfoSearchZzsbBean}"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/zzsb/zwOrgInfoSeView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <style>
            .card-panel {
                height: 98px;
                width: 260px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
            }
            .card-panel.panel-a {
                background-image: url('/resources/images/fwgk/card_fuwudanwei.png');
            }
            .card-panel.panel-b {
                background-image: url('/resources/images/fwgk/card_laodongzhe.png');
            }
            .card-panel.panel-c {
                background-image: url('/resources/images/fwgk/card_xixinbaosong.png');
            }

            .card-up-num {
                color: rgba(255, 255, 255, 1);
                font-size: 34px !important;
                line-height: 32px;
                font-weight: 600;
                padding-left: 30px;
                padding-top: 15px;
            }
            .card-up-word {
                color: rgba(255, 255, 255, 1);
                font-size: 22px !important;
                font-weight: 600;
                padding-left: 30px;
                padding-top: 15px;
            }

            .card-down {
                color: rgba(255, 255, 255, 1);
                font-size: 16px;
                font-weight: 500;
                padding-left: 30px;
                padding-bottom: 15px;
            }
            .service-overview-hr {
                margin: 10px 0;
                color: #E0DEDE;
            }
        </style>
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml" />
        <script type="text/javascript" src="/resources/js/namespace.js">
        </script>
        <script type="text/javascript" src="/resources/js/validate/system/validate.js">
        </script>
        <script type="text/javascript" src="/resources/echarts/3.0/echarts.min.js">
        </script>
        <script type="text/javascript" src="/resources/echarts/3.0/macarons.js">
        </script>
        <script type="text/javascript">
            var ecConfig = echarts.config;
            var pieChart;
            function buildPieChart() {
                if (pieChart != null) {
                    pieChart.clear();
                }
                var div = document.getElementById("pieDiv");
                if (div != null) {
                    pieChart = echarts.init(div, 'macarons');

                    var json = document.getElementById("pieJson").value;
                    if (json !== '') {
                        json.legend
                        pieChart.setOption(eval("(" + json + ")"));
                    }
                }
            }
        </script>
        <script type="text/javascript">
            //<![CDATA[
            function getDownloadFileClick() {
                document.getElementById("tabView:mainForm:downloadFileBtn").click();
            }
            function disabledInput(){
                var text;
                var $tabView = $("#tabView\\:viewForm\\:codeEditGrid");
                $tabView.find("input,textarea,label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"修改"==text){
                        $(this).remove();
                        var chArr = document.getElementsByClassName("delSpacer");
                        for(i=0;i<chArr.length;i++){
                            //删除元素 元素.parentNode.removeChild(元素);
                            if (chArr[i] != null)
                                chArr[i].parentNode.removeChild(chArr[i]);
                        }
                    }else if("查看附件"==text || "培训履历"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box'],div[class*='ui-chkbox']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("删除"==text){
                        $(this).remove();
                    }else if("关闭"==text||"查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            //]]>
        </script>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构查询"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{zwOrgInfoSearchZzsbBean.searchAction}"
                                 update="dataTable"
                                 process="@this,:tabView:mainForm:mainGrid"/>
                <p:commandButton value="导出" icon="ui-icon-document" id="exportDataBtn"
                                 onclick="getDownloadFileClick();"/>
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                	onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{zwOrgInfoSearchZzsbBean.export()}"/>
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 0px;width: 220px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{zwOrgInfoSearchZzsbBean.zoneList}"
                                    zoneCode="#{zwOrgInfoSearchZzsbBean.searchZoneCode}"
                                    zoneName="#{zwOrgInfoSearchZzsbBean.searchZoneName}"
                                    zoneType="#{zwOrgInfoSearchZzsbBean.searchZoneType}"
                                    onchange="initSearchUnit()" />
                <p:remoteCommand name="initSearchUnit" action="#{zwOrgInfoSearchZzsbBean.searchAction}" process="@this,searchZone"
                                 update="dataTable"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="资质类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;padding-top: 8px;width: 200px;" >
                <p:selectOneMenu id="searchUnit" style="width:180px" value="#{zwOrgInfoSearchZzsbBean.searchZwOrhType}"
                                 onchange="searchUnitArtInfo()">
                    <f:selectItem itemLabel="--全部--" itemValue=""/>
                    <f:selectItems  value="#{zwOrgInfoSearchZzsbBean.searchZwOrhTypeList}"  />
                </p:selectOneMenu>
                <p:remoteCommand name="searchUnitArtInfo" action="#{zwOrgInfoSearchZzsbBean.searchAction}" process="@this,searchUnit"
                                 update="dataTable"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="单位名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;">
                <p:inputText value="#{zwOrgInfoSearchZzsbBean.searchUnitName}" maxlength="40" size="27"/>
            </p:column>
        </p:row>


    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="地区" style="width:180px;padding-left: 3px;">
            <h:outputLabel value="#{itm[11] > 3 ? itm[12].substring(itm[12].indexOf('_')+1,itm[12].length()) : itm[12]}">
            </h:outputLabel>
        </p:column>
        <p:column headerText="单位名称" style="width:300px;padding-left: 3px;">
            <h:outputLabel value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="资质类型" style="width:170px;padding-left: 3px;">
            <h:outputLabel rendered="#{itm[1] == 0}" value="职业健康检查机构"/>
            <h:outputLabel rendered="#{itm[1] == 1}" value="放射卫生技术服务机构"/>
            <h:outputLabel rendered="#{itm[1] == 2}" value="职业病诊断机构"/>
            <h:outputLabel rendered="#{itm[1] == 3}" value="职业卫生技术服务机构"/>
            <h:outputLabel rendered="#{itm[1] == 4}" value="技术支撑机构"/>
        </p:column>
        <p:column headerText="法定代表人" style="width:80px;text-align: center">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="法定代表人职务" style="width:100px;text-align: center">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="备案（证书）编号" style="width:220px;text-align: center">
            <h:outputLabel value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="备案（发证）日期" style="width:120px;text-align: center">
            <h:outputLabel value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="失效日期" style="width:80px;text-align: center">
            <h:outputLabel value="#{itm[10]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>

        <p:column headerText="最后申报日期" style="width:80px;text-align: center">
            <h:outputLabel value="#{itm[7]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
       <!-- <p:column headerText="状态" style="width:60px;text-align: center">
            <h:outputLabel rendered="{itm[9] == 0}" value="保存"/>
            <h:outputLabel rendered="{itm[9] == 1}" value="提交"/>
        </p:column>-->
        <p:column headerText="操作" style="padding-left: 3px;" exportable="false">
            <p:commandLink value="服务概况" action="#{zwOrgInfoSearchZzsbBean.showServiceOverview}"
                           process="@this" update=":serviceOverviewPanel"
                           rendered="#{null ne itm[1] and 0 == itm[1] and zwOrgInfoSearchZzsbBean.ifShowCheckOrgAnaly == '1'}">
                <f:setPropertyActionListener value="#{itm}" target="#{zwOrgInfoSearchZzsbBean.selectObj}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{null ne itm[1] and 0 == itm[1] and zwOrgInfoSearchZzsbBean.ifShowCheckOrgAnaly == '1'}"/>
            <p:commandLink value="详情" action="#{zwOrgInfoSearchZzsbBean.viewInitAction}"
                           process="@this" update=":tabView">
                <f:setPropertyActionListener value="#{itm}" target="#{zwOrgInfoSearchZzsbBean.selectObj}"/>
                <f:setPropertyActionListener value="#{itm[1]}" target="#{zwOrgInfoSearchZzsbBean.zwOrgType}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertPoll">
        <p:dialog id="serviceOverviewDialog" header="本年度服务概况" widgetVar="ServiceOverviewDialog"
                  resizable="false" width="900" height="630" modal="true">
            <p:scrollPanel id="serviceOverviewPanel" mode="native" style="border:0;padding: 10px 0;">
                <div id="serviceOverviewUp" style="display: flex;justify-content: space-around;padding: 5px 10px;">
                    <div class="card-panel panel-a">
                        <p:outputLabel styleClass="card-up-num" value="#{zwOrgInfoSearchZzsbBean.serviceOverviewStr1}"/>
                        <div class="card-down">服务用人单位数</div>
                    </div>
                    <div class="card-panel panel-b">
                        <p:outputLabel styleClass="card-up-num" value="#{zwOrgInfoSearchZzsbBean.serviceOverviewStr2}"/>
                        <div class="card-down">体检劳动者人次数</div>
                    </div>
                    <div class="card-panel panel-c">
                        <p:outputLabel styleClass="card-up-word" value="#{zwOrgInfoSearchZzsbBean.serviceOverviewStr3}"/>
                        <div class="card-down">个案信息报送方式</div>
                    </div>
                </div>
                <hr class="service-overview-hr"/>
                <div id="serviceOverviewDown">
                    <div id="pieDiv" style="width:100%;height:450px;margin: 35px 0 auto;">
                    </div>
                    <h:inputHidden id="pieJson" value="#{zwOrgInfoSearchZzsbBean.pieJson}"/>
                </div>
            </p:scrollPanel>
        </p:dialog>
    </ui:define>
</ui:composition>