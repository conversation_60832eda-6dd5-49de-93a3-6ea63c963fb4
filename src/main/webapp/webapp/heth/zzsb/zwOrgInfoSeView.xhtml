<ui:composition xmlns="http://www.w3.org/1999/xhtml"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:p="http://primefaces.org/ui">
	<style type="text/css">
		.zwx-tooltip{
			-webkit-line-clamp: 1;
		}
		.psnDiag .ui-widget-content{
			padding-top: 0px !important;
		}
	</style>
<script type="text/javascript">
            //<![CDATA[

			var show = false;
			var target;
			$(function() {
				$("body").mousemove(
						function(e) {
							if (show == true) {
								var label = document.getElementById("label");
								if (e.pageX < $(label).offset().left
										|| e.pageX > ($(label).offset().left + $(label).width())
										|| e.pageY < ($(target).offset().top)
										|| e.pageY > ($(label).offset().top + $(label).height())) {
									$(label).fadeOut("6000",
											function() {
												show = false;
											});
								}

							}
						});

			});
			function getLabelPanel(div, desc) {
				if (!show) {
					var label = document.getElementById("label");
					$(label).css({
						"display" : "none"
					});
					$(label).css(
							{
								"top" : $(div).offset().top + $(div).height()
										+ 3,
								"left" : $(div).offset().left
										- ($(label).width() / 2+20)
							});
					var str = "";
					var code = desc.split(",");
					if (code.length>0) {
						for (var int = 0; int < code.length; int++) {
							str = str + code[int] + "<br/>";
						}
					}

					$("#descDiv").html(str);
					$(label).fadeIn("6000", function() {
						target = div;
						show = true;
					});

				}

			}
			function hideTooltips() {
				jQuery('.zwx-tooltip').unbind('mouseover').unbind('mouseout');
				jQuery('.zwx-tooltip-two').unbind('mouseover').unbind('mouseout');
				jQuery('.ui-tooltip').css('display', 'none');
			}
             //]]>
        </script>
	<h:form id="viewForm" style="height: 100%;">

		<p:outputPanel style="height: 100%;">
			<p:panelGrid style="width:100%;margin-bottom:5px;">
				<f:facet name="header">
					<p:row>
						<p:column style="text-align:left;padding-left:5px;height: 20px;">
							<h:outputText value="职业健康检查机构资质信息"
								rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 0}" />
							<h:outputText value="放射卫生技术服务机构资质信息"
								rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 1}" />
							<h:outputText value="职业病诊断机构资质信息"
								rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 2}" />
							<h:outputText value="职业卫生技术服务机构资质信息"
								rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3}" />
							<h:outputText value="技术支撑机构信息"
										  rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 4}" />
						</p:column>
					</p:row>
				</f:facet>
			</p:panelGrid>

			<p:outputPanel styleClass="zwx_toobar_42">
				<h:panelGrid columns="2"
					style="border-color:transparent;padding:0px;">
					<span class="ui-separator"><span
						class="ui-icon ui-icon-grip-dotted-vertical" /></span>
					<p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
						action="#{zwOrgInfoSearchZzsbBean.backAction}" update=":tabView"
						immediate="true" onclick="hideTooltips();"/>
				</h:panelGrid>
			</p:outputPanel>

			<p:tabView id="tabView"  widgetVar="OrgTableView"
				style="margin-top: 5px;">
				<p:tab title="基本信息">
					<p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;"
						id="caseGrid">
						<p:row>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="单位名称：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;"
								colspan="3">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.orgName}" />
							</p:column>
						</p:row>
						<p:row>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.selectObj[1]!=4?'注册地址':'地址'}：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;"
								colspan="3">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.orgAddr}" />
							</p:column>
						</p:row>
						<p:row rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3}">
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="通讯地址：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;"
								colspan="3">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.communiAddr}" />
							</p:column>
						</p:row>
						<p:row rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3}">
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="实验室地址：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;"
								colspan="3">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.labAddr}" />
							</p:column>
						</p:row>
						<p:row>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="法定代表人：" />
							</p:column>
							<p:column
								style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.orgFz}" />
							</p:column>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3?'单位类型':'法定代表人职务'}：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.orgFzzw}" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]!=3}"/>
								<p:outputLabel value="#{mgrbean.zwOrginfoEntity.fkByUnitTypeId.codeName}" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==3}"/>
							</p:column>
						</p:row>
						<p:row>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="联系人：" />
							</p:column>
							<p:column
								style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.linkMan}" />
							</p:column>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="联系人手机：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.linkMb}" />
							</p:column>
						</p:row>
						<p:row>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="电话：" />
							</p:column>
							<p:column
								style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.linkTel}" />
							</p:column>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="传真：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.fax}" />
							</p:column>
						</p:row>
						<p:row>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="邮编：" />
							</p:column>
							<p:column
								style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.zipcode}" />
							</p:column>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="电子邮箱：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.email}" />
							</p:column>
						</p:row>
						<p:row rendered="#{!((zwOrgInfoSearchZzsbBean.selectObj[1] == 0 and mgrbean.ifZoneRecord==1)or(zwOrgInfoSearchZzsbBean.selectObj[1] == 4))}">
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="资质证书编号：" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 1 or zwOrgInfoSearchZzsbBean.selectObj[1] == 3}"/>
								<p:outputLabel value="备案编号：" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 0 or zwOrgInfoSearchZzsbBean.selectObj[1] == 2}"/>
							</p:column>
							<p:column
								style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.certNo}" />
							</p:column>
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 0 or zwOrgInfoSearchZzsbBean.selectObj[1] == 2?'备案日期':'发证时间'}：" rendered="#{mgrbean.ifValidityPeriod != '1' or (mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType != 0)}"/>
								<p:outputLabel value="备案有效期限：" rendered="#{mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType == 0}" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.ifValidityPeriod != '1' or (mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType != 0)}">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.firstGetday}">
									<f:convertDateTime pattern="yyyy-MM-dd"
										timeZone="Asia/Shanghai" locale="cn" />
								</p:outputLabel>
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType == 0}">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.firstGetday}">
									<f:convertDateTime pattern="yyyy-MM-dd"
										timeZone="Asia/Shanghai" locale="cn" />
								</p:outputLabel>
								~
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.filingDate}">
									<f:convertDateTime pattern="yyyy-MM-dd"
										timeZone="Asia/Shanghai" locale="cn" />
								</p:outputLabel>
							</p:column>
						</p:row>
						<p:row rendered="#{mgrbean.ifShowApproveUnit==1 and (zwOrgInfoSearchZzsbBean.selectObj[1] == 1 or  zwOrgInfoSearchZzsbBean.selectObj[1] == 2 or zwOrgInfoSearchZzsbBean.selectObj[1] == 3)}">
							<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="备案单位名称：" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 2}"/>
								<p:outputLabel value="资质批准单位：" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 1 or zwOrgInfoSearchZzsbBean.selectObj[1] == 3}"/>
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.rcdUnitName}" />
							</p:column>
						</p:row>

						<p:row rendered="#{(zwOrgInfoSearchZzsbBean.selectObj[1] == 0 or zwOrgInfoSearchZzsbBean.selectObj[1] == 2) and zwOrgInfoSearchZzsbBean.ifShowRcdOrg}">
							<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="备案管理机构：" />
							</p:column>
							<p:column colspan="3" style="text-align:left;padding-left:3px;height: 25px;">
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.rcdOrgMap.get(zwOrgInfoSearchZzsbBean.zwOrginfoEntity.rcdOrgId)}" />
							</p:column>
						</p:row>
						<p:row rendered="#{!(zwOrgInfoSearchZzsbBean.selectObj[1] == 0 and mgrbean.ifZoneRecord==1)}">
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="状态：" />
							</p:column>
							<p:column
								style="text-align:left;padding-left:3px;height: 25px;width: 260px;" colspan="#{(zwOrgInfoSearchZzsbBean.selectObj[1] == 4 and 0==zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelState)?3:''}">
								<p:outputLabel value="正常"
									rendered="#{null == zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelState or zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelState == 0}" />
								<p:outputLabel value="注销"
									rendered="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelState == 1}" />
							</p:column>
							<p:column rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 1 or zwOrgInfoSearchZzsbBean.selectObj[1] == 3}"
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="失效日期：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;"
									  rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 1 or zwOrgInfoSearchZzsbBean.selectObj[1] == 3}"
								colspan="3">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.validDate}">
									<f:convertDateTime pattern="yyyy-MM-dd"
										timeZone="Asia/Shanghai" locale="cn" />
								</p:outputLabel>
							</p:column>
							<p:column rendered="#{!(zwOrgInfoSearchZzsbBean.selectObj[1] == 0 and mgrbean.ifZoneRecord==1) and (zwOrgInfoSearchZzsbBean.selectObj[1] == 0 or zwOrgInfoSearchZzsbBean.selectObj[1] == 2 or zwOrgInfoSearchZzsbBean.selectObj[1] == 4) and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelState == 1}"
									style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="注销日期："/>
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{!(zwOrgInfoSearchZzsbBean.selectObj[1] == 0 and mgrbean.ifZoneRecord==1) and (zwOrgInfoSearchZzsbBean.selectObj[1] == 0 or zwOrgInfoSearchZzsbBean.selectObj[1] == 2 or zwOrgInfoSearchZzsbBean.selectObj[1] == 4) and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelState == 1}">
								<p:outputLabel
										value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelDate}">
									<f:convertDateTime pattern="yyyy-MM-dd"
													   timeZone="Asia/Shanghai" locale="cn" />
								</p:outputLabel>
							</p:column>
						</p:row>
						<p:row
							rendered="#{!(zwOrgInfoSearchZzsbBean.selectObj[1] == 0 and mgrbean.ifZoneRecord==1) and (zwOrgInfoSearchZzsbBean.selectObj[1] == 1 or zwOrgInfoSearchZzsbBean.selectObj[1] == 3) and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelState == 1}">
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="注销日期：" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3?'1':'3'}">
								<p:outputLabel
									value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelDate}">
									<f:convertDateTime pattern="yyyy-MM-dd"
										timeZone="Asia/Shanghai" locale="cn" />
								</p:outputLabel>
							</p:column>
							<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3}">
				               <p:outputLabel value="资质等级：" />
				           </p:column>
				           <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3}">
				               <p:outputLabel value="#{mgrbean.zwOrginfoEntity.fkByLevelId.codeName}" />
				           </p:column>
						</p:row>
						<p:row rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3 and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.cancelState != 1}">
				           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
				               <p:outputLabel value="资质等级：" />
				           </p:column>
				           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
				               <p:outputLabel value="#{mgrbean.zwOrginfoEntity.fkByLevelId.codeName}" />
				           </p:column>
				       </p:row>
						<p:row rendered="#{!((zwOrgInfoSearchZzsbBean.selectObj[1] == 0 and mgrbean.ifZoneRecord==1) or (zwOrgInfoSearchZzsbBean.selectObj[1] == 4))}">
							<p:column
								style="text-align:right;padding-right:3px;width:150px;height: 25px;">
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 3?'业务范围：':'服务项目：'}" />
							</p:column>
							<p:column style="text-align:left;padding-left:3px;height: 25px;"
								colspan="3">
								<table width="100%" style="border-color: white">
									<c:forEach items="#{zwOrgInfoSearchZzsbBean.serviceList}"
										var="service">
										<tr>
											<td><h:outputLabel value="#{service}" /></td>
										</tr>
									</c:forEach>
								</table>
							</p:column>
						</p:row>
						<p:row rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 0 and mgrbean.ifZoneRecord==1}">
					   	   	 <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
					             <p:outputLabel value="备案信息："/>
					         </p:column>
					         <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
					         	<p:dataTable value="#{mgrbean.tjorgRecords}" 
					         		emptyMessage="没有数据！" var="itm">
					         		<p:column headerText="备案地区" style="text-align:center;width:100px;">
					         			<h:outputText value="#{itm.fkByZoneId.zoneName}"/>
					         		</p:column>
									<p:column headerText="备案单位名称" style="width:160px;">
										<h:outputText value="#{itm.unitName}"/>
									</p:column>
					         		<p:column headerText="备案编号" style="text-align:center;width:150px;">
					         			<h:outputText value="#{itm.rcdNo}"/>
					         		</p:column>
					         		<p:column headerText="备案日期" style="text-align:center;width:80px;">
					         			<h:outputText value="#{itm.certDate}">
					         				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
					         			</h:outputText>
					         		</p:column>
					         		<p:column headerText="服务项目" style="width:380px;">
					         			<h:outputText value="#{itm.serviceItems}"></h:outputText>
					         		</p:column>
					         		<p:column headerText="状态" style="text-align:center;width:80px;">
					         			<h:outputText value="正常" rendered="#{itm.stateMark==0}"></h:outputText>
					         			<h:outputText value="注销" rendered="#{itm.stateMark==1}"></h:outputText>
					         		</p:column>
					         		<p:column headerText="注销日期" style="text-align:center;width:80px;">
					         			<h:outputText value="#{itm.logoutDate}">
					         				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
					         			</h:outputText>
					         		</p:column>
					         		<p:column headerText="操作" style="width:280px;">
					         		</p:column>
					         	</p:dataTable>
					         </p:column>
					       </p:row>
                        <p:row rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==0 and (zwOrgInfoSearchZzsbBean.ifZoneApprove or zwOrgInfoSearchZzsbBean.ifHethFj)}">
							<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{zwOrgInfoSearchZzsbBean.ifZoneApprove}">
								<p:outputLabel value="批准的执业区域：" />
							</p:column>
							<p:column colspan="#{zwOrgInfoSearchZzsbBean.ifHethFj?1:3}" style="text-align:left;padding-left:3px;height: 25px;#{zwOrgInfoSearchZzsbBean.ifHethFj?'width: 260px;':''}" rendered="#{zwOrgInfoSearchZzsbBean.ifZoneApprove}">
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.zoneApproveName}" />
							</p:column>
                            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{zwOrgInfoSearchZzsbBean.ifHethFj}">
                                <p:outputLabel value="外出开展职业健康检查"/><br/>
                                <p:outputLabel value="工作能力："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="#{zwOrgInfoSearchZzsbBean.ifZoneApprove?1:3}" rendered="#{zwOrgInfoSearchZzsbBean.ifHethFj}">
                                <p:outputLabel value="不具备" rendered="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower == 0}"/>
                                <p:outputLabel value="具备" rendered="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower == 1}"/>
                            </p:column>
                        </p:row>
						<!--支撑机构-->
						<p:row rendered="#{zwOrgInfoSearchZzsbBean.zwOrgType==4 and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.state == 1 and '2' eq zwOrgInfoSearchZzsbBean.unitType}">
							<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
								<p:outputLabel value="机构类型："/>
							</p:column>
							<p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3" >
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.fkByTypeId.codeName}"/>
							</p:column>
						</p:row>
						<p:row rendered="#{zwOrgInfoSearchZzsbBean.zwOrgType==4 and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.state == 1 and null ne zwOrgInfoSearchZzsbBean.unitType}">
							<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
								<p:outputLabel value="技术支撑能力："/>
							</p:column>
							<p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3" >
								<c:forEach items="#{mgrbean.techTypesList}" var="techType">
									<p:outputPanel style="display: flex;vertical-align: middle;">
										<c:if test="#{techType.selNums>0}">
											<p:outputLabel value="#{techType.codeName}"/>
											<p:outputLabel value="（" style="padding-right: 3px"/>
											<p:outputLabel value="#{techType.smallTypeNames}"/>
											<p:outputLabel value="）" style="padding-left: 3px"/>
										</c:if>
									</p:outputPanel>
								</c:forEach>
							</p:column>
						</p:row>
						<p:row rendered="#{zwOrgInfoSearchZzsbBean.zwOrgType==4 and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.state == 1 and null ne zwOrgInfoSearchZzsbBean.unitType}">
							<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
								<p:outputLabel value="具备职业健康技术服务相关资质或能力的情况："/>
							</p:column>
							<p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3">
								<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.quaTypeName}"/>
							</p:column>
						</p:row>
					</p:panelGrid>
					<p:fieldset legend="职业病因素监测开展情况" style="margin-top: 5px;margin-bottom: 5px;"
								toggleable="true" toggleSpeed="500" rendered="#{zwOrgInfoSearchZzsbBean.zwOrgType == 4 and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.state == 1 and null ne zwOrgInfoSearchZzsbBean.unitType}">
						<p:dataTable  paginatorPosition="bottom"
									  value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.supportChkList}"
									  widgetVar="SupportChkTable" var="item"
									  emptyMessage="没有您要找的记录！" rowIndexVar="R">
							<!--@elvariable id="R" type="java.lang.Integer"-->
							<p:column headerText="序号" style="width:40px;text-align: center;">
								<p:outputLabel value="#{R+1}"/>
							</p:column>
							<p:column headerText="监测周期" style="width:300px;text-align: center;">
								<p:outputLabel value="#{item.startDate}">
									<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
								</p:outputLabel>
								~
								<p:outputLabel value="#{item.endDate}">
									<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
								</p:outputLabel>
							</p:column>
							<p:column headerText="累计开展职业病危害因素监测企业数（家）" style="width:300px;text-align: center;"
									  styleClass="th_required_header">
								<p:outputLabel value="#{item.crptNum}"/>
							</p:column>
						</p:dataTable>
					</p:fieldset>
					<p:fieldset legend="财政投入情况" style="margin-top: 5px;margin-bottom: 5px;"
								toggleable="true" toggleSpeed="500" rendered="#{zwOrgInfoSearchZzsbBean.zwOrgType == 4 and zwOrgInfoSearchZzsbBean.zwOrginfoEntity.state == 1 and '2' eq zwOrgInfoSearchZzsbBean.unitType}">
						<p:dataTable paginatorPosition="bottom"
									 value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.supportFundList}"
									 widgetVar="SupportFundTable" var="item"
									 emptyMessage="没有您要找的记录！" rowIndexVar="R">
							<!--@elvariable id="R" type="java.lang.Integer"-->
							<p:column headerText="序号" style="width:40px;text-align: center;">
								<p:outputLabel value="#{R+1}"/>
							</p:column>
							<p:column headerText="年份" style="width:300px;text-align: center;">
								<p:outputLabel value="#{item.year}"/>
							</p:column>
							<p:column headerText="投入情况（万元）" style="width:300px;text-align: center;"
									  styleClass="th_required_header">
								<p:outputLabel value="#{item.fund}"/>
							</p:column>
						</p:dataTable>
					</p:fieldset>
				</p:tab>

				<p:tab title="人员信息">
					<p:panelGrid style="width:100%;margin:10px 0px;" rendered="#{mgrbean.selectObj[1] == 0 and mgrbean.ifPersonSummary==1}">
						<f:facet name="header">
							<p:row>
								<p:column style="text-align:left;padding:6px;" styleClass="ui-state-default">
									<h:outputText value="人员汇总"></h:outputText>
								</p:column>
							</p:row>
						</f:facet>
						<p:row>
							<p:column>
								<p:outputPanel style="padding:12px 6px;" id="personSummary">
									<table>
										<c:forEach items="#{mgrbean.personSummaryList}" var="itm">
											<tr>
												<c:forEach items="#{itm}" var="val" varStatus="valIndex">
													<td style="border:none;">
														<h:outputText value="#{val}" escape="false" style="padding:6px;"></h:outputText>
														<h:outputText value="|" style="float: right;" rendered="#{valIndex.index!=itm.size()-1}"></h:outputText>
													</td>
												</c:forEach>
											</tr>
										</c:forEach>
									</table>
								</p:outputPanel>
							</p:column>
						</p:row>
					</p:panelGrid>
					<p:dataTable var="itm" value="#{mgrbean.zwOrginfoEntity.zwOrgPsns}"
								 id="psnDatatable" emptyMessage="暂无记录！" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
								 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								 rowsPerPageTemplate="#{mgrbean.perPageSize}" lazy="true" pageLinks="5"
								 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
								 style="margin-top:5px;width:100%">
						<p:column headerText="姓名" style="text-align:center;width:80px;">
							<p:commandLink value="#{itm.tdZwPsninfo.empName}"
								update=":tabView:viewForm:codeEditDialog" process="@this"
								oncomplete="PF('CodeEditDialog').show();"
								action="#{zwOrgInfoSearchZzsbBean.codeViewAction}">
								<f:setPropertyActionListener value="#{itm.tdZwPsninfo}"
									target="#{zwOrgInfoSearchZzsbBean.tdZwPsninfo}" />
							</p:commandLink>
						</p:column>
						<p:column headerText="性别" style="text-align:center;width:40px;">
							<p:outputLabel value="#{itm.tdZwPsninfo.sex}" />
						</p:column>
						<p:column headerText="职称" style="text-align:center;width:100px; ">
							<p:outputLabel value="#{itm.tdZwPsninfo.fkByTitleId.codeName}" />
						</p:column>
						<p:column headerText="人员属性" style="width:260px; ">
							<p:outputLabel id="psnTypes" value="#{itm.tdZwPsninfo.psnTypes}" styleClass="zwx-tooltip"/>
							<p:tooltip for="psnTypes" style="max-width:260px;">
								<p:outputLabel value="#{itm.tdZwPsninfo.psnTypes}" escape="false"/>
							</p:tooltip>
						</p:column>
						<p:column headerText="在岗状态" style="text-align:center;width:60px; ">
                           	<p:outputLabel value="在职" rendered="#{itm.onDuty=='1'}"></p:outputLabel>
                           	<p:outputLabel value="离职" rendered="#{itm.onDuty=='2'}"></p:outputLabel>
                        </p:column>
						<p:column headerText="#{zwOrgInfoSearchZzsbBean.selectObj[1]==0?'证书（发文）编号':'证书编号'}" style="width:180px;">
							<p:outputLabel value="#{itm.tdZwPsninfo.certNoStr}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')==-1}"/>
							<p:outputLabel value="#{itm.tdZwPsninfo.certNoStr.substring(0,itm.tdZwPsninfo.getCertNoStr().indexOf(','))}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0}"/>
							<div style="background-color: #F35E37;display: inline;padding: 3px;border-radius: 3px;display:#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0?'':'none'}">
								<p:outputLabel value="其他" style="color:white;"
											   rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0}"
											   onmouseover="getLabelPanel(this,'#{itm.tdZwPsninfo.certNoStr.substring(itm.tdZwPsninfo.getCertNoStr().indexOf(',')+1,itm.tdZwPsninfo.getCertNoStr().length())}')"/>
							</div>
							<p:outputLabel value="#{itm.tdZwPsninfo.certNoStr}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')==0}"/>
						</p:column>
						<p:column headerText="个人剂量计编号" style="width:200px;"
								  rendered="false">
							<p:outputLabel value="#{itm.jlCode}" />
						</p:column>
						<p:column headerText="个人剂量累积剂量" style="width:200px;"
							rendered="false">
							<p:outputLabel value="#{itm.ljJl}" />
						</p:column>
						<p:column headerText="从事项目" style="padding-left:5px;width:260px;"
							rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 0}">
							<!--<p:outputLabel value="#{itm.wordItemStr}" />-->
                            <!-- 只显示跟检查资质相关的资质类别的资质范围 -->
							<p:outputLabel id="zzlbRange" value="#{itm.tdZwPsninfo.zzlbRange}" styleClass="zwx-tooltip"/>
							<p:tooltip for="zzlbRange" style="max-width:260px;">
								<p:outputLabel value="#{itm.tdZwPsninfo.zzlbRange}" escape="false"/>
							</p:tooltip>
						</p:column>
						<p:column headerText="从事科室" style="padding-left:5px;width:260px;" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 0}">
							<p:outputLabel id="wordOfficeName" value="#{itm.wordOfficeName}" styleClass="zwx-tooltip"/>
							<p:tooltip for="wordOfficeName" style="max-width:260px;">
								<p:outputLabel value="#{itm.wordOfficeName}" escape="false"/>
							</p:tooltip>
						</p:column>
					</p:dataTable>
					<p:outputPanel rendered="#{mgrbean.selectObj[1] == 0 and mgrbean.ifPersonSummary==1}" 
						style="min-height: 200px;padding:6px;margin:10px 0px;line-height: 21px;border-radius: 3px;border:1px solid #FFE58F;background-color: #FEFBE6;">
						<!-- 填报说明 -->
						<h:outputText value="#{mgrbean.fillDesc}" escape="false" style="color:#DE7C05;"></h:outputText>
					</p:outputPanel>
				</p:tab>
				<p:tab title="场所建设" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==4}">
					<ui:include src="/webapp/heth/zzsb/zwSupportPlaceInfoView.xhtml"/>
				</p:tab>
				<p:tab title="仪器信息" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]!=4}">
					<p:dataTable var="instItm" value="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.zwInst}"
								 id="instDatatable"
								 emptyMessage="暂无记录！" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
								 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
								 rowsPerPageTemplate="#{mgrbean.perPageSize}"  lazy="true"
								 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
								 style="margin-top:5px;width:100%">
						<p:column headerText="仪器名称" style="padding-left: 3px;width:120px;">
							<p:outputLabel value="#{instItm.tdZwInstinfo.instName}" />
						</p:column>
						<p:column headerText="仪器型号" style="text-align:center;width:100px;">
							<p:outputLabel value="#{instItm.tdZwInstinfo.instModel}" />
						</p:column>
						<p:column headerText="仪器类型" style="text-align:center;width:120px;">
							<p:outputLabel value="#{null == instItm.tdZwInstinfo.fkByInstKindDetalId ? '' :
								instItm.tdZwInstinfo.fkByInstKindDetalId.codeName}"/>
						</p:column>
						<p:column headerText="仪器编号" style="text-align:center;width:120px;">
							<p:outputLabel value="#{instItm.tdZwInstinfo.instCode}" />
						</p:column>
						<p:column headerText="最近校验日期" style="text-align:center;width:120px">
							<p:outputLabel value="#{instItm.tdZwInstinfo.lastAcptDate}">
								<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
							</p:outputLabel>
						</p:column>
						<p:column headerText="校验周期（年）" style="text-align:center;width:120px">
							<p:outputLabel value="#{instItm.tdZwInstinfo.lastAcptCircleDot}"/>
						</p:column>
						<p:column headerText="是否外包" style="text-align:center;width:100px;" rendered="#{(zwOrgInfoSearchZzsbBean.selectObj[1] == 1 or zwOrgInfoSearchZzsbBean.selectObj[1] == 3) ? false:true}">
							<p:outputLabel value="是"
								rendered="#{instItm.tdZwInstinfo.outter == 1}" />
							<p:outputLabel value="否"
								rendered="#{instItm.tdZwInstinfo.outter == 0}" />
						</p:column>
						<p:column headerText="是否支持外检" style="text-align:center;width:6%" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 0}" >
							<p:outputLabel value="是" rendered="#{instItm.tdZwInstinfo.ifSuptOutChk == 1}"/>
							<p:outputLabel value="否" rendered="#{instItm.tdZwInstinfo.ifSuptOutChk == 0}"/>
						</p:column>
					</p:dataTable>
				</p:tab>

				<p:tab title="检查项目"
					rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1] == 0}">
					<p:panelGrid style="width:100%;margin-top: 5px;">
						<f:facet name="header">
							<p:row>
								<p:column style="text-align:center;height: 20px;width:80px;">
									<p:outputLabel value="序号" />
								</p:column>
								<p:column style="text-align:center;height: 20px;width:200px;">
									<p:outputLabel value="项目大类" />
								</p:column>
								<p:column style="text-align:center;height: 20px;">
									<p:outputLabel value="项目小类" />
								</p:column>
								<p:column style="text-align:center;height: 20px;">
									<p:outputLabel value="是否外包" />
								</p:column>
							</p:row>
						</f:facet>
						<c:forEach items="#{zwOrgInfoSearchZzsbBean.showItemList}" var="v">
							<p:row>
								<p:column style="text-align:center;height: 27px;">
									<p:outputLabel value="#{v[2]}" />
								</p:column>
								<p:column style="text-align:center;height: 27px;"
									rendered="#{v[0]}" rowspan="#{v[1]}">
									<p:outputLabel value="#{v[3]}" />
								</p:column>
								<p:column style="height: 27px;text-align:left;padding-left:3px;">
									<p:outputLabel value="#{v[4]}" />
                                </p:column>
								<p:column style="height: 27px;text-align:left;padding-left:3px;">
									<p:outputLabel value="是" rendered="#{v[5]}" />
									<p:outputLabel value="否" rendered="#{(v[5]==null or !v[5])}" />
                                </p:column>
							</p:row>
						</c:forEach>
					</p:panelGrid>
				</p:tab>
				<p:tab title="服务明细" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==0 and mgrbean.ifServiceDetails =='1'}">
					<ui:include src="/webapp/heth/zzsb/zwSerDetailsView.xhtml" />
				</p:tab>
				<p:tab title="外检车辆" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==0 and zwOrgInfoSearchZzsbBean.ifHethFj and null ne zwOrgInfoSearchZzsbBean.zwOrginfoEntity and null ne zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower and 1 == zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower}" >
					<ui:include src="/webapp/heth/zzsb/zwOutCarInfoView.xhtml" />
				</p:tab>
				<p:tab title="质量管理体系" id="annexQualInstitution"  rendered="#{zwOrgInfoSearchZzsbBean.ifQualAnnex != null and zwOrgInfoSearchZzsbBean.ifQualAnnex == 1 and zwOrgInfoSearchZzsbBean.topOfInstitutionList.size()>0 }">
					<c:forEach items="#{zwOrgInfoSearchZzsbBean.topOfInstitutionList}" var="top" >
						<p:fieldset legend="#{top.codeName}"  style="margin-top: 5px;margin-bottom: 5px;" >
							<p:panelGrid style="width:100%;" id="topInsPanel#{top.rid}" >
								<p:row>
									<p:column style="text-align:center;height: 27px;width:260px;padding-left:10px;" styleClass="ui-state-default">
										<p:outputLabel value="制度名称" />
									</p:column>
									<p:column style="text-align:center;width:200px;padding-left:10px;" styleClass="ui-state-default">
										<p:outputLabel value="有无" />
									</p:column>
									<p:column style="text-align:center;padding-left:10px;" styleClass="ui-state-default">
										<p:outputLabel value="附件" />
									</p:column>
								</p:row>
								<c:forEach items="#{zwOrgInfoSearchZzsbBean.qualAnnexMap.get(top.rid)}" var="v">
									<p:row>
										<p:column style="text-align:left;height: 27px;padding-left:10px;">
											<p:outputLabel value="#{v.fkByRuleId.codeName}"
														   rendered="#{v.fkByRuleId.rid != top.rid}" />
											<p:outputLabel value="#{v.otherRuleName}"
														   rendered="#{v.fkByRuleId.rid == top.rid}" />
										</p:column>
										<p:column style="text-align:center;">
											<p:outputLabel value="#{v.ifHas == 1 ? '有' : '无'}" />
										</p:column>
										<p:column style="text-align:left;padding-left:10px;">
											<p:commandLink value="查看" rendered="#{v.annexPath != '' and null != v.annexPath}"
														   process="@this"  onclick="window.open('/webFile/#{v.annexPath}')" />
										</p:column>
									</p:row>
								</c:forEach>
							</p:panelGrid>
						</p:fieldset>
					</c:forEach>
				</p:tab>
                <p:tab title="检测能力管理"  rendered="#{mgrbean.zwOrgType == 3}">
                    <ui:include src="chkAbility.xhtml"/>
                </p:tab>
				<p:tab title="资质附件" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]!=4}">
                    <ui:include src="zwJsffjgCommAnnexView.xhtml"/>
				</p:tab>
				<p:tab title="质量控制结果" rendered="#{(mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2 )and mgrbean.zkCheckRst == '1'}">
					<ui:include src="zkCheckRst.xhtml"/>
				</p:tab>
				<p:tab title="仪器设备配置信息" rendered="#{mgrbean.zwOrgType eq 4}">
					<ui:decorate template="zwSupportInst.xhtml"/>
				</p:tab>
				<p:tab title="能力建设信息" rendered="#{mgrbean.zwOrgType == 4}" >
					<ui:decorate template="capacityBuilding.xhtml" />
				</p:tab>
				<p:tab title="科技攻关能力" rendered="#{mgrbean.zwOrgType eq 4}">
					<ui:include src="supportTrcInfo.xhtml" />
				</p:tab>
				<p:tab title="科研课题信息" rendered="#{mgrbean.zwOrgType eq 4 and mgrbean.hasResearchAndAcademicInfo}">
					<ui:include src="zwResearchTopicsInfo.xhtml" />
				</p:tab>
				<p:tab title="学术培训信息" rendered="#{mgrbean.zwOrgType == 4 and mgrbean.hasResearchAndAcademicInfo}">
					<ui:include src="supportTrain.xhtml" />
				</p:tab>
			</p:tabView>
		</p:outputPanel>

		<!-- 设备检测 -->
		<p:dialog id="instSbJcDialog" header="设备检测情况"
			widgetVar="InstSbJcDialog" resizable="false" width="700" height="400"
			modal="true">
			<p:dataTable id="instSbJcMainList"
				value="#{zwOrgInfoSearchZzsbBean.sbJcMainList}" var="itm"
				rowIndexVar="rowInx" emptyMessage="没有数据">
				<p:column headerText="操作" style="width:120px">
					<p:commandLink value="不合格情况" process="@this"
						update=":tabView:viewForm:instSbJcSubList"
						action="#{zwOrgInfoSearchZzsbBean.addInitInstSbJcSub}"
						oncomplete="PF('InstSbJcSubDialog').show();">
						<f:setPropertyActionListener
							target="#{zwOrgInfoSearchZzsbBean.sbJcMain}" value="#{itm}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="检测日期">
					<p:outputLabel value="#{itm.jcDate}">
						<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
							locale="cn" />
					</p:outputLabel>
				</p:column>
				<p:column headerText="设备检测结果">
					<p:outputLabel value="#{itm.jcRst}" />
				</p:column>
			</p:dataTable>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" icon="ui-icon-close"
							onclick="PF('InstSbJcDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!-- 设备检测子表 -->
		<p:dialog id="instSbJcSubDialog" header="设备检测不合格情况"
			widgetVar="InstSbJcSubDialog" resizable="false" width="700"
			height="300" modal="true">
			<p:dataTable id="instSbJcSubList"
				value="#{zwOrgInfoSearchZzsbBean.sbJcSubList}" var="itm"
				rowIndexVar="rowInx" emptyMessage="没有数据">
				<p:column headerText="不合格指标">
					<p:outputLabel value="#{itm.descr}" />
				</p:column>
				<p:column headerText="不合格指标结果">
					<p:outputLabel value="#{itm.rst}" />
				</p:column>
				<p:column headerText="不合格指标结果单位">
					<p:outputLabel value="#{itm.rstUnit}" />
				</p:column>
			</p:dataTable>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" icon="ui-icon-close"
							onclick="PF('InstSbJcSubDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!-- 场所监测 -->
		<p:dialog id="instJcDialog" header="场所监测情况" widgetVar="InstJcDialog"
			resizable="false" width="700" height="400" modal="true">
			<p:dataTable id="instJcMainList"
				value="#{zwOrgInfoSearchZzsbBean.jcMainList}" var="itm"
				rowIndexVar="rowInx" emptyMessage="没有数据">
				<p:column headerText="操作" style="width:120px">
					<p:commandLink value="不合格情况" process="@this"
						update=":tabView:viewForm:instJcSubList"
						action="#{zwOrgInfoSearchZzsbBean.addInitInstJcSub}"
						oncomplete="PF('InstJcSubDialog').show();">
						<f:setPropertyActionListener
							target="#{zwOrgInfoSearchZzsbBean.jcMain}" value="#{itm}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="年度监测日期">
					<p:outputLabel value="#{itm.jcDate}">
						<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
							locale="cn" />
					</p:outputLabel>
				</p:column>
				<p:column headerText="场所监测结果">
					<p:outputLabel value="#{itm.jcRst}" />
				</p:column>
			</p:dataTable>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" icon="ui-icon-close"
							onclick="PF('InstJcDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!-- 场所监测子表 -->
		<p:dialog id="instJcSubDialog" header="场所不合格情况"
			widgetVar="InstJcSubDialog" resizable="false" width="700"
			height="300" modal="true">
			<p:dataTable id="instJcSubList"
				value="#{zwOrgInfoSearchZzsbBean.jcSubList}" var="itm"
				rowIndexVar="rowInx" emptyMessage="没有数据">
				<p:column headerText="不合格位置说明">
					<p:outputLabel value="#{itm.descr}" />
				</p:column>
				<p:column headerText="不合格位置结果">
					<p:outputLabel value="#{itm.rst}" />
				</p:column>
				<p:column headerText="不合格位置结果单位">
					<p:outputLabel value="#{itm.rstUnit}" />
				</p:column>
			</p:dataTable>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" icon="ui-icon-close"
							onclick="PF('InstJcSubDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>

		<!-- 体检情况 -->
		<p:dialog id="psnTjDialog" header="体检情况" widgetVar="PsnTjDialog"
			resizable="false" width="700" height="400" modal="true">
			<p:panelGrid id="psnTjGrid" style="width:100%;">
				<p:row>
					<p:column styleClass="ui-widget-header" colspan="2">入职前体检</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width: 200px;">体检日期：</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.firstTjMain.tjDate}">
							<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
								locale="cn" />
						</p:outputLabel>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">体检结论：</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.firstTjMain.tjConcl}" />
					</p:column>
				</p:row>
				<p:row>
					<p:column colspan="2">
						<p:dataTable id="firstTjSubList"
							value="#{zwOrgInfoSearchZzsbBean.firstTjSubList}" var="itm"
							rowIndexVar="rowInx" emptyMessage="没有数据">
							<p:column headerText="不合格指标">
								<p:outputLabel value="#{itm.descr}" />
							</p:column>
							<p:column headerText="不合格指标结果">
								<p:outputLabel value="#{itm.rst}" />
							</p:column>
							<p:column headerText="不合格指标结果单位">
								<p:outputLabel value="#{itm.rstUnit}" />
							</p:column>
						</p:dataTable>
					</p:column>
				</p:row>
				<p:row>
					<p:column styleClass="ui-widget-header" colspan="2">末次体检</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">体检日期：</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.lastTjMain.tjDate}">
							<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai"
								locale="cn" />
						</p:outputLabel>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">体检结论：</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:outputLabel value="#{zwOrgInfoSearchZzsbBean.lastTjMain.tjConcl}" />
					</p:column>
				</p:row>
				<p:row>
					<p:column colspan="2">
						<p:dataTable id="lastTjSubList"
							value="#{zwOrgInfoSearchZzsbBean.lastTjSubList}" var="itm"
							rowIndexVar="rowInx" emptyMessage="没有数据">
							<p:column headerText="不合格指标">
								<p:outputLabel value="#{itm.descr}" />
							</p:column>
							<p:column headerText="不合格指标结果">
								<p:outputLabel value="#{itm.rst}" />
							</p:column>
							<p:column headerText="不合格指标结果单位">
								<p:outputLabel value="#{itm.rstUnit}" />
							</p:column>
						</p:dataTable>
					</p:column>
				</p:row>
			</p:panelGrid>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="关闭" icon="ui-icon-close"
							onclick="PF('PsnTjDialog').hide();" type="button" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
		
		<!--人员维护 -->
			<p:dialog id="codeEditDialog" header="人员信息" widgetVar="CodeEditDialog" resizable="false" width="950" height="600" modal="true" styleClass="psnDiag">
				<p:outputPanel  style="width:905px; height: 40px;margin-left: 15px;position: fixed; margin-top: 0px; top: auto; z-index: 9999;" styleClass="zwx_toobar_42">
					<h:panelGrid columns="3" style="border-color:transparent;padding-bottom: 4px;padding-top: 1px;">
							<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
							<p:commandButton value="关闭" icon="ui-icon-close" id="codeCloseBtn" onclick="PF('CodeEditDialog').hide();" immediate="true"/>
					</h:panelGrid>
				</p:outputPanel>
				<p:outputPanel style="width: 100%;height: 40px;" />
				<p:outputPanel  style="width:97%;padding-left: 14px;" id="codeEditGrid">
					<ui:param name="psnInfoBase" value="#{zwOrgInfoSearchZzsbBean.psnInfoBase}"></ui:param>
					<ui:param name="psnInfoForm" value="tabView:viewForm"></ui:param>
					<ui:param name="ifEdit" value="true"></ui:param>
					<ui:include src="psnInfoValue.xhtml">
						<ui:param name="psnTypeNotNull" value="#{zwOrgInfoSearchZzsbBean.selectObj[1]!=3}"></ui:param>
					</ui:include>
				</p:outputPanel>
			</p:dialog>
			<ui:include src="psnInfoDiag.xhtml"></ui:include>
		<div id="label" style="position:absolute;z-index:100;display:none;width:150px;">
			<div id="triangle-up"></div>
			<div style="border-radius:5px;padding:10px;background:#4D4D4D;text-align:left;color:white;word-wrap: break-word;" id="descDiv"></div>
		</div>
		<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
	</h:form>
</ui:composition>



