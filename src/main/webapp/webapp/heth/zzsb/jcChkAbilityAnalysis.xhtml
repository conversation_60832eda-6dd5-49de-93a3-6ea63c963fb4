<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate_simple.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.JcChkAbilityAnalysisBean"-->
    <ui:param name="mgrbean" value="#{jcChkAbilityAnalysisBean}"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputStylesheet library="css" name="ui-cs.css"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function showStatus() {
                PF('StatusDialog').show();
            }

            function hideStatus() {
                PF('StatusDialog').hide();
            }

            function getDownloadFileClick() {
                document.getElementById("mainForm:downloadFileBtn").click();
            }
        </script>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px; height: 20px;">
                <h:outputText value="检测能力汇总"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" update="dataTable"
                                 onclick="zwx_loading_start();" oncomplete="zwx_loading_stop();"/>
                <p:menuButton value="导出" id="exportButton">
                    <c:forEach items="#{mgrbean.exportTypeList}" var="itm" varStatus="varStatus">
                        <p:menuitem value="#{itm.codeName}" icon="ui-icon-document"
                                    process="@this,mainGrid" action="#{mgrbean.exportBefore(itm.extendS2)}">
                        </p:menuitem>
                    </c:forEach>
                </p:menuButton>
                <p:commandButton style="display: none;" id="downloadFileBtn" icon="ui-icon-document" ajax="false"
                                 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                    <p:fileDownload value="#{mgrbean.export()}"/>
                </p:commandButton>
                <p:inputText style="visibility: hidden;width: 0;"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column styleClass="cs-scl-first">
                <h:outputLabel value="地区："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w">
                <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" id="searchZone"
                                       zoneCode="#{mgrbean.searchZoneGb}"
                                       zoneName="#{mgrbean.searchZoneName}"
                                       zonePaddingLeft="0" onchange="onSearchNodeSelect()"/>
                <p:remoteCommand name="onSearchNodeSelect" action="#{mgrbean.clearUnit}"
                                 process="@this,searchZone" update="unitPanel"/>
            </p:column>
            <p:column styleClass="cs-scl-w">
                <h:outputLabel value="职业卫生技术服务机构："/>
            </p:column>
            <p:column style="padding-left: 9px !important;" styleClass="cs-scv-w" colspan="3">
                <p:outputPanel style="display: flex;align-items: center;" id="unitPanel">
                    <p:inputText id="unitName" value="#{mgrbean.searchOrgName}" readonly="true"
                                 style="width: 180px;cursor: pointer; margin-left: 0;"
                                 onclick="document.getElementById('mainForm:selUnitLink').click();"/>
                    <p:commandLink styleClass="ui-icon ui-icon-search" id="selUnitLink"
                                   style="position: relative;left: -20px !important;"
                                   action="#{mgrbean.selUnitAction}" process="@this">
                        <p:ajax event="dialogReturn" listener="#{mgrbean.onSelectUnitAction}"
                                resetValues="true" process="@this" update="unitPanel"/>
                    </p:commandLink>
                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                   style="position: relative;left: -13px !important;"
                                   process="@this" update="unitPanel" action="#{mgrbean.clearUnit}"/>
                </p:outputPanel>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="检测项目" style="width: 350px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="检测标准（方法）名称、编号（含年号）及条款号">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="限制说明" style="width: 300px;">
            <h:outputText id="limitationDescription" value="#{itm[3]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="limitationDescription" style="max-width:300px;">
                <p:outputLabel styleClass="cs-break-word" value="#{itm[3]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="方法验证、确认或论证" style="width: 180px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="是否通过CMA或CNAS" style="width: 150px;text-align: center;">
            <h:outputText value="是" rendered="#{itm[6] eq '1' or itm[7] eq '1'}"/>
            <h:outputText value="否" rendered="#{itm[6] ne '1' and itm[7] ne '1'}"/>
        </p:column>
    </ui:define>
</ui:composition>