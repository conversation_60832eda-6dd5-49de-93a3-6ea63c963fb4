<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:fn="http://java.sun.com/jsp/jstl/functions"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
<style>
    #mainForm\:tabView\:serDetailDatatable table.ui-selectmanycheckbox td label{
        white-space: pre-line;
        width: 250px;
    }


</style>
    <h:inputHidden id="jcItemRid" value="#{mgrbean.jcItemRid}"/>
    <p:panelGrid style="width:100%;margin-top: 8px;" id="serDetailDatatable">
        <f:facet name="header">
            <p:row>
                <p:column style="text-align:center;height: 20px;width:21%;">
                    <p:outputLabel value="服务类别" />
                </p:column>
                <p:column style="text-align:center;height: 20px;width:200px;">
                    <p:outputLabel value="服务项目" />
                </p:column>
                <p:column style="text-align:center;height: 20px;width:100px;" rendered="#{null ne mgrbean.zwOrginfoEntity and null ne mgrbean.zwOrginfoEntity.outWorkPower and 1 == mgrbean.zwOrginfoEntity.outWorkPower}" >
                    <p:outputLabel value="支持外检" />
                </p:column>
            </p:row>
        </f:facet>
        <p:row rendered="#{mgrbean.jkItemRowList.size()==0}">
            <p:column style="text-align:left;height: 27px;" colspan="2" rendered="#{null == mgrbean.zwOrginfoEntity or null == mgrbean.zwOrginfoEntity.outWorkPower and 1 != mgrbean.zwOrginfoEntity.outWorkPower}">
                <p:outputLabel value="没有您要找的记录！" />
            </p:column>
            <p:column style="text-align:left;height: 27px;" colspan="3" rendered="#{null ne mgrbean.zwOrginfoEntity and null ne mgrbean.zwOrginfoEntity.outWorkPower and 1 == mgrbean.zwOrginfoEntity.outWorkPower}">
                <p:outputLabel value="没有您要找的记录！" />
            </p:column>
        </p:row>
        <c:forEach items="#{mgrbean.jkItemRowList}" var="serDetailItm" varStatus="status">
            <p:row rendered="#{mgrbean.zwOrginfoEntity.state==0}">
                <c:if test="#{status.index==serDetailItm.rowspanStart}">
                <p:column style="text-align:left;height: 27px;" rowspan="#{serDetailItm.rowspan}">
                    <p:selectManyCheckbox value="#{serDetailItm.selectedJcItem}"  style="#{mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0?'pointer-events:none;':''}">
                        <p:ajax event="change" listener="#{mgrbean.changeJcItem}"
                                process="@this,jcItemRid" update="serDetailDatatable" onstart="document.getElementById('mainForm:tabView:jcItemRid').value = #{serDetailItm.jcItem.rid}"/>
                        <f:selectItem itemLabel="#{serDetailItm.jcItem.codeName}" itemValue="#{serDetailItm.jcItem.codeNo}" />
                    </p:selectManyCheckbox>
                </p:column>
                </c:if>
                <p:column style="text-align:left;height:27px;width: 50px;" >
                    <p:selectManyCheckbox  value="#{serDetailItm.selectedSerItmDetail}"  style="#{mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0?'pointer-events:none;':''}">
                        <p:ajax event="change"  listener="#{mgrbean.changeSerItmDetail}"
                                process="@this" update="serDetailDatatable" />
                        <f:selectItem  itemLabel="#{serDetailItm.serItem.codeName}"  itemValue="#{serDetailItm.serItem.rid}" />
                    </p:selectManyCheckbox>
                </p:column>
                <p:column style="text-align:left;height:27px;width: 50px;" rendered="#{null ne mgrbean.zwOrginfoEntity and null ne mgrbean.zwOrginfoEntity.outWorkPower and 1 == mgrbean.zwOrginfoEntity.outWorkPower}" >
                    <p:selectManyCheckbox value="#{serDetailItm.selectedIfExterInspect}"  style="#{mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0?'pointer-events:none;':''}" rendered="#{serDetailItm.disabled == false}">
                        <p:ajax event="change"  process="@this" />
                        <f:selectItem itemLabel=""  itemValue="#{serDetailItm.serItem.rid}" />
                    </p:selectManyCheckbox>
                    <p:selectManyCheckbox value="#{serDetailItm.selectedIfExterInspect}" rendered="#{null == serDetailItm.disabled or serDetailItm.disabled == true}">
                        <f:selectItem itemLabel=""  itemValue="#{serDetailItm.serItem.rid}" itemDisabled="true"/>
                    </p:selectManyCheckbox>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.zwOrginfoEntity.state==1}">
                <c:if test="#{status.index==serDetailItm.rowspanStart}">
                    <p:column style="text-align:left;height: 27px;" rowspan="#{serDetailItm.rowspan}">
                        <p:outputLabel value="#{serDetailItm.jcItem.codeName}" />
                    </p:column>
                </c:if>
                <p:column style="text-align:left;height:27px;width: 50px;" >
                    <p:outputLabel value="#{serDetailItm.serItem.codeName}" />
                </p:column>
                <p:column style="text-align:left;height:27px;width: 50px;" rendered="#{null ne mgrbean.zwOrginfoEntity and null ne mgrbean.zwOrginfoEntity.outWorkPower and 1 == mgrbean.zwOrginfoEntity.outWorkPower}" >
                    <p:outputLabel value="是" rendered="#{serDetailItm.ifExterInspect=='1'}" />
                    <p:outputLabel value="否" rendered="#{serDetailItm.ifExterInspect=='0'}" />
                </p:column>
            </p:row>
        </c:forEach>
    </p:panelGrid>
</ui:composition>
