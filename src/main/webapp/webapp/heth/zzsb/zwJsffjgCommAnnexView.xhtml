<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">

    <p:outputPanel>
        <table width="100%">
            <tr>
                <!-- 单位法人证明材料（营业执照） -->
                <td width="50%" style="vertical-align: top">
                    <p:fieldset legend="单位法人证明材料（营业执照）" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                        <p:dataTable value="#{zwOrgInfoSearchZzsbBean.zwAnnexList1}" var="itm1"
                                     rowIndexVar="var1" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var1+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm1.fileName}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm1.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>
                </td>

                <td width="50%" style="vertical-align: top">
                    <!-- 医疗机构执业许可证 -->
                    <p:fieldset legend="医疗机构执业许可证" toggleable="true" toggleSpeed="500"
                                style="margin-top: 5px;margin-bottom: 5px;" rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==0 or zwOrgInfoSearchZzsbBean.selectObj[1]==2}">
                        <p:dataTable id="zwAnnexList2" value="#{zwOrgInfoSearchZzsbBean.zwAnnexList2}" var="itm2"
                                     rowIndexVar="var2" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:40px;text-align: center">
                                <h:outputLabel value="#{var2+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:180px">
                                <h:outputLabel value="#{itm2.fileName}" />
                            </p:column>
                            <p:column headerText="注册地点" style="width:100px;">
                                <h:outputLabel value="#{itm2.fkByRegZoneId.zoneName}" />
                            </p:column>
                            <p:column headerText="发证单位" style="width:150px;">
                                <h:outputLabel value="#{itm2.unitName}"/>
                            </p:column>
                            <p:column headerText="发证单位级别" style="width:80px;text-align: center;">
                                <h:outputLabel value="省级" rendered="#{itm2.unitLevel == 2}" />
                                <h:outputLabel value="市级" rendered="#{itm2.unitLevel == 3}" />
                                <h:outputLabel value="区县级" rendered="#{itm2.unitLevel == 4}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm2.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>

                    <!-- 放射卫生服务机构批准证书 -->
                    <p:fieldset legend="放射卫生技术服务机构批准证书" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                                rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==1}">
                        <p:dataTable id="zwAnnexList8" value="#{zwOrgInfoSearchZzsbBean.zwAnnexList8}" var="itm8"
                                     rowIndexVar="var8" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var8+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm8.fileName}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm8.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>

                    <!-- 职业卫生技术服务机构资质证书 -->
                    <p:fieldset legend="职业卫生技术服务机构资质证书" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                                rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==3}">
                        <p:dataTable id="zwAnnexList9" value="#{zwOrgInfoSearchZzsbBean.zwAnnexList9}"
                                     var="itm9" rowIndexVar="var9" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var9+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm9.fileName}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm9.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>
                </td>
            </tr>

            <tr>
                <!-- 放射诊疗许可证 -->
                <td width="50%" style="vertical-align: top;display: #{(zwOrgInfoSearchZzsbBean.selectObj[1]==3 or zwOrgInfoSearchZzsbBean.selectObj[1]==1)?'none':''};">
                    <p:fieldset legend="放射诊疗许可证" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                        <p:dataTable id="zwAnnexList3" value="#{zwOrgInfoSearchZzsbBean.zwAnnexList3}"
                                     var="itm3" rowIndexVar="var3" emptyMessage="暂无附件信息"
                                     style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var3+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm3.fileName}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm3.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>
                </td>

                <td width="50%" style="vertical-align: top;">
                    <!-- 职业健康检查机构备案回执 -->
                    <p:fieldset legend="职业健康检查机构备案回执" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                                rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==0}">
                        <p:dataTable id="zwAnnexList6" value="#{zwOrgInfoSearchZzsbBean.zwAnnexList6}"
                                     var="itm6" rowIndexVar="var6" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var6+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm6.fileName}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm6.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>

                    <!-- 职业病诊断机构批准证书 -->
                    <p:fieldset legend="职业病诊断机构备案回执" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                                rendered="#{zwOrgInfoSearchZzsbBean.selectObj[1]==2}">
                        <p:dataTable id="zwAnnexList7" value="#{zwOrgInfoSearchZzsbBean.zwAnnexList7}"
                                     var="itm7" rowIndexVar="var7" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var7+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm7.fileName}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm7.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>
                </td>
            </tr>
            <tr>
                <!-- 委托协议和委托项目明细 -->
                <td width="50%" style="vertical-align: top;display: #{mgrbean.zwOrgType==0?'':'none'};">
                    <p:fieldset legend="委托协议和委托项目明细" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                        <p:dataTable id="zwAnnexList20" value="#{zwOrgInfoSearchZzsbBean.zwAnnexList20}"
                                     var="itm20" rowIndexVar="var20" emptyMessage="暂无附件信息"
                                     style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var20+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm20.fileName}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm20.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>
                </td>
                <!-- 房屋所有权证或租赁合同 -->
                <td width="50%" style="vertical-align: top;display: #{mgrbean.ifHaveHouseAnnex==1?'':'none'};">
                    <p:fieldset legend="房屋所有权证或租赁合同" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                        <p:dataTable id="zwAnnexList4" value="#{zwOrgInfoSearchZzsbBean.zwAnnexList4}"
                                     var="itm4" rowIndexVar="var4" emptyMessage="暂无附件信息"
                                     style="margin-top: 5px;">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var4+1}" />
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm4.fileName}" />
                            </p:column>
                            <p:column headerText="操作">
                                <p:commandLink value="查看"
                                               process="@this"  onclick="window.open('/webFile/#{itm4.filePath}')" />
                            </p:column>
                        </p:dataTable>
                    </p:fieldset>
                </td>
            </tr>
        </table>
    </p:outputPanel>
</ui:composition>