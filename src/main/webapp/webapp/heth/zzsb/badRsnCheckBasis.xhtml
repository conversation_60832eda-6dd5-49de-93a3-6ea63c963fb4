<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" xmlns:h="http://java.sun.com/jsf/html" xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>

    </h:head>

    <h:body  onload="document.getElementById('codeForm:pym').focus();">
    <title>检测依据</title>
        <h:outputStylesheet name="css/default.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <h:outputStylesheet name="css/ui-tabs.css"/>
        <h:form id="codeForm">
            <table width="100%">
                <tr>
                    <td style="text-align: left;padding-left: 3px">
                        <h:panelGrid columns="5" id="searchPanel">
                            <p:outputLabel value="法律法规名称：" styleClass="zwx_dialog_font" />
                            <p:inputText id="pym" value="#{lowRadioSelectZzsbBean.searchNamOrPy}" style="width: 180px;" maxlength="20">
                                <p:ajax event="keyup" update="selectedIndusTable" process="@this,searchPanel" listener="#{lowRadioSelectZzsbBean.searchAction}"/>
                            </p:inputText>
                        </h:panelGrid>
                    </td>
                </tr>
            </table>

            <p:dataTable var="itm"   value="#{lowRadioSelectZzsbBean.displayList}" id="selectedIndusTable"
                         paginator="true" rows="10"
                         paginatorPosition="bottom"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                         rowsPerPageTemplate="#{mgrbean.pageSize}" lazy="true" emptyMessage="没有您要找的记录！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"  rendered="#{dataTable==null}"
            >
                <p:column headerText="操作" style="width:50px;text-align:center">
                    <p:commandLink value="选择" action="#{lowRadioSelectZzsbBean.selectAction}" process="@this">
                        <f:setPropertyActionListener value="#{itm}" target="#{lowRadioSelectZzsbBean.selectPro}"/>
                    </p:commandLink>
                </p:column>
                <p:column headerText="标准号" style="width: 180px;text-align: center">
                    <h:outputText value="#{itm.lawCode}"  />
                </p:column>

                <p:column headerText="法律法规名称" style="padding-left: 3px;">
                    <h:outputText value="#{itm.lawName}"  />
                </p:column>
            </p:dataTable>

        </h:form>
    </h:body>
</f:view>
</html>
