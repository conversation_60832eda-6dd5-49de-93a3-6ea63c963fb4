<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--人员选择弹出框-->
    <p:dialog id="supportResearchPsnSelDialog" header="人员选择" widgetVar="SupportResearchPsnSelDialog"
              resizable="false" width="900" height="460" modal="true">
        <p:outputPanel styleClass="zwx_toobar_42" style="display: flex;">
            <h:panelGrid columns="6">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="确定" icon="ui-icon-check"
                                 action="#{mgrbean.saveSupportResearchPsnSelectAction}"/>
                <p:commandButton value="取消" icon="ui-icon-close" process="@this"
                                 oncomplete="PF('SupportResearchPsnSelDialog').hide();"/>
            </h:panelGrid>
        </p:outputPanel>
        <div style="display: flex;align-items: center;padding: 10px 0;">
            <h:outputText value="姓名：" style="padding-left: 10px;"/>
            <p:inputText value="#{mgrbean.supportResearchPsnSelVO.searchPsnName}" style="width: 180px;" maxlength="50"
                         id="searchPsnName">
                <p:ajax event="keyup" process="@this" update=":mainForm:tabView:psnDataTable"
                        listener="#{mgrbean.searchSupportResearchPsnSelectAction}"/>
            </p:inputText>
        </div>
        <p:dataTable var="psn" value="#{mgrbean.supportResearchPsnSelVO.allIncumbencyPsnList}"
                     id="psnDataTable" paginator="true" rows="10" rowsPerPageTemplate="#{'10,20,50'}"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     emptyMessage="没有您要找的记录！" paginatorPosition="bottom" rowIndexVar="R"
                     selection="#{mgrbean.supportResearchPsnSelVO.selectedPsnList}"
                     rowKey="#{psn[0]}" rowSelectMode="add">
            <p:ajax event="page"/>
            <p:ajax event="rowSelect"
                    listener="#{mgrbean.rowSelectSupportResearchPsnSelectListener}" immediate="true"/>
            <p:ajax event="rowUnselect"
                    listener="#{mgrbean.rowUnselectSupportResearchPsnSelectListener}" immediate="true"/>
            <p:ajax event="rowSelectCheckbox"
                    listener="#{mgrbean.rowSelectSupportResearchPsnSelectListener}" immediate="true"/>
            <p:ajax event="rowUnselectCheckbox"
                    listener="#{mgrbean.rowUnselectSupportResearchPsnSelectListener}" immediate="true"/>
            <p:ajax event="toggleSelect"
                    listener="#{mgrbean.toggleSelectSupportResearchPsnSelectListener}" immediate="true"/>
            <!-- 固定列宽 所有列都按百分比设置宽度 -->
            <p:column selectionMode="multiple" style="width: 5%;text-align: center;"/>
            <p:column headerText="姓名" style="width: 25%;text-align: center;">
                <h:outputText value="#{psn[1]}"/>
            </p:column>
            <p:column headerText="性别" style="width: 10%;text-align: center;">
                <h:outputText value="#{psn[2]}"/>
            </p:column>
            <p:column headerText="出生年月" style="width: 15%;text-align: center;">
                <h:outputText value="#{psn[3]}"/>
            </p:column>
            <p:column headerText="职称" style="width: 45%;text-align: center;">
                <h:outputText value="#{psn[4]}"/>
            </p:column>
        </p:dataTable>
    </p:dialog>
    <!--材料上传弹出框-->
    <p:dialog header="材料上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true">
        <table>
            <tr>
                <td style="text-align: right;">
                    <p:outputLabel
                            value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                            style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                </td>
            </tr>
            <tr>
                <td style="position: relative;top: -23px;">
                    <p:fileUpload
                            requiredMessage="请选择要上传的文件！" label="文件选择"
                            fileUploadListener="#{mgrbean.uploadSupportResearchFileAction}"
                            invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，请重新上传！"
                            style="width:600px;" previewWidth="120" cancelLabel="取消" update="@this"
                            fileLimit="1" fileLimitMessage="只能选择一个文件！" uploadLabel="上传"
                            dragDropSupport="true" mode="advanced" sizeLimit="10485760"
                            invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                            allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
                </td>
            </tr>
        </table>
    </p:dialog>
    <!--论文信息弹出框-->
    <p:dialog id="supportResearchThesisDialog" header="论文信息" widgetVar="SupportResearchThesisDialog"
              resizable="false" width="900" height="230" modal="true">
        <p:panelGrid id="supportResearchThesisDialogPanel" style="width: 100%">
            <p:row>
                <p:column style="text-align:right;height: 35px;width: 160px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="作者："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;display: flex;align-items: center;">
                        <p:inputText id="psnName1" style="width: 200px;cursor: pointer;" readonly="true"
                                     value="#{mgrbean.supportThesis.psnSelVO.selPsnName}"
                                     onclick="document.getElementById('mainForm:tabView:supportResearchPsnSelectLink1').click();"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="supportResearchPsnSelectLink1"
                                       style="position: relative;left: -20px !important;"
                                       process="@this" action="#{mgrbean.openSupportResearchPsnSelectAction}">
                            <f:setPropertyActionListener value="mainForm:tabView:psnName1"
                                                         target="#{mgrbean.psnInputId}"/>
                            <f:setPropertyActionListener value="#{mgrbean.supportThesis.psnSelVO}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -15px;"
                                       action="#{mgrbean.emptySupportResearchPsnSelectAction}"
                                       update="psnName1" process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.supportThesis.psnSelVO}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                    </p:outputPanel>
                </p:column>
                <p:column style="text-align:right;width: 160px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="刊物类别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;width: 260px;">
                    <p:selectOneMenu value="#{mgrbean.supportThesis.fkByPublicationId.rid}"
                                     style="width: 200px;">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{mgrbean.publicationList}" var="itm"
                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="论文名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" colspan="3">
                    <p:outputPanel style="padding-left: 3px;">
                        <p:inputTextarea style="width: 627px;height: 50px;" maxlength="200"
                                         value="#{mgrbean.supportThesis.thesisName}"/>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="刊物名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" colspan="3">
                    <p:outputPanel style="padding-left: 3px;">
                        <p:inputTextarea style="width: 627px;height: 50px;" maxlength="200"
                                         value="#{mgrbean.supportThesis.publicationName}"/>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;width: 160px;">
                    <h:outputText value="影响因子："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;">
                        <p:inputText style="width: 200px;" maxlength="7"
                                     onkeyup="SYSTEM.verifyNum(this, 3, 3)" onblur="SYSTEM.verifyNum(this, 3, 3)"
                                     value="#{mgrbean.supportThesis.impactFactors}"/>
                    </p:outputPanel>
                </p:column>
                <p:column style="text-align:right;width: 160px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="支撑材料："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;" id="researchFilePanel1">
                        <p:commandButton value="上传" update="fileDialog"
                                         oncomplete="PF('FileDialog').show();"
                                         rendered="#{empty mgrbean.supportThesis.supportFilePath}">
                            <f:setPropertyActionListener value="1" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.supportThesis.supportFilePath}')"
                                         rendered="#{not empty mgrbean.supportThesis.supportFilePath}"/>
                        <p:spacer width="5" rendered="#{not empty mgrbean.supportThesis.supportFilePath}"/>
                        <p:commandButton value="删除" action="#{mgrbean.deleteSupportResearchFileAction}"
                                         rendered="#{not empty mgrbean.supportThesis.supportFilePath}">
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            <f:setPropertyActionListener value="1" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-disk" action="#{mgrbean.saveSupportThesisAction}"
                                     process="@this,:mainForm:tabView:supportResearchThesisDialogPanel"/>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" type="button"
                                     onclick="PF('SupportResearchThesisDialog').hide();"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!--专著情况弹出框-->
    <p:dialog id="supportResearchMonographDialog" header="专著情况" widgetVar="SupportResearchMonographDialog"
              resizable="false" width="900" height="230" modal="true">
        <p:panelGrid id="supportResearchMonographDialogPanel" style="width: 100%">
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="论著名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" colspan="3">
                    <p:outputPanel style="padding-left: 3px;">
                        <p:inputTextarea style="width: 636px;height: 50px;" maxlength="200"
                                         value="#{mgrbean.supportMomograph.momographName}"/>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="出版社名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" colspan="3">
                    <p:outputPanel style="padding-left: 3px;">
                        <p:inputTextarea style="width: 636px;height: 50px;" maxlength="200"
                                         value="#{mgrbean.supportMomograph.pressName}"/>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;width: 160px;">
                    <h:outputText value="主编姓名："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;display: flex;align-items: center;">
                        <p:inputText id="psnName2" style="width: 200px;cursor: pointer;" readonly="true"
                                     value="#{mgrbean.supportMomograph.psnSelVO1.selPsnName}"
                                     onclick="document.getElementById('mainForm:tabView:supportResearchPsnSelectLink2').click();"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="supportResearchPsnSelectLink2"
                                       style="position: relative;left: -20px !important;"
                                       process="@this" action="#{mgrbean.openSupportResearchPsnSelectAction}">
                            <f:setPropertyActionListener value="mainForm:tabView:psnName2"
                                                         target="#{mgrbean.psnInputId}"/>
                            <f:setPropertyActionListener value="#{mgrbean.supportMomograph.psnSelVO1}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -15px;"
                                       action="#{mgrbean.emptySupportResearchPsnSelectAction}"
                                       update="psnName2" process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.supportMomograph.psnSelVO1}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                    </p:outputPanel>
                </p:column>
                <p:column style="text-align:right;height: 35px;width: 160px;">
                    <h:outputText value="副主编姓名："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;display: flex;align-items: center;">
                        <p:inputText id="psnName3" style="width: 200px;cursor: pointer;" readonly="true"
                                     value="#{mgrbean.supportMomograph.psnSelVO2.selPsnName}"
                                     onclick="document.getElementById('mainForm:tabView:supportResearchPsnSelectLink3').click();"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="supportResearchPsnSelectLink3"
                                       style="position: relative;left: -20px !important;"
                                       process="@this" action="#{mgrbean.openSupportResearchPsnSelectAction}">
                            <f:setPropertyActionListener value="mainForm:tabView:psnName3"
                                                         target="#{mgrbean.psnInputId}"/>
                            <f:setPropertyActionListener value="#{mgrbean.supportMomograph.psnSelVO2}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -15px;"
                                       action="#{mgrbean.emptySupportResearchPsnSelectAction}"
                                       update="psnName3" process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.supportMomograph.psnSelVO2}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;width: 160px;">
                    <h:outputText value="参编姓名："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;display: flex;align-items: center;">
                        <p:inputText id="psnName4" style="width: 200px;cursor: pointer;" readonly="true"
                                     value="#{mgrbean.supportMomograph.psnSelVO3.selPsnName}"
                                     onclick="document.getElementById('mainForm:tabView:supportResearchPsnSelectLink4').click();"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="supportResearchPsnSelectLink4"
                                       style="position: relative;left: -20px !important;"
                                       process="@this" action="#{mgrbean.openSupportResearchPsnSelectAction}">
                            <f:setPropertyActionListener value="mainForm:tabView:psnName4"
                                                         target="#{mgrbean.psnInputId}"/>
                            <f:setPropertyActionListener value="#{mgrbean.supportMomograph.psnSelVO3}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -15px;"
                                       action="#{mgrbean.emptySupportResearchPsnSelectAction}"
                                       update="psnName4" process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.supportMomograph.psnSelVO3}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                    </p:outputPanel>
                </p:column>
                <p:column style="text-align:right;width: 160px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="支撑材料："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;" id="researchFilePanel2">
                        <p:commandButton value="上传" update="fileDialog"
                                         oncomplete="PF('FileDialog').show();"
                                         rendered="#{empty mgrbean.supportMomograph.supportFilePath}">
                            <f:setPropertyActionListener value="2" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.supportMomograph.supportFilePath}')"
                                         rendered="#{not empty mgrbean.supportMomograph.supportFilePath}"/>
                        <p:spacer width="5" rendered="#{not empty mgrbean.supportMomograph.supportFilePath}"/>
                        <p:commandButton value="删除" action="#{mgrbean.deleteSupportResearchFileAction}"
                                         rendered="#{not empty mgrbean.supportMomograph.supportFilePath}">
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            <f:setPropertyActionListener value="2" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-disk" action="#{mgrbean.saveSupportMonographAction}"
                                     process="@this,:mainForm:tabView:supportResearchMonographDialogPanel"/>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" type="button"
                                     onclick="PF('SupportResearchMonographDialog').hide();"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!--科研成果情况-专利-添加-->
    <p:dialog id="addPatentInfoDialog" header="专利信息" widgetVar="AddPatentInfoDialog"
              resizable="false" width="650" height="235" modal="true">
        <p:panelGrid id="addPatentInfoDialogPanel" style="width: 100%">
            <p:row>
                <p:column style="text-align:right;height: 35px;width: 130px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="完成人："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputPanel style="padding-left: 3px;display: flex;align-items: center;">
                        <p:inputText id="psnName5" style="width: 400px;cursor: pointer;" readonly="true"
                                     value="#{mgrbean.supportPositive.psnSelVO.selPsnName}"
                                     onclick="document.getElementById('mainForm:tabView:supportResearchPsnSelectLink5').click();"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="supportResearchPsnSelectLink5"
                                       style="position: relative;left: -20px !important;"
                                       process="@this" action="#{mgrbean.openSupportResearchPsnSelectAction}">
                            <f:setPropertyActionListener value="mainForm:tabView:psnName5"
                                                         target="#{mgrbean.psnInputId}"/>
                            <f:setPropertyActionListener value="#{mgrbean.supportPositive.psnSelVO}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -15px;"
                                       action="#{mgrbean.emptySupportResearchPsnSelectAction}"
                                       update="psnName5" process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.supportPositive.psnSelVO}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="专利名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea style="width: 400px;resize: none;height:50px;" maxlength="200" autoResize="false"
                                     value="#{mgrbean.supportPositive.patentName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="专利类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" colspan="3">
                    <p:selectOneMenu value="#{mgrbean.supportPositive.fkByPatentId.rid}"
                                     style="width: 208px;">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{mgrbean.patentTypelist}" var="itm"
                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                        <p:ajax event="change" listener="#{mgrbean.changePatentType}"
                                process="@this,addPatentInfoDialogPanel" update="addPatentInfoDialogPanel"/>
                    </p:selectOneMenu>
                    <p:inputText value="#{mgrbean.supportPositive.otherPatentType}"
                                 style="width: 180px; margin-left: 10px;vertical-align: top;margin-top: 2px;" maxlength="50"
                                 rendered="#{mgrbean.isShowPatentType}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="专利号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" colspan="3">
                    <p:inputText style="width: 400px;" maxlength="100"
                                 value="#{mgrbean.supportPositive.patentNo}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="支撑材料："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;" id="researchFilePanel3">
                        <p:commandButton value="上传" update="fileDialog"
                                         action="#{mgrbean.openSupportResearchFileUploadAction}"
                                         rendered="#{mgrbean.supportPositive.supportFilePath == null}">
                            <f:setPropertyActionListener value="3" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.supportPositive.supportFilePath}')"
                                         rendered="#{mgrbean.supportPositive.supportFilePath != null}"/>
                        <p:spacer width="5" rendered="#{mgrbean.supportPositive.supportFilePath != null}"/>
                        <p:commandButton value="删除" onclick="PF('DeleteSupportDialog').show();"
                                         rendered="#{mgrbean.supportPositive.supportFilePath != null}">
                            <f:setPropertyActionListener value="3" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-disk" action="#{mgrbean.savePatentInfoAction}"
                                     process="@this,:mainForm:tabView:addPatentInfoDialogPanel"/>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" type="button"
                                     onclick="PF('AddPatentInfoDialog').hide();"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!--科研成果情况-标准-添加-->
    <p:dialog id="addStandardInfoDialog" header="标准信息" widgetVar="AddStandardInfoDialog"
              resizable="false" width="650" height="235" modal="true">
        <p:panelGrid id="addStandardInfoDialogPanel" style="width: 100%">
            <p:row>
                <p:column style="text-align:right;height: 35px;width: 130px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="标准主要起草人："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputPanel style="padding-left: 3px;display: flex;align-items: center;">
                        <p:inputText id="psnName6" style="width: 400px;cursor: pointer;" readonly="true"
                                     value="#{mgrbean.supportPositive.psnSelVO.selPsnName}"
                                     onclick="document.getElementById('mainForm:tabView:supportResearchPsnSelectLink6').click();"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="supportResearchPsnSelectLink6"
                                       style="position: relative;left: -20px !important;"
                                       process="@this" action="#{mgrbean.openSupportResearchPsnSelectAction}">
                            <f:setPropertyActionListener value="mainForm:tabView:psnName6"
                                                         target="#{mgrbean.psnInputId}"/>
                            <f:setPropertyActionListener value="#{mgrbean.supportPositive.psnSelVO}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -15px;"
                                       action="#{mgrbean.emptySupportResearchPsnSelectAction}"
                                       update="psnName6" process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.supportPositive.psnSelVO}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="标准名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;">
                    <p:inputTextarea style="width: 400px;resize: none;height:50px;" maxlength="200" autoResize="false"
                                     value="#{mgrbean.supportPositive.patentName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="标准编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" colspan="3">
                    <p:inputText style="width: 400px;" maxlength="100"
                                 value="#{mgrbean.supportPositive.patentNo}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="标准级别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" colspan="3">
                    <p:selectOneMenu value="#{mgrbean.supportPositive.fkByStandardId.rid}"
                                     style="width: 208px;">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{mgrbean.standardTypelist}" var="itm"
                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="支撑材料："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;">
                    <p:outputPanel style="padding-left: 3px;" id="researchFilePanel4">
                        <p:commandButton value="上传" update="fileDialog"
                                         action="#{mgrbean.openSupportResearchFileUploadAction}"
                                         rendered="#{mgrbean.supportPositive.supportFilePath == null}">
                            <f:setPropertyActionListener value="4" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.supportPositive.supportFilePath}')"
                                         rendered="#{mgrbean.supportPositive.supportFilePath != null}"/>
                        <p:spacer width="5" rendered="#{mgrbean.supportPositive.supportFilePath != null}"/>
                        <p:commandButton value="删除" onclick="PF('DeleteSupportDialog').show();"
                                         rendered="#{mgrbean.supportPositive.supportFilePath != null}">
                            <f:setPropertyActionListener value="4" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-disk" action="#{mgrbean.saveStandardInfoAction}"
                                     process="@this,:mainForm:tabView:addStandardInfoDialogPanel"/>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" type="button"
                                     onclick="PF('AddStandardInfoDialog').hide();"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!--科研成果情况-课题情况-添加-->
    <p:dialog id="supportTopicDialog" header="课题信息" widgetVar="SupportTopicDialog"
              resizable="false" width="650" height="#{mgrbean.supportTopic.topicType == 3 ? 250 : 210}" modal="true">
        <p:panelGrid id="supportTopicDialogPanel" style="width: 100%">
            <p:row>
                <p:column style="text-align:right;height: 35px;width: 130px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="责任人："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" colspan="3">
                    <p:outputPanel style="padding-left: 3px;display: flex;align-items: center;">
                        <p:inputText id="psnName7" style="width: 400px;cursor: pointer;" readonly="true"
                                     value="#{mgrbean.supportTopic.psnSelVO.selPsnName}"
                                     onclick="document.getElementById('mainForm:tabView:supportTopicPsnSelectLink5').click();"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search" id="supportTopicPsnSelectLink5"
                                       style="position: relative;left: -20px !important;"
                                       process="@this" action="#{mgrbean.openSupportResearchPsnSelectAction}">
                            <f:setPropertyActionListener value="mainForm:tabView:psnName7"
                                                         target="#{mgrbean.psnInputId}"/>
                            <f:setPropertyActionListener value="#{mgrbean.supportTopic.psnSelVO}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -15px;"
                                       action="#{mgrbean.emptySupportResearchPsnSelectAction}"
                                       update="psnName7" process="@this">
                            <f:setPropertyActionListener value="#{mgrbean.supportTopic.psnSelVO}"
                                                         target="#{mgrbean.supportResearchPsnSelVO}"/>
                        </p:commandLink>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="#{mgrbean.supportTopic.topicType == 3 ? '项目名称':'课题名称'}："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" colspan="3">
                    <p:inputTextarea style="width: 400px;resize: none;height:50px;" maxlength="200" autoResize="false"
                                     value="#{mgrbean.supportTopic.topicName}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{mgrbean.supportTopic.topicType == 3}">
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="获奖名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" colspan="3">
                    <p:inputText style="width: 400px;" maxlength="100"
                                 value="#{mgrbean.supportTopic.prizeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;height: 35px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="#{mgrbean.supportTopic.topicType == 3 ? '授奖':'立项批准'}部门："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;" colspan="3">
                    <p:inputText style="width: 400px;" maxlength="100"
                                 value="#{mgrbean.supportTopic.awardDepartment}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;" >
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="等级："/>
                </p:column>
                <p:column style="text-align:left;padding-left:6px;width: 120px;" >
                    <p:selectOneMenu value="#{mgrbean.supportTopic.fkByPrizeLevelId.rid}"
                                     style="width: 200px;">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{mgrbean.prizeLeveList}" var="itm"
                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                    </p:selectOneMenu>
                </p:column>
                <p:column style="text-align:right;width: 120px;">
                    <p:outputLabel value="*" style="color: red"/>
                    <h:outputText value="支撑材料："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 260px;" >
                    <p:outputPanel style="padding-left: 3px;" id="researchFilePanel5">
                        <p:commandButton value="上传" update="fileDialog"
                                         action="#{mgrbean.openSupportResearchFileUploadAction}"
                                         rendered="#{mgrbean.supportTopic.supportFilePath == null}">
                            <f:setPropertyActionListener value="5" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>
                        <p:commandButton value="查看" process="@this"
                                         onclick="window.open('/webFile/#{mgrbean.supportTopic.supportFilePath}')"
                                         rendered="#{mgrbean.supportTopic.supportFilePath != null}"/>
                        <p:spacer width="5" rendered="#{mgrbean.supportTopic.supportFilePath != null}"/>
                        <p:commandButton value="删除" onclick="PF('DeleteSupportDialog').show();"
                                         rendered="#{mgrbean.supportTopic.supportFilePath != null}">
                            <f:setPropertyActionListener value="5" target="#{mgrbean.fileOptType}"/>
                        </p:commandButton>

                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-disk" action="#{mgrbean.saveSupportTopicAction}"
                                     process="@this,:mainForm:tabView:supportTopicDialogPanel"/>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" type="button"
                                     onclick="PF('SupportTopicDialog').hide();"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="DeleteSupportDialog">
        <p:commandButton value="确定" action="#{mgrbean.deleteSupportResearchFileAction}"
                         icon="ui-icon-check" oncomplete="PF('DeleteSupportDialog').hide();"/>
        <p:commandButton value="取消" icon="ui-icon-close" type="button"
                         onclick="PF('DeleteSupportDialog').hide();"/>
    </p:confirmDialog>
</ui:composition>
