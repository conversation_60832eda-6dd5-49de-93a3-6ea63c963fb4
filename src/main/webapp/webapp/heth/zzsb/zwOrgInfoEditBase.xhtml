<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwOrgInfoBaseZzsbBean"-->
<f:view contentType="text/html">
    <h:head>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
		<h:outputStylesheet name="css/ui-tabs.css"/>
		<h:outputStylesheet name="css/ui-cs.css"/>
        <style type="text/css">
            body {
				margin: 0;
				padding: 0;
				width: 100%;
				height: 100%;
				overflow-x: hidden;

			}

		table.ui-selectoneradio td label{
				 white-space:nowrap;
		    	 overflow: hidden;
		}
		.icon-alert{
			background-image: url(/resources/images/alert-tip.png) !important;
			background-size: 12px 12px;
			margin-left: 3px;
			margin-top: -6px !important;
		}
         #triangle-up {
			width: 0;
			height: 0;
			border-left: 10px solid transparent;
			border-right: 10px solid transparent;
			border-bottom: 10px solid #4D4D4D;
			position: relative;
			left: 100px;
			top: 3px;
		}
			.zwx-tooltip{
				-webkit-line-clamp: 1;
			}

			.psnDiag .ui-widget-content{
				padding-top: 0px !important;
			}
			.col-required span:before {
				content: '*';
				color: red;
			}
			#mainForm\:infoDialog > div.ui-dialog-buttonpane.ui-widget-content.ui-helper-clearfix{
				border: 0px !important;
			}
        </style>

        <script type="text/javascript">
            //<![CDATA[
 					function selectItems(item){
                        document.getElementById('mainForm:selOfficeId').value = item.checked + '_' + item.value;
                    }
                    /*计算预警时间*/
                    function getWaringDate(form){
                        var years = Number($("#"+form.id+"\\:checkCycle").val());
                        if(!isNaN(years)){
                            $("#"+form.id+"\\:checkCycleHidden").val(years);
                        }
						changeWaringDate();
                    }
                    function disabledInput(){
                    	var text;
    					var $tabView = $("#mainForm\\:codeEditGrid");
    					$tabView.find("input,textarea,label").each(function(){
    						$(this).css("pointer-events","none");
    					});
    					$tabView.find("a").each(function(){
    						text = $(this).text();
    						if(!text){
    							text = $(this).attr("title");
    						}
    						if("删除"==text||"修改"==text){
    							$(this).remove();

    						}else if("查看附件"==text||"培训履历"==text){
								var chArr = document.getElementsByClassName("delSpacer");
								for(i=0;i<chArr.length;i++){
									//删除元素 元素.parentNode.removeChild(元素);
									if (chArr[i] != null)
										chArr[i].parentNode.removeChild(chArr[i]);
								}
    						}else{
    							$(this).prop("disabled",true);
    							$(this).css("pointer-events","none");
    							$(this).css("opacity","0.35");
    						}

    					});
    					$tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box'],div[class*='ui-chkbox']").each(function(){
    						$(this).addClass("ui-state-disabled");
    						$(this).css("opacity","1");
    						$(this).css("pointer-events","none");
    					});
    					//下拉
    					$tabView.find("div[class*='ui-selectonemenu']").each(function(){
    						$(this).addClass("ui-state-disabled");
    						$(this).css("pointer-events","none");
    						$(this).css("opacity","1");
    					});
    					//按钮
    					$tabView.find("button").each(function(){
    						text = $(this).text();
    						if("删除"==text){
    							$(this).remove();
    						}else if("关闭"==text||"查看"==text){

    						}else{
    							$(this).prop("disabled",true);
    							$(this).css("pointer-events","none");
    							$(this).css("opacity","0.35");
    						}
    					});
                    }
                    
                    var show = false;
        			var target;
                    $(function() {
        				$("body").mousemove(
							function(e) {
							if (show == true) {
								var label = document.getElementById("label");
								if (e.pageX < $(label).offset().left
										|| e.pageX > ($(label).offset().left + $(label).width())
										|| e.pageY < ($(target).offset().top)
										|| e.pageY > ($(label).offset().top + $(label).height())) {
									$(label).fadeOut("6000",
										function() {
											show = false;
										});
								}

							}
						});

        			});

        			function getLabelPanel(div, desc) {
        				if (!show) {
        					var label = document.getElementById("label");
        					$(label).css({
        						"display" : "none"
        					});
        					$(label).css(
        							{
        								"top" : $(div).offset().top + $(div).height()
        										+ 3,
        								"left" : $(div).offset().left
        										- ($(label).width() / 2+20)
        							});
        					var str = "";
        					var code = desc.split(",");
        					if (code.length>0) {
								for (var int = 0; int < code.length; int++) {
									str = str + code[int] + "<br/>"; 
								}
							}
        					
        					$("#descDiv").html(str);
        					$(label).fadeIn("6000", function() {
        						target = div;
        						show = true;
        					});

        				}

        			}

			$(document).ready(function() {
				var isDisabled="#{mgrbean.unitType}";
				var orgType="#{mgrbean.zwOrgType}";
				if((orgType==4 || orgType=='4') && isDisabled!=1 && isDisabled!='1' && isDisabled!=2 && isDisabled!='2'){
					document.getElementById("mainForm:openInfoDialog").click();
				}
			});
            //]]>
        </script>
    </h:head>

    <h:body>
    	<h:outputStylesheet name="css/default.css"/>
        <h:outputStylesheet library="css" name="ui-tabs.css"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:form id="mainForm"  style="height: 100%;">
            <p:outputPanel style="height: 100%;">
                <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;">
                    <f:facet name="header">
                        <p:row>
                            <p:column style="text-align:left;padding-left:5px;height: 20px;">
                                <h:outputText value="检查机构资质信息" rendered="#{mgrbean.zwOrgType == 0}" />
                                <h:outputText value="放射机构资质信息" rendered="#{mgrbean.zwOrgType == 1}" />
                                <h:outputText value="诊断机构资质信息" rendered="#{mgrbean.zwOrgType == 2}" />
                                <h:outputText value="职业卫生技术服务机构资质信息" rendered="#{mgrbean.zwOrgType == 3}" />
                                <h:outputText value="支撑机构信息管理" rendered="#{mgrbean.zwOrgType == 4}" />
                            </p:column>
                        </p:row>
                    </f:facet>
                </p:panelGrid>

			<p:outputPanel id="commondButtom" >
				<p:outputPanel id="commondButtom1" styleClass="zwx_toobar_42" style="display:flex;">
                    <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                        <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                        <p:commandButton value="保存" icon="ui-icon-disk" id="saveBtn" action="#{mgrbean.saveAction}" disabled="#{mgrbean.zwOrgType == 0 and !mgrbean.ifZzsbSave and mgrbean.ifReviewProcess?true:false}"
                                         process="@this,tabView"  update="@form" rendered="#{mgrbean.zwOrginfoEntity.state == 0}" >
                            <f:setPropertyActionListener target="#{mgrbean.zwOrginfoEntity.state}" value="0"/>
                        </p:commandButton>
                        <p:commandButton value="提交" icon="ui-icon-check"  disabled="#{mgrbean.zwOrgType == 0 and !mgrbean.ifZzsbSave and mgrbean.ifReviewProcess?true:false}"
										 id="submitBtn" action="#{mgrbean.submitAction}"
                                         process="@this,tabView"  update="@form" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
                            <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert"/>
                        </p:commandButton>
                        <p:commandButton value="撤销" icon="ui-icon-circle-triangle-e" id="cancelBtn" action="#{mgrbean.cancelAction}"
                                         process="@this,tabView"  update="@form" rendered="#{mgrbean.zwOrginfoEntity.state != 0}">
                            <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                        </p:commandButton>
                        <p:commandButton rendered="#{mgrbean.tabTitle == '人员信息' and mgrbean.zwOrginfoEntity.state == 0}"
                                         value="添加" icon="ui-icon-plus"
                                         process="@this" action="#{mgrbean.additmDAction}"
                                         update=":mainForm:codeEditDialog" oncomplete="PF('CodeEditDialog').show();">
                        </p:commandButton>
                        <p:commandButton value="添加 / 修改" icon="ui-icon-plus" rendered="#{mgrbean.tabTitle == '仪器信息' and mgrbean.zwOrginfoEntity.state == 0}"
                                         process="@this" action="#{mgrbean.addInstInitAction}"
                                         update=":mainForm:orgInstInfoDialog"  oncomplete="PF('OrgInstInfoDialog').show();">
                        </p:commandButton>
                        <p:commandButton rendered="#{mgrbean.tabTitle == '检查项目' and mgrbean.zwOrginfoEntity.state == 0 }"
                                         value="选择项目" icon="ui-icon-plus" process="@this" update=":mainForm:checkItemDialog,:mainForm:selItemsGrid"
                                         action="#{mgrbean.addItemInitAction}"  oncomplete="PF('CheckItemDialog').show();" >
                        </p:commandButton>
						<p:commandButton value="添加" icon="ui-icon-plus" rendered="#{mgrbean.tabTitle == '外检车辆' and mgrbean.zwOrginfoEntity.state == 0}"
										 process="@this" action="#{mgrbean.addOutcar}"
										 update=":mainForm:outCarEditDialog"  oncomplete="PF('OutCarEditDialog').show();">
						</p:commandButton>
						<p:commandButton value="添加" icon="ui-icon-plus" rendered="#{mgrbean.tabTitle == '检测能力管理' and mgrbean.zwOrginfoEntity.state == 0}"
										 process="@this" action="#{mgrbean.addChkAbility}"
										 update=":mainForm:abilityDetectionDialog"  oncomplete="PF('AbilityDetectionDialog').show();">
						</p:commandButton>
						<p:commandButton value="添加" icon="ui-icon-plus" rendered="#{mgrbean.tabTitle == '学术培训信息' and mgrbean.zwOrginfoEntity.state == 0}"
										 process="@this" action="#{mgrbean.addSupportTrain}"
										  >
						</p:commandButton>
						<p:commandButton value="退回原因" icon="icon-alert" style="color:red;"
										 process="@this" oncomplete="PF('ReasonDialog').show();"
										 update="reasonDialog" rendered="#{null != mgrbean.zwOrginfoEntity.auditAdv and null != mgrbean.zwOrginfoEntity.checkRst and mgrbean.zwOrginfoEntity.checkRst == 2}"/>
						<p:commandLink
								id="openInfoDialog"
								action="#{mgrbean.openInfoDialog}" process="@this" update=":mainForm" >
						</p:commandLink>
                    </h:panelGrid>
					<p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;"
								   rendered="#{mgrbean.zwOrgType == 0 and !mgrbean.ifZzsbSave and mgrbean.ifReviewProcess}">
						<h:outputLabel value="提示：" style="color:red;"/>
						<h:outputLabel value="请先在【资质机构备案申请】提交备案申请！" style="color:blue;"/>
					</p:outputPanel>
				</p:outputPanel>
				<p:sticky target="commondButtom1"/>
                </p:outputPanel>

                <p:tabView id="tabView" style="margin-top: 5px;" cache="false" dynamic="false" >
                    <ui:insert name="insertTabAction"/>
                    <p:tab title="基本信息">
                    	<ui:include src="/webapp/heth/zzsb/zwQualBaseInfo.xhtml"/>
                    </p:tab>

					<p:tab title="人员信息" disabled="#{!mgrbean.ifZzsbSave}">
						<p:panelGrid style="width:100%;margin:10px 0px;" rendered="#{mgrbean.zwOrgType == 0 and mgrbean.ifPersonSummary==1}">
							<f:facet name="header">
								<p:row>
									<p:column style="text-align:left;padding:6px;" styleClass="ui-state-default">
										<h:outputText value="人员汇总"/>
									</p:column>
								</p:row>
							</f:facet>
							<p:row>
								<p:column>
									<p:outputPanel style="padding:12px 6px;" id="personSummary">
										<table>
											<c:forEach items="#{mgrbean.personSummaryList}" var="itm">
												<tr>
													<c:forEach items="#{itm}" var="val" varStatus="valIndex">
														<td style="border:none;">
															<h:outputText value="#{val}" escape="false" style="padding:6px;"/>
															<h:outputText value="|" style="float: right;" rendered="#{valIndex.index!=itm.size()-1}"/>
														</td>
													</c:forEach>
												</tr>
											</c:forEach>
										</table>
									</p:outputPanel>
								</p:column>
							</p:row>
						</p:panelGrid>
					    <p:dataTable var="itm" value="#{mgrbean.zwOrginfoEntity.zwOrgPsns}"
							id="psnDatatable" emptyMessage="没有您要找的记录！" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
									 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
									 rowsPerPageTemplate="#{mgrbean.perPageSize}" lazy="true" pageLinks="5"
									 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
							style="margin-top:5px;width:100%">

                            <p:column headerText="姓名" style="text-align:center;width:80px;">
                                <p:commandLink value="#{itm.tdZwPsninfo.empName}" update=":mainForm:codeEditDialog"
                                	process="@this" oncomplete="PF('CodeEditDialog').show();"
                                	action="#{mgrbean.codeViewAction}">
                                	<f:setPropertyActionListener value="#{itm}" target="#{mgrbean.selectZwOrgPsn}" />
                                </p:commandLink>
                            </p:column>
                            <p:column headerText="性别" style="text-align:center;width:40px;">
                                <p:outputLabel value="#{itm.tdZwPsninfo.sex}" />
                            </p:column>
                            <p:column headerText="职称" style="text-align:center;width:100px;">
                                <p:outputLabel value="#{itm.tdZwPsninfo.fkByTitleId.codeName}" />
                            </p:column>
                            <p:column headerText="人员属性" style="width:260px; ">
                                <p:outputLabel id="psnTypes" value="#{itm.tdZwPsninfo.psnTypes}" styleClass="zwx-tooltip"/>
								<p:tooltip for="psnTypes" style="max-width:260px;">
									<p:outputLabel value="#{itm.tdZwPsninfo.psnTypes}" escape="false"/>
								</p:tooltip>
                            </p:column>
                            <p:column headerText="在岗状态" style="text-align:center;width:60px; ">
                            	<p:outputLabel value="在职" rendered="#{itm.onDuty=='1'}"/>
                            	<p:outputLabel value="离职" rendered="#{itm.onDuty=='2'}"/>
                            </p:column>
                            <p:column headerText="#{mgrbean.zwOrgType==0?'证书（发文）编号':'证书编号'}" style="width:180px;" >
                            	<p:outputLabel value="#{itm.tdZwPsninfo.certNoStr}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')==-1}"/>
                         		<p:outputLabel value="#{itm.tdZwPsninfo.certNoStr.substring(0,itm.tdZwPsninfo.getCertNoStr().indexOf(','))}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0}"/>
	                            <div style="background-color: #F35E37;display: inline;padding: 3px;border-radius: 3px;display:#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0?'':'none'}">
			                        <p:outputLabel value="其他" style="color:white;"
				                        rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0}"
				                        onmouseover="getLabelPanel(this,'#{itm.tdZwPsninfo.certNoStr.substring(itm.tdZwPsninfo.getCertNoStr().indexOf(',')+1,itm.tdZwPsninfo.getCertNoStr().length())}')"/>
	                            </div>
                                <p:outputLabel value="#{itm.tdZwPsninfo.certNoStr}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')==0}"/>
                            </p:column>
                            <p:column headerText="个人剂量计编号" style="width:200px;"  rendered="false">
                            	<p:inputText value="#{itm.jlCode}" maxlength="25"  style="width: 95%" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"/>
                            	<p:outputLabel value="#{itm.jlCode}" rendered="#{mgrbean.zwOrginfoEntity.state == 1}"/>
                            </p:column>
                            <p:column headerText="个人剂量累积剂量" style="width:200px;"  rendered="false">
                            	<p:inputText value="#{itm.ljJl}" maxlength="25"  style="width: 95%" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"/>
                            	<p:outputLabel value="#{itm.ljJl}" rendered="#{mgrbean.zwOrginfoEntity.state == 1}"/>
                            </p:column>
                            <p:column headerText="从事项目" style="padding-left:5px;width:260px;" rendered="#{mgrbean.zwOrgType=='0'}">
                            	 <!--<p:outputLabel value="#{itm.wordItemStr}"/>-->
                                 <!-- 只显示跟检查资质相关的资质类别的资质范围 -->
                                <p:outputLabel id="zzlbRange" value="#{itm.tdZwPsninfo.zzlbRange}" styleClass="zwx-tooltip"/>
								<p:tooltip for="zzlbRange" style="max-width:260px;">
									<p:outputLabel value="#{itm.tdZwPsninfo.zzlbRange}" escape="false"/>
								</p:tooltip>
                            </p:column>
							<p:column headerText="从事科室" style="padding-left:5px;width:260px;" rendered="#{mgrbean.zwOrgType=='0'}">
								<p:outputLabel id="wordOfficeName" value="#{itm.wordOfficeName}" styleClass="zwx-tooltip"/>
								<p:tooltip for="wordOfficeName" style="max-width:260px;">
									<p:outputLabel value="#{itm.wordOfficeName}" escape="false"/>
								</p:tooltip>
							</p:column>
							<p:column headerText="操作" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
								<!--<ui:insert name="insertPsnActions"/>-->

								<!--<p:commandLink value="从事项目" process="@this,:mainForm:tabView:psnDatatable" update=":mainForm:csProDialog"
                                               rendered="#{mgrbean.zwOrgType=='0'}" action="#{mgrbean.selCsProAction}"   oncomplete="PF('CsProDialog').show();" >
									<f:setPropertyActionListener target="#{mgrbean.selectZwOrgPsn}" value="#{itm}" />
								</p:commandLink>
								<p:spacer width="5"   rendered="#{mgrbean.zwOrgType=='0'}" />-->
								<p:commandLink value="修改" process="@this" update=":mainForm:codeEditDialog"
											   oncomplete="PF('CodeEditDialog').show();" action="#{mgrbean.psnInfoEditAction}">
									<f:setPropertyActionListener value="#{itm}" target="#{mgrbean.selectZwOrgPsn}" />
								</p:commandLink>
								<p:spacer width="5" />
								<p:commandLink value="科室选择" process="@this,:mainForm:tabView:psnDatatable" update=":mainForm:workOfficeDialog"
											   rendered="#{mgrbean.zwOrgType=='0'}" action="#{mgrbean.selWordOfficeAction}"   oncomplete="PF('WorkOfficeDialog').show();" >
									<f:setPropertyActionListener target="#{mgrbean.selectZwOrgPsn}" value="#{itm}" />
								</p:commandLink>
								<p:spacer width="5"   rendered="#{mgrbean.zwOrgType=='0'}" />
								<p:commandLink value="离职" process="@this" update=":mainForm:tabView:psnDatatable"
									action="#{mgrbean.changeDutyAction}" rendered="#{itm.onDuty=='1'}" onclick="hideTooltips()">
									<p:confirm header="消息确认框" message="确定要离职吗？"
										icon="ui-icon-alert" />
									<f:setPropertyActionListener target="#{mgrbean.selectZwOrgPsn}"
										value="#{itm}" />
									<f:setPropertyActionListener target="#{mgrbean.dutySate}"
										value="2" />
								</p:commandLink>
								<p:commandLink value="在职" process="@this" update=":mainForm:tabView:psnDatatable"
									action="#{mgrbean.changeDutyAction}" rendered="#{itm.onDuty=='2'}" onclick="hideTooltips()">
									<p:confirm header="消息确认框" message="确定要在职吗？"
										icon="ui-icon-alert" />
									<f:setPropertyActionListener target="#{mgrbean.selectZwOrgPsn}"
										value="#{itm}" />
									<f:setPropertyActionListener target="#{mgrbean.dutySate}"
										value="1" />
								</p:commandLink>
								<p:spacer width="5" />
								<p:commandLink value="删除" process="@this" update=":mainForm:tabView:psnDatatable"
									action="#{mgrbean.deletePsnInstAction}" >
									<p:confirm header="消息确认框" message="确定要删除吗？"
										icon="ui-icon-alert" />
									<f:setPropertyActionListener target="#{mgrbean.selectZwOrgPsn}"
										value="#{itm}" />
								</p:commandLink>
							</p:column>
						</p:dataTable>
					
						<p:outputPanel rendered="#{mgrbean.zwOrgType == 0 and mgrbean.ifPersonSummary==1}" 
							style="min-height: 200px;padding:6px;margin:10px 0px;line-height: 21px;border-radius: 3px;border:1px solid #FFE58F;background-color: #FEFBE6;">
							<!-- 填报说明 -->
							<h:outputText value="#{mgrbean.fillDesc}" escape="false" style="color:#DE7C05;"/>
						</p:outputPanel>
					</p:tab>
					<p:tab title="场所建设" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType == 4}">
						<ui:include src="/webapp/heth/zzsb/zwSupportPlaceInfo.xhtml"/>
					</p:tab>
					<p:tab title="仪器信息" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType != 4}">
                        <p:dataTable var="instItm" value="#{mgrbean.zwInstInfoList}"
                                     id="instDatatable"
									 emptyMessage="没有您要找的记录！" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
									 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
									 rowsPerPageTemplate="#{mgrbean.perPageSize}"  lazy="true"
									 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                     style="margin-top:5px;width:100%">
                            <p:column headerText="仪器名称" style="padding-left: 3px;width:15%">
                                <p:outputLabel value="#{instItm.tdZwInstinfo.instName}"/>
                            </p:column>
                            <p:column headerText="仪器型号" style="text-align:center;width:10%">
                                <p:outputLabel value="#{instItm.tdZwInstinfo.instModel}"/>
                            </p:column>
							<p:column headerText="仪器类型" style="text-align:center;width:10%">
								<p:outputLabel value="#{null == instItm.tdZwInstinfo.fkByInstKindDetalId ? '' :
								instItm.tdZwInstinfo.fkByInstKindDetalId.codeName}"/>
							</p:column>
                            <p:column headerText="仪器编号" style="text-align:center;width:10%">
                                <p:outputLabel value="#{instItm.tdZwInstinfo.instCode}"/>
                            </p:column>
                            <p:column headerText="最近校验日期" style="text-align:center;width:10%">
                                <p:outputLabel value="#{instItm.tdZwInstinfo.lastAcptDate}">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                                </p:outputLabel>
                            </p:column>
                            <p:column headerText="校验周期（年）" style="text-align:center;width:10%">
                                <p:outputLabel value="#{instItm.tdZwInstinfo.lastAcptCircleDot}"/>
                            </p:column>
                            <p:column headerText="是否外包" style="text-align:center;width:5%" rendered="#{(mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3) ? false: true}">
                                <p:outputLabel value="是" rendered="#{instItm.tdZwInstinfo.outter == 1}"/>
                                <p:outputLabel value="否" rendered="#{instItm.tdZwInstinfo.outter == 0}"/>
                            </p:column>
                            <p:column headerText="是否支持外检" style="text-align:center;width:6%" rendered="#{mgrbean.zwOrgType == 0}" >
                                <p:outputLabel value="是" rendered="#{instItm.tdZwInstinfo.ifSuptOutChk == 1}"/>
                                <p:outputLabel value="否" rendered="#{instItm.tdZwInstinfo.ifSuptOutChk == 0}"/>
                            </p:column>
							<!--<p:column style="width: 5%;text-align: center" headerText="是否放射" rendered="#{mgrbean.zwOrgType == 1 ? false: true}">-->
								<!--<p:outputLabel rendered="#{instItm.tdZwInstinfo.instType == 0}" value="否" />-->
								<!--<p:outputLabel rendered="#{instItm.tdZwInstinfo.instType == 1}" value="是" />-->
							<!--</p:column>-->                            
                            <p:column headerText="操作" style="padding-left:5px;width:9%" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
                            	<ui:insert name="insertInstActions"/>
                            	
                                <p:commandLink value="删除" process="@this" update=":mainForm:tabView:instDatatable"
                                               action="#{mgrbean.deleteOrgInstAction}">
                                    <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                                    <f:setPropertyActionListener target="#{mgrbean.selectOrgInst}" value="#{instItm}"/>
                                </p:commandLink>
                            </p:column>
                        </p:dataTable>
                    </p:tab>

                    <p:tab title="检查项目" rendered="#{mgrbean.zwOrgType == 0}" disabled="#{!mgrbean.ifZzsbSave}" >
                        <p:panelGrid style="width:100%;margin-top: 8px;" id="itemsGrid">
								<f:facet name="header">
									<p:row>
										<p:column style="text-align:center;height: 20px;width:80px;">
											<p:outputLabel value="序号" />
										</p:column>
										<p:column style="text-align:center;height: 20px;width:200px;">
											<p:outputLabel value="项目大类" />
										</p:column>
										<p:column style="text-align:center;height: 20px;">
											<p:outputLabel value="项目小类" />
										</p:column>
										<p:column style="text-align:center;height: 20px;">
											<p:outputLabel value="是否外包" />
										</p:column>
									</p:row>
								</f:facet>
								<c:forEach items="#{mgrbean.showItemList}" var="v">
									<p:row>
										<p:column style="text-align:center;height: 27px;">
											<p:outputLabel value="#{v[2]}" />
										</p:column>
										<p:column style="text-align:center;height: 27px;" rendered="#{v[0]}"
											rowspan="#{v[1]}">
											<p:outputLabel value="#{v[3]}" />
										</p:column>
										<p:column style="height: 27px;text-align:left;padding-left:3px;">
											<p:outputLabel value="#{v[4]}" />
										</p:column>
										<p:column style="height: 27px;text-align:left;padding-left:3px;">
											<p:outputLabel value="是" rendered="#{v[5] and mgrbean.zwOrginfoEntity.state ne 0}" />
											<p:outputLabel value="否" rendered="#{(v[5]==null or !v[5]) and mgrbean.zwOrginfoEntity.state ne 0 }" />
											<p:selectBooleanCheckbox value="#{v[5]}" rendered="#{mgrbean.zwOrginfoEntity.state==0}"/>
										</p:column>
									</p:row>
								</c:forEach>
							</p:panelGrid>
                    </p:tab>
					<!--<p:tab title="服务明细" rendered="#{mgrbean.zwOrgType==0 and mgrbean.ifHethFj and null ne mgrbean.zwOrginfoEntity and null ne mgrbean.zwOrginfoEntity.outWorkPower and 1 == mgrbean.zwOrginfoEntity.outWorkPower}">
					--><p:tab title="服务明细" rendered="#{mgrbean.zwOrgType==0 and mgrbean.ifServiceDetails =='1'}" disabled="#{!mgrbean.ifZzsbSave}">
						<ui:include src="/webapp/heth/zzsb/zwSerDetails.xhtml" />
					</p:tab>
					<p:tab title="外检车辆" rendered="#{mgrbean.zwOrgType==0 and mgrbean.ifHethFj and null ne mgrbean.zwOrginfoEntity and null ne mgrbean.zwOrginfoEntity.outWorkPower and 1 == mgrbean.zwOrginfoEntity.outWorkPower}" disabled="#{!mgrbean.ifZzsbSave}">
						<ui:include src="/webapp/heth/zzsb/zwOutCarInfo.xhtml" />
					</p:tab>

                    <p:tab title="质量管理体系" id="annexQualInstitution"  rendered="#{mgrbean.zwOrgType == 0 and mgrbean.ifQualAnnex != null and mgrbean.ifQualAnnex == 1}" disabled="#{!mgrbean.ifZzsbSave}">
                        <c:forEach items="#{mgrbean.topOfInstitutionList}" var="top" >
                            <p:fieldset legend="#{top.codeName}"  style="margin-top: 5px;margin-bottom: 5px;" >
                                <h:panelGrid columns="2" style="width: 20%;margin-bottom: 2px;">
                                    <p:commandButton value="添加" icon="ui-icon-plus" id="addInstitutionBtn#{top.rid}"
                                                     action="#{mgrbean.institutionAddOne}" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"
                                                     update="@this,topInsPanel#{top.rid}"  process="@this" >
                                        <f:setPropertyActionListener target="#{mgrbean.codeId}" value="#{top.rid}"/>
                                    </p:commandButton>
                                </h:panelGrid>
                                <p:panelGrid style="width:100%;" id="topInsPanel#{top.rid}" >
                                    <p:row>
                                        <p:column style="text-align:center;height: 27px;width:260px;padding-left:10px;" styleClass="ui-state-default">
                                            <p:outputLabel value="制度名称" />
                                        </p:column>
                                        <p:column style="text-align:center;width:200px;padding-left:10px;" styleClass="ui-state-default">
                                            <p:outputLabel value="有无" />
                                        </p:column>
                                        <p:column styleClass="ui-state-default" style="text-align:center;padding-left:10px;#{mgrbean.zwOrginfoEntity.state == 0 ? 'width:280px;' : ''}">
                                            <p:outputLabel value="附件" />
                                        </p:column>
                                        <p:column styleClass="ui-state-default" style="text-align:center;padding-left: 30px;" rendered="#{mgrbean.zwOrginfoEntity.state == 0}" >
                                            <p:outputLabel value="操作" />
                                        </p:column>
                                    </p:row>
                                    <c:forEach items="#{mgrbean.qualAnnexMap.get(top.rid)}" var="v" varStatus="index">
                                        <p:row>
                                            <p:column style="text-align:left;height: 27px;padding-left:10px;">
                                                <p:outputLabel value="#{v.fkByRuleId.codeName}"
                                                               rendered="#{v.fkByRuleId.rid != top.rid}" />
                                                <p:outputLabel value="#{v.otherRuleName}"
                                                               rendered="#{v.fkByRuleId.rid == top.rid and mgrbean.zwOrginfoEntity.state != 0}" />
                                                <p:inputText value="#{v.otherRuleName}"
                                                             rendered="#{v.fkByRuleId.rid == top.rid and mgrbean.zwOrginfoEntity.state == 0 }" maxlength="25">
                                                    <p:ajax event="change" process="@this"   />
                                                </p:inputText>
                                            </p:column>
                                            <p:column style="text-align:left;padding-left:50px;">
                                                <p:selectOneRadio value="#{v.ifHas}" rendered="#{mgrbean.zwOrginfoEntity.state == 0}" style="width:135px;" converter="javax.faces.Integer">
                                                    <f:selectItem itemLabel="有" itemValue="1"></f:selectItem>
                                                    <f:selectItem itemLabel="无" itemValue="0"></f:selectItem>
                                                    <p:ajax event="change" process="@this"  update="uploadLinkOrgAnnex#{top.rid}K#{index.index},viewAnnex#{top.rid}K#{index.index},delAnnex#{top.rid}K#{index.index}"  />
                                                </p:selectOneRadio>
                                                <p:outputLabel style="padding-left: 50px;" value="#{v.ifHas == 1 ? '有' : '无'}"
                                                               rendered="#{mgrbean.zwOrginfoEntity.state != 0}" />
                                            </p:column>
                                            <p:column style="text-align:left;padding-left:10px;">
                                                <p:commandLink value="查看" id="viewAnnex#{top.rid}K#{index.index}" disabled="#{v.ifHas == 1 ? false : true}" rendered="#{v.annexPath != '' and null != v.annexPath}"
                                                               process="@this"  onclick="window.open('/webFile/#{v.annexPath}')" />
                                                <p:commandLink value="上传附件" id="uploadLinkOrgAnnex#{top.rid}K#{index.index}" disabled="#{v.ifHas == 1 ? false : true}" rendered="#{(v.annexPath == '' or null == v.annexPath) and mgrbean.zwOrginfoEntity.state == 0}"
                                                               process="@this" update="@this,topInsPanel#{top.rid}" onclick="PF('TjorgAnnexFileDialog').show();" >
                                                    <f:setPropertyActionListener target="#{mgrbean.annexTmp}" value="#{v}"/>
                                                </p:commandLink>
                                                <p:spacer width="5" rendered="#{v.annexPath != '' and null != v.annexPath}" />
                                                <p:commandLink value="删除" id="delAnnex#{top.rid}K#{index.index}" disabled="#{v.ifHas == 1 ? false : true}" rendered="#{v.annexPath != '' and null != v.annexPath and mgrbean.zwOrginfoEntity.state == 0 }"
                                                               process="@this" update="@this,topInsPanel#{top.rid}" action="#{mgrbean.delInsFile}" >
                                                    <f:setPropertyActionListener target="#{mgrbean.annexTmp}" value="#{v}"/>
                                                    <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                                                </p:commandLink>
                                            </p:column>
                                            <p:column style="text-align:left;padding-left: 10px;" rendered="#{mgrbean.zwOrginfoEntity.state == 0}" >
                                                <p:commandLink value="删除" rendered="#{v.fkByRuleId.rid == top.rid and mgrbean.zwOrginfoEntity.state == 0 }"
                                                               process="@this" update="@this,topInsPanel#{top.rid}" action="#{mgrbean.institutionDeleteOne}" >
                                                    <f:setPropertyActionListener target="#{mgrbean.annexTmp}" value="#{v}"/>
                                                    <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                                                </p:commandLink>
                                            </p:column>
                                        </p:row>
                                    </c:forEach>
                                </p:panelGrid>
                            </p:fieldset>
                        </c:forEach>

                        <p:dialog header="附件上传" widgetVar="TjorgAnnexFileDialog"
                                  resizable="false" modal="true">
                            <table>
                                <tr>
                                    <td style="text-align: right;"><p:outputLabel
                                            value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                                            style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="position: relative;top: -23px;">
                                        <p:fileUpload
                                                requiredMessage="请选择要上传的文件！" label="文件选择"
                                                fileUploadListener="#{mgrbean.institutionAnnexFileUpload}"
                                                invalidSizeMessage="文件大小不能超过100M!" id="upload"
                                                validatorMessage="上传出错啦，请重新上传！" style="width:600px;"
                                                previewWidth="120" cancelLabel="取消" update="@this,#{mgrbean.topInsPanelStr}"
                                                uploadLabel="上传" dragDropSupport="true" mode="advanced"
                                                sizeLimit="104857600" fileLimit="1"
                                                fileLimitMessage="最多只能上传1个文件！"
                                                invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                                allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                                        /></td>
                                </tr>
                            </table>
                        </p:dialog>
                    </p:tab>

                    <p:tab title="检测能力管理" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType == 3}">
                        <ui:include src="chkAbility.xhtml"/>
                    </p:tab>
                    <p:tab title="资质附件" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType != 4}">
                        <ui:include src="zwJsffjgCommAnnex.xhtml"/>
                    </p:tab>
					<p:tab title="质量控制结果" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{(mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2 )and mgrbean.zkCheckRst == '1'}">
						<ui:include src="zkCheckRst.xhtml"/>
					</p:tab>
					<p:tab title="仪器设备配置信息" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType eq 4}">
						<ui:decorate template="zwSupportInst.xhtml">
						</ui:decorate>
					</p:tab>
					<p:tab title="能力建设信息" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType == 4}" >
						<ui:decorate template="capacityBuilding.xhtml" />
					</p:tab>
					<p:tab title="科技攻关能力" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType eq 4}">
						<ui:include src="supportTrcInfo.xhtml" />
					</p:tab>
					<p:tab title="科研课题信息" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType eq 4 and mgrbean.hasResearchAndAcademicInfo}">
						<!-- ui:decorate 可以将ui:define的内容加入到template对应的相同name的ui:insert -->
						<ui:decorate template="zwResearchTopicsInfo.xhtml">
							<!-- 论文情况 -->
							<ui:define name="thesisAdd">
								<p:commandButton value="添加" icon="ui-icon-plus" style="margin-bottom: 5px;"
												 action="#{mgrbean.addSupportThesisAction}" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<ui:define name="thesisOper">
								<p:commandLink value="修改" action="#{mgrbean.editSupportThesisAction(thesis)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
								<p:commandLink value="删除" update=":mainForm:tabView:thesisListTable"
											   action="#{mgrbean.delSupportThesisAction(thesis.rid)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>

							<!-- 专著情况 -->
							<ui:define name="monographAdd">
								<p:commandButton value="添加" icon="ui-icon-plus" style="margin-bottom: 5px;"
												 action="#{mgrbean.addSupportMonographAction()}"
												 rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<ui:define name="monographOper">
								<p:commandLink value="修改" action="#{mgrbean.editSupportMonographAction(monograph)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
								<p:commandLink value="删除" update=":mainForm:tabView:monographListTable"
											   action="#{mgrbean.delSupportMonographAction(monograph.rid)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>

							<!-- 新立项课题 -->
							<ui:define name="newProjectInfoAdd">
								<p:commandButton value="添加" icon="ui-icon-plus" style="margin-left: 10px;margin-top: 3px;"
												 action="#{mgrbean.addSupportTopicAction(1)}" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<ui:define name="newProjectInfoOper">
								<p:commandLink value="修改" action="#{mgrbean.editSupportTopicAction(supportTopic)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
								<p:commandLink value="删除" update=":mainForm:tabView:newProjectTable"
											   action="#{mgrbean.delSupportTopicAction(supportTopic.rid,supportTopic.topicType)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>

							<!-- 在研课题 -->
							<ui:define name="inStudyInfoAdd">
								<p:commandButton value="添加" icon="ui-icon-plus" style="margin-left: 10px;margin-top: 3px;"
												 action="#{mgrbean.addSupportTopicAction(2)}" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<ui:define name="inStudyInfoOper">
								<p:commandLink value="修改" action="#{mgrbean.editSupportTopicAction(supportTopic)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
								<p:commandLink value="删除" update=":mainForm:tabView:inStudyInfoTable"
											   action="#{mgrbean.delSupportTopicAction(supportTopic.rid,supportTopic.topicType)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>

							<!-- 获奖课题 -->
							<ui:define name="awardsInfoAdd">
								<p:commandButton value="添加" icon="ui-icon-plus" style="margin-left: 10px;margin-top: 3px;"
												 action="#{mgrbean.addSupportTopicAction(3)}" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<ui:define name="awardsInfoOper">
								<p:commandLink value="修改" action="#{mgrbean.editSupportTopicAction(supportTopic)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
								<p:commandLink value="删除" update=":mainForm:tabView:awardsInfoTable"
											   action="#{mgrbean.delSupportTopicAction(supportTopic.rid,supportTopic.topicType)}"
											   rendered="#{mgrbean.zwOrginfoEntity.state ne 1}">
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>

							<!-- 专利 -->
							<ui:define name="patentInfoAdd">
								<p:commandButton value="添加" icon="ui-icon-plus" update=":mainForm:tabView:addPatentInfoDialogPanel"
												 style="margin-left: 10px;margin-top: 3px;"  resetValues="true"
												 action="#{mgrbean.addPatentInfoAction()}" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<ui:define name="patentInfoOper">
								<p:commandLink value="修改" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}" resetValues="true"
											   action="#{mgrbean.editPatentInfoAction(item[0])}"/>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}" />
								<p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"
											   action="#{mgrbean.delPatentInfoAction(item[0])}">
									<p:confirm header="消息确认框" message="确定要删除吗？"
											   icon="ui-icon-alert"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}" />
							</ui:define>

							<!-- 标准 -->
							<ui:define name="standardInfoAdd">
								<p:commandButton value="添加" icon="ui-icon-plus" update=":mainForm:tabView:addStandardInfoDialogPanel"
												 style="margin-left: 10px;margin-top: 3px;" resetValues="true"
												 action="#{mgrbean.addStandardInfoAction()}" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<ui:define name="standardInfoOper">
								<p:commandLink value="修改" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"
											   resetValues="true"
											   action="#{mgrbean.editPatentInfoAction(item[0])}"/>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
								<p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"
											   action="#{mgrbean.delStandardInfoAction(item[0])}">
									<p:confirm header="消息确认框" message="确定要删除吗？"
											   icon="ui-icon-alert"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<!-- 先进适宜技术筛选推广应用 -->
							<ui:define name="advanceAdd">
								<p:commandButton value="添加" icon="ui-icon-plus" style="margin-bottom: 5px;"
												 action="#{mgrbean.addSupportAdvanceAction()}"
												 rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>
							<ui:define name="advanceOper">
								<p:commandLink value="删除" update=":mainForm:tabView:advanceListTable"
											   action="#{mgrbean.delSupportAdvanceAction(advance.rid)}"
								>
									<f:setPropertyActionListener value="#{indexVar}" target="#{mgrbean.supportOptIndex}"/>
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandLink>
								<p:spacer width="5" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
							</ui:define>

							<!-- 引入弹框 -->
							<ui:define name="zwResearchTopicsInfoDiag">
								<ui:include src="zwResearchTopicsInfoDiag.xhtml" />
							</ui:define>
						</ui:decorate>
					</p:tab>
					<p:tab title="学术培训信息" disabled="#{!mgrbean.ifZzsbSave}" rendered="#{mgrbean.zwOrgType == 4 and mgrbean.hasResearchAndAcademicInfo}">
						<!-- ui:decorate 可以将ui:define的内容加入到template对应的相同name的ui:insert -->
						<ui:decorate template="supportTrain.xhtml">
							<ui:define name="supportTrainOperColumn">
								<p:column style="height: 35px;padding-left: 10px;" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
									<p:commandLink value="修改" process="@this" action="#{mgrbean.modSupportTrain}" >
										<f:setPropertyActionListener target="#{mgrbean.trainYear}" value="#{dataItm[0].year}"/>
									</p:commandLink>
									<p:spacer width="5" />
									<p:commandLink value="删除"
												   process="@this" update="@this,:mainForm:tabView:supportTrainDataId" action="#{mgrbean.delSupportTrain}">
										<f:setPropertyActionListener target="#{mgrbean.trainYear}" value="#{dataItm[0].year}"/>
										<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
									</p:commandLink>
								</p:column>
							</ui:define>
						</ui:decorate>
					</p:tab>
				</p:tabView>
            </p:outputPanel>
			<p:confirmDialog id="infoDialog" message="当前单位的单位属性不为职防院或疾控，请联系管理员！" header="消息提示框" widgetVar="ConfirmSupportDialog" closable="false">
			</p:confirmDialog>
			<!-- 从事科室选择 -->
			<p:dialog id="workOfficeDialog" header="从事科室选择" widgetVar="WorkOfficeDialog"
				resizable="false" width="350" height="400" modal="true">
				<p:selectManyCheckbox id="workOffice"
					value="#{mgrbean.workOffices}" layout="pageDirection">
					<f:selectItems value="#{mgrbean.workOfficeList}" itemValue="#{itm.rid}"  itemLabel="#{itm.codeName}"  var="itm"  />
				</p:selectManyCheckbox>

				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" icon="ui-icon-check" id="workSaveBtn"
								action="#{mgrbean.workOfficeSaveAction}" process="@this,workOffice" update=":mainForm:tabView:psnDatatable"
								oncomplete="PF('WorkOfficeDialog').hide();" />
							<p:spacer width="5" />
							<p:commandButton value="取消" icon="ui-icon-close" id="workBackBtn"
								onclick="PF('WorkOfficeDialog').hide();" immediate="true" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
			
			<!-- 从事项目选择 -->
			<p:dialog id="csProDialog" header="从事项目选择" widgetVar="CsProDialog"
				resizable="false" width="500" height="300" modal="true">
				<p:selectManyCheckbox id="csPro" value="#{mgrbean.selectCsPro}" layout="pageDirection">
					<f:selectItems value="#{mgrbean.csProMap}"/>
				</p:selectManyCheckbox>

				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" icon="ui-icon-check" 
								action="#{mgrbean.csProSaveAction}" process="@this,csPro" update=":mainForm:tabView:psnDatatable"
								oncomplete="PF('CsProDialog').hide();" />
							<p:spacer width="5" />
							<p:commandButton value="取消" icon="ui-icon-close" 
								onclick="PF('CsProDialog').hide();" type="button"/>
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>

			<!-- 检查项目选择 -->
			<p:dialog id="checkItemDialog" header="检查项目选择"
				widgetVar="CheckItemDialog" resizable="false" width="1000"
				height="500" modal="true">
				<p:panelGrid style="width:100%;" id="selItemsGrid">
					<c:forEach items="#{mgrbean.diagMap}" var="v1" >
						<p:row>
							<p:column style="text-align:left;width: 200px" >
								<p:selectBooleanCheckbox value="#{v1.key.ifSelected}">
									<p:ajax event="change" listener="#{mgrbean.selectItem}"
										process="@this,selItemId" update="user_#{v1.key.rid}"
										onstart="document.getElementById('mainForm:selItemId').value = #{v1.key.rid}" />
								</p:selectBooleanCheckbox>
								<p:outputLabel value="  " />
								<p:outputLabel value="#{v1.key.codeName}" />
							</p:column>
							<p:column style="text-align:left;padding-left:5px;">
								<p:selectManyCheckbox value="#{v1.key.selCodeRids}"
									 id="user_#{v1.key.rid}" columns="4" layout="grid" >
									<f:selectItems value="#{v1.value}" var="code" itemLabel="#{code.codeName}" 
										itemValue="#{code.rid}">
									</f:selectItems>
								</p:selectManyCheckbox>
							</p:column>
						</p:row>
					</c:forEach>
				</p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" icon="ui-icon-check"
								id="checkItemSaveBtn" action="#{mgrbean.checkItemSaveAction}"
								process="@this,checkItemDialog,mainForm:tabView:itemsGrid"
								update=":mainForm:tabView:itemsGrid"
								oncomplete="PF('CheckItemDialog').hide();" />
							<p:spacer width="5" />
							<p:commandButton value="取消" icon="ui-icon-close"
								id="checkItemBackBtn" onclick="PF('CheckItemDialog').hide();"
								immediate="true" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>
			
			<!-- 人员选择 -->
			<p:dialog header="人员选择" widgetVar="OrgPsnInfoDialog" id="orgPsnInfoDialog" resizable="false" modal="true" width="900" height="500">
				<p:outputPanel id="psnSelectButton" styleClass="zwx_toobar_42">
					<h:panelGrid columns="7">
						<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
						<p:commandButton value="查询" icon="ui-icon-search" action="#{mgrbean.searchPsnDAction}" 
							process="@this,searchPanel" update=":mainForm:dataTable"/>
						<p:commandButton value="添加" icon="ui-icon-plus" id="add2Btn" action="#{mgrbean.additmDAction}" process="@this" update=":mainForm:codeEditDialog"
							oncomplete="PF('CodeEditDialog').show();" />
                        <p:commandButton value="人员模板下载" icon="ui-icon-arrowthick-1-s" ajax="false" style="margin-right: 6px;"
                                         onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);">
                            <p:fileDownload value="#{mgrbean.psnfile}"/>
                        </p:commandButton>
                        <p:commandButton value="人员信息导入" icon="ui-icon-arrowreturnthick-1-s" update=":mainForm:dataTable,mainForm:psnFileDialog"
                                         process="@this,:mainForm:dataTable" oncomplete="PF('PsnFileDialog').show()"/>
                        <p:commandButton value="错误数据下载" icon="ui-icon-arrowthickstop-1-s" ajax="false" update=":radReportForm"
                                         process="@this" action="#{mgrbean.downloadPsnErrorFile}" rendered="#{mgrbean.psnIfDownload == 1}"/>
						<p:commandButton value="返回" icon="ui-icon-close" process="@this" oncomplete="PF('OrgPsnInfoDialog').hide();" />
					</h:panelGrid>
				</p:outputPanel>
				<table width="100%">
					<tr>
						<td style="text-align: left;padding-left: 3px">
							<h:panelGrid columns="7" id="searchPanel">
								<p:outputLabel value="人员类型：" styleClass="zwx_dialog_font" /> 
								<p:selectOneRadio value="#{mgrbean.itmDType}" style="width:135px;">
									<f:selectItem itemLabel="本单位" itemValue="1"></f:selectItem>
									<f:selectItem itemLabel="个人" itemValue="2"></f:selectItem>
								</p:selectOneRadio>
								<p:outputLabel value="人员名称：" styleClass="zwx_dialog_font" /> 
								<p:inputText id="itmDName" value="#{mgrbean.itmDName}" style="width: 160px;" maxlength="25"/>
								<p:outputLabel value="身份证号：" styleClass="zwx_dialog_font"/> 
								<p:inputText value="#{mgrbean.itmDIdc}" style="width: 160px;" maxlength="25"/>
								<p:outputLabel value="（精确查询）" styleClass="zwx_dialog_font" style="color:blue;"/> 
							</h:panelGrid>
					    </td>
					</tr>
				</table>
				<p:dataTable var="itm" value="#{mgrbean.displayDList}" id="dataTable" paginator="true" rows="10" emptyMessage="没有数据！" paginatorPosition="bottom"
							 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
							  lazy="true"
							 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
				>
					<p:column headerText="操作" style="width:100px;padding-left: 10px;">
						<p:commandLink value="选择" action="#{mgrbean.selectPsnAction}" process="@this,:mainForm:tabView:psnDatatable,:mainForm:dataTable" update=":mainForm:dataTable,:mainForm:tabView:psnDatatable"
							rendered="#{itm.ifChoose}">
							<f:setPropertyActionListener value="#{itm}" target="#{mgrbean.tdZwPsninfo}" />
						</p:commandLink>
						<p:spacer width="5" rendered="#{itm.ifChoose}"/>
						<p:commandLink value="修改" process="@this" update=":mainForm:codeEditGrid"  rendered="#{!itm.ifChoose || itm.addSource==mgrbean.zwOrgType}"
							oncomplete="PF('CodeEditDialog').show();" action="#{mgrbean.codeEditAction}">
							<f:setPropertyActionListener value="#{itm}" target="#{mgrbean.tdZwPsninfo}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="删除" action="#{mgrbean.deletePsnAction}" 
							process="@this" update=":mainForm:dataTable"
							rendered="#{mgrbean.itmDType=='1' and (!itm.ifChoose || itm.addSource==mgrbean.zwOrgType)}">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener value="#{itm}" target="#{mgrbean.tdZwPsninfo}" />
						</p:commandLink>
					</p:column>
					<p:column style="width: 80px;text-align: center;" headerText="姓名">
						<h:outputText value="#{itm.empName}" />
					</p:column>
					<p:column style="width: 60px;text-align: center" headerText="性别">
						<h:outputText value="#{itm.sex}" />
					</p:column>
					<p:column style="width: 100px;text-align: center" headerText="出生年月">
						<h:outputText value="#{itm.birthday}" />
					</p:column>
					<p:column style="padding-left:3px;" headerText="职称">
						<h:outputText value="#{itm.fkByTitleId.codeName}" />
					</p:column>
					<p:column style="padding-left:3px;" headerText="职务">
						<h:outputText value="#{itm.position}" />
					</p:column>
				</p:dataTable>
			</p:dialog>
			
			<!--人员维护 -->
			<p:dialog id="codeEditDialog" header="人员信息" widgetVar="CodeEditDialog" resizable="false" width="950" height="600" modal="true" styleClass="psnDiag">
				<p:outputPanel id="buttonsPanel" style="width:905px; height: 40px; margin-top: 0px; position: fixed; top: auto; z-index: 9999; " styleClass="zwx_toobar_42">
					<h:panelGrid columns="5" style="border-color:transparent;padding:0px;padding-top: 1px;">
						<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
						<p:commandButton value="保存" icon="ui-icon-check" id="codeSaveBtn" action="#{mgrbean.executeSavePsnInfo}" process="@this,codeEditDialog" update="dataTable,mainForm:tabView:psnDatatable,mainForm:codeEditGrid,:mainForm:tabView:personSummary"
										 rendered="#{mgrbean.psnDialogType == 1}">
							<f:setPropertyActionListener value="#{mgrbean.zwOrgType}" target="#{mgrbean.psnInfoBase.zwOrgType}"/>
						</p:commandButton>
						<p:spacer width="5" rendered="#{mgrbean.psnDialogType == 1}"/>
						<p:commandButton value="取消" icon="ui-icon-close" id="codeBackBtn" onclick="PF('CodeEditDialog').hide();" immediate="true" rendered="#{mgrbean.psnDialogType == 1}"/>
						<p:commandButton value="关闭" icon="ui-icon-close" id="codeCloseBtn" onclick="PF('CodeEditDialog').hide();" immediate="true" rendered="#{mgrbean.psnDialogType == 2}"/>
					</h:panelGrid>
				</p:outputPanel>
				<p:outputPanel style="width: 100%;height: 40px;" />
				<p:outputPanel id="codeEditGrid" style="width:100%;">
					<ui:param name="psnInfoBase" value="#{mgrbean.psnInfoBase}"></ui:param>
					<ui:param name="psnInfoForm" value="mainForm"></ui:param>
					<ui:param name="ifEdit" value="true"></ui:param>
					<ui:include src="psnInfoValue.xhtml">
						<ui:param name="psnTypeNotNull" value="#{mgrbean.zwOrgType!=3}"></ui:param>
					</ui:include>
				</p:outputPanel>
			</p:dialog>
			<ui:include src="psnInfoDiag.xhtml"></ui:include>

			<p:dialog header="仪器选择" widgetVar="OrgInstInfoDialog" id="orgInstInfoDialog" resizable="false" modal="true" width="800" height="470">
				<p:outputPanel id="deviceSelectButton" styleClass="zwx_toobar_42">
					<h:panelGrid columns="10">
						<span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
						<p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{mgrbean.additmAction}" process="@this"
							update=":mainForm:instEditDialog" oncomplete="PF('InstEditDialog').show();" >
							<p:resetInput target=":mainForm:instEditDialog" />
						</p:commandButton>

<!--						<p:commandButton value="仪器模板下载" icon="ui-icon-arrowthick-1-s" ajax="false" style="margin-right: 6px;"-->
<!--										 onclick="PrimeFaces.monitorDownload(zwx_loading_start, zwx_loading_stop);" >-->
<!--							<p:fileDownload value="#{mgrbean.deviceFile}"/>-->
<!--						</p:commandButton>-->
<!--						<p:commandButton value="仪器信息导入" icon="ui-icon-arrowreturnthick-1-s" update=":mainForm:instinfoDataTable,mainForm:deviceFileDialog"-->
<!--										 process="@this" oncomplete="PF('DeviceFileDialog').show()" />-->
<!--						<p:commandButton value="错误数据下载" icon="ui-icon-arrowthickstop-1-s" ajax="false"-->
<!--										 process="@this" action="#{mgrbean.downloadDeviceErrorFile}" rendered="#{mgrbean.deviceIfDownLoad == 1}"/>-->

						<p:commandButton value="返回" icon="ui-icon-close" process="@this" oncomplete="PF('OrgInstInfoDialog').hide();" />
					</h:panelGrid>
				</p:outputPanel>
				<table width="100%">
					<tr>
						<td style="text-align: left;padding-left: 3px">
							<h:panelGrid columns="10" id="srchInstGrid">
								<p:outputLabel value="仪器名称：" styleClass="zwx_dialog_font" />
								<p:inputText id="itmName" value="#{mgrbean.itmName}" style="width: 160px;" maxlength="25">
									<p:ajax event="keyup" update="instinfoDataTable" process="@this,srchInstGrid" listener="#{mgrbean.instSearch()}" />
								</p:inputText>
								<p:spacer width="5" />
								<p:outputLabel value="仪器型号：" styleClass="zwx_dialog_font" />
								<p:inputText id="itmmodle" value="#{mgrbean.itmmodle}" style="width: 160px;" maxlength="25">
									<p:ajax event="keyup" update="instinfoDataTable" process="@this,srchInstGrid" listener="#{mgrbean.instSearch()}" />
								</p:inputText>
								<p:spacer width="5" />
								<p:outputLabel value="仪器编号：" styleClass="zwx_dialog_font" />
								<p:inputText id="itmCode" value="#{mgrbean.itmCode}" style="width: 160px;" maxlength="25">
									<p:ajax event="keyup" update="instinfoDataTable" process="@this,srchInstGrid" listener="#{mgrbean.instSearch()}" />
								</p:inputText>
							</h:panelGrid>
						</td>
					</tr>
				</table>
				<p:dataTable var="institm" value="#{mgrbean.displayList}" id="instinfoDataTable" paginator="true" rows="10" emptyMessage="没有您要找的记录！"
							 paginatorPosition="bottom"
							 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
							 pageLinks="5" rowsPerPageTemplate="#{'10,20,50'}" lazy="true"
							 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
				>
					<p:column headerText="操作" style="width:15%;padding-left: 10px;">
						<p:commandLink value="选择" action="#{mgrbean.selectInstAction}" process="@this" update="instinfoDataTable,:mainForm:tabView:instDatatable"
							rendered="#{!institm.selected}">
							<f:setPropertyActionListener value="#{institm}" target="#{mgrbean.tdZwInstinfo}" />
						</p:commandLink>
						<p:spacer width="5" rendered="#{!institm.selected}"/>
						<p:commandLink value="修改" process="@this" action="#{mgrbean.modInstInitAction}"
							update=":mainForm:instEditDialog" oncomplete="PF('InstEditDialog').show();">
							<f:setPropertyActionListener value="#{institm}" target="#{mgrbean.tdZwInstinfo}" />
						</p:commandLink>
						<p:spacer width="5" />
						<p:commandLink value="删除" action="#{mgrbean.deleteInstinfoAction}" process="@this" update="instinfoDataTable">
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
							<f:setPropertyActionListener value="#{institm}" target="#{mgrbean.tdZwInstinfo}" />
						</p:commandLink>
					</p:column>
					<p:column style="width: 20%;padding-left: 3px;word-wrap: break-word; word-break: break-all;" headerText="仪器名称">
						<h:outputText value="#{institm.instName}" />
					</p:column>
					<p:column style="width: 18%;text-align: center;word-wrap: break-word; word-break: break-all;" headerText="仪器型号">
						<h:outputText value="#{institm.instModel}" />
					</p:column>
					<p:column style="width: 15%;text-align: center;word-wrap: break-word; word-break: break-all;" headerText="仪器类型">
						<h:outputText value="#{null == institm.fkByInstKindDetalId ? '' : institm.fkByInstKindDetalId.codeName}" />
					</p:column>
					<p:column style="width: 15%;text-align: center;word-wrap: break-word; word-break: break-all;" headerText="仪器编号">
						<h:outputText value="#{institm.instCode}" />
					</p:column>
					<p:column style="width: 10%;text-align: center" headerText="是否外包">
						<p:outputLabel rendered="#{institm.outter == 0}" value="否" />
						<p:outputLabel rendered="#{institm.outter == 1}" value="是" />
					</p:column>
					<!--<p:column style="width: 10%;text-align: center" headerText="是否放射">-->
						<!--<p:outputLabel rendered="#{institm.instType == 0}" value="否" />-->
						<!--<p:outputLabel rendered="#{institm.instType == 1}" value="是" />-->
					<!--</p:column>-->
				</p:dataTable>

			</p:dialog>
            <!--mark-->
			<p:dialog id="instEditDialog" header="仪器信息" widgetVar="InstEditDialog" resizable="false" width="500" height="400" modal="true">
				<p:panelGrid style="width:100%;" id="instEditGrid">
					<p:row >
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
                            <h:outputText value="*" style="color: red;"></h:outputText>
							<h:outputText value="仪器类型：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:column style="text-align:left;padding-left:3px;">
								<p:selectOneMenu   rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}"
													value="#{mgrbean.svrTypeId}"  >
									<f:selectItem itemLabel="--请选择--"/>
									<f:selectItems value="#{mgrbean.svrTypeFsNewList}"/>
									<p:ajax event="change" listener="#{mgrbean.surTypeChange}" process="@this" update="instName"/>
								</p:selectOneMenu>
								<p:selectOneMenu   rendered="#{mgrbean.zwOrgType != 1 and mgrbean.zwOrgType != 3}"  value="#{mgrbean.svrTypeId}"
												panelStyle="overflow-y:auto;#{(mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3)?'':'height:200px;'}">
									<f:selectItem itemLabel="--请选择--"/>
									<f:selectItems value="#{mgrbean.svrTypeList}"/>
									<p:ajax event="change" listener="#{mgrbean.surTypeChange}" process="@this" update="instName"/>
								</p:selectOneMenu>
							</p:column>
						</p:column>
					</p:row>					
					<p:row>
						<p:column style="text-align:right;padding-right:3px;width:40%;height: 35px;">
							<font color="red">*</font>
							<h:outputText value="仪器名称：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText  size="25"
							value="#{mgrbean.instinfo.instName}" maxlength="50" id="instName"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<font color="red">*</font>
							<h:outputText value="仪器型号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px">
							<p:column style="text-align:left;padding-left:3px;">
								<p:inputText value="#{mgrbean.instinfo.instModel}" maxlength="25" size="25"  />
							</p:column>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<font color="red">*</font>
							<h:outputText value="仪器编号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px">
							<p:column style="text-align:left;padding-left:3px;">
								<p:inputText value="#{mgrbean.instinfo.instCode}" maxlength="25" size="25"  />
							</p:column>
						</p:column>
					</p:row>
					<p:row >
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="生产厂家：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:column style="text-align:left;padding-left:3px;">
								<p:inputText value="#{mgrbean.instinfo.instFactory}" maxlength="100" size="25" />
							</p:column>
						</p:column>
					</p:row>
					<p:row >
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="生产日期：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:column style="text-align:left;padding-left:3px;">
								<p:calendar value="#{mgrbean.instinfo.instProDate}" size="11" navigator="true"
                                                yearRange="c-50:c" converterMessage="生产日期格式输入不正确！"
											maxdate="new Date()" pattern="yyyy-MM-dd"
											showButtonPanel="true" showOtherMonths="true"/>
							</p:column>
						</p:column>
					</p:row>		
					<p:row >
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="购置日期：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:column style="text-align:left;padding-left:3px;">
								<p:calendar value="#{mgrbean.instinfo.purchaseDate}" size="11" navigator="true"
                                                yearRange="c-50:c" converterMessage="购置日期格式输入不正确！"
											    maxdate="new Date()" pattern="yyyy-MM-dd"
											showButtonPanel="true" showOtherMonths="true"/>
							</p:column>
						</p:column>
					</p:row>		
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<font color="red">*</font>
							<h:outputText value="是否外包：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:selectOneRadio  value="#{mgrbean.instinfo.outter}">
								<f:selectItem itemValue="1" itemLabel="是" />
								<f:selectItem itemValue="0" itemLabel="否" />
							</p:selectOneRadio>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="序号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:inputText value="#{mgrbean.instinfo.num}" size="10" maxlength="4" onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<font color="red">*</font>
							<h:outputText value="检定要求：" />
							<br/>
							<h:outputText value="（仪器定期校验和检查）"/>
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:selectOneMenu  value="#{mgrbean.instinfo.acptReqId.rid}" id="acptReqId">
								<f:selectItem  itemLabel="--请选择--"/>
								<f:selectItems value="#{mgrbean.acptReqList}" var="acpt" itemLabel="#{acpt.codeName}" itemValue="#{acpt.rid}"/>
								<p:ajax event="change" listener="#{mgrbean.instAcptChangeListener}" process="@this,acptReqId" update="instAcptPanel"/>
							</p:selectOneMenu>
							<p:outputPanel id="instAcptPanel" style="display:flex;float:right;width:160px;height:28px;">
								<p:commandButton value="上传" rendered="#{empty mgrbean.instinfo.checkAnnexName and mgrbean.showInstAcpt}"
												 process="@this"  oncomplete="PF('InstAcptFileUId').show();" update=":mainForm:instAcptFileUId" >
								</p:commandButton>
								<p:commandButton value="查看" rendered="#{mgrbean.instinfo.checkAnnexName!=null and mgrbean.showInstAcpt}"
											   process="@this"  onclick="window.open('/webFile/#{mgrbean.instinfo.checkAnnexName}')" />
								<p:spacer width="5"/>
								<p:commandButton value="删除" rendered="#{mgrbean.instinfo.checkAnnexName!=null and mgrbean.showInstAcpt}"
											   process="@this" update="instAcptPanel" action="#{mgrbean.deleteCheckAnnex}" >
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
								</p:commandButton>
							</p:outputPanel>
						</p:column>
					</p:row>
                    <p:row rendered="#{mgrbean.zwOrgType == 0}">
                        <p:column style="text-align:right;padding-right:3px;height: 35px;">
                            <font color="red">*</font>
                            <h:outputText value="是否支持外检：" />
                        </p:column>
                        <p:column style="text-align:left;padding-left:3px;">
                            <p:selectOneRadio value="#{mgrbean.instinfo.ifSuptOutChk}" >
                                <f:selectItem itemValue="1" itemLabel="是" />
                                <f:selectItem itemValue="0" itemLabel="否" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;height: 35px;">
                            <font color="red">*</font>
                            <h:outputText value="是否需要校验：" />
                        </p:column>
                        <p:column style="text-align:left;padding-left:3px;">
                            <p:selectOneRadio  value="#{mgrbean.instinfo.ifNeedAcpt}" >
                                <f:selectItem itemValue="1" itemLabel="是" />
                                <f:selectItem itemValue="0" itemLabel="否" />
                                <p:ajax event="change" update="@this,recentCheckTip,recentCheckDate,checkCycleTip,checkCycle,checkCycleHidden,waringDate" />
                            </p:selectOneRadio>
                        </p:column>
                    </p:row>
					<p:row >
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
                            <h:outputText id="recentCheckTip" value="#{(mgrbean.instinfo.ifNeedAcpt == 1 or mgrbean.instinfo.ifNeedAcpt == '1') ? '*' : ''} "  style="color: #ff0000;"/>
							<h:outputText value="最近校验日期：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:column style="text-align:left;padding-left:3px;">
								<p:calendar disabled="#{mgrbean.instinfo.ifNeedAcpt == 0 or mgrbean.instinfo.ifNeedAcpt == '0'}" id="recentCheckDate" value="#{mgrbean.instinfo.lastAcptDate}" size="11" navigator="true"
                                                yearRange="c-50:c" converterMessage="最近校验日期格式输入不正确！" readonlyInput="true"
											    maxdate="new Date()" pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true">
                                    <p:ajax event="dateSelect" listener ="#{mgrbean.onDateSelect}" process="@this,checkCycleHidden" update="waringDate"/>

                                </p:calendar>
							</p:column>
						</p:column>
					</p:row>
					<p:row >
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
                            <h:outputText id="checkCycleTip" value="#{(mgrbean.instinfo.ifNeedAcpt == 1 or mgrbean.instinfo.ifNeedAcpt == '1') ? '*' : ''} " style="color: #ff0000;" />
							<h:outputText value="校验周期（年）：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:remoteCommand name="changeWaringDate" action="#{mgrbean.changeWaringDate()}" process="@this,recentCheckDate,checkCycleHidden"
											 update="waringDate" />
							<p:inputText id="checkCycle" disabled="#{mgrbean.instinfo.ifNeedAcpt == 0 or mgrbean.instinfo.ifNeedAcpt == '0'}"
                                         value="#{mgrbean.tdZwInstinfo.lastAcptCircleDot}" style="width: 90px;"
										maxlength="6" onkeyup="SYSTEM.verifyNum(this,4,1)" onblur="SYSTEM.verifyNum(this,4,1); getWaringDate(form);" />
                            <p:spacer width="5px"/>
                            <h:inputHidden id="checkCycleHidden" value="#{mgrbean.tdZwInstinfo.lastAcptCircleDot}"/>
                            <h:outputText id="waringDate" value="#{(mgrbean.instinfo.ifNeedAcpt == 1 or mgrbean.instinfo.ifNeedAcpt == '1') ? mgrbean.waringDate : ''} "
                                          style="color:#ff0000;" />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<font color="red">*</font>
							<h:outputText value="仪器状态：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:selectOneRadio value="#{mgrbean.instinfo.instStateId.rid}" >
								<f:selectItems value="#{mgrbean.instStateList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"/>
							</p:selectOneRadio>
						</p:column>
					</p:row>
                </p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" icon="ui-icon-check" process="@this,instEditGrid" action="#{mgrbean.saveInstAction}" update=":mainForm:tabView:instDatatable,:mainForm:instinfoDataTable">
                            </p:commandButton>
							<p:spacer width="5" />
							<p:commandButton value="取消" icon="ui-icon-close" onclick="PF('InstEditDialog').hide();" immediate="true" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>

            <!-- 人员导入弹出框 -->
            <p:dialog header="人员信息导入" widgetVar="PsnFileDialog" id="psnFileDialog" resizable="false" modal="true"  width="800">
                <table width="100%">
                    <tr>
                        <td style="text-align: right;">
                            <p:outputLabel value="（支持文件格式为：xls、xlsx）" styleClass="blueColorStyle"
                                           style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                        </td>
                    </tr>
                    <tr>
                        <td style="position: relative;top: -23px;">
                            <p:fileUpload  requiredMessage="请选择要文件上传！" styleClass="table-border-none" id="psnFileUpload"
                                           fileUploadListener="#{mgrbean.importPsnAction}" process="@this"
                                           label="选择文件" invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"
                                           allowTypes="/(\.|\/)(xls|xlsx)$/" fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                                           invalidFileMessage="只能上传xls、xlsx格式的文件！"
                                           previewWidth="120" cancelLabel="取消"
                                           uploadLabel="导入" update="@this,:mainForm:dataTable" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"
                                           dragDropSupport="true" mode="advanced" sizeLimit="104857600"/>
                        </td>
                    </tr>
                </table>
            </p:dialog>

<!--			<p:dialog header="仪器信息导入" widgetVar="DeviceFileDialog" id="deviceFileDialog" resizable="false" modal="true"  width="800">-->
<!--                <table width="100%">-->
<!--                    <tr>-->
<!--                        <td style="text-align: right;">-->
<!--                            <p:outputLabel value="（支持文件格式为：xls、xlsx）" styleClass="blueColorStyle"-->
<!--                                           style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>-->
<!--                        </td>-->
<!--                    </tr>-->
<!--                    <tr>-->
<!--                        <td style="position: relative;top: -23px;">-->
<!--                            <p:fileUpload  requiredMessage="请选择要文件上传！" styleClass="table-border-none" id="deviceFileUpload"-->
<!--                                           fileUploadListener="#{mgrbean.importDeviceAction}" process="@this"-->
<!--                                           label="选择文件" invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"-->
<!--                                           allowTypes="/(\.|\/)(xls|xlsx)$/" fileLimit="1" fileLimitMessage="最多只能上传1个文件！"-->
<!--                                           invalidFileMessage="只能上传xls、xlsx格式的文件！"-->
<!--                                           previewWidth="120" cancelLabel="取消"-->
<!--                                           uploadLabel="导入" update=":mainForm:instinfoDataTable" oncomplete="zwx_loading_stop()" onstart="zwx_loading_start()"-->
<!--                                           dragDropSupport="true" mode="advanced" sizeLimit="104857600"/>-->
<!--                        </td>-->
<!--                    </tr>-->
<!--                </table>-->
<!--            </p:dialog>-->
			<p:dialog id="outCarEditDialog" header="外检车辆信息" widgetVar="OutCarEditDialog" resizable="false" width="550" height="#{mgrbean.outCarVersion != '1'?300:425}" modal="true">
				<p:panelGrid style="width:100%;" id="outCarEditGrid">
					<!-- 车牌号（必填）、品牌、型号、车架号、出厂日期、状态（启用、停用）、配套仪器（弹出框选择多选仪器，从资质关联的仪器中选择） -->
					<p:row>
						<p:column style="text-align:right;width:200px;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;"/>
							<h:outputText value="车牌号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:inputText value="#{mgrbean.outcarZzsb.carNo}" maxlength="10" size="25"  />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;" rendered="#{mgrbean.outCarVersion == '1'}"/>
							<h:outputText value="品牌：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:inputText value="#{mgrbean.outcarZzsb.carBand}" maxlength="20" size="25"  />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;" rendered="#{mgrbean.outCarVersion == '1'}"/>
							<h:outputText value="型号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:inputText value="#{mgrbean.outcarZzsb.carStyle}" maxlength="30" size="25"  />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;" rendered="#{mgrbean.outCarVersion == '1'}"/>
							<h:outputText value="车架号：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:inputText value="#{mgrbean.outcarZzsb.carFrameNo}" maxlength="30" size="25"  />
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;" rendered="#{mgrbean.outCarVersion == '1'}"/>
							<h:outputText value="出厂日期：" rendered="#{mgrbean.outCarVersion != '1'}"/>
							<h:outputText value="购置日期：" rendered="#{mgrbean.outCarVersion == '1'}"/>
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:calendar value="#{mgrbean.outcarZzsb.productionDate}" size="11" navigator="true"
										converterMessage="#{mgrbean.outCarVersion != '1'?'出厂':'购置'}日期格式输入不正确！"
										yearRange="c-50:c" maxdate="new Date()" pattern="yyyy-MM-dd"
										showButtonPanel="true" showOtherMonths="true"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;" rendered="#{mgrbean.outCarVersion == '1'}"/>
							<h:outputText value="配套仪器：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
								<p:inputText  style="width:300px;" readonly="true" value="#{mgrbean.outCarInstNames}" id="outCarInstNames"
											  onclick="document.getElementById('mainForm:selMechineLink').click();"/>
								<p:commandLink styleClass="ui-icon ui-icon-search" type="button" style="position: relative;left: -30px;"
											   id="selMechineLink" action="#{mgrbean.initOutCarInstAction}"
											   process="@this" oncomplete="PF('AddOutCarInstDialog').show();" update="selectedMechineTable,searchInstPanel"
											   resetValues="true"
								/>
								<p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
											   action="#{mgrbean.clearOutCarInst}"
											   style="position: relative;left: -33px;"
											   process="@this" update="outCarInstNames"
								>
								</p:commandLink>
							</h:panelGrid>
						</p:column>
					</p:row>
					<p:row rendered="#{mgrbean.outCarVersion == '1'}">
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;"/>
							<h:outputText value="行驶证复印件：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:commandLink value="查看" id="viewDrivingAnnex1" rendered="#{mgrbean.outcarZzsb.drivingAnnexPath != '' and null != mgrbean.outcarZzsb.drivingAnnexPath}"
										   process="@this"  onclick="window.open('/webFile/#{mgrbean.outcarZzsb.drivingAnnexPath}')" />
							<p:commandLink value="上传附件" id="uploadLinkDrivingAnnex1" rendered="#{(mgrbean.outcarZzsb.drivingAnnexPath == '' or null == mgrbean.outcarZzsb.drivingAnnexPath)}"
										   process="@this,outCarEditGrid" update="@this,:mainForm:outcarZzsbFileUploadText,:mainForm:outcarZzsbFileUpload" action="#{mgrbean.showOutCarFileUpload}" >
								<f:setPropertyActionListener target="#{mgrbean.outCarOpFile}" value="#{1}"/>
							</p:commandLink>
							<p:spacer width="5" rendered="#{mgrbean.outcarZzsb.drivingAnnexPath != '' and null != mgrbean.outcarZzsb.drivingAnnexPath}" />
							<p:commandLink value="删除" id="delDrivingAnnex1" rendered="#{mgrbean.outcarZzsb.drivingAnnexPath != '' and null != mgrbean.outcarZzsb.drivingAnnexPath}"
										   process="@this,outCarEditGrid" update="@this,outCarEditGrid" action="#{mgrbean.outCarFileDel}" >
								<f:setPropertyActionListener target="#{mgrbean.outCarOpFile}" value="#{1}"/>
								<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
							</p:commandLink>
						</p:column>
					</p:row>
					<p:row rendered="#{mgrbean.outCarVersion == '1'}">
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;"/>
							<h:outputText value="车辆照片：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:commandLink value="查看" id="viewDrivingAnnex2" rendered="#{mgrbean.outcarZzsb.carPhotoAnnexPath != '' and null != mgrbean.outcarZzsb.carPhotoAnnexPath}"
										   process="@this"  onclick="window.open('/webFile/#{mgrbean.outcarZzsb.carPhotoAnnexPath}')" />
							<p:commandLink value="上传附件" id="uploadLinkDrivingAnnex2" rendered="#{(mgrbean.outcarZzsb.carPhotoAnnexPath == '' or null == mgrbean.outcarZzsb.carPhotoAnnexPath)}"
										   process="@this,outCarEditGrid" update="@this,mainForm:outcarZzsbFileUploadText,mainForm:outcarZzsbFileUpload" action="#{mgrbean.showOutCarFileUpload}" >
								<f:setPropertyActionListener target="#{mgrbean.outCarOpFile}" value="#{2}"/>
							</p:commandLink>
							<p:spacer width="5" rendered="#{mgrbean.outcarZzsb.carPhotoAnnexPath != '' and null != mgrbean.outcarZzsb.carPhotoAnnexPath}" />
							<p:commandLink value="删除" id="delDrivingAnnex2" rendered="#{mgrbean.outcarZzsb.carPhotoAnnexPath != '' and null != mgrbean.outcarZzsb.carPhotoAnnexPath}"
										   process="@this,outCarEditGrid" update="@this,outCarEditGrid" action="#{mgrbean.outCarFileDel}" >
								<f:setPropertyActionListener target="#{mgrbean.outCarOpFile}" value="#{2}"/>
								<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
							</p:commandLink>
						</p:column>
					</p:row>
					<p:row rendered="#{mgrbean.outCarVersion == '1'}">
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;"/>
							<h:outputText value="车载DR照片：" />
						</p:column>
						<p:column style="text-align:left;padding-left:8px;">
							<p:commandLink value="查看" id="viewDrivingAnne3x" rendered="#{mgrbean.outcarZzsb.carDrAnnexPath != '' and null != mgrbean.outcarZzsb.carDrAnnexPath}"
										   process="@this"  onclick="window.open('/webFile/#{mgrbean.outcarZzsb.carDrAnnexPath}')" />
							<p:commandLink value="上传附件" id="uploadLinkDrivingAnnex3" rendered="#{(mgrbean.outcarZzsb.carDrAnnexPath == '' or null == mgrbean.outcarZzsb.carDrAnnexPath)}"
										   process="@this,outCarEditGrid" update="@this,mainForm:outcarZzsbFileUploadText,mainForm:outcarZzsbFileUpload" action="#{mgrbean.showOutCarFileUpload}" >
								<f:setPropertyActionListener target="#{mgrbean.outCarOpFile}" value="#{3}"/>
							</p:commandLink>
							<p:spacer width="5" rendered="#{mgrbean.outcarZzsb.carDrAnnexPath != '' and null != mgrbean.outcarZzsb.carDrAnnexPath}" />
							<p:commandLink value="删除" id="delDrivingAnnex3" rendered="#{mgrbean.outcarZzsb.carDrAnnexPath != '' and null != mgrbean.outcarZzsb.carDrAnnexPath}"
										   process="@this,outCarEditGrid" update="@this,outCarEditGrid" action="#{mgrbean.outCarFileDel}" >
								<f:setPropertyActionListener target="#{mgrbean.outCarOpFile}" value="#{3}"/>
								<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
							</p:commandLink>
						</p:column>
					</p:row>
					<p:row>
						<p:column style="text-align:right;padding-right:3px;height: 35px;">
							<h:outputText value="*" style="color: red;" rendered="#{mgrbean.outCarVersion == '1'}"/>
							<h:outputText value="状态：" />
						</p:column>
						<p:column style="text-align:left;padding-left:3px;">
							<p:selectOneRadio value="#{mgrbean.outcarZzsb.stateMark}"  >
								<f:selectItem itemLabel="启用" itemValue="1" />
								<f:selectItem itemLabel="停用" itemValue="0" />
							</p:selectOneRadio>
						</p:column>
					</p:row>
				</p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" icon="ui-icon-check" process="@this,outCarEditGrid" action="#{mgrbean.submitOutcar}" update=":mainForm:tabView:outCarDatatable">
							</p:commandButton>
							<p:spacer width="5" />
							<p:commandButton value="取消" icon="ui-icon-close" onclick="PF('OutCarEditDialog').hide();" immediate="true" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>

			<p:dialog header="附件上传" widgetVar="OutCarFileDialog" resizable="false" modal="true">
				<table>
					<tr>
						<td style="text-align: right;">
							<p:outputLabel value="（支持附件格式为：图片#{mgrbean.outCarOpFile == 1?'、PDF':''}）"
										   styleClass="blueColorStyle" id="outcarZzsbFileUploadText"
										   style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"/>
						</td>
					</tr>
					<tr>
						<td style="position: relative;top: -23px;">
							<p:fileUpload
									requiredMessage="请选择要上传的文件！" label="文件选择"
									fileUploadListener="#{mgrbean.outCarFileUpload}"
									invalidSizeMessage="文件大小不能超过100M!" id="outcarZzsbFileUpload"
									validatorMessage="上传出错啦，请重新上传！" style="width:600px;"
									previewWidth="120" cancelLabel="取消" update="@this,mainForm:outCarEditGrid"
									uploadLabel="上传" dragDropSupport="true" mode="advanced"
									sizeLimit="104857600" fileLimit="1"
									fileLimitMessage="最多只能上传1个文件！"
									invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png#{mgrbean.outCarOpFile == 1?',pdf':''}类型文件"
									allowTypes="/(\.|\/)(gif|jpe?g|png#{mgrbean.outCarOpFile == 1?'|pdf':''})$/"
							/></td>
					</tr>
				</table>
			</p:dialog>

			<p:dialog header="仪器选择" widgetVar="AddOutCarInstDialog"
					  resizable="false" width="650" height="480" modal="true">
				<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
					<h:panelGrid columns="3"
								 style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span
										class="ui-icon ui-icon-grip-dotted-vertical"/></span>
						<p:commandButton value="确定" icon="ui-icon-check"
										 action="#{mgrbean.sureAddOutCarInst}"
										 process="@this,selectedMechineTable" update="mainForm:outCarInstNames" resetValues="true"/>
						<p:commandButton value="取消" icon="ui-icon-close"
										 onclick="PF('AddOutCarInstDialog').hide();" process="@this" />
					</h:panelGrid>
				</p:outputPanel>
				<table width="100%">
					<tr>
						<td style="text-align: left;padding-left: 3px">
							<h:panelGrid columns="10" id="searchInstPanel">
								<p:outputLabel value="仪器名称：" styleClass="zwx_dialog_font" />
								<p:inputText id="pym" value="#{mgrbean.searchOutCarInstName}" style="width: 160px;" maxlength="25">
									<p:ajax event="keyup" update="selectedMechineTable" process="@this,searchInstPanel" listener="#{mgrbean.searchOutCarInst}" />
								</p:inputText>
								<p:spacer width="5" />
								<p:outputLabel value="仪器型号：" styleClass="zwx_dialog_font" />
								<p:inputText id="pym1" value="#{mgrbean.searchOutCarModel}" style="width: 160px;" maxlength="25">
									<p:ajax event="keyup" update="selectedMechineTable" process="@this,searchInstPanel" listener="#{mgrbean.searchOutCarInst}" />
								</p:inputText>
							</h:panelGrid>
						</td>
					</tr>
				</table>
				<p:dataTable var="itm" value="#{mgrbean.outCarInstList}" id="selectedMechineTable"
							 paginator="true" rows="#{10}"
							 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
							 rowsPerPageTemplate="#{10}" lazy="true" emptyMessage="暂无数据！"
							 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
							 paginatorPosition="bottom">
					<p:column headerText="选择" style="text-align:center;width:30px;">
						<p:selectBooleanCheckbox value="#{itm.selected}">
							<p:ajax event="change" listener="#{mgrbean.selectCarInstListAction(itm)}" process="@this" />
						</p:selectBooleanCheckbox>
					</p:column>
					<p:column headerText="仪器名称" style="text-align: center">
						<h:outputText value="#{itm.instName}"/>
					</p:column>
					<p:column headerText="仪器型号" style="text-align: center">
						<h:outputText value="#{itm.instModel}"/>
					</p:column>
					<p:column headerText="仪器编号" style="text-align: center">
						<h:outputText value="#{itm.instCode}"/>
					</p:column>
				</p:dataTable>
			</p:dialog>
			<p:dialog header="文件上传" widgetVar="InstAcptFileUId" id="instAcptFileUId" resizable="false" modal="true">
				<table>
					<tr>
						<td style="text-align: right;"><p:outputLabel
								value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
								style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
						</td>
					</tr>
					<tr>
						<td style="position: relative;top: -23px;">
							<p:fileUpload requiredMessage="请选择上传文件！" style="width:700px;" previewWidth="120"
										  fileUploadListener="#{mgrbean.handleAcptFileUpload}"
										  label="选择文件" uploadLabel="上传" cancelLabel="取消"
										  fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
										  sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
										  validatorMessage="上传出错啦，重新上传！"
										  invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
										  process="@this" update="@this,instAcptPanel"
										  mode="advanced" dragDropSupport="true"
										  allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
						</td>
					</tr>
				</table>
			</p:dialog>
			<!--退回原因-->
			<p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
					  height="300" header="退回原因" resizable="false" modal="true">
				<h:inputText
						style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
				<p:inputTextarea value="#{mgrbean.zwOrginfoEntity.auditAdv}"
								 style="resize:none;width:97%;height:95%;" autoResize="false" readonly="true"
								 id="reasonContent"/>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: right;">
						<h:panelGroup>
							<p:commandButton value="关闭" onclick="PF('ReasonDialog').hide();"
											 process="@this" immediate="true" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>

			<!-- 学术培训信息弹框 -->
			<p:dialog id="supportTrainDialog" header="学术培训信息"
					  widgetVar="SupportTrainDialog" resizable="false" width="600"
					  height="390" modal="true">
				<p:panelGrid style="width:100%;" id="trainGrid" >
					<p:row>
						<p:column style="height:28px;text-align:center;" styleClass="ui-state-default" colspan="2">
							<p:outputLabel value="学术培训类别"/>
						</p:column>
						<p:column style="text-align:center;" styleClass="ui-state-default">
							<p:outputLabel value="*" style="color: red;"/><p:outputLabel value="参与人数"/>
						</p:column>
						<p:column style="text-align:center;" styleClass="ui-state-default">
							<p:outputLabel value="培训证明材料"/>
						</p:column>
					</p:row>
					<c:forEach items="#{mgrbean.supportTrainDiagDataList}" var="itm">
						<c:forEach var="subItm" items="#{itm.trainList}" varStatus="subItmStatus">
							<p:row>
								<c:if test="#{subItmStatus.index == 0}">
									<p:column style="text-align: center; vertical-align: middle; width: 20%;" rowspan="#{itm.rowSpan}">
										<p:outputLabel value="#{itm.simpleCode.codeName}"/>
									</p:column>
								</c:if>
								<p:column style="height:28px;text-align: center; width: 20%;">
									<p:outputLabel value="#{subItm.fkByTrainId.codeName}"/>
								</p:column>
								<p:column style="text-align: center; width: 30%;">
									<p:inputText style="width: 80%;" value="#{subItm.partakeNumber}" onkeyup="SYSTEM.verifyNum3(this, 3, 0, false)" onblur="SYSTEM.verifyNum3(this, 3, 0, true)" />
								</p:column>
								<p:column style="padding-left: 10px; ">
									<p:commandLink value="查看" rendered="#{subItm.evidenceFilePath != '' and null != subItm.evidenceFilePath}"
												   process="@this"  onclick="window.open('/webFile/#{subItm.evidenceFilePath}')" />
									<p:commandLink value="上传" rendered="#{subItm.evidenceFilePath == '' or null == subItm.evidenceFilePath}"
												   process="@this,:mainForm:trainGrid" oncomplete="PF('SupportTrainFileDiag').show();" update=":mainForm:supportTrainFileDiag" >
										<f:setPropertyActionListener target="#{mgrbean.editSupportTrain}" value="#{subItm}"/>
									</p:commandLink>
									<p:spacer width="5" rendered="#{subItm.evidenceFilePath != '' and null != subItm.evidenceFilePath}" />
									<p:commandLink value="删除" rendered="#{subItm.evidenceFilePath != '' and null != subItm.evidenceFilePath}"
												   process="@this,:mainForm:trainGrid" update="@this,:mainForm:trainGrid" action="#{mgrbean.removeSupportTrainFilePath}" >
										<f:setPropertyActionListener target="#{mgrbean.editSupportTrain}" value="#{subItm}"/>
									</p:commandLink>
								</p:column>
							</p:row>
						</c:forEach>
					</c:forEach>
				</p:panelGrid>
				<f:facet name="footer">
					<h:panelGrid style="width: 100%;text-align: center;">
						<h:panelGroup>
							<p:commandButton value="保存" icon="ui-icon-check"
											 action="#{mgrbean.saveOrUpdateSupportTrain}"
											 process="@this,supportTrainDialog,mainForm:trainGrid"/>
							<p:spacer width="5" />
							<p:commandButton value="取消" icon="ui-icon-close"
											 onclick="PF('SupportTrainDialog').hide();"
											 immediate="true" />
						</h:panelGroup>
					</h:panelGrid>
				</f:facet>
			</p:dialog>

			<!-- 学术培训信息文件上传 -->
			<p:dialog header="文件上传" widgetVar="SupportTrainFileDiag" id="supportTrainFileDiag" resizable="false" modal="true">
				<table>
					<tr>
						<td style="text-align: right;"><p:outputLabel
								value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
								style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;" />
						</td>
					</tr>
					<tr>
						<td style="position: relative;top: -23px;">
							<p:fileUpload requiredMessage="请选择上传文件！" style="width:700px;" previewWidth="120"
										  fileUploadListener="#{mgrbean.handleSupportTrainFileUpload}"
										  label="选择文件" uploadLabel="上传" cancelLabel="取消"
										  fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
										  sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
										  validatorMessage="上传出错啦，重新上传！"
										  invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
										  process="@this" update="@this,:mainForm:trainGrid"
										  mode="advanced" dragDropSupport="true"
										  allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
						</td>
					</tr>
				</table>
			</p:dialog>
	    	<!--能力检测-->
			<p:dialog id="abilityDetectionDialog" header="检测能力"
					  widgetVar="AbilityDetectionDialog" resizable="false" width="700"
					  height="560" modal="true" rendered="#{mgrbean.zwOrgType == 3}">
				<p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
					<h:panelGrid columns="3"
								 style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span
										class="ui-icon ui-icon-grip-dotted-vertical"/></span>
						<p:commandButton value="保存" icon="ui-icon-check"
										 action="#{mgrbean.saveAbilityAction}"
										 process="@this,abilityDetectionDialog,:mainForm:tabView:chkAbilityTable,:mainForm:tabView:chkAbilityGrid" >
						</p:commandButton>
						<p:commandButton value="取消" icon="ui-icon-close"
										 onclick="PF('AbilityDetectionDialog').hide();" process="@this" />
					</h:panelGrid>
				</p:outputPanel>
				<p:panelGrid style="width:100%;margin-top:5px;margin-bottom:5px;" id="abilityInfo">
					<p:row>
						<p:column styleClass="cs-scl-first">
							<p:inputText style="visibility: hidden;width: 0px;"/>
							<h:outputText styleClass="cs-required" value="检测项目名称：" />
						</p:column>
						<p:column styleClass="cs-scv" style="padding-left: 3px !important;">
							<h:panelGrid columns="4" id="checkPro"
										 style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
								<p:outputLabel id="checkProName" value="#{mgrbean.tbYsjcChkAbilityRel.fkByItemId.codeName}" escape="false"
											   onclick="$('#mainForm\\:selCheckProLink').click();"
											   styleClass="ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all"
											   style="float: left;width:400px;height: 15px;overflow: hidden;"/>
								<p:commandLink styleClass="ui-icon ui-icon-search"
											   id="selCheckProLink"
											   process="@this" action="#{mgrbean.selectCheckProAction}"
											   style="position: relative;left: -30px;">
									<p:ajax event="dialogReturn"
											process="@this" listener="#{mgrbean.onCheckProSearch}"
											resetValues="true" update="checkPro" />
								</p:commandLink>

							</h:panelGrid>
						</p:column>
					</p:row>

					<p:row>
						<p:column styleClass="cs-scl-first">
							<h:outputText styleClass="cs-required" value="检测依据：" />
						</p:column>
						<p:column styleClass="cs-scv" style="padding-left: 3px !important;">
							<h:panelGrid columns="4" id="checkBasis"
										 style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
								<p:inputText readonly="true" style="width:400px"
											 id="checkBasisCode" maxlength="25" value="#{mgrbean.tbYsjcChkAbilityRel.fkByJcBaseId.lawCode}"
											 onclick="$('#mainForm\\:selCheckBasisLink').click();"/>
								<p:commandLink styleClass="ui-icon ui-icon-search"
											   id="selCheckBasisLink"  process="@this" action="#{mgrbean.selectCheckBasisAction}"
											   style="position: relative;left: -30px;">
									<p:ajax event="dialogReturn"
											process="@this" listener="#{mgrbean.onCheckBasisSearch}"
											resetValues="true" update="checkBasis,jsWay" />
								</p:commandLink>
							</h:panelGrid>
						</p:column>
					</p:row>
					<p:row>
						<p:column styleClass="cs-scl-first" >
							<h:outputText styleClass="cs-required" value="检测方法：" />
						</p:column>
						<p:column styleClass="cs-scv" >
							<p:inputText id="jsWay" style="width: 400px" maxlength="50" value="#{mgrbean.tbYsjcChkAbilityRel.jcWay}"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column styleClass="cs-scl-first" >
							<h:outputText value="限制说明：" />
						</p:column>
						<p:column styleClass="cs-scv" >
							<p:inputTextarea rows="4" autoResize="false"
											 style="resize: none;width: 400px;height: 100px;"
											 value="#{mgrbean.tbYsjcChkAbilityRel.limitDesc}" maxlength="500"/>
						</p:column>
					</p:row>
					<p:row>
						<p:column styleClass="cs-scl-first" >
							<h:outputText value="方法验证、确认或论证：" />
						</p:column>
						<p:column styleClass="cs-scv" >
							<p:selectManyCheckbox value="#{mgrbean.chkMethodRids}">
								<f:selectItems value="#{mgrbean.chkMethodList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" />
							</p:selectManyCheckbox>
						</p:column>
					</p:row>
					<p:row>
						<p:column styleClass="cs-scl-first">
							<h:outputText styleClass="cs-required" value="实验室认可标记（CNAS）：" />
						</p:column>
						<p:column styleClass="cs-scv">
							<p:selectOneRadio style="width: 140px" value="#{mgrbean.tbYsjcChkAbilityRel.labApprovalTag}" >
								<f:selectItem itemLabel="是" itemValue="1"/>
								<f:selectItem itemLabel="否" itemValue="0"/>
							</p:selectOneRadio>
						</p:column>
					</p:row>
					<p:row>
						<p:column styleClass="cs-scl-first">
							<h:outputText styleClass="cs-required" value="资质认定标记（CMA）：" />
						</p:column>
						<p:column styleClass="cs-scv" >
							<p:selectOneRadio style="width: 140px" value="#{mgrbean.tbYsjcChkAbilityRel.qualApprovalTag}">
								<f:selectItem itemLabel="是" itemValue="1"/>
								<f:selectItem itemLabel="否" itemValue="0"/>
							</p:selectOneRadio>
						</p:column>
					</p:row>
					<p:row>
						<p:column styleClass="cs-scl-first">
							<h:outputText styleClass="cs-required" value="外包标记：" />
						</p:column>
						<p:column styleClass="cs-scv" >
							<p:selectOneRadio style="width: 140px" value="#{mgrbean.tbYsjcChkAbilityRel.outTag}">
								<f:selectItem itemLabel="是" itemValue="1"/>
								<f:selectItem itemLabel="否" itemValue="0"/>
							</p:selectOneRadio>
						</p:column>
					</p:row>
					<p:row>
						<p:column styleClass="cs-scl-first">
							<h:outputLabel styleClass="cs-required" value="状态：" />
						</p:column>
						<p:column styleClass="cs-scv" >
							<p:selectOneRadio style="width: 140px"  value="#{mgrbean.tbYsjcChkAbilityRel.state}">
								<f:selectItem itemLabel="启用" itemValue="1" />
								<f:selectItem itemLabel="停用" itemValue="0" />
							</p:selectOneRadio>
						</p:column>
					</p:row>
				</p:panelGrid>
			</p:dialog>
			<ui:insert name="insertDialogs"/>
			
			<h:inputHidden id="selItemId" value="#{mgrbean.selItemId}" /> 
			
			<div id="label"
				style="position:absolute;z-index:100;display:none;width:150px;">
			<div id="triangle-up"></div>
			<div
				style="border-radius:5px;padding:10px;background:#4D4D4D;text-align:left;color:white;word-wrap: break-word;"
				id="descDiv"></div>
			</div>
			
        </h:form>
		<ui:include src="/WEB-INF/templates/system/confirm.xhtml"></ui:include>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"></ui:include>
        <ui:include src="/WEB-INF/templates/system/ajaxStatus.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"></ui:include>
		<ui:include src="/WEB-INF/templates/system/hideTooltips.xhtml"/>
    </h:body>
</f:view>
</html>



