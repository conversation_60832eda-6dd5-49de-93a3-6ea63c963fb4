<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
    <!--@elvariable id="orgApplyBean" type="com.chis.modules.heth.zzsb.web.TdZwQualOrgApplyInfoBaseBean"-->
    <style>
        table.ui-selectoneradio td label{
            white-space:nowrap;
            overflow: hidden;
        }
    </style>
    <p:dataTable var="outCarItm" value="#{orgApplyBean.tjorgOutcarApplyList}"
                 id="outCarDatatable1"
                 emptyMessage="没有您要找的记录！" paginator="true" rows="#{20}" paginatorPosition="bottom" rowIndexVar="R"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                 rowsPerPageTemplate="#{'10,20,50'}"  lazy="true"
                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                 style="margin-top:5px;width:100%">
        <p:column headerText="车牌号" style="padding-left: 3px;text-align:center;width:10%;">
            <p:outputLabel value="#{outCarItm[1]}"/>
        </p:column>
        <p:column headerText="品牌" style="text-align:center;width:10%;">
            <p:outputLabel value="#{outCarItm[2]}"/>
        </p:column>
        <p:column headerText="型号" style="text-align:center;width:10%;">
            <p:outputLabel value="#{outCarItm[3]}"/>
        </p:column>
        <p:column headerText="车架号" style="text-align:center;width:15%;">
            <p:outputLabel value="#{outCarItm[4]}"/>
        </p:column>
        <p:column headerText="#{mgrbean.applyInfoBaseBean.outCarVersion != '1'?'出厂':'购置'}日期" style="text-align:center;width:10%;">
            <p:outputLabel value="#{outCarItm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </p:outputLabel>
        </p:column>
        <p:column headerText="配套仪器" style="width:20%;" >
            <!-- 如果缺少id instNamesId 不生效 -->
            <h:outputLabel id="instNamesId"
                           value="#{outCarItm[8]}"
                           style="cursor: pointer;text-align: left; word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;color:#25AAE1">
                <p:tooltip for="@parent" value="#{outCarItm[8]}"  style="width:300px;word-break:break-all;word-wrap:break-word;" />
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="text-align:center;width:5%;" >
            <p:outputLabel value="启用" rendered="#{null ne outCarItm[6] and 1 == outCarItm[6]}"/>
            <p:outputLabel value="停用" rendered="#{null ne outCarItm[6] and 0 == outCarItm[6]}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left:5px;"  rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
            <p:commandLink value="修改" process="@this"
                           action="#{orgApplyBean.modOutcar}" update=":tabView:editForm:orgTjApplyTabView:outCarEditDialog">
                <f:setPropertyActionListener target="#{orgApplyBean.modOutcarZzsb}" value="#{outCarItm}"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" process="@this" update=":tabView:editForm:orgTjApplyTabView:outCarDatatable1"
                           action="#{orgApplyBean.delOutcar}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                <f:setPropertyActionListener target="#{orgApplyBean.modOutcarZzsb}" value="#{outCarItm}"/>
            </p:commandLink>
        </p:column>
    </p:dataTable>

    <p:dialog id="outCarEditDialog" header="外检车辆信息" widgetVar="OutCarEditDialog" resizable="false" width="550" height="#{orgApplyBean.outCarVersion != '1'?300:425}" modal="true">
        <p:panelGrid style="width:100%;" id="outCarEditGrid">
            <!-- 车牌号（必填）、品牌、型号、车架号、出厂日期、状态（启用、停用）、配套仪器（弹出框选择多选仪器，从资质关联的仪器中选择） -->
            <p:row>
                <p:column style="text-align:right;width:200px;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="车牌号：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:inputText value="#{orgApplyBean.outcarZzsb.carNo}" maxlength="10" size="25"  />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;" rendered="#{orgApplyBean.outCarVersion == '1'}"/>
                    <h:outputText value="品牌：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:inputText value="#{orgApplyBean.outcarZzsb.carBand}" maxlength="20" size="25"  />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;" rendered="#{orgApplyBean.outCarVersion == '1'}"/>
                    <h:outputText value="型号：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:inputText value="#{orgApplyBean.outcarZzsb.carStyle}" maxlength="30" size="25"  />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;" rendered="#{orgApplyBean.outCarVersion == '1'}"/>
                    <h:outputText value="车架号：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:inputText value="#{orgApplyBean.outcarZzsb.carFrameNo}" maxlength="30" size="25"  />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;" rendered="#{orgApplyBean.outCarVersion == '1'}"/>
                    <h:outputText value="出厂日期：" rendered="#{orgApplyBean.outCarVersion != '1'}"/>
                    <h:outputText value="购置日期：" rendered="#{orgApplyBean.outCarVersion == '1'}"/>
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:calendar value="#{orgApplyBean.outcarZzsb.productionDate}" size="11" navigator="true"
                                yearRange="c-50:c" converterMessage="#{orgApplyBean.outCarVersion != '1'?'出厂':'购置'}日期格式输入不正确！"
                                maxdate="new Date()" pattern="yyyy-MM-dd"
                                showButtonPanel="true" showOtherMonths="true"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;" rendered="#{orgApplyBean.outCarVersion == '1'}"/>
                    <h:outputText value="配套仪器：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <h:panelGrid columns="3" style="border-color: #ffffff;margin: 0px;padding: 0px;">
                        <p:inputText  style="width:300px;" readonly="true" value="#{orgApplyBean.outCarInstNames}" id="outCarInstNames"
                                      onclick="document.getElementById('tabView:editForm:orgTjApplyTabView:selMechineLink').click();"/>
                        <p:commandLink styleClass="ui-icon ui-icon-search" type="button" style="position: relative;left: -30px;"
                                       id="selMechineLink" action="#{orgApplyBean.initOutCarInstAction}"
                                       process="@this" oncomplete="PF('AddOutCarInstDialog').show();" update="searchInstPanel,selectedMechineTable"
                                       resetValues="true"
                        />
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       action="#{orgApplyBean.clearOutCarInst}"
                                       style="position: relative;left: -33px;"
                                       process="@this" update="outCarInstNames"
                        >
                        </p:commandLink>
                    </h:panelGrid>
                </p:column>
            </p:row>
            <p:row rendered="#{orgApplyBean.outCarVersion == '1'}">
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;"/>
                    <h:outputText value="行驶证复印件：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:commandLink value="查看" id="viewDrivingAnnex1" rendered="#{orgApplyBean.outcarZzsb.drivingAnnexPath != '' and null != orgApplyBean.outcarZzsb.drivingAnnexPath}"
                                   process="@this"  onclick="window.open('/webFile/#{orgApplyBean.outcarZzsb.drivingAnnexPath}')" />
                    <p:commandLink value="上传附件" id="uploadLinkDrivingAnnex1" rendered="#{(orgApplyBean.outcarZzsb.drivingAnnexPath == '' or null == orgApplyBean.outcarZzsb.drivingAnnexPath)}"
                                   process="@this,outCarEditGrid" update="@this,:tabView:editForm:orgTjApplyTabView:outcarZzsbFileUploadText,:tabView:editForm:orgTjApplyTabView:outcarZzsbFileUpload" action="#{orgApplyBean.showOutCarFileUpload}" >
                        <f:setPropertyActionListener target="#{orgApplyBean.outCarOpFile}" value="#{1}"/>
                    </p:commandLink>
                    <p:spacer width="5" rendered="#{orgApplyBean.outcarZzsb.drivingAnnexPath != '' and null != orgApplyBean.outcarZzsb.drivingAnnexPath}" />
                    <p:commandLink value="删除" id="delDrivingAnnex1" rendered="#{orgApplyBean.outcarZzsb.drivingAnnexPath != '' and null != orgApplyBean.outcarZzsb.drivingAnnexPath}"
                                   process="@this,outCarEditGrid" update="@this,outCarEditGrid" action="#{orgApplyBean.outCarFileDel}" >
                        <f:setPropertyActionListener target="#{orgApplyBean.outCarOpFile}" value="#{1}"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                    </p:commandLink>
                </p:column>
            </p:row>
            <p:row rendered="#{orgApplyBean.outCarVersion == '1'}">
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;"/>
                    <h:outputText value="车辆照片：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:commandLink value="查看" id="viewDrivingAnnex2" rendered="#{orgApplyBean.outcarZzsb.carPhotoAnnexPath != '' and null != orgApplyBean.outcarZzsb.carPhotoAnnexPath}"
                                   process="@this"  onclick="window.open('/webFile/#{orgApplyBean.outcarZzsb.carPhotoAnnexPath}')" />
                    <p:commandLink value="上传附件" id="uploadLinkDrivingAnnex2" rendered="#{(orgApplyBean.outcarZzsb.carPhotoAnnexPath == '' or null == orgApplyBean.outcarZzsb.carPhotoAnnexPath)}"
                                   process="@this,outCarEditGrid" update="@this,:tabView:editForm:orgTjApplyTabView:outcarZzsbFileUploadText,:tabView:editForm:orgTjApplyTabView:outcarZzsbFileUpload" action="#{orgApplyBean.showOutCarFileUpload}" >
                        <f:setPropertyActionListener target="#{orgApplyBean.outCarOpFile}" value="#{2}"/>
                    </p:commandLink>
                    <p:spacer width="5" rendered="#{orgApplyBean.outcarZzsb.carPhotoAnnexPath != '' and null != orgApplyBean.outcarZzsb.carPhotoAnnexPath}" />
                    <p:commandLink value="删除" id="delDrivingAnnex2" rendered="#{orgApplyBean.outcarZzsb.carPhotoAnnexPath != '' and null != orgApplyBean.outcarZzsb.carPhotoAnnexPath}"
                                   process="@this,outCarEditGrid" update="@this,outCarEditGrid" action="#{orgApplyBean.outCarFileDel}" >
                        <f:setPropertyActionListener target="#{orgApplyBean.outCarOpFile}" value="#{2}"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                    </p:commandLink>
                </p:column>
            </p:row>
            <p:row rendered="#{orgApplyBean.outCarVersion == '1'}">
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;"/>
                    <h:outputText value="车载DR照片：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:commandLink value="查看" id="viewDrivingAnnex3" rendered="#{orgApplyBean.outcarZzsb.carDrAnnexPath != '' and null != orgApplyBean.outcarZzsb.carDrAnnexPath}"
                                   process="@this"  onclick="window.open('/webFile/#{orgApplyBean.outcarZzsb.carDrAnnexPath}')" />
                    <p:commandLink value="上传附件" id="uploadLinkDrivingAnnex3" rendered="#{(orgApplyBean.outcarZzsb.carDrAnnexPath == '' or null == orgApplyBean.outcarZzsb.carDrAnnexPath)}"
                                   process="@this,outCarEditGrid" update="@this,:tabView:editForm:orgTjApplyTabView:outcarZzsbFileUploadText,:tabView:editForm:orgTjApplyTabView:outcarZzsbFileUpload" action="#{orgApplyBean.showOutCarFileUpload}" >
                        <f:setPropertyActionListener target="#{orgApplyBean.outCarOpFile}" value="#{3}"/>
                    </p:commandLink>
                    <p:spacer width="5" rendered="#{orgApplyBean.outcarZzsb.carDrAnnexPath != '' and null != orgApplyBean.outcarZzsb.carDrAnnexPath}" />
                    <p:commandLink value="删除" id="delDrivingAnnex3" rendered="#{orgApplyBean.outcarZzsb.carDrAnnexPath != '' and null != orgApplyBean.outcarZzsb.carDrAnnexPath}"
                                   process="@this,outCarEditGrid" update="@this,outCarEditGrid" action="#{orgApplyBean.outCarFileDel}" >
                        <f:setPropertyActionListener target="#{orgApplyBean.outCarOpFile}" value="#{3}"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                    </p:commandLink>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="*" style="color: red;" rendered="#{orgApplyBean.outCarVersion == '1'}"/>
                    <h:outputText value="状态：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneRadio value="#{orgApplyBean.outcarZzsb.stateMark}"  >
                        <f:selectItem itemLabel="启用" itemValue="1" />
                        <f:selectItem itemLabel="停用" itemValue="0" />
                    </p:selectOneRadio>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" process="@this,outCarEditGrid" action="#{orgApplyBean.submitOutcar}" update=":tabView:editForm:orgTjApplyTabView:outCarDatatable1">
                    </p:commandButton>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('OutCarEditDialog').hide();" immediate="true" />
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>

    <p:dialog header="附件上传" widgetVar="OutCarFileDialog" resizable="false" modal="true">
        <table>
            <tr>
                <td style="text-align: right;">
                    <p:outputLabel value="（支持附件格式为：图片#{orgApplyBean.outCarOpFile == 1?'、PDF':''}）"
                                   styleClass="blueColorStyle" id="outcarZzsbFileUploadText"
                                   style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                </td>
            </tr>
            <tr>
                <td style="position: relative;top: -23px;">
                    <p:fileUpload
                            requiredMessage="请选择要上传的文件！" label="文件选择"
                            fileUploadListener="#{orgApplyBean.outCarFileUpload}"
                            invalidSizeMessage="文件大小不能超过100M!" id="outcarZzsbFileUpload"
                            validatorMessage="上传出错啦，请重新上传！" style="width:600px;"
                            previewWidth="120" cancelLabel="取消" update="@this,:tabView:editForm:orgTjApplyTabView:outCarEditGrid"
                            uploadLabel="上传" dragDropSupport="true" mode="advanced"
                            sizeLimit="104857600" fileLimit="1"
                            fileLimitMessage="最多只能上传1个文件！"
                            invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png#{orgApplyBean.outCarOpFile == 1?',pdf':''}类型文件"
                            allowTypes="/(\.|\/)(gif|jpe?g|png#{orgApplyBean.outCarOpFile == 1?'|pdf':''})$/"
                    /></td>
            </tr>
        </table>
    </p:dialog>

    <p:dialog header="仪器选择" widgetVar="AddOutCarInstDialog"
              resizable="false" width="650" height="480" modal="true">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3"
                         style="border-color:transparent;padding:0;">
                                <span class="ui-separator"><span
                                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="确定" icon="ui-icon-check"
                                 action="#{orgApplyBean.sureAddOutCarInst}"
                                 process="@this,selectedMechineTable" update="outCarInstNames" resetValues="true"/>
                <p:commandButton value="取消" icon="ui-icon-close"
                                 onclick="PF('AddOutCarInstDialog').hide();" process="@this" />
            </h:panelGrid>
        </p:outputPanel>
        <table width="100%">
            <tr>
                <td style="text-align: left;padding-left: 3px">
                    <h:panelGrid columns="10" id="searchInstPanel">
                        <p:outputLabel value="仪器名称：" styleClass="zwx_dialog_font" />
                        <p:inputText id="pym" value="#{orgApplyBean.searchOutCarInstName}" style="width: 160px;" maxlength="25">
                            <p:ajax event="keyup" update="selectedMechineTable" process="@this,searchInstPanel" listener="#{orgApplyBean.searchOutCarInst}" />
                        </p:inputText>
                        <p:spacer width="5" />
                        <p:outputLabel value="仪器型号：" styleClass="zwx_dialog_font" />
                        <p:inputText id="pym1" value="#{orgApplyBean.searchOutCarModel}" style="width: 160px;" maxlength="25">
                            <p:ajax event="keyup" update="selectedMechineTable" process="@this,searchInstPanel" listener="#{orgApplyBean.searchOutCarInst}" />
                        </p:inputText>
                    </h:panelGrid>
                </td>
            </tr>
        </table>
        <p:dataTable var="itm" value="#{orgApplyBean.outCarInstList}" id="selectedMechineTable"
                     paginator="true" rows="#{10}"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                     rowsPerPageTemplate="#{10}" lazy="true" emptyMessage="暂无数据！"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                     paginatorPosition="bottom">
            <p:column headerText="选择" style="text-align:center;width:30px;">
                <p:selectBooleanCheckbox value="#{itm.selected}">
                    <p:ajax event="change" listener="#{orgApplyBean.selectCarInstListAction(itm)}" process="@this" />
                </p:selectBooleanCheckbox>
            </p:column>
            <p:column headerText="仪器名称" style="text-align: center">
                <h:outputText value="#{itm.instName}"/>
            </p:column>
            <p:column headerText="仪器型号" style="text-align: center">
                <h:outputText value="#{itm.instModel}"/>
            </p:column>
            <p:column headerText="仪器编号" style="text-align: center">
                <h:outputText value="#{itm.instCode}"/>
            </p:column>
        </p:dataTable>
    </p:dialog>

</ui:composition>