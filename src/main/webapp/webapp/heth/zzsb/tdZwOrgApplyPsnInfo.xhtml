<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwQualAgencyFilingAppBean"-->
    <!--@elvariable id="orgApplyBean" type="com.chis.modules.heth.zzsb.web.TdZwQualOrgApplyInfoBaseBean"-->
    <style type="text/css">
        .psnDiag .ui-widget-content {
            margin: 0.5em 0;
            padding: 0 1em;
        }

        #triangle-up {
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 10px solid #4D4D4D;
            position: relative;
            left: 100px;
            top: 3px;
        }

        .zwx-tooltip {
            -webkit-line-clamp: 1;
        }

    </style>
    <script type="text/javascript">
        //<![CDATA[
        var show = false;
        var target;
        jQuery(function () {
            jQuery("body").mousemove(
                function (e) {
                    if (show) {
                        var label = document.getElementById("label");
                        if (e.pageX < jQuery(label).offset().left
                            || e.pageX > (jQuery(label).offset().left + jQuery(label).width())
                            || e.pageY < (jQuery(target).offset().top)
                            || e.pageY > (jQuery(label).offset().top + jQuery(label).height())) {
                            jQuery(label).fadeOut("6000",
                                function () {
                                    show = false;
                                });
                        }
                    }
                });
        });

        function getLabelPanel(div, desc) {
            if (!show) {
                var label = document.getElementById("label");
                jQuery(label).css({
                    "display": "none"
                });
                jQuery(label).css(
                    {
                        "top": jQuery(div).offset().top + jQuery(div).height() + 3,
                        "left": jQuery(div).offset().left - (jQuery(label).width() / 2 + 20)
                    });
                var str = "";
                var code = desc.split(",");
                if (code.length > 0) {
                    for (var int = 0; int < code.length; int++) {
                        str = str + code[int] + "<br/>";
                    }
                }
                jQuery("#descDiv").html(str);
                jQuery(label).fadeIn("6000", function () {
                    target = div;
                    show = true;
                });
            }
        }

        //]]>
    </script>
    <!--机构人员-->
    <p:dataTable var="itm" value="#{orgApplyBean.psnInfoVO.orgPsnApplyListView}"
                 id="psnDatatable" emptyMessage="没有您要找的记录！" paginator="true" rows="#{mgrbean.pageSize}"
                 paginatorPosition="bottom" rowIndexVar="R"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                 rowsPerPageTemplate="#{mgrbean.perPageSize}" lazy="true" pageLinks="5"
                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                 style="margin-top:5px;width:100%">
        <p:column headerText="姓名" style="text-align:center;width:80px;">
            <p:commandLink value="#{itm.fkByEmpId.empName}" update=":tabView:editForm:orgTjApplyTabView:psnEditDialog"
                           process="@this" oncomplete="PF('PsnEditDialog').show();"
                           action="#{orgApplyBean.viewPsnInfoAction}">
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.rid}"
                                             value="#{itm.rid}"/>
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.fkByEmpId.rid}"
                                             value="#{itm.fkByEmpId.rid}"/>
            </p:commandLink>
        </p:column>
        <p:column headerText="性别" style="text-align:center;width:40px;">
            <p:outputLabel value="#{itm.fkByEmpId.sex}"/>
        </p:column>
        <p:column headerText="职称" style="text-align:center;width:100px;">
            <p:outputLabel value="#{itm.fkByEmpId.fkByTitleId.codeName}"/>
        </p:column>
        <p:column headerText="人员属性" style="width:260px; ">
            <p:outputLabel id="psnTypes" value="#{itm.fkByEmpId.psnTypes}" styleClass="zwx-tooltip"/>
            <p:tooltip for="psnTypes" style="max-width:260px;">
                <p:outputLabel value="#{itm.fkByEmpId.psnTypes}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="#{'1' eq orgApplyBean.orgType?'证书（发文）编号':'证书编号'}" style="width:180px;">
            <p:outputLabel value="#{itm.fkByEmpId.certNoStr}"
                           rendered="#{itm.fkByEmpId.getCertNoStr().indexOf(',')==-1}"/>
            <p:outputLabel value="#{itm.fkByEmpId.certNoStr.substring(0,itm.fkByEmpId.getCertNoStr().indexOf(','))}"
                           rendered="#{itm.fkByEmpId.getCertNoStr().indexOf(',')>0}"/>
            <div style="background-color: #F35E37;padding: 3px;border-radius: 3px;display: inline;display:#{itm.fkByEmpId.getCertNoStr().indexOf(',')>0?'':'none'};">
                <p:outputLabel value="其他" style="color:white;"
                               rendered="#{itm.fkByEmpId.getCertNoStr().indexOf(',')>0}"
                               onmouseover="getLabelPanel(this,'#{itm.fkByEmpId.certNoStr.substring(itm.fkByEmpId.getCertNoStr().indexOf(',')+1,itm.fkByEmpId.getCertNoStr().length())}')"/>
            </div>
            <p:outputLabel value="#{itm.fkByEmpId.certNoStr}"
                           rendered="#{itm.fkByEmpId.getCertNoStr().indexOf(',')==0}"/>
        </p:column>
        <p:column headerText="从事项目" style="padding-left:5px;width:260px;" rendered="#{'1' eq orgApplyBean.orgType}">
            <!-- 只显示跟检查资质相关的资质类别的资质范围 -->
            <p:outputLabel id="zzlbRange" value="#{itm.fkByEmpId.zzlbRange}" styleClass="zwx-tooltip"/>
            <p:tooltip for="zzlbRange" style="max-width:260px;">
                <p:outputLabel value="#{itm.fkByEmpId.zzlbRange}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="从事科室" style="padding-left:5px;width:260px;" rendered="#{'1' eq orgApplyBean.orgType}">
            <p:outputLabel id="wordOfficeName" value="#{itm.wordOfficeNames}" styleClass="zwx-tooltip"/>
            <p:tooltip for="wordOfficeName" style="max-width:260px;">
                <p:outputLabel value="#{itm.wordOfficeNames}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="操作" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
            <p:commandLink value="修改" process="@this" update=":tabView:editForm:orgTjApplyTabView:psnEditDialog"
                           oncomplete="PF('PsnEditDialog').show();" action="#{orgApplyBean.editPsnInfoAction}">
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.rid}"
                                             value="#{itm.rid}"/>
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.fkByEmpId.rid}"
                                             value="#{itm.fkByEmpId.rid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{orgApplyBean.orgType=='1'}"/>
            <p:commandLink value="科室选择"
                           process="@this,:tabView:editForm:orgTjApplyTabView:psnDatatable"
                           update=":tabView:editForm:orgTjApplyTabView:workOfficeDialog"
                           oncomplete="PF('WorkOfficeDialog').show();"
                           action="#{orgApplyBean.selPsnWordOfficeAction}"
                           rendered="#{orgApplyBean.orgType=='1'}">
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.rid}"
                                             value="#{itm.rid}"/>
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.fkByEmpId.rid}"
                                             value="#{itm.fkByEmpId.rid}"/>
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.wordOfficeRids}"
                                             value="#{itm.wordOfficeRids}"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" process="@this" update=":tabView:editForm:orgTjApplyTabView:psnDatatable"
                           action="#{orgApplyBean.deletePsnAction}">
                <p:confirm header="消息确认框" message="确定要删除吗？"
                           icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.rid}"
                                             value="#{itm.rid}"/>
                <f:setPropertyActionListener target="#{orgApplyBean.psnInfoVO.selectOrgPsnApply.fkByEmpId.rid}"
                                             value="#{itm.fkByEmpId.rid}"/>
            </p:commandLink>
        </p:column>
    </p:dataTable>
    <!--人员信息维护-->
    <p:dialog id="psnEditDialog" header="人员信息" widgetVar="PsnEditDialog" resizable="false" width="950" height="600"
              modal="true" styleClass="psnDiag">
        <p:outputPanel id="buttonsPanel"
                       style="width:909px; height: 40px; margin-top: 0; position: fixed; top: auto; z-index: 9999;margin-left: -2px;"
                       styleClass="zwx_toobar_42">
            <h:panelGrid columns="5" style="border-color:transparent;padding: 1px 0 0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="保存" icon="ui-icon-check" id="codeSaveBtn"
                                 action="#{orgApplyBean.savePsnAction}"
                                 process="@this,psnEditDialog"
                                 update=":tabView:editForm:orgTjApplyTabView:codeEditGrid,:tabView:editForm:orgTjApplyTabView:psnDatatable"
                                 rendered="#{orgApplyBean.psnInfoVO.psnDialogType != 2}">
                </p:commandButton>
                <p:spacer width="5" rendered="#{orgApplyBean.psnInfoVO.psnDialogType != 2}"/>
                <p:commandButton value="取消" icon="ui-icon-close" id="codeBackBtn"
                                 onclick="PF('PsnEditDialog').hide();"
                                 immediate="true" rendered="#{orgApplyBean.psnInfoVO.psnDialogType != 2}"/>
                <p:commandButton value="关闭" icon="ui-icon-close" id="codeCloseBtn"
                                 onclick="PF('PsnEditDialog').hide();" immediate="true"
                                 rendered="#{orgApplyBean.psnInfoVO.psnDialogType == 2}"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:outputPanel style="width: 100%;height: 40px;"/>
        <p:outputPanel id="codeEditGrid" style="width:100%;">
            <ui:param name="psnInfoBase" value="#{orgApplyBean.psnInfoBase}"/>
            <ui:param name="psnInfoForm" value="tabView:editForm:orgTjApplyTabView"/>
            <ui:param name="ifEdit" value="#{orgApplyBean.psnInfoVO.psnDialogType != 2}"/>
            <ui:include src="psnInfoValueNew.xhtml">
                <ui:param name="psnTypeNotNull" value="#{true}"/>
            </ui:include>
        </p:outputPanel>
    </p:dialog>
    <!--专业资质-->
    <ui:include src="psnInfoDiagNew.xhtml"/>
    <!--从事科室选择-->
    <p:dialog id="workOfficeDialog" header="从事科室选择" widgetVar="WorkOfficeDialog"
              resizable="false" width="350" height="400" modal="true">
        <p:outputPanel style="width:100%;" id="workOfficeSelectGrid">
            <p:selectManyCheckbox id="workOffice" layout="pageDirection"
                                  value="#{orgApplyBean.psnInfoVO.selectWorkOfficeRidList}">
                <f:selectItems value="#{orgApplyBean.psnInfoVO.workOfficeList}" var="workOffice"
                               itemValue="#{workOffice.rid}" itemLabel="#{workOffice.codeName}"/>
            </p:selectManyCheckbox>
        </p:outputPanel>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="workSaveBtn"
                                     action="#{orgApplyBean.savePsnWorkOfficeAction}"
                                     process="@this,workOffice"
                                     oncomplete="PF('WorkOfficeDialog').hide();"
                                     update=":tabView:editForm:orgTjApplyTabView:psnDatatable"/>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" id="workBackBtn"
                                     onclick="PF('WorkOfficeDialog').hide();" immediate="true"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
</ui:composition>