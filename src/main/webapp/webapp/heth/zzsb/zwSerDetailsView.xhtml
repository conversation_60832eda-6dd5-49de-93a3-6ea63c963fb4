<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
<style>
    #mainForm\:tabView\:serDetailDatatable table.ui-selectmanycheckbox td label{
        white-space: pre-line;
        width: 250px;
    }


</style>

    <p:panelGrid style="width:100%;margin-top: 8px;" id="serDetailDatatable">
        <f:facet name="header">
            <p:row>
                <p:column style="text-align:center;height: 20px;width:21%;">
                    <p:outputLabel value="服务类别" />
                </p:column>
                <p:column style="text-align:center;height: 20px;width:200px;">
                    <p:outputLabel value="服务项目" />
                </p:column>
                <p:column style="text-align:center;height: 20px;width:200px;" rendered="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower == 1}">
                    <p:outputLabel value="支持外检" />
                </p:column>
            </p:row>
        </f:facet>
        <p:row rendered="#{zwOrgInfoSearchZzsbBean.jkItemRowList.size()==0}">
            <p:column style="text-align:left;height: 27px;" colspan="2" rendered="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity == null or zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower== null or zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower != 1}">
                <p:outputLabel value="没有您要找的记录！" />
            </p:column>
            <p:column style="text-align:left;height: 27px;" colspan="3" rendered="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower == 1}">
                <p:outputLabel value="没有您要找的记录！" />
            </p:column>
        </p:row>
        <c:forEach items="#{zwOrgInfoSearchZzsbBean.jkItemRowList}" var="serDetailItm" varStatus="status" >
            <p:row>
            <c:if test="#{status.index==serDetailItm.rowspanStart}">
                <p:column style="text-align:left;height: 27px;" rowspan="#{serDetailItm.rowspan}">
                    <p:outputLabel value="#{serDetailItm.jcItem.codeName}" />
                </p:column>
            </c:if>
            <p:column style="text-align:left;height:27px;width: 50px;" >
                <p:outputLabel value="#{serDetailItm.serItem.codeName}" />
            </p:column>
            <p:column style="text-align:left;height:27px;width: 50px;" rendered="#{zwOrgInfoSearchZzsbBean.zwOrginfoEntity.outWorkPower == 1}" >
                <p:outputLabel value="是" rendered="#{serDetailItm.ifExterInspect=='1'}" />
                <p:outputLabel value="否" rendered="#{serDetailItm.ifExterInspect=='0'}" />
            </p:column>
            </p:row>
        </c:forEach>
    </p:panelGrid>
</ui:composition>
