<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <p:dialog header="专业资质信息" widgetVar="ZzlbInfoDialog" id="zzlbInfoDialog" resizable="false" modal="true" width="650" height="300">
        <p:panelGrid id="zzlbGrid" style="width:100%;">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;height: 30px;">
                    <h:outputText value="资质类别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" >
                    <p:selectOneMenu value="#{psnInfoBase.psnZzlbNew.fkByQualId.rid}" >
                        <f:selectItem itemLabel=""  itemValue=""/>
                        <f:selectItems value="#{psnInfoBase.qualList}" var="itm"
                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;height: 30px;">
                    <h:outputText value="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2==8?'发文':'证书'}编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px" >
                    <p:inputText value="#{psnInfoBase.psnZzlbNew.centNo}" maxlength="25" style="width: 180px;" disabled="false"/>
                    <p:outputLabel value="（请完整维护#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2==8?'发文':'证书'}编号）" style="padding-left:5px;color:blue;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;height: 30px;">
                    <h:outputText value="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2==8?'发文':'发证'}日期：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px" >
                    <p:outputLabel value="#{psnInfoBase.psnZzlbNew.sendDate}" >
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;height: 30px;">
                    <h:outputText value="失效日期：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px" >
                    <p:outputLabel value="#{psnInfoBase.psnZzlbNew.validDate}" >
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
            </p:row>
            <p:row rendered="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2!=8}">
                <p:column style="text-align:right;padding-right:3px;width:120px;height: 30px;">
                    <h:outputText value="资质获取途径：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px" >
                    <p:selectOneMenu value="#{psnInfoBase.psnZzlbNew.zzFetchWay}" style="width: 95px;" disabled="false">
                        <f:selectItem itemLabel="" itemValue=""/>
                        <f:selectItem itemLabel="培训发证" itemValue="1"/>
                        <f:selectItem itemLabel="单位内训" itemValue="2"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row rendered="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS5!=null}">
                <p:column style="text-align:right;padding-right:3px;width:120px;height: 30px;">
                    <h:outputText value="资质范围：" />
                </p:column>
                <p:column style="text-align:left;padding-left:3px" >
                    <p:selectManyCheckbox value="#{psnInfoBase.psnZzlbNew.selectRanges}" disabled="false"
                                          columns="1" layout="grid">
                        <f:selectItems value="#{psnInfoBase.rangeList}" var="itm"
                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                    </p:selectManyCheckbox>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <h:outputText value="附件：" />
                </p:column>
                <p:column>
                    <h:outputText value="#{psnInfoBase.psnZzlbNew.annexName}"
                                  rendered="#{psnInfoBase.psnZzlbNew.annexPath !=null}"></h:outputText>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnZzlbNew.annexPath}')"
                                     rendered="#{psnInfoBase.psnZzlbNew.annexPath !=null}"></p:commandButton>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="关闭" icon="ui-icon-close" id="zzlbBackBtn" onclick="PF('ZzlbInfoDialog').hide();" immediate="true" />
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <p:dialog header="培训履历" widgetVar="TrainHisDialog" id="trainHisDialog" resizable="false" modal="true" width="750" height="300">
        <p:panelGrid style="width:100%;margin-top:5px;margin-bottom:30px;" >
            <p:row>
                <p:column style="width: 120px;text-align: center;" styleClass="ui-state-default">
                    <p:outputLabel value="继续教育证书编号"/>
                </p:column>
                <p:column style="width:200px;text-align: center;" styleClass="ui-state-default" rendered="#{psnInfoBase.psnZzlb.ifShowRange}">
                    <p:outputLabel value="合格类型"/>
                </p:column>
                <p:column style="width:120px;text-align:center;" styleClass="ui-state-default">
                    <p:outputLabel value="证书有效期"/>
                </p:column>
            </p:row>
            <c:forEach items="#{psnInfoBase.psnZzlb.trainHisList}" var="trainHis">
                <p:row>
                    <p:column style="text-align: left;height:25px;">
                        <p:outputLabel value="#{trainHis.certNo}"/>
                    </p:column>
                    <p:column style="text-align: left;height:25px;" rendered="#{psnInfoBase.psnZzlb.ifShowRange}">
                        <p:outputLabel value="#{trainHis.rangeTypes}"/>
                    </p:column>
                    <p:column style="text-align: center;height:25px;">
                        <p:outputLabel value="#{trainHis.certDate}"/>
                    </p:column>
                </p:row>
            </c:forEach>
        </p:panelGrid>
    </p:dialog>
</ui:composition>