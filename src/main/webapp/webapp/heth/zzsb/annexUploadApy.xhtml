<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
            <script>
                var removeExtraZonePael = function(){
                    var el = jQuery('#tabView\\:editForm\\:orgTjApplyTabView\\:registrationZone\\:zonePanel');
                    if(el.length>1){
                        el.each(function(index){
                            if(index>0){
                              $(this).remove();
                            }
                        });
                    }
                }
            </script>
    <table width="100%">
        <tr>
            <c:forEach items="#{orgApplyBean.annexsPOList}" var="item" varStatus="status">
                <c:if test="#{item.annexType==1 or item.annexType==2}">
                    <td width="50%" style="vertical-align: top">
                        <p:fieldset legend="#{item.annexName}" rendered="#{item.annexType==1 or item.annexType==2}" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                            <p:outputPanel styleClass="zwx_toobar_42" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
                                <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                                    <p:commandButton value="添加" icon="ui-icon-plus" action="#{orgApplyBean.annexOpenDiag}"
                                                     process="@this" oncomplete="removeExtraZonePael()" >
                                        <f:setPropertyActionListener value="#{item.annexType}" target="#{orgApplyBean.selectAnnexType}"/>
                                    </p:commandButton>
                                </h:panelGrid>
                            </p:outputPanel>

                                <p:dataTable id="annexList#{item.annexType}" value="#{item.values}" var="itm1" rowIndexVar="var1"
                                             emptyMessage="暂无附件信息" style="margin-top: 5px;">
                                    <c:if test="#{item.annexType==2}">
                                        <p:column headerText="序号" style="width:40px;text-align: center">
                                            <h:outputLabel value="#{var1+1}"/>
                                        </p:column>
                                        <p:column headerText="附件名称" style="width:180px;">
                                            <h:outputLabel value="#{itm1[1]}"/>
                                        </p:column>
                                        <p:column headerText="注册地点" style="width:100px;">
                                            <h:outputLabel value="#{itm1[4]}"/>
                                        </p:column>
                                        <p:column headerText="发证单位" style="width:150px;">
                                            <h:outputLabel value="#{itm1[6]}"/>
                                        </p:column>
                                        <p:column headerText="发证单位级别" style="width:80px;text-align: center;">
                                            <h:outputLabel value="#{itm1[5]}"/>

                                        </p:column>
                                    </c:if>
                                    <c:if test="#{item.annexType!=2}">
                                            <p:column headerText="序号" style="width:10%;text-align: center">
                                                <h:outputLabel value="#{var1+1}"/>
                                            </p:column>
                                            <p:column headerText="附件名称" style="width:50%">
                                                <h:outputLabel value="#{itm1[1]}"/>
                                            </p:column>
                                    </c:if>
                                    <p:column headerText="操作" >
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm1[3]}')" />
                                        <p:spacer width="5"/>
                                        <p:commandLink value="删除" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                                                       process="@this"  action="#{orgApplyBean.deleteAnnex}" >
                                            <f:setPropertyActionListener value="#{itm1}" target="#{orgApplyBean.selectZwAnnex}"/>
                                            <f:setPropertyActionListener value="#{item.annexType}" target="#{orgApplyBean.selectAnnexType}"/>
                                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                                        </p:commandLink>
                                    </p:column>
                                </p:dataTable>
                        </p:fieldset>
                    </td>
                </c:if>
            </c:forEach>
        </tr>
        <tr>
            <c:forEach items="#{orgApplyBean.annexsPOList}" var="item" varStatus="status">
                <c:if test="#{item.annexType==3 or item.annexType==20}">
                    <td width="50%" style="vertical-align: top">
                         <p:fieldset legend="#{item.annexName}"  toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus" action="#{orgApplyBean.annexOpenDiag}"
                                             process="@this" oncomplete="removeExtraZonePael()" >
                                <f:setPropertyActionListener value="#{item.annexType}" target="#{orgApplyBean.selectAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>

                    <p:dataTable id="annexList#{item.annexType}" value="#{item.values}" var="itm1" rowIndexVar="var1"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <c:if test="#{item.annexType==2}">
                            <p:column headerText="序号" style="width:40px;text-align: center">
                                <h:outputLabel value="#{var1+1}"/>
                            </p:column>
                            <p:column headerText="附件名称" style="width:180px;">
                                <h:outputLabel value="#{itm1[1]}"/>
                            </p:column>
                            <p:column headerText="注册地点" style="width:100px;">
                                <h:outputLabel value="#{itm1[4]}"/>
                            </p:column>
                            <p:column headerText="发证单位" style="width:150px;">
                                <h:outputLabel value="#{itm1[6]}"/>
                            </p:column>
                            <p:column headerText="发证单位级别" style="width:80px;text-align: center;">
                                <h:outputLabel value="#{itm1[5]}"/>

                            </p:column>
                        </c:if>
                        <c:if test="#{item.annexType!=2}">
                            <p:column headerText="序号" style="width:10%;text-align: center">
                                <h:outputLabel value="#{var1+1}"/>
                            </p:column>
                            <p:column headerText="附件名称" style="width:50%">
                                <h:outputLabel value="#{itm1[1]}"/>
                            </p:column>
                        </c:if>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm1[3]}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                                           process="@this"  action="#{orgApplyBean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm1}" target="#{orgApplyBean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="#{item.annexType}" target="#{orgApplyBean.selectAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
                    </td>
                </c:if>
            </c:forEach>
        </tr>
    </table>
    <p:dialog header="文件上传" widgetVar="FileUIdVarUpload" id="fileUIdVarUpload" resizable="false" modal="true">
        <table>
            <tr>
                <td style="text-align: right;"><p:outputLabel
                        value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                        style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                </td>
            </tr>
            <tr>
                <td style="position: relative;top: -23px;">
                    <p:fileUpload requiredMessage="请选择上传文件！" style="width:700px;" previewWidth="120"
                                  fileUploadListener="#{orgApplyBean.handleFileZZUpload}" multiple="true"
                                  label="选择文件" uploadLabel="上传" cancelLabel="取消"
                                  fileLimit="5" fileLimitMessage="最多只能上传5个文件！"
                                  sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                                  validatorMessage="上传出错啦，重新上传！"
                                  invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                  process="@this" update="@this, #{updateId}:orgTjApplyTabView:annexList#{orgApplyBean.selectAnnexType}"
                                  mode="advanced" dragDropSupport="true"
                                  allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
                </td>
            </tr>
        </table>
    </p:dialog>

    <p:dialog id="photoDialog" header="许可证信息" widgetVar="PhotoDialog"
              resizable="false" modal="true" width="500" style="height: 670px;">
        <p:commandButton value="上传" icon="ui-icon-plus" update=":tabView:editForm:orgTjApplyTabView:ylFileUId"
                         process="@this"  oncomplete="PF('YlFileUId').show();"
                         style="margin-bottom:5px;">
            <f:setPropertyActionListener value="2" target="#{orgApplyBean.annexTypeBase}"/>
        </p:commandButton>
        <p:panelGrid id="photoDesc" style="width:100%;">
            <p:row>
                <p:column style="text-align:right;padding-right:8px;width:100px;">
                    <h:outputText value="*" style="color: red" />注册地点：
                </p:column>
                <p:column style="text-align:left;padding-left:0px;">
                    <zwx:ZoneSingleNewComp zoneList="#{orgApplyBean.zoneList}" id="registrationZone"
                                           zoneCodeNew="#{orgApplyBean.searchZoneCode}"
                                           zoneName="#{orgApplyBean.searchZoneName}"
                                           zoneId="#{orgApplyBean.searchZoneId}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:8px;width:100px;">
                    <h:outputText value="*" style="color: red" />发证单位：
                </p:column>
                <p:column style="text-align:left;padding-left:9px;">
                    <p:inputText value="#{orgApplyBean.unitName}" maxlength="100"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:8px;width:100px;">
                    <h:outputText value="*" style="color: red" />发证单位级别：
                </p:column>
                <p:column style="text-align:left;padding-left:9px;">
                    <p:selectOneMenu value="#{orgApplyBean.unitLevel}" >
                        <f:selectItem itemLabel="&#45;&#45;请选择&#45;&#45;" itemValue=""/>
                        <f:selectItem itemLabel="省级" itemValue="2"/>
                        <f:selectItem itemLabel="市级" itemValue="3"/>
                        <f:selectItem itemLabel="区县级" itemValue="4"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:8px;width:100px;">
                    <h:outputText value="附件：" />
                </p:column>
                <p:column id="photo" style="text-align:left;padding-left:8px;height:28px;"  >
                    <p:commandLink value="查看" rendered="#{orgApplyBean.ylAnnex.filePath!=null}"
                                   process="@this"  onclick="window.open('/webFile/#{orgApplyBean.ylAnnex.filePath}')" />
                    <p:spacer width="5"/>
                    <p:commandLink value="删除" rendered="#{orgApplyBean.ylAnnex.filePath!=null}"
                                   process="@this,photoDesc" update="photoDesc" action="#{orgApplyBean.deleteYlAnnex}" >
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                    </p:commandLink>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="确认" icon="ui-icon-check" id="photoSaveBtn"
                                     process="@this,photoDesc"
                                     action="#{orgApplyBean.saveYlFile}" >
                    </p:commandButton>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="photoBackBtn"  process="@this"
                                     oncomplete="PF('PhotoDialog').hide();"   >
                    </p:commandButton>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <p:dialog header="文件上传" widgetVar="YlFileUId" id="ylFileUId" resizable="false" modal="true">
        <table>
            <tr>
                <td style="text-align: right;"><p:outputLabel
                        value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                        style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                </td>
            </tr>
            <tr>
                <td style="position: relative;top: -23px;">
                    <p:fileUpload requiredMessage="请选择上传文件！" style="width:700px;" previewWidth="120"
                                  fileUploadListener="#{mgrbean.applyInfoBaseBean.handleFileUpload}"
                                  label="选择文件" uploadLabel="上传" cancelLabel="取消"
                                  fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                                  sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                                  validatorMessage="上传出错啦，重新上传！"
                                  invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                  process="@this,photoDesc" update="@this, photoDesc"
                                  mode="advanced" dragDropSupport="true"
                                  allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
                </td>
            </tr>
        </table>
    </p:dialog>
</ui:composition>