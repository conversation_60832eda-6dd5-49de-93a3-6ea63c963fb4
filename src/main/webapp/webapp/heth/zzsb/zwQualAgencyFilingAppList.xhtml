<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{zwQualAgencyFilingAppBean}"/>
    <!-- 编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/zzsb/zwQualAgencyFilingAppEdit.xhtml"/>

    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml" />
        <h:outputScript library="js" name="namespace.js" />
        <h:outputScript name="js/validate/system/validate.js" />
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构备案申请"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}"
                                 update=":tabView:mainForm:dataTable"
                                 process="@this,:tabView:mainForm:mainGrid"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" update="dataTable,:tabView:mainForm:addDialog" process="@this"
                                 action="#{mgrbean.addInitAction}" >
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 150px;">
                <p:outputLabel value="机构资质："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 10px;width: 269px;padding-top: 10px;">
                <p:selectOneMenu value="#{mgrbean.selectQualType}" style="width: 200px;">
                    <f:selectItem itemLabel="--请选择--" itemValue="" />
                    <f:selectItems value="#{mgrbean.qualTypeList}" var="item" itemLabel="#{item.codeName}" itemValue="#{item.rid}"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-left:5px;width: 148px;">
                <p:outputLabel value="申请类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;padding-top: 8px;width: 200px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.applyTypeName}" panelWidth="210"
                                        selectedIds="#{mgrbean.selectApplyType}"
                                        simpleCodeList="#{mgrbean.applyTypeList}"
                                        height="200" inputWidth="200"></zwx:SimpleCodeManyComp>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 150px;height: 43px;">
                <p:outputLabel value="审核状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="3">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>

    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:columnGroup type="header">
            <p:row>
                <p:column headerText="机构资质" style="width:150px;"/>
                <p:column headerText="申请类型" style="width:100px;"/>
                <p:column headerText="行政区划所属地区" style="width:250px;"/>
                <p:column headerText="申请日期" style="width:100px;"/>
                <p:column headerText="审核状态" style="width:120px;"/>
                <p:column headerText="审核日期" style="width:100px;"/>
                <p:column headerText="审核机构" style="width:200px;"/>
                <p:column headerText="驳回理由" style="width:250px;"/>
                <p:column headerText="操作" />
            </p:row>
        </p:columnGroup>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[1]}"/>
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[2]}"/>
        </p:column>
        <p:column style="text-align: left;">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{itm[4]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column style="text-align: center;#{itm[5]==2 or itm[5]==4?'color: red;':''}">
            <h:outputLabel rendered="#{itm[5]==0}" value="待提交"/>
            <h:outputLabel rendered="#{itm[5]==1}" value="待初审"/>
            <h:outputLabel rendered="#{itm[5]==2}" value="初审驳回"/>
            <h:outputLabel rendered="#{itm[5]==3}" value="初审通过"/>
            <h:outputLabel rendered="#{itm[5]==4}" value="终审驳回"/>
            <h:outputLabel rendered="#{itm[5]==5}" value="终审通过"/>
        </p:column>
        <p:column style="text-align: center;">
            <h:outputLabel value="#{(itm[5]==3 or itm[5]==2)?itm[6]:(itm[5]==5 or itm[5]==4)?itm[8]:''}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column style="text-align: left;">
            <h:outputLabel value="#{(itm[5]==3 or itm[5]==2)?itm[7]:(itm[5]==5 or itm[5]==4)?itm[9]:''}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column style="text-align: left;">
            <h:outputLabel id="backRsn" value="#{itm[5]==2?itm[10]:itm[5]==4?itm[11]:''}" styleClass="zwx-tooltip">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
            <p:tooltip for="backRsn" style="max-width:260px;">
                <p:outputLabel value="#{itm[5]==2?itm[10]:itm[5]==4?itm[11]:''}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column style="text-align: left;" exportable="false">
            <p:commandLink value="修改" rendered="#{itm[5]==0}" process="@this" action="#{mgrbean.preModAction}" update=":tabView" >
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.ifView}" value="0"/>
            </p:commandLink>
            <p:commandLink value="详情" rendered="#{itm[5]!=0}" process="@this" action="#{mgrbean.preModAction}" update=":tabView" >
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.ifView}" value="1"/>
            </p:commandLink>
        </p:column>

    </ui:define>
    <ui:define name="insertOtherMainContents">
        <p:dialog header="资质备案" widgetVar="AddDialog" id="addDialog" resizable="false" modal="true"  width="500" >
                <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;" id="baseGrid">
                    <p:row >
                        <p:column style="text-align:right;padding-right:3px;width:120px;height: 35px;">
                            <p:outputLabel value="*" style="color: red"/>
                            <p:outputLabel value="机构资质：" />
                        </p:column>
                        <p:column style="text-align:left;padding-left:12px;" colspan="3">
                            <p:selectOneMenu value="#{mgrbean.dialogSelectQualType}" style="width: 200px;">
                                <f:selectItems value="#{mgrbean.qualTypeList}" var="item" itemLabel="#{item.codeName}" itemValue="#{item.rid}"/>
                            </p:selectOneMenu>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="text-align:right;padding-right:3px;height: 35px;">
                            <p:outputLabel value="*" style="color: red"/>
                            <h:outputText value="申请类型："/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:12px;">
                            <p:selectOneMenu value="#{mgrbean.dialogSelectApplyType}" style="width: 200px;">
                                <f:selectItem itemLabel="--请选择--" itemValue="" />
                                <f:selectItems value="#{mgrbean.applyTypeList}" var="item" itemLabel="#{item.codeName}" itemValue="#{item.rid}"/>
                            </p:selectOneMenu>
                        </p:column>
                    </p:row>
                </p:panelGrid>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: center;">
                    <h:panelGroup>
                        <p:commandButton value="确定" icon="ui-icon-check" id="submitBtn" action="#{mgrbean.QualOrgSaveAction}"
                                         process="@this,baseGrid" />
                        <p:spacer width="5"/>
                        <p:commandButton value="关闭" icon="ui-icon-close" id="backBtn" onclick="PF('AddDialog').hide();" immediate="true" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
    </ui:define>
</ui:composition>