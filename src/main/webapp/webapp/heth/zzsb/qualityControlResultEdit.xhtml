<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <script type="text/javascript">

        </script>
        <style type="text/css">

        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制结果录入" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="暂存" icon="ui-icon-disk" update=":tabView:editForm"
                                 action="#{mgrbean.saveAction}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="提交" icon="ui-icon-check" update=":tabView:editForm"
                                 action="#{mgrbean.submitAction}" process="@this,:tabView:editForm" >
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
	<ui:define name="insertOtherContents">
        <p:panelGrid id="baseInfo"  style="margin-bottom: 10px;width:100%" >
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                	<p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="考核类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:15px;width: 280px;">
                	<!-- 刷新反馈表名称 -->
                	<p:selectOneMenu  value="#{mgrbean.checkRst.fkCheckTypeId.rid}" id="checkType" style="width:205px;">
                        <c:forEach items="#{mgrbean.checkTypeList}" var="itm" varStatus="varStatus">
                            <f:selectItem itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" />
                        </c:forEach>
                		<p:ajax event="change" process="@this" listener="#{mgrbean.changeCheckType}" update="unitName"></p:ajax>
                	</p:selectOneMenu>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                	<p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="机构名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" >
                	<h:panelGrid columns="2" style="border-color: transparent;margin: 0px;padding: 0px;">
						<p:inputText id="unitName"  readonly="true" style="width:240px;"
							value="#{mgrbean.checkRst.fkUnitId.unitname}"
							onclick="$('#tabView\\:editForm\\:onOrgSelect').click()"/>
						<p:commandLink id="onOrgSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
									   action="#{mgrbean.selectOrgList}" process="@this,checkType" style="position: relative;left: -30px;"
									   >
							<p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onOrgSelect}" update="unitName"/>
						</p:commandLink>
					</h:panelGrid>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="质控机构："/>
                </p:column>
                <p:column style="text-align:left;padding-left:15px;width: 280px;">
                    <h:outputText value="#{mgrbean.checkRst.fkCheckUnitId.unitname}"/>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                	<p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="质控日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:9px;" >
                    <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                                showOtherMonths="true"  size="11" navigator="true"
                                yearRange="c-10:c" converterMessage="质控日期，格式输入不正确！"
                                showButtonPanel="true" maxdate="#{mgrbean.today}"
                                value="#{mgrbean.checkRst.checkDate}" />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="专家组组长："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;width: 280px;">
                    <h:panelGrid columns="3" style="border-color: transparent;margin: 0px;padding: 0px;">
                        <p:inputText id="expertLeaders"   style="width:196px;" maxlength="200"
                                     value="#{mgrbean.checkRst.expertLeaders}" />
                        <p:commandLink id="onLeadersSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectPsnList}" process="@this,expertLeaders" style="position: relative;left: -30px;"
                        >
                            <f:setPropertyActionListener value="1" target="#{mgrbean.psnSelectType}"/>
                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onPsnSelect}" update="expertLeaders">
                            </p:ajax>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -33px;"
                                       action="#{mgrbean.clearSelectLeaders}" process="@this"
                                       update="expertLeaders"
                                       rendered="#{mgrbean.checkRst.state==0}"/>
                    </h:panelGrid>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="专家组成员："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" >
                    <h:panelGrid columns="3" style="border-color: transparent;margin: 0px;padding: 0px;">
                        <p:inputText id="expertMembers"   style="width:240px;" maxlength="200"
                                     value="#{mgrbean.checkRst.expertMembers}"/>
                        <p:commandLink id="onMembersSelect" styleClass="mysearch-icon ui-icon ui-icon-search" partialSubmit="true"
                                       action="#{mgrbean.selectPsnList}" process="@this,expertMembers" style="position: relative;left: -30px;"
                        >
                            <f:setPropertyActionListener value="2" target="#{mgrbean.psnSelectType}"/>
                            <p:ajax event="dialogReturn" process="@this" resetValues="true" listener="#{mgrbean.onPsnSelect}" update="expertMembers">
                            </p:ajax>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                       style="position: relative;left: -33px;"
                                       action="#{mgrbean.clearSelectMembers}" process="@this"
                                       update="expertMembers"
                                       rendered="#{mgrbean.checkRst.state==0}"/>
                    </h:panelGrid>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="发现的问题："/>
                </p:column>
                <p:column style="text-align:left;padding-left:11px;" colspan="3">
                    <p:inputTextarea rows="5" autoResize="false"
                                     style="resize: none;width: 799px;height: 120px;margin-left: 5px"
                                     maxlength="1000" value="#{mgrbean.checkRst.problems}"
                    />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="质控结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left:15px;width: 280px;">
                    <p:selectOneMenu  value="#{mgrbean.checkRst.fkCheckRstId.rid}"  style="width:205px;">
                        <f:selectItem itemLabel="--请选择--" itemValue="" />
                        <c:forEach items="#{mgrbean.qcConclusionSimpleCodeList}" var="itm" varStatus="varStatus">
                            <f:selectItem itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" />
                        </c:forEach>
                    </p:selectOneMenu>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="*" style="color:red;" ></p:outputLabel>
                    <p:outputLabel value="质控结果告知书："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" >
                    <p:outputPanel id="noticePanel">
                    <p:spacer width="5" />
                    <p:commandButton value="上传" icon="ui-icon-plus"  process="@this,:tabView:editForm" oncomplete="PF('FileDialog').show();"
                                     update=":tabView:editForm:fileDialog"  rendered="#{mgrbean.checkRst.noticeFilePath==null}"
                                     >
                        <f:setPropertyActionListener value="1" target="#{mgrbean.uploadType}"/>
                    </p:commandButton>
                    <p:commandButton value="查看"  onclick="window.open('/webFile/#{mgrbean.checkRst.noticeFilePath}')" rendered="#{mgrbean.checkRst.noticeFilePath!=null}"
                                     process="@this"/>
                    <p:spacer width="5" />
                    <p:commandButton value="删除"  action="#{mgrbean.delNoticeFile}" rendered="#{mgrbean.checkRst.noticeFilePath!=null}"
                                     update="noticePanel" process="@this">
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="整改结果："/>
                </p:column>
                <p:column style="text-align:left;padding-left:15px;width: 280px;">
                    <p:selectOneMenu  value="#{mgrbean.checkRst.fkZgRstId.rid}"  style="width:205px;">
                        <f:selectItem itemLabel="--请选择--" itemValue="" />
                        <c:forEach items="#{mgrbean.rectificationResultSimpleCodeList}" var="itm" varStatus="varStatus">
                            <f:selectItem itemLabel="#{itm.codeName}" itemValue="#{itm.rid}" />
                        </c:forEach>
                        <p:ajax event="change" process="@this" listener="#{mgrbean.changeZgRst}" update="zgRequiredPanel,zgPanel"></p:ajax>
                    </p:selectOneMenu>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputPanel id="zgRequiredPanel">
                        <p:outputLabel value="*" style="color:red;" rendered="#{mgrbean.checkRst.fkZgRstId.rid != null}"/>
                        <p:outputLabel value="整改报告："/>
                    </p:outputPanel>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;" >
                    <p:outputPanel id="zgPanel">
                        <p:spacer width="5" />
                    <p:commandButton value="上传" icon="ui-icon-plus"  process="@this,:tabView:editForm" oncomplete="PF('FileDialog').show();"
                                     update=":tabView:editForm:fileDialog"  rendered="#{mgrbean.checkRst.zgFilePath==null}"
                                    disabled="#{mgrbean.checkRst.fkZgRstId.rid == null}"
                                     >
                        <f:setPropertyActionListener value="2" target="#{mgrbean.uploadType}"/>
                    </p:commandButton>
                    <p:commandButton value="查看"  onclick="window.open('/webFile/#{mgrbean.checkRst.zgFilePath}')" rendered="#{mgrbean.checkRst.zgFilePath!=null}"
                                     process="@this"/>
                    <p:spacer width="5" />
                    <p:commandButton value="删除"  action="#{mgrbean.delZgFile}" rendered="#{mgrbean.checkRst.zgFilePath!=null}"
                                     update="zgPanel" process="@this">
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true"  width="810">
            <table>
                <tr>
                    <td style="text-align: right;"><p:outputLabel
                            value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                            style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择上传文件！" style="width:770px;" previewWidth="120"
                                      fileUploadListener="#{mgrbean.fileUpload}"
                                      label="选择文件" uploadLabel="上传" cancelLabel="取消"
                                      fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                                      sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                                      validatorMessage="上传出错啦，重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                      process="@this" update="@this"
                                      mode="advanced" dragDropSupport="true"
                                      onstart="zwx_loading_start();" oncomplete="zwx_loading_stop()"
                                      allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>