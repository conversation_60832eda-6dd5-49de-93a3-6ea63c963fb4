<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwQualAgencyFilingAppBean"-->
    <!--@elvariable id="orgApplyBean" type="com.chis.modules.heth.zzsb.web.TdZwQualOrgApplyInfoBaseBean"-->
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <script type="text/javascript">
        //<![CDATA[
        /*计算预警时间*/
        function getWaringDate() {
            changeWaringDate();
        }
        //]]>
    </script>
    <p:dataTable var="instItm" value="#{orgApplyBean.instInfoVO.orgInstApplyListView}" id="instDataTable"
                 emptyMessage="没有您要找的记录！" rowIndexVar="R" lazy="true" paginator="true" paginatorPosition="bottom"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                 rows="#{mgrbean.pageSize}" rowsPerPageTemplate="#{mgrbean.perPageSize}"
                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                 style="margin-top:5px;width:100%">
        <p:column headerText="仪器名称" style="padding-left: 3px;width:15%">
            <p:outputLabel value="#{instItm.fkByInstId.instName}"/>
        </p:column>
        <p:column headerText="仪器型号" style="text-align:center;width:10%">
            <p:outputLabel value="#{instItm.fkByInstId.instModel}"/>
        </p:column>
        <p:column headerText="仪器编号" style="text-align:center;width:10%">
            <p:outputLabel value="#{instItm.fkByInstId.instCode}"/>
        </p:column>
        <p:column headerText="最近校验日期" style="text-align:center;width:10%">
            <p:outputLabel value="#{instItm.fkByInstId.lastAcptDate}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </p:outputLabel>
        </p:column>
        <p:column headerText="校验周期（年）" style="text-align:center;width:10%">
            <p:outputLabel value="#{instItm.fkByInstId.lastAcptCircleDot}"/>
        </p:column>
        <p:column headerText="是否外包" style="text-align:center;width:5%">
            <p:outputLabel value="是" rendered="#{instItm.fkByInstId.outter == 1}"/>
            <p:outputLabel value="否" rendered="#{instItm.fkByInstId.outter == 0}"/>
        </p:column>
        <p:column headerText="是否支持外检" style="text-align:center;width:6%">
            <p:outputLabel value="是" rendered="#{instItm.fkByInstId.ifSuptOutChk == 1}"/>
            <p:outputLabel value="否" rendered="#{instItm.fkByInstId.ifSuptOutChk == 0}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left:5px;width:9%" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
            <ui:insert name="insertInstActions"/>
            <p:commandLink value="删除" process="@this" update=":tabView:editForm:orgTjApplyTabView:instDataTable"
                           action="#{orgApplyBean.deleteOrgInstApplyAction}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener target="#{orgApplyBean.instInfoVO.selectOrgInstApply.rid}"
                                             value="#{instItm.rid}"/>
            </p:commandLink>
        </p:column>
    </p:dataTable>

    <p:dialog header="仪器选择" widgetVar="OrgInstInfoDialog" id="orgInstInfoDialog" resizable="false" modal="true"
              width="800" height="470">
        <p:outputPanel id="deviceSelectButton" styleClass="zwx_toobar_42">
            <h:panelGrid columns="10">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn" action="#{orgApplyBean.addInstInfoAction}"
                                 update=":tabView:editForm:orgTjApplyTabView:instEditGrid">
                    <p:resetInput target=":tabView:editForm:orgTjApplyTabView:instEditGrid"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-close" process="@this"
                                 oncomplete="PF('OrgInstInfoDialog').hide();"/>
            </h:panelGrid>
        </p:outputPanel>
        <table width="100%">
            <tr>
                <td style="text-align: left;padding-left: 3px">
                    <h:panelGrid columns="10" id="srchInstGrid">
                        <p:outputLabel value="仪器名称：" styleClass="zwx_dialog_font"/>
                        <p:inputText id="itmName" value="#{orgApplyBean.instInfoVO.searchInstInfo[0]}"
                                     style="width: 160px;"
                                     maxlength="25">
                            <p:ajax event="keyup" update="instinfoDataTable" process="@this,srchInstGrid"
                                    listener="#{orgApplyBean.searchInstAction()}"/>
                        </p:inputText>
                        <p:spacer width="5"/>
                        <p:outputLabel value="仪器型号：" styleClass="zwx_dialog_font"/>
                        <p:inputText id="itmmodle" value="#{orgApplyBean.instInfoVO.searchInstInfo[1]}"
                                     style="width: 160px;"
                                     maxlength="25">
                            <p:ajax event="keyup" update="instinfoDataTable" process="@this,srchInstGrid"
                                    listener="#{orgApplyBean.searchInstAction()}"/>
                        </p:inputText>
                    </h:panelGrid>
                </td>
            </tr>
        </table>
        <p:dataTable var="institm" value="#{orgApplyBean.instInfoVO.allInstListView}" id="instinfoDataTable"
                     paginator="true"
                     rows="10"
                     emptyMessage="没有您要找的记录！"
                     paginatorPosition="bottom"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                     pageLinks="5" rowsPerPageTemplate="#{'10,20,50'}" lazy="true"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页">
            <p:column headerText="操作" style="width:15%;padding-left: 10px;">
                <p:commandLink value="选择" action="#{orgApplyBean.addOrgInstApplyAction}" process="@this"
                               update="@this,instinfoDataTable,:tabView:editForm:orgTjApplyTabView:instDataTable"
                               rendered="#{!institm.selected}">
                    <f:setPropertyActionListener value="#{institm}"
                                                 target="#{orgApplyBean.instInfoVO.tempInstInfo}"/>
                </p:commandLink>
                <p:spacer width="5" rendered="#{!institm.selected}"/>
                <p:commandLink value="修改" process="@this" action="#{orgApplyBean.modInstInfoAction}"
                               update=":tabView:editForm:orgTjApplyTabView:instEditGrid">
                    <f:setPropertyActionListener value="#{institm.rid}"
                                                 target="#{orgApplyBean.instInfoVO.selectInstInfo.rid}"/>
                </p:commandLink>
                <p:spacer width="5"/>
                <p:commandLink value="删除" action="#{orgApplyBean.deleteInstInfoAction}" process="@this"
                               update="instinfoDataTable">
                    <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                    <f:setPropertyActionListener value="#{institm.rid}"
                                                 target="#{orgApplyBean.instInfoVO.selectInstInfo.rid}"/>
                </p:commandLink>
            </p:column>
            <p:column style="width: 20%;padding-left: 3px;word-wrap: break-word; word-break: break-all;"
                      headerText="仪器名称">
                <h:outputText value="#{institm.instName}"/>
            </p:column>
            <p:column style="width: 18%;text-align: center;word-wrap: break-word; word-break: break-all;"
                      headerText="仪器型号">
                <h:outputText value="#{institm.instModel}"/>
            </p:column>
            <p:column style="width: 15%;text-align: center;word-wrap: break-word; word-break: break-all;"
                      headerText="仪器编号">
                <h:outputText value="#{institm.instCode}"/>
            </p:column>
            <p:column style="width: 10%;text-align: center" headerText="是否外包">
                <p:outputLabel rendered="#{institm.outter == 0}" value="否"/>
                <p:outputLabel rendered="#{institm.outter == 1}" value="是"/>
            </p:column>
        </p:dataTable>
    </p:dialog>
    <p:dialog id="instEditDialog" header="仪器信息" widgetVar="InstEditDialog" resizable="false" width="500" height="400"
              modal="true">
        <p:panelGrid style="width:100%;" id="instEditGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:40%;height: 35px;">
                    <h:outputText value="*" style="color: red;" />
                    <h:outputText value="仪器类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:selectOneMenu panelStyle="overflow-y: auto;height: 200px;"
                                         value="#{orgApplyBean.instInfoVO.selectInstInfo.fkByInstKindDetalId.rid}">
                            <f:selectItem itemLabel="--请选择--"/>
                            <f:selectItems value="#{orgApplyBean.instInfoVO.instTypeList}"/>
                            <p:ajax event="change" process="@this" update="instName"
                                    listener="#{orgApplyBean.instTypeChange}"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="仪器名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText size="25"
                                 value="#{orgApplyBean.instInfoVO.selectInstInfo.instName}" maxlength="50"
                                 id="instName"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="仪器型号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px">
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText value="#{orgApplyBean.instInfoVO.selectInstInfo.instModel}" maxlength="25"
                                     size="25"/>
                    </p:column>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="仪器编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px">
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText value="#{orgApplyBean.instInfoVO.selectInstInfo.instCode}" maxlength="25"
                                     size="25"/>
                    </p:column>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="生产厂家："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:inputText value="#{orgApplyBean.instInfoVO.selectInstInfo.instFactory}" maxlength="100"
                                     size="25"/>
                    </p:column>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="生产日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:calendar value="#{orgApplyBean.instInfoVO.selectInstInfo.instProDate}" size="11"
                                    navigator="true" maxlength="10" readonlyInput="true"
                                    yearRange="c-50:c" converterMessage="生产日期格式输入不正确！"
                                    maxdate="new Date()" pattern="yyyy-MM-dd"
                                    showButtonPanel="true" showOtherMonths="true"/>
                    </p:column>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="购置日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:calendar value="#{orgApplyBean.instInfoVO.selectInstInfo.purchaseDate}" size="11"
                                    navigator="true" maxlength="10" readonlyInput="true"
                                    yearRange="c-50:c" converterMessage="购置日期格式输入不正确！"
                                    maxdate="new Date()" pattern="yyyy-MM-dd"
                                    showButtonPanel="true" showOtherMonths="true"/>
                    </p:column>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="是否外包："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneRadio value="#{orgApplyBean.instInfoVO.selectInstInfo.outter}">
                        <f:selectItem itemValue="1" itemLabel="是"/>
                        <f:selectItem itemValue="0" itemLabel="否"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText value="序号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:inputText value="#{orgApplyBean.instInfoVO.selectInstInfo.num}" size="10" maxlength="4"
                                 onkeyup="SYSTEM.clearNoNum(this)" onblur="SYSTEM.clearNoNum(this)"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="检定要求："/>
                    <br/>
                    <h:outputText value="（仪器定期校验和检查）"/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneMenu
                            value="#{orgApplyBean.instInfoVO.selectInstInfo.acptReqId.rid}">
                        <f:selectItem itemLabel="--请选择--"/>
                        <f:selectItems var="acpt" itemLabel="#{acpt.codeName}" itemValue="#{acpt.rid}"
                                       value="#{orgApplyBean.instInfoVO.instAcptReqList}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="是否支持外检："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneRadio value="#{orgApplyBean.instInfoVO.selectInstInfo.ifSuptOutChk}">
                        <f:selectItem itemValue="1" itemLabel="是"/>
                        <f:selectItem itemValue="0" itemLabel="否"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="是否需要校验："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneRadio value="#{orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt}">
                        <f:selectItem itemValue="1" itemLabel="是"/>
                        <f:selectItem itemValue="0" itemLabel="否"/>
                        <p:ajax event="change"
                                update="@this,recentCheckTip,recentCheckDate,checkCycleTip,checkCycle,waringDate"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText id="recentCheckTip"
                                  value="#{(orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == 1 or orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == '1') ? '*' : ''} "
                                  style="color: #ff0000;"/>
                    <h:outputText value="最近校验日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:column style="text-align:left;padding-left:3px;">
                        <p:calendar
                                disabled="#{orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == 0 or orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == '0'}"
                                id="recentCheckDate" value="#{orgApplyBean.instInfoVO.selectInstInfo.lastAcptDate}"
                                size="11" maxlength="10" navigator="true"
                                yearRange="c-50:c" converterMessage="最近校验日期格式输入不正确！" readonlyInput="true"
                                maxdate="new Date()" pattern="yyyy-MM-dd" showButtonPanel="true"
                                showOtherMonths="true">
                            <p:ajax event="dateSelect" listener="#{orgApplyBean.onDateSelect}"
                                    process="@this" update="waringDate"/>
                        </p:calendar>
                    </p:column>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <h:outputText id="checkCycleTip"
                                  value="#{(orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == 1 or orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == '1') ? '*' : ''} "
                                  style="color: #ff0000;"/>
                    <h:outputText value="校验周期（年）："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:remoteCommand name="changeWaringDate" action="#{orgApplyBean.changeWaringDate()}"
                                     process="@this,checkCycle,recentCheckDate"
                                     update="waringDate"/>
                    <p:inputText id="checkCycle"
                                 disabled="#{orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == 0 or orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == '0'}"
                                 value="#{orgApplyBean.instInfoVO.selectInstInfo.lastAcptCircleDot}" style="width: 90px;"
                                 maxlength="6" onkeyup="SYSTEM.verifyNum(this,4,1)"
                                 onblur="SYSTEM.verifyNum(this,4,1); getWaringDate();"/>
                    <p:spacer width="5px"/>
                    <h:outputText id="waringDate"
                                  value="#{(orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == 1 or orgApplyBean.instInfoVO.selectInstInfo.ifNeedAcpt == '1') ? orgApplyBean.instInfoVO.waringDate : ''} "
                                  style="color:#ff0000;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 35px;">
                    <font color="red">*</font>
                    <h:outputText value="仪器状态："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneRadio value="#{orgApplyBean.instInfoVO.selectInstInfo.instStateId.rid}"
                                      style="width: auto;">
                        <f:selectItems var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"
                                       value="#{orgApplyBean.instInfoVO.instStateList}"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" process="@this,instEditGrid"
                                     action="#{orgApplyBean.saveInstAction}"
                                     update=":tabView:editForm:orgTjApplyTabView:instinfoDataTable,:tabView:editForm:orgTjApplyTabView:instDataTable">
                    </p:commandButton>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('InstEditDialog').hide();"
                                     immediate="true"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>

</ui:composition>