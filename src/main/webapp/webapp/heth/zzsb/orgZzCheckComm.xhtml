<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!-- ui:decorate 可以将ui:define的内容加入到template对应的相同name的ui:insert -->
    <ui:decorate template="orgInfoView.xhtml">
        <ui:define name="orgInfoPsnInfoHead">
            <p:panelGrid style="width:100%;margin:10px 0px;">
                <f:facet name="header">
                    <p:row>
                        <p:column style="text-align:left;padding:6px;" styleClass="ui-state-default">
                            <h:outputText value="人员汇总" />
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column>
                        <p:outputPanel style="padding:12px 6px;" id="personSummaryList">
                            <table>
                                <c:forEach items="#{mgrbean.showPersonSummaryList}" var="itm" varStatus="itmIndex">
                                    <tr>
                                        <c:forEach items="#{itm}" var="val" varStatus="valIndex">
                                            <td style="border:none;">
                                                <h:outputText value="#{val[0]}" escape="false" style="padding:6px; color: red;" rendered="#{val[1] == 1}" />
                                                <h:outputText value="#{val[0]}" escape="false" style="padding:6px;" rendered="#{val[1] ne 1}" />
                                                <h:outputText value="|" style="float: right;" rendered="#{valIndex.index!=itm.size()-1}"></h:outputText>
                                            </td>
                                            <c:if test="#{valIndex.index==itm.size()-1}">
                                                <td style="border:none;">
                                                    <c:if test="#{itmIndex.index == mgrbean.showPersonSummaryList.size()-1 and mgrbean.psnSummaryState ne 0}">
                                                        <span style="font-size:12px;vertical-align: middle;float:left;">
                                                            <c:if test="#{mgrbean.psnSummaryState == 1}">
                                                                <p:commandLink value="更多>>" style="color:#61b3e3;text-decoration: blink;padding-right: 10px;"
                                                                               action="#{mgrbean.changeShowState(0)}" process="@this" update="personSummaryList">
                                                                </p:commandLink>
                                                            </c:if>
                                                            <c:if test="#{mgrbean.psnSummaryState == 2}">
                                                                <p:commandLink value="&lt;&lt;收起" style="color:#61b3e3;text-decoration: blink;padding-right: 10px;"
                                                                               action="#{mgrbean.changeShowState(0)}" process="@this" update="personSummaryList">
                                                                </p:commandLink>
                                                            </c:if>
                                                        </span>
                                                    </c:if>
                                                </td>
                                            </c:if>
                                        </c:forEach>
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </ui:define>
        <ui:define name="orgInfoInstInfoHead">
            <p:panelGrid style="width:100%;margin:10px 0px;">
                <f:facet name="header">
                    <p:row>
                        <p:column style="text-align:left;padding:6px;" styleClass="ui-state-default">
                            <h:outputText value="仪器汇总" />
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column>
                        <p:outputPanel style="padding:12px 6px;" id="instSummaryList">
                            <table>
                                <c:forEach items="#{mgrbean.showInstSummaryList}" var="itm" varStatus="itmIndex">
                                    <tr>
                                        <c:forEach items="#{itm}" var="val" varStatus="valIndex">
                                            <td style="border:none;">
                                                <h:outputText value="#{val[0]}" escape="false" style="padding:6px; color: red;" rendered="#{val[1] == 1}" />
                                                <h:outputText value="#{val[0]}" escape="false" style="padding:6px;" rendered="#{val[1] ne 1}" />
                                                <h:outputText value="|" style="float: right;" rendered="#{valIndex.index!=itm.size()-1}"></h:outputText>
                                            </td>
                                            <c:if test="#{valIndex.index==itm.size()-1}">
                                                <td style="border:none;">
                                                    <c:if test="#{itmIndex.index == mgrbean.showInstSummaryList.size()-1 and mgrbean.instSummaryState ne 0}">
                                                        <span style="font-size:12px;vertical-align: middle;float:left;">
                                                            <c:if test="#{mgrbean.instSummaryState == 1}">
                                                                <p:commandLink value="更多>>" style="color:#61b3e3;text-decoration: blink;padding-right: 10px;"
                                                                               action="#{mgrbean.changeShowState(1)}" process="@this" update="instSummaryList">
                                                                </p:commandLink>
                                                            </c:if>
                                                            <c:if test="#{mgrbean.instSummaryState == 2}">
                                                                <p:commandLink value="&lt;&lt;收起" style="color:#61b3e3;text-decoration: blink;padding-right: 10px;"
                                                                               action="#{mgrbean.changeShowState(1)}" process="@this" update="instSummaryList">
                                                                </p:commandLink>
                                                            </c:if>
                                                        </span>
                                                    </c:if>
                                                </td>
                                            </c:if>
                                        </c:forEach>
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </ui:define>
        <ui:define name="orgInfoJcxmInfoHead">
            <p:panelGrid style="width:100%;margin:10px 0px;" rendered="#{null ne mgrbean.unHaveItems}">
                <f:facet name="header">
                    <p:row>
                        <p:column style="text-align:left;padding:6px;" styleClass="ui-state-default">
                            <h:outputText value="未具备的必检项目" />
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column>
                        <p:outputPanel style="padding:12px 6px;">
                            <h:outputText id="unHaveItems" value="#{mgrbean.unHaveItems}" styleClass="zwx-tooltip" style="-webkit-line-clamp: 3;width:80%;"/>
                            <p:tooltip for="unHaveItems" style="max-width: 600px;">
                                <p:outputLabel value="#{mgrbean.unHaveItems}" escape="false"/>
                            </p:tooltip>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </ui:define>
        <ui:define name="orgInfoAnnexQualInfoHead">
            <p:panelGrid style="width:100%;margin:10px 0px;" rendered="#{null ne mgrbean.unHaveQualAnnex}">
                <f:facet name="header">
                    <p:row>
                        <p:column style="text-align:left;padding:6px;" styleClass="ui-state-default">
                            <h:outputText value="未具备的制度" />
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column>
                        <p:outputPanel style="padding:12px 6px;">
                            <h:outputText id="unHaveQualAnnex" value="#{mgrbean.unHaveQualAnnex}" styleClass="zwx-tooltip" style="-webkit-line-clamp: 3;width:80%;"/>
                            <p:tooltip for="unHaveQualAnnex" style="max-width: 600px;">
                                <p:outputLabel value="#{mgrbean.unHaveQualAnnex}" escape="false"/>
                            </p:tooltip>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </ui:define>
        <ui:param name="mgrViewbean" value="#{mgrbean.infoViewBean}" />
        <!-- 类似tabView:viewForm -->
        <ui:param name="baseComposition" value="#{baseComposition}" />
    </ui:decorate>
</ui:composition>