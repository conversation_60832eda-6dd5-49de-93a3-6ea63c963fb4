<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <!--@elvariable id="psnInfoForm" type="java.lang.String"-->
    <!--@elvariable id="psnInfoBase" type="com.chis.modules.heth.zzsb.web.TdzwPsnInfoZzsbBase"-->
    <!--@elvariable id="orgApplyBean" type="com.chis.modules.heth.zzsb.web.TdZwQualOrgApplyInfoBaseBean"-->
    <h:outputStylesheet name="css/ui-tabs.css"/>
    <p:dialog header="专业资质信息" widgetVar="ZzlbInfoDialog" id="zzlbInfoDialog" resizable="false" modal="true" width="650"
              height="300">
        <p:panelGrid id="zzlbGrid" style="width:100%;">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;">
                    <font color="red">*</font>
                    <h:outputText value="资质类别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:selectOneMenu value="#{psnInfoBase.psnZzlb.fkByQualId.rid}">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{psnInfoBase.qualList}" var="itm"
                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                        <p:ajax event="change" listener="#{psnInfoBase.onQualChange}"
                                process="@this,:#{psnInfoForm}:zzlbGrid"
                                update=":#{psnInfoForm}:zzlbGrid"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;">
                    <font color="red">*</font>
                    <h:outputText
                            value="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlb.getFkByQualId().getRid()).extendS2==8?'发文':'证书'}编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px">
                    <p:inputText value="#{psnInfoBase.psnZzlb.centNo}" maxlength="25" style="width: 180px;"/>
                    <p:outputLabel
                            value="（请完整维护#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlb.getFkByQualId().getRid()).extendS2==8?'发文':'证书'}编号）"
                            style="padding-left:5px;color:blue;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;">
                    <font color="red">*</font>
                    <h:outputText
                            value="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlb.getFkByQualId().getRid()).extendS2==8?'发文':'发证'}日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px">
                    <p:calendar value="#{psnInfoBase.psnZzlb.sendDate}" size="11" navigator="true"
                                yearRange="c-50:c"
                                converterMessage="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlb.getFkByQualId().getRid()).extendS2==8?'发文':'发证'}日期格式输入不正确！"
                                pattern="yyyy-MM-dd" showButtonPanel="true" showOtherMonths="true"
                                maxdate="new Date()" readonlyInput="true"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;">
                    <h:outputText value="失效日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px">
                    <p:calendar value="#{psnInfoBase.psnZzlb.validDate}" size="11" navigator="true"
                                yearRange="c-50:c+20" converterMessage="失效日期格式输入不正确！"
                                pattern="yyyy-MM-dd" showButtonPanel="true" showOtherMonths="true"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width:120px;">
                    <h:outputText value="下次培训日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px">
                    <p:calendar value="#{psnInfoBase.psnZzlb.ageinSendDate}" size="11" navigator="true"
                                yearRange="c-50:c+20" converterMessage="下次培训日期格式输入不正确！"
                                pattern="yyyy-MM-dd" showButtonPanel="true" showOtherMonths="true"/>
                </p:column>
            </p:row>
            <p:row rendered="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlb.getFkByQualId().getRid()).extendS2!=8}">
                <p:column style="text-align:right;padding-right:3px;width:120px;">
                    <h:outputText value="资质获取途径："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px">
                    <p:selectOneMenu value="#{psnInfoBase.psnZzlb.zzFetchWay}" style="width: 95px;">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItem itemLabel="培训发证" itemValue="1"/>
                        <f:selectItem itemLabel="单位内训" itemValue="2"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row rendered="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlb.getFkByQualId().getRid()).extendS5!=null}">
                <p:column style="text-align:right;padding-right:3px;width:120px;">
                    <font color="red">*</font>
                    <h:outputText value="资质范围："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px">
                    <p:selectManyCheckbox value="#{psnInfoBase.psnZzlb.selectRanges}"
                                          columns="1" layout="grid">
                        <f:selectItems value="#{psnInfoBase.rangeList}" var="itm"
                                       itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
                    </p:selectManyCheckbox>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;">
                    <font color="red">*</font>
                    <h:outputText value="附件："/>
                </p:column>
                <p:column>
                    <p:commandButton value="上传" action="#{psnInfoBase.beforeFileUpload}"
                                     process="@this,:#{psnInfoForm}:zzlbGrid"
                                     rendered="#{psnInfoBase.psnZzlb.annexPath ==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="4"/>
                    </p:commandButton>
                    <h:outputText value="#{psnInfoBase.psnZzlb.annexName}"
                                  rendered="#{psnInfoBase.psnZzlb.annexPath !=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnZzlb.annexPath}')"
                                     rendered="#{psnInfoBase.psnZzlb.annexPath !=null}"/>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnZzlb.annexPath!=null}"/>
                    <p:commandButton value="删除" action="#{psnInfoBase.delAnnex}"
                                     process="@this,:#{psnInfoForm}:zzlbGrid"
                                     update=":#{psnInfoForm}:zzlbGrid,:#{psnInfoForm}:qualificationDataTable"
                                     rendered="#{psnInfoBase.psnZzlb.annexPath !=null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="4"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                    </p:commandButton>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check" id="zzlbSaveBtn"
                                     action="#{orgApplyBean.executeSaveZzlbAction}"
                                     process="@this,:#{psnInfoForm}:zzlbGrid"
                                     update=":#{psnInfoForm}:qualificationDataTable,:#{psnInfoForm}:psnDatatable"/>
                    <p:spacer width="5"/>
                    <p:commandButton value="取消" icon="ui-icon-close" id="zzlbBackBtn"
                                     onclick="PF('ZzlbInfoDialog').hide();" immediate="true"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <!-- 相关材料附件上传弹出框 -->
    <p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true">
        <table>
            <tr>
                <td style="text-align: right;">
                    <p:outputLabel value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                                   style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                </td>
            </tr>
            <tr>
                <td style="position: relative;top: -23px;">
                    <p:fileUpload requiredMessage="请选择要上传的文件！"
                                  label="文件选择" fileUploadListener="#{psnInfoBase.fileUpload}"
                                  invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！"
                                  style="width:600px;" previewWidth="120" cancelLabel="取消"
                                  fileLimit="1" fileLimitMessage="只能选择一个文件！"
                                  uploadLabel="上传" dragDropSupport="true" mode="advanced" sizeLimit="104857600"
                                  invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                  allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/" update="@this"
                    />
                </td>
            </tr>
        </table>
    </p:dialog>
</ui:composition>