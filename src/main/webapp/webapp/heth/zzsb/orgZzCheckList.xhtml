<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.OrgZzCheckListBean"-->
    <ui:param name="mgrbean" value="#{orgZzCheckListBean}"/>
    <!-- 审核页面 -->
    <ui:param name="editPage" value="/webapp/heth/zzsb/orgZzCheckEdit.xhtml"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/zzsb/orgZzCheckView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="4" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 process="@this,mainGrid" action="#{mgrbean.searchAction}"/>
                <p:commandButton value="全部审核" icon="ui-icon-check" id="allCheckBtn"
                                 process="@this,:tabView:mainForm:mainGrid"
                                 action="#{mgrbean.openCheckConfirmDialog()}"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:153px;height:38px;">
                <h:outputText value="机构地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 0;width:284px;">
                <zwx:ZoneSingleNewComp id="searchZone" zoneList="#{mgrbean.orgZoneList}"
                                       zoneCode="#{mgrbean.searchOrgZoneCode}"
                                       zoneName="#{mgrbean.searchOrgZoneName}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:153px;">
                <h:outputText value="机构名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 275px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchOrgName}" style="width: 180px;"
                             maxlength="50"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:153px;">
                <h:outputText value="资质类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:selectOneMenu value="#{mgrbean.searchZzType}" id="searchZzType"
                                 style="width: 198px;vertical-align: middle;">
                    <f:selectItem itemLabel="--全部--" itemValue=""/>
                    <f:selectItems value="#{mgrbean.zzTypeList}"/>
                </p:selectOneMenu>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;" colspan="5">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="机构地区" style="width: 250px;height:22px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="机构名称" style="width: 380px;height:22px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="资质类型" style="width: 160px;height:22px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="状态" style="width: 100px;height:22px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="退回原因" style="width: 450px;height:22px;padding-left: 8px;">
            <h:outputText id="returnReason" value="#{itm[5]}" styleClass="zwx-tooltip" style="-webkit-line-clamp: 1;"/>
            <p:tooltip for="returnReason" style="max-width:450px;">
                <p:outputLabel value="#{itm[5]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="操作" style="height:22px;padding-left: 8px;">
            <p:commandLink value="审核" rendered="#{itm[8] eq '2'}" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           action="#{mgrbean.modAndViewComm(0)}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.zzType}" value="#{itm[9]}"/>
            </p:commandLink>
            <p:commandLink value="详情" rendered="#{itm[8] ne '2'}" onclick="hideTooltips();"
                           process="@this,:tabView:mainForm:mainGrid" update=":tabView"
                           action="#{mgrbean.modAndViewComm(1)}">
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.zzType}" value="#{itm[9]}"/>
            </p:commandLink>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <!-- 批量/全部审核弹出框 -->
        <ui:include src="/webapp/heth/zzsb/orgZzCheckConfirmDialog.xhtml"/>
    </ui:define>
</ui:composition>