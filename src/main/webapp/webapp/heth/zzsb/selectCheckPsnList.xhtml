<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core" 
      xmlns:h="http://java.sun.com/jsf/html" 
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:p="http://primefaces.org/ui">
<f:view contentType="text/html">
    <h:head>
        <title>选择人员</title>
        <h:outputStylesheet name="css/default.css"/>
        <h:outputScript name="js/datatable.js"/>
        <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml" />
        <script type="text/javascript">
            //<![CDATA[
            //]]>
        </script>
        <style type="text/css">
        </style>

    </h:head>

    <h:body>
        <h:form id="codeForm">
        	<h:outputStylesheet name="css/ui-tabs.css"/>
            <p:outputPanel styleClass="zwx_toobar_42">
                <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                    <p:commandButton value="查询" icon="ui-icon-search" update="dataTable"
                                     action="#{selectCheckPsnListBean.searchAction}" process="@this,searchPanel" >
                    </p:commandButton>
                    <p:commandButton value="确定" icon="ui-icon-check"
                                     action="#{selectCheckPsnListBean.submitAction}" process="@this" >
                    </p:commandButton>
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     action="#{selectCheckPsnListBean.dialogClose}" process="@this"/>
                    <p:inputText style="visibility: hidden;width: 0"/>
                </h:panelGrid>
            </p:outputPanel>
		    <h:panelGrid columns="10" id="searchPanel">
		    	<p:inputText style="visibility: hidden;width: 0"/>
                <p:outputLabel value="单位名称：" styleClass="zwx_dialog_font" />
                <p:inputText  value="#{selectCheckPsnListBean.searchUnitName}" style="width: 180px;" />
                <p:spacer width="5"/>
                <p:outputLabel value="姓名：" styleClass="zwx_dialog_font" />
                <p:inputText  value="#{selectCheckPsnListBean.searchPsnName}" style="width: 180px;" />
            </h:panelGrid>
            <p:dataTable var="itm" value="#{selectCheckPsnListBean.psnDataModel}" id="dataTable"
                         paginator="true" rows="10" emptyMessage="没有数据！" rowKey="#{itm.rid}"
                         paginatorPosition="bottom" lazy="true" selection="#{selectCheckPsnListBean.selectedList}"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         rowSelectMode="add">
                <p:ajax event="page" process="@this,dataTable" listener="#{selectCheckPsnListBean.pageListener}"/>
                <p:ajax event="rowSelect" process="@this,dataTable" listener="#{selectCheckPsnListBean.rowSelectListener}"
                        immediate="true"/>
                <p:ajax event="rowUnselect" process="@this,dataTable" listener="#{selectCheckPsnListBean.rowUnselectListener}"
                        immediate="true"/>
                <p:ajax event="rowSelectCheckbox" process="@this,dataTable" listener="#{selectCheckPsnListBean.rowSelectListener}"
                        immediate="true"/>
                <p:ajax event="rowUnselectCheckbox" process="@this,dataTable" listener="#{selectCheckPsnListBean.rowUnselectListener}"
                        immediate="true"/>
                <p:ajax event="toggleSelect" process="@this,dataTable" listener="#{selectCheckPsnListBean.toggleSelectListener}"
                        immediate="true"/>
                <p:column style="width: 16px;text-align: center;" selectionMode="multiple"/>
                <p:column headerText="姓名" style="text-align: center;padding-left: 3px;width:100px;">
                    <h:outputText escape="false" value="#{itm.empName}" />
                </p:column>
                <p:column headerText="性别" style="text-align: center;padding-left: 3px;width:80px;">
                    <h:outputText escape="false" value="#{itm.sex}" />
                </p:column>
                <p:column headerText="职称" style="text-align: center;padding-left: 3px;width:150px;">
                    <h:outputText escape="false" value="#{itm.theTitle}" />
                </p:column>
                <p:column headerText="单位名称" style="padding-left: 3px;">
                    <h:outputText escape="false" value="#{itm.orgName}" />
                </p:column>
            </p:dataTable>
        </h:form>
		<ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
    	<ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>
</f:view>
</html>
