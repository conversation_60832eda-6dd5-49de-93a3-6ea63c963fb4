<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <!-- 资质机构备案 信息公共页 -->
    <p:tabView id="orgTjApplyTabView" widgetVar="OrgTjApplyTabView" style="margin-top: 5px;" cache="false" dynamic="false" rendered="#{orgApplyBean.orgType == 1}" >
        <p:ajax event="tabChange" process="@this" listener="#{orgApplyBean.tableChange}" update="#{updateId}"  />
        <p:tab title="基本信息">
            <ui:include src="/webapp/heth/zzsb/tdZwTjOrgApplyBaseInfo.xhtml" >
                <ui:param name="tabViewId" value="orgTjApplyTabView" />
            </ui:include>
        </p:tab>
        <p:tab title="人员信息" >
            <ui:include src="/webapp/heth/zzsb/tdZwOrgApplyPsnInfo.xhtml" >
                <ui:param name="tabViewId" value="orgTjApplyTabView" />
            </ui:include>
        </p:tab>
        <p:tab title="仪器信息">
            <ui:include src="/webapp/heth/zzsb/tdZwOrgApplyInstInfo.xhtml">
                <ui:param name="tabViewId" value="orgTjApplyTabView"/>
            </ui:include>
        </p:tab>
        <p:tab title="检查项目" >
            <ui:include src="/webapp/heth/zzsb/tdZwOrgApplyItemInfo.xhtml" >
                <ui:param name="tabViewId" value="orgTjApplyTabView" />
            </ui:include>
        </p:tab>
        <p:tab title="服务明细" >
            <ui:include src="/webapp/heth/zzsb/tdZwOrgApplySerDetails.xhtml" >
                <ui:param name="tabViewId" value="orgTjApplyTabView" />
            </ui:include>
        </p:tab>
        <p:tab title="外检车辆" rendered="#{orgApplyBean.ifHethFj and orgApplyBean.ifOutWork}" >
            <ui:include src="/webapp/heth/zzsb/tdZwTjorgOutCarApply.xhtml" >
                <ui:param name="tabViewId" value="orgTjApplyTabView" />
            </ui:include>
        </p:tab>
        <p:tab title="质量管理体系">
            <ui:include src="/webapp/heth/zzsb/qualAnnexApy.xhtml" >
                <ui:param name="tabViewId" value="orgTjApplyTabView" />
            </ui:include>
        </p:tab>
        <p:tab title="资质附件" >
            <ui:include src="/webapp/heth/zzsb/annexUploadApy.xhtml" >
                <ui:param name="tabViewId" value="orgTjApplyTabView" />
            </ui:include>
        </p:tab>
    </p:tabView>

    <div id="label" style="position:absolute;z-index:100;display:none;width:150px;">
        <div id="triangle-up"></div>
        <div id="descDiv"
             style="border-radius:5px;padding:10px;background:#4D4D4D;text-align:left;color:white;word-wrap: break-word;"></div>
    </div>
</ui:composition>