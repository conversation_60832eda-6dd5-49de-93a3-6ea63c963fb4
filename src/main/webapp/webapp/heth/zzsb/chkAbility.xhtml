<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <style type="text/css">
        .zwx-tooltip {
            -webkit-line-clamp: 2;
        }
    </style>
    <p:outputPanel id="chkAbilityGrid" style="display: flex;align-items: center;margin-bottom: 5px;margin-top: 5px;margin-left: 5px;"  >
        <h:outputText value="检测项目名称："/>
        <p:inputText value="#{mgrbean.searchProjectName}" style="width:220px;"
                     maxlength="50"/>
        <h:outputText value="检测方法：" style="padding-left: 20px;"/>
        <p:inputText value="#{mgrbean.searchJcWay}" style="width:220px;" maxlength="50"/>
        <h:outputText value="状态：" style="padding-left: 20px;"/>
        <p:selectManyCheckbox value="#{mgrbean.searchStates}">
            <f:selectItem itemValue="1" itemLabel="启用"></f:selectItem>
            <f:selectItem itemValue="0" itemLabel="停用"></f:selectItem>
        </p:selectManyCheckbox>
        <p:commandButton value="查询" process="@this,chkAbilityGrid"
                         action="#{mgrbean.initCheckCapability}" update="chkAbilityGrid,chkAbilityTable"
                         style="margin-left: 20px;"/>
    </p:outputPanel>
    <!--停用弹出框-->
    <p:confirmDialog widgetVar="StopDialog" id="stopDialog"
                     header="消息确认框" message="确定要停用吗？"
                     rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
        <div style="text-align: center !important;">
            <p:commandButton value="确定" action="#{mgrbean.stopAction}"
                             process="@this,:mainForm:tabView:chkAbilityGrid,:mainForm:tabView:chkAbilityTable"
                             icon="ui-icon-check" oncomplete="PF('StopDialog').hide();"
                             update="@this,:mainForm:tabView:chkAbilityGrid,:mainForm:tabView:chkAbilityTable"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('StopDialog').hide();" type="button"/>
        </div>
    </p:confirmDialog>
    <!--启用弹出框-->
    <p:confirmDialog widgetVar="StartDialog" id="startDialog"
                     header="消息确认框" message="确定要启用吗？"
                     rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
        <div style="text-align: center !important;">
            <p:commandButton value="确定" action="#{mgrbean.startAction}"
                             process="@this,:mainForm:tabView:chkAbilityGrid,:mainForm:tabView:chkAbilityTable"
                             icon="ui-icon-check" oncomplete="PF('StartDialog').hide();"
                             update="@this,:mainForm:tabView:chkAbilityGrid,:mainForm:tabView:chkAbilityTable"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('StartDialog').hide();" type="button"/>
        </div>
    </p:confirmDialog>
    <!--删除弹出框-->
    <p:confirmDialog widgetVar="DeleteDialog" id="deleteDialog"
                     header="消息确认框" message="确定要删除吗？"
                     rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
        <div style="text-align: center !important;">
            <p:commandButton value="确定" action="#{mgrbean.deleteAction}"
                             process="@this,:mainForm:tabView:chkAbilityGrid,:mainForm:tabView:chkAbilityTable"
                             icon="ui-icon-check" oncomplete="PF('DeleteDialog').hide();"
                             update="@this,:mainForm:tabView:chkAbilityGrid,:mainForm:tabView:chkAbilityTable"/>
            <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('DeleteDialog').hide();" type="button"/>
        </div>
    </p:confirmDialog>
    <p:dataTable var="itm" value="#{mgrbean.chkAbilityList}"
                 id="chkAbilityTable" emptyMessage="没有您要找的记录！" paginator="true" rows="20" paginatorPosition="bottom" rowIndexVar="R"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                 rowsPerPageTemplate="#{'20,50,100'}" lazy="true" pageLinks="5"
                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                 style="margin-top:5px;width:100%">
        <p:column headerText="检测项目名称" style="width:200px;text-align: center;">
            <h:outputText value="#{itm[1]}" escape="false"/>
        </p:column>
        <p:column headerText="检测依据" style="width:200px;text-align: center;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="检测方法" style="width:320px; ">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="限制说明" style="width:220px; ">
            <h:outputText id="limitDesc" value="#{itm[8]}" styleClass="zwx-tooltip"/>
            <p:tooltip for="limitDesc" style="max-width:400px;">
                <p:outputLabel styleClass="cs-break-word" value="#{itm[8]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="方法验证、确认或论证" style="width:140px; ">
            <h:outputText value="#{itm[9]}"/>
        </p:column>
        <p:column headerText="实验室认可标记（CNAS）" style="width:100px;text-align:center;">
            <h:outputLabel value="#{null ne itm[4] and 1 == itm[4] ?  '✔' : '×'}"/>
        </p:column>
        <p:column headerText="资质认定标记（CMA）" style="width:80px;text-align:center; ">
            <h:outputText value="#{null ne itm[5] and 1 == itm[5] ?  '✔' : '×'}"/>
        </p:column>
        <p:column headerText="外包标记" style="width:60px;text-align:center; ">
            <h:outputLabel value="#{null ne itm[6] and 1 == itm[6] ?  '✔' : '×'}">
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="width:60px;text-align:center; ">
            <h:outputLabel value="#{null ne itm[7] and 1 == itm[7] ? '启用' : '停用'}"/>
        </p:column>
        <p:column headerText="操作"  rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
            <p:commandLink value="修改" action="#{mgrbean.modChkAbility}"
                           process="@this,:mainForm:tabView:chkAbilityGrid"  update=":mainForm:abilityDetectionDialog">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.modAbilityRelRid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{null ne itm[7] and 1 == itm[7]}" />
            <p:commandLink value="停用" action="#{mgrbean.stopBeforeAction}"
                           process="@this" rendered="#{null ne itm[7] and 1 == itm[7]}" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.modAbilityRelRid}"/>
            </p:commandLink>
            <p:spacer width="5" rendered="#{null ne itm[7] and 0 == itm[7]}" />
            <p:commandLink value="启用" action="#{mgrbean.startBeforeAction}"
                           process="@this" rendered="#{null ne itm[7] and 0 == itm[7]}" >
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.modAbilityRelRid}"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" action="#{mgrbean.deleteBeforeAction}" process="@this">
                <f:setPropertyActionListener target="#{mgrbean.modAbilityRelRid}" value="#{itm[0]}" />
            </p:commandLink>
        </p:column>
    </p:dataTable>
</ui:composition>
