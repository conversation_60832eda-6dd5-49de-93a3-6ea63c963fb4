<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/webapp/heth/zzsb/zwOrgInfoEditBase.xhtml">
    <ui:param name="mgrbean" value="#{zwSrvOrgInfoEditZzsbBean}"/>
    <ui:define name="insertTabAction">
        <p:ajax event="tabChange" process="@this" listener="#{zwSrvOrgInfoEditZzsbBean.tableChange}" update=":mainForm:commondButtom" >
        </p:ajax>
    </ui:define>
    
    <ui:define name="insertPsnActions">
		<p:commandLink value="体检情况" process="@this,:mainForm:tabView:psnDatatable" update=":mainForm:psnTjDialog"
             action="#{zwSrvOrgInfoEditZzsbBean.addInitTjRst}" oncomplete="PF('PsnTjDialog').show();" >
			<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.selectZwOrgPsn}" value="#{itm}" />
		</p:commandLink>
		<p:spacer width="5"/>    
    </ui:define>
    
    <ui:define name="insertInstActions">
		<!--<p:commandLink value="场所监测2" process="@this" update=":mainForm:instJcDialog"
             action="#{zwSrvOrgInfoEditZzsbBean.addInitInstJc}" oncomplete="PF('InstJcDialog').show();" >
			<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.selectOrgInst}" value="#{instItm}" />
		</p:commandLink>
		<p:spacer width="5"/>      
		<p:commandLink value="设备检测" process="@this" update=":mainForm:instSbJcDialog"
             action="#{zwSrvOrgInfoEditZzsbBean.addInitInstSbJc}" oncomplete="PF('InstSbJcDialog').show();" >
			<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.selectOrgInst}" value="#{instItm}" />
		</p:commandLink>
		<p:spacer width="5"/>    -->
    </ui:define>
    
    
    <ui:define name="insertDialogs">
		<!-- 设备检测 -->
		<p:dialog id="instSbJcDialog" header="设备检测情况" widgetVar="InstSbJcDialog" resizable="false" width="700" height="400" modal="true">
			<p:commandButton value="添行" action="#{zwSrvOrgInfoEditZzsbBean.addLineInstSbJc}" update="instSbJcMainList" process="@this,instSbJcMainList"/>
			<input type="text" style="width: 1px;height: 1px;border: 0px;"/>
			<p:dataTable id="instSbJcMainList" value="#{zwSrvOrgInfoEditZzsbBean.sbJcMainList}" var="itm"  rowIndexVar="rowInx" emptyMessage="没有数据">
				<p:column headerText="操作" style="width:120px">
					<p:commandLink value="删除" process="@this" update=":mainForm:instSbJcMainList"
						action="#{zwSrvOrgInfoEditZzsbBean.delLineInstSbJc}" >
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.sbJcMain}" value="#{itm}" />
					</p:commandLink>
					<p:spacer width="5"/>								
					<p:commandLink value="不合格情况" process="@this" update=":mainForm:instSbJcSubList"
						action="#{zwSrvOrgInfoEditZzsbBean.addInitInstSbJcSub}" oncomplete="PF('InstSbJcSubDialog').show();">
						<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.sbJcMain}" value="#{itm}" />
					</p:commandLink>								
				</p:column>
				<p:column headerText="检测日期">
					<p:calendar value="#{itm.jcDate}" size="11" navigator="true" required="true" requiredMessage="检测日期不能为空！"
                                             yearRange="c-50:c" converterMessage="日期格式输入不正确！"
                                             pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
				</p:column>
				<p:column headerText="设备检测结果">
					<p:inputText value="#{itm.jcRst}" maxlength="100" size="40"/>
				</p:column>
			</p:dataTable>		

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" 
							action="#{zwSrvOrgInfoEditZzsbBean.saveInstSbJc}" process="@this,instSbJcDialog" update=":mainForm:tabView:psnDatatable"/>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" 
							onclick="PF('InstSbJcDialog').hide();" type="button"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>  
		   
		<!-- 设备检测子表 -->
		<p:dialog id="instSbJcSubDialog" header="设备检测不合格情况" widgetVar="InstSbJcSubDialog" resizable="false" width="700" height="300" modal="true">
			<p:commandButton value="添行" action="#{zwSrvOrgInfoEditZzsbBean.addLineInstSbJcSub}" update="instSbJcSubList" process="@this,instSbJcSubList"/>
			<input type="text" style="width: 1px;height: 1px;border: 0px;"/>
			<p:dataTable id="instSbJcSubList" value="#{zwSrvOrgInfoEditZzsbBean.sbJcSubList}" var="itm"  rowIndexVar="rowInx" emptyMessage="没有数据">
				<p:column headerText="操作" style="width:50px">
					<p:commandLink value="删除" process="@this" update=":mainForm:instSbJcSubList"
						action="#{zwSrvOrgInfoEditZzsbBean.delLineInstSbJcSub}" >
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.sbJcSub}" value="#{itm}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="不合格指标">
					<p:inputText value="#{itm.descr}" maxlength="50" size="30" required="true" requiredMessage="不合格指标不能为空！"/>
				</p:column>
				<p:column headerText="不合格指标结果">
					<p:inputText value="#{itm.rst}" maxlength="25" size="20"/>
				</p:column>
				<p:column headerText="不合格指标结果单位">
					<p:inputText value="#{itm.rstUnit}" maxlength="10" size="12"/>
				</p:column>
			</p:dataTable>		

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" 
							action="#{zwSrvOrgInfoEditZzsbBean.saveInstSbJcSub}" process="@this,instSbJcSubList,instSbJcMainList" update=":mainForm:instSbJcMainList"/>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" 
							onclick="PF('InstSbJcSubDialog').hide();" type="button"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>     
		
		<!-- 场所监测 -->
		<p:dialog id="instJcDialog" header="场所监测情况" widgetVar="InstJcDialog" resizable="false" width="700" height="400" modal="true">
			<p:commandButton value="添行" action="#{zwSrvOrgInfoEditZzsbBean.addLineInstJc}" update="instJcMainList" process="@this,instJcMainList"/>
			<input type="text" style="width: 1px;height: 1px;border: 0px;"/>
			<p:dataTable id="instJcMainList" value="#{zwSrvOrgInfoEditZzsbBean.jcMainList}" var="itm"  rowIndexVar="rowInx" emptyMessage="没有数据">
				<p:column headerText="操作" style="width:120px">
					<p:commandLink value="删除" process="@this, :mainForm:instJcMainList" update=":mainForm:instJcMainList"
						action="#{zwSrvOrgInfoEditZzsbBean.delLineInstJc}" >
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.jcMain}" value="#{itm}" />
					</p:commandLink>
					<p:spacer width="5"/>								
					<p:commandLink value="不合格情况" process="@this" update=":mainForm:instJcSubList"
						action="#{zwSrvOrgInfoEditZzsbBean.addInitInstJcSub}" oncomplete="PF('InstJcSubDialog').show();">
						<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.jcMain}" value="#{itm}" />
					</p:commandLink>								
				</p:column>
				<p:column headerText="年度监测日期">
					<p:calendar value="#{itm.jcDate}" size="11" navigator="true" required="true" requiredMessage="监测日期不能为空！"
                                             yearRange="c-50:c" converterMessage="日期格式输入不正确！"
                                             pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
				</p:column>
				<p:column headerText="场所监测结果">
					<p:inputText value="#{itm.jcRst}" maxlength="100" size="40"/>
				</p:column>
			</p:dataTable>		

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" 
							action="#{zwSrvOrgInfoEditZzsbBean.saveInstJc}" process="@this,instJcDialog" update=":mainForm:tabView:psnDatatable"/>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" 
							onclick="PF('InstJcDialog').hide();" type="button"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>  
		   
		<!-- 场所监测子表 -->
		<p:dialog id="instJcSubDialog" header="场所不合格情况" widgetVar="InstJcSubDialog" resizable="false" width="700" height="300" modal="true">
			<p:commandButton value="添行" action="#{zwSrvOrgInfoEditZzsbBean.addLineInstJcSub}" update="instJcSubList" process="@this,instJcSubList"/>
			<input type="text" style="width: 1px;height: 1px;border: 0px;"/>
			<p:dataTable id="instJcSubList" value="#{zwSrvOrgInfoEditZzsbBean.jcSubList}" var="itm"  rowIndexVar="rowInx" emptyMessage="没有数据">
				<p:column headerText="操作" style="width:50px">
					<p:commandLink value="删除" process="@this, :mainForm:instJcSubList" update=":mainForm:instJcSubList"
						action="#{zwSrvOrgInfoEditZzsbBean.delLineInstJcSub}" >
						<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.jcSub}" value="#{itm}" />
					</p:commandLink>
				</p:column>
				<p:column headerText="不合格位置说明">
					<p:inputText value="#{itm.descr}" maxlength="50" size="30" required="true" requiredMessage="不合格位置说明不能为空！"/>
				</p:column>
				<p:column headerText="不合格位置结果">
					<p:inputText value="#{itm.rst}" maxlength="25" size="20"/>
				</p:column>
				<p:column headerText="不合格位置结果单位">
					<p:inputText value="#{itm.rstUnit}" maxlength="10" size="12"/>
				</p:column>
			</p:dataTable>		

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" 
							action="#{zwSrvOrgInfoEditZzsbBean.saveInstJcSub}" process="@this,instJcSubList,instJcMainList" update=":mainForm:instJcMainList"/>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" 
							onclick="PF('InstJcSubDialog').hide();" type="button"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>     
    
		<!-- 体检情况 -->
		<p:dialog id="psnTjDialog" header="体检情况" widgetVar="PsnTjDialog" resizable="false" width="700" height="400" modal="true">
			<p:panelGrid id="psnTjGrid" style="width:100%;">
				<p:row>
					<p:column styleClass="ui-widget-header" colspan="2">入职前体检</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">
					<input type="text" style="width: 1px;height: 1px;border: 0px;"/>体检日期：</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:calendar value="#{zwSrvOrgInfoEditZzsbBean.firstTjMain.tjDate}" size="11" navigator="true"
                                               yearRange="c-50:c" converterMessage="日期格式输入不正确！"
                                               pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>						
					
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">体检结论：</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText value="#{zwSrvOrgInfoEditZzsbBean.firstTjMain.tjConcl}" maxlength="100" size="40"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column colspan="2">
						<p:commandButton value="添行" action="#{zwSrvOrgInfoEditZzsbBean.addLine(1)}" update="firstTjSubList" process="@this, firstTjSubList"/>
						<p:dataTable id="firstTjSubList" value="#{zwSrvOrgInfoEditZzsbBean.firstTjSubList}" var="itm" 
							rowIndexVar="rowInx" emptyMessage="没有数据">
							<p:column headerText="操作" style="width:50px">
								<p:commandLink value="删除" process="@this, :mainForm:firstTjSubList" update=":mainForm:firstTjSubList"
									action="#{zwSrvOrgInfoEditZzsbBean.delLine(1)}" >
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
									<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.tjSub}" value="#{itm}" />
								</p:commandLink>								
							</p:column>
							<p:column headerText="不合格指标">
								<p:inputText value="#{itm.descr}" maxlength="50" size="30" required="true" requiredMessage="不合格指标不能为空！"/>
							</p:column>
							<p:column headerText="不合格指标结果">
								<p:inputText value="#{itm.rst}" maxlength="25" size="20"/>
							</p:column>
							<p:column headerText="不合格指标结果单位">
								<p:inputText value="#{itm.rstUnit}" maxlength="10" size="12"/>
							</p:column>
						</p:dataTable>
					</p:column>
				</p:row>
				<p:row>
					<p:column styleClass="ui-widget-header" colspan="2">最近一次体检</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">体检日期：</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:calendar value="#{zwSrvOrgInfoEditZzsbBean.lastTjMain.tjDate}" size="11" navigator="true"
                                               yearRange="c-50:c" converterMessage="日期格式输入不正确！"
                                               pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>						
					
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">体检结论：</p:column>
					<p:column style="text-align:left;padding-left:3px;">
						<p:inputText value="#{zwSrvOrgInfoEditZzsbBean.lastTjMain.tjConcl}" maxlength="100" size="40"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column colspan="2">
						<p:commandButton value="添行" action="#{zwSrvOrgInfoEditZzsbBean.addLine(2)}" update="lastTjSubList" process="@this, lastTjSubList"/>
						<p:dataTable id="lastTjSubList" value="#{zwSrvOrgInfoEditZzsbBean.lastTjSubList}" var="itm" 
							rowIndexVar="rowInx" emptyMessage="没有数据">
							<p:column headerText="操作" style="width:50px">
								<p:commandLink value="删除" process="@this, :mainForm:lastTjSubList" update=":mainForm:lastTjSubList"
									action="#{zwSrvOrgInfoEditZzsbBean.delLine(2)}" >
									<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
									<f:setPropertyActionListener target="#{zwSrvOrgInfoEditZzsbBean.tjSub}" value="#{itm}" />
								</p:commandLink>								
							</p:column>
							<p:column headerText="不合格指标">
								<p:inputText value="#{itm.descr}" maxlength="50" size="30" required="true" requiredMessage="不合格指标不能为空！"/>
							</p:column>
							<p:column headerText="不合格指标结果">
								<p:inputText value="#{itm.rst}" maxlength="25" size="20"/>
							</p:column>
							<p:column headerText="不合格指标结果单位">
								<p:inputText value="#{itm.rstUnit}" maxlength="10" size="12"/>
							</p:column>
						</p:dataTable>
					</p:column>
				</p:row>
			</p:panelGrid>

			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" 
							action="#{zwSrvOrgInfoEditZzsbBean.saveTjRst}" process="@this,psnTjGrid" update=":mainForm:tabView:psnDatatable"/>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" 
							onclick="PF('PsnTjDialog').hide();" type="button"/>
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>    
    </ui:define>    
</ui:composition>