<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <style type="text/css">
        .calClass input{
            width:81px;
        }
    </style>
    <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;">
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="*" style="color: red"
                               rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="单位名称：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.orgName}" maxlength="50" style="width:604px;"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                             onkeyup="this.value=this.value.replace(/\s+/g,'')"/>
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.orgName}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="行政区划所属地区：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;border-right:0px;width: 260px;"
                      rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
                <zwx:ZoneSingleNewComp zoneList="#{orgApplyBean.orgZoneList}" zonePaddingLeft="0" zoneWidth="208"
                                       zoneId="#{orgApplyBean.orgZoneId}" zoneName="#{orgApplyBean.orgZoneName}" />
            </p:column>
            <p:column style="text-align:left;border-left:0px;" colspan="2"
                      rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
                <h:outputText  value="提示：" style="color: red;margin-left: -216px;"/>
                <h:outputText  value="必须选择到区县及以下" style="color: blue;"/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3"
                      rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}">
                <p:outputLabel value="#{orgApplyBean.orgZoneName}" />
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="注册地址：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.orgAddr}" maxlength="100" style="width:604px;"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                             onkeyup="this.value=this.value.replace(/\s+/g,'')" />
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.orgAddr}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="*" style="color: red"
                               rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="法定代表人：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 260px;">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.orgFz}" maxlength="25" size="25"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                             onkeyup="this.value=this.value.replace(/\s+/g,'')" />
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.orgFz}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="法定代表人职务：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;padding-top: 0px; padding-bottom: 0px;">
                <p:selectOneMenu value="#{orgApplyBean.tjorginfoApply.orgFzzw}" editable="true" maxlength="25"
                                 rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                                 style="width: 188px; padding-top: 0px; padding-bottom: 0px; margin-top: 0px; margin-bottom: 0px;">
                    <f:selectItems value="#{orgApplyBean.careerList}"/>
                </p:selectOneMenu>
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.orgFzzw}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="联系人：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 260px;">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.linkMan}" maxlength="25" size="25"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                             onkeyup="this.value=this.value.replace(/\s+/g,'')"/>
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.linkMan}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="联系人手机：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.linkMb}" maxlength="11" style="width:180px;"
                             onkeyup="this.value = this.value.replace(/[^\d]/g, '');"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value=" (格式：13616161616)" style="color: red"
                               rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"/>
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.linkMb}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="电话：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 450px;">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.linkTel}" maxlength="25" size="25"
                             onkeyup="this.value = this.value.replace(/[^\d|-]/g, '');"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"/>
                <p:outputLabel value=" (格式：0510-85373786)" style="color: red" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" />
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.linkTel}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <p:outputLabel value="传真：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.fax}" maxlength="25" style="width:180px;"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                             onkeyup="this.value = this.value.replace(/[^\d|-]/g, '');"/>
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.fax}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="邮编：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 260px;">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.zipcode}" maxlength="10" size="25"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                             onkeyup="this.value = this.value.replace(/[^\d]/g, '');"/>
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.zipcode}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <p:outputLabel value="电子邮箱：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.email}" maxlength="25" style="width:180px;"
                             rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                             onkeyup="this.value=this.value.replace(/\s+/g,'')"/>
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.email}" rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{orgApplyBean.ifBaseInfoApplyShow}" >
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoApplyInfoEdit}"/>
                <p:outputLabel value="备案编号：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 260px;">
                <p:inputText value="#{orgApplyBean.tjorginfoApply.certNo}" maxlength="25" size="25"
                             rendered="#{orgApplyBean.ifBaseInfoApplyInfoEdit}"
                             onkeyup="this.value=this.value.replace(/\s+/g,'')" style="width: 260px;"/>
                <p:outputLabel value="#{orgApplyBean.tjorginfoApply.certNo}" rendered="#{!orgApplyBean.ifBaseInfoApplyInfoEdit}"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoApplyInfoEdit and orgApplyBean.ifValidityPeriod}" />
                <p:outputLabel value="备案有效期限：" rendered="#{orgApplyBean.ifValidityPeriod}" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" rendered="#{orgApplyBean.ifBaseInfoApplyInfoEdit and orgApplyBean.ifValidityPeriod}">
               <!-- 备案有效期限组件 orgApplyBean.ifValidityPeriod -->
                <p:calendar value="#{orgApplyBean.tjorginfoApply.firstGetday}" styleClass="calClass" navigator="true"
                            yearRange="c-20:c" converterMessage="日期格式输入不正确！" id="bDate"
                            pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                            maxdate="#{orgApplyBean.firstGetdayMaxDate}" readonlyInput="true" >
                    <p:ajax event="dateSelect" listener="#{orgApplyBean.filingDateListener}"  process="@this,bDate,eDate"  update="bDate,eDate" />
                </p:calendar>
                ~
                <p:calendar value="#{orgApplyBean.tjorginfoApply.filingDate}" styleClass="calClass" navigator="true"
                            yearRange="c-20:c+20" converterMessage="日期格式输入不正确！" id="eDate"
                            pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                            mindate="#{orgApplyBean.tjorginfoApply.firstGetday}" readonlyInput="true">
                    <p:ajax event="dateSelect" listener="#{orgApplyBean.filingDateListener}" process="@this,bDate,eDate"  update="bDate,eDate" />
                </p:calendar>

            </p:column>
            <p:column style="text-align:left;padding-left:3px;height: 26px;" rendered="#{!orgApplyBean.ifBaseInfoApplyInfoEdit}">
                <!-- 备案有效期限详情 -->
                <h:outputText value="#{orgApplyBean.tjorginfoApply.firstGetday}" rendered="#{orgApplyBean.ifValidityPeriod}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputText>
                <p:outputLabel value="~" rendered="#{orgApplyBean.ifValidityPeriod and null ne orgApplyBean.tjorginfoApply.firstGetday}" />
                <h:outputText value="#{orgApplyBean.tjorginfoApply.filingDate}" rendered="#{orgApplyBean.ifValidityPeriod}" >
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </h:outputText>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.ifBaseInfoServiceInfoEdit}"/>
                <p:outputLabel value="服务项目：" />
            </p:column>
            <p:column style="text-align:left;padding-left:0px;height: 25px;" colspan="3">
                <p:selectManyCheckbox columns="1" layout="grid" value="#{orgApplyBean.selectServiceObjList}"
                                      rendered="#{orgApplyBean.orgType==1 and orgApplyBean.ifBaseInfoServiceInfoEdit}">
                    <f:selectItems value="#{orgApplyBean.serviceObjList}"/>
                    <p:ajax event="change" listener="#{orgApplyBean.serviceChange}" process="@this"
                            update="#{updateId}:#{tabViewId}:serDetailDatatable"></p:ajax>
                </p:selectManyCheckbox>
                <p:outputPanel rendered="#{!orgApplyBean.ifBaseInfoServiceInfoEdit}">
                    <table width="100%" style="border-color: white">
                        <c:forEach items="#{orgApplyBean.serviceItemNameList}" var="str">
                            <tr>
                                <td>
                                    <h:outputLabel value="#{str}"/>
                                </td>
                            </tr>
                        </c:forEach>
                    </table>
                </p:outputPanel>
            </p:column>
        </p:row>

        <p:row rendered="#{orgApplyBean.orgType==1 and orgApplyBean.ifHethFj}">
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 26px;">
                <p:outputLabel value="外出开展职业健康检查"/><br/>
                <p:outputLabel value="工作能力："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3"
                      rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}" >
                <p:selectOneRadio id="outWorkPower" style="width: 180px;" value="#{orgApplyBean.tjorginfoApply.outWorkPower}"
                                  >
                    <f:selectItem itemLabel="不具备" itemValue="0"/>
                    <f:selectItem itemLabel="具备" itemValue="1"/>
                    <!-- 需要控制外检车辆 显示 所以这里需要处理 -->
                    <p:ajax event="change" listener="#{orgApplyBean.onChangeOutWorkPower}" process="@this,#{updateId}" update="#{updateId}:#{tabViewId}"/>
                </p:selectOneRadio>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3"
                      rendered="#{!orgApplyBean.ifBaseInfoCommInfoEdit}">
                <p:outputLabel value="不具备" rendered="#{orgApplyBean.tjorginfoApply.outWorkPower == 0}"/>
                <p:outputLabel value="具备" rendered="#{orgApplyBean.tjorginfoApply.outWorkPower == 1}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                <p:outputLabel value="*" style="color: red" rendered="#{orgApplyBean.tjorginfoApply.state == 0}"/>
                <p:outputLabel value="申请材料："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
                <p:commandButton value="添加"  icon="ui-icon-plus"
                                 update="#{updateId}:fileUId,applyAnnexTable" oncomplete="PF('fileUIdVar').show();"
                                 process="@this" rendered="#{orgApplyBean.tjorginfoApply.state == 0}"
                                 style="margin:3px 0px 5px 0px;">
                    <f:setPropertyActionListener value="21" target="#{orgApplyBean.annexTypeBase}"/>
                </p:commandButton>
                <p:dataTable id="applyAnnexTable" value="#{orgApplyBean.applyAnnexList}"
                             emptyMessage="没有数据！" var="itm" rowIndexVar="index">
                    <p:column headerText="序号" style="width:10%;text-align: center">
                        <h:outputLabel value="#{index+1}"/>
                    </p:column>
                    <p:column headerText="申请材料名称" style="width:50%">
                        <h:outputLabel value="#{itm.fileName}"/>
                    </p:column>
                    <p:column headerText="操作" >
                        <p:commandLink value="查看"  onclick="window.open('/webFile/#{itm.filePath}')" rendered="#{itm.filePath!=null}"
                                       process="@this,#{updateId}"/>
                        <p:spacer width="5"/>
                        <p:commandLink value="删除" rendered="#{orgApplyBean.tjorginfoApply.state == 0}"
                                       process="@this" update="applyAnnexTable" action="#{orgApplyBean.deleteApplyAnnex}" >
                            <f:setPropertyActionListener value="#{itm}" target="#{orgApplyBean.selectAnnex}"/>
                            <f:setPropertyActionListener value="21" target="#{orgApplyBean.annexTypeBase}"/>
                            <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                        </p:commandLink>
                    </p:column>
                </p:dataTable>
            </p:column>
        </p:row>
    </p:panelGrid>
</ui:composition>
