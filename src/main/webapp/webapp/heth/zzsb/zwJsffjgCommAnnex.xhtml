<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <script type="text/javascript">

        //<![CDATA[
        function removeExtraZonePael(){
            //移除掉多余的地区框
            var el = jQuery('#mainForm\\:tabView\\:registrationZone\\:zonePanel');
            if(el.length>1){
                el.each(function(index){
                    if(index>0){
                        $(this).remove();
                    }
                });
            }
        }
        //]]>
        </script>
        <table width="100%">
        <tr>
            <!-- 单位法人证明材料（营业执照） -->
            <td width="50%" style="vertical-align: top">
                <p:fieldset legend="单位法人证明材料（营业执照）" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0 and !(mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0)}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             process="@this" update="fileUId" oncomplete="PF('fileUIdVar').show();">
                                <f:setPropertyActionListener value="1" target="#{mgrbean.selectZwAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList1" value="#{mgrbean.zwAnnexList1}" var="itm1" rowIndexVar="var1"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:10%;text-align: center">
                            <h:outputLabel value="#{var1+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:50%">
                            <h:outputLabel value="#{itm1.fileName}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm1.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0 and !(mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0)}"
                                           process="@this" update="zwAnnexList1" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm1}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="1" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>

                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>

            <!-- 医疗机构执业许可证 -->
            <td width="50%" style="vertical-align: top;display: #{(mgrbean.zwOrgType==3 or mgrbean.zwOrgType==1)?'none':''}" >
                <p:fieldset legend="医疗机构执业许可证" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0 and !(mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0)}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus" action="#{mgrbean.ylAddAction}"
                                             process="@this" update="photoDialog" oncomplete="removeExtraZonePael();PF('PhotoDialog').show();">
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList2" value="#{mgrbean.zwAnnexList2}" var="itm2" rowIndexVar="var2"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:40px;text-align: center">
                            <h:outputLabel value="#{var2+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:180px;">
                            <h:outputLabel value="#{itm2.fileName}"/>
                        </p:column>
                        <p:column headerText="注册地点" style="width:100px;">
                            <h:outputLabel value="#{itm2.fkByRegZoneId.zoneName}"/>
                        </p:column>
                        <p:column headerText="发证单位" style="width:150px;">
                            <h:outputLabel value="#{itm2.unitName}"/>
                        </p:column>
                        <p:column headerText="发证单位级别" style="width:80px;text-align: center;">
                            <h:outputLabel value="省级" rendered="#{itm2.unitLevel == 2}"/>
                            <h:outputLabel value="市级" rendered="#{itm2.unitLevel == 3}"/>
                            <h:outputLabel value="区县级" rendered="#{itm2.unitLevel == 4}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm2.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除"  rendered="#{mgrbean.zwOrginfoEntity.state == 0 and !(mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0)}"
                                           process="@this" update="zwAnnexList2" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm2}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="2" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>

            <!-- 放射卫生服务机构批准证书 -->
            <td width="50%" style="vertical-align: top;display: #{mgrbean.zwOrgType==1?'':'none'}">
                <p:fieldset legend="放射卫生技术服务机构批准证书" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                            rendered="#{mgrbean.zwOrgType==1}">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             process="@this" update="fileUId" oncomplete="PF('fileUIdVar').show();">
                                <f:setPropertyActionListener value="8" target="#{mgrbean.selectZwAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList8" value="#{mgrbean.zwAnnexList8}" var="itm8" rowIndexVar="var8"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:10%;text-align: center">
                            <h:outputLabel value="#{var8+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:50%">
                            <h:outputLabel value="#{itm8.fileName}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm8.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"
                                           process="@this" update="zwAnnexList8" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm8}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="8" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>

            <!-- 职业卫生技术服务机构资质证书 -->
            <td width="50%" style="vertical-align: top;display: #{mgrbean.zwOrgType==3?'':'none'}">
                <p:fieldset legend="职业卫生技术服务机构资质证书" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                            rendered="#{mgrbean.zwOrgType==3}">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             process="@this" update="fileUId" oncomplete="PF('fileUIdVar').show();">
                                <f:setPropertyActionListener value="11" target="#{mgrbean.selectZwAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList9" value="#{mgrbean.zwAnnexList9}" var="itm9" rowIndexVar="var9"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:10%;text-align: center">
                            <h:outputLabel value="#{var9+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:50%">
                            <h:outputLabel value="#{itm9.fileName}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm9.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"
                                           process="@this" update="zwAnnexList9" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm9}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="11" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>
        </tr>

        <tr>
            <!-- 放射诊疗许可证 -->
            <td width="50%" style="vertical-align: top;display: #{(mgrbean.zwOrgType==3 or mgrbean.zwOrgType==1)?'none':''}">
                <p:fieldset legend="放射诊疗许可证" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0 and !(mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0)}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             process="@this" update="fileUId" oncomplete="PF('fileUIdVar').show();">
                                <f:setPropertyActionListener value="3" target="#{mgrbean.selectZwAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList3" value="#{mgrbean.zwAnnexList3}" var="itm3" rowIndexVar="var3"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:10%;text-align: center">
                            <h:outputLabel value="#{var3+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:50%">
                            <h:outputLabel value="#{itm3.fileName}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm3.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0 and !(mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0)}"
                                           process="@this" update="zwAnnexList3" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm3}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="3" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>

            <!-- 职业健康检查机构备案回执 -->
            <td width="50%" style="vertical-align: top;display: #{mgrbean.zwOrgType!=0?'none':''}">
                <p:fieldset legend="职业健康检查机构备案回执" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                            rendered="#{mgrbean.zwOrgType==0}">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0 and !(mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0)}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             process="@this" update="fileUId" oncomplete="PF('fileUIdVar').show();">
                                <f:setPropertyActionListener value="6" target="#{mgrbean.selectZwAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList6" value="#{mgrbean.zwAnnexList6}" var="itm6" rowIndexVar="var6"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:10%;text-align: center">
                            <h:outputLabel value="#{var6+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:50%">
                            <h:outputLabel value="#{itm6.fileName}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm6.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0 and !(mgrbean.ifReviewProcess and mgrbean.zwOrgType == 0)}"
                                           process="@this" update="zwAnnexList6" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm6}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="6" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>

            <!-- 职业病诊断机构批准证书 -->
            <td width="50%" style="vertical-align: top;display: #{mgrbean.zwOrgType!=2?'none':''}">
                <p:fieldset legend="职业病诊断机构备案回执" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                            rendered="#{mgrbean.zwOrgType==2}">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             process="@this" update="fileUId" oncomplete="PF('fileUIdVar').show();">
                                <f:setPropertyActionListener value="7" target="#{mgrbean.selectZwAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList7" value="#{mgrbean.zwAnnexList7}" var="itm7" rowIndexVar="var7"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:10%;text-align: center">
                            <h:outputLabel value="#{var7+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:50%">
                            <h:outputLabel value="#{itm7.fileName}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm7.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"
                                           process="@this" update="zwAnnexList7" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm7}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="7" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>
        </tr>
        <tr>
            <!-- 委托协议和委托项目明细 -->
            <td width="50%" style="vertical-align: top;display: #{mgrbean.zwOrgType==0?'':'none'}">
                <p:fieldset legend="委托协议和委托项目明细" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             process="@this" update="fileUId" oncomplete="PF('fileUIdVar').show();">
                                <f:setPropertyActionListener value="20" target="#{mgrbean.selectZwAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList20" value="#{mgrbean.zwAnnexList20}" var="itm20" rowIndexVar="var20"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:10%;text-align: center">
                            <h:outputLabel value="#{var20+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:50%">
                            <h:outputLabel value="#{itm20.fileName}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm20.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"
                                           process="@this" update="zwAnnexList20" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm20}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="20" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>

            <!-- 房屋所有权证或租赁合同 -->
            <td width="50%" style="vertical-align: top;display: #{mgrbean.ifHaveHouseAnnex==1?'':'none'}">
                <p:fieldset legend="房屋所有权证或租赁合同" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                    <p:outputPanel styleClass="zwx_toobar_42" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
                        <h:panelGrid columns="3" style="border-color:transparent;padding:0px;">
                            <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                            <p:commandButton value="添加" icon="ui-icon-plus"
                                             process="@this" update="fileUId" oncomplete="PF('fileUIdVar').show();">
                                <f:setPropertyActionListener value="4" target="#{mgrbean.selectZwAnnexType}"/>
                            </p:commandButton>
                        </h:panelGrid>
                    </p:outputPanel>
                    <p:dataTable id="zwAnnexList4" value="#{mgrbean.zwAnnexList4}" var="itm4" rowIndexVar="var4"
                                 emptyMessage="暂无附件信息" style="margin-top: 5px;">
                        <p:column headerText="序号" style="width:10%;text-align: center">
                            <h:outputLabel value="#{var4+1}"/>
                        </p:column>
                        <p:column headerText="附件名称" style="width:50%">
                            <h:outputLabel value="#{itm4.fileName}"/>
                        </p:column>
                        <p:column headerText="操作" >
                            <p:commandLink value="查看"
                                           process="@this"  onclick="window.open('/webFile/#{itm4.filePath}')" />
                            <p:spacer width="5"/>
                            <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"
                                           process="@this" update="zwAnnexList4" action="#{mgrbean.deleteAnnex}" >
                                <f:setPropertyActionListener value="#{itm4}" target="#{mgrbean.selectZwAnnex}"/>
                                <f:setPropertyActionListener value="4" target="#{mgrbean.selectZwAnnexType}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </p:fieldset>
            </td>
        </tr>

    </table>

    <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId" resizable="false" modal="true">
        <table>
        <tr>
            <td style="text-align: right;"><p:outputLabel
                    value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                    style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
            </td>
        </tr>
        <tr>
            <td style="position: relative;top: -23px;">
            <p:fileUpload requiredMessage="请选择上传文件！" style="width:700px;" previewWidth="50"
                          fileUploadListener="#{mgrbean.handleFileUpload}"
                          label="选择文件" uploadLabel="上传" cancelLabel="取消"
                          fileLimit="5" fileLimitMessage="最多只能上传5个文件！"
                          sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                          validatorMessage="上传出错啦，重新上传！"
                          invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                          process="@this" update="@this,zwAnnexList1,zwAnnexList3,
                                              zwAnnexList6,zwAnnexList7,
                                              zwAnnexList8,zwAnnexList9,zwAnnexList20,zwAnnexList4"
                          mode="advanced" dragDropSupport="true"
                          allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
            </td>
        </tr>
        </table>
    </p:dialog>
    <p:dialog id="photoDialog" header="许可证信息" widgetVar="PhotoDialog"
              resizable="false" modal="true" width="500" style="height: 670px;">
        <p:commandButton value="上传" icon="ui-icon-plus"
                         process="@this" update="ylFileUId" oncomplete="PF('YlFileUId').show();"
                         style="margin-bottom:5px;">
            <f:setPropertyActionListener value="2" target="#{mgrbean.selectZwAnnexType}"/>
        </p:commandButton>
        <p:panelGrid id="photoDesc" style="width:100%;">
            <p:row>
                <p:column style="text-align:right;padding-right:8px;width:100px;">
                    <h:outputText value="*" style="color: red" />注册地点：
                </p:column>
                <p:column style="text-align:left;padding-left:0px;">
                    <zwx:ZoneSingleNewComp zoneList="#{mgrbean.zoneList}" id="registrationZone"
                                           zoneCodeNew="#{mgrbean.searchZoneCode}"
                                           zoneName="#{mgrbean.searchZoneName}"
                                           zoneId="#{mgrbean.searchZoneId}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:8px;width:100px;">
                    <h:outputText value="*" style="color: red" />发证单位：
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:inputText value="#{mgrbean.unitName}" maxlength="100"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:8px;width:100px;">
                    <h:outputText value="*" style="color: red" />发证单位级别：
                </p:column>
                <p:column style="text-align:left;padding-left:8px;">
                    <p:selectOneMenu value="#{mgrbean.unitLevel}" >
                        <f:selectItem itemLabel="&#45;&#45;请选择&#45;&#45;" itemValue=""/>
                        <f:selectItem itemLabel="省级" itemValue="2"/>
                        <f:selectItem itemLabel="市级" itemValue="3"/>
                        <f:selectItem itemLabel="区县级" itemValue="4"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:8px;width:100px;">
                    <h:outputText value="附件：" />
                </p:column>
                <p:column style="text-align:left;padding-left:8px;height:28px;">
                    <p:outputPanel id="photo" style="#{null != mgrbean.ylAnnex and null != mgrbean.ylAnnex.filePath?'':'display:none;'}">
                    <p:commandLink value="查看"
                                   process="@this"  onclick="window.open('/webFile/#{mgrbean.ylAnnex.filePath}')" />
                    <p:spacer width="5"/>
                    <p:commandLink value="删除" rendered="#{mgrbean.zwOrginfoEntity.state == 0}"
                                   process="@this,photo" update="photo" action="#{mgrbean.deleteYlAnnex}" >
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                    </p:commandLink>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="确认" icon="ui-icon-check" id="photoSaveBtn"
                                     process="@this,photoDesc"
                                     action="#{mgrbean.saveYlFile}" update="zwAnnexList2">
                    </p:commandButton>
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close" id="photoBackBtn"  process="@this"
                                     oncomplete="PF('PhotoDialog').hide();"   >
                    </p:commandButton>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <p:dialog header="文件上传" widgetVar="YlFileUId" id="ylFileUId" resizable="false" modal="true">
        <table>
        <tr>
            <td style="text-align: right;"><p:outputLabel
                    value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                    style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
            </td>
        </tr>
        <tr>
            <td style="position: relative;top: -23px;">
                <p:fileUpload requiredMessage="请选择上传文件！" style="width:700px;" previewWidth="50"
                              fileUploadListener="#{mgrbean.ylHandleFileUpload}"
                              label="选择文件" uploadLabel="上传" cancelLabel="取消"
                              fileLimit="1" fileLimitMessage="最多只能上传1个文件！"
                              sizeLimit="10485760" invalidSizeMessage="文件大小不能超过10M!"
                              validatorMessage="上传出错啦，重新上传！"
                              invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                              process="@this,photo" update="@this,photo"
                              mode="advanced" dragDropSupport="true"
                              allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"/>
            </td>
        </tr>
        </table>
    </p:dialog>
</ui:composition>