<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:fn="http://java.sun.com/jsp/jstl/functions"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
<style>

</style>
    <h:inputHidden id="jcItemRid" value="#{orgApplyBean.jcItemRid}"/>
    <p:panelGrid style="width:100%;margin-top: 5px;" id="serDetailDatatable">
        <f:facet name="header">
            <p:row>
                <p:column style="text-align:center;height: 20px;width:21%;">
                    <p:outputLabel value="服务类别" />
                </p:column>
                <p:column style="text-align:center;height: 20px;width:200px;">
                    <p:outputLabel value="服务项目" />
                </p:column>
                <p:column style="text-align:center;height: 20px;width:100px;" rendered="#{null ne orgApplyBean.tjorginfoApply and null ne orgApplyBean.tjorginfoApply.outWorkPower and 1 == orgApplyBean.tjorginfoApply.outWorkPower}" >
                    <p:outputLabel value="支持外检" />
                </p:column>
            </p:row>
        </f:facet>
        <p:row rendered="#{orgApplyBean.jkItemRowList.size()==0}">
            <p:column style="text-align:left;height: 27px;" colspan="2" rendered="#{null == orgApplyBean.tjorginfoApply or null == orgApplyBean.tjorginfoApply.outWorkPower and 1 != orgApplyBean.tjorginfoApply.outWorkPower}}">
                <p:outputLabel value="没有您要找的记录！" />
            </p:column>
            <p:column style="text-align:left;height: 27px;" colspan="3" rendered="#{null ne orgApplyBean.tjorginfoApply and null ne orgApplyBean.tjorginfoApply.outWorkPower and 1 == orgApplyBean.tjorginfoApply.outWorkPower}">
                <p:outputLabel value="没有您要找的记录！" />
            </p:column>
        </p:row>
        <c:forEach items="#{orgApplyBean.jkItemRowList}" var="serDetailItm" varStatus="status">
            <p:row rendered="#{orgApplyBean.ifBaseInfoServiceInfoEdit}">
                <c:if test="#{status.index==serDetailItm.rowspanStart}">
                <p:column style="text-align:left;height: 27px;" rowspan="#{serDetailItm.rowspan}">
                    <p:selectManyCheckbox value="#{serDetailItm.selectedJcItem}" >
                        <p:ajax event="change" listener="#{orgApplyBean.changeJcItem}"
                                process="@this,jcItemRid" update="#{updateId}:#{tabViewId}:serDetailDatatable"
                                onstart="document.getElementById('#{updateName}:#{tabViewId}:jcItemRid').value = #{serDetailItm.jcItem.rid}"/>
                        <f:selectItem itemLabel="#{serDetailItm.jcItem.codeName}" itemValue="#{serDetailItm.jcItem.codeNo}" />
                    </p:selectManyCheckbox>
                </p:column>
                </c:if>
                <p:column style="text-align:left;height:27px;width: 50px;" >
                    <p:selectManyCheckbox  value="#{serDetailItm.selectedSerItmDetail}" >
                        <p:ajax event="change"  listener="#{orgApplyBean.changeSerItmDetail}"
                                process="@this" update="#{updateId}:#{tabViewId}:serDetailDatatable" />
                        <f:selectItem  itemLabel="#{serDetailItm.serItem.codeName}"  itemValue="#{serDetailItm.serItem.rid}" />
                    </p:selectManyCheckbox>
                </p:column>
                <p:column style="text-align:left;height:27px;width: 50px;" rendered="#{1 == orgApplyBean.tjorginfoApply.outWorkPower}" >
                    <p:selectManyCheckbox value="#{serDetailItm.selectedIfExterInspect}"  rendered="#{serDetailItm.disabled == false}">
                        <p:ajax event="change"  process="@this" />
                        <f:selectItem itemLabel=""  itemValue="#{serDetailItm.serItem.rid}" />
                    </p:selectManyCheckbox>
                    <p:selectManyCheckbox value="#{serDetailItm.selectedIfExterInspect}" rendered="#{null == serDetailItm.disabled or serDetailItm.disabled == true}">
                        <f:selectItem itemLabel=""  itemValue="#{serDetailItm.serItem.rid}" itemDisabled="true"/>
                    </p:selectManyCheckbox>
                </p:column>
            </p:row>
            <p:row rendered="#{!orgApplyBean.ifBaseInfoServiceInfoEdit}">
                <c:if test="#{status.index==serDetailItm.rowspanStart}">
                    <p:column style="text-align:left;height: 27px;" rowspan="#{serDetailItm.rowspan}">
                        <p:outputLabel value="#{serDetailItm.jcItem.codeName}" />
                    </p:column>
                </c:if>
                <p:column style="text-align:left;height:27px;width: 50px;" >
                    <p:outputLabel value="#{serDetailItm.serItem.codeName}" />
                </p:column>
                <p:column style="text-align:left;height:27px;width: 50px;" rendered="#{orgApplyBean.tjorginfoApply.outWorkPower == 1}" >
                    <p:outputLabel value="是" rendered="#{serDetailItm.ifExterInspect=='1'}" />
                    <p:outputLabel value="否" rendered="#{serDetailItm.ifExterInspect!='1'}" />
                </p:column>
            </p:row>
        </c:forEach>
    </p:panelGrid>
</ui:composition>
