<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwOrgInfoBaseZzsbBean"-->
    <h:outputScript name="js/datatable.js"/>
    <p:fieldset legend="论文情况" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
        <ui:insert name="thesisAdd"/>
        <p:dataTable var="thesis" rowIndexVar="indexVar" value="#{mgrbean.thesisList}" id="thesisListTable"
                     emptyMessage="没有您要找的记录！">
            <p:column style="width: 50px;text-align: center;" headerText="年份">
                <p:outputLabel value="#{thesis.year}"/>
            </p:column>
            <p:column style="width: 200px;text-align: center;" headerText="作者">
                <p:outputLabel value="#{thesis.author}"/>
            </p:column>
            <p:column style="width: 375px;padding-left: 5px;" headerText="论文名称">
                <p:outputLabel value="#{thesis.thesisName}" style="text-align: left;"/>
            </p:column>
            <p:column style="width: 80px;text-align: center;" headerText="刊物类别">
                <p:outputLabel value="#{thesis.fkByPublicationId.codeName}"/>
            </p:column>
            <p:column style="width: 375px;padding-left: 5px;" headerText="刊物名称">
                <p:outputLabel value="#{thesis.publicationName}"/>
            </p:column>
            <p:column style="width: 80px;text-align: center;" headerText="影响因子">
                <p:outputLabel value="#{thesis.impactFactors}"/>
            </p:column>
            <p:column headerText="操作">
                <ui:insert name="thesisOper"/>
                <p:commandLink value="查看支撑材料" process="@this" onclick="window.open('/webFile/#{thesis.supportFilePath}')"
                               rendered="#{not empty thesis.supportFilePath}"/>
            </p:column>
        </p:dataTable>
    </p:fieldset>
    <p:fieldset legend="专著情况" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
        <ui:insert name="monographAdd"/>
        <p:dataTable var="monograph" rowIndexVar="indexVar" value="#{mgrbean.monographList}" id="monographListTable"
                     emptyMessage="没有您要找的记录！">
            <p:column style="width: 50px;text-align: center;" headerText="年份">
                <p:outputLabel value="#{monograph.year}"/>
            </p:column>
            <p:column style="width: 450px;padding-left: 5px;" headerText="论著名称">
                <p:outputLabel value="#{monograph.momographName}"/>
            </p:column>
            <p:column style="width: 375px;padding-left: 5px;" headerText="出版社名称">
                <p:outputLabel value="#{monograph.pressName}"/>
            </p:column>
            <p:column style="width: 100px;text-align: center;" headerText="主编姓名">
                <p:outputLabel value="#{monograph.psnName1}"/>
            </p:column>
            <p:column style="width: 100px;text-align: center;" headerText="副主编姓名">
                <p:outputLabel value="#{monograph.psnName2}"/>
            </p:column>
            <p:column style="width: 100px;text-align: center;" headerText="参编姓名">
                <p:outputLabel value="#{monograph.psnName3}"/>
            </p:column>
            <p:column headerText="操作">
                <ui:insert name="monographOper"/>
                <p:commandLink value="查看支撑材料" process="@this" onclick="window.open('/webFile/#{monograph.supportFilePath}')"
                               rendered="#{not empty monograph.supportFilePath}"/>
            </p:column>
        </p:dataTable>
    </p:fieldset>
    <p:fieldset legend="课题情况" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
        <p:panelGrid id="supportTopic1" style="width:100%;">
            <p:row>
                <p:column style="border-color: transparent;">
                    <p:outputPanel id="newProjectInfo">
                        <p:panelGrid style="width:100%;">
                            <f:facet name="header">
                                <p:row>
                                    <p:column
                                            style="text-align:left;padding-left:5px;height: 20px;border-bottom-color: transparent;border-right-color: transparent;"
                                            colspan="3">
                                        <p:outputLabel value="新立项课题"/>
                                    </p:column>
                                </p:row>
                            </f:facet>
                            <p:row>
                                <p:column>
                                    <ui:insert name="newProjectInfoAdd"/>
                                    <p:dataTable var="supportTopic" rowIndexVar="indexVar" value="#{mgrbean.supportTopics1}" id="newProjectTable"
                                                 emptyMessage="没有您要找的记录！" style="padding: 6px 10px 10px 10px">
                                        <p:column style="width: 80px;text-align: center;" headerText="年份">
                                            <p:outputLabel value="#{supportTopic.year}"/>
                                        </p:column>
                                        <p:column style="width: 250px;text-align: center;" headerText="责任人">
                                            <p:outputLabel value="#{supportTopic.responsible}"/>
                                        </p:column>
                                        <p:column style="width: 400px;padding-left: 5px;" headerText="课题名称">
                                            <p:outputLabel value="#{supportTopic.topicName}" style="text-align: left;"/>
                                        </p:column>
                                        <p:column style="width: 250px;padding-left: 5px;" headerText="立项批准部门">
                                            <p:outputLabel value="#{supportTopic.awardDepartment}"/>
                                        </p:column>
                                        <p:column style="width: 100px;text-align: center" headerText="等级">
                                            <p:outputLabel value="#{supportTopic.fkByPrizeLevelId.codeName}"/>
                                        </p:column>
                                        <p:column headerText="操作">
                                            <ui:insert name="newProjectInfoOper"/>
                                            <p:commandLink value="查看支撑材料" process="@this" onclick="window.open('/webFile/#{supportTopic.supportFilePath}')"
                                                           rendered="#{not empty supportTopic.supportFilePath}"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid id="supportTopic2" style="width:100%;">
            <p:row>
                <p:column style="border-color: transparent;">
                    <p:outputPanel id="inStudyInfo">
                        <p:panelGrid style="width:100%;">
                            <f:facet name="header">
                                <p:row>
                                    <p:column
                                            style="text-align:left;padding-left:5px;height: 20px;border-bottom-color: transparent;border-right-color: transparent;"
                                            colspan="3">
                                        <p:outputLabel value="在研课题"/>
                                    </p:column>
                                </p:row>
                            </f:facet>
                            <p:row>
                                <p:column>
                                    <ui:insert name="inStudyInfoAdd"/>
                                    <p:dataTable var="supportTopic" rowIndexVar="indexVar" value="#{mgrbean.supportTopics2}" id="inStudyInfoTable"
                                                 emptyMessage="没有您要找的记录！" style="padding: 6px 10px 10px 10px">
                                        <p:column style="width: 80px;text-align: center;" headerText="年份">
                                            <p:outputLabel value="#{supportTopic.year}"/>
                                        </p:column>
                                        <p:column style="width: 250px;text-align: center;" headerText="责任人">
                                            <p:outputLabel value="#{supportTopic.responsible}"/>
                                        </p:column>
                                        <p:column style="width: 400px;padding-left: 5px;" headerText="课题名称">
                                            <p:outputLabel value="#{supportTopic.topicName}" style="text-align: left;"/>
                                        </p:column>
                                        <p:column style="width: 250px;padding-left: 5px;" headerText="立项批准部门">
                                            <p:outputLabel value="#{supportTopic.awardDepartment}"/>
                                        </p:column>
                                        <p:column style="width: 100px;text-align: center" headerText="等级">
                                            <p:outputLabel value="#{supportTopic.fkByPrizeLevelId.codeName}"/>
                                        </p:column>
                                        <p:column headerText="操作">
                                            <ui:insert name="inStudyInfoOper"/>
                                            <p:commandLink value="查看支撑材料" process="@this" onclick="window.open('/webFile/#{supportTopic.supportFilePath}')"
                                                           rendered="#{not empty supportTopic.supportFilePath}"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
        <p:panelGrid id="supportTopic3" style="width:100%;">
            <p:row>
                <p:column style="border-color: transparent;">
                    <p:outputPanel id="awardsInfo">
                        <p:panelGrid style="width:100%;">
                            <f:facet name="header">
                                <p:row>
                                    <p:column
                                            style="text-align:left;padding-left:5px;height: 20px;border-bottom-color: transparent;border-right-color: transparent;"
                                            colspan="3">
                                        <p:outputLabel value="获奖课题"/>
                                    </p:column>
                                </p:row>
                            </f:facet>
                            <p:row>
                                <p:column>
                                    <ui:insert name="awardsInfoAdd"/>
                                    <p:dataTable var="supportTopic" rowIndexVar="indexVar" value="#{mgrbean.supportTopics3}" id="awardsInfoTable"
                                                 emptyMessage="没有您要找的记录！" style="padding: 6px 10px 10px 10px">
                                        <p:column style="width: 80px;text-align: center;" headerText="年份">
                                            <p:outputLabel value="#{supportTopic.year}"/>
                                        </p:column>
                                        <p:column style="width: 250px;text-align: center;" headerText="责任人">
                                            <p:outputLabel value="#{supportTopic.responsible}"/>
                                        </p:column>
                                        <p:column style="width: 280px;padding-left: 5px;" headerText="项目名称">
                                            <p:outputLabel value="#{supportTopic.topicName}" style="text-align: left;"/>
                                        </p:column>
                                        <p:column style="width: 280px;padding-left: 5px;" headerText="获奖名称">
                                            <p:outputLabel value="#{supportTopic.prizeName}"/>
                                        </p:column>
                                        <p:column style="width: 100px;text-align: center" headerText="等级">
                                            <p:outputLabel value="#{supportTopic.fkByPrizeLevelId.codeName}"/>
                                        </p:column>
                                        <p:column style="width: 180px;padding-left: 5px;" headerText="授奖部门">
                                            <p:outputLabel value="#{supportTopic.awardDepartment}"/>
                                        </p:column>
                                        <p:column headerText="操作">
                                            <ui:insert name="awardsInfoOper"/>
                                            <p:commandLink value="查看支撑材料" process="@this" onclick="window.open('/webFile/#{supportTopic.supportFilePath}')"
                                                           rendered="#{not empty supportTopic.supportFilePath}"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:fieldset legend="科研成果情况" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
        <p:panelGrid id="registFiles" style="width:100%;">
            <p:row>
                <p:column style="border-color: transparent;">
                    <p:outputPanel id="patentInfo">
                        <p:panelGrid style="width:100%;">
                            <f:facet name="header">
                                <p:row>
                                    <p:column
                                            style="text-align:left;padding-left:5px;height: 20px;border-bottom-color: transparent;border-right-color: transparent;"
                                            colspan="3">
                                        <p:outputLabel value="专利"/>
                                    </p:column>
                                </p:row>
                            </f:facet>
                            <p:row>
                                <p:column>
                                    <ui:insert name="patentInfoAdd"/>
                                    <p:dataTable var="item" value="#{mgrbean.patentList}" id="patentListTable"
                                                 emptyMessage="没有您要找的记录！" style="padding: 6px 10px 10px 10px">
                                        <p:column style="width: 80px;text-align: center;" headerText="年份">
                                            <p:outputLabel value="#{item[1]}"/>
                                        </p:column>
                                        <p:column style="width: 300px;text-align: center;" headerText="完成人">
                                            <p:outputLabel value="#{item[2]}"/>
                                        </p:column>
                                        <p:column style="width: 500px;" headerText="专利名称">
                                            <p:outputLabel value="#{item[3]}"/>
                                        </p:column>
                                        <p:column style="width: 150px;text-align: center;" headerText="专利类型">
                                            <p:outputLabel value="#{item[4]}"/>
                                            <p:outputLabel value="（" rendered="#{'1' eq item[5]}"/>
                                            <p:outputLabel value="#{item[9]}" rendered="#{'1' eq item[5]}"/>
                                            <p:outputLabel value="）" rendered="#{'1' eq item[5]}"/>
                                        </p:column>
                                        <p:column style="width: 200px;text-align: center;" headerText="专利号">
                                            <p:outputLabel value="#{item[6]}"/>
                                        </p:column>
                                        <p:column headerText="操作" >
                                            <ui:insert name="patentInfoOper"/>
                                            <p:commandLink value="查看支撑材料" process="@this" onclick="window.open('/webFile/#{item[8]}')"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:outputPanel>
                    <p:outputPanel id="standardInfo" style="margin-top: 10px;">
                        <p:panelGrid style="width:100%;">
                            <f:facet name="header">
                                <p:row>
                                    <p:column
                                            style="text-align:left;padding-left:5px;height: 20px;border-bottom-color: transparent;border-right-color: transparent;"
                                            colspan="3">
                                        <p:outputLabel value="标准"/>
                                    </p:column>
                                </p:row>
                            </f:facet>
                            <p:row>
                                <p:column>
                                    <ui:insert name="standardInfoAdd"/>
                                    <p:dataTable var="item" value="#{mgrbean.standardList}" id="standardListTable"
                                                 emptyMessage="没有您要找的记录！" style="padding: 6px 10px 10px 10px">
                                        <p:column style="width: 80px;text-align: center;" headerText="年份">
                                            <p:outputLabel value="#{item[1]}"/>
                                        </p:column>
                                        <p:column style="width: 500px;" headerText="标准名称">
                                            <p:outputLabel value="#{item[3]}"/>
                                        </p:column>
                                        <p:column style="width: 300px;text-align: center;" headerText="标准主要起草人">
                                            <p:outputLabel value="#{item[2]}"/>
                                        </p:column>
                                        <p:column style="width: 200px;text-align: center;" headerText="标准编号">
                                            <p:outputLabel value="#{item[6]}"/>
                                        </p:column>
                                        <p:column style="width: 150px;text-align: center;" headerText="标准级别">
                                            <p:outputLabel value="#{item[7]}"/>
                                        </p:column>
                                        <p:column headerText="操作" >
                                            <ui:insert name="standardInfoOper"/>
                                            <p:commandLink value="查看支撑材料" process="@this"
                                                           onclick="window.open('/webFile/#{item[8]}')"/>
                                        </p:column>
                                    </p:dataTable>
                                </p:column>
                            </p:row>
                        </p:panelGrid>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <!--先进适宜技术筛选推广应用-->
    <p:fieldset legend="先进适宜技术筛选推广应用" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
        <ui:insert name="advanceAdd"/>
        <p:dataTable var="advance" rowIndexVar="indexVar" value="#{mgrbean.advanceList}" id="advanceListTable"
                     emptyMessage="没有您要找的记录！">
            <p:column style="width: 70%;padding-left: 5px;"   headerText="名称">
                <p:inputText style="width: 80%;" maxlength="100"
                             value="#{advance.advanceName}"  rendered="#{mgrbean.zwOrginfoEntity.state ne 1}"/>
                <p:outputLabel value="#{advance.advanceName}" rendered="#{mgrbean.zwOrginfoEntity.state eq 1}"/>
            </p:column>
            <p:column  headerText="操作" rendered="#{mgrbean.zwOrginfoEntity.state ne 1}" >
                <ui:insert name="advanceOper"/>
            </p:column>
        </p:dataTable>
    </p:fieldset>
    <!-- 加入弹框 -->
    <ui:insert name="zwResearchTopicsInfoDiag"/>
</ui:composition>