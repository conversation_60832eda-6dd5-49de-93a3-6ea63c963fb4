<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
   <!-- 托管Bean -->
   <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.BusScopeItemConfigBean"-->
   <ui:param name="mgrbean" value="#{busScopeItemConfigBean}"/>
   <h:head>
      <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
      <script type="text/javascript">
         //<![CDATA[
         function mergeCells() {
            // 获取元素
            var tds = $("#tabView\\:mainForm\\:dataTable_data > tr.ui-widget-content> td:nth-child(1)");
            // 初始化变量
            var cellsToMerge = [];
            var i=0;
            tds.each(function() {
               var td = $(this);
               var innerText = td.text();
               cellsToMerge.push({ text: innerText, index: i, rowspan: 1 });
               i++;
            });

            // 对单元格进行处理
            let tdText="";
            let tdIndex=0;
            for (let i = 0; i < cellsToMerge.length; i++) {
               let current = cellsToMerge[i];
               if(tdText===""){
                  tdText=current.text;
                  tdIndex=current.index;
                  continue;
               }
               // 如果下一个单元格存在且文本相同，则增加rowspan
               if (tdText === current.text) {
                  cellsToMerge[tdIndex].rowspan++;
                  tds[i].remove();
                  continue;
               }
               // 更新单元格的rowspan属性，并重置当前和下一个的文本和rowspan
               tdText = current.text;
               tdIndex = current.index;
            }

            // 最后一次遍历后，如果有跨行的单元格，需要再次处理以确保rowspan正确
            for (let i = 0; i < cellsToMerge.length; i++) {
               let current = cellsToMerge[i];
               if (current.rowspan > 1) {
                  let td = tds.eq(current.index);
                  td.attr("rowspan", current.rowspan);
               }
            }
         }

         //]]>
      </script>
      <style type="text/css">

      </style>
   </h:head>
   <h:body onload="mergeCells();">
      <p:tabView id="tabView" dynamic="true" cache="true"
                 activeIndex="#{mgrbean.activeTab}" style="border:1px; padding:0;">
         <p:tab id="list" title="mainTitle" titleStyle="display:none;">
            <h:form id="mainForm">
               <h:outputStylesheet name="css/default.css"/>
               <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
               <h:outputStylesheet name="css/ui-tabs.css"/>
               <h:outputStylesheet name="css/ui-cs.css"/>
               <h:outputScript library="js" name="namespace.js"/>
               <h:outputScript name="js/validate/system/validate.js"/>
               <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                  <f:facet name="header">
                     <p:row>
                        <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                           <p:outputLabel value="业务范围与检测项目关系配置"/>
                        </p:column>
                     </p:row>
                  </f:facet>
               </p:panelGrid>
               <p:dataTable var="itm" value="#{mgrbean.dataModel}"
                            paginator="true" rows="#{mgrbean.pageSize}"
                            paginatorPosition="bottom" rowIndexVar="R"
                            paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                            rowsPerPageTemplate="#{mgrbean.pageSize}" id="dataTable"
                            lazy="true" emptyMessage="没有您要找的记录！"
                            currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                            rendered="#{dataTable==null}">
                  <p:columnGroup type="header">
                     <p:row>
                        <p:column headerText="业务范围大类" style="width:200px;text-align: center"/>
                        <p:column headerText="检测项目大类" style="width:150px;text-align: center;"/>
                        <p:column headerText="操作" style="text-align: center;"/>
                     </p:row>
                  </p:columnGroup>
                  <p:column style="text-align: center;">
                     <h:outputText value="#{itm[1]}"/>
                  </p:column>
                  <p:column>
                     <h:outputText value="#{itm[3]}"/>
                  </p:column>
                  <p:column>
                     <p:commandLink value="配置" resetValues="true"
                                    action="#{mgrbean.modInitAction}" update=":tabView"
                                    process="@this">
                        <f:setPropertyActionListener
                                target="#{mgrbean.busiRelItem}" value="#{itm}"/>
                     </p:commandLink>
                  </p:column>
               </p:dataTable>
            </h:form>
         </p:tab>
         <p:tab id="edit" title="edit" titleStyle="display:none;">
            <ui:include src="/webapp/heth/zzsb/busScopeItemConfigEdit.xhtml"/>
         </p:tab>

      </p:tabView>

      <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
      <ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
      <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
   </h:body>
</f:view>
</html>