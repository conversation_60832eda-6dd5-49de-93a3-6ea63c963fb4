<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <script type="text/javascript">

        </script>
        <style type="text/css">

        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制结果录入" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel"
                                 action="#{mgrbean.cancelAction}" process="@this"
                                 update=":tabView" rendered="#{mgrbean.checkRst.state == 1}">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
	<ui:define name="insertOtherContents">
        <p:panelGrid style="margin-bottom: 10px;width:100%" >
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="考核类型："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;width: 280px;">
                    <h:outputText value="#{mgrbean.checkRst.fkCheckTypeId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="机构名称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;" >
                    <h:outputText value="#{mgrbean.checkRst.fkUnitId.unitname}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="质控机构："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;width: 280px;">
                    <h:outputText value="#{mgrbean.checkRst.fkCheckUnitId.unitname}"/>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="质控日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:9px;" >
                    <h:outputText value="#{mgrbean.checkRst.checkDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputText>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="专家组组长："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;width: 280px;">
                    <h:outputText value="#{mgrbean.checkRst.expertLeaders}"/>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="专家组成员："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;" >
                    <h:outputText value="#{mgrbean.checkRst.expertMembers}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="发现的问题："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;width: 280px;" colspan="3">
                    <p:inputTextarea rows="5"  readonly="true" autoResize="false"
                                     style="resize: none;width: 799px;height: 120px;"
                                     maxlength="1000" value="#{mgrbean.checkRst.problems}"
                    />
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="质控结论："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;width: 280px;">
                    <h:outputText value="#{mgrbean.checkRst.fkCheckRstId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="质控结果告知书："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;" >
                    <p:outputPanel id="noticePanel">
                    <p:spacer width="5" />
                    <p:commandButton value="查看"  onclick="window.open('/webFile/#{mgrbean.checkRst.noticeFilePath}')" rendered="#{mgrbean.checkRst.noticeFilePath!=null}"
                                     process="@this"/>
                    </p:outputPanel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;width: 260px;height: 38px ">
                    <p:outputLabel value="整改结果："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;width: 280px;">
                    <h:outputText value="#{mgrbean.checkRst.fkZgRstId == null ? '' :mgrbean.checkRst.fkZgRstId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;width: 260px;">
                    <p:outputLabel value="整改报告："/>
                </p:column>
                <p:column style="text-align:left;padding-left:10px;" >
                    <p:outputPanel id="zgPanel">
                        <p:spacer width="5" />
                        <p:commandButton value="查看"  onclick="window.open('/webFile/#{mgrbean.checkRst.zgFilePath}')" rendered="#{mgrbean.checkRst.zgFilePath!=null}"
                                     process="@this"/>
                    </p:outputPanel>
                </p:column>
            </p:row>
        </p:panelGrid>
    </ui:define>
</ui:composition>