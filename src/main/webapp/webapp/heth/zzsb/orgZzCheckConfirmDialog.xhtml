<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <p:dialog id="checkConfirmDialog" header="审核意见"
              widgetVar="CheckConfirmDialog" resizable="false" width="840"
              modal="true">
        <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.OrgZzCheckListBean"-->
        <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;">
            <h:outputLabel value="提示：" style="color:red;position: relative;"/>
            <h:outputLabel value="将审核所有已查询的待审核数据，请确认！" style="color:blue;position: relative;"/>
        </p:outputPanel>
        <p:panelGrid style="margin-top: 10px;margin-bottom: 10px;width:100%" id="auditPanel">
            <p:row>
                <p:column style="text-align: right;width: 100px">
                    <h:outputText value="*" style="color:red;"/>
                    <p:outputLabel value="审核结果："/>
                </p:column>
                <p:column style="text-align:left;width: 300px">
                    <p:selectOneRadio value="#{mgrbean.editCheckRst}" style="width: 120px">
                        <f:selectItem itemLabel="通过" itemValue="1"/>
                        <f:selectItem itemLabel="退回" itemValue="2"/>
                        <p:ajax event="change" process="@this,auditPanel" listener="#{mgrbean.changeCheckState}"
                                update="auditPanel" resetValues="true"/>
                    </p:selectOneRadio>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align: right;width: 100px">
                    <h:outputText value="*" style="color:red;" rendered="#{2 == mgrbean.editCheckRst}"/>
                    <p:outputLabel value="审核意见："/>
                </p:column>
                <p:column style="text-align:left;width: 300px;">
                    <p:inputTextarea rows="5" autoResize="false" id="batchCheckRst"
                                     style="resize: none;width: 594px;height: 120px;margin-left: 5px"
                                     maxlength="200" value="#{mgrbean.editCheckAudit}"/>
                </p:column>
            </p:row>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="确定" icon="ui-icon-check"
                                     action="#{mgrbean.checkBatchAction}"
                                     onclick="zwx_loading_start()" oncomplete="zwx_loading_stop()"/>
                    <p:commandButton value="取消" icon="ui-icon-close" type="button" resetValues="true"
                                     onclick="PF('CheckConfirmDialog').hide();"/>
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>

</ui:composition>