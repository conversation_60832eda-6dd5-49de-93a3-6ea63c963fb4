<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:p="http://primefaces.org/ui">
    <style type="text/css">
        .zwx-tooltip{
            -webkit-line-clamp: 1;
        }
        .psnDiag .ui-widget-content{
            padding-top: 0px !important;
        }
        .zwx-tooltip-two{
            cursor: pointer;
            color:#25AAE1;
            word-break:break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }
        .zwx-tooltip-two:hover{
            color:orange !important;
            text-decoration:underline;
        }
    </style>
    <script type="text/javascript">
        //<![CDATA[
        var show = false;
        var target;
        $(function() {
            $("body").mousemove(
                function(e) {
                    if (show == true) {
                        var label = document.getElementById("label");
                        if (e.pageX < $(label).offset().left
                            || e.pageX > ($(label).offset().left + $(label).width())
                            || e.pageY < ($(target).offset().top)
                            || e.pageY > ($(label).offset().top + $(label).height())) {
                            $(label).fadeOut("6000",
                                function() {
                                    show = false;
                                });
                        }
                    }
                });
        });
        function getLabelPanel(div, desc) {
            if (!show) {
                var label = document.getElementById("label");
                $(label).css({
                    "display" : "none"
                });
                $(label).css(
                    {
                        "top" : $(div).offset().top + $(div).height()
                            + 3,
                        "left" : $(div).offset().left
                            - ($(label).width() / 2+20)
                    });
                var str = "";
                var code = desc.split(",");
                if (code.length>0) {
                    for (var int = 0; int < code.length; int++) {
                        str = str + code[int] + "<br/>";
                    }
                }
                $("#descDiv").html(str);
                $(label).fadeIn("6000", function() {
                    target = div;
                    show = true;
                });
            }
        }
        //]]>
    </script>
    <p:tabView widgetVar="OrgInfoViewTableView"
               style="margin-top: 5px;">
        <p:tab title="基本信息">
            <!-- 基本信息tab头部加入的内容 -->
            <ui:insert name="orgInfoBaseInfoHead"/>
            <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;"
                         id="caseGrid">
                <p:row>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="单位名称：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;"
                              colspan="3">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.orgName}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="注册地址：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;"
                              colspan="3">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.orgAddr}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrViewbean.orgType == 3}">
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="通讯地址：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;"
                              colspan="3">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.communiAddr}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrViewbean.orgType == 3}">
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="实验室地址：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;"
                              colspan="3">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.labAddr}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="法定代表人：" />
                    </p:column>
                    <p:column
                            style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.orgFz}" />
                    </p:column>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="#{mgrViewbean.orgType == 3?'单位类型':'法定代表人职务'}：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.orgFzzw}" rendered="#{mgrViewbean.orgType!=3}"/>
                        <p:outputLabel value="#{mgrViewbean.zwOrginfoEntity.fkByUnitTypeId.codeName}" rendered="#{mgrViewbean.orgType==3}"/>
                    </p:column>
                </p:row>
                <p:row>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="联系人：" />
                    </p:column>
                    <p:column
                            style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.linkMan}" />
                    </p:column>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="联系人手机：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.linkMb}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="电话：" />
                    </p:column>
                    <p:column
                            style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.linkTel}" />
                    </p:column>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="传真：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.fax}" />
                    </p:column>
                </p:row>
                <p:row>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="邮编：" />
                    </p:column>
                    <p:column
                            style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.zipcode}" />
                    </p:column>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="电子邮箱：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.email}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{!(mgrViewbean.orgType == 0 and mgrViewbean.ifZoneRecord==1)}">
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="资质证书编号：" rendered="#{mgrViewbean.orgType == 1 or mgrViewbean.orgType == 3}"/>
                        <p:outputLabel value="备案编号：" rendered="#{mgrViewbean.orgType == 0 or mgrViewbean.orgType == 2}"/>
                    </p:column>
                    <p:column
                            style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.certNo}" />
                    </p:column>
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="#{mgrViewbean.orgType == 0 or mgrViewbean.orgType == 2?'备案日期':'发证时间'}：" rendered="#{mgrViewbean.ifValidityPeriod != '1' or (mgrViewbean.ifValidityPeriod == '1' and mgrViewbean.orgType != 0)}"/>
                        <p:outputLabel value="备案有效期限：" rendered="#{mgrViewbean.ifValidityPeriod == '1' and mgrViewbean.orgType == 0}" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrViewbean.ifValidityPeriod != '1' or (mgrViewbean.ifValidityPeriod == '1' and mgrViewbean.orgType != 0)}">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.firstGetday}">
                            <f:convertDateTime pattern="yyyy-MM-dd"
                                               timeZone="Asia/Shanghai" locale="cn" />
                        </p:outputLabel>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrViewbean.ifValidityPeriod == '1' and mgrViewbean.orgType == 0}">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.firstGetday}">
                            <f:convertDateTime pattern="yyyy-MM-dd"
                                               timeZone="Asia/Shanghai" locale="cn" />
                        </p:outputLabel>
                        ~
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.filingDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd"
                                               timeZone="Asia/Shanghai" locale="cn" />
                        </p:outputLabel>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrViewbean.ifShowApproveUnit==1 and (mgrViewbean.orgType == 1 or  mgrViewbean.orgType == 2 or mgrViewbean.orgType == 3)}">
                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="备案单位名称：" rendered="#{mgrViewbean.orgType == 2}"/>
                        <p:outputLabel value="资质批准单位：" rendered="#{mgrViewbean.orgType == 1 or mgrViewbean.orgType == 3}"/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
                        <p:outputLabel value="#{mgrViewbean.zwOrginfoEntity.rcdUnitName}" />
                    </p:column>
                </p:row>

                <p:row rendered="#{(mgrViewbean.orgType == 0 or mgrViewbean.orgType == 2) and mgrViewbean.ifShowRcdOrg}">
                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="备案管理机构：" />
                    </p:column>
                    <p:column colspan="3" style="text-align:left;padding-left:3px;height: 25px;">
                        <p:outputLabel value="#{mgrViewbean.rcdOrgMap.get(mgrViewbean.zwOrginfoEntity.rcdOrgId)}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{!(mgrViewbean.orgType == 0 and mgrViewbean.ifZoneRecord==1)}">
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="状态：" />
                    </p:column>
                    <p:column
                            style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                        <p:outputLabel value="正常"
                                       rendered="#{null == mgrViewbean.zwOrginfoEntity.cancelState or mgrViewbean.zwOrginfoEntity.cancelState == 0}" />
                        <p:outputLabel value="注销"
                                       rendered="#{mgrViewbean.zwOrginfoEntity.cancelState == 1}" />
                    </p:column>
                    <p:column rendered="#{mgrViewbean.orgType == 1 or mgrViewbean.orgType == 3}"
                              style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="失效日期：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;"
                              rendered="#{mgrViewbean.orgType == 1 or mgrViewbean.orgType == 3}"
                              colspan="3">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.validDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd"
                                               timeZone="Asia/Shanghai" locale="cn" />
                        </p:outputLabel>
                    </p:column>
                    <p:column rendered="#{!(mgrViewbean.orgType == 0 and mgrViewbean.ifZoneRecord==1) and (mgrViewbean.orgType == 0 or mgrViewbean.orgType == 2) and mgrViewbean.zwOrginfoEntity.cancelState == 1}"
                              style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="注销日期："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{!(mgrViewbean.orgType == 0 and mgrViewbean.ifZoneRecord==1) and (mgrViewbean.orgType == 0 or mgrViewbean.orgType == 2) and mgrViewbean.zwOrginfoEntity.cancelState == 1}">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.cancelDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd"
                                               timeZone="Asia/Shanghai" locale="cn" />
                        </p:outputLabel>
                    </p:column>
                </p:row>
                <p:row
                        rendered="#{!(mgrViewbean.orgType == 0 and mgrViewbean.ifZoneRecord==1) and (mgrViewbean.orgType == 1 or mgrViewbean.orgType == 3) and mgrViewbean.zwOrginfoEntity.cancelState == 1}">
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="注销日期：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="#{mgrViewbean.orgType == 3?'1':'3'}">
                        <p:outputLabel
                                value="#{mgrViewbean.zwOrginfoEntity.cancelDate}">
                            <f:convertDateTime pattern="yyyy-MM-dd"
                                               timeZone="Asia/Shanghai" locale="cn" />
                        </p:outputLabel>
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrViewbean.orgType == 3}">
                        <p:outputLabel value="资质等级：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrViewbean.orgType == 3}">
                        <p:outputLabel value="#{mgrViewbean.zwOrginfoEntity.fkByLevelId.codeName}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrViewbean.orgType == 3 and mgrViewbean.zwOrginfoEntity.cancelState != 1}">
                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="资质等级：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
                        <p:outputLabel value="#{mgrViewbean.zwOrginfoEntity.fkByLevelId.codeName}" />
                    </p:column>
                </p:row>
                <p:row rendered="#{!(mgrViewbean.orgType == 0 and mgrViewbean.ifZoneRecord==1)}">
                    <p:column
                            style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="#{mgrViewbean.orgType == 3?'业务范围：':'服务项目：'}" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;"
                              colspan="3">
                        <table width="100%" style="border-color: white">
                            <c:forEach items="#{mgrViewbean.serviceList}"
                                       var="service">
                                <tr>
                                    <td><h:outputLabel value="#{service}" /></td>
                                </tr>
                            </c:forEach>
                        </table>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrViewbean.orgType == 0 and mgrViewbean.ifZoneRecord==1}">
                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                        <p:outputLabel value="备案信息："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
                        <p:dataTable value="#{mgrViewbean.tjorgRecords}"
                                     emptyMessage="没有数据！" var="itm">
                            <p:column headerText="备案地区" style="text-align:center;width:100px;">
                                <h:outputText value="#{itm.fkByZoneId.zoneName}"/>
                            </p:column>
                            <p:column headerText="备案单位名称" style="width:160px;">
                                <h:outputText value="#{itm.unitName}"/>
                            </p:column>
                            <p:column headerText="备案编号" style="text-align:center;width:150px;">
                                <h:outputText value="#{itm.rcdNo}"/>
                            </p:column>
                            <p:column headerText="备案日期" style="text-align:center;width:80px;">
                                <h:outputText value="#{itm.certDate}">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                                </h:outputText>
                            </p:column>
                            <p:column headerText="服务项目" >
                                <h:outputText value="#{itm.serviceItems}"></h:outputText>
                            </p:column>
                            <p:column headerText="状态" style="text-align:center;width:80px;">
                                <h:outputText value="正常" rendered="#{itm.stateMark==0}"></h:outputText>
                                <h:outputText value="注销" rendered="#{itm.stateMark==1}"></h:outputText>
                            </p:column>
                            <p:column headerText="注销日期" style="text-align:center;width:80px;">
                                <h:outputText value="#{itm.logoutDate}">
                                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                                </h:outputText>
                            </p:column>
                        </p:dataTable>
                    </p:column>
                </p:row>
                <p:row rendered="#{mgrViewbean.orgType==0 and (mgrViewbean.ifZoneApprove or mgrViewbean.ifHethFj)}">
                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrViewbean.ifZoneApprove}">
                        <p:outputLabel value="批准的执业区域：" />
                    </p:column>
                    <p:column colspan="#{mgrViewbean.ifHethFj?1:3}" style="text-align:left;padding-left:3px;height: 25px;#{mgrViewbean.ifHethFj?'width: 260px;':''}" rendered="#{mgrViewbean.ifZoneApprove}">
                        <p:outputLabel value="#{mgrViewbean.zoneApproveName}" />
                    </p:column>
                    <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrViewbean.ifHethFj}">
                        <p:outputLabel value="外出开展职业健康检查"/><br/>
                        <p:outputLabel value="工作能力："/>
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="#{mgrViewbean.ifZoneApprove?1:3}" rendered="#{mgrViewbean.ifHethFj}">
                        <p:outputLabel value="不具备" rendered="#{mgrViewbean.zwOrginfoEntity.outWorkPower == 0}"/>
                        <p:outputLabel value="具备" rendered="#{mgrViewbean.zwOrginfoEntity.outWorkPower == 1}"/>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:tab>
        <p:tab title="人员信息">
            <!-- 人员信息tab头部加入的内容 -->
            <ui:insert name="orgInfoPsnInfoHead"/>

            <p:panelGrid style="width:100%;margin:10px 0px;" rendered="#{mgrViewbean.orgType == 0 and mgrViewbean.ifPersonSummary==1}">
                <f:facet name="header">
                    <p:row>
                        <p:column style="text-align:left;padding:6px;" styleClass="ui-state-default">
                            <h:outputText value="人员汇总"></h:outputText>
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row>
                    <p:column>
                        <p:outputPanel style="padding:12px 6px;" id="personSummary">
                            <table>
                                <c:forEach items="#{mgrViewbean.personSummaryList}" var="itm">
                                    <tr>
                                        <c:forEach items="#{itm}" var="val" varStatus="valIndex">
                                            <td style="border:none;">
                                                <h:outputText value="#{val}" escape="false" style="padding:6px;"></h:outputText>
                                                <h:outputText value="|" style="float: right;" rendered="#{valIndex.index!=itm.size()-1}"></h:outputText>
                                            </td>
                                        </c:forEach>
                                    </tr>
                                </c:forEach>
                            </table>
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
            <p:dataTable var="itm" value="#{mgrViewbean.zwOrginfoEntity.zwOrgPsns}"
                         id="psnDatatable" emptyMessage="暂无记录！" paginator="true" rows="#{mgrViewbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="#{mgrViewbean.perPageSize}" lazy="true" pageLinks="5"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         style="margin-top:5px;width:100%">
                <p:column headerText="姓名" style="text-align:center;width:80px;">
                    <p:commandLink value="#{itm.tdZwPsninfo.empName}"
                                   update=":#{baseComposition}:codeEditDialog" process="@this"
                                   oncomplete="PF('CodeEditDialog').show();"
                                   action="#{mgrViewbean.codeViewAction}">
                        <f:setPropertyActionListener value="#{itm.tdZwPsninfo}"
                                                     target="#{mgrViewbean.tdZwPsninfo}" />
                    </p:commandLink>
                </p:column>
                <p:column headerText="性别" style="text-align:center;width:40px;">
                    <p:outputLabel value="#{itm.tdZwPsninfo.sex}" />
                </p:column>
                <p:column headerText="职称" style="text-align:center;width:100px; ">
                    <p:outputLabel value="#{itm.tdZwPsninfo.fkByTitleId.codeName}" />
                </p:column>
                <p:column headerText="人员属性" style="width:260px; ">
                    <p:outputLabel id="psnTypes" value="#{itm.tdZwPsninfo.psnTypes}" styleClass="zwx-tooltip"/>
                    <p:tooltip for="psnTypes" style="max-width:260px;">
                        <p:outputLabel value="#{itm.tdZwPsninfo.psnTypes}" escape="false"/>
                    </p:tooltip>
                </p:column>
                <p:column headerText="在岗状态" style="text-align:center;width:60px; ">
                    <p:outputLabel value="在职" rendered="#{itm.onDuty=='1'}"></p:outputLabel>
                    <p:outputLabel value="离职" rendered="#{itm.onDuty=='2'}"></p:outputLabel>
                </p:column>
                <p:column headerText="#{mgrViewbean.orgType==0?'证书（发文）编号':'证书编号'}" style="width:180px;">
                    <p:outputLabel value="#{itm.tdZwPsninfo.certNoStr}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')==-1}"/>
                    <p:outputLabel value="#{itm.tdZwPsninfo.certNoStr.substring(0,itm.tdZwPsninfo.getCertNoStr().indexOf(','))}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0}"/>
                    <div style="background-color: #F35E37;display: inline;padding: 3px;border-radius: 3px;display:#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0?'':'none'}">
                        <p:outputLabel value="其他" style="color:white;"
                                       rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')>0}"
                                       onmouseover="getLabelPanel(this,'#{itm.tdZwPsninfo.certNoStr.substring(itm.tdZwPsninfo.getCertNoStr().indexOf(',')+1,itm.tdZwPsninfo.getCertNoStr().length())}')"/>
                    </div>
                    <p:outputLabel value="#{itm.tdZwPsninfo.certNoStr}" rendered="#{itm.tdZwPsninfo.getCertNoStr().indexOf(',')==0}"/>
                </p:column>
                <p:column headerText="个人剂量计编号" style="width:200px;"
                          rendered="false">
                    <p:outputLabel value="#{itm.jlCode}" />
                </p:column>
                <p:column headerText="个人剂量累积剂量" style="width:200px;"
                          rendered="false">
                    <p:outputLabel value="#{itm.ljJl}" />
                </p:column>
                <p:column headerText="从事项目" style="padding-left:5px;width:260px;"
                          rendered="#{mgrViewbean.orgType == 0}">
                    <!--<p:outputLabel value="#{itm.wordItemStr}" />-->
                    <!-- 只显示跟检查资质相关的资质类别的资质范围 -->
                    <p:outputLabel id="zzlbRange" value="#{itm.tdZwPsninfo.zzlbRange}" styleClass="zwx-tooltip"/>
                    <p:tooltip for="zzlbRange" style="max-width:260px;">
                        <p:outputLabel value="#{itm.tdZwPsninfo.zzlbRange}" escape="false"/>
                    </p:tooltip>
                </p:column>
                <p:column headerText="从事科室" style="padding-left:5px;width:260px;" rendered="#{mgrViewbean.orgType == 0}">
                    <p:outputLabel id="wordOfficeName" value="#{itm.wordOfficeName}" styleClass="zwx-tooltip"/>
                    <p:tooltip for="wordOfficeName" style="max-width:260px;">
                        <p:outputLabel value="#{itm.wordOfficeName}" escape="false"/>
                    </p:tooltip>
                </p:column>
            </p:dataTable>
            <p:outputPanel rendered="#{mgrViewbean.orgType == 0 and mgrViewbean.ifPersonSummary==1}"
                           style="min-height: 200px;padding:6px;margin:10px 0px;line-height: 21px;border-radius: 3px;border:1px solid #FFE58F;background-color: #FEFBE6;">
                <!-- 填报说明 -->
                <h:outputText value="#{mgrViewbean.fillDesc}" escape="false" style="color:#DE7C05;"></h:outputText>
            </p:outputPanel>
        </p:tab>
        <p:tab title="仪器信息">
            <!-- 仪器信息tab头部加入的内容 -->
            <ui:insert name="orgInfoInstInfoHead"/>
            <p:dataTable var="instItm" value="#{mgrViewbean.zwOrginfoEntity.zwInst}"
                         id="instDatatable"
                         emptyMessage="暂无记录！" paginator="true" rows="#{mgrViewbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="#{mgrViewbean.perPageSize}"  lazy="true"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         style="margin-top:5px;width:100%">
                <p:column headerText="仪器名称" style="padding-left: 3px;width:120px;">
                    <p:outputLabel value="#{instItm.tdZwInstinfo.instName}" />
                </p:column>
                <p:column headerText="仪器型号" style="text-align:center;width:100px;">
                    <p:outputLabel value="#{instItm.tdZwInstinfo.instModel}" />
                </p:column>
                <p:column headerText="仪器类型" style="text-align:center;width:120px;">
                    <p:outputLabel value="#{null == instItm.tdZwInstinfo.fkByInstKindDetalId ? '' :
								instItm.tdZwInstinfo.fkByInstKindDetalId.codeName}"/>
                </p:column>
                <p:column headerText="仪器编号" style="text-align:center;width:120px;">
                    <p:outputLabel value="#{instItm.tdZwInstinfo.instCode}" />
                </p:column>
                <p:column headerText="仪器状态" style="text-align:center;width:100px;" >
                    <p:outputLabel value="#{null ne instItm.tdZwInstinfo.instStateId ? instItm.tdZwInstinfo.instStateId.codeName : ''}"/>
                </p:column>
                <p:column headerText="最近校验日期" style="text-align:center;width:120px">
                    <p:outputLabel value="#{instItm.tdZwInstinfo.lastAcptDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
                <p:column headerText="校验周期（年）" style="text-align:center;width:120px">
                    <p:outputLabel value="#{instItm.tdZwInstinfo.lastAcptCircleDot}"/>
                </p:column>
                <p:column headerText="是否外包" style="text-align:center;width:100px;" rendered="#{(mgrViewbean.orgType == 1 or mgrViewbean.orgType == 3) ? false:true}">
                    <p:outputLabel value="是"
                                   rendered="#{instItm.tdZwInstinfo.outter == 1}" />
                    <p:outputLabel value="否"
                                   rendered="#{instItm.tdZwInstinfo.outter == 0}" />
                </p:column>
                <p:column headerText="是否支持外检" style="text-align:center;width:6%" rendered="#{mgrViewbean.orgType == 0}" >
                    <p:outputLabel value="是" rendered="#{instItm.tdZwInstinfo.ifSuptOutChk == 1}"/>
                    <p:outputLabel value="否" rendered="#{instItm.tdZwInstinfo.ifSuptOutChk == 0}"/>
                </p:column>
            </p:dataTable>
        </p:tab>
        <p:tab title="检查项目"
               rendered="#{mgrViewbean.orgType == 0}">
            <!-- 检查项目tab头部加入的内容 -->
            <ui:insert name="orgInfoJcxmInfoHead"/>
            <p:panelGrid style="width:100%;margin-top: 5px;">
                <f:facet name="header">
                    <p:row>
                        <p:column style="text-align:center;height: 20px;width:80px;">
                            <p:outputLabel value="序号" />
                        </p:column>
                        <p:column style="text-align:center;height: 20px;width:200px;">
                            <p:outputLabel value="项目大类" />
                        </p:column>
                        <p:column style="text-align:center;height: 20px;">
                            <p:outputLabel value="项目小类" />
                        </p:column>
                        <p:column style="text-align:center;height: 20px;">
                            <p:outputLabel value="是否外包" />
                        </p:column>
                    </p:row>
                </f:facet>
                <c:forEach items="#{mgrViewbean.showItemList}" var="v">
                    <p:row>
                        <p:column style="text-align:center;height: 27px;">
                            <p:outputLabel value="#{v[2]}" />
                        </p:column>
                        <p:column style="text-align:center;height: 27px;"
                                  rendered="#{v[0]}" rowspan="#{v[1]}">
                            <p:outputLabel value="#{v[3]}" />
                        </p:column>
                        <p:column style="height: 27px;text-align:left;padding-left:3px;">
                            <p:outputLabel value="#{v[4]}" />
                        </p:column>
                        <p:column style="height: 27px;text-align:left;padding-left:3px;">
                            <p:outputLabel value="是" rendered="#{v[5]}" />
                            <p:outputLabel value="否" rendered="#{(v[5]==null or !v[5])}" />
                        </p:column>
                    </p:row>
                </c:forEach>
            </p:panelGrid>
        </p:tab>
        <p:tab title="服务明细" rendered="#{mgrViewbean.orgType == 0 and mgrViewbean.ifServiceDetails =='1'}">
            <!-- 服务明细tab头部加入的内容 -->
            <ui:insert name="orgInfoServerDetailInfoHead"/>
            <p:panelGrid style="width:100%;margin-top: 8px;" id="serDetailDatatable">
                <f:facet name="header">
                    <p:row>
                        <p:column style="text-align:center;height: 20px;width:21%;">
                            <p:outputLabel value="服务类别" />
                        </p:column>
                        <p:column style="text-align:center;height: 20px;width:200px;">
                            <p:outputLabel value="服务项目" />
                        </p:column>
                        <p:column style="text-align:center;height: 20px;width:200px;" rendered="#{mgrViewbean.zwOrginfoEntity.outWorkPower == 1}">
                            <p:outputLabel value="支持外检" />
                        </p:column>
                    </p:row>
                </f:facet>
                <p:row rendered="#{mgrViewbean.jkItemRowList.size()==0}">
                    <p:column style="text-align:left;height: 27px;" colspan="2" rendered="#{mgrViewbean.zwOrginfoEntity == null or mgrViewbean.zwOrginfoEntity.outWorkPower== null or mgrViewbean.zwOrginfoEntity.outWorkPower != 1}">
                        <p:outputLabel value="没有您要找的记录！" />
                    </p:column>
                    <p:column style="text-align:left;height: 27px;" colspan="3" rendered="#{mgrViewbean.zwOrginfoEntity.outWorkPower == 1}">
                        <p:outputLabel value="没有您要找的记录！" />
                    </p:column>
                </p:row>
                <c:forEach items="#{mgrViewbean.jkItemRowList}" var="serDetailItm" varStatus="status" >
                    <p:row>
                        <c:if test="#{status.index==serDetailItm.rowspanStart}">
                            <p:column style="text-align:left;height: 27px;" rowspan="#{serDetailItm.rowspan}">
                                <p:outputLabel value="#{serDetailItm.jcItem.codeName}" />
                            </p:column>
                        </c:if>
                        <p:column style="text-align:left;height:27px;width: 50px;" >
                            <p:outputLabel value="#{serDetailItm.serItem.codeName}" />
                        </p:column>
                        <p:column style="text-align:left;height:27px;width: 50px;" rendered="#{mgrViewbean.zwOrginfoEntity.outWorkPower == 1}" >
                            <p:outputLabel value="是" rendered="#{serDetailItm.ifExterInspect=='1'}" />
                            <p:outputLabel value="否" rendered="#{serDetailItm.ifExterInspect=='0'}" />
                        </p:column>
                    </p:row>
                </c:forEach>
            </p:panelGrid>
        </p:tab>
        <p:tab title="外检车辆" rendered="#{mgrViewbean.orgType == 0 and mgrViewbean.ifHethFj and null ne mgrViewbean.zwOrginfoEntity and null ne mgrViewbean.zwOrginfoEntity.outWorkPower and 1 == mgrViewbean.zwOrginfoEntity.outWorkPower}" >
            <!-- 外检车辆tab头部加入的内容 -->
            <ui:insert name="orgInfoOutCarInfoHead"/>
            <p:dataTable var="outCarItm" value="#{mgrViewbean.tjorgOutcarZzsbList}"
                         id="outCarDatatable"
                         emptyMessage="没有您要找的记录！" paginator="true" rows="#{mgrViewbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                         rowsPerPageTemplate="#{mgrViewbean.pageSize}"  lazy="true"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         style="margin-top:5px;width:100%">
                <p:column headerText="车牌号" style="padding-left: 3px;text-align:center;width:10%;">
                    <p:outputLabel value="#{outCarItm.carNo}"/>
                </p:column>
                <p:column headerText="品牌" style="text-align:center;width:10%;">
                    <p:outputLabel value="#{outCarItm.carBand}"/>
                </p:column>
                <p:column headerText="型号" style="text-align:center;width:10%;">
                    <p:outputLabel value="#{outCarItm.carStyle}"/>
                </p:column>
                <p:column headerText="车架号" style="text-align:center;width:15%;">
                    <p:outputLabel value="#{outCarItm.carFrameNo}"/>
                </p:column>
                <p:column headerText="#{mgrViewbean.outCarVersion != '1'?'出厂':'购置'}日期" style="text-align:center;width:10%;">
                    <p:outputLabel value="#{outCarItm.productionDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
                <p:column headerText="配套仪器" style="width:35%;" >
                    <!-- 如果缺少id instNamesId 不生效 -->
                    <h:outputLabel id="instNamesId"
                                   value="#{outCarItm.instNames}"
                                   style="cursor: pointer;text-align: left; word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;color:#25AAE1">
                        <p:tooltip for="@parent" value="#{outCarItm.instNames}"  style="width:300px;word-break:break-all;word-wrap:break-word;" />
                    </h:outputLabel>
                </p:column>
                <p:column headerText="状态" style="text-align:center;width:5%;" >
                    <p:outputLabel value="启用" rendered="#{null ne outCarItm.stateMark and 1 == outCarItm.stateMark}"/>
                    <p:outputLabel value="停用" rendered="#{null ne outCarItm.stateMark and 0 == outCarItm.stateMark}"/>
                </p:column>
            </p:dataTable>
        </p:tab>
        <p:tab title="质量管理体系"  rendered="#{mgrViewbean.ifQualAnnex != null and mgrViewbean.ifQualAnnex == '1' and null ne mgrViewbean.topOfInstitutionList and mgrViewbean.topOfInstitutionList.size()>0 }">
            <!-- 质量管理体系tab头部加入的内容 -->
            <ui:insert name="orgInfoAnnexQualInfoHead"/>
            <c:forEach items="#{mgrViewbean.topOfInstitutionList}" var="top" >
                <p:fieldset legend="#{top.codeName}"  style="margin-top: 5px;margin-bottom: 5px;" >
                    <p:panelGrid style="width:100%;" id="topInsPanel#{top.rid}" >
                        <p:row>
                            <p:column style="text-align:center;height: 27px;width:260px;padding-left:10px;" styleClass="ui-state-default">
                                <p:outputLabel value="制度名称" />
                            </p:column>
                            <p:column style="text-align:center;width:200px;padding-left:10px;" styleClass="ui-state-default">
                                <p:outputLabel value="有无" />
                            </p:column>
                            <p:column style="text-align:center;padding-left:10px;" styleClass="ui-state-default">
                                <p:outputLabel value="附件" />
                            </p:column>
                        </p:row>
                        <c:forEach items="#{mgrViewbean.qualAnnexMap.get(top.rid)}" var="v">
                            <p:row>
                                <p:column style="text-align:left;height: 27px;padding-left:10px;">
                                    <p:outputLabel value="#{v.fkByRuleId.codeName}"
                                                   rendered="#{v.fkByRuleId.rid != top.rid}" />
                                    <p:outputLabel value="#{v.otherRuleName}"
                                                   rendered="#{v.fkByRuleId.rid == top.rid}" />
                                </p:column>
                                <p:column style="text-align:center;">
                                    <p:outputLabel value="#{v.ifHas == 1 ? '有' : '无'}" />
                                </p:column>
                                <p:column style="text-align:left;padding-left:10px;">
                                    <p:commandLink value="查看" rendered="#{v.annexPath != '' and null != v.annexPath}"
                                                   process="@this"  onclick="window.open('/webFile/#{v.annexPath}')" />
                                </p:column>
                            </p:row>
                        </c:forEach>
                    </p:panelGrid>
                </p:fieldset>
            </c:forEach>
        </p:tab>
        <p:tab title="资质附件">
            <!-- 资质附件tab头部加入的内容 -->
            <ui:insert name="orgInfoZzfjInfoHead"/>
            <p:outputPanel>
                <table width="100%">
                    <tr>
                        <!-- 单位法人证明材料（营业执照） -->
                        <td width="50%" style="vertical-align: top">
                            <p:fieldset legend="单位法人证明材料（营业执照）" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                                <p:dataTable value="#{mgrViewbean.zwAnnexList1}" var="itm1"
                                             rowIndexVar="var1" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:10%;text-align: center">
                                        <h:outputLabel value="#{var1+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:50%">
                                        <h:outputLabel value="#{itm1.fileName}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm1.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>
                        </td>

                        <td width="50%" style="vertical-align: top">
                            <!-- 医疗机构执业许可证 -->
                            <p:fieldset legend="医疗机构执业许可证" toggleable="true" toggleSpeed="500"
                                        style="margin-top: 5px;margin-bottom: 5px;" rendered="#{mgrViewbean.orgType==0 or mgrViewbean.orgType==2}">
                                <p:dataTable id="zwAnnexList2" value="#{mgrViewbean.zwAnnexList2}" var="itm2"
                                             rowIndexVar="var2" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:40px;text-align: center">
                                        <h:outputLabel value="#{var2+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:180px">
                                        <h:outputLabel value="#{itm2.fileName}" />
                                    </p:column>
                                    <p:column headerText="注册地点" style="width:100px;">
                                        <h:outputLabel value="#{itm2.fkByRegZoneId.zoneName}" />
                                    </p:column>
                                    <p:column headerText="发证单位" style="width:150px;">
                                        <h:outputLabel value="#{itm2.unitName}"/>
                                    </p:column>
                                    <p:column headerText="发证单位级别" style="width:80px;text-align: center;">
                                        <h:outputLabel value="省级" rendered="#{itm2.unitLevel == 2}" />
                                        <h:outputLabel value="市级" rendered="#{itm2.unitLevel == 3}" />
                                        <h:outputLabel value="区县级" rendered="#{itm2.unitLevel == 4}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm2.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>

                            <!-- 放射卫生服务机构批准证书 -->
                            <p:fieldset legend="放射卫生技术服务机构批准证书" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                                        rendered="#{mgrViewbean.orgType==1}">
                                <p:dataTable id="zwAnnexList8" value="#{mgrViewbean.zwAnnexList8}" var="itm8"
                                             rowIndexVar="var8" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:10%;text-align: center">
                                        <h:outputLabel value="#{var8+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:50%">
                                        <h:outputLabel value="#{itm8.fileName}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm8.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>

                            <!-- 职业卫生技术服务机构资质证书 -->
                            <p:fieldset legend="职业卫生技术服务机构资质证书" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                                        rendered="#{mgrViewbean.orgType==3}">
                                <p:dataTable id="zwAnnexList9" value="#{mgrViewbean.zwAnnexList9}"
                                             var="itm9" rowIndexVar="var9" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:10%;text-align: center">
                                        <h:outputLabel value="#{var9+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:50%">
                                        <h:outputLabel value="#{itm9.fileName}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm9.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>
                        </td>
                    </tr>

                    <tr>
                        <!-- 放射诊疗许可证 -->
                        <td width="50%" style="vertical-align: top;display: #{(zwOrgInfoSearchZzsbBean.selectObj[1]==3 or zwOrgInfoSearchZzsbBean.selectObj[1]==1)?'none':''};">
                            <p:fieldset legend="放射诊疗许可证" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                                <p:dataTable id="zwAnnexList3" value="#{mgrViewbean.zwAnnexList3}"
                                             var="itm3" rowIndexVar="var3" emptyMessage="暂无附件信息"
                                             style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:10%;text-align: center">
                                        <h:outputLabel value="#{var3+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:50%">
                                        <h:outputLabel value="#{itm3.fileName}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm3.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>
                        </td>

                        <td width="50%" style="vertical-align: top;">
                            <!-- 职业健康检查机构备案回执 -->
                            <p:fieldset legend="职业健康检查机构备案回执" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                                        rendered="#{mgrViewbean.orgType==0}">
                                <p:dataTable id="zwAnnexList6" value="#{mgrViewbean.zwAnnexList6}"
                                             var="itm6" rowIndexVar="var6" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:10%;text-align: center">
                                        <h:outputLabel value="#{var6+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:50%">
                                        <h:outputLabel value="#{itm6.fileName}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm6.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>

                            <!-- 职业病诊断机构批准证书 -->
                            <p:fieldset legend="职业病诊断机构备案回执" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;"
                                        rendered="#{mgrViewbean.orgType==2}">
                                <p:dataTable id="zwAnnexList7" value="#{mgrViewbean.zwAnnexList7}"
                                             var="itm7" rowIndexVar="var7" emptyMessage="暂无附件信息" style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:10%;text-align: center">
                                        <h:outputLabel value="#{var7+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:50%">
                                        <h:outputLabel value="#{itm7.fileName}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm7.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>
                        </td>
                    </tr>
                    <tr>
                        <!-- 委托协议和委托项目明细 -->
                        <td width="50%" style="vertical-align: top;display: #{mgrViewbean.orgType==0?'':'none'};">
                            <p:fieldset legend="委托协议和委托项目明细" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                                <p:dataTable id="zwAnnexList20" value="#{mgrViewbean.zwAnnexList20}"
                                             var="itm20" rowIndexVar="var20" emptyMessage="暂无附件信息"
                                             style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:10%;text-align: center">
                                        <h:outputLabel value="#{var20+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:50%">
                                        <h:outputLabel value="#{itm20.fileName}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm20.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>
                        </td>
                        <!-- 房屋所有权证或租赁合同 -->
                        <td width="50%" style="vertical-align: top;display: #{mgrViewbean.ifHaveHouseAnnex==1?'':'none'};">
                            <p:fieldset legend="房屋所有权证或租赁合同" toggleable="true" toggleSpeed="500" style="margin-top: 5px;margin-bottom: 5px;">
                                <p:dataTable id="zwAnnexList4" value="#{mgrViewbean.zwAnnexList4}"
                                             var="itm4" rowIndexVar="var4" emptyMessage="暂无附件信息"
                                             style="margin-top: 5px;">
                                    <p:column headerText="序号" style="width:10%;text-align: center">
                                        <h:outputLabel value="#{var4+1}" />
                                    </p:column>
                                    <p:column headerText="附件名称" style="width:50%">
                                        <h:outputLabel value="#{itm4.fileName}" />
                                    </p:column>
                                    <p:column headerText="操作">
                                        <p:commandLink value="查看"
                                                       process="@this"  onclick="window.open('/webFile/#{itm4.filePath}')" />
                                    </p:column>
                                </p:dataTable>
                            </p:fieldset>
                        </td>
                    </tr>
                </table>
            </p:outputPanel>
        </p:tab>
        <p:tab title="质量控制结果" rendered="#{(mgrViewbean.orgType == 0 or mgrViewbean.orgType == 2 )and mgrViewbean.zkCheckRst == '1'}">
            <!-- 质量控制结果tab头部加入的内容 -->
            <ui:insert name="orgInfoZkCheckRstInfoHead"/>
            <p:dataTable var="rstItm" value="#{mgrViewbean.zwCheckRstList}"
                         id="zkCheckRstDatatable"
                         emptyMessage="没有您要找的记录！" paginator="true" rows="#{mgrViewbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                         rowsPerPageTemplate="#{mgrViewbean.perPageSize}"  lazy="true"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         style="margin-top:5px;width:100%">
                <p:column headerText="专家组组长" style="padding-left: 3px;width:10%">
                    <p:outputLabel value="#{rstItm[0]}"/>
                </p:column>
                <p:column headerText="专家组成员" style="padding-left: 3px;width:10%">
                    <p:outputLabel value="#{rstItm[1]}"/>
                </p:column>
                <p:column headerText="发现的问题" style="padding-left: 3px;width:24%">
                    <p:outputLabel id="rstTypes" value="#{rstItm[2]}" styleClass="zwx-tooltip-two"/>
                    <p:tooltip for="rstTypes" style="max-width:799px;">
                        <p:outputLabel value="#{rstItm[2]}" escape="false"/>
                    </p:tooltip>
                </p:column>
                <p:column headerText="质控结论" style="text-align:center;width:6%">
                    <p:outputLabel value="#{rstItm[3]}"/>
                </p:column>
                <p:column headerText="整改结果" style="text-align:center;width:6%">
                    <p:outputLabel value="#{rstItm[4]}"/>
                </p:column>
                <p:column headerText="质控日期" style="text-align:center;width:6%">
                    <p:outputLabel value="#{rstItm[5]}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
                <p:column headerText="质控机构" style="padding-left: 3px;width:15%">
                    <p:outputLabel value="#{rstItm[6]}"/>
                </p:column>
                <p:column headerText="操作" style="padding-left:5px;">
                    <ui:insert name="insertInstActions"/>
                    <p:commandLink value="结果告知书"
                                   process="@this"  onclick="window.open('/webFile/#{rstItm[7]}')" />
                    <p:spacer width="5" rendered="#{rstItm[8] != null}"/>
                    <p:commandLink value="整改报告" rendered="#{rstItm[8] != null}"
                                   process="@this"  onclick="window.open('/webFile/#{rstItm[8]}')" />
                </p:column>
            </p:dataTable>
        </p:tab>
    </p:tabView>


    <!-- 人员信息 -->
    <p:dialog id="codeEditDialog" header="人员信息" widgetVar="CodeEditDialog" resizable="false" width="950" height="600" modal="true" styleClass="psnDiag">
        <p:outputPanel  style="width:905px; height: 40px;margin-left: 15px;position: fixed; margin-top: 0px; top: auto; z-index: 9999;" styleClass="zwx_toobar_42">
            <h:panelGrid columns="3" style="border-color:transparent;padding-bottom: 4px;padding-top: 1px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="关闭" icon="ui-icon-close" id="codeCloseBtn" onclick="PF('CodeEditDialog').hide();" immediate="true"/>
            </h:panelGrid>
        </p:outputPanel>
        <p:outputPanel style="width: 100%;height: 40px;" />
        <p:outputPanel  style="width:97%;padding-left: 14px;" id="codeEditGrid">
            <ui:param name="psnInfoBase" value="#{mgrViewbean.psnInfoBase}"/>
            <ui:param name="psnInfoForm" value="#{baseComposition}"/>
            <ui:param name="ifEdit" value="true"/>
            <ui:include src="psnInfoViewComm.xhtml">
                <ui:param name="zwOrgType" value="#{mgrViewbean.orgType}" />
                <ui:param name="psnTypeNotNull" value="#{mgrViewbean.orgType != 3}" />
            </ui:include>
        </p:outputPanel>
    </p:dialog>
    <!-- 人员信息-专业资质信息弹框 -->
    <ui:include src="zzlbInfoViewDialogComm.xhtml" />
    <div id="label"
         style="position:absolute;z-index:100;display:none;width:150px;">
        <div id="triangle-up"></div>
        <div
                style="border-radius:5px;padding:10px;background:#4D4D4D;text-align:left;color:white;word-wrap: break-word;"
                id="descDiv"></div>
    </div>
</ui:composition>