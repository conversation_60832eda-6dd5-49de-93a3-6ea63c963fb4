<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <!--@elvariable id="tdZwPsnInfoZzsbBean" type="com.chis.modules.heth.zzsb.web.TdZwPsnInfoZzsbBean"-->
    <h:form id="viewForm">
        <p:panelGrid style="width:100%;margin-bottom:5px;" id="viewTitleGrid">
            <f:facet name="header">
                <p:row>
                    <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                        <p:outputLabel value="专业技术人员详情"/>
                    </p:column>
                </p:row>
            </f:facet>
        </p:panelGrid>

        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="2" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="返回" icon="ui-icon-close" id="backBtn"
                                 action="#{tdZwPsnInfoZzsbBean.backAction}"
                                 update=":tabView" immediate="true"/>
            </h:panelGrid>
        </p:outputPanel>

        <p:panelGrid style="width:100%;margin-top:5px;" id="viewGrid">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;width: 150px;height: 30px;">
                    <p:outputLabel value="姓名："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;width: 350px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.empName}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;width: 150px;height: 30px;">
                    <p:outputLabel value="身份证号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.idcCard}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="性别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.sex}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="出生年月："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="**********"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="国籍："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.nationality}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="民族："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.fkByNation.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="学历："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.fkByEducationId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="毕业证书编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.gradNo}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="学位证书编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.certNo}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="手机："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.mobileNo}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="职称："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.thetitle}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="职称证书编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.titleCertNo}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="医师资格证编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.doctCertNo}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="医师资格级别："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.fkByDoctLevelId.codeName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="医师资格专业："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.fkByDoctSpecId.codeName}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="医师资格发证日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.doctCertDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="医师执业证书编号："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.doctPraNo}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="医师执业地点："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.doctPraPlace}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="医师执业发证日期："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.doctPraDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="职务："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.position}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="固定电话："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.telPhone}"/>
                </p:column>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="邮政编码："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.postcode}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 30px;">
                    <p:outputLabel value="通讯地址："/>
                </p:column>
                <p:column style="text-align:left;padding-left:3px;height: 30px;" colspan="3">
                    <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.linkAddr}"/>
                </p:column>
            </p:row>

            <!--
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <p:outputLabel value="医师资格证编号："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:3px;">
                                <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.doctCertNo}"/>
                            </p:column>
                        </p:row>
                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;">
                                <p:outputLabel value="医师资格类别："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:3px;">
                                <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.fkByDoctTypeId.codeName}"/>
                            </p:column>
                            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                                <p:outputLabel value="从事专业："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:3px;">
                                <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.prof}"/>
                            </p:column>
                        </p:row>

                        <p:row>
                            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                                <p:outputLabel value="创建人："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:3px;">
                                <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.createManid}"/>
                            </p:column>

                            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                                <p:outputLabel value="单位名称："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:3px;">
                                <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.tsUnit.unitname}"/>
                            </p:column>
                            <p:column style="text-align:right;padding-right:3px;">
                                <p:outputLabel value="创建日期："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:3px;">
                                <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.createDate}">
                                    <f:convertDateTime pattern="yyyy-MM-dd HH:mm:ss" timeZone="Asia/Shanghai" locale="cn"/>
                                </p:outputLabel>
                            </p:column>
                            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                                <p:outputLabel value="手机："/>
                            </p:column>
                            <p:column style="text-align:left;padding-left:3px;">
                                <p:outputLabel value="#{tdZwPsnInfoZzsbBean.entity.mobileNo}"/>
                            </p:column>

                        </p:row>-->


        </p:panelGrid>


        <!--<p:panelGrid style="width:100%;margin-top:5px;" id="subViewGrid">
            <f:facet name="header">
                <p:row>
                    <p:column style="height: 25px;">
                        <h:outputLabel value=""/>
                    </p:column>
                </p:row>
            </f:facet>-->
        <!--<p:row>
            <p:column width="100%">-->
        <p:fieldset id="parttimeFiltset" legend="人员资质" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable var="itm" value="#{tdZwPsnInfoZzsbBean.zzlbDataList}" paginator="true" rows="10"
                         paginatorPosition="bottom"
                         id="dangerItemListDataTable" emptyMessage="没有数据！">
                <p:column headerText="资质类型" style="width:150px;">
                    <h:outputLabel value="#{itm[1]}"/>
                </p:column>
                <p:column headerText="证书编号" style="width:150px;">
                    <h:outputLabel value="#{itm[2]}"/>
                </p:column>
                <p:column headerText="发证日期" style="width:150px;">
                    <h:outputLabel value="#{itm[3]}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </h:outputLabel>
                </p:column>
                <p:column headerText="资质范围" style="width:150px;">
                    <h:outputLabel value="#{itm[4]}"/>
                </p:column>
            </p:dataTable>
        </p:fieldset>

        <p:fieldset id="parttimeFiltset1" legend="工作关系" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:dataTable var="itm" value="#{tdZwPsnInfoZzsbBean.workRelationships}" paginator="true" rows="10"
                         paginatorPosition="bottom" rowIndexVar="RowIndex"
                         id="dangerItemListDataTable1" emptyMessage="没有数据！">
                <p:column headerText="序号" style="width:100px;text-align: center">
                    <!--@elvariable id="RowIndex" type="java.lang.Integer"-->
                    <h:outputLabel value="${RowIndex+1}"/>
                </p:column>
                <p:column headerText="工作单位">
                    <h:outputLabel value="#{itm}"/>
                </p:column>
                <!-- <p:column headerText="单位资质" >
                     <h:outputLabel value="职业健康检查机构资质" rendered="#{itm[1] == 0}" />
                     <h:outputLabel value="放射卫生技术服务机构资质" rendered="#{itm[1] == 1}" />
                     <h:outputLabel value="职业病诊断机构资质" rendered="#{itm[1] == 2}" />
                 </p:column>-->
            </p:dataTable>
        </p:fieldset>
        <!--   </p:column>
       </p:row>-->
        <!--      </p:panelGrid>-->
    </h:form>
</ui:composition>

