<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <style>
        myCalendar input{
            width: 164px;
        }

    </style>
    <p:fieldset legend="基本信息" toggleable="true" toggleSpeed="500"
                style="margin-top: 5px;margin-bottom: 5px;" >
        <p:remoteCommand name="findFlowByIdc" action="#{psnInfoBase.findFlowByIdc(psnInfoForm)}" process="@this,:#{psnInfoForm}:baseInfo"/>
        <p:remoteCommand name="findTitleCentInfo" action="#{psnInfoBase.findTitleCentInfo}" process="@this,:#{psnInfoForm}:resume" update=":#{psnInfoForm}:resume"/>

        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" id="baseInfo">
            <p:row>
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <p:inputText style="visibility: hidden;width: 0px;"/>
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="身份证号："/>
                </p:column>
                <p:column style="width:240px;">
                    <p:outputPanel style="align-items: center;-webkit-box-align: center;display: flex;">
                        <p:inputText value="#{psnInfoBase.psnInfo.idcCard}" style="width:180px;"
                                     onblur="findFlowByIdc();" id="idcCard" rendered="#{psnInfoBase.orgPsnInfoRid == null}" />
                        <p:inputText value="#{psnInfoBase.psnInfo.idcCard}" style="width:180px;" readonly="true"
                                     rendered="#{psnInfoBase.orgPsnInfoRid != null}" />
                    </p:outputPanel>
                </p:column>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="姓名："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.empName}" maxlength="50" style="width:180px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="性别："/>
                </p:column>
                <p:column>
                    <p:selectOneRadio value="#{psnInfoBase.psnInfo.sex}" id="sex">
                        <f:selectItem itemValue="男" itemLabel="男"/>
                        <f:selectItem itemValue="女" itemLabel="女"/>
                    </p:selectOneRadio>
                </p:column>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="出生年月："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.birthday}" maxlength="10" style="width:180px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="国籍："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.nationality}" maxlength="25" style="width:180px;"/>
                </p:column>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="民族："/>
                </p:column>
                <p:column>
                    <p:selectOneMenu value="#{psnInfoBase.psnInfo.fkByNation.rid}" style="width: 187px;">
                        <f:selectItem itemValue="" itemLabel="--请选择--"/>
                        <f:selectItems value="#{psnInfoBase.nationList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"/>
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="手机号码："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.mobileNo}" style="width:180px;"/>
                </p:column>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="电子邮箱："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.email}" style="width:180px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="固定电话："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.telPhone}" style="width:180px;"/>
                </p:column>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="邮政编码："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.postcode}" style="width:180px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="通讯地址："/>
                </p:column>
                <p:column colspan="3">
                    <p:inputText value="#{psnInfoBase.psnInfo.linkAddr}" maxlength="100" style="width:450px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="身份证复印件："/>
                </p:column>
                <p:column colspan="3">
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.idcPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="1"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.idcAnnexName}" rendered="#{psnInfoBase.psnInfo.idcPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.idcPath}')"
                                     rendered="#{psnInfoBase.psnInfo.idcPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.idcPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:baseInfo"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.idcPath!=null}"
                                     update=":#{psnInfoForm}:baseInfo">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="1"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:fieldset legend="个人履历" toggleable="true" toggleSpeed="500"
                style="margin-top: 5px;margin-bottom: 5px;" >
        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" id="resume">
            <p:row>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="学历："/>
                </p:column>
                <p:column style="width:240px;">
                    <p:selectOneMenu value="#{psnInfoBase.psnInfo.fkByEducationId.rid}" style="width: 187px;">
                        <f:selectItem itemValue="" itemLabel="--请选择--"/>
                        <f:selectItems value="#{psnInfoBase.educationList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"/>
                    </p:selectOneMenu>
                </p:column>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="毕业证书编号："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.gradNo}" maxlength="50" style="width:180px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="学位："/>
                </p:column>
                <p:column>
                    <p:selectOneMenu value="#{psnInfoBase.psnInfo.fkByDegreeId.rid}" style="width: 187px;" >
                        <f:selectItem itemValue="" itemLabel="--请选择--"/>
                        <f:selectItems value="#{psnInfoBase.degreeList}" var="itm" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}"/>
                    </p:selectOneMenu>
                </p:column>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="学位证书编号："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.certNo}"  maxlength="50" style="width:180px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="职称："/>
                </p:column>
                <p:column>
                    <p:selectOneMenu value="#{psnInfoBase.psnInfo.fkByTitleId.rid}" style="width: 187px;" onchange="findTitleCentInfo();">
                        <f:selectItem itemValue="" itemLabel="--请选择--"/>
                        <f:selectItems value="#{psnInfoBase.titleList}" var="itm"
                                       itemValue="#{itm.rid}" itemLabel="#{itm.codeName}" />
                    </p:selectOneMenu>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="职称级别："/>
                </p:column>
                <p:column>
                    <p:selectOneMenu style="width:187px;" value="#{psnInfoBase.psnInfo.titleLevelId}">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{psnInfoBase.titleLevelList}"
                                       itemValue="#{itm.rid}" itemLabel="#{itm.codeName}" var="itm" />
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="职称证书编号："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.titleCertNo}" maxlength="50" style="width:180px;"/>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;" rendered="#{psnInfoBase.titleCentflag!='1'}"/>
                    <h:outputText value="职称证书附件："/>
                </p:column>
                <p:column>
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.titleCentPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="6"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.titleCentAnnexName}" rendered="#{psnInfoBase.psnInfo.titleCentPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.titleCentPath}')"
                                     rendered="#{psnInfoBase.psnInfo.titleCentPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.titleCentPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:resume"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.titleCentPath!=null}"
                                     update=":#{psnInfoForm}:resume">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="6"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="职务："/>
                </p:column>
                <p:column colspan="#{psnInfoBase.zwOrgType eq 4 ? '3' : '1'}">
                    <p:selectOneMenu editable="true" style="width:187px;"
                                     value="#{psnInfoBase.psnInfo.position}">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{psnInfoBase.zcList}" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}" var="itm" />
                    </p:selectOneMenu>
                </p:column>
                <p:column style="width:180px;text-align: right;" rendered="#{psnInfoBase.zwOrgType ne 4}">
                    <h:outputText value="从事专业 (岗位)："/>
                </p:column>
                <p:column rendered="#{psnInfoBase.zwOrgType ne 4}">
                    <p:inputText value="#{psnInfoBase.psnInfo.beJob}"  maxlength="50" style="width:180px;"/>
                </p:column>
            </p:row>
            <p:row rendered="#{psnInfoBase.zwOrgType eq 4}">
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="从事专业："/>
                </p:column>
                <p:column colspan="3">
                    <h:outputText value="#{psnInfoBase.psnInfo.fkByBeJobId.codeName}"/>
                    <h:outputText value="（#{psnInfoBase.psnInfo.beJobOther}）"
                                  rendered="#{not empty psnInfoBase.psnInfo.beJobOther}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;height: 32px;" rendered="false">
                    <h:outputText value="*" style="color:red;" rendered="#{ifEdit}"/>
                    <h:outputText value="在职状态："/>
                </p:column>
                <p:column rendered="false">
                    <p:selectOneRadio value="#{psnInfoBase.psnInfo.onDuty}" rendered="#{ifEdit}">
                        <f:selectItem itemValue="1" itemLabel="在职"/>
                        <f:selectItem itemValue="2" itemLabel="离职"/>
                    </p:selectOneRadio>
                    <p:outputLabel value="在职" rendered="#{psnInfoBase.psnInfo.onDuty=='1' and !ifEdit}"/>
                    <p:outputLabel value="退休" rendered="#{psnInfoBase.psnInfo.onDuty=='2' and !ifEdit}"/>
                </p:column>
                <p:column style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="从事职业卫生相关工作年限：" rendered="#{psnInfoBase.zwOrgType != 1}"/>
                    <h:outputText value="从事放射卫生相关工作年限：" rendered="#{psnInfoBase.zwOrgType == 1}"/>
                </p:column>
                <p:column colspan="#{psnInfoBase.zwOrgType eq 4 ? '1' : '3'}">
                    <p:inputText value="#{psnInfoBase.psnInfo.beZyWorkYears}"
                                 onblur="SYSTEM.clearNoNum(this)" onkeyup="SYSTEM.clearNoNum(this)"
                                 style="width:180px;" maxlength="2" rendered="#{psnInfoBase.zwOrgType != 1}"/>
                    <p:inputText value="#{psnInfoBase.psnInfo.beFsWorkYears}"
                                 onblur="SYSTEM.clearNoNum(this)" onkeyup="SYSTEM.clearNoNum(this)"
                                 style="width:180px;" maxlength="2" rendered="#{psnInfoBase.zwOrgType == 1}" />
                </p:column>
                <p:column style="width:180px;text-align: right;" rendered="#{psnInfoBase.zwOrgType eq 4}">
                    <h:outputText value="是否承担教学和科研任务："/>
                </p:column>
                <p:column rendered="#{psnInfoBase.zwOrgType eq 4}">
                    <h:outputText value="否" rendered="#{psnInfoBase.psnInfo.ifScienTask eq '0'}"/>
                    <h:outputText value="是" rendered="#{psnInfoBase.psnInfo.ifScienTask eq '1'}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <!--<h:outputText value="*" style="color:red;"></h:outputText>-->
                    <h:outputText value="毕业证书附件："/>
                </p:column>
                <p:column  colspan="3">
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.gradPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="5"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.gradAnnxeName}" rendered="#{psnInfoBase.psnInfo.gradPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.gradPath}')"
                                     rendered="#{psnInfoBase.psnInfo.gradPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.gradPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:resume"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.gradPath!=null}"
                                     update=":#{psnInfoForm}:resume">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="5"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:fieldset legend="资质信息" toggleable="true" toggleSpeed="500"
                style="margin-top: 5px;margin-bottom: 5px;" >
        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" id="zzlbPanel" rendered="#{zwOrgType == 0 || zwOrgType == 2 || zwOrgType == 4}">
            <p:row>
                <p:column  style="width:180px;text-align: right;height:26px;">
                    <h:outputText value="*" style="color:red;" rendered="#{psnTypeNotNull}"/>
                    <h:outputText value="人员属性："/>
                </p:column>
                <p:column colspan="3">
                    <table>
                        <c:forEach items="#{psnInfoBase.psnTypeRows}" var="row">
                            <tr>
                                <c:forEach items="#{row.typePOs}" var="psnType" varStatus="colIndex">
                                    <td style="border: none;" colspan="#{colIndex.index==row.typePOs.size()-1?6-colIndex.index:1}">
                                        <p:outputPanel style="display:flex;align-items: center;">
                                            <p:selectBooleanCheckbox value="#{psnType.ifSelected}" itemLabel="#{psnType.fkByPsnType.codeName}"
                                            >
                                                <p:ajax event="change" listener="#{psnInfoBase.changePsnType}" process="@this,zzlbPanel" update="zzlbPanel"/>
                                            </p:selectBooleanCheckbox>
                                            <p:outputLabel value="（" style="padding-right:3px;" rendered="#{psnType.fkByPsnType.extendS2==1}"/>
                                            <p:selectBooleanCheckbox value="#{psnType.ifJdExpert}" itemLabel="鉴定专家" disabled="#{!psnType.ifSelected}" rendered="#{psnType.fkByPsnType.extendS2==1}"/>
                                            <h:outputText value="）" rendered="#{psnType.fkByPsnType.extendS2==1}"/>
                                            <p:inputText value="#{psnType.otherType}" maxlength="25"
                                                         disabled="#{!psnType.ifSelected}" style="margin-left:6px;width:120px;"
                                                         rendered="#{psnType.fkByPsnType.extendS3==4}"/>
                                        </p:outputPanel>
                                    </td>
                                </c:forEach>
                            </tr>
                        </c:forEach>
                    </table>
                </p:column>
            </p:row>
            <p:row >
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="*" style="color:red;" rendered="#{psnInfoBase.psnType == 1}"/>
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}资格证编号："/>
                </p:column>
                <p:column  style="width:240px;">
                    <p:inputText value="#{psnInfoBase.psnInfo.doctCertNo}"/>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;" rendered="#{psnInfoBase.psnType == 1}"/>
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}资格证发证日期："/>
                </p:column>
                <p:column>
                    <p:calendar value="#{psnInfoBase.psnInfo.doctCertDate}" styleClass="myCalendar" navigator="true"
                                yearRange="c-50:c" converterMessage="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}资格发证日期格式输入不正确！"
                                pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                                maxdate="new Date()" readonlyInput="true"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="*" style="color:red;" rendered="#{psnInfoBase.psnType == 1}"/>
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}资格类别："/>
                </p:column>
                <p:column>
                    <table style="border-color: transparent;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
                        <tr>
                            <td style="padding-left:0px;">
                                <p:inputText value="#{psnInfoBase.psnInfo.fkByDoctSpecId.codeName==null?null:psnInfoBase.psnInfo.fkByDoctSpecId.codeName}"
                                             style="width: 164px;cursor: pointer;" id="doctSpec"
                                             readonly="true"
                                             onclick="document.getElementById('#{psnInfoForm}:selDoctSpecLink').click();"
                                />
                            </td>
                            <td>
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selDoctSpecLink"
                                               action="#{psnInfoBase.selectDoctTypeAction(1)}" process="@this"
                                               style="position: relative;left: -30px;">
                                    <p:ajax event="dialogReturn"
                                            listener="#{psnInfoBase.onDoctSpecAction}" process="@this"
                                            resetValues="true" update=":#{psnInfoForm}:doctSpec" />
                                </p:commandLink>
                            </td>
                            <td>
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               style="position: relative;left: -28px;"
                                               action="#{psnInfoBase.clearDoctSpec}" process="@this"
                                               update=":#{psnInfoForm}:doctSpec">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}资格级别："/>
                </p:column>
                <p:column>
                    <p:selectOneMenu style="width:172px;" value="#{psnInfoBase.psnInfo.fkByDoctLevelId.rid}">
                        <f:selectItem itemLabel="--请选择--" itemValue=""/>
                        <f:selectItems value="#{psnInfoBase.docLevList}" itemValue="#{itm.rid}" itemLabel="#{itm.codeName}" var="itm" />
                    </p:selectOneMenu>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="*" style="color:red;" rendered="#{'1'eq psnInfoBase.ifQualAnnex}"/>
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}资格证书附件："/>
                </p:column>
                <p:column colspan="1">
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.doctQualPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="2"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.qualAnnexName}" rendered="#{psnInfoBase.psnInfo.doctQualPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.doctQualPath}')"
                                     rendered="#{psnInfoBase.psnInfo.doctQualPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.doctQualPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:zzlbPanel"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.doctQualPath!=null}"
                                     update=":#{psnInfoForm}:zzlbPanel">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="2"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;" rendered="#{'1'eq psnInfoBase.ifPraAnnex}"/>
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}执业证书附件："/>
                </p:column>
                <p:column>
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.doctPraPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="3"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.praAnnxeName}" rendered="#{psnInfoBase.psnInfo.doctPraPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.doctPraPath}')"
                                     rendered="#{psnInfoBase.psnInfo.doctPraPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.doctPraPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:zzlbPanel"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.doctPraPath!=null}"
                                     update=":#{psnInfoForm}:zzlbPanel">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="3"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}执业证书编号："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.doctPraNo}" maxlength="50"/>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}执业证发证日期："/>
                </p:column>
                <p:column>
                    <p:calendar value="#{psnInfoBase.psnInfo.doctPraDate}" styleClass="myCalendar" navigator="true"
                                yearRange="c-50:c" converterMessage="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}资格发证日期格式输入不正确！"
                                pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                                readonlyInput="true" maxdate="new Date()"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="*" style="color:red;" rendered="#{psnInfoBase.psnType == 1}"/>
                    <h:outputText value="#{'1' eq psnInfoBase.ifNurseOrDoctor?'护师':'医师'}执业类别："/>
                </p:column>
                <p:column>
                    <table style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;">
                        <tr>
                            <td style="padding-left:0px;">
                                <p:inputText value="#{psnInfoBase.psnInfo.fkByDoctTypeId.codeName==null?null:psnInfoBase.psnInfo.fkByDoctTypeId.codeName}"
                                             style="width: 164px;cursor: pointer;" id="doctType"
                                             readonly="true"
                                             onclick="document.getElementById('#{psnInfoForm}:selDoctTypeLink').click();"
                                />
                            </td>
                            <td>
                                <p:commandLink styleClass="ui-icon ui-icon-search"
                                               id="selDoctTypeLink"
                                               action="#{psnInfoBase.selectDoctTypeAction(2)}" process="@this"
                                               style="position: relative;left: -30px;">
                                    <p:ajax event="dialogReturn"
                                            listener="#{psnInfoBase.onDoctTypeAction}" process="@this"
                                            resetValues="true" update=":#{psnInfoForm}:doctType" />
                                </p:commandLink>
                            </td>
                            <td>
                                <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                               style="position: relative;left: -28px;"
                                               action="#{psnInfoBase.clearDoctType}" process="@this"
                                               update=":#{psnInfoForm}:doctType">
                                </p:commandLink>
                            </td>
                        </tr>
                    </table>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;"  rendered="#{psnInfoBase.psnType == 1}"/>
                    <h:outputText value="主执业地点："></h:outputText>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.doctPraPlace}" maxlength="100" style="width:164px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="其他执业地点："/>
                </p:column>
                <p:column colspan="3">
                    <p:inputText value="#{psnInfoBase.psnInfo.doctOtherPlace}" maxlength="100" style="width:450px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="*" style="color:red;"  rendered="#{psnInfoBase.ifHoldCent}"/>
                    <h:outputText value="#{'1' eq psnInfoBase.ifRequiredAnnex?'职业病诊断':'中级以上（含）专业技术任职'}资格证书附件："/>
                </p:column>
                <p:column>
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.holdCentPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="7"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.holdCentAnnexName}" rendered="#{psnInfoBase.psnInfo.holdCentPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.holdCentPath}')"
                                     rendered="#{psnInfoBase.psnInfo.holdCentPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.holdCentPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:zzlbPanel"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.holdCentPath!=null}"
                                     update=":#{psnInfoForm}:zzlbPanel">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="7"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;"  rendered="#{psnInfoBase.ifEmployCent}"/>
                    <h:outputText value="#{'1' eq psnInfoBase.ifRequiredAnnex?'专业人员任命文件附件':'中级以上（含）专业技术任职聘任书附件'}："/>
                </p:column>
                <p:column  colspan="1">
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.employCentPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="8"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.employCentAnnexName}" rendered="#{psnInfoBase.psnInfo.employCentPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.employCentPath}')"
                                     rendered="#{psnInfoBase.psnInfo.employCentPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.employCentPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:zzlbPanel"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.employCentPath!=null}"
                                     update=":#{psnInfoForm}:zzlbPanel">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="8"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;" >
                    <h:outputText value="*" style="color:red;"  rendered="#{psnInfoBase.ifLaborAnnex}"/>
                    <h:outputText value="劳动关系证明："/>
                </p:column>
                <p:column  colspan="3">
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.laborRelAnnexPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="11"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.laborRelAnnexName}" rendered="#{psnInfoBase.psnInfo.laborRelAnnexPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.laborRelAnnexPath}')"
                                     rendered="#{psnInfoBase.psnInfo.laborRelAnnexPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.laborRelAnnexPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:zzlbPanel"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.laborRelAnnexPath!=null}"
                                     update=":#{psnInfoForm}:zzlbPanel">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="11"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
            </p:row>
            <p:row>
                <p:column  style="width:180px;text-align: right;height: 32px;">
                    <h:outputText value="放射培训合格证书附件："/>
                </p:column>
                <p:column colspan="1">
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.trainQualAnnexPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="9"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.trainQualAnnexName}" rendered="#{psnInfoBase.psnInfo.trainQualAnnexPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.trainQualAnnexPath}')"
                                     rendered="#{psnInfoBase.psnInfo.trainQualAnnexPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.trainQualAnnexPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:zzlbPanel"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.trainQualAnnexPath!=null}"
                                     update=":#{psnInfoForm}:zzlbPanel">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="9"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
                <p:column  style="width:180px;text-align: right;">
                    <h:outputText value="放射卫生检测与评价技术考核合格证书附件："/>
                </p:column>
                <p:column colspan="1">
                    <p:commandButton value="上传" process="@this,:#{psnInfoForm}:codeEditGrid"
                                     action="#{psnInfoBase.beforeFileUpload}"
                                     rendered="#{psnInfoBase.psnInfo.fsQualAnnexPath==null}">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="10"/>
                    </p:commandButton>
                    <p:outputLabel value="#{psnInfoBase.psnInfo.fsQualAnnexName}" rendered="#{psnInfoBase.psnInfo.fsQualAnnexPath!=null}"/>
                    <p:commandButton value="查看" process="@this"
                                     onclick="window.open('/webFile/#{psnInfoBase.psnInfo.fsQualAnnexPath}')"
                                     rendered="#{psnInfoBase.psnInfo.fsQualAnnexPath!=null}">
                    </p:commandButton>
                    <p:spacer width="5" rendered="#{psnInfoBase.psnInfo.fsQualAnnexPath!=null}"/>
                    <p:commandButton value="删除"
                                     process="@this,:#{psnInfoForm}:zzlbPanel"
                                     action="#{psnInfoBase.delAnnex}"
                                     rendered="#{psnInfoBase.psnInfo.fsQualAnnexPath!=null}"
                                     update=":#{psnInfoForm}:zzlbPanel">
                        <f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="10"/>
                        <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                    </p:commandButton>
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" id="zzlbPanel2" rendered="#{zwOrgType == 1|| zwOrgType == 3}">
            <p:row>
                <p:column  style="width:180px;text-align: right;height:26px;">
                    <h:outputText value="*" style="color:red;" rendered="#{psnTypeNotNull}"/>
                    <h:outputText value="人员属性："/>
                </p:column>
                <p:column colspan="3">
                    <table>
                        <c:forEach items="#{psnInfoBase.psnTypeRows}" var="row">
                            <tr>
                                <c:forEach items="#{row.typePOs}" var="psnType" varStatus="colIndex">
                                    <td style="border: none;" colspan="#{colIndex.index==row.typePOs.size()-1?6-colIndex.index:1}">
                                        <p:outputPanel style="display:flex;align-items: center;">
                                            <p:selectBooleanCheckbox value="#{psnType.ifSelected}" itemLabel="#{psnType.fkByPsnType.codeName}"
                                            >
                                                <p:ajax event="change" listener="#{psnInfoBase.changePsnType}" process="@this,zzlbPanel2" update="zzlbPanel2"/>
                                            </p:selectBooleanCheckbox>
                                            <p:outputLabel value="（" style="padding-right:3px;" rendered="#{psnType.fkByPsnType.extendS2==1}"/>
                                            <p:selectBooleanCheckbox value="#{psnType.ifJdExpert}" itemLabel="鉴定专家" disabled="#{!psnType.ifSelected}" rendered="#{psnType.fkByPsnType.extendS2==1}"/>
                                            <h:outputText value="）" rendered="#{psnType.fkByPsnType.extendS2==1}"/>
                                            <p:inputText value="#{psnType.otherType}" maxlength="25"
                                                         disabled="#{!psnType.ifSelected}" style="margin-left:6px;width:120px;"
                                                         rendered="#{psnType.fkByPsnType.extendS3==4}"/>
                                        </p:outputPanel>
                                    </td>
                                </c:forEach>
                            </tr>
                        </c:forEach>
                    </table>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:fieldset legend="收件人信息" toggleable="true" toggleSpeed="500"
                style="margin-top: 5px;margin-bottom: 5px;" rendered="#{psnInfoBase.ifShowReceiveInfo}">
        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" id="receiveInfoId">
            <p:row>
                <p:column style="width:180px;text-align: right;">
                    <p:inputText style="visibility: hidden;width: 0px;"/>
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="姓名："/>
                </p:column>
                <p:column style="width:240px;">
                    <p:inputText value="#{psnInfoBase.psnInfo.receiveEmpName}" maxlength="50"/>
                </p:column>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="联系电话："/>
                </p:column>
                <p:column>
                    <p:inputText value="#{psnInfoBase.psnInfo.receiveMobileNo}" style="width:180px;"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="邮编："/>
                </p:column>
                <p:column colspan="3">
                    <p:inputText value="#{psnInfoBase.psnInfo.receivePostcode}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column style="width:180px;text-align: right;">
                    <h:outputText value="*" style="color:red;"/>
                    <h:outputText value="收件地址："/>
                </p:column>
                <p:column colspan="3">
                    <p:inputText value="#{psnInfoBase.psnInfo.receiveLinkAddr}"
                                 maxlength="100" style="width:450px;"/>
                </p:column>
            </p:row>
        </p:panelGrid>
    </p:fieldset>
    <p:outputLabel value="（专业技术人员资质选填）" style="margin-left:5px;color:blue;"/>
    <p:panelGrid style="width:100%;margin-top:5px;margin-bottom:30px;" id="instDatatable" styleClass="globleTableReverse">
        <p:row>
            <p:column style="width: 200px;text-align: center;" styleClass="ui-state-default">
                <p:outputLabel value="资质类别"/>
            </p:column>
            <p:column style="width:220px;text-align: center;" styleClass="ui-state-default">
                <p:outputLabel value="#{psnInfoBase.zwOrgType==0?'证书（发文）编号':'证书编号'}"/>
            </p:column>
            <p:column style="width:120px;text-align:center;" styleClass="ui-state-default">
                <p:outputLabel value="#{psnInfoBase.zwOrgType==0?'发证（发文）日期':'发证日期'}"/>
            </p:column>
            <p:column style="width:100px;text-align:center;" styleClass="ui-state-default">
                <p:outputLabel value="失效日期"/>
            </p:column>
            <p:column style="text-align: center;" styleClass="ui-state-default">
                <p:outputLabel value="操作"/>
            </p:column>
        </p:row>
        <c:forEach items="#{psnInfoBase.psnInfo.zzlbList}" var="instItm">
            <p:row rendered="#{instItm.show}">
                <p:column style="text-align: center;height:25px;">
                    <p:outputLabel value="#{instItm.fkByQualId.codeName}"/>
                </p:column>
                <p:column style="text-align: center;">
                    <p:outputLabel value="#{instItm.centNo}"/>
                </p:column>
                <p:column style="text-align: center;">
                    <p:outputLabel value="#{instItm.sendDate}" >
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
                <p:column style="text-align: center;">
                    <p:outputLabel value="#{instItm.validDate}">
                        <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                    </p:outputLabel>
                </p:column>
                <p:column>
                    <p:spacer width="5"/>
                    <p:commandLink value="查看附件" process="@this"
                                   onclick="window.open('/webFile/#{instItm.annexPath}')"
                                   rendered="#{instItm.annexPath!=null}">
                    </p:commandLink>
                    <p:spacer width="5" rendered="#{instItm.annexPath!=null}"/>
                    <p:commandLink value="查看" oncomplete="PF('ZzlbInfoDialog').show()" update=":#{psnInfoForm}:zzlbGrid" process="@this"
                                   action="#{mgrViewbean.zzlbEditAction}">
                        <f:setPropertyActionListener target="#{psnInfoBase.psnZzlb}" value="#{instItm}" />
                    </p:commandLink>
                    <p:spacer width="5" rendered="#{instItm.ifShowTran}" />
                    <p:commandLink value="培训履历" rendered="#{instItm.ifShowTran}"  oncomplete="PF('TrainHisDialog').show()"
                                   update=":#{psnInfoForm}:trainHisDialog" process="@this">
                        <f:setPropertyActionListener target="#{psnInfoBase.psnZzlb}" value="#{instItm}" />
                    </p:commandLink>
                </p:column>
            </p:row>
        </c:forEach>
        <p:row rendered="#{!psnInfoBase.ifHaveZz}">
            <p:column colspan="6">
                <p:outputLabel value="暂无记录！" style="padding-left:6px;"/>
            </p:column>
        </p:row>
    </p:panelGrid>
</ui:composition>