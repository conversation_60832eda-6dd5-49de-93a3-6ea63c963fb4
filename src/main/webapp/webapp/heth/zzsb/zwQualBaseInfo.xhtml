<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwOrgInfoBaseZzsbBean"-->
    <style type="text/css">
    	.calendarClass input{
    		width:180px;
    	}
    	.calendarClass1 input{
    		width:77px;
    	}
        #mainForm\:tabView\:fsServiceObjTable table.ui-selectmanycheckbox td label{
            white-space: pre-line;
            width: max-content;
        }
        .myCalendar1 input{
            width:180px;
        }
    </style>
    <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;" id="caseGrid"
           rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
       <p:row >
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="单位名称：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.orgName}"  maxlength="50" style="width:604px;#{mgrbean.ifReviewProcess and mgrbean.ifZzsbSave and mgrbean.zwOrgType == 0?'pointer-events:none;':''}" required="true" requiredMessage="单位名称不能为空！"  onkeyup="this.value=this.value.replace(/\s+/g,'')"/>
               <!-- 地区多选弹出框 -->
               <p:overlayPanel id="zoneApproveOverlayPanel" widgetVar="ZoneApproveOverlayPanel" for="zoneApproveNames" style="width:280px;"
                               showCloseIcon="true" onHide="HideZoneApproveOverlayPanel();">
                   <p:tree id="zoneApproveTree" value="#{mgrbean.zoneApproveTree}" var="zoneApproveNode" selectionMode="checkbox"
                           style="width: 250px;height: 320px;overflow-y: auto;"
                           selection="#{mgrbean.selectZoneApproves}">
                       <p:treeNode>
                           <p:outputLabel value="#{zoneApproveNode.zoneName}" />
                       </p:treeNode>
                   </p:tree>
               </p:overlayPanel>
           </p:column>
       </p:row>
       <p:row >
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="注册地址：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.orgAddr}" maxlength="100" style="width:604px;#{mgrbean.ifReviewProcess and mgrbean.ifZzsbSave and mgrbean.zwOrgType == 0?'pointer-events:none;':''}"
                         required="true"  requiredMessage="注册地址不能为空！"  onkeyup="this.value=this.value.replace(/\s+/g,'')" />
           </p:column>
       </p:row>
       <p:row rendered="#{mgrbean.zwOrgType == 3}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="通讯地址：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.communiAddr}" maxlength="100" style="width:604px;"
                         onkeyup="this.value=this.value.replace(/\s+/g,'')" />
           </p:column>
       </p:row>
       <p:row rendered="#{mgrbean.zwOrgType == 3}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="实验室地址：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.labAddr}" maxlength="100" style="width:604px;"
                         onkeyup="this.value=this.value.replace(/\s+/g,'')" />
           </p:column>
       </p:row>
       <p:row>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="法定代表人：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.orgFz}" maxlength="25" style="#{mgrbean.ifReviewProcess and mgrbean.ifZzsbSave and mgrbean.zwOrgType == 0?'pointer-events:none;':''}; width: 205px;"
                          required="true" requiredMessage="法定代表人不能为空！" onkeyup="this.value=this.value.replace(/\s+/g,'')" />
           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="#{mgrbean.zwOrgType == 3?'单位类型':'法定代表人职务'}：" />
           </p:column>
           <p:column style="text-align:left;padding-left:#{mgrbean.zwOrgType != 3?3:0}px;height: 25px;">
           		<p:selectOneMenu value="#{mgrbean.zwOrginfoEntity.orgFzzw}" editable="true" maxlength="25"
                                style="width: 188px;#{mgrbean.ifReviewProcess and mgrbean.ifZzsbSave and mgrbean.zwOrgType == 0?'pointer-events:none;':''}"  required="true" requiredMessage="法定代表人职务不能为空！"
                                rendered="#{mgrbean.zwOrgType != 3}">
                   <f:selectItems value="#{mgrbean.careerList}"/>
                </p:selectOneMenu>
                <!-- 单位类型 -->
                <h:panelGrid columns="4"
					style="border-color: #ffffff;padding: 0 0 0 0;border-spacing: 0;cellpadding:0;"
					rendered="#{mgrbean.zwOrgType == 3}">
					<p:inputText id="unitTypeName"
						value="#{mgrbean.zwOrginfoEntity.fkByUnitTypeId.codeName}"
						style="width: 180px;cursor: pointer;"
						onclick="document.getElementById('mainForm:tabView:selUnitTypeLink').click();"
						readonly="true"/>
					<p:commandLink styleClass="ui-icon ui-icon-search"
						id="selUnitTypeLink"
						action="#{mgrbean.selectUnitTypeAction}" process="@this"
						style="position: relative;left: -30px;">
						<p:ajax event="dialogReturn"
							listener="#{mgrbean.onUnitTypeSearch}" process="@this"
							resetValues="true" update="unitTypeName" />
					</p:commandLink>
				</h:panelGrid>
           </p:column>
       </p:row>
       <p:row >
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
           	   <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="联系人：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.linkMan}"  maxlength="25"  style="width: 205px;" required="true" requiredMessage="联系人不能为空！" onkeyup="this.value=this.value.replace(/\s+/g,'')"/>
           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="联系人手机：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.linkMb}" maxlength="11" style="width:180px;"
                            onkeyup="this.value = this.value.replace(/[^\d]/g, '');" required="true" requiredMessage="联系人手机不能为空！"/>
                    <p:outputLabel value=" (格式：13616161616)" style="color: red"/>         
           </p:column>
       </p:row>
       <p:row >
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="电话：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;width: 450px;">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.linkTel}" maxlength="25" style="width: 205px;"
                            onkeyup="this.value = this.value.replace(/[^\d|-]/g, '');" required="true" requiredMessage="电话不能为空！"/>
                        <p:outputLabel value=" (格式：0510-85373786)" style="color: red"/>      
           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="传真：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.fax}" maxlength="25" style="width:180px;"
                            onkeyup="this.value = this.value.replace(/[^\d|-]/g, '');"/>
           </p:column>
       </p:row>
       <p:row >
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="邮编：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.zipcode}" maxlength="10" style="width: 205px;"
                            onkeyup="this.value = this.value.replace(/[^\d]/g, '');"/>
           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="电子邮箱：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.email}" maxlength="25" style="width:180px;" onkeyup="this.value=this.value.replace(/\s+/g,'')"/>
           </p:column>
       </p:row>
       <p:row rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and mgrbean.zwOrgType != 4}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="资质证书编号：" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}"/>
               <p:outputLabel value="备案编号：" rendered="#{mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2}"/>
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
               <p:inputText value="#{mgrbean.zwOrginfoEntity.certNo}" maxlength="25"  style="width:205px;#{mgrbean.ifReviewProcess and mgrbean.ifZzsbSave and mgrbean.zwOrgType == 0?'pointer-events:none;':''}"
                            rendered="#{mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2}"
                           required="true" requiredMessage="备案编号不能为空！" onkeyup="this.value=this.value.replace(/\s+/g,'')"/>
               <p:outputLabel value=" (#{mgrbean.certNoTip})" style="color: red" rendered="#{(mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2) and mgrbean.certNoTip!=null and mgrbean.certNoTip!=''}"/>
               <p:inputText value="#{mgrbean.zwOrginfoEntity.certNo}" maxlength="25" style="width: 205px;"
                            rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}"
                            required="true" requiredMessage="资质证书编号不能为空！" onkeyup="this.value=this.value.replace(/\s+/g,'')"/>

           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="发证时间：" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}"/>
               <p:outputLabel value="备案日期：" rendered="#{(mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2)
               and (mgrbean.ifValidityPeriod != '1' or (mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType != 0)) }" />
               <p:outputLabel value="备案有效期限：" rendered="#{mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType == 0}" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.ifValidityPeriod != '1' or (mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType != 0)}">
               <p:calendar value="#{mgrbean.zwOrginfoEntity.firstGetday}" styleClass="calendarClass" navigator="true"
                           yearRange="c-20:c" converterMessage="日期格式输入不正确！"
                           style="#{mgrbean.ifReviewProcess and mgrbean.ifZzsbSave and mgrbean.zwOrgType == 0?'pointer-events:none;':''}"
                           pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                           required="true" requiredMessage="#{mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2?'备案日期':'发证时间'}不能为空！"
                           maxdate="new Date()" readonlyInput="true" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType == 0}">
               <p:calendar value="#{mgrbean.zwOrginfoEntity.firstGetday}" styleClass="calendarClass1" navigator="true"
                           yearRange="c-20:c" converterMessage="日期格式输入不正确！" id="bDate" style="#{'1' eq mgrbean.ifValidityPeriod and mgrbean.ifReviewProcess?'pointer-events:none;':''}"
                           pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                           maxdate="#{mgrbean.firstGetdayMaxDate}" readonlyInput="true" >
                   <p:ajax event="dateSelect" listener="#{mgrbean.filingDateListener}"  process="@this,bDate,eDate"  update="bDate,eDate" />
                </p:calendar>
               ~
               <p:calendar value="#{mgrbean.zwOrginfoEntity.filingDate}" styleClass="calendarClass1" navigator="true"
                           yearRange="c-20:c+20" converterMessage="日期格式输入不正确！" id="eDate"
                           pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true" style="#{'1' eq mgrbean.ifValidityPeriod and mgrbean.ifReviewProcess?'pointer-events:none;':''}"
                           mindate="#{mgrbean.zwOrginfoEntity.firstGetday}" readonlyInput="true">
                   <p:ajax event="dateSelect" listener="#{mgrbean.filingDateListener}" process="@this,bDate,eDate"  update="bDate,eDate" />
               </p:calendar>
           </p:column>
       </p:row>
        <p:row rendered="#{mgrbean.ifShowApproveUnit==1 and (mgrbean.zwOrgType == 1 or  mgrbean.zwOrgType == 2 or mgrbean.zwOrgType == 3)}">
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="备案单位名称：" rendered="#{mgrbean.zwOrgType == 2}"/>
                <p:outputLabel value="资质批准单位：" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}"/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
                <p:inputText value="#{mgrbean.zwOrginfoEntity.rcdUnitName}" maxlength="100" style="width:604px;" />
            </p:column>
        </p:row>
        <p:row rendered="#{(mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2) and mgrbean.ifShowRcdOrg}">
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="备案管理机构："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
                <p:selectOneMenu value="#{mgrbean.zwOrginfoEntity.rcdOrgId}"
                                 style="width: 213px;" required="true" requiredMessage="备案管理机构不能为空！">
                    <f:selectItem itemValue="" itemLabel=""/>
                    <f:selectItems value="#{mgrbean.rcdOrgList}" var="rcdOrg" itemValue="#{rcdOrg.rid}" itemLabel="#{rcdOrg.codeName}" />
                </p:selectOneMenu>
            </p:column>
        </p:row>
       <p:row rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and mgrbean.zwOrgType != 4}">
       	<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="状态：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
               <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.cancelState}">
               	<f:selectItem itemLabel="正常" itemValue="0"/>
               	<f:selectItem itemLabel="注销" itemValue="1"/>
               	<p:ajax event="change" process="@this,cancelDate,cancelDate1" update="cancelDate,cancelDate1"/>
               </p:selectOneRadio>
           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="失效日期：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}">
               <p:calendar value="#{mgrbean.zwOrginfoEntity.validDate}" navigator="true"
                           yearRange="c-20:c+50" converterMessage="失效日期格式输入不正确！"
                           pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                           styleClass="calendarClass" required="true" requiredMessage="失效日期不能为空！"
               />
           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2}">
               <p:outputLabel value="注销日期：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2}">
               <p:calendar value="#{mgrbean.zwOrginfoEntity.cancelDate}" size="25" navigator="true"
                           yearRange="c-20:c" converterMessage="注销日期格式输入不正确！"
                           pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                           id="cancelDate" styleClass="myCalendar1"
                           disabled="#{mgrbean.zwOrginfoEntity.cancelState != 1}"/>
           </p:column>
       </p:row>
        <p:row rendered="#{mgrbean.zwOrgType == 4}">
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
                <p:outputLabel value="状态：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.cancelState}">
                    <f:selectItem itemLabel="正常" itemValue="0"/>
                    <f:selectItem itemLabel="注销" itemValue="1"/>
                    <p:ajax event="change" process="@this,cancelDate2" update="cancelDate2"/>
                </p:selectOneRadio>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;"
            >
                <p:outputLabel value="注销日期：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="#{mgrbean.zwOrgType==3?0:3}">
                <p:calendar value="#{mgrbean.zwOrginfoEntity.cancelDate}" size="25" navigator="true"
                            yearRange="c-20:c" converterMessage="注销日期格式输入不正确！"
                            pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                            id="cancelDate2"
                            disabled="#{mgrbean.zwOrginfoEntity.cancelState != 1}"/>
            </p:column>
        </p:row>
       <p:row rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and mgrbean.zwOrgType != 4}">
       		<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;"
       				rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}">
               <p:outputLabel value="注销日期：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="#{mgrbean.zwOrgType==3?0:3}" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}">
	           	<p:calendar value="#{mgrbean.zwOrginfoEntity.cancelDate}" size="25" navigator="true"
	                           yearRange="c-20:c" converterMessage="注销日期格式输入不正确！"
	                           pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
	                           id="cancelDate1" styleClass="myCalendar1"
	                           disabled="#{mgrbean.zwOrginfoEntity.cancelState != 1}"/>
           </p:column>

           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.zwOrgType==3}">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="资质等级：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.zwOrgType==3}">
               <p:selectOneMenu value="#{mgrbean.zwOrginfoEntity.fkByLevelId.rid}" style="width: 188px;" required="true" requiredMessage="资质等级不能为空！">
                   <f:selectItem itemValue="" itemLabel="--请选择--"/>
                   <f:selectItems value="#{mgrbean.levelList}" var="itm" itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
               </p:selectOneMenu>
           </p:column>
       </p:row>
        <!---->
       <p:row rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and mgrbean.zwOrgType != 4}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="*" style="color: red"/>
               <p:outputLabel value="服务项目：" rendered="#{mgrbean.zwOrgType!=3}"/>
               <p:outputLabel value="业务范围：" rendered="#{mgrbean.zwOrgType==3}"/>
           </p:column>
           <p:column style="text-align:left;padding-left:0px;height: 25px;" colspan="3">
               <p:selectManyCheckbox columns="1" layout="grid" value="#{mgrbean.serviceObj}"
               		rendered="#{mgrbean.zwOrgType==0}" style="#{mgrbean.ifReviewProcess and mgrbean.ifZzsbSave and mgrbean.zwOrgType == 0?'pointer-events:none;':''}">
                   <f:selectItems value="#{mgrbean.serviceObjList}"/>
                   <p:ajax event="change" listener="#{mgrbean.serviceChange}" process="@this" update=":mainForm:tabView:serDetailDatatable"></p:ajax>
               </p:selectManyCheckbox>
               <p:selectManyCheckbox columns="1" layout="grid" value="#{mgrbean.serviceObj}"
                                     rendered="#{mgrbean.zwOrgType==2}">
                   <f:selectItems value="#{mgrbean.serviceObjList}"/>
               </p:selectManyCheckbox>
               <p:panelGrid style="width:100%;" id="scopeDataTable" rendered="#{mgrbean.zwOrgType==3}">
                   <p:row>
                       <p:column style="text-align:center;padding:6px;" styleClass="ui-state-default">
                           <h:outputText value="业务范围大类"></h:outputText>
                       </p:column>
                       <p:column style="text-align:center;padding:6px;" styleClass="ui-state-default">
                            <h:outputText value="业务范围小类"></h:outputText>
                       </p:column>
                   </p:row>
                   <c:forEach items="#{mgrbean.itemsShows}" var="itm"  >
                       <p:row>
                            <p:column style="text-align:center;width: 445px;">
                                <h:outputText value="#{itm.item.codeName}"/>
                            </p:column>
                            <p:column style="text-align:center;padding-left:10px;">
                                <p:selectManyCheckbox value="#{itm.selectItems}" layout="grid" columns="4"
                                    style="text-align: left;">
                                    <f:selectItems value="#{itm.childList}" var="child"
                                        itemValue="#{child.codeNo}" itemLabel="#{child.codeName}"></f:selectItems>
                                </p:selectManyCheckbox>
                            </p:column>
                       </p:row>
                   </c:forEach>
                   <p:row rendered="#{mgrbean.zwOrgType==3 and mgrbean.itemsShows.size()==0}">
                       <p:column colspan="2">
                           <p:outputLabel value="没有数据！" style="height: 20px;"> </p:outputLabel>
                       </p:column>
                   </p:row>
               </p:panelGrid>

               <p:dataTable id="fsServiceObjTable" paginatorPosition="bottom"  rendered="#{mgrbean.zwOrgType==1}"
                            value="#{mgrbean.fsServiceObjList}" style="padding-bottom: 20px;"
                            widgetVar="fsServiceObjTable" var="fsServiceObj"
                            emptyMessage="没有数据！" rowIndexVar="R">
                   <p:column headerText="服务项目大类" style="padding-left:3px;width: 260px;">
                       <table>
                           <tr>
                               <td style="width:24px;padding:0;border:0px;">
                                   <p:selectBooleanCheckbox value="#{fsServiceObj.ifSelected}" disabled="#{fsServiceObj.disabled}">
                                       <p:ajax event="change" listener="#{mgrbean.fsServiceChangeActioin}" process="@this,@parent,fsServiceObjTable" update="fsServiceObjTable"/>
                                   </p:selectBooleanCheckbox>
                               </td>
                               <td style="padding:0;border:0px;">
                                   #{fsServiceObj.codeName}

                               </td>
                           </tr>
                       </table>
                   </p:column>
                   <p:column headerText="服务项目小类" style="padding-left:3px;">
                       <p:selectManyCheckbox columns="5" layout="grid" value="#{fsServiceObj.itemSubs}"   style="width: 250px;">
                           <f:selectItems value="#{fsServiceObj.zwItemSubPOList}" var="sub" itemValue="#{sub.codeNo}" itemLabel="#{sub.codeName}" itemDisabled="#{sub.disabled}"/>
                       </p:selectManyCheckbox>
                   </p:column>
               </p:dataTable>
           </p:column>

       </p:row>
       <!-- 按地区备案 -->
       <p:row rendered="#{mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1}">
   	   	 <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
             <p:outputLabel value="*" style="color: red"/>
             <p:outputLabel value="备案信息："/>
         </p:column>
         <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
         	<p:commandButton value="添加" action="#{mgrbean.addRecordInit}" icon="ui-icon-plus"
         		process="@this" update="recordDialog" oncomplete="PF('RecordDialog').show()"
         		style="margin:3px 0px 5px 0px;"></p:commandButton>
         	<p:dataTable id="recordTable" value="#{mgrbean.tjorgRecords}" 
         		emptyMessage="没有数据！" var="itm">
         		<p:column headerText="备案地区" style="text-align:center;width:100px;">
         			<h:outputText value="#{mgrbean.recordZoneMap.get(itm.zoneId).zoneName}"/>
         		</p:column>
                <p:column headerText="备案单位名称" style="width:160px;">
                    <h:outputText value="#{itm.unitName}"/>
                </p:column>
         		<p:column headerText="备案编号" style="text-align:center;width:150px;">
         			<h:outputText value="#{itm.rcdNo}"/>
         		</p:column>
         		<p:column headerText="备案日期" style="text-align:center;width:80px;">
         			<h:outputText value="#{itm.certDate}">
         				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
         			</h:outputText>
         		</p:column>
         		<p:column headerText="服务项目" style="width:380px;">
         			<h:outputText value="#{itm.serviceItems}"></h:outputText>
         		</p:column>
         		<p:column headerText="状态" style="text-align:center;width:80px;">
         			<h:outputText value="正常" rendered="#{itm.stateMark==0}"></h:outputText>
         			<h:outputText value="注销" rendered="#{itm.stateMark==1}"></h:outputText>
         		</p:column>
         		<p:column headerText="注销日期" style="text-align:center;width:80px;">
         			<h:outputText value="#{itm.logoutDate}">
         				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
         			</h:outputText>
         		</p:column>
         		<p:column headerText="操作" style="width:280px;">
         			<p:commandLink value="修改" process="@this" update=":mainForm:tabView:recordDialog" action="#{mgrbean.updateRecordInit}" oncomplete="PF('RecordDialog').show()">
         				<f:setPropertyActionListener target="#{mgrbean.sourceRecord}" value="#{itm}"></f:setPropertyActionListener>
         			</p:commandLink>
         			<p:spacer width="5"></p:spacer>
         			<p:commandLink value="删除" action="#{mgrbean.delRecord}" process="@this,recordTable" update="recordTable">
         				<f:setPropertyActionListener target="#{mgrbean.record}" value="#{itm}"></f:setPropertyActionListener>
         				<p:confirm icon="ui-icon-alert" message="确定删除吗？" header="消息提示框"/>
         			</p:commandLink>
         		</p:column>
         	</p:dataTable>
         </p:column>
     </p:row>
        <p:row rendered="#{mgrbean.zwOrgType==0 and (mgrbean.ifZoneApprove or mgrbean.ifHethFj)}">
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.ifZoneApprove}">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="批准的执业区域："/>
            </p:column>
            <p:column style="text-align:left;padding-left:-3px;height: 25px;#{mgrbean.ifHethFj?'width: 260px;':''}" colspan="#{mgrbean.ifHethFj?1:3}" rendered="#{mgrbean.ifZoneApprove}">
                <h:panelGrid columns="3"
                             style="border-color: #ffffff;padding: 0;margin-left: -6px;" id="zoneApproveGrid">
                    <p:inputText id="zoneApproveNames" value="#{mgrbean.selectZoneApproveName}" style="width: 206px;"
                                 readonly="true"/>
                    <p:commandLink styleClass="ui-icon ui-icon-search" process="@this"
                                   style="position: relative;left: -30px;"
                                   oncomplete="PF('ZoneApproveOverlayPanel').show()" type="button"/>
                    <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空"
                                   style="position: relative;left: -33px;"
                                   action="#{mgrbean.clearSelectZoneApproves}" process="@this"
                                   update="zoneApproveNames,zoneApproveTree">
                    </p:commandLink>
                </h:panelGrid>
                <p:remoteCommand name="HideZoneApproveOverlayPanel"
                                 process="@this,zoneApproveNames,zoneApproveOverlayPanel"
                                 action="#{mgrbean.hideZoneApproveAction}" update="zoneApproveGrid"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.ifHethFj}">
                <p:outputLabel value="外出开展职业健康检查"/><br/>
                <p:outputLabel value="工作能力："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="#{mgrbean.ifZoneApprove?1:3}" rendered="#{mgrbean.ifHethFj}">
                <p:selectOneRadio id="outWorkPower" value="#{mgrbean.zwOrginfoEntity.outWorkPower}"
                                  style="#{mgrbean.ifReviewProcess and mgrbean.ifZzsbSave and mgrbean.zwOrgType == 0?'pointer-events:none;':''}">
                    <f:selectItem itemLabel="不具备" itemValue="0"/>
                    <f:selectItem itemLabel="具备" itemValue="1"/>
                    <!-- 需要控制外检车辆 显示 所以这里需要处理 -->
                    <p:ajax event="change" listener="#{mgrbean.onChangeOutWorkPower}" process="@this,:mainForm:tabView"
                            update=":mainForm:tabView"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <!--支撑机构-->
        <p:row rendered="#{mgrbean.zwOrgType==4 and mgrbean.zwOrginfoEntity.state == 0 and '2' eq mgrbean.unitType}">
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="机构类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3" >
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.fkByTypeId.rid}" >
                    <f:selectItems value="#{mgrbean.unitTypeList}" var="item"
                                   itemValue="#{item.rid}"
                                   itemLabel="#{item.codeName}"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.zwOrgType==4 and mgrbean.zwOrginfoEntity.state == 0 and null ne mgrbean.unitType}">
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="技术支撑能力："/>
            </p:column>
            <p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3" >
                <c:forEach items="#{mgrbean.techTypesList}" var="techType">
                    <p:outputPanel style="display: flex;vertical-align: middle;">
                        <c:if test="#{techType.techTypes.size()>0}">
                            <p:outputLabel value="#{techType.codeName}"/>
                            <p:outputLabel value="（" style="padding-right: 6px"/>
                        </c:if>
                        <c:forEach items="#{techType.techTypes}" var="item">
                            <p:selectBooleanCheckbox value="#{item.ifSelected}"
                                                     style="#{tdTjBhkCltListCommBean.view eq null?'':'pointer-events:none;'}">
                            </p:selectBooleanCheckbox>
                            <p:outputLabel value="#{item.codeName}" style="margin: 0 6px;"/>
                        </c:forEach>
                        <c:if test="#{techType.techTypes.size()>0}">
                            <p:outputLabel value="）"/>
                        </c:if>
                    </p:outputPanel>
                </c:forEach>
            </p:column>
        </p:row>
        <p:row rendered="#{mgrbean.zwOrgType==4 and mgrbean.zwOrginfoEntity.state == 0 and null ne mgrbean.unitType}">
            <p:column style="text-align:right;padding-right:3px;width:150px;height: 37px;" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="具备职业健康技术服务相关资质或能力的情况："/>
            </p:column>
            <p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3"  rendered="#{'1' eq mgrbean.unitType}">
                <p:outputPanel columns="#{mgrbean.qualTypeList1.size()}" id="qualType1">
                    <c:forEach items="#{mgrbean.qualTypeList1}" var="symCode">
                        <p:selectBooleanCheckbox value="#{symCode.ifSelected}"
                                                 style="#{tdTjBhkCltListCommBean.view eq null?'':'pointer-events:none;'}">
                            <p:ajax event="change" listener="#{mgrbean.changeQualTypeAction(symCode)}" process="@this,:mainForm:tabView:qualType1" update=":mainForm:tabView:qualType1"/>
                        </p:selectBooleanCheckbox>
                        <p:outputLabel value="#{symCode.codeName}" style="margin: 0 10px;"/>
                    </c:forEach>
                </p:outputPanel>
            </p:column>
            <p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3" rendered="#{'2' eq mgrbean.unitType}">
                <p:outputPanel columns="#{mgrbean.qualTypeList2.size()}" id="qualType2">
                    <c:forEach items="#{mgrbean.qualTypeList2}" var="symCode">
                        <p:selectBooleanCheckbox value="#{symCode.ifSelected}"
                                                 style="#{tdTjBhkCltListCommBean.view eq null?'':'pointer-events:none;'}">
                            <p:ajax event="change" listener="#{mgrbean.changeQualTypeAction(symCode)}" process="@this,:mainForm:tabView:qualType2" update=":mainForm:tabView:qualType2"/>
                        </p:selectBooleanCheckbox>
                        <p:outputLabel value="#{symCode.codeName}" style="margin: 0 10px;"/>
                    </c:forEach>
                </p:outputPanel>
            </p:column>
        </p:row>
   </p:panelGrid>
   <c:if test="#{mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1}">
   		<ui:include src="tjOrgZoneRecordDialog.xhtml"></ui:include>
   </c:if>
    <p:fieldset legend="职业病因素监测开展情况" style="margin-top: 5px;margin-bottom: 5px;"
                toggleable="true" toggleSpeed="500" rendered="#{mgrbean.zwOrgType == 4 and mgrbean.zwOrginfoEntity.state == 0 and null ne mgrbean.unitType}">
        <p:commandButton value="添加" icon="ui-icon-plus" style="margin-bottom: 10px;"
                         action="#{mgrbean.addSupportChk}" update=":mainForm:tabView:supportChkTable"
                         process="@this,:mainForm:tabView:supportChkTable">
        </p:commandButton>
        <p:dataTable id="supportChkTable" paginatorPosition="bottom"
                     value="#{mgrbean.zwOrginfoEntity.supportChkList}"
                     widgetVar="SupportChkTable" var="item"
                     emptyMessage="没有您要找的记录！" rowIndexVar="R">
            <!--@elvariable id="R" type="java.lang.Integer"-->
            <p:column headerText="序号" style="width:40px;text-align: center;">
                <p:outputLabel value="#{R+1}"/>
            </p:column>
            <p:column headerText="#{mgrbean.redStr}监测周期" style="width:300px;text-align: center;">
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="reportBeginDate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="监测周期开始日期，格式输入不正确！"
                            showButtonPanel="true"  styleClass="myCalendar2" maxdate="new Date()"
                            value="#{item.startDate}">
                    <p:ajax event="dateSelect"  process="@this,:mainForm:tabView:supportChkTable"  update=":mainForm:tabView:supportChkTable" />
                </p:calendar>
                ~
                <p:calendar pattern="yyyy-MM-dd" maxlength="10" readonlyInput="true"
                            showOtherMonths="true" id="reportEndDate" size="11" navigator="true"
                            yearRange="c-10:c+10" converterMessage="监测周期结束日期，格式输入不正确！"
                            showButtonPanel="true" maxdate="new Date()" styleClass="myCalendar2"
                            value="#{item.endDate}">
                    <p:ajax event="dateSelect"  process="@this,:mainForm:tabView:supportChkTable"  update=":mainForm:tabView:supportChkTable" />
                </p:calendar>
            </p:column>
            <p:column headerText="#{mgrbean.redStr}累计开展职业病危害因素监测企业数（家）" style="width:300px;text-align: center;"
                      styleClass="th_required_header">
                <p:inputText value="#{item.crptNum}" style="width: 180px;"
                             onkeyup="SYSTEM.clearNoNumBig0(this)" onblur="SYSTEM.clearNoNumBig0(this)" onkeypress="SYSTEM.clearNoNumBig0(this)"
                             maxlength="5"/>
            </p:column>
            <p:column headerText="操作">
                <p:commandLink value="删除"
                               onclick="PF('ChkDeleteDialog#{R+1}').show();"
                               process="@this,:mainForm:tabView:supportChkTable"/>
                <p:confirmDialog message="确定要删除吗？" header="消息确认框"
                                 widgetVar="ChkDeleteDialog#{R+1}">
                    <p:commandButton value="确定" icon="ui-icon-check" process=":mainForm:tabView:supportChkTable"
                                     action="#{mgrbean.delSupportChk(item)}"
                                     update=":mainForm:tabView:supportChkTable"
                                     oncomplete="PF('ChkDeleteDialog#{R+1}').hide();"/>
                    <p:commandButton type="button" value="取消" icon="ui-icon-close"
                                     onclick="PF('ChkDeleteDialog#{R+1}').hide();"/>
                </p:confirmDialog>
            </p:column>
        </p:dataTable>
    </p:fieldset>
    <p:fieldset legend="财政投入情况" style="margin-top: 5px;margin-bottom: 5px;"
                toggleable="true" toggleSpeed="500" rendered="#{mgrbean.zwOrgType == 4 and mgrbean.zwOrginfoEntity.state == 0 and '2' eq mgrbean.unitType}">
        <p:commandButton value="添加" icon="ui-icon-plus" style="margin-bottom: 10px;"
                         action="#{mgrbean.addSupportFund}" update=":mainForm:tabView:supportFundTable"
                         process="@this,:mainForm:tabView:supportFundTable">
        </p:commandButton>
        <p:dataTable id="supportFundTable" paginatorPosition="bottom"
                     value="#{mgrbean.zwOrginfoEntity.supportFundList}"
                     widgetVar="SupportFundTable" var="item"
                     emptyMessage="没有您要找的记录！" rowIndexVar="R">
            <!--@elvariable id="R" type="java.lang.Integer"-->
            <p:column headerText="序号" style="width:40px;text-align: center;">
                <p:outputLabel value="#{R+1}"/>
            </p:column>
            <p:column headerText="#{mgrbean.redStr}年份" style="width:300px;text-align: center;">
                <p:selectOneMenu value="#{item.year}" style="width: 100px;">
                    <f:selectItems value="#{mgrbean.yearList}" />
                </p:selectOneMenu>
            </p:column>
            <p:column headerText="#{mgrbean.redStr}投入情况（万元）" style="width:300px;text-align: center;"
                      styleClass="th_required_header">
                <p:inputText value="#{item.fund}" style="width: 200px;"  onkeypress="SYSTEM.verifyNum4(this, 4, 2)"
                             onkeyup="SYSTEM.verifyNum4(this, 4, 2)" onblur="SYSTEM.verifyNum4(this, 4, 2)"/>
            </p:column>
            <p:column headerText="操作">
                <p:commandLink value="删除"
                               onclick="PF('FundDeleteDialog#{R+1}').show();"
                               process="@this,:mainForm:tabView:supportFundTable"/>
                <p:confirmDialog message="确定要删除吗？" header="消息确认框"
                                 widgetVar="FundDeleteDialog#{R+1}">
                    <p:commandButton value="确定" icon="ui-icon-check" process=":mainForm:tabView:supportFundTable"
                                     action="#{mgrbean.delSupportFund(item)}"
                                     update=":mainForm:tabView:supportFundTable"
                                     oncomplete="PF('FundDeleteDialog#{R+1}').hide();"/>
                    <p:commandButton type="button" value="取消" icon="ui-icon-close"
                                     onclick="PF('FundDeleteDialog#{R+1}').hide();"/>
                </p:confirmDialog>
            </p:column>
        </p:dataTable>
    </p:fieldset>
   <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;"
               rendered="#{mgrbean.zwOrginfoEntity.state != 0}">
      <p:row>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="单位名称：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.orgName}"/>
          </p:column>
      </p:row>
      <p:row>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="注册地址：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.orgAddr}" />
          </p:column>
      </p:row>
      <p:row rendered="#{mgrbean.zwOrgType == 3}">
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="通讯地址：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.communiAddr}" />
          </p:column>
      </p:row>
      <p:row rendered="#{mgrbean.zwOrgType == 3}">
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="实验室地址：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.labAddr}" />
          </p:column>
      </p:row>
      <p:row>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="法定代表人：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.orgFz}" />
          </p:column>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="#{mgrbean.zwOrgType == 3?'单位类型':'法定代表人职务'}：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.orgFzzw}" rendered="#{mgrbean.zwOrgType != 3}"/>
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.fkByUnitTypeId.codeName}" rendered="#{mgrbean.zwOrgType == 3}"/>
          </p:column>
      </p:row>
      <p:row>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="联系人：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.linkMan}" />
          </p:column>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="联系人手机：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.linkMb}" />
          </p:column>
      </p:row>
      <p:row>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="电话：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.linkTel}"/>
          </p:column>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="传真：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.fax}" />
          </p:column>
      </p:row>
      <p:row>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="邮编：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.zipcode}" />
          </p:column>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="电子邮箱：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.email}"/>
          </p:column>
      </p:row>
      <p:row rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and mgrbean.zwOrgType != 4}">
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="资质证书编号：" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}"/>
              <p:outputLabel value="备案编号：" rendered="#{mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2}"/>
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.certNo}" />
          </p:column>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="#{mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2?'备案日期':'发证时间'}：" rendered="#{mgrbean.ifValidityPeriod != '1' or (mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType != 0)}"/>
              <p:outputLabel value="备案有效期限：" rendered="#{mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType == 0}"/>
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.ifValidityPeriod != '1' or (mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType != 0)}">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.firstGetday}" >
                  <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
              </p:outputLabel>
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;" rendered="#{mgrbean.ifValidityPeriod == '1' and mgrbean.zwOrgType == 0}">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.firstGetday}" >
                  <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
              </p:outputLabel>
              ~
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.filingDate}" >
                  <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
              </p:outputLabel>
          </p:column>
      </p:row>
       <p:row rendered="#{mgrbean.ifShowApproveUnit==1 and (mgrbean.zwOrgType == 1 or  mgrbean.zwOrgType == 2 or mgrbean.zwOrgType == 3)}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="备案单位名称：" rendered="#{mgrbean.zwOrgType == 2}"/>
               <p:outputLabel value="资质批准单位：" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}"/>
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
               <p:outputLabel value="#{mgrbean.zwOrginfoEntity.rcdUnitName}" />
           </p:column>
       </p:row>
       <p:row rendered="#{(mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2) and mgrbean.ifShowRcdOrg}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="备案管理机构："/>
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;" colspan="3">
               <h:outputText value="#{mgrbean.rcdOrgMap.get(mgrbean.zwOrginfoEntity.rcdOrgId)}"/>
           </p:column>
       </p:row>
      <p:row rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and mgrbean.zwOrgType != 4}">
      	<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="状态：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;" colspan="#{((mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2) and mgrbean.zwOrginfoEntity.cancelState == 0)?3:0}">
              <p:outputLabel value="正常" rendered="#{null == mgrbean.zwOrginfoEntity.cancelState or mgrbean.zwOrginfoEntity.cancelState == 0}"/>
              <p:outputLabel value="注销" rendered="#{mgrbean.zwOrginfoEntity.cancelState == 1}"/>
          </p:column>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}">
              <p:outputLabel value="失效日期：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3" rendered="#{mgrbean.zwOrgType == 1 or mgrbean.zwOrgType == 3}">
          	<p:outputLabel value="#{mgrbean.zwOrginfoEntity.validDate}">
          		<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
          	</p:outputLabel>
          </p:column>
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and (mgrbean.zwOrgType == 0 or mgrbean.zwOrgType ==2) and mgrbean.zwOrginfoEntity.cancelState == 1}">
              <p:outputLabel value="注销日期：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3" rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and (mgrbean.zwOrgType == 0 or mgrbean.zwOrgType == 2) and mgrbean.zwOrginfoEntity.cancelState == 1}">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.cancelDate}">
                  <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
              </p:outputLabel>
          </p:column>
      </p:row>

       <p:row rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and mgrbean.zwOrgType == 1 and mgrbean.zwOrginfoEntity.cancelState == 1}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="注销日期：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
               <p:outputLabel value="#{mgrbean.zwOrginfoEntity.cancelDate}">
                   <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
               </p:outputLabel>
           </p:column>
       </p:row>

      <p:row rendered="#{mgrbean.zwOrgType == 3 and mgrbean.zwOrginfoEntity.cancelState == 1}">
      	<p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="注销日期：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;">
          	<p:outputLabel value="#{mgrbean.zwOrginfoEntity.cancelDate}">
          		<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
          	</p:outputLabel>
          </p:column>

          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="资质等级：" />
          </p:column>
          <p:column style="text-align:left;padding-left:3px;height: 25px;">
              <p:outputLabel value="#{mgrbean.zwOrginfoEntity.fkByLevelId.codeName}" />
          </p:column>
      </p:row>

       <p:row rendered="#{mgrbean.zwOrgType == 3 and mgrbean.zwOrginfoEntity.cancelState != 1}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="资质等级：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
               <p:outputLabel value="#{mgrbean.zwOrginfoEntity.fkByLevelId.codeName}" />
           </p:column>
       </p:row>

      <p:row rendered="#{!(mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1) and mgrbean.zwOrgType != 4}">
          <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
              <p:outputLabel value="服务项目：" rendered="#{mgrbean.zwOrgType != 3}"/>
              <p:outputLabel value="业务范围：" rendered="#{mgrbean.zwOrgType == 3}"/>
          </p:column>
          <p:column style="text-align:left;padding-left:0px;height: 25px;" colspan="3">
          	  <p:outputPanel>
         	  	 <table width="100%" style="border-color: white">
	              	  <c:forEach items="#{mgrbean.itemStr}" var="str">
	                      <tr>
	                          <td>
	                          <h:outputLabel value="#{str}"/>
	                          </td>
	                      </tr>
	                  </c:forEach>
	              </table>
          	  </p:outputPanel>
          </p:column>
      </p:row>
      <p:row rendered="#{mgrbean.zwOrgType == 0 and mgrbean.ifZoneRecord==1}">
   	   	 <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
             <p:outputLabel value="备案信息："/>
         </p:column>
         <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3">
         	<p:dataTable value="#{mgrbean.tjorgRecords}" 
         		emptyMessage="没有数据！" var="itm">
         		<p:column headerText="备案地区" style="text-align:center;width:100px;">
         			<h:outputText value="#{mgrbean.recordZoneMap.get(itm.zoneId).zoneName}"/>
         		</p:column>
                <p:column headerText="备案单位名称" style="width:160px;">
                    <h:outputText value="#{itm.unitName}"/>
                </p:column>
         		<p:column headerText="备案编号" style="text-align:center;width:150px;">
         			<h:outputText value="#{itm.rcdNo}"/>
         		</p:column>
         		<p:column headerText="备案日期" style="text-align:center;width:80px;">
         			<h:outputText value="#{itm.certDate}">
         				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
         			</h:outputText>
         		</p:column>
         		<p:column headerText="服务项目" style="width:380px;">
         			<h:outputText value="#{itm.serviceItems}"></h:outputText>
         		</p:column>
         		<p:column headerText="状态" style="text-align:center;width:80px;">
         			<h:outputText value="正常" rendered="#{itm.stateMark==0}"></h:outputText>
         			<h:outputText value="注销" rendered="#{itm.stateMark==1}"></h:outputText>
         		</p:column>
         		<p:column headerText="注销日期" style="text-align:center;width:80px;">
         			<h:outputText value="#{itm.logoutDate}">
         				<f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
         			</h:outputText>
         		</p:column>
         		<p:column headerText="操作" style="width:280px;">
         		</p:column>
         	</p:dataTable>
         </p:column>
       </p:row>

       <p:row rendered="#{mgrbean.zwOrgType==0 and (mgrbean.ifZoneApprove or mgrbean.ifHethFj)}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.ifZoneApprove}">
               <p:outputLabel value="批准的执业区域："/>
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;#{mgrbean.ifHethFj?'width: 260px;':''}" colspan="#{mgrbean.ifHethFj?1:3}" rendered="#{mgrbean.ifZoneApprove}">
               <h:outputText value="#{mgrbean.selectZoneApproveName}"/>
           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.ifHethFj}">
               <p:outputLabel value="外出开展职业健康检查"/><br/>
               <p:outputLabel value="工作能力："/>
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="#{mgrbean.ifZoneApprove?1:3}" rendered="#{mgrbean.ifHethFj}">
               <p:outputLabel value="不具备" rendered="#{mgrbean.zwOrginfoEntity.outWorkPower == 0}"/>
               <p:outputLabel value="具备" rendered="#{mgrbean.zwOrginfoEntity.outWorkPower == 1}"/>
           </p:column>
       </p:row>

       <p:row rendered="#{mgrbean.zwOrgType == 4}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;">
               <p:outputLabel value="状态：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;width: 260px;" colspan="#{mgrbean.zwOrginfoEntity.cancelState == 1 ? 1 : 5 }">
               <p:outputLabel value="正常" rendered="#{null == mgrbean.zwOrginfoEntity.cancelState or mgrbean.zwOrginfoEntity.cancelState == 0}"/>
               <p:outputLabel value="注销" rendered="#{mgrbean.zwOrginfoEntity.cancelState == 1}"/>
           </p:column>
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" rendered="#{mgrbean.zwOrginfoEntity.cancelState == 1}" >
               <p:outputLabel value="注销日期：" />
           </p:column>
           <p:column style="text-align:left;padding-left:3px;height: 25px;" colspan="3" rendered="#{mgrbean.zwOrginfoEntity.cancelState == 1}">
               <p:outputLabel value="#{mgrbean.zwOrginfoEntity.cancelDate}">
                   <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
               </p:outputLabel>
           </p:column>
       </p:row>
       <!--支撑机构-->
       <p:row rendered="#{mgrbean.zwOrgType==4 and mgrbean.zwOrginfoEntity.state == 1 and '2' eq mgrbean.unitType}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
               <p:outputLabel value="机构类型："/>
           </p:column>
           <p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3" >
               <p:outputLabel value="#{mgrbean.zwOrginfoEntity.fkByTypeId.codeName}"/>
           </p:column>
       </p:row>
       <p:row rendered="#{mgrbean.zwOrgType==4 and mgrbean.zwOrginfoEntity.state == 1 and null ne mgrbean.unitType}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
               <p:outputLabel value="技术支撑能力："/>
           </p:column>
           <p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3" >
               <c:forEach items="#{mgrbean.techTypesList}" var="techType">
                   <p:outputPanel style="display: flex;vertical-align: middle;">
                       <c:if test="#{techType.selNums>0}">
                           <p:outputLabel value="#{techType.codeName}"/>
                           <p:outputLabel value="（" style="padding-right: 3px"/>
                           <p:outputLabel value="#{techType.smallTypeNames}"/>
                           <p:outputLabel value="）" style="padding-left: 3px"/>
                       </c:if>
                   </p:outputPanel>
               </c:forEach>
           </p:column>
       </p:row>
       <p:row rendered="#{mgrbean.zwOrgType==4 and mgrbean.zwOrginfoEntity.state == 1 and null ne mgrbean.unitType}">
           <p:column style="text-align:right;padding-right:3px;width:150px;height: 25px;" >
               <p:outputLabel value="具备职业健康技术服务相关资质或能力的情况："/>
           </p:column>
           <p:column style="text-align:left;padding-left:-3px;height: 25px;width: 260px;" colspan="3">
               <p:outputLabel value="#{mgrbean.zwOrginfoEntity.quaTypeName}"/>
           </p:column>
       </p:row>
  </p:panelGrid>
    <p:fieldset legend="职业病因素监测开展情况" style="margin-top: 5px;margin-bottom: 5px;"
                toggleable="true" toggleSpeed="500" rendered="#{mgrbean.zwOrgType == 4 and mgrbean.zwOrginfoEntity.state == 1 and null ne mgrbean.unitType}">
        <p:dataTable  paginatorPosition="bottom"
                      value="#{mgrbean.zwOrginfoEntity.supportChkList}"
                      widgetVar="SupportChkTable" var="item"
                      emptyMessage="没有您要找的记录！" rowIndexVar="R">
            <!--@elvariable id="R" type="java.lang.Integer"-->
            <p:column headerText="序号" style="width:40px;text-align: center;">
                <p:outputLabel value="#{R+1}"/>
            </p:column>
            <p:column headerText="监测周期" style="width:300px;text-align: center;">
                <p:outputLabel value="#{item.startDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
                ~
                <p:outputLabel value="#{item.endDate}">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="zh_CN" />
                </p:outputLabel>
            </p:column>
            <p:column headerText="累计开展职业病危害因素监测企业数（家）" style="width:300px;text-align: center;"
                      styleClass="th_required_header">
                <p:outputLabel value="#{item.crptNum}"/>
            </p:column>
        </p:dataTable>
    </p:fieldset>
    <p:fieldset legend="财政投入情况" style="margin-top: 5px;margin-bottom: 5px;"
                toggleable="true" toggleSpeed="500" rendered="#{mgrbean.zwOrgType == 4 and mgrbean.zwOrginfoEntity.state == 1 and '2' eq mgrbean.unitType}">
        <p:dataTable paginatorPosition="bottom"
                     value="#{mgrbean.zwOrginfoEntity.supportFundList}"
                     widgetVar="SupportFundTable" var="item"
                     emptyMessage="没有您要找的记录！" rowIndexVar="R">
            <!--@elvariable id="R" type="java.lang.Integer"-->
            <p:column headerText="序号" style="width:40px;text-align: center;">
                <p:outputLabel value="#{R+1}"/>
            </p:column>
            <p:column headerText="年份" style="width:300px;text-align: center;">
                <p:outputLabel value="#{item.year}"/>
            </p:column>
            <p:column headerText="投入情况（万元）" style="width:300px;text-align: center;"
                      styleClass="th_required_header">
                <p:outputLabel value="#{item.fund}"/>
            </p:column>
        </p:dataTable>
    </p:fieldset>
</ui:composition>
