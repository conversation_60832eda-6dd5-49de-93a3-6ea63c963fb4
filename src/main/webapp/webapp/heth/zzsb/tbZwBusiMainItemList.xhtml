<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.TbZwBusiMainItemListBean"-->
    <ui:param name="mgrbean" value="#{tbZwBusiMainItemListBean}"/>
    <h:head>
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <script type="text/javascript">
            //<![CDATA[

            //]]>
        </script>
        <style type="text/css">

        </style>
    </h:head>

    <h:body>
        <p:tabView id="tabView" dynamic="true" cache="true"
                   activeIndex="#{mgrbean.activeTab}" style="border:1px; padding:0;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <h:outputStylesheet name="css/default.css"/>
                    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
                    <h:outputStylesheet name="css/ui-tabs.css"/>
                    <h:outputStylesheet name="css/ui-cs.css"/>
                    <h:outputScript library="js" name="namespace.js"/>
                    <h:outputScript name="js/validate/system/validate.js"/>
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                                    <p:outputLabel value="业务范围与重点、一般检测项目关系维护"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:panelGrid style="width:100%; line-height: 20px;">
                        <f:facet name="header">
                            <p:row>
                                <p:column style="width:200px;text-align: center;" styleClass="ui-state-default">
                                    <h:outputText value="业务范围大类"/>
                                </p:column>
                                <p:column style="width:360px;text-align: center;" styleClass="ui-state-default">
                                    <h:outputText value="业务范围小类"/>
                                </p:column>
                                <p:column style="text-align: center;" styleClass="ui-state-default">
                                    <h:outputText value="操作"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                        <c:if test="#{mgrbean.datas != null and mgrbean.datas.size() > 0}">
                            <c:forEach var="data" items="#{mgrbean.datas}">
                                <c:forEach var="da" items="#{data.simpleCodeList}" varStatus="index">
                                    <p:row>
                                        <p:column style="text-align: center;" rowspan="#{data.simpleCodeList.size()}" rendered="#{index.index == 0}">
                                            <h:outputText value="#{data.simpleCode.codeName}"/>
                                        </p:column>
                                        <p:column style="padding-left: 8px;">
                                            <h:outputText value="#{da.codeName}"/>
                                        </p:column>
                                        <p:column style="padding-left: 8px;">
                                            <p:commandLink value="配置" resetValues="true"
                                                           action="#{mgrbean.modInitAction}" update=":tabView"
                                                           process="@this">
                                                <f:setPropertyActionListener target="#{mgrbean.bigId}" value="#{data.simpleCode.rid}"/>
                                                <f:setPropertyActionListener target="#{mgrbean.simpleId}" value="#{da.rid}"/>
                                            </p:commandLink>
                                        </p:column>
                                    </p:row>
                                </c:forEach>
                            </c:forEach>
                        </c:if>
                        <c:if test="#{mgrbean.datas == null or mgrbean.datas.size() == 0}">
                            <p:row>
                                <p:column  colspan="3">
                                    <p:outputLabel value="没有您要找的记录！"></p:outputLabel>
                                </p:column>
                            </p:row>
                        </c:if>

                    </p:panelGrid>

                </h:form>
            </p:tab>
            <p:tab id="edit" title="edit" titleStyle="display:none;">
                <ui:include src="/webapp/heth/zzsb/tbZwBusiMainItemEdit.xhtml"/>
            </p:tab>

        </p:tabView>

        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>


</f:view>
</html>