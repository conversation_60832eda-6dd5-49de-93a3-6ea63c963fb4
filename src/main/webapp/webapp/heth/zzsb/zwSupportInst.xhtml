<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwSupportOrgEditZzsbBean"-->
    <h:outputScript name="js/datatable.js"/>
    <style type="text/css">
        .blue_vertical {
            width: 3px;
            height: 12px;
            background: #2e6e9e;
            margin-top: 1px;
            margin-right: 6px;
            display: inline-block;
        }

        .inst_view_table tr {
            border-left: none;
            border-right: none;
        }

        .inst_view_table td {
            border: 1px solid #a6c9e2;
        }

        .inst_view_table tr:first-child {
            border-top: none;
        }

        .inst_view_table td:first-child {
            border-top: none;
        }
    </style>
    <c:if test="#{not empty mgrbean.supportInstVOList}">
        <c:forEach items="#{mgrbean.supportInstVOList}" varStatus="var1" var="supportInstVO1">
            <p:fieldset legend="#{supportInstVO1.simpleCode.codeName}" toggleable="true" toggleSpeed="500" id="fieldSupport#{var1.index}"
                        style="margin-top: 5px;margin-bottom: 5px;">
                <c:if test="#{not supportInstVO1.onlyLevel2}">
                    <p:outputPanel>
                        <p:panelGrid styleClass="inst_view_table" style="width: 100%;">
                            <c:forEach items="#{supportInstVO1.childList}" varStatus="var2" var="supportInstVO2">
                                <c:if test="#{not empty supportInstVO2.childList}">
                                    <p:row>
                                        <p:column colspan="2" style="border-left: none;border-right: none;">
                                            <p:outputPanel styleClass="cs-flex cs-flex-ai-center" style="height: 36px;">
                                                <p:outputPanel styleClass="blue_vertical"/>
                                                <p:outputLabel value="#{supportInstVO2.simpleCode.codeName}"/>
                                            </p:outputPanel>
                                        </p:column>
                                    </p:row>
                                    <c:forEach items="#{supportInstVO2.childList}" varStatus="var3"
                                               var="supportInstVO3">
                                        <c:if test="#{not empty supportInstVO3.childList}">
                                            <p:row>
                                                <p:column style="height: 36px;width: 36px;text-align: center;">
                                                    <p:outputLabel value="#{supportInstVO3.num}"/>
                                                </p:column>
                                                <p:column style="background-color: #dfeffc;">
                                                    <p:outputPanel styleClass="cs-flex cs-flex-ai-center"
                                                                   style="height: 100%;width: 100%;">
                                                        <p:outputLabel style="padding-left: 6px;"
                                                                       value="#{supportInstVO3.simpleCode.codeName}"/>
                                                    </p:outputPanel>
                                                </p:column>
                                            </p:row>
                                            <c:forEach items="#{supportInstVO3.childList}" varStatus="var4"
                                                       var="supportInstVO4">
                                                <p:row>
                                                    <p:column style="height: 36px;width: 36px;text-align: center;">
                                                        <p:outputLabel value="#{supportInstVO4.num}"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:outputPanel styleClass="cs-flex cs-flex-ai-center"
                                                                       style="height: 100%;width: 100%;padding-left: 36px;">
                                                            <p:selectBooleanCheckbox
                                                                    id="sel#{var1.index}-#{var2.index}-#{var3.index}-#{var4.index}"
                                                                    style=""
                                                                    value="#{supportInstVO4.sel}"
                                                                    rendered="#{mgrbean.zwOrginfoEntity.state eq 0}"/>
                                                            <p:outputLabel
                                                                    for="sel#{var1.index}-#{var2.index}-#{var3.index}-#{var4.index}"
                                                                    style="padding-left: 6px;"
                                                                    value="#{supportInstVO4.simpleCode.codeName}"/>
                                                        </p:outputPanel>
                                                    </p:column>
                                                </p:row>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="#{empty supportInstVO3.childList}">
                                            <p:row>
                                                <p:column style="height: 36px;width: 36px;text-align: center;">
                                                    <p:outputLabel value="#{supportInstVO3.num}"/>
                                                </p:column>
                                                <p:column>
                                                    <p:outputPanel styleClass="cs-flex cs-flex-ai-center">
                                                        <p:selectBooleanCheckbox
                                                                id="sel#{var1.index}-#{var2.index}-#{var3.index}"
                                                                style="padding-left: 6px;"
                                                                value="#{supportInstVO3.sel}"
                                                                rendered="#{mgrbean.zwOrginfoEntity.state eq 0}"/>
                                                        <p:outputLabel
                                                                for="sel#{var1.index}-#{var2.index}-#{var3.index}"
                                                                style="padding-left: 6px;"
                                                                value="#{supportInstVO3.simpleCode.codeName}"/>
                                                    </p:outputPanel>
                                                </p:column>
                                            </p:row>
                                        </c:if>
                                    </c:forEach>
                                </c:if>
                            </c:forEach>
                        </p:panelGrid>
                    </p:outputPanel>
                </c:if>
                <c:if test="#{supportInstVO1.onlyLevel2}">
                    <p:outputPanel>
                        <p:panelGrid style="width: 100%;">
                            <c:forEach items="#{supportInstVO1.childList}" varStatus="var2" var="supportInstVO2">
                                <p:row>
                                    <p:column style="height: 36px;width: 36px;text-align: center;">
                                        <p:outputLabel value="#{supportInstVO2.num}"/>
                                    </p:column>
                                    <p:column>
                                        <p:outputPanel styleClass="cs-flex cs-flex-ai-center">
                                            <p:selectBooleanCheckbox id="sel#{var1.index}-#{var2.index}"
                                                                     style="padding-left: 6px;"
                                                                     value="#{supportInstVO2.sel}"
                                                                     rendered="#{mgrbean.zwOrginfoEntity.state eq 0}"/>
                                            <p:outputLabel for="sel#{var1.index}-#{var2.index}"
                                                           style="padding-left: 6px;"
                                                           value="#{supportInstVO2.simpleCode.codeName}"/>
                                        </p:outputPanel>
                                    </p:column>
                                </p:row>
                            </c:forEach>
                        </p:panelGrid>
                    </p:outputPanel>
                </c:if>
            </p:fieldset>
        </c:forEach>
    </c:if>
    <c:if test="#{empty mgrbean.supportInstVOList}">
        <p:outputPanel style="padding: 10px;">
            <p:outputLabel value="无"/>
        </p:outputPanel>
    </c:if>
</ui:composition>