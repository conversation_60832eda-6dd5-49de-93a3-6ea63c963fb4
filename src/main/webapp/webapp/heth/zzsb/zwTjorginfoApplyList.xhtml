<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">


    <!-- 托管Bean -->
    <ui:param name="mgrbean" value="#{zwTjorginfoApplyListBean}"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
    <!-- 审核页面 -->
    <ui:param name="editPage" value="/webapp/heth/zzsb/zwTjorginfoApplyEdit.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml" />
        <script src="#{request.contextPath}/resources/js/namespace.js" type="text/javascript"> </script>
        <script src="#{request.contextPath}/resources/js/validate/system/validate.js" ></script>
        <script type="text/javascript">
            //<![CDATA[

            //]]>
        </script>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构备案审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="5" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{mgrbean.searchAction}"
                                 update="dataTable"
                                 process="@this,:tabView:mainForm:mainGrid"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="备案单位地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 2px;width: 220px;">
                <zwx:ZoneSingleComp id="searchZone" zoneList="#{mgrbean.zoneList}"
                                    zoneCode="#{mgrbean.searchZoneCode}"
                                    zoneName="#{mgrbean.searchZoneName}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="备案单位："/>
            </p:column>
            <p:column style="text-align:left;padding-left:11px;width: 240px;">
                <p:inputText value="#{mgrbean.searchOrgName}" style="width: 200px;" maxlength="50" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="申请日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:10px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchApplyDateStart}" endDate="#{mgrbean.searchApplyDateEnd}"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="机构资质："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 8px;width: 220px;">
                <p:selectOneMenu style="width:188px" value="#{mgrbean.searchQualType}">
                    <f:selectItems  value="#{mgrbean.searchQualTypeList}"  />
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="申请类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;width: 240px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectApplyTypeNames}"
                                        selectedIds="#{mgrbean.searchApplyTypeState}"
                                        simpleCodeList="#{mgrbean.applyTypeList}"
                                        height="200" inputWidth="200"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 120px;">
                <p:outputLabel value="审核状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:5px;">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"></f:selectItems>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <p:column headerText="备案单位地区" style="width:180px;padding-left: 3px;">
            <h:outputLabel value="#{itm[1]}">
            </h:outputLabel>
        </p:column>
        <p:column headerText="备案单位" style="width:300px;padding-left: 3px;">
            <h:outputLabel value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="机构资质" style="width:170px;padding-left: 3px;text-align: center">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="申请类型" style="width:80px;text-align: center">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="申请日期" style="width:100px;text-align: center">
            <h:outputLabel value="#{itm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="审核状态" style="width:100px;text-align: center">
            <h:outputLabel value="待初审" rendered="#{itm[6] == 1}"/>
            <h:outputLabel value="初审驳回" rendered="#{itm[6] == 2}"/>
            <h:outputLabel value="初审通过" rendered="#{itm[6] == 3 and mgrbean.firstTrial}"/>
            <h:outputLabel value="待终审"   rendered="#{itm[6] == 3 and mgrbean.finalTrial}"/>
            <h:outputLabel value="终审驳回" rendered="#{itm[6] == 4}"/>
            <h:outputLabel value="终审通过" rendered="#{itm[6] == 5}"/>
        </p:column>
        <p:column headerText="驳回理由" style="width:350px;">
            <h:outputLabel value="#{itm[7]}" id="tips" styleClass="zwx-tooltip" rendered="#{itm[6] == 2}"/>
            <p:tooltip for="tips" style="max-width:350px;" rendered="#{itm[6] == 2}">
                <p:outputLabel value="#{itm[7]}" escape="false"></p:outputLabel>
            </p:tooltip>
            <h:outputLabel value="#{itm[8]}" id="ftips" styleClass="zwx-tooltip" rendered="#{itm[6] == 4}"/>
            <p:tooltip for="ftips" style="max-width:350px;" rendered="#{itm[6] == 4}">
                <p:outputLabel value="#{itm[8]}" escape="false"></p:outputLabel>
            </p:tooltip>
        </p:column>
        <p:column headerText="操作" style="padding-left: 3px;" exportable="false">
            <p:commandLink value="审核" action="#{mgrbean.preModAction}" process="@this" update=":tabView" rendered="#{(mgrbean.firstTrial and itm[6] == 1) or (mgrbean.finalTrial and itm[6] == 3) }">
                <f:setPropertyActionListener target="#{mgrbean.ifView}" value="0"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.orgType}" value="#{itm[9]}"/>
            </p:commandLink>
            <p:commandLink value="详情" action="#{mgrbean.preModAction}"  process="@this" update=":tabView" rendered="#{(mgrbean.firstTrial and itm[6] > 1) or (mgrbean.finalTrial and itm[6] > 3)}">
                <f:setPropertyActionListener target="#{mgrbean.ifView}" value="1"/>
                <f:setPropertyActionListener target="#{mgrbean.rid}" value="#{itm[0]}"/>
                <f:setPropertyActionListener target="#{mgrbean.orgType}" value="#{itm[9]}"/>
            </p:commandLink>
        </p:column>

    </ui:define>

</ui:composition>