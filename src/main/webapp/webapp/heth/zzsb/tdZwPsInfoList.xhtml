<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">
    <!-- 托管Bean -->
    <!--@elvariable id="tdZwPsnInfoZzsbBean" type="com.chis.modules.heth.zzsb.web.TdZwPsnInfoZzsbBean"-->
    <ui:param name="mgrbean" value="#{tdZwPsnInfoZzsbBean}"/>
    <ui:param name="mgrViewbean" value="#{tdZwPsnInfoZzsbBean}"/>
    <!-- 是否启用光标定位功能 -->
    <ui:param name="onfocus" value="false"/>
    <!-- 详情页面 -->
    <ui:param name="viewPage" value="/webapp/heth/zzsb/tdZwPsnInfoView.xhtml"/>
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <style type="text/css">
            .zwx-tooltip {
                -webkit-line-clamp: 1;
            }

            .psnDiag .ui-widget-content {
                padding-top: 0 !important;
            }
        </style>
        <script type="text/javascript">
            //<![CDATA[
            function disabledInput() {
                var text;
                var $tabView = $("#tabView\\:mainForm\\:codeEditGrid");
                $tabView.find("input,textarea,label").each(function () {
                    $(this).css("pointer-events", "none");
                });
                $tabView.find("a").each(function () {
                    text = $(this).text();
                    if (!text) {
                        text = $(this).attr("title");
                    }
                    if ("删除" == text || "修改" == text) {
                        $(this).remove();
                    } else if ("查看附件" == text || "查看" == text || "培训履历"== text) {

                    } else {
                        $(this).prop("disabled", true);
                        $(this).css("pointer-events", "none");
                        $(this).css("opacity", "0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box'],div[class*='ui-chkbox']").each(function () {
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity", "1");
                    $(this).css("pointer-events", "none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function () {
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events", "none");
                    $(this).css("opacity", "1");
                });
                //按钮
                $tabView.find("button").each(function () {
                    text = $(this).text();
                    if ("删除" == text) {
                        $(this).remove();
                    } else if ("关闭" == text || "查看" == text) {
                    } else {
                        $(this).prop("disabled", true);
                        $(this).css("pointer-events", "none");
                        $(this).css("opacity", "0.35");
                    }
                });
            }
            function disabledZzlbInput(){
                var text;
                var $tabView = $("#tabView\\:mainForm\\:zzlbInfoDialog");
                $tabView.find("input,textarea,label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"修改"==text){
                        $(this).remove();
                    }else if("查看附件"==text || "查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box'],div[class*='ui-chkbox']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("删除"==text){
                        $(this).remove();
                    }else if("关闭"==text||"查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            //]]>
        </script>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="专业技术人员档案检索"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn"
                                 action="#{tdZwPsnInfoZzsbBean.searchAction}"
                                 update="dataTable"
                                 process="@this,searchEmpName,searchIdcCard,searchZzlb,searchZone"/>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:153px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 274px;">
                <zwx:ZoneSingleComp zoneList="#{tdZwPsnInfoZzsbBean.zoneList}"
                                    zoneCode="#{tdZwPsnInfoZzsbBean.searchZoneCode}"
                                    zoneName="#{tdZwPsnInfoZzsbBean.searchZoneName}" id="searchZone"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 153px;">
                <p:outputLabel value="姓名："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 268px;">
                <p:inputText id="searchEmpName" value="#{tdZwPsnInfoZzsbBean.searchEntity.empName}" maxlength="50"
                             style="width: 180px;"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width: 153px;">
                <p:outputLabel value="身份证号："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:inputText id="searchIdcCard" value="#{tdZwPsnInfoZzsbBean.searchEntity.idcCard}" maxlength="18"
                             style="width: 180px;"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="资质类型："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <p:selectOneMenu value="#{tdZwPsnInfoZzsbBean.searchZzlx}" id="searchZzlx"
                                 style="width: 188px;vertical-align: middle;">
                    <f:selectItems value="#{tdZwPsnInfoZzsbBean.zzlxList}"/>
                    <p:ajax event="change" listener="#{tdZwPsnInfoZzsbBean.changeZzlbList()}"
                            process=":tabView:mainForm:mainGrid"
                            update=":tabView:mainForm:mainGrid"/>
                </p:selectOneMenu>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <p:outputLabel value="资质类别："/>
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <zwx:SimpleCodeManyComp id="searchZzlb" codeName="#{tdZwPsnInfoZzsbBean.searchZzlbNames}"
                                        selectedIds="#{tdZwPsnInfoZzsbBean.searchZzlbRids}" reset="true"
                                        simpleCodeList="#{tdZwPsnInfoZzsbBean.zzlbList}"
                                        height="300"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="地区" style="width:250px;padding-left: 3px;">
            <h:outputLabel value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="单位名称" style="width:250px;padding-left: 3px;">
            <h:outputLabel value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="姓名" style="width:180px;padding-left: 3px;text-align: center;">
            <p:commandLink value="#{itm[2]}"
                           update=":tabView:mainForm" process="@this"
                           oncomplete="PF('CodeEditDialog').show();"
                           action="#{tdZwPsnInfoZzsbBean.codeViewAction}">
                <f:setPropertyActionListener target="#{tdZwPsnInfoZzsbBean.rid}" value="#{itm[0]}"/>
            </p:commandLink>
        </p:column>
        <p:column headerText="身份证号" style="width:200px;padding-left: 3px;text-align: center;">
            <h:outputLabel value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="资质类别" style="padding-left: 3px;">
            <h:outputLabel value="#{itm[5]}"/>
        </p:column>
    </ui:define>
    <ui:define name="insertOtherMainContents">
        <!--人员维护 -->
        <p:dialog id="codeEditDialog" header="人员信息" widgetVar="CodeEditDialog" resizable="false" width="950"
                  height="600" modal="true" styleClass="psnDiag">
            <p:outputPanel
                    style="width:905px; height: 40px;position: fixed; margin-top: 0; top: auto; z-index: 9999;"
                    styleClass="zwx_toobar_42">
                <h:panelGrid columns="3" style="border-color:transparent;padding-bottom: 4px;padding-top: 1px;">
                    <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                    <p:commandButton value="关闭" icon="ui-icon-close" id="codeCloseBtn"
                                     onclick="PF('CodeEditDialog').hide();" immediate="true"/>
                </h:panelGrid>
            </p:outputPanel>
            <p:outputPanel style="width: 100%;height: 40px;"/>
            <p:outputPanel id="codeEditGrid" style="width:100%;">
                <ui:param name="psnInfoBase" value="#{tdZwPsnInfoZzsbBean.psnInfoBase}"/>
                <ui:param name="psnInfoForm" value="tabView:mainForm"/>
                <ui:param name="ifEdit" value="false"/>
                <ui:include src="psnInfoViewComm.xhtml">
                    <ui:param name="zwOrgType" value="#{tdZwPsnInfoZzsbBean.searchZzlx}"/>
                    <ui:param name="psnTypeNotNull" value="#{tdZwPsnInfoZzsbBean.searchZzlx ne '3'}"/>
                </ui:include>
            </p:outputPanel>
        </p:dialog>
        <ui:include src="zzlbInfoViewDialogComm.xhtml"/>
    </ui:define>
</ui:composition>