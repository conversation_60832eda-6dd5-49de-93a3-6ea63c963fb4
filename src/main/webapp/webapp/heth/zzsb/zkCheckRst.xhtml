<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <script type="text/javascript">

        //<![CDATA[
        //]]>
        </script>
<style>
    .zwx-tooltip-two{
        cursor: pointer;
        color:#25AAE1;
        word-break:break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }
    .zwx-tooltip-two:hover{
        color:orange !important;
        text-decoration:underline;
    }
</style>
    <p:dataTable var="rstItm" value="#{mgrbean.zwCheckRstList}"
                 id="zkCheckRstDatatable"
                 emptyMessage="没有您要找的记录！" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}"
                 rowsPerPageTemplate="#{mgrbean.perPageSize}"  lazy="true"
                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                 style="margin-top:5px;width:100%">
        <p:column headerText="专家组组长" style="padding-left: 3px;width:10%">
            <p:outputLabel value="#{rstItm[0]}"/>
        </p:column>
        <p:column headerText="专家组成员" style="padding-left: 3px;width:10%">
            <p:outputLabel value="#{rstItm[1]}"/>
        </p:column>
        <p:column headerText="发现的问题" style="padding-left: 3px;width:24%">
            <p:outputLabel id="rstTypes" value="#{rstItm[2]}" styleClass="zwx-tooltip-two"/>
            <p:tooltip for="rstTypes" style="max-width:799px;">
                <p:outputLabel value="#{rstItm[2]}" escape="false"/>
            </p:tooltip>
        </p:column>
        <p:column headerText="质控结论" style="text-align:center;width:6%">
            <p:outputLabel value="#{rstItm[3]}"/>
        </p:column>
        <p:column headerText="整改结果" style="text-align:center;width:6%">
            <p:outputLabel value="#{rstItm[4]}"/>
        </p:column>
        <p:column headerText="质控日期" style="text-align:center;width:6%">
            <p:outputLabel value="#{rstItm[5]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </p:outputLabel>
        </p:column>
        <p:column headerText="质控机构" style="padding-left: 3px;width:15%">
            <p:outputLabel value="#{rstItm[6]}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left:5px;">
            <ui:insert name="insertInstActions"/>
            <p:commandLink value="结果告知书"
                           process="@this"  onclick="window.open('/webFile/#{rstItm[7]}')" />
            <p:spacer width="5" rendered="#{rstItm[8] != null}"/>
            <p:commandLink value="整改报告" rendered="#{rstItm[8] != null}"
                           process="@this"  onclick="window.open('/webFile/#{rstItm[8]}')" />
        </p:column>
    </p:dataTable>

</ui:composition>