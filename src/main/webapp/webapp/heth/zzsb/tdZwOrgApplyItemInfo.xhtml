<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <style type="text/css">
        .calendarClass input{
            width:180px;
        }
    </style>
    <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;" id="itemsGrid">
        <f:facet name="header">
            <p:row>
                <p:column style="text-align:center;height: 20px;width:80px;">
                    <p:outputLabel value="序号" />
                </p:column>
                <p:column style="text-align:center;height: 20px;width:200px;">
                    <p:outputLabel value="项目大类" />
                </p:column>
                <p:column style="text-align:center;height: 20px;">
                    <p:outputLabel value="项目小类" />
                </p:column>
                <p:column style="text-align:center;height: 20px;">
                    <p:outputLabel value="是否外包" />
                </p:column>
            </p:row>
        </f:facet>
        <c:forEach items="#{orgApplyBean.showItemList}" var="v">
            <p:row>
                <p:column style="text-align:center;height: 27px;">
                    <p:outputLabel value="#{v.xh}" />
                </p:column>
                <p:column style="text-align:center;height: 27px;" rendered="#{v.isFirst}"
                          rowspan="#{v.rowspan}">
                    <p:outputLabel value="#{v.itemPro}" />
                </p:column>
                <p:column style="height: 27px;text-align:left;padding-left:3px;">
                    <p:outputLabel value="#{v.itemName}" />
                </p:column>
                <p:column style="height: 27px;text-align:left;padding-left:3px;">
                    <p:outputLabel value="是" rendered="#{(v.isOutter and mgrbean.applyInfoBaseBean.ifView) or (!orgApplyBean.ifBaseInfoCommInfoEdit and v.isOutter)}" />
                    <p:outputLabel value="否" rendered="#{((v.isOutter==null or !v.isOutter) and mgrbean.applyInfoBaseBean.ifView) or (v.isOutter==null or !v.isOutter and !orgApplyBean.ifBaseInfoCommInfoEdit)}" />
                    <p:selectBooleanCheckbox value="#{v.isOutter}" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
                        <p:ajax event="change" process="@this" listener="#{orgApplyBean.changeIsOutter(v)}" update="@this,#{updateId}:#{tabViewId}:itemsGrid"></p:ajax>
                    </p:selectBooleanCheckbox>
                </p:column>
            </p:row>
        </c:forEach>
    </p:panelGrid>
    <!-- 检查项目选择 -->
    <p:dialog id="checkItemDialog" header="检查项目选择"
              widgetVar="CheckItemDialog" resizable="false" width="1000"
              height="500" modal="true">
        <p:panelGrid style="width:100%;" id="selItemsGrid">
            <c:forEach items="#{orgApplyBean.diagMap}" var="v1" >
                <p:row>
                    <p:column style="text-align:left;width: 200px" >
                        <p:selectBooleanCheckbox value="#{v1.key.ifSelected}">
                            <p:ajax event="change" listener="#{orgApplyBean.selectItem}"
                                    process="@this,#{updateId}:#{tabViewId}:selItemId" update="#{updateId}:#{tabViewId}:user_#{v1.key.rid}"
                                    onstart="document.getElementById('#{updateName}:#{tabViewId}:selItemId').value = #{v1.key.rid}"
                            />
                        </p:selectBooleanCheckbox>
                        <p:outputLabel value="  " />
                        <p:outputLabel value="#{v1.key.codeName}" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:5px;">
                        <p:selectManyCheckbox value="#{v1.key.selCodeRids}"
                                              id="user_#{v1.key.rid}" columns="4" layout="grid" >
                            <f:selectItems value="#{v1.value}" var="code" itemLabel="#{code.codeName}"
                                           itemValue="#{code.rid}">
                            </f:selectItems>
                        </p:selectManyCheckbox>
                    </p:column>
                </p:row>
            </c:forEach>
        </p:panelGrid>
        <f:facet name="footer">
            <h:panelGrid style="width: 100%;text-align: center;">
                <h:panelGroup>
                    <p:commandButton value="保存" icon="ui-icon-check"
                                     id="checkItemSaveBtn" action="#{orgApplyBean.checkItemSaveAction}"
                                     process="@this,checkItemDialog,#{updateId}:#{tabViewId}:itemsGrid"
                                     update="#{updateId}:#{tabViewId}:itemsGrid"
                                     oncomplete="PF('CheckItemDialog').hide();" />
                    <p:spacer width="5" />
                    <p:commandButton value="取消" icon="ui-icon-close"
                                     id="checkItemBackBtn" onclick="PF('CheckItemDialog').hide();"
                                     immediate="true" />
                </h:panelGroup>
            </h:panelGrid>
        </f:facet>
    </p:dialog>
    <h:inputHidden id="selItemId" value="#{orgApplyBean.selItemId}" />
</ui:composition>
