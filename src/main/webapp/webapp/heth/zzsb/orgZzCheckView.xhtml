<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/viewTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.OrgZzCheckListBean"-->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            //<![CDATA[
            function disabledInput(){
                var text;
                var $tabView = $("#tabView\\:viewForm\\:codeEditGrid");
                $tabView.find("input,textarea,label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"修改"==text){
                        $(this).remove();
                    }else if("查看附件"==text || "查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box'],div[class*='ui-chkbox']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("删除"==text){
                        $(this).remove();
                    }else if("关闭"==text||"查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }

            function disabledZzlbInput(){
                var text;
                var $tabView = $("#tabView\\:viewForm\\:zzlbGrid");
                $tabView.find("input,textarea,label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"修改"==text){
                        $(this).remove();
                    }else if("查看附件"==text || "查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box'],div[class*='ui-chkbox']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("删除"==text){
                        $(this).remove();
                    }else if("关闭"==text||"查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            //]]>
        </script>
        <style type="text/css">
            #tabView\:viewForm\:serDetailDatatable table.ui-selectmanycheckbox td label{
                white-space: pre-line;
                width: 250px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构审核详情" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display: flex;">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <p:commandButton value="撤销" icon="ui-icon-cancel"
                                 action="#{mgrbean.cancelAction}" process="@this"
                                 update=":tabView">
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert"/>
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this" onclick="hideTooltips();"
                                 update=":tabView"/>
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputLabel value="最新更新日期：" style="color:blue;font-weight: bold;" rendered="#{null ne mgrbean.infoViewBean.zwOrginfoEntity.lastSmtDate}" />
                <p:outputLabel value="#{mgrbean.infoViewBean.zwOrginfoEntity.lastSmtDate}" style="color: blue;font-weight: bold;">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </p:outputLabel>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="审核意见" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;" >
            <p:panelGrid style="margin-top: 10px;margin-bottom: 10px;width:100%" >
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <p:outputLabel value="审核结果："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:3px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:outputLabel value="#{1 == mgrbean.editCheckRst ? '通过' : (2 == mgrbean.editCheckRst) ? '退回' : ''}" />
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <p:outputLabel value="审核意见：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:outputPanel style="width: 60%;">
                            <h:outputText value="#{mgrbean.editCheckAudit}"   />
                        </p:outputPanel>
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <ui:include src="orgZzCheckComm.xhtml" >
            <ui:param name="baseComposition" value="tabView:viewForm" />
        </ui:include>
    </ui:define>
</ui:composition>