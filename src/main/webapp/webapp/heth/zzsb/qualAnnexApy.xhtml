<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <c:forEach items="#{orgApplyBean.topOfInstitutionList}" var="top">
        <p:fieldset legend="#{top.codeName}" style="margin-top: 5px;margin-bottom: 5px;">
            <h:panelGrid columns="2" style="width: 20%;margin-bottom: 2px;">
                <p:commandButton value="添加" icon="ui-icon-plus" id="addInstitutionBtn#{top.rid}"
                                 action="#{orgApplyBean.institutionAddOne}" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                                 update="@this,topInsPanel#{top.rid}" process="@this">
                    <f:setPropertyActionListener target="#{orgApplyBean.codeId}" value="#{top.rid}"/>
                </p:commandButton>
            </h:panelGrid>
            <p:panelGrid style="width:100%;" id="topInsPanel#{top.rid}">
                <p:row>
                    <p:column style="text-align:center;height: 27px;width:260px;padding-left:10px;"
                              styleClass="ui-state-default">
                        <p:outputLabel value="制度名称"/>
                    </p:column>
                    <p:column style="text-align:center;width:200px;padding-left:10px;" styleClass="ui-state-default">
                        <p:outputLabel value="有无"/>
                    </p:column>
                    <p:column styleClass="ui-state-default"
                              style="text-align:center;padding-left:10px;#{orgApplyBean.ifBaseInfoCommInfoEdit? 'width:280px;' : ''}">
                        <p:outputLabel value="附件"/>
                    </p:column>
                    <p:column styleClass="ui-state-default" style="text-align:center;padding-left: 30px;"
                              rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}">
                        <p:outputLabel value="操作"/>
                    </p:column>
                </p:row>
                <c:forEach items="#{orgApplyBean.qualAnnexMap.get(top.rid)}" var="v" varStatus="index">
                    <p:row>
                        <p:column style="text-align:left;height: 27px;padding-left:10px;">
                            <p:outputLabel value="#{v.fkByRuleId.codeName}"
                                           rendered="#{v.fkByRuleId.rid != top.rid }"/>
                            <p:outputLabel value="#{v.otherRuleName}"
                                           rendered="#{(v.fkByRuleId.rid == top.rid and orgApplyBean.ifView) or (!orgApplyBean.ifBaseInfoCommInfoEdit)}"/>
                            <p:inputText value="#{v.otherRuleName}"
                                         rendered="#{v.fkByRuleId.rid == top.rid and !orgApplyBean.ifView and orgApplyBean.ifBaseInfoCommInfoEdit}"
                                         maxlength="25">
                                <p:ajax event="change" process="@this"/>
                            </p:inputText>
                        </p:column>
                        <p:column style="text-align:#{orgApplyBean.ifBaseInfoCommInfoEdit?'left':'center'};#{orgApplyBean.ifBaseInfoCommInfoEdit?'padding-left:50px;':''}">
                            <p:selectOneRadio value="#{v.ifHas}" rendered="#{orgApplyBean.ifBaseInfoCommInfoEdit}"
                                              style="width:135px;" converter="javax.faces.Integer">
                                <f:selectItem itemLabel="有" itemValue="1"></f:selectItem>
                                <f:selectItem itemLabel="无" itemValue="0"></f:selectItem>
                                <p:ajax event="change" process="@this"
                                        update="uploadLinkOrgAnnex#{top.rid}K#{index.index},viewAnnex#{top.rid}K#{index.index},delAnnex#{top.rid}K#{index.index}"/>
                            </p:selectOneRadio>
                            <p:outputLabel style="#{orgApplyBean.ifBaseInfoCommInfoEdit?'padding-left: 50px;':''}" value="#{v.ifHas == 1 ? '有' : '无'}"
                                           rendered="#{(orgApplyBean.ifView) or(!orgApplyBean.ifBaseInfoCommInfoEdit)}"/>
                        </p:column>
                        <p:column style="text-align:left;padding-left:10px;">
                            <p:commandLink value="查看" id="viewAnnex#{top.rid}K#{index.index}"
                                           disabled="#{v.ifHas == 1 ? false : true}"
                                           rendered="#{v.annexPath != '' and null != v.annexPath}"
                                           process="@this" onclick="window.open('/webFile/#{v.annexPath}')"/>
                            <p:commandLink value="上传附件" id="uploadLinkOrgAnnex#{top.rid}K#{index.index}"
                                           disabled="#{v.ifHas == 1 ? false : true}"
                                           rendered="#{(v.annexPath == '' or null == v.annexPath) and orgApplyBean.ifBaseInfoCommInfoEdit}"
                                           process="@this" update="@this,topInsPanel#{top.rid}"
                                           onclick="PF('TjorgAnnexFileDialog').show();">
                                <f:setPropertyActionListener target="#{orgApplyBean.annexTmp}" value="#{v}"/>
                            </p:commandLink>
                            <p:spacer width="5" rendered="#{v.annexPath != '' and null != v.annexPath}"/>
                            <p:commandLink value="删除" id="delAnnex#{top.rid}K#{index.index}"
                                           disabled="#{v.ifHas == 1 ? false : true}"
                                           rendered="#{v.annexPath != '' and null != v.annexPath and orgApplyBean.ifBaseInfoCommInfoEdit}"
                                           process="@this" update="@this,topInsPanel#{top.rid}"
                                           action="#{orgApplyBean.delInsFile}">
                                <f:setPropertyActionListener target="#{orgApplyBean.annexTmp}" value="#{v}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                        <p:column style="text-align:left;padding-left: 10px;"
                                  rendered="#{!orgApplyBean.ifView and orgApplyBean.ifBaseInfoCommInfoEdit}">
                            <p:commandLink value="删除"
                                           rendered="#{v.fkByRuleId.rid == top.rid and orgApplyBean.ifBaseInfoCommInfoEdit}"
                                           process="@this" update="@this,topInsPanel#{top.rid}"
                                           action="#{orgApplyBean.institutionDeleteOne}">
                                <f:setPropertyActionListener target="#{orgApplyBean.annexTmp}" value="#{v}"/>
                                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                            </p:commandLink>
                        </p:column>
                    </p:row>
                </c:forEach>
            </p:panelGrid>
        </p:fieldset>
    </c:forEach>
    <p:dialog header="附件上传" widgetVar="TjorgAnnexFileDialog"
              resizable="false" modal="true">
        <table>
            <tr>
                <td style="text-align: right;"><p:outputLabel
                        value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                        style="position: relative;bottom: -6px;padding-right: 120px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
                </td>
            </tr>
            <tr>
                <td style="position: relative;top: -23px;">
                    <p:fileUpload
                            requiredMessage="请选择要上传的文件！" label="文件选择"
                            fileUploadListener="#{orgApplyBean.institutionAnnexFileUpload}"
                            invalidSizeMessage="文件大小不能超过100M!" id="upload"
                            validatorMessage="上传出错啦，请重新上传！" style="width:600px;"
                            previewWidth="120" cancelLabel="取消" update="@this,#{orgApplyBean.topInsPanelStr}"
                            uploadLabel="上传" dragDropSupport="true" mode="advanced"
                            sizeLimit="104857600" fileLimit="1"
                            fileLimitMessage="最多只能上传1个文件！"
                            invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                            allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                    /></td>
            </tr>
        </table>
    </p:dialog>
</ui:composition>