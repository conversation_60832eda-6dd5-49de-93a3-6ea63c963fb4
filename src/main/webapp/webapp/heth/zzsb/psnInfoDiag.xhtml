<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
	<h:outputStylesheet name="css/ui-tabs.css"/>
	<p:dialog header="专业资质信息" widgetVar="ZzlbInfoDialog" id="zzlbInfoDialog" resizable="false" modal="true" width="650" height="300">
			<p:panelGrid id="zzlbGrid" style="width:100%;">
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<font color="red">*</font>
						<h:outputText value="资质类别："/>
					</p:column>
					<p:column style="text-align:left;padding-left:3px;" >
						<p:selectOneMenu value="#{psnInfoBase.psnZzlbNew.fkByQualId.rid}">
							<f:selectItem itemLabel="--请选择--"  itemValue=""/>
							<f:selectItems value="#{psnInfoBase.qualList}" var="itm"
								itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/> 
							<p:ajax event="change" listener="#{psnInfoBase.onQualChange}" process="@this,:#{psnInfoForm}:zzlbGrid"
								update=":#{psnInfoForm}:zzlbGrid"/>
						</p:selectOneMenu>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<font color="red">*</font>
						<h:outputText value="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2==8?'发文':'证书'}编号："/>
					</p:column>
					<p:column style="text-align:left;padding-left:3px" >
						<p:inputText value="#{psnInfoBase.psnZzlbNew.centNo}" maxlength="25" style="width: 180px;"/>
						<p:outputLabel value="（请完整维护#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2==8?'发文':'证书'}编号）" style="padding-left:5px;color:blue;"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<font color="red">*</font>
						<h:outputText value="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2==8?'发文':'发证'}日期：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px" >
						<p:calendar value="#{psnInfoBase.psnZzlbNew.sendDate}" size="11" navigator="true"
                                 yearRange="c-50:c" converterMessage="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2==8?'发文':'发证'}日期格式输入不正确！"
                                 pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
                                 maxdate="new Date()" readonlyInput="true"/>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<h:outputText value="失效日期：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px" >
						<p:calendar value="#{psnInfoBase.psnZzlbNew.validDate}" size="11" navigator="true"
                                 yearRange="c-50:c+20" converterMessage="失效日期格式输入不正确！"
                                 pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"/>
					</p:column>
				</p:row>
                <p:row rendered="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS2!=8}">
                    <p:column style="text-align:right;padding-right:3px;width:120px;">
                        <h:outputText value="资质获取途径：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:3px" >
                        <p:selectOneMenu value="#{psnInfoBase.psnZzlbNew.zzFetchWay}" style="width: 95px;">
                            <f:selectItem itemLabel="--请选择--" itemValue=""/>
                            <f:selectItem itemLabel="培训发证" itemValue="1"/>
                            <f:selectItem itemLabel="单位内训" itemValue="2"/>
                        </p:selectOneMenu>
                    </p:column>
                </p:row>
				<p:row rendered="#{psnInfoBase.qualMap.get(psnInfoBase.psnZzlbNew.getFkByQualId().getRid()).extendS5!=null}">
					<p:column style="text-align:right;padding-right:3px;width:120px;">
						<font color="red">*</font>
						<h:outputText value="资质范围：" />
					</p:column>
					<p:column style="text-align:left;padding-left:3px" >
						<p:selectManyCheckbox value="#{psnInfoBase.psnZzlbNew.selectRanges}"
							columns="1" layout="grid">
							<f:selectItems value="#{psnInfoBase.rangeList}" var="itm"	
								itemLabel="#{itm.codeName}" itemValue="#{itm.rid}"/>
						</p:selectManyCheckbox>
					</p:column>
				</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;">
                        <font color="red">*</font>
						<h:outputText value="附件：" />
					</p:column>
					<p:column>
						<p:commandButton value="上传" action="#{psnInfoBase.beforeFileUpload}"
							process="@this,:#{psnInfoForm}:zzlbGrid"
							rendered="#{psnInfoBase.psnZzlbNew.annexPath ==null}">
							<f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="4"></f:setPropertyActionListener>	
						</p:commandButton>
						<h:outputText value="#{psnInfoBase.psnZzlbNew.annexName}"
							rendered="#{psnInfoBase.psnZzlbNew.annexPath !=null}"></h:outputText>
						<p:commandButton value="查看" process="@this"
							onclick="window.open('/webFile/#{psnInfoBase.psnZzlbNew.annexPath}')"
							rendered="#{psnInfoBase.psnZzlbNew.annexPath !=null}"></p:commandButton>
						<p:spacer width="5" rendered="#{psnInfoBase.psnZzlbNew.annexPath!=null}"/>
						<p:commandButton value="删除" action="#{psnInfoBase.delAnnex}"
							process="@this,:#{psnInfoForm}:zzlbGrid"
							update=":#{psnInfoForm}:zzlbGrid,:#{psnInfoForm}:instDatatable"
							rendered="#{psnInfoBase.psnZzlbNew.annexPath !=null}">
							<f:setPropertyActionListener target="#{psnInfoBase.fileIndex}" value="4"></f:setPropertyActionListener>
							<p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
						</p:commandButton>
					</p:column>
				</p:row>
			</p:panelGrid>
			<f:facet name="footer">
				<h:panelGrid style="width: 100%;text-align: center;">
					<h:panelGroup>
						<p:commandButton value="保存" icon="ui-icon-check" id="zzlbSaveBtn" action="#{mgrbean.executeSaveZzlbAction}"
										 process="@this,:#{psnInfoForm}:zzlbGrid" >
								<f:setPropertyActionListener target="#{mgrbean.psnInfoForm}" value="#{psnInfoForm}"></f:setPropertyActionListener>
						</p:commandButton>
						<p:spacer width="5" />
						<p:commandButton value="取消" icon="ui-icon-close" id="zzlbBackBtn" onclick="PF('ZzlbInfoDialog').hide();" immediate="true" />
					</h:panelGroup>
				</h:panelGrid>
			</f:facet>
		</p:dialog>
	<p:dialog header="培训履历" widgetVar="TrainHisDialog" id="trainHisDialog" resizable="false" modal="true" width="750" height="300">
		<p:panelGrid style="width:100%;margin-top:5px;margin-bottom:30px;" >
			<p:row>
				<p:column style="width: 120px;text-align: center;" styleClass="ui-state-default">
					<p:outputLabel value="继续教育证书编号"/>
				</p:column>
				<p:column style="width:200px;text-align: center;" styleClass="ui-state-default" rendered="#{psnInfoBase.psnZzlb.ifShowRange}">
					<p:outputLabel value="合格类型"/>
				</p:column>
				<p:column style="width:120px;text-align:center;" styleClass="ui-state-default">
					<p:outputLabel value="证书有效期"/>
				</p:column>
			</p:row>
			<c:forEach items="#{psnInfoBase.psnZzlb.trainHisList}" var="trainHis">
				<p:row>
					<p:column style="text-align: left;height:25px;">
						<p:outputLabel value="#{trainHis.certNo}"/>
					</p:column>
					<p:column style="text-align: left;height:25px;" rendered="#{psnInfoBase.psnZzlb.ifShowRange}">
						<p:outputLabel value="#{trainHis.rangeTypes}"/>
					</p:column>
					<p:column style="text-align: center;height:25px;">
						<p:outputLabel value="#{trainHis.certDate}"/>
					</p:column>
				</p:row>
			</c:forEach>
		</p:panelGrid>
	</p:dialog>
	<!-- 相关材料附件上传弹出框 -->
	<p:dialog header="附件上传" widgetVar="FileDialog" id="fileDialog" resizable="false" modal="true" >
			<table>
				<tr>
					<td style="text-align: right;">
						<p:outputLabel value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
				                style="position: relative;bottom: -6px;padding-right: 138px;font-weight: bold;color: #ffffff;z-index: 10;"></p:outputLabel>
					</td>
				</tr>
				<tr>
					<td style="position: relative;top: -23px;">
					<p:fileUpload requiredMessage="请选择要上传的文件！"
						label="文件选择" fileUploadListener="#{psnInfoBase.fileUpload}"
						invalidSizeMessage="文件大小不能超过100M!" validatorMessage="上传出错啦，请重新上传！" 
						style="width:600px;" previewWidth="120" cancelLabel="取消" 
						fileLimit="1" fileLimitMessage="只能选择一个文件！" update="@this"
						uploadLabel="上传" dragDropSupport="true" mode="advanced" sizeLimit="104857600"
						invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
						allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
						 />
					</td>
				</tr>
			</table>
		</p:dialog>	 
</ui:composition>