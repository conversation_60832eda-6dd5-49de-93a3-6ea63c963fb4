<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwSupportOrgEditZzsbBean"-->
    <h:outputScript name="js/datatable.js"/>
    <style type="text/css">
        .blue_vertical {
            width: 3px;
            height: 12px;
            background: #2e6e9e;
            margin-top: 1px;
            margin-right: 6px;
            display: inline-block;
        }

        .inst_view_table  tr {
            border-left: none;
            border-right: none;
        }

        .inst_view_table td {
            border: 1px solid #a6c9e2;
        }

        .inst_view_table  tr:first-child {
            border-top: none;
        }

        .inst_view_table td:first-child {
            border-top: none;
        }
        .spc_view_table tr {
            border-top: 1px solid #a6c9e2 !important;
        }
        .spc_view_table td {
            border-top: 1px solid #a6c9e2 !important;
        }
    </style>
    <c:forEach items="#{mgrbean.abilityShowList}" varStatus="var1" var="supportInstVO1">
        <p:fieldset legend="#{supportInstVO1.simpleCode.codeName}" toggleable="true" toggleSpeed="500" id="fieldCapacity#{var1.index}"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <c:if test="#{null ne supportInstVO1.simpleCode.extendS1 and supportInstVO1.simpleCode.extendS1 == '1'}">
                <p:panelGrid style="width: 100%;" id="supportHeth" >
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="职业健康知识宣传："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;" colspan="5">
                            <p:selectOneRadio value="#{mgrbean.supportHealth.healthPublic}" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                              style="width:120px;">
                                <f:selectItem itemLabel="否" itemValue="0" />
                                <f:selectItem itemLabel="是" itemValue="1" />
                                <p:ajax process=":mainForm:tabView:supportHeth" update=":mainForm:tabView:supportHeth" />
                            </p:selectOneRadio>
                            <c:if test="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}">
                                <p:outputLabel value="否" rendered="#{0 eq mgrbean.supportHealth.healthPublic}"/>
                                <p:outputLabel value="是" rendered="#{1 eq mgrbean.supportHealth.healthPublic}" />
                            </c:if>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;#{1 eq mgrbean.supportHealth.healthPublic?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="宣传活动（场）："/>
                        </p:column>
                        <p:column style="width: 15%;text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.healthPublic?'':'display: none;'}">
                            <p:inputText value="#{mgrbean.supportHealth.publicNum}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.publicNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                        <p:column style="width: 15%;text-align: right; padding-right: 5px;#{1 eq mgrbean.supportHealth.healthPublic?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="培训活动（场）："/>
                        </p:column>
                        <p:column style="width: 15%;text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.healthPublic?'':'display: none;'}">
                            <p:inputText value="#{mgrbean.supportHealth.trainNum}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.trainNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                        <p:column style="width: 15%;text-align: right; padding-right: 5px;#{1 eq mgrbean.supportHealth.healthPublic?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="培训学员（人）："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.healthPublic?'':'display: none;'}">
                            <p:inputText value="#{mgrbean.supportHealth.trainPsnNum}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.trainPsnNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="职业健康宣传信息报送："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;" colspan="5">
                            <p:selectOneRadio value="#{mgrbean.supportHealth.ifPublicRpt}" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                              style="width:120px;">
                                <f:selectItem itemLabel="否" itemValue="0" />
                                <f:selectItem itemLabel="是" itemValue="1" />
                                <p:ajax process=":mainForm:tabView:supportHeth" update=":mainForm:tabView:supportHeth" />
                            </p:selectOneRadio>
                            <c:if test="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}">
                                <p:outputLabel value="否" rendered="#{0 eq mgrbean.supportHealth.ifPublicRpt}"/>
                                <p:outputLabel value="是" rendered="#{1 eq mgrbean.supportHealth.ifPublicRpt}" />
                            </c:if>
                        </p:column>
                    </p:row>
                    <p:row >
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;#{1 eq mgrbean.supportHealth.ifPublicRpt?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="上报宣传信息（条）："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.ifPublicRpt?'':'display: none;'}" colspan="5">
                            <p:inputText value="#{mgrbean.supportHealth.publicRptNum}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.publicRptNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                            <p:outputLabel value="（仅统计向上级主管部门或主流媒体公布的信息）" style="margin-left: 10px;" />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="职业健康科普作品（仅统计原创作品）："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;" colspan="5">
                            <p:selectOneRadio value="#{mgrbean.supportHealth.ifScience}" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                              style="width:120px;">
                                <f:selectItem itemLabel="否" itemValue="0" />
                                <f:selectItem itemLabel="是" itemValue="1" />
                                <p:ajax process=":mainForm:tabView:supportHeth" update=":mainForm:tabView:supportHeth" />
                            </p:selectOneRadio>
                            <c:if test="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}">
                                <p:outputLabel value="否" rendered="#{0 eq mgrbean.supportHealth.ifScience}"/>
                                <p:outputLabel value="是" rendered="#{1 eq mgrbean.supportHealth.ifScience}" />
                            </c:if>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="视频（件）："/>
                        </p:column>
                        <p:column style="width: 15%;text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:inputText value="#{mgrbean.supportHealth.videoNum}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.videoNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                        <p:column style="width: 15%;text-align: right; padding-right: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="音频（件）："/>
                        </p:column>
                        <p:column style="width: 15%;text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:inputText value="#{mgrbean.supportHealth.audioNum}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.audioNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                        <p:column style="width: 15%;text-align: right; padding-right: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="书刊（件）："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:inputText value="#{mgrbean.supportHealth.books}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.books}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="宣传册（件）："/>
                        </p:column>
                        <p:column style="width: 15%;text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:inputText value="#{mgrbean.supportHealth.brochure}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.brochure}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                        <p:column style="width: 15%;text-align: right; padding-right: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="其它（件）："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.ifScience?'':'display: none;'}" colspan="3">
                            <p:inputText value="#{mgrbean.supportHealth.otherNum}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.otherNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="职业健康促进："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;" colspan="5">
                            <p:selectOneRadio value="#{mgrbean.supportHealth.ifPromote}" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                              style="width:120px;">
                                <f:selectItem itemLabel="否" itemValue="0" />
                                <f:selectItem itemLabel="是" itemValue="1" />
                            </p:selectOneRadio>
                            <c:if test="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}">
                                <p:outputLabel value="否" rendered="#{0 eq mgrbean.supportHealth.ifPromote}"/>
                                <p:outputLabel value="是" rendered="#{1 eq mgrbean.supportHealth.ifPromote}" />
                            </c:if>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="健康企业建设技术指导与支持："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;" colspan="5">
                            <p:selectOneRadio value="#{mgrbean.supportHealth.ifGuid}" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                              style="width:120px;">
                                <f:selectItem itemLabel="否" itemValue="0" />
                                <f:selectItem itemLabel="是" itemValue="1" />
                                <p:ajax process=":mainForm:tabView:supportHeth" update=":mainForm:tabView:supportHeth" />
                            </p:selectOneRadio>
                            <c:if test="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}">
                                <p:outputLabel value="否" rendered="#{0 eq mgrbean.supportHealth.ifGuid}"/>
                                <p:outputLabel value="是" rendered="#{1 eq mgrbean.supportHealth.ifGuid}" />
                            </c:if>
                        </p:column>
                    </p:row>
                    <p:row>
                        <p:column style="height: 36px;width: 15%;text-align: right;padding-right: 5px;#{1 eq mgrbean.supportHealth.ifGuid?'':'display: none;'}">
                            <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                            <p:outputLabel value="指导企业（家）："/>
                        </p:column>
                        <p:column style="text-align: left; padding-left: 5px;#{1 eq mgrbean.supportHealth.ifGuid?'':'display: none;'}" colspan="5">
                            <p:inputText value="#{mgrbean.supportHealth.guidCrpts}" style="width:180px;"
                                         maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                         onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                         onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                            <p:outputLabel value="#{mgrbean.supportHealth.guidCrpts}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                        </p:column>
                    </p:row>
                </p:panelGrid>
            </c:if>
            <c:if test="#{null == supportInstVO1.simpleCode.extendS1 or supportInstVO1.simpleCode.extendS1 ne '1'}">
                <c:if test="#{not supportInstVO1.onlyLevel2}">
                    <p:outputPanel>
                        <p:panelGrid styleClass="inst_view_table" style="width: 100%;">
                            <c:forEach items="#{supportInstVO1.childList}" varStatus="var2" var="supportInstVO2">
                                <c:if test="#{not empty supportInstVO2.childList}">
                                    <p:row>
                                        <p:column colspan="2" style="border-left: none;border-right: none;">
                                            <p:outputPanel styleClass="cs-flex cs-flex-ai-center" style="height: 36px;">
                                                <p:outputPanel styleClass="blue_vertical"/>
                                                <p:outputLabel value="#{supportInstVO2.simpleCode.codeName}"/>
                                            </p:outputPanel>
                                        </p:column>
                                    </p:row>
                                    <c:forEach items="#{supportInstVO2.childList}" varStatus="var3" var="supportInstVO3">
                                        <c:if test="#{not empty supportInstVO3.childList}">
                                            <p:row>
                                                <p:column style="height: 36px;width: 36px;text-align: center;">
                                                    <p:outputLabel value="#{supportInstVO3.num}"/>
                                                </p:column>
                                                <p:column style="background-color: #dfeffc;">
                                                    <p:outputPanel styleClass="cs-flex cs-flex-ai-center"
                                                                   style="height: 100%;width: 100%;">
                                                        <p:outputLabel style="padding-left: 6px;"
                                                                       value="#{supportInstVO3.simpleCode.codeName}"/>
                                                    </p:outputPanel>
                                                </p:column>
                                            </p:row>
                                            <c:forEach items="#{supportInstVO3.childList}" varStatus="var4"
                                                       var="supportInstVO4">
                                                <p:row>
                                                    <p:column style="height: 36px;width: 36px;text-align: center;">
                                                        <p:outputLabel value="#{supportInstVO4.num}"/>
                                                    </p:column>
                                                    <p:column>
                                                        <p:outputPanel styleClass="cs-flex cs-flex-ai-center"
                                                                       style="height: 100%;width: 100%;">
                                                            <p:selectBooleanCheckbox rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                    id="selCapacity#{var1.index}-#{var2.index}-#{var3.index}-#{var4.index}"
                                                                    style="padding-left: 36px;"
                                                                    value="#{supportInstVO4.sel}"/>
                                                            <p:outputLabel
                                                                    for="selCapacity#{var1.index}-#{var2.index}-#{var3.index}-#{var4.index}"
                                                                    style="padding-left: 6px;" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                    value="#{supportInstVO4.simpleCode.codeName}"/>
                                                            <p:outputLabel
                                                                    style="padding-left: 6px;" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}"
                                                                    value="#{supportInstVO4.simpleCode.codeName}"/>
                                                        </p:outputPanel>
                                                    </p:column>
                                                </p:row>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="#{empty supportInstVO3.childList}">
                                            <p:row>
                                                <p:column style="height: 36px;width: 36px;text-align: center;">
                                                    <p:outputLabel value="#{supportInstVO3.num}"/>
                                                </p:column>
                                                <p:column>
                                                    <p:outputPanel styleClass="cs-flex cs-flex-ai-center">
                                                        <p:selectBooleanCheckbox rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                id="selCapacity#{var1.index}-#{var2.index}-#{var3.index}"
                                                                style="padding-left: 6px;"
                                                                value="#{supportInstVO3.sel}"/>
                                                        <p:outputLabel for="selCapacity#{var1.index}-#{var2.index}-#{var3.index}"
                                                                       style="padding-left: 6px;" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                       value="#{supportInstVO3.simpleCode.codeName}"/>
                                                        <p:outputLabel style="padding-left: 6px;" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}"
                                                                       value="#{supportInstVO3.simpleCode.codeName}"/>
                                                    </p:outputPanel>
                                                </p:column>
                                            </p:row>
                                        </c:if>
                                    </c:forEach>
                                </c:if>

                                <c:if test="#{null ne supportInstVO2.simpleCode.extendS1 and supportInstVO2.simpleCode.extendS1 == '2'}">
                                    <c:if test="#{empty supportInstVO2.childList}">
                                        <p:row>
                                            <p:column colspan="2" style="border-left: none;border-right: none;">
                                                <p:outputPanel styleClass="cs-flex cs-flex-ai-center" style="height: 36px;">
                                                    <p:outputPanel styleClass="blue_vertical"/>
                                                    <p:outputLabel value="#{supportInstVO2.simpleCode.codeName}"/>
                                                </p:outputPanel>
                                            </p:column>
                                        </p:row>
                                    </c:if>
                                    <p:row>
                                        <p:column colspan="2" >
                                            <p:panelGrid style="width: 100%;" styleClass="spc_view_table">
                                                <p:row>
                                                    <p:column style="height: 36px;width: 15%;text-align: right; padding-right: 5px;">
                                                        <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                                                        <p:outputLabel value="收治病例（人）："/>
                                                    </p:column>
                                                    <p:column style="width: 15%;text-align: left; padding-left: 5px;">
                                                        <p:inputText value="#{mgrbean.supportHealth.totalNum}" style="width:180px;"
                                                                     maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                     onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                                                     onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                                                        <p:outputLabel value="#{mgrbean.supportHealth.totalNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                                                    </p:column>
                                                    <p:column style="width: 15%;text-align: right; padding-right: 5px;">
                                                        <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                                                        <p:outputLabel value="治愈（例）："/>
                                                    </p:column>
                                                    <p:column style="text-align: left; padding-left: 5px;">
                                                        <p:inputText value="#{mgrbean.supportHealth.cureNum}" style="width:180px;"
                                                                     maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                     onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                                                     onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                                                        <p:outputLabel value="#{mgrbean.supportHealth.cureNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                                                    </p:column>
                                                </p:row>
                                                <p:row>
                                                    <p:column style="height: 36px;width: 15%;text-align: right; padding-right: 5px;">
                                                        <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                                                        <p:outputLabel value="好转（例）："/>
                                                    </p:column>
                                                    <p:column style="width: 15%;text-align: left; padding-left: 5px;">
                                                        <p:inputText value="#{mgrbean.supportHealth.betterNum}" style="width:180px;"
                                                                     maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                     onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                                                     onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                                                        <p:outputLabel value="#{mgrbean.supportHealth.betterNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                                                    </p:column>
                                                    <p:column style="width: 15%;text-align: right; padding-right: 5px;">
                                                        <p:outputLabel value="*" style="color: red" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"/>
                                                        <p:outputLabel value="死亡（例）："/>
                                                    </p:column>
                                                    <p:column style="text-align: left; padding-left: 5px;">
                                                        <p:inputText value="#{mgrbean.supportHealth.deathNum}" style="width:180px;"
                                                                     maxlength="6" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                     onkeydown="SYSTEM.verifyNum4(this, 6, 0, false)"
                                                                     onkeyup="SYSTEM.verifyNum4(this, 6, 0, false)" onblur="SYSTEM.verifyNum4(this, 6, 0, true)"/>
                                                        <p:outputLabel value="#{mgrbean.supportHealth.deathNum}" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" />
                                                    </p:column>
                                                </p:row>
                                            </p:panelGrid>
                                        </p:column>
                                    </p:row>
                                </c:if>
                            </c:forEach>
                        </p:panelGrid>
                    </p:outputPanel>
                </c:if>
                <c:if test="#{supportInstVO1.onlyLevel2 and (null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0)}">
                    <p:outputPanel>
                        <p:panelGrid style="width: 100%;">
                            <c:forEach items="#{supportInstVO1.childList}" varStatus="var2" var="supportInstVO2">
                                <p:row>
                                    <p:column style="height: 36px;width: 36px;text-align: center;">
                                        <p:outputLabel value="#{supportInstVO2.num}"/>
                                    </p:column>
                                    <p:column>
                                        <p:outputPanel styleClass="cs-flex cs-flex-ai-center">
                                            <p:selectBooleanCheckbox id="selCapacity#{var1.index}-#{var2.index}"
                                                                     style="padding-left: 6px;" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                                     value="#{supportInstVO2.sel}"/>
                                            <p:outputLabel for="selCapacity#{var1.index}-#{var2.index}" style="padding-left: 6px;" rendered="#{null == mgrbean.zwOrginfoEntity.state or mgrbean.zwOrginfoEntity.state == 0}"
                                                           value="#{supportInstVO2.simpleCode.codeName}"/>
                                            <p:outputLabel  style="padding-left: 6px;" rendered="#{null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}"
                                                           value="#{supportInstVO2.simpleCode.codeName}"/>
                                        </p:outputPanel>
                                    </p:column>
                                </p:row>
                            </c:forEach>
                        </p:panelGrid>
                    </p:outputPanel>
                </c:if>
                <c:if test="#{supportInstVO1.onlyLevel2 and null ne mgrbean.zwOrginfoEntity.state and mgrbean.zwOrginfoEntity.state == 1}" >
                    <p:outputPanel>
                        <p:panelGrid styleClass="inst_view_table" style="width: 100%;" rendered="#{supportInstVO1.ifChildSpec}">
                            <c:forEach items="#{supportInstVO1.childList}" varStatus="var2" var="supportInstVO2">
                                <c:if test="#{null == supportInstVO2.simpleCode.extendS1 or supportInstVO2.simpleCode.extendS1 ne '2'}">
                                    <p:row>
                                        <p:column style="height: 36px;width: 36px;text-align: center;">
                                            <p:outputLabel value="#{supportInstVO2.num}"/>
                                        </p:column>
                                        <p:column>
                                            <p:outputPanel styleClass="cs-flex cs-flex-ai-center">
                                                <p:outputLabel style="padding-left: 6px;"
                                                               value="#{supportInstVO2.simpleCode.codeName}"/>
                                            </p:outputPanel>
                                        </p:column>
                                    </p:row>
                                </c:if>
                                <c:if test="#{null ne supportInstVO2.simpleCode.extendS1 and supportInstVO2.simpleCode.extendS1 == '2'}">
                                    <p:row>
                                        <p:column colspan="2" style="border-left: none;border-right: none;">
                                            <p:outputPanel styleClass="cs-flex cs-flex-ai-center" style="height: 36px;">
                                                <p:outputPanel styleClass="blue_vertical"/>
                                                <p:outputLabel value="#{supportInstVO2.simpleCode.codeName}"/>
                                            </p:outputPanel>
                                        </p:column>
                                    </p:row>
                                    <p:row>
                                        <p:column colspan="2" >
                                            <p:panelGrid style="width: 100%;" styleClass="spc_view_table">
                                                <p:row>
                                                    <p:column style="height: 36px;width: 15%;text-align: right; padding-right: 5px;">
                                                        <p:outputLabel value="收治病例（人）："/>
                                                    </p:column>
                                                    <p:column style="width: 15%;text-align: left; padding-left: 5px;">
                                                        <p:outputLabel value="#{mgrbean.supportHealth.totalNum}"  />
                                                    </p:column>
                                                    <p:column style="width: 15%;text-align: right; padding-right: 5px;">
                                                        <p:outputLabel value="治愈（例）："/>
                                                    </p:column>
                                                    <p:column style="text-align: left; padding-left: 5px;">
                                                        <p:outputLabel value="#{mgrbean.supportHealth.cureNum}"  />
                                                    </p:column>
                                                </p:row>
                                                <p:row>
                                                    <p:column style="height: 36px;width: 15%;text-align: right; padding-right: 5px;">
                                                        <p:outputLabel value="好转（例）："/>
                                                    </p:column>
                                                    <p:column style="width: 15%;text-align: left; padding-left: 5px;">
                                                        <p:outputLabel value="#{mgrbean.supportHealth.betterNum}"  />
                                                    </p:column>
                                                    <p:column style="width: 15%;text-align: right; padding-right: 5px;">
                                                        <p:outputLabel value="死亡（例）："/>
                                                    </p:column>
                                                    <p:column style="text-align: left; padding-left: 5px;">
                                                        <p:outputLabel value="#{mgrbean.supportHealth.deathNum}"  />
                                                    </p:column>
                                                </p:row>
                                            </p:panelGrid>
                                        </p:column>
                                    </p:row>
                                </c:if>
                            </c:forEach>
                        </p:panelGrid>

                        <p:panelGrid style="width: 100%;" rendered="#{!supportInstVO1.ifChildSpec}">
                            <c:forEach items="#{supportInstVO1.childList}" varStatus="var2" var="supportInstVO2">
                                <p:row>
                                    <p:column style="height: 36px;width: 36px;text-align: center;">
                                        <p:outputLabel value="#{supportInstVO2.num}"/>
                                    </p:column>
                                    <p:column>
                                        <p:outputPanel styleClass="cs-flex cs-flex-ai-center">
                                            <p:outputLabel style="padding-left: 6px;"
                                                           value="#{supportInstVO2.simpleCode.codeName}"/>
                                        </p:outputPanel>
                                    </p:column>
                                </p:row>
                            </c:forEach>
                        </p:panelGrid>
                    </p:outputPanel>
                </c:if>
            </c:if>
        </p:fieldset>
    </c:forEach>
</ui:composition>