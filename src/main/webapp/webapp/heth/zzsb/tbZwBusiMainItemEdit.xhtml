<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!-- 标题栏 -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.OrgZzWarningConfigListBean"-->
    <ui:param name="onfocus" value="false"/>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="业务范围与重点、一般检测项目关系维护"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky"  style="display:flex;">
            <h:panelGrid columns="4"
                         style="border-color:transparent;padding:0;" id="headButton">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" update=":tabView"
                                 immediate="true" >
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <p:panelGrid styleClass="cs-w-full cs-h-full cs-my-5">
            <p:row>
                <p:column styleClass="cs-scl-first" style="padding-right:3px;width:200px;">
                    <h:outputText value="业务范围大类："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.bigName}"/>
                </p:column>
            </p:row>
            <p:row>
                <p:column styleClass="cs-scl-first" >
                    <h:outputText value="业务范围小类："/>
                </p:column>
                <p:column styleClass="cs-scv">
                    <h:outputText value="#{mgrbean.simpleName}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:commandButton value="添加重点检测项目" icon="ui-icon-plus"
                         style="margin-bottom: 5px;"
                         action="#{mgrbean.addItemAction}" process="@form" update=":tabView">
            <f:setPropertyActionListener target="#{mgrbean.type}" value="1"/>
            <p:ajax event="dialogReturn" listener="#{mgrbean.onItemSelect}" process="@this"  resetValues="true"/>
        </p:commandButton>
        <p:dataTable id="emphasisTable" value="#{mgrbean.emphasisBusiMainItem}" var="itm"
                     paginator="true"
                     rows="10" paginatorPosition="bottom" style="width:100%;padding-bottom: 10px;height: auto"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} "
                     rowsPerPageTemplate="#{'10,20,50'}" pageLinks="5" lazy="true"
                     emptyMessage="没有您要找的记录！"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                     rowIndexVar="R">
            <p:columnGroup type="header">
                <p:row>
                    <p:column headerText="序号" style="width:50px;text-align: center;"/>
                    <p:column headerText="检测项目" style="width:400px;text-align: center"/>
                    <p:column headerText="操作" style="text-align: center;"/>
                </p:row>
            </p:columnGroup>
            <p:column style="text-align:center;padding-left:5px;height:20px;">
                <h:outputText value="#{R+1}" style="width: 30px;text-align: center;"/>
            </p:column>
            <p:column style="padding-left:5px;height:25px;">
                <h:outputText value="#{itm.fkByItemId.codeName}"/>
            </p:column>
            <p:column style="padding-left:5px;">
                <p:commandLink value="删除" process="@this" onclick="PF('DeleteDialog').show();">
                    <f:setPropertyActionListener target="#{mgrbean.type}" value="1"/>
                    <f:setPropertyActionListener target="#{mgrbean.busiId}" value="#{itm.rid}"/>
                </p:commandLink>
            </p:column>
        </p:dataTable>
        <p:commandButton value="添加一般检测项目" icon="ui-icon-plus"
                         style="margin-bottom: 5px;"
                         action="#{mgrbean.addItemAction}" process="@form" update=":tabView">
            <f:setPropertyActionListener target="#{mgrbean.type}" value="2"/>
            <p:ajax event="dialogReturn" listener="#{mgrbean.onItemSelect}" process="@this"  resetValues="true"/>
        </p:commandButton>
        <p:dataTable id="ordinaryTable" value="#{mgrbean.ordinaryBusiMainItem}" var="itm"
                     paginator="true"
                     rows="10" paginatorPosition="bottom" style="width:100%;height: auto"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} "
                     rowsPerPageTemplate="#{'10,20,50'}" pageLinks="5" lazy="true"
                     emptyMessage="没有您要找的记录！"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                     rowIndexVar="R">
            <p:columnGroup type="header">
                <p:row>
                    <p:column headerText="序号" style="width:50px;text-align: center;"/>
                    <p:column headerText="检测项目" style="width:400px;text-align: center"/>
                    <p:column headerText="操作" style="text-align: center;"/>
                </p:row>
            </p:columnGroup>
            <p:column style="text-align:center;padding-left:5px;height:20px;">
                <h:outputText value="#{R+1}" style="width: 30px;text-align: center;"/>
            </p:column>
            <p:column style="padding-left:5px;height:25px;">
                <h:outputText value="#{itm.fkByItemId.codeName}"/>
            </p:column>
            <p:column style="padding-left:5px;">
                <p:commandLink value="删除" process="@this" onclick="PF('DeleteDialog').show();">
                    <f:setPropertyActionListener target="#{mgrbean.type}" value="2"/>
                    <f:setPropertyActionListener target="#{mgrbean.busiId}" value="#{itm.rid}"/>
                </p:commandLink>
            </p:column>
        </p:dataTable>

        <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="DeleteDialog">
            <p:commandButton value="确定" action="#{mgrbean.delItemAction}" icon="ui-icon-check"
                             oncomplete="PF('DeleteDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('DeleteDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
    </ui:define>

</ui:composition>