<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core">
    <p:dataTable var="outCarItm" value="#{mgrbean.tjorgOutcarZzsbList}"
                 id="outCarDatatable"
                 emptyMessage="没有您要找的记录！" paginator="true" rows="#{mgrbean.pageSize}" paginatorPosition="bottom" rowIndexVar="R"
                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                 rowsPerPageTemplate="#{mgrbean.pageSize}"  lazy="true"
                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                 style="margin-top:5px;width:100%">
        <p:column headerText="车牌号" style="padding-left: 3px;text-align:center;width:10%;">
            <p:outputLabel value="#{outCarItm.carNo}"/>
        </p:column>
        <p:column headerText="品牌" style="text-align:center;width:10%;">
            <p:outputLabel value="#{outCarItm.carBand}"/>
        </p:column>
        <p:column headerText="型号" style="text-align:center;width:10%;">
            <p:outputLabel value="#{outCarItm.carStyle}"/>
        </p:column>
        <p:column headerText="车架号" style="text-align:center;width:15%;">
            <p:outputLabel value="#{outCarItm.carFrameNo}"/>
        </p:column>
        <p:column headerText="#{mgrbean.outCarVersion != '1'?'出厂':'购置'}日期" style="text-align:center;width:10%;">
            <p:outputLabel value="#{outCarItm.productionDate}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </p:outputLabel>
        </p:column>
        <p:column headerText="配套仪器" style="width:20%;" >
            <!-- 如果缺少id instNamesId 不生效 -->
            <h:outputLabel id="instNamesId"
                           value="#{outCarItm.instNames}"
                           style="cursor: pointer;text-align: left; word-break:break-all;text-overflow: ellipsis;display: -webkit-box; -webkit-box-orient: vertical;-webkit-line-clamp: 2;overflow: hidden;color:#25AAE1">
                <p:tooltip for="@parent" value="#{outCarItm.instNames}"  style="width:300px;word-break:break-all;word-wrap:break-word;" />
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="text-align:center;width:5%;" >
            <p:outputLabel value="启用" rendered="#{null ne outCarItm.stateMark and 1 == outCarItm.stateMark}"/>
            <p:outputLabel value="停用" rendered="#{null ne outCarItm.stateMark and 0 == outCarItm.stateMark}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left:5px;" rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
            <p:commandLink value="修改" process="@this"
                           action="#{mgrbean.modOutcar}" update=":mainForm:outCarEditDialog">
                <f:setPropertyActionListener target="#{mgrbean.modOutcarZzsb}" value="#{outCarItm}"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" process="@this" update=":mainForm:tabView:outCarDatatable"
                           action="#{mgrbean.delOutcar}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert" />
                <f:setPropertyActionListener target="#{mgrbean.outcarZzsb}" value="#{outCarItm}"/>
            </p:commandLink>
        </p:column>
    </p:dataTable>
</ui:composition>