<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwOrgInfoBaseZzsbBean"-->
    <h:outputScript name="js/datatable.js"/>
    <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;" id="supportTrcGrid">
        <c:if test="#{mgrbean.zwOrginfoEntity.state ne 1}">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:200px;height: 25px;">
                <p:outputLabel value="省部级技术研究中心：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.supportTrc.ifTrc}"  style="width:120px;">
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                <p:outputLabel value="职业健康人才培训基地：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.supportTrc.ifTjTrain}"  style="width:120px;">
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <p:ajax event="change" process="@this,supportTrcGrid" update="supportTrcGrid"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;height:  31px;#{mgrbean.supportTrc.ifTjTrain eq 1?'':'display: none;'}">
                <p:outputLabel value="培训人员数量（人）：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{mgrbean.supportTrc.ifTjTrain eq 1?'':'display: none;'}" colspan="3">
                <p:inputText value="#{mgrbean.supportTrc.tjTrainNum}" maxlength="6" styleClass="cs-w-180"
                             onkeyup="SYSTEM.verifyNumClear0(this)" onblur="SYSTEM.verifyNumClear0(this)"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                <p:outputLabel value="职业健康技术转化基地：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.supportTrc.ifTtb}"  style="width:120px;">
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <p:ajax event="change" process="@this,supportTrcGrid" update="supportTrcGrid"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:  31px;#{mgrbean.supportTrc.ifTtb eq 1?'':'display: none;'}">
                <p:outputLabel value="转化成果（项）：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{mgrbean.supportTrc.ifTtb eq 1?'':'display: none;'}" colspan="3">
                <p:inputText value="#{mgrbean.supportTrc.tranNum}" maxlength="6" styleClass="cs-w-180"
                             onkeyup="SYSTEM.verifyNumClear0(this)" onblur="SYSTEM.verifyNumClear0(this)"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                <p:outputLabel value="职业暴露人群生物标本库平台：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.supportTrc.ifExpose}"  style="width:120px;">
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                <p:outputLabel value="职业病诊断救治人才培训基地：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.supportTrc.ifDiagTrain}"  style="width:120px;">
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <p:ajax event="change" process="@this,supportTrcGrid" update="supportTrcGrid"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;height:  31px;#{mgrbean.supportTrc.ifDiagTrain eq 1?'':'display: none;'}">
                <p:outputLabel value="培训人员数量（人）：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{mgrbean.supportTrc.ifDiagTrain eq 1?'':'display: none;'}" colspan="3">
                <p:inputText value="#{mgrbean.supportTrc.diagNum}" maxlength="6" styleClass="cs-w-180"
                             onkeyup="SYSTEM.verifyNumClear0(this)" onblur="SYSTEM.verifyNumClear0(this)"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                <p:outputLabel value="职业病诊断救治技术研究中心：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.supportTrc.ifDiagTrc}"  style="width:120px;">
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                <p:outputLabel value="基层科研技术指导：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.supportTrc.ifScienGuid}"  style="width:120px;">
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <p:ajax event="change" process="@this,supportTrcGrid" update="supportTrcGrid"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;height:  31px;#{mgrbean.supportTrc.ifScienGuid eq 1?'':'display: none;'}">
                <p:outputLabel value="开展基层技术指导（次）：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:260px;#{mgrbean.supportTrc.ifScienGuid eq 1?'':'display: none;'}" >
                <p:inputText value="#{mgrbean.supportTrc.guidNum}" maxlength="6" styleClass="cs-w-180"
                             onkeyup="SYSTEM.verifyNumClear0(this)" onblur="SYSTEM.verifyNumClear0(this)"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:200px;#{mgrbean.supportTrc.ifScienGuid eq 1?'':'display: none;'}">
                <p:outputLabel value="培训（人）：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{mgrbean.supportTrc.ifScienGuid eq 1?'':'display: none;'}" >
                <p:inputText value="#{mgrbean.supportTrc.scienTrainNum}" maxlength="6" styleClass="cs-w-180"
                             onkeyup="SYSTEM.verifyNumClear0(this)" onblur="SYSTEM.verifyNumClear0(this)"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height: 25px;">
                <p:outputLabel value="高校科研实践基地：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.supportTrc.ifUniversity}"  style="width:120px;">
                    <f:selectItem itemValue="0" itemLabel="否"></f:selectItem>
                    <f:selectItem itemValue="1" itemLabel="是"></f:selectItem>
                    <p:ajax event="change" process="@this,supportTrcGrid" update="supportTrcGrid"/>
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;height:  31px;#{mgrbean.supportTrc.ifUniversity eq 1?'':'display: none;'}">
                <p:outputLabel value="高校实习（人）：" styleClass="cs-required" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{mgrbean.supportTrc.ifUniversity eq 1?'':'display: none;'}" colspan="3">
                <p:inputText value="#{mgrbean.supportTrc.universityNum}"  maxlength="6" styleClass="cs-w-180"
                             onkeyup="SYSTEM.verifyNumClear0(this)" onblur="SYSTEM.verifyNumClear0(this)"/>
            </p:column>
        </p:row>
        </c:if>

        <c:if test="#{mgrbean.zwOrginfoEntity.state eq 1}">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:200px;height:  31px;">
                <p:outputLabel value="省部级技术研究中心："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.supportTrc.ifTrc eq 0}" />
                <p:outputLabel value="是" rendered="#{mgrbean.supportTrc.ifTrc eq 1}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="职业健康人才培训基地："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.supportTrc.ifTjTrain eq 0}" />
                <p:outputLabel value="是" rendered="#{mgrbean.supportTrc.ifTjTrain eq 1}" />
            </p:column>
        </p:row>
        <p:row rendered = "#{mgrbean.supportTrc.ifTjTrain eq 1}">
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="培训人员数量（人）："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.supportTrc.tjTrainNum}"  />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="职业健康技术转化基地："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.supportTrc.ifTtb eq 0}" />
                <p:outputLabel value="是" rendered="#{mgrbean.supportTrc.ifTtb eq 1}" />
            </p:column>
        </p:row>
        <p:row rendered = "#{mgrbean.supportTrc.ifTtb eq 1}">
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="转化成果（项）："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.supportTrc.tranNum}"  />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="职业暴露人群生物标本库平台："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.supportTrc.ifExpose eq 0}" />
                <p:outputLabel value="是" rendered="#{mgrbean.supportTrc.ifExpose eq 1}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="职业病诊断救治人才培训基地："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.supportTrc.ifDiagTrain eq 0}" />
                <p:outputLabel value="是" rendered="#{mgrbean.supportTrc.ifDiagTrain eq 1}" />
            </p:column>
        </p:row>
        <p:row rendered = "#{mgrbean.supportTrc.ifDiagTrain eq 1}">
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="培训人员数量（人）："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.supportTrc.diagNum}"  />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="职业病诊断救治技术研究中心："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.supportTrc.ifDiagTrc eq 0}" />
                <p:outputLabel value="是" rendered="#{mgrbean.supportTrc.ifDiagTrc eq 1}" />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="基层科研技术指导："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.supportTrc.ifScienGuid eq 0}" />
                <p:outputLabel value="是" rendered="#{mgrbean.supportTrc.ifScienGuid eq 1}" />
            </p:column>
        </p:row>
        <p:row rendered = "#{mgrbean.supportTrc.ifScienGuid eq 1}">
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="开展基层技术指导（次）："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width:260px;" >
                <p:outputLabel value="#{mgrbean.supportTrc.guidNum}"  />
            </p:column>
            <p:column style="text-align:right;width:200px;padding-right:3px;">
                <p:outputLabel value="培训（人）："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" >
                <p:outputLabel value="#{mgrbean.supportTrc.scienTrainNum}"  />
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="高校科研实践基地："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.supportTrc.ifUniversity eq 0}" />
                <p:outputLabel value="是" rendered="#{mgrbean.supportTrc.ifUniversity eq 1}" />
            </p:column>
        </p:row>
        <p:row rendered = "#{mgrbean.supportTrc.ifUniversity eq 1}">
            <p:column style="text-align:right;padding-right:3px;height:  31px;">
                <p:outputLabel value="高校实习（人）："  />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.supportTrc.universityNum}"  />
            </p:column>
        </p:row>
        </c:if>
    </p:panelGrid>

</ui:composition>