<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwQualAgencyFilingAppBean"-->
    <ui:param name="onfocus" value="1" />
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构备案申请"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <!-- 保存、提交以及撤销后需要查询并更新列表 -->
                <p:commandButton value="保存" icon="ui-icon-disk" id="saveBtn" action="#{mgrbean.applyInfoSave}" rendered="#{!mgrbean.applyInfoBaseBean.ifView and mgrbean.applyInfoBaseBean.tjorginfoApply.state == 0}"
                                 process="@this,:tabView:editForm" update=":tabView" />
                <p:commandButton value="提交" icon="ui-icon-check" rendered="#{!mgrbean.applyInfoBaseBean.ifView and mgrbean.applyInfoBaseBean.tjorginfoApply.state == 0}" id="submitBtn"
                                 update=":tabView"
                                 action="#{mgrbean.applyInfoSubmit}" process="@this,:tabView:editForm">
                    <p:confirm header="消息确认框" message="确定要提交吗？" icon="ui-icon-alert" />
                </p:commandButton>
                <p:commandButton value="添加人员" icon="ui-icon-plus"
                                 rendered="#{!mgrbean.applyInfoBaseBean.ifView and mgrbean.applyInfoBaseBean.tabTitle == '人员信息'}"
                                 process="@this" action="#{mgrbean.applyInfoBaseBean.addPsnInfoAction}"
                                 update=":tabView:editForm:orgTjApplyTabView:psnEditDialog" oncomplete="PF('PsnEditDialog').show();">
                </p:commandButton>
                <p:commandButton value="添加 / 修改" icon="ui-icon-plus"
                                 rendered="#{!mgrbean.applyInfoBaseBean.ifView and mgrbean.applyInfoBaseBean.tabTitle == '仪器信息'}"
                                 process="@this" action="#{mgrbean.applyInfoBaseBean.initInstInfoListAction}"
                                 update=":tabView:editForm:orgTjApplyTabView:orgInstInfoDialog" oncomplete="PF('OrgInstInfoDialog').show();">
                </p:commandButton>
                <!-- rendered加入 状态等于1 -->
                <p:commandButton value="撤销" icon="ui-icon-cancel" id="cancelBtn" action="#{mgrbean.applyInfoCancel}"
                                 rendered="#{mgrbean.applyInfoBaseBean.ifView and mgrbean.applyInfoBaseBean.tjorginfoApply.state == 1}"
                                 process="@this" update=":tabView" >
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert" />
                </p:commandButton>
                <!--检查项目添加状态等于0 mgrbean.applyInfoBaseBean.tabTitle == '检查项目' and -->
                <p:commandButton rendered="#{mgrbean.applyInfoBaseBean.tabTitle == '检查项目' and !mgrbean.applyInfoBaseBean.ifView}"
                                 value="选择项目" icon="ui-icon-plus" process="@this" update=":tabView:editForm"
                                 action="#{mgrbean.applyInfoBaseBean.addItemInitAction}"  oncomplete="PF('CheckItemDialog').show();" >
                </p:commandButton>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addOutCarBtn" action="#{mgrbean.applyInfoBaseBean.addOutCarDialog}"
                                 rendered="#{mgrbean.applyInfoBaseBean.tabTitle == '外检车辆' and !mgrbean.applyInfoBaseBean.ifView and mgrbean.applyInfoBaseBean.tjorginfoApply.state == 0}"
                                 process="@this,:tabView:editForm" update=":tabView" />
                <p:commandButton value="查看备案回执" icon="ui-icon-zoomin" process="@this,:tabView:editForm"
                                 rendered="#{null ne mgrbean.applyInfoBaseBean.reportReceipt and null ne mgrbean.applyInfoBaseBean.reportReceipt.filePath and mgrbean.applyInfoBaseBean.ifView}"
                                 oncomplete="window.open('/webFile/#{mgrbean.applyInfoBaseBean.reportReceipt.filePath}');"/>

                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.backAction}"
                                 update=":tabView"  />
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 编辑页面的内容-->
    <ui:define name="insertOtherContents">
        <ui:include src="tdZwQualTjOrgApplyInfo.xhtml">
            <ui:param name="updateId" value=":tabView:editForm" />
            <ui:param name="updateName" value="tabView:editForm" />
            <ui:param name="orgApplyBean" value="#{mgrbean.applyInfoBaseBean}" />
        </ui:include>
        <ui:include src="tdZwQualDiagOrgApplyInfo.xhtml">
            <ui:param name="updateId" value=":tabView:editForm" />
            <ui:param name="updateName" value="tabView:editForm" />
            <ui:param name="orgApplyBean" value="#{mgrbean.applyInfoBaseBean}" />
        </ui:include>
        <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId" resizable="false" modal="true" width="800"  >
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要上传的文件！" multiple="true"
                                      label="文件选择" fileUploadListener="#{mgrbean.applyInfoBaseBean.handleFileUpload}"
                                      invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，请重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                      allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                                      style="width:768px;" previewWidth="120"
                                      process="@this" update="@this,:tabView:editForm:orgTjApplyTabView:applyAnnexTable"
                                      cancelLabel="取消" uploadLabel="上传" dragDropSupport="true" mode="advanced"
                                      sizeLimit="10485760" />
                    </td>
                </tr>
            </table>
        </p:dialog>
    </ui:define>
</ui:composition>