<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!-- 标题栏 -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.OrgZzWarningConfigListBean"-->
    <ui:param name="onfocus" value="false"/>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构预警配置"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="4"
                         style="border-color:transparent;padding:0;" id="headButton">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="保存" icon="ui-icon-disk"
                                 action="#{mgrbean.saveAction}"
                                 update=":tabView:editForm:postTables,:tabView:editForm:specialPostTables"
                                 process="@this,:tabView:editForm"
                                 onclick="zwx_loading_start()" oncomplete="zwx_loading_stop()"/>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" update=":tabView"
                                 immediate="true" oncomplete="mergeCells();">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <p:fieldset legend="#{1 eq mgrbean.type?'人员配置基本要求':'基本设备设施要求'}" toggleable="true"
                    toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:commandButton value="#{1 eq mgrbean.type?'添加岗位':'添加'}" icon="ui-icon-plus"
                             style="margin-bottom: 10px;"
                             action="#{mgrbean.addPsnAction}" process="@form" update=":tabView">
                <c:if test="#{1 eq mgrbean.type}">
                    <f:setPropertyActionListener target="#{mgrbean.codeNo}" value="5620"/>
                </c:if>
                <c:if test="#{2 eq mgrbean.type}">
                    <f:setPropertyActionListener target="#{mgrbean.codeNo}" value="5024"/>
                </c:if>
                <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                        process="@this" update=":tabView:editForm:postTables" resetValues="true"/>
            </p:commandButton>
            <p:dataTable id="postTables" value="#{mgrbean.orgWarnConfigList}" var="itm"
                         paginator="true"
                         rows="10" paginatorPosition="bottom" style="width:100%;height: auto"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} "
                         rowsPerPageTemplate="#{'10,20,50'}" pageLinks="5" lazy="true"
                         emptyMessage="没有您要找的记录！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         rowIndexVar="R">
                <p:columnGroup type="header">
                    <p:row rendered="#{1 eq mgrbean.type}">
                        <p:column headerText="#{mgrbean.fontcolor}序号" style="width:50px;text-align: center;"/>
                        <p:column headerText="人员岗位" style="width:200px;text-align: center"/>
                        <p:column headerText="#{mgrbean.fontcolor}人数最低要求" style="width:150px;text-align: center"/>
                        <p:column headerText="培训周期（年）" style="width:150px;text-align: center"/>
                        <p:column headerText="职称级别最低要求" style="width:150px;text-align: center"/>
                        <p:column headerText="执业机构上限（家）" style="width:150px;text-align: center"/>
                        <p:column headerText="操作" style="text-align: center;"/>
                    </p:row>
                    <p:row rendered="#{2 eq mgrbean.type}">
                        <p:column headerText="#{mgrbean.fontcolor}序号" style="width:50px;text-align: center;"/>
                        <p:column headerText="仪器类型" style="width:200px;text-align: center"/>
                        <p:column headerText="数量最低要求" style="width:150px;text-align: center"/>
                        <p:column headerText="检定仪器" style="width:150px;text-align: center"/>
                        <p:column headerText="操作" style="text-align: center;"/>
                    </p:row>
                </p:columnGroup>
                <p:column style="text-align:center;padding-left:5px;height:20px;">
                    <p:inputText value="#{itm.xh}" maxlength="5" style="width: 30px;text-align: center;"
                                 onkeydown="SYSTEM.verifyNum3(this, 3, 0, false)"
                                 onkeyup="SYSTEM.verifyNum3(this, 3, 0, false)"
                                 onblur="SYSTEM.verifyNum3(this, 3, 0, true)"/>
                </p:column>
                <p:column style="padding-left:5px;height:25px;">
                    <h:outputText value="#{1 eq mgrbean.type?itm.fkByPostId.codeName:itm.fkByInstTypeId.codeName}"/>
                </p:column>
                <p:column style="text-align:center;padding-left:5px;height:25px;">
                    <p:inputText value="#{itm.minNums}" maxlength="6" style="text-align: center;"
                                 onkeydown="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"
                                 onkeyup="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"
                                 onblur="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"/>
                </p:column>
                <p:column style="text-align:center;padding-left:5px;height:25px;" rendered="#{1 eq mgrbean.type}">
                    <p:inputText value="#{itm.trainYear}" style="text-align: center;" maxlength="2"
                                 onkeydown="SYSTEM.verifyNumRange(this,2,0,true,1,999999)"
                                 onkeyup="SYSTEM.verifyNumRange(this,2,0,true,1,999999)"
                                 onblur="SYSTEM.verifyNumRange(this,2,0,true,1,999999)"/>
                </p:column>
                <p:column style="text-align:center;padding-left:5px;height:25px;" rendered="#{1 eq mgrbean.type}">
                    <p:selectOneMenu value="#{itm.titleRid}" style="width: 100px;text-align: left;">
                        <f:selectItem itemLabel="请选择" itemValue=""/>
                        <f:selectItems value="#{mgrbean.titleList}" var="item" itemLabel="#{item.codeName}"
                                       itemValue="#{item.rid}"/>
                    </p:selectOneMenu>
                </p:column>
                <p:column style="text-align:center;padding-left:5px;height:25px;" rendered="#{1 eq mgrbean.type}">
                    <p:inputText value="#{itm.orgNums}" style="text-align: center;"
                                 onkeydown="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"
                                 onkeyup="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"
                                 onblur="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"/>
                </p:column>
                <p:column style="text-align:center;padding-left:5px;height:25px;" rendered="#{2 eq mgrbean.type}">
                    <p:outputPanel style="margin-top:5px;text-align: -webkit-center;" id="instPanel">
                        <div style="display: table-row;">
                            <div style="display: table-cell;vertical-align: middle;">
                                <p:selectBooleanCheckbox value="#{itm.ifAppTrue}">
                                    <p:ajax event="change" process="@this,instPanel"
                                            listener="#{mgrbean.changeTestInst(1,itm)}" update="@this,instPanel"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;padding: 0 10px;">
                                <h:outputText value="是"/>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;padding: 0 10px;">
                                <p:selectBooleanCheckbox value="#{itm.ifAppFalse}">
                                    <p:ajax event="change" process="@this,instPanel"
                                            listener="#{mgrbean.changeTestInst(2,itm)}" update="@this,instPanel"/>
                                </p:selectBooleanCheckbox>
                            </div>
                            <div style="display: table-cell;vertical-align: middle;">
                                <h:outputText value="否"/>
                            </div>
                        </div>
                    </p:outputPanel>
                </p:column>
                <p:column style="padding-left:5px;">
                    <p:commandLink value="删除" process="@this" action="#{mgrbean.beforeDelWarnConfig}">
                        <f:setPropertyActionListener target="#{mgrbean.warnConfig}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>
        </p:fieldset>
        <p:fieldset legend="#{1 eq mgrbean.type?'各类职业健康检查岗位人员配置要求':'特殊要求'}" toggleable="true"
                    toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;">
            <p:commandButton value="添加" icon="ui-icon-plus" style="margin-bottom: 10px;" resetValues="true"
                             action="#{mgrbean.addSpecialPostAction}" process="@form" update=":tabView">
            </p:commandButton>
            <p:dataTable id="specialPostTables" value="#{mgrbean.specialWarnConfigList}" var="itm"
                         paginator="true"
                         rows="10" paginatorPosition="bottom" style="width:100%;height: auto"
                         paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} "
                         rowsPerPageTemplate="#{'10,20,50'}" pageLinks="5" lazy="true"
                         emptyMessage="没有您要找的记录！"
                         currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                         rowIndexVar="R" >
                <p:columnGroup type="header">
                    <p:row>
                        <p:column headerText="#{mgrbean.fontcolor}序号" style="width:50px;text-align: center;"/>
                        <p:column headerText="#{mgrbean.fontcolor}备案类别"
                                  style="width:320px;text-align: center"/>
                        <p:column headerText="#{mgrbean.fontcolor}#{1 eq mgrbean.type?'人员岗位':'仪器类型'}" style="width:300px;text-align: center"/>
                        <p:column headerText="#{mgrbean.fontcolor}#{1 eq mgrbean.type?'人数':'数量'}最低要求" style="width:150px;text-align: center"/>
                        <p:column headerText="操作" style="text-align: center;"/>
                    </p:row>
                </p:columnGroup>
                <p:column style="text-align:center;padding-left:5px;height:20px;">
                    <p:inputText value="#{itm.xh}" maxlength="5" style="width: 30px;text-align: center;"
                                 onkeydown="SYSTEM.verifyNum3(this, 3, 0, false)"
                                 onkeyup="SYSTEM.verifyNum3(this, 3, 0, false)"
                                 onblur="SYSTEM.verifyNum3(this, 3, 0, true)"/>
                </p:column>
                <p:column style="text-align:center;padding-left:5px;height:25px;">
                    <p:selectOneMenu value="#{itm.itemId}" style="width: 300px;text-align: left;">
                        <f:selectItem itemLabel="请选择" itemValue=""/>
                        <f:selectItems value="#{mgrbean.regisTypeList}" var="item" itemLabel="#{item.codeName}"
                                       itemValue="#{item.rid}"/>
                    </p:selectOneMenu>
                </p:column>
                <p:column style="text-align:center;height:25px;">
                    <div class="cs-flex cs-flex-ai-center">
                        <p:inputText id="specialPost" readonly="true" style="width:240px;"
                                     value="#{itm.specialPost}" title="#{itm.specialPost}"
                                     onclick="$('#tabView\\:editForm\\:specialPostTables\\:#{R}\\:onOrgSelect').click()"/>
                        <p:commandLink id="onOrgSelect" styleClass="mysearch-icon ui-icon ui-icon-search"
                                       partialSubmit="true"
                                       action="#{mgrbean.selectSpecialPostAction}"
                                       process="@this,:tabView:editForm:specialPostTables"
                                       style="position: relative;left: -20px;">
                            <c:if test="#{1 eq mgrbean.type}">
                                <f:setPropertyActionListener target="#{mgrbean.codeNo}" value="5620"/>
                            </c:if>
                            <c:if test="#{2 eq mgrbean.type}">
                                <f:setPropertyActionListener target="#{mgrbean.codeNo}" value="5024"/>
                            </c:if>
                            <f:setPropertyActionListener target="#{mgrbean.warnConfig}" value="#{itm}"/>
                            <p:ajax event="dialogReturn" process="@this" resetValues="true"
                                    listener="#{mgrbean.onSpecialPostSelect}"
                                    update=":tabView:editForm:specialPostTables"/>
                        </p:commandLink>
                        <p:commandLink styleClass="ui-icon ui-icon-trash" title="清空" style="margin-left: -10px;"
                                       process="@this" update=":tabView:editForm:specialPostTables"
                                       action="#{mgrbean.clearPost}">
                            <f:setPropertyActionListener target="#{mgrbean.warnConfig}" value="#{itm}"/>
                        </p:commandLink>
                    </div>
                </p:column>
                <p:column style="text-align:center;padding-left:5px;height:25px;">
                    <p:inputText value="#{itm.minNums}" maxlength="6" style="text-align: center;"
                                 onkeydown="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"
                                 onkeyup="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"
                                 onblur="SYSTEM.verifyNumRange(this,6,0,true,1,999999)"/>
                </p:column>
                <p:column style="padding-left:5px;">
                    <p:commandLink value="删除" process="@this" action="#{mgrbean.beforeDelWarnConfig}">
                        <f:setPropertyActionListener target="#{mgrbean.warnConfig}" value="#{itm}"/>
                    </p:commandLink>
                </p:column>
            </p:dataTable>
        </p:fieldset>

        <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="DeleteDialog">
            <p:commandButton value="确定" action="#{mgrbean.delWarnConfig}" icon="ui-icon-check"
                             oncomplete="PF('DeleteDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('DeleteDialog').hide();"
                             type="button"/>
        </p:confirmDialog>

    </ui:define>

</ui:composition>