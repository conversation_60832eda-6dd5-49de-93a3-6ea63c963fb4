<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!-- 标题栏 -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.BusScopeItemConfigBean"-->
    <ui:param name="onfocus" value="false"/>
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="2"
                      style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="业务范围与检测项目关系配置"/>
            </p:column>
        </p:row>
    </ui:define>
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" id="sticky">
            <h:panelGrid columns="4"
                         style="border-color:transparent;padding:0;" id="headButton">
				<span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" update=":tabView"
                                 immediate="true" oncomplete="mergeCells();">
                </p:commandButton>
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 其它内容 -->
    <ui:define name="insertOtherContents">
        <p:panelGrid style="width:100%;height:100%;margin-top:5px;margin-bottom:5px;" styleClass="planGrid"
                     id="planGridRpt">
            <p:row>
                <p:column style="text-align:right;padding-right:3px;height: 38px;width:200px;">
                    <p:outputLabel value="业务范围大类："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <p:outputLabel value="#{mgrbean.busiRelItem[1]}"/>
                </p:column>
            </p:row>
            <p:row rendered="#{null ne mgrbean.busiRelItem[3]}">
                <p:column style="text-align:right;padding-right:3px;height: 38px;width:200px;">
                    <p:outputLabel value="检测项目大类："/>
                </p:column>
                <p:column style="text-align:left;padding-right:3px;">
                    <p:outputLabel value="#{mgrbean.busiRelItem[3]}"/>
                </p:column>
            </p:row>
        </p:panelGrid>

        <p:commandButton value="添加" icon="ui-icon-plus"
                         style="margin-bottom: 5px;"
                         action="#{mgrbean.addItemAction}" process="@form" update=":tabView">
            <p:ajax event="dialogReturn" listener="#{mgrbean.onSimpleCodeAction}"
                    process="@this" update=":tabView:editForm:itemTables" resetValues="true"/>
        </p:commandButton>
        <p:dataTable id="itemTables" value="#{mgrbean.busiRelItemList}" var="itm"
                     paginator="true"
                     rows="20" paginatorPosition="bottom" style="width:100%;height: auto"
                     paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown} "
                     rowsPerPageTemplate="#{'20,50,100'}" pageLinks="5" lazy="true"
                     emptyMessage="没有您要找的记录！"
                     currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                     rowIndexVar="R">
            <p:columnGroup type="header">
                <p:row>
                    <p:column headerText="序号" style="width:50px;text-align: center;"/>
                    <p:column headerText="检测项目" style="width:400px;text-align: center"/>
                    <p:column headerText="操作" style="text-align: center;"/>
                </p:row>
            </p:columnGroup>
            <p:column style="text-align:center;padding-left:5px;height:20px;">
                <h:outputText value="#{R+1}" style="width: 30px;text-align: center;"/>
            </p:column>
            <p:column style="padding-left:5px;height:25px;">
                <h:outputText value="#{itm.fkByItemId.codeName}"/>
            </p:column>
            <p:column style="padding-left:5px;">
                <p:commandLink value="删除" process="@this" action="#{mgrbean.beforeDelAction}">
                    <f:setPropertyActionListener target="#{mgrbean.relItem}" value="#{itm}"/>
                </p:commandLink>
            </p:column>
        </p:dataTable>

        <p:confirmDialog message="确定要删除吗？" header="消息确认框" widgetVar="DeleteDialog">
            <p:commandButton value="确定" action="#{mgrbean.delItemAction}" icon="ui-icon-check"
                             update=":tabView:editForm:itemTables"
                             oncomplete="PF('DeleteDialog').hide();"/>
            <p:commandButton value="取消" icon="ui-icon-close"
                             onclick="PF('DeleteDialog').hide();"
                             type="button"/>
        </p:confirmDialog>
    </ui:define>
</ui:composition>