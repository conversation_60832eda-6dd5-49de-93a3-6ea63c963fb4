<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <p:dataTable var="dataItm" value="#{mgrbean.showSupportTrainList}"
                 style="margin-top:5px;width:100%"
                 emptyMessage="没有您要找的记录！" id="supportTrainDataId">
        <p:columnGroup type="header">
            <p:row>
                <p:column style="height:20px;text-align:center;width: 60px; " headerText="年份" rowspan="2" />
                <c:forEach var="itm" items="#{mgrbean.showAcademicList}">
                    <c:forEach var="subItm" items="#{itm}" varStatus="subItmStatus">
                        <c:if test="#{subItmStatus.index == 0}">
                            <p:column style="text-align:center;" rowspan="#{itm.size() == 1 ? 2 : 1}" headerText="#{subItm.codeName}#{subItm.extendS3}" colspan="#{itm.size() == 1 ? 1 : itm.size()-1}" />
                        </c:if>
                    </c:forEach>
                </c:forEach>
                <p:column style="text-align:center;width: 100px; " rowspan="2" headerText="操作" rendered="#{mgrbean.zwOrginfoEntity.state == 0}" />
            </p:row>
            <p:row>
                <c:forEach var="itm" items="#{mgrbean.showAcademicList}">
                    <c:forEach var="subItm" items="#{itm}" varStatus="subItmStatus">
                        <c:if test="#{subItmStatus.index != 0}">
                            <p:column style="text-align:center;" headerText="#{subItm.codeName}" />
                        </c:if>
                    </c:forEach>
                </c:forEach>
            </p:row>
        </p:columnGroup>
        <!-- 直接使用dataItm.size()不好使 -->
        <c:forEach begin="0" end="#{mgrbean.academicDataSize}" step="1" varStatus="index">
            <p:column style="text-align:center;height: 35px;">
                <p:outputLabel value="#{dataItm[0].year}" rendered="#{index.index == 0}" />

                <p:commandLink value="#{dataItm[index.index-1].partakeNumber}"
                               rendered="#{index.index ne 0 and dataItm[index.index-1].evidenceFilePath != '' and null != dataItm[index.index-1].evidenceFilePath}"
                               process="@this"  onclick="window.open('/webFile/#{dataItm[index.index-1].evidenceFilePath}')" />
                <p:outputLabel value="#{dataItm[index.index-1].partakeNumber}" rendered="#{index.index ne 0 and (dataItm[index.index-1].evidenceFilePath == '' or null == dataItm[index.index-1].evidenceFilePath)}" />
            </p:column>
        </c:forEach>
        <!-- 加入操作列 -->
        <ui:insert name="supportTrainOperColumn"/>
    </p:dataTable>
</ui:composition>