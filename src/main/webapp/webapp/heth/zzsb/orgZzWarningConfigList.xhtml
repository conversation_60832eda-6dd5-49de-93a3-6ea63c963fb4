<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:p="http://primefaces.org/ui"
      xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
      xmlns:c="http://java.sun.com/jsp/jstl/core">
<f:view contentType="text/html">
    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.OrgZzWarningConfigListBean"-->
    <ui:param name="mgrbean" value="#{orgZzWarningConfigListBean}"/>
    <h:head>
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <script type="text/javascript">
            //<![CDATA[
            function mergeCells() {
                // 获取元素
                var div = $("#tabView\\:mainForm\\:dataTable_data > tr.ui-widget-content.ui-datatable-even > td:nth-child(1)");
                // 添加属性
                div.attr('rowspan', '2');
            }

            //]]>
        </script>
        <style type="text/css">

        </style>
    </h:head>
    <h:body onload="mergeCells();">
        <p:tabView id="tabView" dynamic="true" cache="true"
                   activeIndex="#{mgrbean.activeTab}" style="border:1px; padding:0;">
            <p:tab id="list" title="mainTitle" titleStyle="display:none;">
                <h:form id="mainForm">
                    <h:outputStylesheet name="css/default.css"/>
                    <ui:include src="/WEB-INF/templates/system/loadUserSkin.xhtml"/>
                    <h:outputStylesheet name="css/ui-tabs.css"/>
                    <h:outputStylesheet name="css/ui-cs.css"/>
                    <h:outputScript library="js" name="namespace.js"/>
                    <h:outputScript name="js/validate/system/validate.js"/>
                    <p:panelGrid style="width:100%;height:100%;margin-bottom:5px;" id="titleGrid">
                        <f:facet name="header">
                            <p:row>
                                <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                                    <p:outputLabel value="资质机构预警配置"/>
                                </p:column>
                            </p:row>
                        </f:facet>
                    </p:panelGrid>
                    <p:dataTable var="itm" value="#{mgrbean.dataModel}"
                                 paginator="true" rows="#{mgrbean.pageSize}"
                                 paginatorPosition="bottom" rowIndexVar="R"
                                 paginatorTemplate="{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink}"
                                 rowsPerPageTemplate="#{mgrbean.pageSize}" id="dataTable"
                                 lazy="true" emptyMessage="没有您要找的记录！"
                                 currentPageReportTemplate="查询到{totalRecords}条记录，共{totalPages}页 第{currentPage}页"
                                 rendered="#{dataTable==null}">
                        <p:columnGroup type="header">
                            <p:row>
                                <p:column headerText="资质类型" style="width:200px;text-align: center"/>
                                <p:column headerText="预警类别" style="width:150px;text-align: center;"/>
                                <p:column headerText="操作" style="text-align: center;"/>
                            </p:row>
                        </p:columnGroup>
                        <p:column rendered="#{null ne itm[0]}">
                            <h:outputText value="#{itm[0]}"/>
                        </p:column>
                        <p:column>
                            <h:outputText value="#{itm[1]}"/>
                        </p:column>
                        <p:column>
                            <p:commandLink value="配置" resetValues="true"
                                           action="#{mgrbean.modInitAction}" update=":tabView"
                                           process="@this">
                                <f:setPropertyActionListener
                                        target="#{mgrbean.type}" value="#{itm[2]}"/>
                            </p:commandLink>
                        </p:column>
                    </p:dataTable>
                </h:form>
            </p:tab>
            <p:tab id="edit" title="edit" titleStyle="display:none;">
                <ui:include src="/webapp/heth/zzsb/orgZzWarningConfigEdit.xhtml"/>
            </p:tab>

        </p:tabView>

        <ui:include src="/WEB-INF/templates/system/ajaxLoading.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/focus.xhtml"/>
        <ui:include src="/WEB-INF/templates/system/growl.xhtml"/>
    </h:body>
</f:view>
</html>