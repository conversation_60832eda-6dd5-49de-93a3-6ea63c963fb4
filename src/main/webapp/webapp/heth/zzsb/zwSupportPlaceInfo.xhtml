<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.ZwOrgInfoBaseZzsbBean"-->
    <style type="text/css">
        .calendarClass input{
            width:180px;
        }
        .calendarClass1 input{
            width:77px;
        }
        #mainForm\:tabView\:fsServiceObjTable table.ui-selectmanycheckbox td label{
            white-space: pre-line;
            width: max-content;
        }
        .myCalendar1 input{
            width:180px;
        }
        .myCalendar2 input{
            width:78px;
        }
    </style>
    <!--编辑-->
    <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;" id="placeGrid"  rendered="#{mgrbean.zwOrginfoEntity.state == 0}">
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 25px;">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="是否设有职业卫生检测分析实验室：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.supportPlaces.ifZwLab}"
                                  style="width:120px;">
                    <f:selectItem itemLabel="否" itemValue="0" />
                    <f:selectItem itemLabel="是" itemValue="1" />
                    <p:ajax process=":mainForm:tabView:placeGrid" update=":mainForm:tabView:placeGrid" />
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 31px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifZwLab?'':'display: none;'}" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="实验室建筑面积（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifZwLab?'':'display: none;'}" colspan="3" >
                <p:inputText value="#{mgrbean.zwOrginfoEntity.supportPlaces.zwLabArea}" style="width:180px;" onkeypress="SYSTEM.verifyNum4(this, 6, 2)"
                             onkeyup="SYSTEM.verifyNum4(this, 6, 2)" onblur="SYSTEM.verifyNum4(this, 6, 2)"/>
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 25px;">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="是否设有放射卫生检测分析实验室：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.supportPlaces.ifFsLab}"
                                  style="width:120px;">
                    <f:selectItem itemLabel="否" itemValue="0" />
                    <f:selectItem itemLabel="是" itemValue="1" />
                    <p:ajax process=":mainForm:tabView:placeGrid" update=":mainForm:tabView:placeGrid" />
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 31px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifFsLab?'':'display: none;'}" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="实验室建筑面积（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifFsLab?'':'display: none;'}" colspan="3">
                <p:inputText value="#{mgrbean.zwOrginfoEntity.supportPlaces.fsLabArea}" style="width:180px;" onkeypress="SYSTEM.verifyNum4(this, 6, 2)"
                             onkeyup="SYSTEM.verifyNum4(this, 6, 2)" onblur="SYSTEM.verifyNum4(this, 6, 2)"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 25px;">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="是否承担教学和科研任务：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.supportPlaces.ifScienTask}"
                                  style="width:120px;">
                    <f:selectItem itemLabel="否" itemValue="0" />
                    <f:selectItem itemLabel="是" itemValue="1" />
                    <p:ajax process=":mainForm:tabView:placeGrid" update=":mainForm:tabView:placeGrid" />
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 31px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifScienTask?'':'display: none;'}" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="教学用房（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 410px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifScienTask?'':'display: none;'}">
                <p:inputText value="#{mgrbean.zwOrginfoEntity.supportPlaces.teachArea}" style="width:180px;" onkeypress="SYSTEM.verifyNum4(this, 6, 2)"
                             onkeyup="SYSTEM.verifyNum4(this, 6, 2)" onblur="SYSTEM.verifyNum4(this, 6, 2)"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:360px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifScienTask?'':'display: none;'}" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="科研用房（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifScienTask?'':'display: none;'}">
                <p:inputText value="#{mgrbean.zwOrginfoEntity.supportPlaces.scienArea}" style="width:180px;" onkeypress="SYSTEM.verifyNum4(this, 6, 2)"
                             onkeyup="SYSTEM.verifyNum4(this, 6, 2)" onblur="SYSTEM.verifyNum4(this, 6, 2)"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 25px;">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="是否设有职业健康检查中心：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.supportPlaces.ifTj}"
                                  style="width:120px;">
                    <f:selectItem itemLabel="否" itemValue="0" />
                    <f:selectItem itemLabel="是" itemValue="1" />
                    <p:ajax process=":mainForm:tabView:placeGrid" update=":mainForm:tabView:placeGrid" />
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 31px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifTj?'':'display: none;'}" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="工作用房建筑总面积（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 410px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifTj?'':'display: none;'}">
                <p:inputText value="#{mgrbean.zwOrginfoEntity.supportPlaces.tjLabArea}" style="width:180px;" onkeypress="SYSTEM.verifyNum4(this, 6, 2)"
                             onkeyup="SYSTEM.verifyNum4(this, 6, 2)" onblur="SYSTEM.verifyNum4(this, 6, 2)"/>
                <p:outputLabel value="(仅指职业健康检查中心面积)" style="margin-left: 5px;" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:360px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifTj?'':'display: none;'}" >
                <p:outputLabel value="是否设置以下科室：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifTj?'':'display: none;'}">
                <p:selectManyCheckbox  value="#{mgrbean.zwOrginfoEntity.supportPlaces.selOffices}">
                    <f:selectItems value="#{mgrbean.officeList}" var="item" itemValue="#{item.rid}" itemLabel="#{item.codeName}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 25px;">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="是否开展职业病诊断工作："  rendered="#{'1' eq mgrbean.unitType}" />
                <p:outputLabel value="是否开展职业病诊断救治工作：" rendered="#{'2' eq mgrbean.unitType}" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.supportPlaces.ifDiag}"
                                  style="width:120px;">
                    <f:selectItem itemLabel="否" itemValue="0" />
                    <f:selectItem itemLabel="是" itemValue="1" />
                    <p:ajax process=":mainForm:tabView:placeGrid" update=":mainForm:tabView:placeGrid" />
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 31px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag?'':'display: none;'}" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="工作用房建筑总面积（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 410px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag?'':'display: none;'}">
                <p:inputText value="#{mgrbean.zwOrginfoEntity.supportPlaces.diagLabArea}" style="width:180px;" onkeypress="SYSTEM.verifyNum4(this, 6, 2)"
                             onkeyup="SYSTEM.verifyNum4(this, 6, 2)" onblur="SYSTEM.verifyNum4(this, 6, 2)"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:360px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag?'':'display: none;'}" >
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="床位（张）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag?'':'display: none;'}">
                <p:inputText value="#{mgrbean.zwOrginfoEntity.supportPlaces.diagBed}" style="width:180px;" onkeypress="SYSTEM.verifyNumClear0(this)"
                             onkeyup="SYSTEM.verifyNumClear0(this)" onblur="SYSTEM.verifyNumClear0(this)"
                             maxlength="6"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 25px;#{(1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag and '2' eq mgrbean.unitType)?'':'display: none;'}">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="本单位场所建设情况相当于达到以下标准：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{(1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag and '2' eq mgrbean.unitType)?'':'display: none;'}" colspan="3">
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.supportPlaces.unitStdId}"
                                  style="width:120px;">
                    <f:selectItems value="#{mgrbean.stdList}" var="item"  itemLabel="#{item.codeName}" itemValue="#{item.rid}" />
                </p:selectOneRadio>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:360px;height: 25px;#{(1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag and '2' eq mgrbean.unitType)?'':'display: none;'}">
                <p:outputLabel value="*" style="color: red"/>
                <p:outputLabel value="开展职业病救治工作的专业人员配置相当于达到以下标准：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;#{(1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag and '2' eq mgrbean.unitType)?'':'display: none;'}" colspan="3">
                <p:selectOneRadio value="#{mgrbean.zwOrginfoEntity.supportPlaces.psnStdId}"
                                  style="width:120px;">
                    <f:selectItems value="#{mgrbean.stdList1}" var="item"  itemLabel="#{item.codeName}" itemValue="#{item.rid}" />
                </p:selectOneRadio>
            </p:column>
        </p:row>
    </p:panelGrid>

    <!--详情-->
    <p:panelGrid style="width:100%;margin-top: 5px;margin-bottom: 5px;"
                 rendered="#{mgrbean.zwOrginfoEntity.state != 0}">
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;">
                <p:outputLabel value="是否设有职业卫生检测分析实验室：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifZwLab==0}"/>
                <p:outputLabel value="是" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifZwLab==1}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifZwLab}">
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;" >
                <p:outputLabel value="实验室建筑面积（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.zwLabArea}" />
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;">
                <p:outputLabel value="是否设有放射卫生检测分析实验室：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifFsLab==0}"/>
                <p:outputLabel value="是" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifFsLab==1}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifFsLab}">
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;" >
                <p:outputLabel value="实验室建筑面积（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.fsLabArea}" />
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;">
                <p:outputLabel value="是否承担教学和科研任务：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifScienTask==0}"/>
                <p:outputLabel value="是" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifScienTask==1}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifScienTask}">
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;" >
                <p:outputLabel value="教学用房（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 300px;">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.teachArea}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:250px;" >
                <p:outputLabel value="科研用房（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.scienArea}" />
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;">
                <p:outputLabel value="是否设有职业健康检查中心：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifTj==0}"/>
                <p:outputLabel value="是" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifTj==1}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifTj}">
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;" >
                <p:outputLabel value="工作用房建筑总面积（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 380px;">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.tjLabArea}" />
                <p:outputLabel value="(仅指职业健康检查中心面积)" style="margin-left: 5px;" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:250px;" >
                <p:outputLabel value="是否设置以下科室：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.selOfficeName}" />
            </p:column>
        </p:row>
        <p:row >
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;">
                <p:outputLabel value="是否开展职业病诊断工作："  rendered="#{'1' eq mgrbean.unitType}" />
                <p:outputLabel value="是否开展职业病诊断救治工作：" rendered="#{'2' eq mgrbean.unitType}" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="否" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifDiag==0}"/>
                <p:outputLabel value="是" rendered="#{mgrbean.zwOrginfoEntity.supportPlaces.ifDiag==1}"/>
            </p:column>
        </p:row>
        <p:row rendered="#{1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag}">
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;" >
                <p:outputLabel value="工作用房建筑总面积（平方米）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;width: 300px;">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.diagLabArea}" />
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:250px;" >
                <p:outputLabel value="床位（张）：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.diagBed}" />
            </p:column>
        </p:row>
        <p:row rendered="#{'2' eq mgrbean.unitType and 1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag}">
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;">
                <p:outputLabel value="本单位场所建设情况相当于达到以下标准：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.fkByUnitStdId.codeName}" />
            </p:column>
        </p:row>
        <p:row rendered="#{'2' eq mgrbean.unitType and 1 eq mgrbean.zwOrginfoEntity.supportPlaces.ifDiag}">
            <p:column style="text-align:right;padding-right:3px;width:250px;height: 31px;">
                <p:outputLabel value="开展职业病救治工作的专业人员配置相当于达到以下标准：" />
            </p:column>
            <p:column style="text-align:left;padding-left:3px;" colspan="3">
                <p:outputLabel value="#{mgrbean.zwOrginfoEntity.supportPlaces.fkByPsnStdId.codeName}" />
            </p:column>
        </p:row>
    </p:panelGrid>

</ui:composition>
