<ui:composition xmlns="http://www.w3.org/1999/xhtml"
        xmlns:ui="http://java.sun.com/jsf/facelets"
        xmlns:h="http://java.sun.com/jsf/html"
        xmlns:f="http://java.sun.com/jsf/core"
        xmlns:p="http://primefaces.org/ui">
        <p:dialog id="recordDialog" widgetVar="RecordDialog" width="680" height="400" header="备案信息" resizable="false" modal="true">
	   		<p:panelGrid style="width:100%" id="recordPanel">
	   			<p:row>
	   				<p:column style="text-align:right;padding-right:3px;width:150px;height: 32px;">
	   					<h:outputText value="*" style="color:red;"></h:outputText>
	   					<h:outputText value="备案地区："></h:outputText>
	   				</p:column>
	   				<p:column>
	   					<p:selectOneMenu value="#{mgrbean.record.zoneId}" style="width:158px;">
	         				<f:selectItem itemValue="" itemLabel="--请选择--"></f:selectItem>
	         				<f:selectItems value="#{mgrbean.recordZoneList}" var="zone" 
	         					itemValue="#{zone.rid}" itemLabel="#{zone.zoneName}"></f:selectItems>
	         			</p:selectOneMenu>
	   				</p:column>
	   			</p:row>
				<p:row>
					<p:column style="text-align:right;padding-right:3px;width:150px;height: 32px;">
						<h:outputText value="*" style="color:red;"></h:outputText>
						<h:outputText value="备案单位名称："></h:outputText>
					</p:column>
					<p:column>
						<p:inputText value="#{mgrbean.record.unitName}" maxlength="100" style="width:150px;"></p:inputText>
					</p:column>
				</p:row>
	   			<p:row>
	   				<p:column style="text-align:right;padding-right:3px;width:150px;height: 32px;">
	   					<h:outputText value="*" style="color:red;"></h:outputText>
	   					<h:outputText value="备案编号："></h:outputText>
	   				</p:column>
	   				<p:column>
	   					<p:inputText value="#{mgrbean.record.rcdNo}" maxlength="25" style="width:150px;"></p:inputText>
	   				</p:column>
	   			</p:row>
	   			<p:row>
	   				<p:column style="text-align:right;padding-right:3px;width:150px;height: 32px;">
	   					<h:outputText value="*" style="color:red;"></h:outputText>
	   					<h:outputText value="备案日期："></h:outputText>
	   				</p:column>
	   				<p:column>
	   					<p:calendar value="#{mgrbean.record.certDate}" navigator="true" size="10"
	                           yearRange="c-20:c" converterMessage="日期格式输入不正确！"
	                           pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
	                           maxdate="new Date()" readonlyInput="true"/>
	   				</p:column>
	   			</p:row>
	   			<p:row>
	   				<p:column style="text-align:right;padding-right:3px;width:150px;">
	   					<h:outputText value="*" style="color:red;"></h:outputText>
	   					<h:outputText value="服务项目："></h:outputText>
	   				</p:column>
	   				<p:column>
	   					<p:selectManyCheckbox columns="1" layout="grid" value="#{mgrbean.record.serviceObj}">
		                   <f:selectItems value="#{mgrbean.jkList}" var="jk" itemValue="#{jk.rid}" itemLabel="#{jk.codeName}"/>
		               </p:selectManyCheckbox>
	   				</p:column>
	   			</p:row>
	   			<p:row>
	   				<p:column style="text-align:right;padding-right:3px;width:150px;height: 32px;">
	   					<h:outputText value="*" style="color:red;"></h:outputText>
	   					<h:outputText value="状态："></h:outputText>
	   				</p:column>
	   				<p:column>
	   					<p:selectOneRadio value="#{mgrbean.record.stateMark}">
	         				<f:selectItem itemValue="0" itemLabel="正常"></f:selectItem>
	         				<f:selectItem itemValue="1" itemLabel="注销"></f:selectItem>
	         				<p:ajax event="change" process="@this,recordPanel" update="recordPanel"></p:ajax>
	         			</p:selectOneRadio>
	   				</p:column>
	   			</p:row>
	   			<p:row>
	   				<p:column style="text-align:right;padding-right:3px;width:150px;height: 32px;">
	   					<h:outputText value="*" style="color:red;" rendered="#{mgrbean.record.stateMark == 1}"></h:outputText>
	   					<h:outputText value="注销日期："></h:outputText>
	   				</p:column>
	   				<p:column>
	   					<p:calendar value="#{mgrbean.record.logoutDate}" size="10" navigator="true"
		                           yearRange="c-20:c" converterMessage="注销日期格式输入不正确！"
		                           pattern="yyyy-MM-dd"  showButtonPanel="true" showOtherMonths="true"
		                           disabled="#{mgrbean.record.stateMark != 1}"/>
	   				</p:column>
	   			</p:row>
	   		</p:panelGrid>
	   		<f:facet name="footer">
	   			<h:panelGrid style="width: 100%;text-align: center;">
	                <h:panelGroup>
	                    <p:commandButton value="保存" icon="ui-icon-check" action="#{mgrbean.saveRecordAction}"
	                                     process="@this,recordPanel" update="recordTable"/>
	                    <p:spacer width="5" />
	                    <p:commandButton value="取消" icon="ui-icon-close" onclick="PF('RecordDialog').hide();" process="@this"/>
	                </h:panelGroup>
	            </h:panelGrid>
	         </f:facet>
	   </p:dialog>
        
</ui:composition>