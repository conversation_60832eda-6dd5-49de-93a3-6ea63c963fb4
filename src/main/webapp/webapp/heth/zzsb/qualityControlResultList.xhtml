<ui:composition xmlns="http://www.w3.org/1999/xhtml" xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html" xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui" xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/mainTemplate.xhtml">

    <!-- 托管Bean -->
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.QualityControlResultListBean"-->
    <ui:param name="mgrbean" value="#{qualityControlResultListBean}"/>
    <!-- 添加/编辑页面 -->
    <ui:param name="editPage" value="/webapp/heth/zzsb/qualityControlResultEdit.xhtml"/>
    <ui:param name="viewPage" value="/webapp/heth/zzsb/qualityControlResultView.xhtml"/>
    <!-- 详情页面 -->
    <!--<ui:param name="viewPage" value=""/>-->
    <!-- 样式或javascripts -->
    <ui:define name="insertScripts">
        <!--引入中文日期-->
        <ui:include src="/WEB-INF/templates/system/calendar.xhtml"/>
        <h:outputScript library="js" name="namespace.js"/>
        <h:outputScript name="js/validate/system/validate.js"/>
        <script type="text/javascript">
            function datatableOffClick() {
                $(document).off("click.datatable", "#tabView\\:mainForm\\:dataTable tbody.ui-datatable-data > tr.ui-widget-content.ui-datatable-selectable");
            }
        </script>
        <style>
            .myCalendar1 input {
                width: 78px;
            }

            table.ui-selectoneradio td label {
                white-space: nowrap;
                overflow: hidden;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="质量控制结果录入"/>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 按钮 -->
    <ui:define name="insertButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;">
            <h:panelGrid columns="3" style="border-color:transparent;padding:0;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical"/></span>
                <p:commandButton value="查询" icon="ui-icon-search" id="searchBtn" action="#{mgrbean.searchAction}"
                                 process="@this,mainGrid" oncomplete="datatableOffClick()"/>
                <p:commandButton value="添加" icon="ui-icon-plus" id="addBtn"
                                 update=":tabView" action="#{mgrbean.addInitAction}"
                                 process="@this" />
            </h:panelGrid>
        </p:outputPanel>
    </ui:define>

    <!-- 查询条件 -->
    <ui:define name="insertSearchConditons">
        <p:row>
            <p:column style="text-align:right;padding-right:3px;width:150px;height:33px;">
                <h:outputText value="地区："/>
            </p:column>
            <p:column style="text-align:left;padding-left: 4px;width:280px;">
                <zwx:ZoneSingleComp zoneList="#{mgrbean.searchZoneList}"
                                    zoneCode="#{mgrbean.searchZoneGb}"
                                    zoneName="#{mgrbean.searchZoneName}"/>
            </p:column>

            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="机构名称："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;width: 280px;">
                <p:inputText id="searchCrptName" value="#{mgrbean.searchOrgName}" style="width: 180px;"
                             maxlength="50" placeholder="模糊查询"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;width:150px;">
                <h:outputText value="质控日期："/>
            </p:column>
            <p:column style="text-align:left;padding-left:9px;">
                <zwx:CalendarDynamicLimitComp startDate="#{mgrbean.searchQcBeginDate}"
                                              endDate="#{mgrbean.searchQcEndDate}"
                                              styleClass="myCalendar2"/>
            </p:column>
        </p:row>
        <p:row>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="质控结论："/>
            </p:column>
            <p:column style="text-align:left;padding-left:4px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectQcConclusionNames}"
                                        selectedIds="#{mgrbean.selectQcConclusionIds}"
                                        simpleCodeList="#{mgrbean.qcConclusionSimpleCodeList}"
                                        panelWidth="190"
                                        height="200"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;height:38px;">
                <h:outputText value="整改结果："/>
            </p:column>
            <p:column style="text-align:left;padding-left:4px;">
                <zwx:SimpleCodeManyComp codeName="#{mgrbean.selectRectificationResultNames}"
                                        selectedIds="#{mgrbean.selectRectificationResultIds}"
                                        simpleCodeList="#{mgrbean.rectificationResultSimpleCodeList}"
                                        panelWidth="190"
                                        height="200"/>
            </p:column>
            <p:column style="text-align:right;padding-right:3px;">
                <h:outputText value="状态："/>
            </p:column>
            <p:column style="text-align:left;padding-left:8px;">
                <p:selectManyCheckbox value="#{mgrbean.states}">
                    <f:selectItems value="#{mgrbean.stateList}"/>
                </p:selectManyCheckbox>
            </p:column>
        </p:row>
    </ui:define>

    <!-- 表格列 -->
    <ui:define name="insertDataTable">
        <!--@elvariable id="itm" type="java.util.List"-->
        <p:column headerText="地区" style="width: 220px;padding-left: 8px;">
            <h:outputText value="#{itm[1]}"/>
        </p:column>
        <p:column headerText="机构名称" style="width:240px;padding-left: 8px;">
            <h:outputText value="#{itm[2]}"/>
        </p:column>
        <p:column headerText="考核类型" style="width:220px;text-align: center;">
            <h:outputText value="#{itm[3]}"/>
        </p:column>
        <p:column headerText="质控结论" style="width:100px;text-align: center;">
            <h:outputText value="#{itm[4]}"/>
        </p:column>
        <p:column headerText="整改结果" style="width:100px;padding-left: 8px;text-align: center;">
            <h:outputText value="#{itm[5]}"/>
        </p:column>
        <p:column headerText="质控日期" style="width:80px;text-align: center;">
            <h:outputLabel value="#{itm[6]}">
                <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
            </h:outputLabel>
        </p:column>
        <p:column headerText="状态" style="padding-left: 3px; width:80px;text-align:center;">
            <h:outputText value="#{itm[7]}"/>
        </p:column>
        <p:column headerText="操作" style="padding-left: 8px;">
            <p:commandLink value="详情" action="#{mgrbean.viewInitAction}" process="@this" update=":tabView"
                           rendered="#{'已提交' eq itm[7]}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
            </p:commandLink>
            <p:commandLink value="修改" action="#{mgrbean.modInitAction}" process="@this" update=":tabView"
                           rendered="#{'待提交' eq itm[7]}">
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
            </p:commandLink>
            <p:spacer width="5"/>
            <p:commandLink value="删除" action="#{mgrbean.deleteAction}" process="@this" update=":tabView"
                           rendered="#{'待提交' eq itm[7]}">
                <p:confirm header="消息确认框" message="确定要删除吗？" icon="ui-icon-alert"/>
                <f:setPropertyActionListener value="#{itm[0]}" target="#{mgrbean.mainRid}"/>
            </p:commandLink>
        </p:column>
    </ui:define>

    <ui:define name="insertOtherMainContents">
    </ui:define>
</ui:composition>