<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                xmlns:c="http://java.sun.com/jsp/jstl/core"
                xmlns:zwx="http://java.sun.com/jsf/composite/customComponent"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <!--@elvariable id="mgrbean" type="com.chis.modules.heth.zzsb.web.OrgZzCheckListBean"-->
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            //<![CDATA[
            function disabledInput(){
                var text;
                var $tabView = $("#tabView\\:editForm\\:codeEditGrid");
                $tabView.find("input,textarea,label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"修改"==text){
                        $(this).remove();
                    }else if("查看附件"==text || "查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box'],div[class*='ui-chkbox']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("删除"==text){
                        $(this).remove();
                    }else if("关闭"==text||"查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            function disabledZzlbInput(){
                var text;
                var $tabView = $("#tabView\\:editForm\\:zzlbGrid");
                $tabView.find("input,textarea,label").each(function(){
                    $(this).css("pointer-events","none");
                });
                $tabView.find("a").each(function(){
                    text = $(this).text();
                    if(!text){
                        text = $(this).attr("title");
                    }
                    if("删除"==text||"修改"==text){
                        $(this).remove();
                    }else if("查看附件"==text || "查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }

                });
                $tabView.find("div[class*='ui-chkbox-box'],div[class*='ui-radiobutton-box'],div[class*='ui-chkbox']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("opacity","1");
                    $(this).css("pointer-events","none");
                });
                //下拉
                $tabView.find("div[class*='ui-selectonemenu']").each(function(){
                    $(this).addClass("ui-state-disabled");
                    $(this).css("pointer-events","none");
                    $(this).css("opacity","1");
                });
                //按钮
                $tabView.find("button").each(function(){
                    text = $(this).text();
                    if("删除"==text){
                        $(this).remove();
                    }else if("关闭"==text||"查看"==text){

                    }else{
                        $(this).prop("disabled",true);
                        $(this).css("pointer-events","none");
                        $(this).css("opacity","0.35");
                    }
                });
            }
            //]]>
        </script>
        <style type="text/css">
            #tabView\:editForm\:serDetailDatatable table.ui-selectmanycheckbox td label{
                white-space: pre-line;
                width: 250px;
            }
        </style>
    </ui:define>
    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="6" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构审核" />
            </p:column>
        </p:row>
    </ui:define>
    <!-- 按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display: flex;">
            <h:panelGrid columns="10" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <!-- 注意 orgZzCheckComm不可以被process -->
                <p:commandButton value="提交" icon="ui-icon-check"
                                 action="#{mgrbean.submitAction}" process="@this,:tabView:editForm:auditPanel" >
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w"
                                 action="#{mgrbean.backAction}" process="@this"
                                 update=":tabView" onclick="hideTooltips();" />
                <p:inputText style="visibility: hidden;width: 0"/>
            </h:panelGrid>
            <p:outputPanel style="display:flex;flex:1;align-items:center;justify-content: flex-end;padding-right:10px;">
                <h:outputLabel value="最新更新日期：" style="color:blue;font-weight: bold;" rendered="#{null ne mgrbean.infoViewBean.zwOrginfoEntity.lastSmtDate}" />
                <p:outputLabel value="#{mgrbean.infoViewBean.zwOrginfoEntity.lastSmtDate}" style="color: blue;font-weight: bold;">
                    <f:convertDateTime pattern="yyyy-MM-dd" timeZone="Asia/Shanghai" locale="cn"/>
                </p:outputLabel>
            </p:outputPanel>
        </p:outputPanel>
    </ui:define>
    <ui:define name="insertOtherContents">
        <p:fieldset legend="审核意见" toggleable="true" toggleSpeed="500"
                    style="margin-top: 5px;margin-bottom: 5px;" >
            <p:panelGrid style="margin-top: 10px;margin-bottom: 10px;width:100%" id="auditPanel" >
                <p:row>
                    <p:column styleClass="column_title" style="width: 183px;height: 30px;text-align:right;">
                        <h:outputText value="*" style="color:red;" />
                        <p:outputLabel value="审核结果："/>
                    </p:column>
                    <p:column style="text-align:left;height: 30px;padding-left:3px;padding-top: 0px; padding-bottom: 0px;width: 210px;">
                        <p:selectOneRadio  style="width:120px;"  value="#{mgrbean.editCheckRst}" >
                            <f:selectItem itemLabel="通过" itemValue="1" />
                            <f:selectItem itemLabel="退回" itemValue="2" />
                            <p:ajax event="change" process="@this,:tabView:editForm:auditPanel" listener="#{mgrbean.changeCheckState}" update="auditPanel" resetValues="true" />
                        </p:selectOneRadio>
                    </p:column>
                    <p:column styleClass="column_title" style="width: 183px;text-align:right;">
                        <h:outputText value="*" style="color:red;" rendered="#{2 == mgrbean.editCheckRst}" />
                        <p:outputLabel value="审核意见：" />
                    </p:column>
                    <p:column style="text-align:left;padding-left:6px;">
                        <p:inputTextarea rows="1" autoResize="false" id="editCheckRstId"
                                         style="overflow-y:hidden;resize: none;width: 594px;"
                                         maxlength="200" value="#{mgrbean.editCheckAudit}" />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:fieldset>
        <ui:include src="orgZzCheckComm.xhtml" >
            <ui:param name="baseComposition" value="tabView:editForm" />
        </ui:include>
    </ui:define>
</ui:composition>