<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui"
                template="/WEB-INF/templates/system/editTemplate.xhtml">
    <ui:define name="insertEditScripts">
        <script type="text/javascript">
            function generateClick(){
                document.getElementById("tabView:editForm:generateReportId").click();
            }
        </script>
        <style>
            .ui-dialog .ui-dialog-buttonpane{
                text-align: center;
            }
            .shadeTip {
                border-radius: 5px;
                padding: 10px;
                background: #4D4D4D !important;
                text-align: left;
                color: white !important;
                word-wrap: break-word;
                border: none;
            }

            .shadeTip .ui-dialog-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }

            .shadeTip .ui-widget-content {
                border: 1px solid transparent;
                background: #4D4D4D !important;
            }
            .shadeTip>div:first-child {
                overflow: hidden;
                margin-top: -10px;
            }
        </style>
    </ui:define>

    <!-- 标题栏 -->
    <ui:define name="insertEditTitle">
        <p:row>
            <p:column colspan="4" style="text-align:left;padding-left:5px;height: 20px;">
                <h:outputText value="资质机构备案审核"/>
            </p:column>
        </p:row>
    </ui:define>
    <!-- 编辑页面的按钮 -->
    <ui:define name="insertEditButtons">
        <p:outputPanel styleClass="zwx_toobar_42" style="display:flex;" id="editButtonsId">
            <h:panelGrid columns="8" style="border-color:transparent;padding:0px;">
                <span class="ui-separator"><span
                        class="ui-icon ui-icon-grip-dotted-vertical" /></span>
                <!-- 保存、提交以及撤销后需要查询并更新列表 -->
                <p:commandButton value="通过" icon="ui-icon-check"
                                 action="#{mgrbean.openConfirmDialog}"
                                 process="@this,:tabView:editForm" rendered="#{mgrbean.ifView==0}">
                </p:commandButton>
                <p:commandButton value="驳回" icon="ui-icon-cancel"
                                 process="@this" action="#{mgrbean.openReasonDialog}"
                                  rendered="#{mgrbean.ifView==0}">
                </p:commandButton>
                <p:commandButton value="生成备案回执" icon="ui-icon-print"
                                 rendered="#{(null == mgrbean.applyInfoBaseBean.reportReceipt or null == mgrbean.applyInfoBaseBean.reportReceipt.filePath) and mgrbean.ifView==0 and mgrbean.applyInfoBaseBean.ifFinalAudit}"
                                 action="#{mgrbean.applyInfoBaseBean.generateReport}" process="@this,:tabView:editForm"
                                 onstart="PF('shadeTip').show();" oncomplete="PF('shadeTip').hide();"
                />
                <p:commandButton style="display: none;" id="generateReportId" ajax="false" icon="ui-icon-print"
                                 process="@this,:tabView:editForm"  >
                    <p:fileDownload  value="#{mgrbean.applyInfoBaseBean.reportReceiptFile}" />
                </p:commandButton>
                <p:commandButton value="上传备案回执" icon="ui-icon-arrowreturnthick-1-n" update=":tabView:editForm"
                                 rendered="#{(null == mgrbean.applyInfoBaseBean.reportReceipt or null == mgrbean.applyInfoBaseBean.reportReceipt.filePath) and mgrbean.ifView==0 and mgrbean.applyInfoBaseBean.ifFinalAudit}"
                                 action="#{mgrbean.applyInfoBaseBean.preUploadReportReceipt}" process="@this,:tabView:editForm"  >
                    <f:setPropertyActionListener value="6" target="#{mgrbean.applyInfoBaseBean.annexTypeBase}"/>
                </p:commandButton>
                <p:commandButton value="查看备案回执" icon="ui-icon-zoomin" process="@this,:tabView:editForm"
                                 rendered="#{null ne mgrbean.applyInfoBaseBean.reportReceipt and null ne mgrbean.applyInfoBaseBean.reportReceipt.filePath and (mgrbean.ifView==1 or (mgrbean.ifView==0 and mgrbean.applyInfoBaseBean.ifFinalAudit))}"
                                 oncomplete="window.open('/webFile/#{mgrbean.applyInfoBaseBean.reportReceipt.filePath}');"/>
                <p:commandButton value="删除备案回执" icon="ui-icon-trash" process="@this,:tabView:editForm" update=":tabView:editForm"
                                 rendered="#{null ne mgrbean.applyInfoBaseBean.reportReceipt and null ne mgrbean.applyInfoBaseBean.reportReceipt.filePath and mgrbean.ifView==0 and mgrbean.applyInfoBaseBean.ifFinalAudit}"
                                 action="#{mgrbean.applyInfoBaseBean.delReportReceipt}"/>
                <p:commandButton value="撤销" icon="ui-icon-cancel" id="cancelBtn" action="#{mgrbean.applyCancel}"
                                 rendered="#{mgrbean.ifView==1 and mgrbean.applyInfoBaseBean.tjorginfoApply.state == 3}"
                                 process="@this" update=":tabView" >
                    <p:confirm header="消息确认框" message="确定要撤销吗？" icon="ui-icon-alert" />
                </p:commandButton>
                <p:commandButton value="返回" icon="ui-icon-arrowreturnthick-1-w" id="backBtn" action="#{mgrbean.backAction}"
                                 update=":tabView"  />

            </h:panelGrid>
        </p:outputPanel>
    </ui:define>
    <!-- 编辑页面的内容-->
    <ui:define name="insertOtherContents">
        <ui:include src="tdZwQualTjOrgApplyInfo.xhtml">
            <ui:param name="updateId" value=":tabView:editForm" />
            <ui:param name="updateName" value="tabView:editForm" />
            <ui:param name="orgApplyBean" value="#{mgrbean.applyInfoBaseBean}" />
        </ui:include>
        <ui:include src="tdZwQualDiagOrgApplyInfo.xhtml">
            <ui:param name="updateId" value=":tabView:editForm" />
            <ui:param name="updateName" value="tabView:editForm" />
            <ui:param name="orgApplyBean" value="#{mgrbean.applyInfoBaseBean}" />
        </ui:include>
        <p:confirmDialog message="确定要审核通过吗？" header="消息确认框"
                         severity="alert" closable="false" widgetVar="cd" id="conDia">
            <p:commandButton value="确定"
                             action="#{mgrbean.beforeSaveAction}"
                             process="@this,:tabView:editForm" />
            <p:commandButton value="取消"
                             oncomplete="PF('cd').hide();" process="@this"/>
        </p:confirmDialog>
        <!--终审-->
        <p:confirmDialog message="审核通过后不可撤回，确定审核通过？" header="消息确认框"
                         severity="alert" closable="false" widgetVar="zs" >
            <p:commandButton value="确定"
                             action="#{mgrbean.beforeSaveAction}"
                             process="@this,:tabView:editForm" />
            <p:commandButton value="取消"
                             oncomplete="PF('zs').hide();" process="@this"/>
        </p:confirmDialog>
        <p:dialog id="reasonDialog" widgetVar="ReasonDialog" width="500"
                  height="200" header="驳回理由" resizable="false" modal="true">
            <h:inputText
                    style="visibility:hidden;height:0px;margin:0;padding:0;border:none;width:1px" />
            <p:inputTextarea value="#{mgrbean.backRsn}"
                             style="resize:none;width:97%;height:90%;" autoResize="false"
                             id="reasonContent" maxlength="200"/>
            <f:facet name="footer">
                <h:panelGrid style="width: 100%;text-align: right;">
                    <h:panelGroup>
                        <p:commandButton value="取消" onclick="PF('ReasonDialog').hide();"
                                         process="@this" immediate="true" />
                        <p:spacer width="5" />
                        <p:commandButton value="确定" styleClass="submit_btn"
                                         action="#{mgrbean.returnAction}" />
                    </h:panelGroup>
                </h:panelGrid>
            </f:facet>
        </p:dialog>
        <p:dialog header="文件上传" widgetVar="fileUIdVar" id="fileUId" resizable="false" modal="true" width="800"  >
            <table width="100%">
                <tr>
                    <td style="text-align: right;">
                        <p:outputLabel value="（支持附件格式为：图片、PDF）" styleClass="blueColorStyle"
                                       style="position: relative;bottom: -6px;padding-right: 150px;font-weight: bold;color: #ffffff;z-index: 10;"/>
                    </td>
                </tr>
                <tr>
                    <td style="position: relative;top: -23px;">
                        <p:fileUpload requiredMessage="请选择要上传的文件！" multiple="false"
                                      label="文件选择" fileUploadListener="#{mgrbean.applyInfoBaseBean.handleFileUpload}"
                                      invalidSizeMessage="文件大小不能超过10M!" validatorMessage="上传出错啦，请重新上传！"
                                      invalidFileMessage="无效的文件类型！只能上传gif,jpeg,jpg,png,pdf类型文件"
                                      allowTypes="/(\.|\/)(gif|jpe?g|png|pdf)$/"
                                      style="width:768px;" previewWidth="120"
                                      fileLimit="1" fileLimitMessage="只能上传一个文件"
                                      process="@this" update="@this"
                                      cancelLabel="取消" uploadLabel="上传" dragDropSupport="true" mode="advanced"
                                      sizeLimit="10485760" />
                    </td>
                </tr>
            </table>
        </p:dialog>
        <p:dialog id="shadeTip" widgetVar="shadeTip" modal="true" height="20" resizable="false" showHeader="false" closeOnEscape="true" styleClass="shadeTip">
            <p:panelGrid >
                <p:row style="border:1px solid transparent !important;">
                    <p:column style="border: transparent !important;">
                        <p:graphicImage url="/resources/images/main/loading5.gif" style="margin-top: 4px;"/>
                    </p:column>
                    <p:column style="border: transparent !important;">
                        <h:outputText style="color: #FFFFFF;font-size: 15px;" value="备案回执生成中，请等待..." />
                    </p:column>
                </p:row>
            </p:panelGrid>
        </p:dialog>
    </ui:define>
</ui:composition>