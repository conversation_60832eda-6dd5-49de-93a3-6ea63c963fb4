<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">
    <!-- 诊断资质机构备案 -->
    <p:tabView id="orgDiagApplyTabView" widgetVar="OrgDiagApplyTabView" style="margin-top: 5px;" cache="false" dynamic="false"
               rendered="#{orgApplyBean.orgType == 2}" >
        <p:ajax event="tabChange" process="@this" listener="#{orgApplyBean.tableChange}" update="#{updateId}"  />
        <p:tab title="基本信息">
            <ui:include src="/webapp/heth/zzsb/tdZwDiagOrgApplyBaseInfo.xhtml" >
                <ui:param name="tabViewId" value="orgDiagApplyTabView" />
            </ui:include>
        </p:tab>
        <p:tab title="人员信息" >
            <ui:include src="/webapp/heth/zzsb/tdZwOrgApplyPsnInfo.xhtml" >
                <ui:param name="tabViewId" value="orgTjApplyTabView" />
            </ui:include>
        </p:tab>
        <p:tab title="仪器信息">
            <ui:include src="/webapp/heth/zzsb/tdZwOrgApplyInstInfo.xhtml">
                <ui:param name="tabViewId" value="orgTjApplyTabView"/>
            </ui:include>
        </p:tab>
        <p:tab title="资质附件" ></p:tab>
    </p:tabView>
</ui:composition>